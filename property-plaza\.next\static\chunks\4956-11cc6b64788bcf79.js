"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4956],{98575:function(t,e,n){n.d(e,{F:function(){return r},e:function(){return s}});var i=n(2265);function r(...t){return e=>t.forEach(t=>{"function"==typeof t?t(e):null!=t&&(t.current=e)})}function s(...t){return i.useCallback(r(...t),t)}},82912:function(t,e,n){n.d(e,{WV:function(){return f},jH:function(){return h}});var i=n(2265),r=n(54887),s=n(98575),o=n(57437),u=i.forwardRef((t,e)=>{let{children:n,...r}=t,s=i.Children.toArray(n),u=s.find(l);if(u){let t=u.props.children,n=s.map(e=>e!==u?e:i.Children.count(t)>1?i.Children.only(null):i.isValidElement(t)?t.props.children:null);return(0,o.jsx)(c,{...r,ref:e,children:i.isValidElement(t)?i.cloneElement(t,void 0,n):null})}return(0,o.jsx)(c,{...r,ref:e,children:n})});u.displayName="Slot";var c=i.forwardRef((t,e)=>{let{children:n,...r}=t;if(i.isValidElement(n)){let t,o;let u=(t=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?n.ref:(t=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?n.props.ref:n.props.ref||n.ref;return i.cloneElement(n,{...function(t,e){let n={...e};for(let i in e){let r=t[i],s=e[i];/^on[A-Z]/.test(i)?r&&s?n[i]=(...t)=>{s(...t),r(...t)}:r&&(n[i]=r):"style"===i?n[i]={...r,...s}:"className"===i&&(n[i]=[r,s].filter(Boolean).join(" "))}return{...t,...n}}(r,n.props),ref:e?(0,s.F)(e,u):u})}return i.Children.count(n)>1?i.Children.only(null):null});c.displayName="SlotClone";var a=({children:t})=>(0,o.jsx)(o.Fragment,{children:t});function l(t){return i.isValidElement(t)&&t.type===a}var f=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((t,e)=>{let n=i.forwardRef((t,n)=>{let{asChild:i,...r}=t,s=i?u:e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,o.jsx)(s,{...r,ref:n})});return n.displayName=`Primitive.${e}`,{...t,[e]:n}},{});function h(t,e){t&&r.flushSync(()=>t.dispatchEvent(e))}},87045:function(t,e,n){n.d(e,{j:function(){return s}});var i=n(24112),r=n(45345),s=new class extends i.l{#t;#e;#n;constructor(){super(),this.#n=t=>{if(!r.sk&&window.addEventListener){let e=()=>t();return window.addEventListener("visibilitychange",e,!1),()=>{window.removeEventListener("visibilitychange",e)}}}}onSubscribe(){this.#e||this.setEventListener(this.#n)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(t){this.#n=t,this.#e?.(),this.#e=t(t=>{"boolean"==typeof t?this.setFocused(t):this.onFocus()})}setFocused(t){this.#t!==t&&(this.#t=t,this.onFocus())}onFocus(){let t=this.isFocused();this.listeners.forEach(e=>{e(t)})}isFocused(){return"boolean"==typeof this.#t?this.#t:globalThis.document?.visibilityState!=="hidden"}}},2894:function(t,e,n){n.d(e,{R:function(){return u},m:function(){return o}});var i=n(18238),r=n(7989),s=n(11255),o=class extends r.F{#i;#r;#s;constructor(t){super(),this.mutationId=t.mutationId,this.#r=t.mutationCache,this.#i=[],this.state=t.state||u(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){this.#i.includes(t)||(this.#i.push(t),this.clearGcTimeout(),this.#r.notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){this.#i=this.#i.filter(e=>e!==t),this.scheduleGc(),this.#r.notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){this.#i.length||("pending"===this.state.status?this.scheduleGc():this.#r.remove(this))}continue(){return this.#s?.continue()??this.execute(this.state.variables)}async execute(t){this.#s=(0,s.Mz)({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(Error("No mutationFn found")),onFail:(t,e)=>{this.#o({type:"failed",failureCount:t,error:e})},onPause:()=>{this.#o({type:"pause"})},onContinue:()=>{this.#o({type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#r.canRun(this)});let e="pending"===this.state.status,n=!this.#s.canStart();try{if(!e){this.#o({type:"pending",variables:t,isPaused:n}),await this.#r.config.onMutate?.(t,this);let e=await this.options.onMutate?.(t);e!==this.state.context&&this.#o({type:"pending",context:e,variables:t,isPaused:n})}let i=await this.#s.start();return await this.#r.config.onSuccess?.(i,t,this.state.context,this),await this.options.onSuccess?.(i,t,this.state.context),await this.#r.config.onSettled?.(i,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(i,null,t,this.state.context),this.#o({type:"success",data:i}),i}catch(e){try{throw await this.#r.config.onError?.(e,t,this.state.context,this),await this.options.onError?.(e,t,this.state.context),await this.#r.config.onSettled?.(void 0,e,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,e,t,this.state.context),e}finally{this.#o({type:"error",error:e})}}finally{this.#r.runNext(this)}}#o(t){this.state=(e=>{switch(t.type){case"failed":return{...e,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...e,isPaused:!0};case"continue":return{...e,isPaused:!1};case"pending":return{...e,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...e,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...e,data:void 0,error:t.error,failureCount:e.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}})(this.state),i.V.batch(()=>{this.#i.forEach(e=>{e.onMutationUpdate(t)}),this.#r.notify({mutation:this,type:"updated",action:t})})}};function u(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},18238:function(t,e,n){n.d(e,{V:function(){return i}});var i=function(){let t=[],e=0,n=t=>{t()},i=t=>{t()},r=t=>setTimeout(t,0),s=i=>{e?t.push(i):r(()=>{n(i)})},o=()=>{let e=t;t=[],e.length&&r(()=>{i(()=>{e.forEach(t=>{n(t)})})})};return{batch:t=>{let n;e++;try{n=t()}finally{--e||o()}return n},batchCalls:t=>(...e)=>{s(()=>{t(...e)})},schedule:s,setNotifyFunction:t=>{n=t},setBatchNotifyFunction:t=>{i=t},setScheduler:t=>{r=t}}}()},57853:function(t,e,n){n.d(e,{N:function(){return s}});var i=n(24112),r=n(45345),s=new class extends i.l{#u=!0;#e;#n;constructor(){super(),this.#n=t=>{if(!r.sk&&window.addEventListener){let e=()=>t(!0),n=()=>t(!1);return window.addEventListener("online",e,!1),window.addEventListener("offline",n,!1),()=>{window.removeEventListener("online",e),window.removeEventListener("offline",n)}}}}onSubscribe(){this.#e||this.setEventListener(this.#n)}onUnsubscribe(){this.hasListeners()||(this.#e?.(),this.#e=void 0)}setEventListener(t){this.#n=t,this.#e?.(),this.#e=t(this.setOnline.bind(this))}setOnline(t){this.#u!==t&&(this.#u=t,this.listeners.forEach(e=>{e(t)}))}isOnline(){return this.#u}}},7989:function(t,e,n){n.d(e,{F:function(){return r}});var i=n(45345),r=class{#c;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,i.PN)(this.gcTime)&&(this.#c=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(t){this.gcTime=Math.max(this.gcTime||0,t??(i.sk?1/0:3e5))}clearGcTimeout(){this.#c&&(clearTimeout(this.#c),this.#c=void 0)}}},11255:function(t,e,n){n.d(e,{DV:function(){return l},Kw:function(){return c},Mz:function(){return f}});var i=n(87045),r=n(57853),s=n(16803),o=n(45345);function u(t){return Math.min(1e3*2**t,3e4)}function c(t){return(t??"online")!=="online"||r.N.isOnline()}var a=class extends Error{constructor(t){super("CancelledError"),this.revert=t?.revert,this.silent=t?.silent}};function l(t){return t instanceof a}function f(t){let e,n=!1,l=0,f=!1,h=(0,s.O)(),d=()=>i.j.isFocused()&&("always"===t.networkMode||r.N.isOnline())&&t.canRun(),p=()=>c(t.networkMode)&&t.canRun(),v=n=>{f||(f=!0,t.onSuccess?.(n),e?.(),h.resolve(n))},y=n=>{f||(f=!0,t.onError?.(n),e?.(),h.reject(n))},m=()=>new Promise(n=>{e=t=>{(f||d())&&n(t)},t.onPause?.()}).then(()=>{e=void 0,f||t.onContinue?.()}),b=()=>{let e;if(f)return;let i=0===l?t.initialPromise:void 0;try{e=i??t.fn()}catch(t){e=Promise.reject(t)}Promise.resolve(e).then(v).catch(e=>{if(f)return;let i=t.retry??(o.sk?0:3),r=t.retryDelay??u,s="function"==typeof r?r(l,e):r,c=!0===i||"number"==typeof i&&l<i||"function"==typeof i&&i(l,e);if(n||!c){y(e);return}l++,t.onFail?.(l,e),(0,o._v)(s).then(()=>d()?void 0:m()).then(()=>{n?y(e):b()})})};return{promise:h,cancel:e=>{f||(y(new a(e)),t.abort?.())},continue:()=>(e?.(),h),cancelRetry:()=>{n=!0},continueRetry:()=>{n=!1},canStart:p,start:()=>(p()?b():m().then(b),h)}}},24112:function(t,e,n){n.d(e,{l:function(){return i}});var i=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(t){return this.listeners.add(t),this.onSubscribe(),()=>{this.listeners.delete(t),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}}},16803:function(t,e,n){n.d(e,{O:function(){return i}});function i(){let t,e;let n=new Promise((n,i)=>{t=n,e=i});function i(t){Object.assign(n,t),delete n.resolve,delete n.reject}return n.status="pending",n.catch(()=>{}),n.resolve=e=>{i({status:"fulfilled",value:e}),t(e)},n.reject=t=>{i({status:"rejected",reason:t}),e(t)},n}},45345:function(t,e,n){n.d(e,{CN:function(){return j},Ht:function(){return E},KC:function(){return c},Kp:function(){return u},Nc:function(){return a},PN:function(){return o},Rm:function(){return h},SE:function(){return s},VS:function(){return v},VX:function(){return C},X7:function(){return f},Ym:function(){return d},ZT:function(){return r},_v:function(){return w},_x:function(){return l},cG:function(){return x},oE:function(){return g},sk:function(){return i},to:function(){return p}});var i="undefined"==typeof window||"Deno"in globalThis;function r(){}function s(t,e){return"function"==typeof t?t(e):t}function o(t){return"number"==typeof t&&t>=0&&t!==1/0}function u(t,e){return Math.max(t+(e||0)-Date.now(),0)}function c(t,e){return"function"==typeof t?t(e):t}function a(t,e){return"function"==typeof t?t(e):t}function l(t,e){let{type:n="all",exact:i,fetchStatus:r,predicate:s,queryKey:o,stale:u}=t;if(o){if(i){if(e.queryHash!==h(o,e.options))return!1}else if(!p(e.queryKey,o))return!1}if("all"!==n){let t=e.isActive();if("active"===n&&!t||"inactive"===n&&t)return!1}return("boolean"!=typeof u||e.isStale()===u)&&(!r||r===e.state.fetchStatus)&&(!s||!!s(e))}function f(t,e){let{exact:n,status:i,predicate:r,mutationKey:s}=t;if(s){if(!e.options.mutationKey)return!1;if(n){if(d(e.options.mutationKey)!==d(s))return!1}else if(!p(e.options.mutationKey,s))return!1}return(!i||e.state.status===i)&&(!r||!!r(e))}function h(t,e){return(e?.queryKeyHashFn||d)(t)}function d(t){return JSON.stringify(t,(t,e)=>m(e)?Object.keys(e).sort().reduce((t,n)=>(t[n]=e[n],t),{}):e)}function p(t,e){return t===e||typeof t==typeof e&&!!t&&!!e&&"object"==typeof t&&"object"==typeof e&&!Object.keys(e).some(n=>!p(t[n],e[n]))}function v(t,e){if(!e||Object.keys(t).length!==Object.keys(e).length)return!1;for(let n in t)if(t[n]!==e[n])return!1;return!0}function y(t){return Array.isArray(t)&&t.length===Object.keys(t).length}function m(t){if(!b(t))return!1;let e=t.constructor;if(void 0===e)return!0;let n=e.prototype;return!!(b(n)&&n.hasOwnProperty("isPrototypeOf"))&&Object.getPrototypeOf(t)===Object.prototype}function b(t){return"[object Object]"===Object.prototype.toString.call(t)}function w(t){return new Promise(e=>{setTimeout(e,t)})}function g(t,e,n){return"function"==typeof n.structuralSharing?n.structuralSharing(t,e):!1!==n.structuralSharing?function t(e,n){if(e===n)return e;let i=y(e)&&y(n);if(i||m(e)&&m(n)){let r=i?e:Object.keys(e),s=r.length,o=i?n:Object.keys(n),u=o.length,c=i?[]:{},a=0;for(let s=0;s<u;s++){let u=i?s:o[s];(!i&&r.includes(u)||i)&&void 0===e[u]&&void 0===n[u]?(c[u]=void 0,a++):(c[u]=t(e[u],n[u]),c[u]===e[u]&&void 0!==e[u]&&a++)}return s===u&&a===s?e:c}return n}(t,e):e}function C(t,e,n=0){let i=[...t,e];return n&&i.length>n?i.slice(1):i}function E(t,e,n=0){let i=[e,...t];return n&&i.length>n?i.slice(0,-1):i}var j=Symbol();function x(t,e){return!t.queryFn&&e?.initialPromise?()=>e.initialPromise:t.queryFn&&t.queryFn!==j?t.queryFn:()=>Promise.reject(Error(`Missing queryFn: '${t.queryHash}'`))}},29827:function(t,e,n){n.d(e,{NL:function(){return o},aH:function(){return u}});var i=n(2265),r=n(57437),s=i.createContext(void 0),o=t=>{let e=i.useContext(s);if(t)return t;if(!e)throw Error("No QueryClient set, use QueryClientProvider to set one");return e},u=t=>{let{client:e,children:n}=t;return i.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),(0,r.jsx)(s.Provider,{value:e,children:n})}},64131:function(t,e,n){function i(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var i in n)t[i]=n[i]}return t}n.d(e,{Z:function(){return r}});var r=function t(e,n){function r(t,r,s){if("undefined"!=typeof document){"number"==typeof(s=i({},n,s)).expires&&(s.expires=new Date(Date.now()+864e5*s.expires)),s.expires&&(s.expires=s.expires.toUTCString()),t=encodeURIComponent(t).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape);var o="";for(var u in s)s[u]&&(o+="; "+u,!0!==s[u]&&(o+="="+s[u].split(";")[0]));return document.cookie=t+"="+e.write(r,t)+o}}return Object.create({set:r,get:function(t){if("undefined"!=typeof document&&(!arguments.length||t)){for(var n=document.cookie?document.cookie.split("; "):[],i={},r=0;r<n.length;r++){var s=n[r].split("="),o=s.slice(1).join("=");try{var u=decodeURIComponent(s[0]);if(i[u]=e.read(o,u),t===u)break}catch(t){}}return t?i[t]:i}},remove:function(t,e){r(t,"",i({},e,{expires:-1}))},withAttributes:function(e){return t(this.converter,i({},this.attributes,e))},withConverter:function(e){return t(i({},this.converter,e),this.attributes)}},{attributes:{value:Object.freeze(n)},converter:{value:Object.freeze(e)}})}({read:function(t){return'"'===t[0]&&(t=t.slice(1,-1)),t.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent)},write:function(t){return encodeURIComponent(t).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,decodeURIComponent)}},{path:"/"})}}]);