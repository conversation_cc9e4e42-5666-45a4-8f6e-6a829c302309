(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2184],{2552:function(e,a,t){Promise.resolve().then(t.bind(t,10163)),Promise.resolve().then(t.bind(t,63372)),Promise.resolve().then(t.bind(t,27613)),Promise.resolve().then(t.bind(t,97867)),Promise.resolve().then(t.bind(t,31085))},10163:function(e,a,t){"use strict";t.d(a,{default:function(){return u}});var n=t(57437),l=t(47521),s=t(13465),r=t(42586),i=t(64748),o=t(50408),d=t(2265),c=t(62102);function u(e){var a,t,u,m,g,x,p,h,v,f;let{query:y,types:j,conversions:w,...b}=e,N=(0,r.useTranslations)("seeker"),k=(0,r.useLocale)(),_=(0,o.h)(),S=(0,c.w)(e=>e.setHighlightedListing),{query:C}=(0,i.Q)({page:b.page.toString(),per_page:b.perPage||"16",search:null==y?void 0:y.replaceAll("--"," ").replaceAll("-",", "),type:j.split(","),bathroom_total:b.bathroomTotal,bedroom_total:b.bedroomTotal,max_price:b.maxPrice,min_price:0==b.minPrice?1:b.minPrice,years_of_building:b.yearsOfBuilding,area:b.lat&&b.lng?{latitude:b.lat,longitude:b.lng,zoom:b.zoom||"13"}:void 0,rental_offers:null===(a=b.rentalOffers)||void 0===a?void 0:a.split(","),selling_points:null===(t=b.sellingPoints)||void 0===t?void 0:t.split(","),features:null===(u=b.feature)||void 0===u?void 0:u.split(","),sort_by:b.sortBy,building_largest:b.buildingLargest,building_smallest:0==b.buildingSmallest?1:b.buildingSmallest,garden_largest:b.gardenLargest,garden_smallest:0==b.gardenSmallest?1:b.gardenSmallest,land_largest:b.LandLargest,land_smallest:0==b.LandSmallest?1:b.LandSmallest,electricity:b.electricity,parking_option:b.parkingOption,pool_option:b.poolOption,living_option:b.typeLiving,furnishing_option:b.furnishingOption,property_of_view:null===(m=b.propertyOfView)||void 0===m?void 0:m.split(","),location_type:b.propertyLocation,contract_duration:b.minimumContract},!0,k);return(0,d.useEffect)(()=>{if(C.isError)return _.setIsLoading(!1)},[C.isError]),(0,d.useEffect)(()=>{var e,a;_.setIsLoading(C.isPending),C.isSuccess&&(_.setData((null===(e=C.data)||void 0===e?void 0:e.data)||[]),_.setTotal((null===(a=C.data.meta)||void 0===a?void 0:a.total)||0))},[null===(g=C.data)||void 0===g?void 0:g.data]),(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("section",{className:"min-h-[calc(100vh-202px)]",children:(0,n.jsx)("div",{className:"grid md:grid-cols-2 xl:grid-cols-3 gap-3 max-sm:px-4 max-sm:my-4 md:mr-6 gap-x-3 gap-y-6",children:C.isPending?Array(12).fill(0).map((e,a)=>(0,n.jsx)(l.yZ,{},a)):_.data&&_.data.length>0?(0,n.jsx)(n.Fragment,{children:_.data.map((e,a)=>(0,n.jsx)("div",{onMouseEnter:()=>{S(e.code)},children:(0,n.jsxs)(l.yd,{className:"space-y-3",data:e,conversion:w,children:[(0,n.jsx)(l.Zf,{heartSize:"large"}),(0,n.jsxs)("div",{className:"space-y-2 px-0.5",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)(l.xI,{}),(0,n.jsx)(l.I5,{className:"!-mt-1"})]}),(0,n.jsx)(l.$D,{className:"text-seekers-text"}),(0,n.jsx)(l.hj,{})]})]})},a))}):(0,n.jsx)(n.Fragment,{children:(0,n.jsx)("p",{className:"col-span-full text-center font-semibold py-8",children:N("listing.misc.propertyNotFound")})})})}),(0,n.jsx)("section",{className:"!mt-12",children:C.isPending||(null===(p=C.data)||void 0===p?void 0:null===(x=p.data)||void 0===x?void 0:x.length)&&(null===(v=C.data)||void 0===v?void 0:null===(h=v.data)||void 0===h?void 0:h.length)<16&&1==C.data.meta.pageCount?(0,n.jsx)(n.Fragment,{}):(0,n.jsx)("div",{className:"w-fit mx-auto",children:(0,n.jsx)(s.g,{meta:null==C?void 0:null===(f=C.data)||void 0===f?void 0:f.meta,totalThreshold:16,disableRowPerPage:!0})})})]})}},92324:function(e,a,t){"use strict";t.d(a,{Z:function(){return h}});var n=t(57437),l=t(57612),s=t(94508),r=t(72227),i=t(18133),o=t(14938),d=t(95252),c=t(81197),u=t(67410),m=t(16275),g=t(56096),x=t(75745),p=t(86595);function h(e){let{category:a,className:t}=e;switch(a){case l.yJ.villa:case l.yJ.villas:return(0,n.jsx)(r.Z,{className:(0,s.cn)("!w-6 !h-6",t)});case l.yJ.apartment:return(0,n.jsx)(i.Z,{className:(0,s.cn)("!w-6 !h-6",t)});case l.yJ.homestay:case l.yJ.guestHouse:case l.yJ.rooms:return(0,n.jsx)(o.Z,{className:(0,s.cn)("!w-6 !h-6",t)});case l.yJ.ruko:case l.yJ.commercialSpace:return(0,n.jsx)(d.Z,{className:(0,s.cn)("!w-6 !h-6",t)});case l.yJ.cafeOrRestaurants:return(0,n.jsx)(c.Z,{className:(0,s.cn)("!w-6 !h-6",t)});case l.yJ.offices:return(0,n.jsx)(u.Z,{className:(0,s.cn)("!w-6 !h-6",t)});case l.yJ.shops:return(0,n.jsx)(m.Z,{className:(0,s.cn)("!w-6 !h-6",t)});case l.yJ.shellAndCore:return(0,n.jsx)(g.Z,{className:(0,s.cn)("!w-6 !h-6",t)});case l.yJ.lands:return(0,n.jsx)(x.Z,{className:(0,s.cn)("!w-6 !h-6",t)});default:return(0,n.jsx)(p.Z,{className:(0,s.cn)("!w-6 !h-6",t)})}}},63372:function(e,a,t){"use strict";t.d(a,{default:function(){return y}});var n=t(57437),l=t(14938),s=t(42586),r=t(94508),i=t(2265),o=t(98482),d=t(20653);let c=i.forwardRef((e,a)=>{let{...t}=e;return(0,n.jsx)("nav",{ref:a,"aria-label":"breadcrumb",...t})});c.displayName="Breadcrumb";let u=i.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,n.jsx)("ol",{ref:a,className:(0,r.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",t),...l})});u.displayName="BreadcrumbList";let m=i.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,n.jsx)("li",{ref:a,className:(0,r.cn)("inline-flex items-center gap-1.5",t),...l})});m.displayName="BreadcrumbItem",i.forwardRef((e,a)=>{let{asChild:t,className:l,...s}=e,i=t?o.g7:"a";return(0,n.jsx)(i,{ref:a,className:(0,r.cn)("transition-colors hover:text-foreground",l),...s})}).displayName="BreadcrumbLink",i.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,n.jsx)("span",{ref:a,role:"link","aria-disabled":"true","aria-current":"page",className:(0,r.cn)("font-normal text-foreground",t),...l})}).displayName="BreadcrumbPage";let g=e=>{let{children:a,className:t,...l}=e;return(0,n.jsx)("li",{role:"presentation","aria-hidden":"true",className:(0,r.cn)("[&>svg]:w-3.5 [&>svg]:h-3.5",t),...l,children:null!=a?a:(0,n.jsx)(d.XCv,{})})};g.displayName="BreadcrumbSeparator";var x=t(27668),p=t(74953),h=t(71635),v=t(91430),f=t(51179);function y(e){let{query:a,types:t,conversions:i}=e,o=(0,s.useTranslations)("seeker"),{propertyTypeFormatHelper:d}=(0,f.Z)();return(0,n.jsxs)(x.Z,{className:(0,r.cn)("max-sm:hidden flex max-sm:flex-col items-center space-y-0 h-[100px] max-lg:!h-[80px]  gap-4 sticky md:top-[90px] lg:top-[104px] xl:top-[114px] z-[1] bg-white"),children:[(0,n.jsxs)("div",{className:"flex-grow space-y-2",children:[(0,n.jsx)(h.Z,{}),(0,n.jsx)(c,{className:" hidden md:block ",children:(0,n.jsxs)(u,{className:"space-x-4 sm:gap-0 flex-nowrap",children:[(0,n.jsx)(m,{className:"text-seekers-text font-medium text-sm",children:(0,n.jsx)(v.rU,{href:"/",className:"flex gap-2.5 items-center",children:(0,n.jsx)(l.Z,{className:"w-4 h-4",strokeWidth:1})})}),(0,n.jsx)(g,{className:"text-seekers-text font-medium text-sm w-3 h-fit text-center",children:"/"}),"all"==a?(0,n.jsx)(n.Fragment,{}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(m,{className:"capitalize text-seekers-text font-medium text-sm",children:a.replaceAll("-"," ").replaceAll("--"," ")}),(0,n.jsx)(g,{className:"text-seekers-text font-medium text-sm w-3 h-fit text-center",children:"/"})]}),(0,n.jsx)(m,{className:"text-seekers-text font-semibold text-sm line-clamp-1",children:t.split(",").includes("all")?(0,n.jsx)(n.Fragment,{children:o("misc.allProperty")}):(0,n.jsx)(n.Fragment,{children:d(t.replaceAll("-"," ").replaceAll("--"," ").split(",")).toString().replaceAll(",",", ")})})]})})]}),(0,n.jsx)(p.default,{conversions:i})]})}},27613:function(e,a,t){"use strict";t.d(a,{default:function(){return L}});var n=t(57437),l=t(50408),s=t(1322),r=t(2265),i=t(47521),o=t(62869),d=t(32489),c=t(62102),u=t(92324),m=t(94508);function g(e){let{data:a,conversions:t}=e,[l,r]=(0,s.Rt)(),{focusedListing:g,setFocusedListing:x,highlightedListing:p}=(0,c.w)();return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(s._Q,{position:{lat:a.geolocation[0],lng:a.geolocation[1]},clickable:!0,onClick:()=>x(a.code),ref:l,zIndex:p==a.code?10:1,children:(0,n.jsx)("div",{className:(0,m.cn)(p==a.code?"w-12 h-12 bg-seekers-text text-white":"w-6 h-6 bg-white","flex items-center justify-center py-3 rounded-full text-sm font-medium shadow-md border"),children:(0,n.jsx)(u.Z,{category:a.category||"",className:p==a.code?"":"!w-4 !h-4 text-seekers-primary"})})},a.code),g==a.code&&(0,n.jsx)(n.Fragment,{children:(0,n.jsx)(s.nx,{anchor:r,onClose:()=>x(null),headerDisabled:!0,style:{overflow:"auto !important",borderRadius:"24px !important"},className:"!rounded-xl",minWidth:240,children:(0,n.jsxs)(i.yd,{conversion:t,data:a,children:[(0,n.jsx)(i.Zf,{forceLazyloading:!0,containerClassName:"!rounded-b-none",extraHeaderAction:(0,n.jsx)(n.Fragment,{children:(0,n.jsx)(o.z,{variant:"secondary",className:"bg-white rounded-full !h-6 !w-6 hover:bg-white/80",size:"icon",onClick:()=>x(null),children:(0,n.jsx)(d.Z,{className:"!w-3 !h-3"})})})}),(0,n.jsxs)("div",{className:"space-y-2 px-2 pb-2",children:[(0,n.jsx)(i.xI,{className:"leading-6"}),(0,n.jsx)(i.I5,{}),(0,n.jsx)(i.hj,{})]})]})})})]})}var x=t(8450);let p=e=>{let{data:a,onClick:t,setMarkerRef:l}=e,{focusedListing:i,setFocusedListing:o}=(0,c.w)(),d=(0,r.useCallback)(()=>{t(a),o(a.code)},[t,a,o]),g=(0,r.useCallback)(e=>l(e,a.code),[l,a.code]);return(0,n.jsx)(s._Q,{position:{lat:a.geolocation[0],lng:a.geolocation[1]},ref:g,onClick:d,children:(0,n.jsx)("div",{className:(0,m.cn)(i==a.code?"w-16 h-16 bg-seekers-text text-white":"hover:w-10 hover:h-10 w-8 h-8 bg-white","flex items-center justify-center py-3 rounded-full text-sm font-medium shadow-md border"),children:(0,n.jsx)(u.Z,{category:a.category||"",className:i==a.code?"":"!w-4 !h-4 text-seekers-primary"})})})},h=e=>{let{data:a,conversions:t}=e,[l,u]=(0,r.useState)({}),{focusedListing:m,setFocusedListing:g}=(0,c.w)(),h=(0,c.w)(e=>e.viewMode),[v,f]=(0,r.useState)(),y=(0,s.Sx)(),j=(0,r.useMemo)(()=>y&&"list"!==h?new x.XL({map:y,renderer:{render:e=>{let a=document.createElement("div");return a.style.width="36px",a.style.height="36px",a.style.backgroundColor="#B48B55",a.style.borderRadius="50%",a.style.display="flex",a.style.alignItems="center",a.style.justifyContent="center",a.style.color="#FFFFFF",a.style.fontWeight="bold",a.style.fontSize="14px",a.textContent=e.count.toString(),new google.maps.marker.AdvancedMarkerElement({position:e.position,content:a})}}}):null,[y,h]);(0,r.useEffect)(()=>{if(j)return null==j||j.clearMarkers(),j.addMarkers(Object.values(l)),()=>{j.removeMarkers(Object.values(l))}},[j,l,h]);let w=(0,r.useCallback)((e,a)=>{u(t=>{if(e&&t[a]||!e&&!t[a])return t;if(e)return{...t,[a]:e};{let{[a]:e,...n}=t;return n}})},[]),b=(0,r.useCallback)(()=>{g(null)},[g]),N=(0,r.useCallback)(e=>{g(e.code),f(e)},[g]);return(0,n.jsx)(n.Fragment,{children:a.map(e=>(0,n.jsxs)(r.Fragment,{children:[(0,n.jsx)(p,{data:e,onClick:N,setMarkerRef:w},e.code),m==e.code&&(0,n.jsx)(s.nx,{anchor:l[e.code],onCloseClick:b,headerDisabled:!0,style:{overflow:"auto !important",borderRadius:"24px !important"},className:"!rounded-xl",minWidth:240,maxWidth:240,children:(0,n.jsxs)(i.yd,{data:e,conversion:t,children:[(0,n.jsx)(i.Zf,{forceLazyloading:!0,containerClassName:"!rounded-b-none",extraHeaderAction:(0,n.jsx)(n.Fragment,{children:(0,n.jsx)(o.z,{variant:"secondary",className:"bg-white rounded-full !h-6 !w-6 hover:bg-white/80",size:"icon",onClick:()=>g(null),children:(0,n.jsx)(d.Z,{className:"!w-3 !h-3"})})})}),(0,n.jsxs)("div",{className:"space-y-2 px-2 pb-2",children:[(0,n.jsx)(i.xI,{className:"leading-6"}),(0,n.jsx)(i.I5,{}),(0,n.jsx)(i.hj,{})]})]})})]},e.code))})};var v=t(16850),f=t(71363),y=t(62398),j=t(35153),w=t(6404),b=t(30078),N=t(89047);function k(e){let{conversions:a}=e,{createMultipleQueryString:t,searchParams:i}=(0,f.Z)(),{data:o}=(0,l.h)(),{seekers:d}=(0,b.L)(),u=(0,s.Sx)(),{setFocusedListing:m}=(0,c.w)(),[x,p]=(0,r.useState)(),k=(0,v.N)(x),[_,S]=(0,r.useState)(!1),[C,Z]=(0,r.useState)(12),{toast:z}=(0,j.pm)();return(0,r.useEffect)(()=>{if(o.length>1){let e=o[0];null==u||u.moveCamera({center:{lat:e.geolocation[0],lng:e.geolocation[1]}})}},[o]),(0,r.useEffect)(()=>{if(null!=k&&"list"!=i.get(w.Y.viewMode)&&null!=i.get(w.Y.viewMode))t([{name:"lat",value:k[0].toString()},{name:"lng",value:k[1].toString()},{name:w.Y.zoom,value:k[2].toString()}])},[k]),(0,n.jsxs)("div",{className:"rounded-lg overflow-hidden relative w-full h-full",children:[_&&(0,n.jsx)(n.Fragment,{children:(0,n.jsx)(y.Z,{})}),(0,n.jsx)(s.D5,{reuseMaps:!0,mapId:"7c91273179940241",style:{width:"100%",height:"100%"},mapTypeControl:!1,fullscreenControl:!1,defaultZoom:12,defaultCenter:{lat:-8.639736,lng:115.1341357},maxZoom:d.accounts.zoomFeature.max,minZoom:d.accounts.zoomFeature.min,disableDefaultUI:!0,onDragend:e=>{if(e.map.getCenter()){var a,t;p([null===(a=e.map.getCenter())||void 0===a?void 0:a.lat(),null===(t=e.map.getCenter())||void 0===t?void 0:t.lng(),e.map.getZoom()]),m(null)}},onZoomChanged:e=>{var a,t;e.detail.zoom>=d.accounts.zoomFeature.max&&C!==e.detail.zoom&&d.accounts.membership===N.B9.free?S(!0):S(!1),p([null===(a=e.map.getCenter())||void 0===a?void 0:a.lat(),null===(t=e.map.getCenter())||void 0===t?void 0:t.lng(),e.map.getZoom()]),Z(e.map.getZoom()),m(null)},children:"list"==i.get(w.Y.viewMode)||null==i.get(w.Y.viewMode)?o.map(e=>(0,n.jsx)(g,{conversions:a,data:e},e.code)):(0,n.jsx)(h,{conversions:a,data:o})})]})}var _=t(92451),S=t(10407),C=t(52048),Z=t(7586),z=t(42586),B=t(74953),R=t(71635),F=t(27668);function L(e){let{children:a,conversions:t}=e,l=(0,z.useTranslations)("seeker"),{createQueryString:s,createMultipleQueryString:i,searchParams:d}=(0,f.Z)(),[u,g]=(0,r.useState)(!0),x=(0,c.w)(e=>e.setViewMode),p=(0,c.w)(e=>e.setHighlightedListing);(0,r.useEffect)(()=>{let e=d.get(w.Y.viewMode);e&&"list"!=e?(g(!1),x("map")):(g(!0),x("list"))},[d,x]);let h=()=>{"map"==d.get(w.Y.viewMode)?(s(w.Y.viewMode,"list"),g(!1)):(s(w.Y.viewMode,"map"),g(!0)),p(null),window.scrollTo({top:0})};return(0,n.jsxs)(F.Z,{className:"space-y-0 max-sm:px-0",children:[(0,n.jsxs)("div",{className:"hidden md:flex mb-6 mx-auto h-full",children:[(0,n.jsx)("div",{className:(0,m.cn)("",u?"flex-1 ":"w-0 hidden"),children:a}),(0,n.jsx)("div",{className:(0,m.cn)("sticky  md:top-[182px] lg:top-[208px] xl:top-[220px] h-[calc(100vh-220px)]",u?"min-w-[28%]":"w-full"),children:(0,n.jsxs)("div",{className:"w-full h-full relative",children:[(0,n.jsx)(o.z,{className:"absolute z-20 top-4 left-4",size:"icon",onClick:()=>{h()},variant:"outline",children:u?(0,n.jsx)(_.Z,{className:"!w-5 !h-5 !text-seekers-primary"}):(0,n.jsx)(S.Z,{className:"!w-5 !h-5 !text-seekers-primary"})}),(0,n.jsx)(k,{lat:-8.535522079444435,lng:115.2228026405029,conversions:t})]})})]}),(0,n.jsxs)("div",{className:"md:hidden isolate",children:[u?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:" p-4 sticky space-y-2 top-[140px] z-10 bg-white",children:[(0,n.jsx)(B.default,{conversions:t}),(0,n.jsx)(R.Z,{})]}),a]}):(0,n.jsx)("div",{className:(0,m.cn)("sticky h-[calc(100vh-176px)]"),children:(0,n.jsx)("div",{className:"w-full h-full relative",children:(0,n.jsx)(k,{lat:-8.535522079444435,lng:115.2228026405029,conversions:t})})}),(0,n.jsx)("button",{className:"inline-flex items-center gap-2 sticky z-10 bottom-4 left-1/2 -translate-x-1/2 text-white bg-seekers-text p-4 rounded-full",onClick:()=>h(),children:u?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(C.Z,{})," ",(0,n.jsx)("span",{children:l("cta.maps")})]}):(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(Z.Z,{})," ",(0,n.jsx)("span",{children:l("cta.list")})]})})]})]})}},71635:function(e,a,t){"use strict";t.d(a,{Z:function(){return o}});var n=t(57437),l=t(93022),s=t(50408),r=t(62102),i=t(42586);function o(){let{highlightedListing:e}=(0,r.w)(),{total:a,isLoading:t}=(0,s.h)(),o=(0,i.useTranslations)("seeker");return(0,n.jsx)("h3",{className:"font-semibold max-sm:text-xs text-xl text-seekers-text",children:t?(0,n.jsx)(n.Fragment,{children:(0,n.jsx)(l.O,{className:"w-40 h-6"})}):0==a?o("listing.misc.searchNoResultCount"):o("listing.misc.searchResultCount",{count:a})})}},27668:function(e,a,t){"use strict";t.d(a,{Z:function(){return s}});var n=t(57437),l=t(94508);function s(e){return(0,n.jsx)("div",{...e,ref:e.ref,className:(0,l.cn)("xl:max-w-screen-2xl max-md:px-4 px-8 mx-auto space-y-12",e.className),children:e.children})}},62398:function(e,a,t){"use strict";t.d(a,{Z:function(){return m}});var n=t(57437),l=t(94508),s=t(65613),r=t(62102),i=t(91430),o=t(62869),d=t(42586),c=t(71517),u=t(30078);function m(e){let{isSubscribe:a,className:t}=e,m=(0,r.w)(e=>e.viewMode),{email:g}=(0,u.L)(e=>e.seekers),x=(0,d.useTranslations)("seeker");return(0,n.jsx)(n.Fragment,{children:a?(0,n.jsx)(n.Fragment,{}):(0,n.jsx)(s.bZ,{className:(0,l.cn)("max-w-xs bg-[#B48B5599] font-semibold text-white  shadow-md absolute   z-10","map"==m?"top-4 right-4 max-sm:right-1/2 max-sm:translate-x-1/2":"top-16 right-1/2 translate-x-1/2",t),children:(0,n.jsxs)(s.X,{className:"text-xs",children:[x("misc.subscibePropgram.searchPage.description")," "," ",(0,n.jsx)(o.z,{asChild:!0,variant:"link",size:"sm",className:"p-0 h-fit w-fit text-white underline",children:(0,n.jsx)(i.rU,{href:g?c.OM:c.GA,children:x("cta.subscribe")})})]})})})}},65613:function(e,a,t){"use strict";t.d(a,{X:function(){return d},bZ:function(){return o}});var n=t(57437),l=t(2265),s=t(90535),r=t(94508);let i=(0,s.j)("relative w-full rounded-lg border px-4 py-3 text-xs [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground [&>svg~*]:pl-7",{variants:{variant:{default:"bg-background text-neutral",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=l.forwardRef((e,a)=>{let{className:t,variant:l,...s}=e;return(0,n.jsx)("div",{ref:a,role:"alert",className:(0,r.cn)(i({variant:l}),t),...s})});o.displayName="Alert",l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,n.jsx)("h5",{ref:a,className:(0,r.cn)("mb-1 font-medium tracking-tight",t),...l})}).displayName="AlertTitle";let d=l.forwardRef((e,a)=>{let{className:t,...l}=e;return(0,n.jsx)("div",{ref:a,className:(0,r.cn)("text-sm [&_p]:leading-relaxed",t),...l})});d.displayName="AlertDescription"},64748:function(e,a,t){"use strict";t.d(a,{Q:function(){return c}});var n=t(39392),l=t(16593),s=t(2069),r=t(57612),i=t(8946),o=t.n(i),d=t(45558);function c(e){var a;let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"en",c=(0,s.Q)(),u=null==c?void 0:null===(a=c.data)||void 0===a?void 0:a.data,m=["filtered-seekers-listing",e],{failureCount:g,...x}=(0,l.a)({queryKey:m,queryFn:async()=>{var a,t,l,s;let c=e.max_price||(null==u?void 0:u.priceRange.max),m=e.min_price||(null==u?void 0:u.priceRange.min)||1,g=e.building_largest||(null==u?void 0:u.buildingSizeRange.max),x=e.building_smallest||(null==u?void 0:u.buildingSizeRange.min)||1,p=e.land_largest||(null==u?void 0:u.landSizeRange.max),h=e.land_smallest||(null==u?void 0:u.landSizeRange.min)||1,v=e.garden_largest||(null==u?void 0:u.gardenSizeRange.max),f=e.garden_smallest||(null==u?void 0:u.gardenSizeRange.min)||1,y=e.area;(null===(a=e.area)||void 0===a?void 0:a.zoom)==d.lJ.toString()&&(y=void 0);let j=(null===(t=e.type)||void 0===t?void 0:t.includes("all"))?void 0:o().uniq(null===(l=e.type)||void 0===l?void 0:l.flatMap(e=>e!==r.yJ.commercialSpace?e:[r.yJ.cafeOrRestaurants,r.yJ.shops,r.yJ.offices])),w={...e,type:j,search:"all"==e.search?void 0:null===(s=e.search)||void 0===s?void 0:s.replaceAll(" , ",", "),min_price:m,max_price:c,building_largest:g,building_smallest:x,land_largest:p,land_smallest:h,garden_largest:v,garden_smallest:f,area:y||void 0,property_of_view:e.property_of_view};return e.min_price&&e.min_price!=(null==u?void 0:u.priceRange.min)||c!=(null==u?void 0:u.priceRange.max)||(w.max_price=void 0,w.min_price=void 0),e.building_smallest&&e.building_smallest!=(null==u?void 0:u.buildingSizeRange.min)||g!=(null==u?void 0:u.buildingSizeRange.max)||(w.building_largest=void 0,w.building_smallest=void 0),e.land_smallest&&e.land_smallest!=(null==u?void 0:u.landSizeRange.min)||p!=(null==u?void 0:u.landSizeRange.max)||(w.land_largest=void 0,w.land_smallest=void 0),e.garden_smallest&&e.garden_smallest!=(null==u?void 0:u.gardenSizeRange.min)||v!=(null==u?void 0:u.gardenSizeRange.max)||(w.garden_largest=void 0,w.garden_smallest=void 0),await (0,n.p)(w,i)},enabled:t,retry:!1});return{query:x,filterQueryKey:m}}},51179:function(e,a,t){"use strict";t.d(a,{Z:function(){return u}});var n=t(74316),l=t(2265),s=t(71517),r=t(94508),i=t(75189),o=t(42586),d=t(57612),c=t(6404);function u(){let e=(0,o.useTranslations)(),a=(0,n.V)(e=>e),[t,u]=(0,l.useState)(!1),[m,g]=(0,l.useState)(null),x=(0,i.useRouter)(),p=[{content:e("seeker.listing.category.villa"),id:"1",value:d.yJ.villas},{content:e("seeker.listing.category.apartment"),id:"2",value:d.yJ.apartment},{content:e("seeker.listing.category.guestHouse"),id:"3",value:d.yJ.rooms},{content:e("seeker.listing.category.commercial"),id:"4",value:d.yJ.commercialSpace},{content:e("seeker.listing.category.cafeAndRestaurent"),id:"5",value:d.yJ.cafeOrRestaurants},{content:e("seeker.listing.category.office"),id:"6",value:d.yJ.offices},{content:e("seeker.listing.category.shops"),id:"7",value:d.yJ.shops},{content:e("seeker.listing.category.shellAndCore"),id:"8",value:d.yJ.shellAndCore},{content:e("seeker.listing.category.land"),id:"9",value:d.yJ.lands}],h=[{name:"Canggu, Bali",description:"Popular surf spot & digital nomad hub",icon:"Canggu",value:"canggu"},{name:"Ubud, Bali",description:"Cultural heart with rice terraces",value:"ubud",icon:"Ubud"},{name:"Seminyak, Bali",description:"Upscale beach resort area",icon:"Seminyak",value:"seminyak"},{name:"Uluwatu, Bali",description:"Clifftop temples & luxury resorts",icon:"Uluwatu",value:"uluwatu"},{name:"Nusa Dua, Bali",description:"Gated resort area with pristine beaches",icon:"NusaDua",value:"Nusa Dua"}],v={canggu:["Babakan","Batu Bolong","Berawa","Cemagi","Cempaka","Echo Beach","Kayu Tulang","Munggu","Nelayan","North Canggu","Nyanyi","Padonan","Pantai Lima","Pererenan","Seseh","Tiying Tutul","Tumbak Bayuh"].sort(),ubud:["Bentuyung","Junjungan","Kedewatan","Nyuh Kuning","Penestanan","Sambahan","Sanggingan","Taman Kaja","Tegallantang","Ubud Center"],uluwatu:["Balangan","Bingin","Green Bowl","Karang Boma","Nyang Nyang","Padang Padang","Pecatu","Suluban"],nusaDua:["Benoa","BTDC Area","Bualu","Kampial","Peminge","Sawangan","Tanjung Benoa"],seminyak:[]},f=(0,l.useMemo)(()=>!a.query||t?h:h.filter(e=>{let t=e.name.replace(", Bali","").toLowerCase(),n=a.query.toLowerCase();return!!t.includes(n)||(v[e.value]||[]).some(e=>e.toLowerCase().includes(n))}),[a.query,t]);return{seekersSearch:a,handleSetQuery:e=>{let t=e.split(","),n=e.length;t.length>3&&","==e.charAt(n-1)||a.setQuery(e)},handleSetType:e=>{(!(a.propertyType.length>=3)||a.propertyType.includes(e))&&a.setPropertyType(e)},propertyType:p,handleSearch:(e,t)=>{e&&a.setQuery(e),t&&a.setPropertyTypeFromArray(t);let n=e||a.query,l=t||a.propertyType;""!==a.activeSearch.query&&a.setSearchHistory({propertyType:a.activeSearch.propertyType,query:a.activeSearch.query}),a.setActiveSearch({query:n,propertyType:l});let i=(0,r.Fg)(n);x.push(s.rr+"/"+(i||"all")+"?"+c.Y.type+"="+(l.toString()||"all"))},propertyTypeFormatHelper:e=>e.map(e=>{let a=p.find(a=>a.value==e);return null==a?void 0:a.content}),locations:h,banjars:v,getMatchingBanjars:e=>{let t=a.query;return t?(v[e]||[]).filter(e=>e.toLowerCase().includes(t.toLowerCase())):[]},showBanjars:t,setShowBanjars:u,selectedLocation:m,setSelectedLocation:g,handleSelectLocation:e=>{u(!0),g(e),a.setQuery(e)},handleBackToLocations:()=>{u(!1);let e=a.query.replace(m||"","");a.setQuery(e)},handleSetBanjar:function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=a.query.split(",").filter(e=>""!==e.trim()&&e!==m);if(n.includes(e)){let t=n.filter(a=>a!==e);a.setQuery(t.toString());return}if(!(n.length>=3)||""===n[n.length-1]){if(t){let a=n.length;n[a-1]=e}else n.push(e);a.setQuery(n.toString())}},filteredLocations:f}}},62102:function(e,a,t){"use strict";t.d(a,{w:function(){return n}});let n=(0,t(59625).Ue)()(e=>({focusedListing:null,highlightedListing:null,zoom:13,mapVariantId:0,viewMode:"list",setViewMode:a=>e(()=>({viewMode:a})),setMapVariantId:a=>e(()=>({mapVariantId:a})),setZoom:a=>e(()=>({zoom:a})),setFocusedListing:a=>e(()=>({focusedListing:a})),setHighlightedListing:a=>e(()=>({highlightedListing:a}))}))}},function(e){e.O(0,[6990,8310,7699,680,1866,6290,8094,2586,2957,4956,3448,8658,6088,9623,3398,6205,4216,3682,3145,1298,4461,7060,4797,6245,1322,8750,3675,8725,8100,3267,9389,2971,2117,1744],function(){return e(e.s=2552)}),_N_E=e.O()}]);