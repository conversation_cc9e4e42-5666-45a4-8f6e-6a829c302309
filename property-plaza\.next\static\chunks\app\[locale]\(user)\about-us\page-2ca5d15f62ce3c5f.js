(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2105],{44662:function(e,t,s){Promise.resolve().then(s.bind(s,23996)),Promise.resolve().then(s.bind(s,97867)),Promise.resolve().then(s.bind(s,31085))},23996:function(e,t,s){"use strict";s.d(t,{default:function(){return h}});var a=s(57437),r=s(2265),i=s(42586),n=s(83774),o=s(89345),l=s(13041),m=s(27668),c=s(33145),d=s(62869),u=s(91430),b=s(29985),x={src:"/_next/static/media/office-building.73328fb0.webp",height:2560,width:1920,blurDataURL:"data:image/webp;base64,UklGRlIAAABXRUJQVlA4IEYAAADwAQCdASoGAAgAAkA4JYgCdAD0Y7s8OoAA/vj0FNF0Yy42grTicPZx5nQtif1x0Jkyvbx4Z5yYy2WdVGDoiwyt6c2QAAAA",blurWidth:6,blurHeight:8};function h(){let e=(0,i.useTranslations)("seeker"),[t,s]=(0,r.useState)("company");return(0,a.jsx)(a.Fragment,{children:(0,a.jsxs)("div",{className:"w-full bg-white",children:[(0,a.jsxs)("section",{"aria-label":"About Property Plaza Hero",className:"relative w-full h-[400px] md:h-[500px]",children:[(0,a.jsx)(c.default,{src:b.default,alt:"Property Plaza",fill:!0,className:"object-cover",style:{objectFit:"cover"},priority:!0}),(0,a.jsx)("div",{className:"absolute inset-0 bg-black/40 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center px-4",children:[(0,a.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-white mb-4",children:e("aboutUs.hero.title")}),(0,a.jsx)("p",{className:"text-xl text-white max-w-3xl mx-auto",children:e("aboutUs.hero.description")})]})})]}),(0,a.jsx)(m.Z,{children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"flex flex-wrap border-b border-gray-200 mb-8",children:[(0,a.jsx)("button",{onClick:()=>s("company"),className:"mr-8 py-4 text-lg font-medium border-b-2 transition-colors ".concat("company"===t?"border-seekers-primary text-seekers-primary":"border-transparent text-gray-500 hover:text-seekers-primary"),children:e("aboutUs.tabs.company")}),(0,a.jsx)("button",{onClick:()=>s("team"),className:"mr-8 py-4 text-lg font-medium border-b-2 transition-colors ".concat("team"===t?"border-seekers-primary text-seekers-primary":"border-transparent text-gray-500 hover:text-seekers-primary"),children:e("aboutUs.tabs.team")}),(0,a.jsx)("button",{onClick:()=>s("mission"),className:"py-4 text-lg font-medium border-b-2 transition-colors ".concat("mission"===t?"border-seekers-primary text-seekers-primary":"border-transparent text-gray-500 hover:text-seekers-primary"),children:e("aboutUs.tabs.mission")})]}),"company"===t&&(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-12 items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-3xl font-bold mb-6 text-gray-800",children:e("aboutUs.story.companyTitle")}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:e("aboutUs.story.paragraph1")}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:e("aboutUs.story.paragraph2")}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:e("aboutUs.story.paragraph3")}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-4",children:[(0,a.jsx)(d.z,{asChild:!0,variant:"default-seekers",children:(0,a.jsx)(u.rU,{href:"/contact",children:e("aboutUs.hero.contactUs")})}),(0,a.jsx)(d.z,{asChild:!0,variant:"outline",children:(0,a.jsx)(u.rU,{href:"/s/all",children:e("aboutUs.hero.browseProperties")})})]})]}),(0,a.jsx)("div",{className:"relative h-[400px] rounded-lg overflow-hidden",children:(0,a.jsx)(c.default,{src:x,alt:"Property Plaza Office",fill:!0,className:"object-cover"})})]}),"team"===t&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold mb-4 text-gray-800",children:e("aboutUs.team.title")}),(0,a.jsx)("p",{className:"text-gray-600 max-w-3xl mx-auto",children:e("aboutUs.team.description")})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[{name:e("aboutUs.team.members.rt.name"),position:e("aboutUs.team.roles.ceo"),bio:e("aboutUs.team.members.rt.bio"),image:"/team-member-ricardo-2.jpg"},{name:e("aboutUs.team.members.thijs.name"),position:e("aboutUs.team.roles.cto"),bio:e("aboutUs.team.members.thijs.bio"),image:"/team-member-thijs-2.jpg"},{name:e("aboutUs.team.members.joost.name"),position:e("aboutUs.team.roles.marketing"),bio:e("aboutUs.team.members.joost.bio"),image:"/team-member-joost.jpg"},{name:e("aboutUs.team.members.dennis.name"),position:e("aboutUs.team.roles.propertySpecialist"),bio:e("aboutUs.team.members.dennis.bio"),image:"/team-member-dennis.jpg"},{name:e("aboutUs.team.members.andrea.name"),position:e("aboutUs.team.roles.propertySpecialist"),bio:e("aboutUs.team.members.andrea.bio"),image:"/team-member-andrea.jpg"},{name:e("aboutUs.team.members.natha.name"),position:e("aboutUs.team.roles.propertySpecialist"),bio:e("aboutUs.team.members.natha.bio"),image:"/team-member-natha.jpg"},{name:e("aboutUs.team.members.aditya.name"),position:e("aboutUs.team.roles.frontend"),bio:e("aboutUs.team.members.aditya.bio"),image:"/team-member-aditya.jpg"},{name:e("aboutUs.team.members.anjas.name"),position:e("aboutUs.team.roles.backend"),bio:e("aboutUs.team.members.anjas.bio"),image:"/team-member-anjas.jpg"},{name:e("aboutUs.team.members.nuni.name"),position:e("aboutUs.team.roles.backend2"),bio:e("aboutUs.team.members.nuni.bio"),image:"/team-member-nuni.jpg"},{name:e("aboutUs.team.members.rizki.name"),position:e("aboutUs.team.roles.tester"),bio:e("aboutUs.team.members.rizki.bio"),image:"/team-member-rizki.jpg"}].map((e,t)=>(0,a.jsxs)("div",{className:"bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow",children:[(0,a.jsx)("div",{className:"flex justify-center pt-6",children:(0,a.jsx)("div",{className:"relative !h-[180px] !w-[180px] rounded-full overflow-hidden border-4 border-seekers-primary/10",children:(0,a.jsx)(c.default,{src:e.image,alt:e.name,fill:!0})})}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-1 text-gray-800",children:e.name}),(0,a.jsx)("p",{className:"text-seekers-primary font-medium mb-3",children:e.position}),(0,a.jsx)("p",{className:"text-gray-600 text-sm mb-4",children:e.bio})]})]},t))})]}),"mission"===t&&(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-12",children:[(0,a.jsxs)("div",{className:"bg-seekers-foreground/10 p-8 rounded-lg",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-seekers-primary rounded-full flex items-center justify-center mb-6",children:(0,a.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"text-white",children:(0,a.jsx)("path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"})})}),(0,a.jsx)("h3",{className:"text-2xl font-bold mb-4 text-gray-800",children:e("aboutUs.mission.ourMission.title")}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:e("aboutUs.mission.description")}),(0,a.jsx)("p",{className:"text-gray-600",children:e("aboutUs.mission.ourMission.additionalText")})]}),(0,a.jsxs)("div",{className:"bg-seekers-foreground/10 p-8 rounded-lg",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-seekers-primary rounded-full flex items-center justify-center mb-6",children:(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"text-white",children:[(0,a.jsx)("circle",{cx:"12",cy:"12",r:"10"}),(0,a.jsx)("path",{d:"m16 10-4 4-4-4"})]})}),(0,a.jsx)("h3",{className:"text-2xl font-bold mb-4 text-gray-800",children:e("aboutUs.mission.ourVision.title")}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:e("aboutUs.mission.ourVision.description")}),(0,a.jsx)("p",{className:"text-gray-600",children:e("aboutUs.mission.ourVision.additionalText")})]}),(0,a.jsxs)("div",{className:"md:col-span-2 mt-8",children:[(0,a.jsx)("h3",{className:"text-2xl font-bold mb-6 text-gray-800",children:e("aboutUs.mission.ourCoreValues.title")}),(0,a.jsx)("div",{className:"grid md:grid-cols-3 gap-6",children:[{title:e("aboutUs.mission.values.global.title"),description:e("aboutUs.mission.values.global.description")},{title:e("aboutUs.mission.values.trust.title"),description:e("aboutUs.mission.values.trust.description")},{title:e("aboutUs.mission.values.quality.title"),description:e("aboutUs.mission.values.quality.description")},{title:e("aboutUs.mission.values.community.title"),description:e("aboutUs.mission.values.community.description")},{title:e("aboutUs.mission.values.innovation.title"),description:e("aboutUs.mission.values.innovation.description")},{title:e("aboutUs.mission.values.personalization.title"),description:e("aboutUs.mission.values.personalization.description")}].map((e,t)=>(0,a.jsxs)("div",{className:"border border-gray-200 p-6 rounded-lg hover:border-seekers-primary transition-colors",children:[(0,a.jsx)("h4",{className:"text-xl font-semibold mb-3 text-gray-800",children:e.title}),(0,a.jsx)("p",{className:"text-gray-600",children:e.description})]},t))})]})]})]})}),(0,a.jsx)("div",{className:"bg-seekers-foreground/10 py-16 mt-16",children:(0,a.jsxs)(m.Z,{children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[(0,a.jsx)("h2",{className:"text-3xl font-bold mb-4 text-gray-800",children:e("aboutUs.contact.title")}),(0,a.jsx)("p",{className:"text-gray-600 max-w-2xl mx-auto",children:e("aboutUs.cta.description")})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md flex flex-col items-center text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-seekers-primary rounded-full flex items-center justify-center mb-4",children:(0,a.jsx)(n.Z,{className:"h-6 w-6 text-white"})}),(0,a.jsx)("h3",{className:"text-xl font-semibold mb-2 text-gray-800",children:e("aboutUs.contact.visitUs.title")}),(0,a.jsx)("p",{className:"text-gray-600 whitespace-pre-line",children:e("aboutUs.contact.visitUs.address")})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md flex flex-col items-center text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-seekers-primary rounded-full flex items-center justify-center mb-4",children:(0,a.jsx)(o.Z,{className:"h-6 w-6 text-white"})}),(0,a.jsx)("h3",{className:"text-xl font-semibold mb-2 text-gray-800",children:e("aboutUs.contact.emailUs.title")}),(0,a.jsx)("p",{className:"text-gray-600 mb-2",children:e("aboutUs.contact.emailUs.general")}),(0,a.jsx)("a",{href:"mailto:<EMAIL>",className:"text-seekers-primary hover:underline",children:e("aboutUs.contact.emailUs.generalEmail")}),(0,a.jsx)("p",{className:"text-gray-600 mt-2 mb-2",children:e("aboutUs.contact.emailUs.listings")}),(0,a.jsx)("a",{href:"mailto:<EMAIL>",className:"text-seekers-primary hover:underline",children:e("aboutUs.contact.emailUs.listingsEmail")})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md flex flex-col items-center text-center",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-seekers-primary rounded-full flex items-center justify-center mb-4",children:(0,a.jsx)(l.Z,{className:"h-6 w-6 text-white"})}),(0,a.jsx)("h3",{className:"text-xl font-semibold mb-2 text-gray-800",children:e("aboutUs.contact.callUs.title")}),(0,a.jsx)("p",{className:"text-gray-600 mb-2",children:e("aboutUs.contact.callUs.officeHours")}),(0,a.jsx)("p",{className:"text-gray-600 mt-4 mb-2",children:e("aboutUs.contact.callUs.whatsapp")}),(0,a.jsx)("a",{href:"https://wa.me/6281234567890",className:"text-seekers-primary hover:underline",children:e("aboutUs.contact.callUs.whatsappNumber")})]})]}),(0,a.jsx)("div",{className:"flex justify-center mt-12",children:(0,a.jsx)(d.z,{asChild:!0,variant:"default-seekers",size:"lg",children:(0,a.jsx)(u.rU,{href:"/s/all",children:e("aboutUs.cta.findProperty")})})})]})})]})})}},27668:function(e,t,s){"use strict";s.d(t,{Z:function(){return i}});var a=s(57437),r=s(94508);function i(e){return(0,a.jsx)("div",{...e,ref:e.ref,className:(0,r.cn)("xl:max-w-screen-2xl max-md:px-4 px-8 mx-auto space-y-12",e.className),children:e.children})}},62869:function(e,t,s){"use strict";s.d(t,{z:function(){return c}});var a=s(57437),r=s(2265),i=s(98482),n=s(90535),o=s(94508),l=s(51817);let m=(0,n.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-seekers-primary text-white shadow hover:bg-seekers-primary/90","default-seekers":"bg-seekers-primary text-white shadow hover:bg-seekers-primary-light/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),c=r.forwardRef((e,t)=>{let{className:s,variant:r,size:n,asChild:c=!1,loading:d=!1,...u}=e,b=c?i.g7:"button";return(0,a.jsx)(b,{className:(0,o.cn)(m({variant:r,size:n,className:s})),ref:t,disabled:d||u.disabled,...u,children:d?(0,a.jsx)(l.Z,{className:(0,o.cn)("h-4 w-4 animate-spin")}):u.children})});c.displayName="Button"},91430:function(e,t,s){"use strict";s.d(t,{DI:function(){return r},jD:function(){return o},rU:function(){return i}});var a=s(53795);let r={locales:["en","id"],defaultLocale:"en"},{Link:i,redirect:n,usePathname:o,useRouter:l}=(0,a.os)(r)},94508:function(e,t,s){"use strict";s.d(t,{E6:function(){return d},ET:function(){return x},Fg:function(){return b},cn:function(){return o},g6:function(){return u},pl:function(){return h},uf:function(){return c},xG:function(){return m},yT:function(){return p}});var a=s(61994),r=s(77398),i=s.n(r),n=s(53335);function o(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,n.m6)((0,a.W)(t))}s(25566);let l=e=>{switch(e){case"USD":default:return"en-us";case"IDR":return"id-ID";case"GBP":return"en-GB";case"AUD":return"en-AU";case"EUR":return"nl-NL"}},m=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return arguments.length>2&&void 0!==arguments[2]&&arguments[2],new Intl.NumberFormat(l(t),{style:"currency",currency:t,minimumFractionDigits:0,maximumFractionDigits:"IDR"==t?0:2}).format(e)},c=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"en-US";return new Intl.NumberFormat(t,{style:"decimal",minimumFractionDigits:0,maximumFractionDigits:0}).format(e)};function d(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}function u(e){let t=i()(e),s=i()();return t.isSame(s,"day")?t.format("HH:mm"):t.format("DD/MM/YY")}function b(e){return e.replaceAll(/[^a-zA-Z0-9\s]/g,"-").replaceAll(/\s/g,"--")}let x=(e,t)=>e.includes(t)?e.filter(e=>e!==t):[...e,t];function h(e,t){return e.some(e=>t.includes(e))}let p=e=>e.charAt(0).toUpperCase()+e.slice(1)},29985:function(e,t,s){"use strict";s.r(t),t.default={src:"/_next/static/media/about-us-image.267ed102.webp",height:1279,width:1920,blurDataURL:"data:image/webp;base64,UklGRj4AAABXRUJQVlA4IDIAAACQAQCdASoIAAUAAkA4JZgAAudZtgAA/uYL+8l5/VVhaFlCK8//pnoGQ+Tg9E15n5nAAA==",blurWidth:8,blurHeight:5}}},function(e){e.O(0,[6990,6290,8094,2586,3145,3493,2971,2117,1744],function(){return e(e.s=44662)}),_N_E=e.O()}]);