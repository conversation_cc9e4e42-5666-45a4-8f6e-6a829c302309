import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import PlansBreadCrumb from "./bread-crumb";
import { Metadata } from "next";
import { getLocale, getTranslations } from "next-intl/server";
import { useTranslations } from "next-intl";
import PaymentMethod from "./payment-method";
import BillingHistory from "./billing-history";
import { getPaymentMethodService } from "@/core/infrastructures/transaction/services";
import { getCurrencyConversion } from "@/core/infrastructures/currency-converter/api";
import { billingUrl } from "@/lib/constanta/route";
import { routing } from "@/lib/locale/routing";

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations("seeker")
  const locale = await getLocale() || routing.defaultLocale
  const baseUrl = process.env.USER_DOMAIN || "https://www.property-plaza.com/"
  return {
    title: t("metadata.subsriptionPlan.title"),
    description: t("metadata.subsriptionPlan.description"),
    alternates: {
      canonical: baseUrl + locale + billingUrl,
      languages: {
        en: baseUrl + "/en" + billingUrl,
        id: baseUrl + "/id" + billingUrl,
        "x-default": baseUrl + billingUrl.replace("/", ""),
      }
    },
    openGraph: {
      title: t("metadata.subsriptionPlan.title"),
      description: t("metadata.subsriptionPlan.description"),
      images: [
        {
          url: baseUrl + "og.png",
          width: 1200,
          height: 630,
          alt: "Property Plaza",
        }
      ],
      type: "website",
      url: baseUrl + locale + billingUrl,
      countryName: "Indonesia",
      emails: "<EMAIL>",
      locale: locale,
      alternateLocale: routing.locales,
      siteName: "Property plaza"
    },
    applicationName: "Property plaza",
    twitter: {
      card: "summary_large_image",
      title: t("metadata.subsriptionPlan.title"),
      description: t("metadata.subsriptionPlan.description"),
      images: [baseUrl + "og.jpg"],
    },

    robots: {
      index: true,
      follow: true,
      nocache: false,
    },
  }
}


export default async function BillingPage() {
  const t = await getTranslations("seeker")
  const paymentMethods = await getPaymentMethodService()
  const conversionRates = await getCurrencyConversion("EUR")

  return <>
    <PlansBreadCrumb />
    <MainContentLayout className="max-sm:px-0 mb-12 my-8 space-y-8">
      <div className="flex justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">{t("setting.subscriptionStatus.billing.title")}</h1>
          <h2 className="text-muted-foreground mt-2">{t("setting.subscriptionStatus.billing.description")}</h2>
        </div>
      </div>
      <PaymentMethod paymentMethod={paymentMethods?.data || []} />
      <BillingHistory conversionRate={conversionRates.data} />
    </MainContentLayout>
  </>
}