(()=>{var e={};e.id=5685,e.ids=[5685],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},56022:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>o.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d}),s(83913),s(84448),s(81729),s(90996);var t=s(30170),a=s(45002),i=s(83876),o=s.n(i),l=s(66299),n={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);s.d(r,n);let d=["",{children:["[locale]",{children:["create-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,83913)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\create-password\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,80603))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,84448)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,81729)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(s.t.bind(s,90996,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\create-password\\page.tsx"],m="/[locale]/create-password/page",u={require:s,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/[locale]/create-password/page",pathname:"/[locale]/create-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},400:(e,r,s)=>{Promise.resolve().then(s.bind(s,24)),Promise.resolve().then(s.bind(s,26793)),Promise.resolve().then(s.bind(s,70697))},84244:(e,r,s)=>{"use strict";s.d(r,{E:()=>l,i:()=>o});var t=s(16718),a=s(84879),i=s(47751);let o=/^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[\W_])[A-Za-z\d\W_]{8,}$/;function l(){let e=(0,a.useTranslations)("seeker");return i.z.object({firstName:i.z.string().min(t.nM,{message:e("form.utility.minimumLength",{field:e("form.field.firstName"),length:t.nM})}).max(t.ac,{message:e("form.utility.maximumLength",{field:e("form.field.firstName"),length:t.ac})}),lastName:i.z.string().min(t.nM,{message:e("form.utility.minimumLength",{field:e("form.field.lastName"),length:t.nM})}).max(t.ac,{message:e("form.utility.maximumLength",{field:e("form.field.lastName"),length:t.ac})}),contact:i.z.string().email({message:e("form.utility.enterValidField",{field:` ${e("form.field.email")}`})}),password:i.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.password")})}),confirmPassword:i.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.confirmPassword")})})}).refine(e=>e.password==e.confirmPassword,{message:e("form.utility.fieldNotMatch",{field:`${e("form.field.password")}`}),path:["confirmPassword"]})}},24:(e,r,s)=>{"use strict";s.d(r,{default:()=>b});var t=s(97247),a=s(10906),i=s(84879),o=s(16718),l=s(47751),n=s(84244),d=s(2704),c=s(34631),m=s(52208),u=s(82328),p=s(58053),f=s(80818),x=s(6649),h=s(88111),w=s(28964),g=s(25008),v=s(48799),y=s(37013);function b({email:e,token:r}){let s=(0,i.useTranslations)("universal"),{toast:b}=(0,a.pm)(),j=(0,f.useRouter)(),N=(0,i.useLocale)(),P=function(){let e=(0,i.useTranslations)("universal");return l.z.object({password:l.z.string().min(1,{message:e("form.utility.fieldRequired",{field:e("form.field.password")})}).min(o.Z9,{message:e("form.utility.minimumLength",{length:o.Z9,field:e("form.field.password")})}).refine(e=>n.i.test(e),{message:e("form.utility.passwordWeak")}),confirmPassword:l.z.string().min(1,{message:e("form.utility.fieldRequired",{field:e("form.field.confirmPassword")})})}).refine(e=>e.password==e.confirmPassword,{message:e("form.utility.fieldNotMatch",{field:`${e("form.field.password")} ${e("conjuntion.and")} ${e("form.field.confirmPassword")}`}),path:["confirmPassword"]})}(),z=(0,h.D)({mutationFn:e=>(0,x.rb)(e,{headers:{Authorization:e.token}})}),[Z,C]=(0,w.useState)({length:!1,number:!1,special:!1,notCommon:!0,uppercase:!1}),q=(0,d.cI)({resolver:(0,c.F)(P),defaultValues:{password:"",confirmPassword:""}}),k=q.watch("password");async function _(t){let a={email:e,token:r,password:t.password,confirm_password:t.confirmPassword,locale:N};try{await z.mutateAsync(a),b({title:s("universal.success.createPassword.title"),description:s("success.createPassword.description")}),j.push("/")}catch(e){b({title:s("error.resetPassword.title"),description:e.response.data.message,variant:"destructive"})}}return t.jsx(m.l0,{...q,children:(0,t.jsxs)("form",{onSubmit:q.handleSubmit(_),className:"space-y-8",children:[t.jsx("div",{className:"space-y-2 text-center",children:t.jsx("h1",{className:"text-2xl font-semibold text-center",children:s("form.title.createPassword")})}),(0,t.jsxs)("div",{className:"space-y-2 md:min-w-80",children:[t.jsx(u.Z,{form:q,name:"password",label:s("form.label.password"),placeholder:s("form.placeholder.basePlaceholder",{field:`${s("form.field.password")}`})}),t.jsx(u.Z,{form:q,name:"confirmPassword",label:s("form.label.confirmPassword"),placeholder:s("form.placeholder.basePlaceholder",{field:`${s("form.field.confirmPassword")}`})})]}),k&&(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-xs",children:[(0,t.jsxs)("div",{className:(0,g.cn)(Z.length?"text-green-500":"text-red-500"),children:[Z.length?t.jsx(v.Z,{className:"inline w-3 h-3 mr-1"}):t.jsx(y.Z,{className:"inline w-3 h-3 mr-1"}),s("form.utility.password.minimumLength")]}),(0,t.jsxs)("div",{className:(0,g.cn)(Z.number?"text-green-500":"text-red-500"),children:[Z.number?t.jsx(v.Z,{className:"inline w-3 h-3 mr-1"}):t.jsx(y.Z,{className:"inline w-3 h-3 mr-1"}),s("form.utility.password.numberRequired")]}),(0,t.jsxs)("div",{className:(0,g.cn)(Z.special?"text-green-500":"text-red-500"),children:[Z.special?t.jsx(v.Z,{className:"inline w-3 h-3 mr-1"}):t.jsx(y.Z,{className:"inline w-3 h-3 mr-1"}),s("form.utility.password.specialCharacter")]}),(0,t.jsxs)("div",{className:(0,g.cn)(Z.notCommon?"text-green-500":"text-red-500"),children:[Z.notCommon?t.jsx(v.Z,{className:"inline w-3 h-3 mr-1"}):t.jsx(y.Z,{className:"inline w-3 h-3 mr-1"}),s("form.utility.password.notCommonWord")]}),(0,t.jsxs)("div",{className:(0,g.cn)(Z.uppercase?"text-green-500":"text-red-500"),children:[Z.uppercase?t.jsx(v.Z,{className:"inline w-3 h-3 mr-1"}):t.jsx(y.Z,{className:"inline w-3 h-3 mr-1"}),s("form.utility.password.uppercaseRequired")]}),(0,t.jsxs)("div",{className:(0,g.cn)(Z.lowercase?"text-green-500":"text-red-500"),children:[Z.lowercase?t.jsx(v.Z,{className:"inline w-3 h-3 mr-1"}):t.jsx(y.Z,{className:"inline w-3 h-3 mr-1"}),s("form.utility.password.lowercaseRequired")]})]}),t.jsx(p.z,{className:"w-full",variant:"default-seekers",loading:z.isPending,children:s("cta.changePassword")})]})})}},93572:(e,r,s)=>{"use strict";s.d(r,{Z:()=>o});var t=s(97247),a=s(52208),i=s(25008);function o({children:e,description:r,label:s,containerClassName:o,labelClassName:l,variant:n="default"}){return(0,t.jsxs)("div",{className:"w-full",children:[(0,t.jsxs)(a.xJ,{className:(0,i.cn)("w-full relative","float"==n?"border rounded-xl focus-within:border-neutral-light px-4 py-1 !space-y-0 focus-visible:outline-none focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 disabled:cursor-not-allowed":"",o),onClick:e=>e.stopPropagation(),children:[s&&t.jsx(a.lX,{className:l,children:s}),t.jsx(a.NI,{className:"group relative w-full",children:e}),r&&t.jsx(a.pf,{children:r}),"default"==n&&t.jsx(a.zG,{})]}),"float"==n&&t.jsx(a.zG,{})]})}},82328:(e,r,s)=>{"use strict";s.d(r,{Z:()=>u});var t=s(97247),a=s(52208),i=s(70170),o=s(93572),l=s(28964),n=s(58053),d=s(58406),c=s(70457),m=s(25008);function u({form:e,label:r,name:s,placeholder:u,description:p,inputProps:f,labelClassName:x,containerClassName:h,inputContainer:w,variant:g="default"}){let[v,y]=(0,l.useState)(!1);return t.jsx(a.Wi,{control:e.control,name:s,render:({field:e})=>t.jsx(o.Z,{label:r,description:p,labelClassName:(0,m.cn)("float"==g?"absolute -top-2 left-2 px-1 text-xs bg-background z-10":"",x),containerClassName:h,variant:g,children:(0,t.jsxs)("div",{className:(0,m.cn)("flex gap-2 w-full overflow-hidden","float"==g?"":"border rounded-sm focus-within:border-neutral-light",w),children:[t.jsx(i.I,{type:v?"text":"password",placeholder:u,...e,...f,className:(0,m.cn)("border-none focus:outline-none shadow-none focus-visible:ring-0 w-full","float"==g?"px-0":"",f?.className)}),t.jsx(n.z,{size:"icon",variant:"ghost",className:"bg-transparent hover:bg-transparent text-seekers-text-light",type:"button",onClick:e=>{e.preventDefault(),e.stopPropagation(),y(e=>!e)},children:v?t.jsx(d.Z,{className:"w-4 h-4"}):t.jsx(c.Z,{className:"w-4 h-4"})})]})})})}},52208:(e,r,s)=>{"use strict";s.d(r,{NI:()=>h,Wi:()=>m,l0:()=>d,lX:()=>x,pf:()=>w,xJ:()=>f,zG:()=>g});var t=s(97247),a=s(28964),i=s(12341),o=s(2704),l=s(25008),n=s(22394);let d=o.RV,c=a.createContext({}),m=({...e})=>t.jsx(c.Provider,{value:{name:e.name},children:t.jsx(o.Qr,{...e})}),u=()=>{let e=a.useContext(c),r=a.useContext(p),{getFieldState:s,formState:t}=(0,o.Gc)(),i=s(e.name,t);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=r;return{id:l,name:e.name,formItemId:`${l}-form-item`,formDescriptionId:`${l}-form-item-description`,formMessageId:`${l}-form-item-message`,...i}},p=a.createContext({}),f=a.forwardRef(({className:e,...r},s)=>{let i=a.useId();return t.jsx(p.Provider,{value:{id:i},children:t.jsx("div",{ref:s,className:(0,l.cn)("space-y-2",e),...r})})});f.displayName="FormItem";let x=a.forwardRef(({className:e,...r},s)=>{let{error:a,formItemId:i}=u();return t.jsx(n._,{ref:s,className:(0,l.cn)(a&&"text-destructive",e),htmlFor:i,...r})});x.displayName="FormLabel";let h=a.forwardRef(({...e},r)=>{let{error:s,formItemId:a,formDescriptionId:o,formMessageId:l}=u();return t.jsx(i.g7,{ref:r,id:a,"aria-describedby":s?`${o} ${l}`:`${o}`,"aria-invalid":!!s,...e})});h.displayName="FormControl";let w=a.forwardRef(({className:e,...r},s)=>{let{formDescriptionId:a}=u();return t.jsx("p",{ref:s,id:a,className:(0,l.cn)("text-[0.8rem] text-muted-foreground",e),...r})});w.displayName="FormDescription";let g=a.forwardRef(({className:e,children:r,...s},a)=>{let{error:i,formMessageId:o}=u(),n=i?String(i?.message):r;return n?t.jsx("p",{ref:a,id:o,className:(0,l.cn)("text-[0.8rem] font-medium text-destructive",e),...s,children:n}):null});g.displayName="FormMessage"},70170:(e,r,s)=>{"use strict";s.d(r,{I:()=>o});var t=s(97247),a=s(28964),i=s(25008);let o=a.forwardRef(({className:e,type:r,...s},a)=>t.jsx("input",{type:r,className:(0,i.cn)("flex max-sm:h-8 h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-inset focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...s}));o.displayName="Input"},22394:(e,r,s)=>{"use strict";s.d(r,{_:()=>d});var t=s(97247),a=s(28964),i=s(40768),o=s(87972),l=s(25008);let n=(0,o.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef(({className:e,...r},s)=>t.jsx(i.f,{ref:s,className:(0,l.cn)(n(),e),...r}));d.displayName=i.f.displayName},74993:(e,r,s)=>{"use strict";s.d(r,{apiClient:()=>n,v:()=>d});var t=s(16718),a=s(10863),i=s(72266),o=s(35240);let l=new(s.n(o)()).Agent({rejectUnauthorized:!1}),n=a.Z.create({baseURL:"https://dev.property-plaza.id/api/v1",headers:{Authorization:i.Z.get(t.LA)?"Bearer "+i.Z.get(t.LA):""},httpsAgent:l}),d=a.Z.create({baseURL:"/api/",httpsAgent:l})},87721:(e,r,s)=>{"use strict";s.d(r,{AS:()=>d,Af:()=>p,Ew:()=>u,PQ:()=>c,kS:()=>i,rb:()=>m,u8:()=>o,vJ:()=>n,x4:()=>a,zl:()=>l});var t=s(74993);let a=(e,r)=>t.apiClient.post("auth/login",e,{headers:{"g-token":r||""}}),i=()=>t.apiClient.post("auth/logout"),o=e=>t.apiClient.post("notifications/email",e),l=e=>t.apiClient.post("auth/otp-verification",e),n=e=>t.apiClient.post("auth/forgot-password",e),d=e=>t.apiClient.get(`auth/verify-reset-password?email=${e.email}&token=${e.token}`),c=e=>t.apiClient.post("auth/reset-password",e),m=(e,r)=>t.apiClient.post("auth/create-password",e,r),u=e=>t.apiClient.post("users/security",e),p=e=>t.apiClient.post("auth/totp-verification",e)},6649:(e,r,s)=>{"use strict";s.d(r,{AS:()=>t.AS,Af:()=>t.Af,Ew:()=>t.Ew,PQ:()=>t.PQ,kS:()=>t.kS,rb:()=>t.rb,u8:()=>t.u8,vJ:()=>t.vJ,x4:()=>t.x4,zl:()=>t.zl});var t=s(87721)},48799:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});let t=(0,s(26323).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},37013:(e,r,s)=>{"use strict";s.d(r,{Z:()=>t});let t=(0,s(26323).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},83913:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>n,generateMetadata:()=>l});var t=s(72051);let a=(0,s(45347).createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\app\[locale]\create-password\form\create-password.form.tsx#default`);var i=s(41288),o=s(29507);async function l({params:e,searchParams:r}){let s=await (0,o.Z)("seeker");return{title:s("metadata.rootLayout.title"),description:s("metadata.rootLayout.description"),alternates:{languages:{id:process.env.USER_DOMAIN+"/id",en:process.env.USER_DOMAIN+"/en","x-default":process.env.USER_DOMAIN+"/en"}},robots:{index:!1,follow:!1}}}function n({searchParams:e}){let{email:r,token:s}=e;return r||s?t.jsx("div",{className:"container flex items-center justify-center min-h-screen py-10",children:t.jsx("div",{className:"max-w-sm max-sm:p-4",children:t.jsx(a,{email:r,token:s})})}):(0,i.redirect)("/")}},29507:(e,r,s)=>{"use strict";s.d(r,{Z:()=>o});var t=s(26269),a=s(95817),i=s(60434),o=(0,t.cache)(async function(e){let r,s;"string"==typeof e?r=e:e&&(s=e.locale,r=e.namespace);let t=await (0,i.Z)(s);return(0,a.eX)({...t,namespace:r,messages:t.messages})})}};var r=require("../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[9379,5063,4916,9467,6666],()=>s(56022));module.exports=t})();