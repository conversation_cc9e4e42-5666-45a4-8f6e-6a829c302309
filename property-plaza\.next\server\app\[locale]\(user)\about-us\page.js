(()=>{var e={};e.id=2105,e.ids=[2105],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},5611:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,originalPathname:()=>d,pages:()=>c,routeModule:()=>p,tree:()=>m}),s(71979),s(52250),s(7505),s(84448),s(81729),s(90996);var a=s(30170),r=s(45002),i=s(83876),o=s.n(i),l=s(66299),n={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);s.d(t,n);let m=["",{children:["[locale]",{children:["(user)",{children:["about-us",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,71979)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user)\\about-us\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,52250)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,7505)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user)\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,80603))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,84448)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,81729)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(s.t.bind(s,90996,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user)\\about-us\\page.tsx"],d="/[locale]/(user)/about-us/page",u={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/[locale]/(user)/about-us/page",pathname:"/[locale]/about-us",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:m}})},89449:(e,t,s)=>{let a={ace39bf07124e0ba39e8486050a53bc79b0621b3:()=>Promise.resolve().then(s.bind(s,18714)).then(e=>e.default)};async function r(e,...t){return(await a[e]()).apply(null,t)}e.exports={ace39bf07124e0ba39e8486050a53bc79b0621b3:r.bind(null,"ace39bf07124e0ba39e8486050a53bc79b0621b3")}},89206:(e,t,s)=>{Promise.resolve().then(s.bind(s,12818)),Promise.resolve().then(s.bind(s,26793)),Promise.resolve().then(s.bind(s,70697))},75588:(e,t,s)=>{Promise.resolve().then(s.bind(s,38819)),Promise.resolve().then(s.bind(s,81578)),Promise.resolve().then(s.bind(s,84059)),Promise.resolve().then(s.bind(s,78781)),Promise.resolve().then(s.bind(s,91860)),Promise.resolve().then(s.bind(s,33626)),Promise.resolve().then(s.bind(s,26793)),Promise.resolve().then(s.bind(s,70697)),Promise.resolve().then(s.bind(s,92941)),Promise.resolve().then(s.t.bind(s,15889,23)),Promise.resolve().then(s.bind(s,62648))},12818:(e,t,s)=>{"use strict";s.d(t,{default:()=>h});var a=s(97247),r=s(28964),i=s(84879),o=s(9527),l=s(26323);let n=(0,l.Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]),m=(0,l.Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);var c=s(22288),d=s(44597),u=s(58053),p=s(75476),b=s(84033);let x={src:"/_next/static/media/office-building.73328fb0.webp",height:2560,width:1920,blurDataURL:"data:image/webp;base64,UklGRlIAAABXRUJQVlA4IEYAAADwAQCdASoGAAgAAkA4JYgCdAD0Y7s8OoAA/vj0FNF0Yy42grTicPZx5nQtif1x0Jkyvbx4Z5yYy2WdVGDoiwyt6c2QAAAA",blurWidth:6,blurHeight:8};function h(){let e=(0,i.useTranslations)("seeker"),[t,s]=(0,r.useState)("company");return a.jsx(a.Fragment,{children:(0,a.jsxs)("div",{className:"w-full bg-white",children:[(0,a.jsxs)("section",{"aria-label":"About Property Plaza Hero",className:"relative w-full h-[400px] md:h-[500px]",children:[a.jsx(d.default,{src:b.default,alt:"Property Plaza",fill:!0,className:"object-cover",style:{objectFit:"cover"},priority:!0}),a.jsx("div",{className:"absolute inset-0 bg-black/40 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center px-4",children:[a.jsx("h1",{className:"text-4xl md:text-5xl font-bold text-white mb-4",children:e("aboutUs.hero.title")}),a.jsx("p",{className:"text-xl text-white max-w-3xl mx-auto",children:e("aboutUs.hero.description")})]})})]}),a.jsx(c.Z,{children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"flex flex-wrap border-b border-gray-200 mb-8",children:[a.jsx("button",{onClick:()=>s("company"),className:`mr-8 py-4 text-lg font-medium border-b-2 transition-colors ${"company"===t?"border-seekers-primary text-seekers-primary":"border-transparent text-gray-500 hover:text-seekers-primary"}`,children:e("aboutUs.tabs.company")}),a.jsx("button",{onClick:()=>s("team"),className:`mr-8 py-4 text-lg font-medium border-b-2 transition-colors ${"team"===t?"border-seekers-primary text-seekers-primary":"border-transparent text-gray-500 hover:text-seekers-primary"}`,children:e("aboutUs.tabs.team")}),a.jsx("button",{onClick:()=>s("mission"),className:`py-4 text-lg font-medium border-b-2 transition-colors ${"mission"===t?"border-seekers-primary text-seekers-primary":"border-transparent text-gray-500 hover:text-seekers-primary"}`,children:e("aboutUs.tabs.mission")})]}),"company"===t&&(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-12 items-center",children:[(0,a.jsxs)("div",{children:[a.jsx("h2",{className:"text-3xl font-bold mb-6 text-gray-800",children:e("aboutUs.story.companyTitle")}),a.jsx("p",{className:"text-gray-600 mb-4",children:e("aboutUs.story.paragraph1")}),a.jsx("p",{className:"text-gray-600 mb-4",children:e("aboutUs.story.paragraph2")}),a.jsx("p",{className:"text-gray-600 mb-6",children:e("aboutUs.story.paragraph3")}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-4",children:[a.jsx(u.z,{asChild:!0,variant:"default-seekers",children:a.jsx(p.rU,{href:"/contact",children:e("aboutUs.hero.contactUs")})}),a.jsx(u.z,{asChild:!0,variant:"outline",children:a.jsx(p.rU,{href:"/s/all",children:e("aboutUs.hero.browseProperties")})})]})]}),a.jsx("div",{className:"relative h-[400px] rounded-lg overflow-hidden",children:a.jsx(d.default,{src:x,alt:"Property Plaza Office",fill:!0,className:"object-cover"})})]}),"team"===t&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[a.jsx("h2",{className:"text-3xl font-bold mb-4 text-gray-800",children:e("aboutUs.team.title")}),a.jsx("p",{className:"text-gray-600 max-w-3xl mx-auto",children:e("aboutUs.team.description")})]}),a.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[{name:e("aboutUs.team.members.rt.name"),position:e("aboutUs.team.roles.ceo"),bio:e("aboutUs.team.members.rt.bio"),image:"/team-member-ricardo-2.jpg"},{name:e("aboutUs.team.members.thijs.name"),position:e("aboutUs.team.roles.cto"),bio:e("aboutUs.team.members.thijs.bio"),image:"/team-member-thijs-2.jpg"},{name:e("aboutUs.team.members.joost.name"),position:e("aboutUs.team.roles.marketing"),bio:e("aboutUs.team.members.joost.bio"),image:"/team-member-joost.jpg"},{name:e("aboutUs.team.members.dennis.name"),position:e("aboutUs.team.roles.propertySpecialist"),bio:e("aboutUs.team.members.dennis.bio"),image:"/team-member-dennis.jpg"},{name:e("aboutUs.team.members.andrea.name"),position:e("aboutUs.team.roles.propertySpecialist"),bio:e("aboutUs.team.members.andrea.bio"),image:"/team-member-andrea.jpg"},{name:e("aboutUs.team.members.natha.name"),position:e("aboutUs.team.roles.propertySpecialist"),bio:e("aboutUs.team.members.natha.bio"),image:"/team-member-natha.jpg"},{name:e("aboutUs.team.members.aditya.name"),position:e("aboutUs.team.roles.frontend"),bio:e("aboutUs.team.members.aditya.bio"),image:"/team-member-aditya.jpg"},{name:e("aboutUs.team.members.anjas.name"),position:e("aboutUs.team.roles.backend"),bio:e("aboutUs.team.members.anjas.bio"),image:"/team-member-anjas.jpg"},{name:e("aboutUs.team.members.nuni.name"),position:e("aboutUs.team.roles.backend2"),bio:e("aboutUs.team.members.nuni.bio"),image:"/team-member-nuni.jpg"},{name:e("aboutUs.team.members.rizki.name"),position:e("aboutUs.team.roles.tester"),bio:e("aboutUs.team.members.rizki.bio"),image:"/team-member-rizki.jpg"}].map((e,t)=>(0,a.jsxs)("div",{className:"bg-white rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow",children:[a.jsx("div",{className:"flex justify-center pt-6",children:a.jsx("div",{className:"relative !h-[180px] !w-[180px] rounded-full overflow-hidden border-4 border-seekers-primary/10",children:a.jsx(d.default,{src:e.image,alt:e.name,fill:!0})})}),(0,a.jsxs)("div",{className:"p-6",children:[a.jsx("h3",{className:"text-xl font-semibold mb-1 text-gray-800",children:e.name}),a.jsx("p",{className:"text-seekers-primary font-medium mb-3",children:e.position}),a.jsx("p",{className:"text-gray-600 text-sm mb-4",children:e.bio})]})]},t))})]}),"mission"===t&&(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-12",children:[(0,a.jsxs)("div",{className:"bg-seekers-foreground/10 p-8 rounded-lg",children:[a.jsx("div",{className:"w-16 h-16 bg-seekers-primary rounded-full flex items-center justify-center mb-6",children:a.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"text-white",children:a.jsx("path",{d:"m12 3-1.912 5.813a2 2 0 0 1-1.275 1.275L3 12l5.813 1.912a2 2 0 0 1 1.275 1.275L12 21l1.912-5.813a2 2 0 0 1 1.275-1.275L21 12l-5.813-1.912a2 2 0 0 1-1.275-1.275L12 3Z"})})}),a.jsx("h3",{className:"text-2xl font-bold mb-4 text-gray-800",children:e("aboutUs.mission.ourMission.title")}),a.jsx("p",{className:"text-gray-600 mb-4",children:e("aboutUs.mission.description")}),a.jsx("p",{className:"text-gray-600",children:e("aboutUs.mission.ourMission.additionalText")})]}),(0,a.jsxs)("div",{className:"bg-seekers-foreground/10 p-8 rounded-lg",children:[a.jsx("div",{className:"w-16 h-16 bg-seekers-primary rounded-full flex items-center justify-center mb-6",children:(0,a.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"text-white",children:[a.jsx("circle",{cx:"12",cy:"12",r:"10"}),a.jsx("path",{d:"m16 10-4 4-4-4"})]})}),a.jsx("h3",{className:"text-2xl font-bold mb-4 text-gray-800",children:e("aboutUs.mission.ourVision.title")}),a.jsx("p",{className:"text-gray-600 mb-4",children:e("aboutUs.mission.ourVision.description")}),a.jsx("p",{className:"text-gray-600",children:e("aboutUs.mission.ourVision.additionalText")})]}),(0,a.jsxs)("div",{className:"md:col-span-2 mt-8",children:[a.jsx("h3",{className:"text-2xl font-bold mb-6 text-gray-800",children:e("aboutUs.mission.ourCoreValues.title")}),a.jsx("div",{className:"grid md:grid-cols-3 gap-6",children:[{title:e("aboutUs.mission.values.global.title"),description:e("aboutUs.mission.values.global.description")},{title:e("aboutUs.mission.values.trust.title"),description:e("aboutUs.mission.values.trust.description")},{title:e("aboutUs.mission.values.quality.title"),description:e("aboutUs.mission.values.quality.description")},{title:e("aboutUs.mission.values.community.title"),description:e("aboutUs.mission.values.community.description")},{title:e("aboutUs.mission.values.innovation.title"),description:e("aboutUs.mission.values.innovation.description")},{title:e("aboutUs.mission.values.personalization.title"),description:e("aboutUs.mission.values.personalization.description")}].map((e,t)=>(0,a.jsxs)("div",{className:"border border-gray-200 p-6 rounded-lg hover:border-seekers-primary transition-colors",children:[a.jsx("h4",{className:"text-xl font-semibold mb-3 text-gray-800",children:e.title}),a.jsx("p",{className:"text-gray-600",children:e.description})]},t))})]})]})]})}),a.jsx("div",{className:"bg-seekers-foreground/10 py-16 mt-16",children:(0,a.jsxs)(c.Z,{children:[(0,a.jsxs)("div",{className:"text-center mb-12",children:[a.jsx("h2",{className:"text-3xl font-bold mb-4 text-gray-800",children:e("aboutUs.contact.title")}),a.jsx("p",{className:"text-gray-600 max-w-2xl mx-auto",children:e("aboutUs.cta.description")})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md flex flex-col items-center text-center",children:[a.jsx("div",{className:"w-12 h-12 bg-seekers-primary rounded-full flex items-center justify-center mb-4",children:a.jsx(o.Z,{className:"h-6 w-6 text-white"})}),a.jsx("h3",{className:"text-xl font-semibold mb-2 text-gray-800",children:e("aboutUs.contact.visitUs.title")}),a.jsx("p",{className:"text-gray-600 whitespace-pre-line",children:e("aboutUs.contact.visitUs.address")})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md flex flex-col items-center text-center",children:[a.jsx("div",{className:"w-12 h-12 bg-seekers-primary rounded-full flex items-center justify-center mb-4",children:a.jsx(n,{className:"h-6 w-6 text-white"})}),a.jsx("h3",{className:"text-xl font-semibold mb-2 text-gray-800",children:e("aboutUs.contact.emailUs.title")}),a.jsx("p",{className:"text-gray-600 mb-2",children:e("aboutUs.contact.emailUs.general")}),a.jsx("a",{href:"mailto:<EMAIL>",className:"text-seekers-primary hover:underline",children:e("aboutUs.contact.emailUs.generalEmail")}),a.jsx("p",{className:"text-gray-600 mt-2 mb-2",children:e("aboutUs.contact.emailUs.listings")}),a.jsx("a",{href:"mailto:<EMAIL>",className:"text-seekers-primary hover:underline",children:e("aboutUs.contact.emailUs.listingsEmail")})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-md flex flex-col items-center text-center",children:[a.jsx("div",{className:"w-12 h-12 bg-seekers-primary rounded-full flex items-center justify-center mb-4",children:a.jsx(m,{className:"h-6 w-6 text-white"})}),a.jsx("h3",{className:"text-xl font-semibold mb-2 text-gray-800",children:e("aboutUs.contact.callUs.title")}),a.jsx("p",{className:"text-gray-600 mb-2",children:e("aboutUs.contact.callUs.officeHours")}),a.jsx("p",{className:"text-gray-600 mt-4 mb-2",children:e("aboutUs.contact.callUs.whatsapp")}),a.jsx("a",{href:"https://wa.me/6281234567890",className:"text-seekers-primary hover:underline",children:e("aboutUs.contact.callUs.whatsappNumber")})]})]}),a.jsx("div",{className:"flex justify-center mt-12",children:a.jsx(u.z,{asChild:!0,variant:"default-seekers",size:"lg",children:a.jsx(p.rU,{href:"/s/all",children:e("aboutUs.cta.findProperty")})})})]})})]})})}},38819:(e,t,s)=>{"use strict";s.d(t,{default:()=>p});var a=s(97247),r=s(75476),i=s(55961),o=s(15238),l=s(50555),n=s(58053),m=s(84879);function c({open:e,setOpen:t,trigger:s}){let c=(0,m.useTranslations)("universal");return(0,a.jsxs)(l.Z,{open:e,setOpen:t,openTrigger:s,children:[a.jsx(o.Z,{children:a.jsx("h3",{className:"text-base font-bold text-seekers-text",children:c("popup.followInstagram.title")})}),a.jsx("div",{children:a.jsx("p",{children:c("popup.followInstagram.description")})}),a.jsx(i.Z,{children:a.jsx(n.z,{asChild:!0,className:"w-full",variant:"default-seekers",children:a.jsx(r.rU,{href:"https://www.instagram.com/join.propertyplaza/",children:c("cta.followUsOnInstagram")})})})]})}var d=s(92199),u=s(28964);function p(){let{successSignUp:e,setSuccessSignUp:t,loading:s}=(0,d.I)(),[r,i]=(0,u.useState)(!1),[o,l]=(0,u.useState)(!0);return a.jsx(a.Fragment,{children:a.jsx(c,{open:r,setOpen:e=>{t(e),i(e)},trigger:a.jsx(a.Fragment,{})})})}},81578:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var a=s(97247),r=s(23866),i=s(92894);function o(){let{setSeekers:e,setRole:t}=(0,i.L)(e=>e);return(0,r.l)(),a.jsx(a.Fragment,{})}s(28964)},71979:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>c,generateMetadata:()=>m});var a=s(72051);let r=(0,s(45347).createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\app\[locale]\(user)\about-us\about-us-content.tsx#default`);var i=s(29507),o=s(83266),l=s(92898),n=s(93844);async function m(){let e=await (0,i.Z)("seeker"),t=await (0,o.Z)()||n.DI.defaultLocale,s=process.env.USER_DOMAIN||"https://www.property-plaza.com/";return{title:e("metadata.aboutUs.title"),description:e("metadata.aboutUs.description"),keywords:e("metadata.aboutUs.keyword"),openGraph:{title:e("metadata.aboutUs.title"),description:e("metadata.aboutUs.description"),images:[{url:s+"og.png",width:1200,height:630,alt:"Property Plaza Team"}],type:"website",siteName:"Property Plaza",url:s+l.W5.replace("/","")},twitter:{card:"summary_large_image",title:e("metadata.aboutUs.title"),description:e("metadata.aboutUs.description"),images:[{url:s+"og.png",width:1200,height:630,alt:"Property Plaza"}],site:s+l.W5.replace("/","")},alternates:{canonical:s+t+l.W5,languages:{en:s+"en"+l.W5,id:s+"id"+l.W5,"x-default":s+l.W5.replace("/","")}},robots:{index:!0,follow:!0}}}function c(){return a.jsx(a.Fragment,{children:a.jsx(r,{})})}},52250:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var a=s(72051),r=s(81413),i=s(98798),o=s(56886);s(26269);var l=s(35254),n=s(52845);let m=(0,s(45347).createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\app\[locale]\(user)\pop-up.tsx#default`);var c=s(86677);async function d({children:e}){let t=await (0,n.cookies)(),s=t.get("seekers-settings")?.value||"",d=s?JSON.parse(s):void 0,u=t.get("NEXT_LOCALE")?.value;return(0,a.jsxs)(a.Fragment,{children:[a.jsx(c.Z,{isSeeker:!0}),a.jsx(l.Z,{}),a.jsx(i.Z,{}),a.jsx("div",{className:"w-full sticky top-0 z-10 bg-white !mt-0",children:a.jsx(o.Z,{currency_:d?.state?.currency,localeId:u})}),a.jsx("div",{className:"!mt-0 relative min-h-screen max-sm:max-w-screen-sm",children:e}),a.jsx("div",{className:"!mt-0",children:a.jsx(r.Z,{})}),a.jsx(m,{})]})}},7505:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(41288);function r(){(0,a.redirect)("/")}},35254:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(45347).createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\app\[locale]\(user)\setup-seekers.tsx#default`)},18714:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var a=s(28713);s(9640);var r=s(53020);async function i(e,t,s){let a=(0,r.cookies)(),i=a.get("tkn")?.value;try{let a=await fetch(e,{method:t,headers:{Authorization:`Bearer ${i}`,"Content-Type":"application/json"},...s});if(!a.ok)return{data:null,meta:void 0,error:{status:a.status,name:a.statusText,message:await a.text()||"Unexpected error",details:{}}};let r=await a.json();if(r.error)return{data:null,meta:void 0,error:r.error};return{data:r.data,meta:r.meta,error:void 0}}catch(e){return{data:null,meta:void 0,error:{status:500,details:{cause:e.cause},message:e.message,name:e.name}}}}(0,s(83557).h)([i]),(0,a.j)("ace39bf07124e0ba39e8486050a53bc79b0621b3",i)},29507:(e,t,s)=>{"use strict";s.d(t,{Z:()=>o});var a=s(26269),r=s(95817),i=s(60434),o=(0,a.cache)(async function(e){let t,s;"string"==typeof e?t=e:e&&(s=e.locale,t=e.namespace);let a=await (0,i.Z)(s);return(0,r.eX)({...a,namespace:t,messages:a.messages})})},84033:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a={src:"/_next/static/media/about-us-image.267ed102.webp",height:1279,width:1920,blurDataURL:"data:image/webp;base64,UklGRj4AAABXRUJQVlA4IDIAAACQAQCdASoIAAUAAkA4JZgAAudZtgAA/uYL+8l5/VVhaFlCK8//pnoGQ+Tg9E15n5nAAA==",blurWidth:8,blurHeight:5}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[9379,5063,4916,9467,4859,5268,3327,8530,6666,9965,595],()=>s(5611));module.exports=a})();