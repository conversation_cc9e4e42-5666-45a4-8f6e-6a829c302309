"use strict";exports.id=5007,exports.ids=[5007],exports.modules={65007:(A,a,e)=>{e.r(a),e.d(a,{default:()=>n});var t=e(72051),s=e(695);let r=(0,e(45347).createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\app\[locale]\(user)\(listings)\blog-items.tsx#default`),l={src:"/_next/static/media/blog-main-image.41a3082d.jpg",height:1706,width:1280,blurDataURL:"data:image/jpeg;base64,/9j/2wBDAAoHBwgHBgoICAgLCgoLDhgQDg0NDh0VFhEYIx8lJCIfIiEmKzcvJik0KSEiMEExNDk7Pj4+JS5ESUM8SDc9Pjv/2wBDAQoLCw4NDhwQEBw7KCIoOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozv/wgARCAAIAAYDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAP/xAAVAQEBAAAAAAAAAAAAAAAAAAABA//aAAwDAQACEAMQAAABuGf/xAAWEAEBAQAAAAAAAAAAAAAAAAADAhH/2gAIAQEAAQUChVz/xAAVEQEBAAAAAAAAAAAAAAAAAAAAEf/aAAgBAwEBPwGP/8QAFBEBAAAAAAAAAAAAAAAAAAAAAP/aAAgBAgEBPwF//8QAGBAAAgMAAAAAAAAAAAAAAAAAAAECEZH/2gAIAQEABj8CqK1H/8QAGBABAQADAAAAAAAAAAAAAAAAAREAITH/2gAIAQEAAT8he1kka6nM/9oADAMBAAIAAwAAABD/AP/EABURAQEAAAAAAAAAAAAAAAAAAAEA/9oACAEDAQE/EEX/xAAWEQADAAAAAAAAAAAAAAAAAAAAARH/2gAIAQIBAT8QrP/EABcQAQEBAQAAAAAAAAAAAAAAAAEhEQD/2gAIAQEAAT8QLZMBoAI0y3v/2Q==",blurWidth:6,blurHeight:8};var g=e(79438),i=e(23141),o=e(38785),u=e(29507);async function n(){let A=await (0,o.g_)(),a=await (0,u.Z)("seeker");return t.jsx("article",{className:"bg-seekers-foreground/50 py-12",children:t.jsx(g.Z,{children:t.jsx(s.Z,{title:(0,t.jsxs)("span",{className:"inline-flex items-center gap-1",children:[t.jsx(i.Z,{className:"max-sm:hidden"})," ",a("blog.sectionTitle")]}),className:"!mt-2",children:t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:A.map((A,a)=>t.jsx(r,{date:A.publishedAt,content:A.metadata,title:A.title,image:A?.mainImage?.asset?.url||l,url:`/posts/${A.slug.current}`},a))})})})})}},38785:(A,a,e)=>{e.d(a,{g_:()=>Q,oK:()=>z,lU:()=>x,zQ:()=>B,YY:()=>w,_b:()=>I,b0:()=>_,t3:()=>E});var t=e(73027),s=e.n(t),r=e(55700);let l={projectId:"r294h68q",dataset:"production",apiVersion:"2024-01-05",useCdn:!1,token:process.env.SANITY_API_KEY,perspective:"published"};var g=e(1601);let i=`{
_id,
  title,
  metadata,
  slug,
  tags,
  author->{
    _id,
    name,
    slug,
    image{asset -> {url}},
    bio
  },
  coverImage{
  asset->{url}},
  mainImage{
  asset->{url}},
  publishedAt,
  category -> {
  _id,
  title
  },
  body
}`;(0,g.Z)`*[_type == "post" && author != "hidden"] ${i}`;let o=(0,g.Z)`*[_type == "post" && author != "hidden"][0...2] ${i}`,u=(0,g.Z)`*[_type == "post" && slug.current == $slug][0]  ${i}
  

`;(0,g.Z)`*[_type == "post" && $slug in tags[]->slug.current] ${i}`,(0,g.Z)`*[_type == "post" && author->slug.current == $slug] ${i}`,(0,g.Z)`*[_type == "post" && category->slug.current == $slug] ${i}`;let n=(0,g.Z)`
  *[_type == "post" && _id != $id] | order(date asc, _updatedAt asc) [0...4] 
    ${i}
  `,d=(0,g.Z)`*[_type == "seoContent" && language == $language]{title,body}`,c=(0,g.Z)`*[_type == "termsOfUse" && language == $language]{title,body}`,p=(0,g.Z)`*[_type == "privacyPolicy" && language == $language]{title,body}`,y=(0,g.Z)`*[_type == "userDataDeletion" && language == $language]{title,body}`,h=(0,r.eI)(l);function E(A){return s()(l).image(A)}async function m({query:A,qParams:a,tags:e}){return h.fetch(A,a,{next:{tags:e,revalidate:3600}})}let Q=async()=>await m({query:o,qParams:{},tags:["post","author","category"]}),B=async A=>await m({query:u,qParams:{slug:A},tags:["post","author","category"]}),x=async(A,a)=>await m({query:n,qParams:{slug:A,id:a},tags:[]}),z=async A=>await m({query:d,qParams:{language:A},tags:[]}),I=async A=>await m({query:c,qParams:{language:A},tags:[]}),w=async A=>await m({query:p,qParams:{language:A},tags:[]}),_=async A=>await m({query:y,qParams:{language:A},tags:[]})},23141:(A,a,e)=>{e.d(a,{Z:()=>t});let t=(0,e(86449).Z)("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])}};