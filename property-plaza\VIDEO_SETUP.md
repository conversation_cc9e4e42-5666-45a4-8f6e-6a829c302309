# Video Setup Instructions

## Required Files

To complete the video implementation on the verify hero section, you need to add the following files:

### 1. Main Video File
**Location**: `public/videos/villa-inspection-hero.mp4`
- **Size**: Recommended max 5MB (compress from your 12MB original)
- **Format**: MP4 (H.264 codec)
- **Dimensions**: Vertical format (9:16 aspect ratio)
- **Duration**: 30-60 seconds recommended for hero videos

### 2. Poster Image
**Location**: `public/images/villa-inspection-poster.jpg`
- **Size**: 200-500KB
- **Format**: JPG or WebP
- **Dimensions**: Same aspect ratio as video (9:16)
- **Purpose**: Shows immediately while video loads

### 3. Optional WebM Version (for better compression)
**Location**: `public/videos/villa-inspection-hero.webm`
- **Format**: WebM (VP9 codec)
- **Purpose**: Fallback for better compression and browser support

## Video Compression Tips

### Using HandBrake (Free):
1. Open your 12MB video in HandBrake
2. Select "Web Optimized" preset
3. Set video codec to H.264
4. Adjust quality to RF 23-28 (lower = better quality, larger file)
5. Set dimensions to maintain 9:16 aspect ratio
6. Enable "Web Optimized" checkbox
7. Export

### Using FFmpeg (Command Line):
```bash
# Compress to ~3-5MB
ffmpeg -i input.mp4 -c:v libx264 -crf 28 -preset slow -c:a aac -b:a 128k -movflags +faststart public/videos/villa-inspection-hero.mp4

# Create WebM version
ffmpeg -i input.mp4 -c:v libvpx-vp9 -crf 30 -b:v 0 -c:a libopus public/videos/villa-inspection-hero.webm

# Create poster image from video
ffmpeg -i input.mp4 -ss 00:00:02 -vframes 1 public/images/villa-inspection-poster.jpg
```

## Directory Structure

After adding files, your structure should look like:

```
public/
├── images/
│   └── villa-inspection-poster.jpg
└── videos/
    ├── villa-inspection-hero.mp4
    └── villa-inspection-hero.webm (optional)
```

## Features Implemented

✅ **Lazy Loading**: Video only loads when user scrolls near it
✅ **Intersection Observer**: Efficient loading detection
✅ **Loading States**: Shows spinner while video loads
✅ **Error Handling**: Fallback content if video fails
✅ **Multiple Formats**: MP4 + WebM support
✅ **Poster Image**: Instant visual feedback
✅ **Mobile Optimized**: playsInline for iOS
✅ **Autoplay**: Muted autoplay (follows browser policies)
✅ **Responsive**: Maintains aspect ratio on all devices

## Performance Benefits

- **Lazy Loading**: Video only downloads when needed
- **Metadata Preload**: Only loads video info, not full file
- **Compressed Format**: Smaller file size = faster loading
- **Edge Caching**: Vercel automatically caches at edge locations
- **Progressive Loading**: Video can start playing before fully downloaded

## Browser Support

- ✅ Chrome/Edge: Full support
- ✅ Firefox: Full support  
- ✅ Safari: Full support (with playsInline)
- ✅ Mobile browsers: Optimized for mobile playback

## Testing Checklist

After adding the video files:

- [ ] Video loads and plays automatically (muted)
- [ ] Poster image shows before video loads
- [ ] Loading spinner appears during video load
- [ ] Video maintains 9:16 aspect ratio on all screen sizes
- [ ] Video works on mobile devices
- [ ] Fallback content shows if video fails to load
- [ ] Page loads quickly (video doesn't block initial render)

## Troubleshooting

**Video doesn't play:**
- Check file path is correct
- Ensure video is properly compressed
- Verify browser supports autoplay (must be muted)

**Video too large:**
- Compress further using tools above
- Consider shorter duration
- Use lower quality settings

**Mobile issues:**
- Ensure `playsInline` is enabled
- Check video codec compatibility
- Test on actual devices, not just browser dev tools
