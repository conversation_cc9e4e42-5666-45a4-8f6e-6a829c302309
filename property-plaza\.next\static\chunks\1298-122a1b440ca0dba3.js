"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1298],{8221:function(e,t){let n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DOMAttributeNames:function(){return r},default:function(){return u},isEqualNode:function(){return a}});let r={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"};function o(e){let{type:t,props:n}=e,o=document.createElement(t);for(let e in n){if(!n.hasOwnProperty(e)||"children"===e||"dangerouslySetInnerHTML"===e||void 0===n[e])continue;let a=r[e]||e.toLowerCase();"script"===t&&("async"===a||"defer"===a||"noModule"===a)?o[a]=!!n[e]:o.setAttribute(a,n[e])}let{children:a,dangerouslySetInnerHTML:u}=n;return u?o.innerHTML=u.__html||"":a&&(o.textContent="string"==typeof a?a:Array.isArray(a)?a.join(""):""),o}function a(e,t){if(e instanceof HTMLElement&&t instanceof HTMLElement){let n=t.getAttribute("nonce");if(n&&!e.getAttribute("nonce")){let r=t.cloneNode(!0);return r.setAttribute("nonce",""),r.nonce=n,n===e.nonce&&e.isEqualNode(r)}}return e.isEqualNode(t)}function u(){return{mountedInstances:new Set,updateHead:e=>{let t={};e.forEach(e=>{if("link"===e.type&&e.props["data-optimized-fonts"]){if(document.querySelector('style[data-href="'+e.props["data-href"]+'"]'))return;e.props.href=e.props["data-href"],e.props["data-href"]=void 0}let n=t[e.type]||[];n.push(e),t[e.type]=n});let r=t.title?t.title[0]:null,o="";if(r){let{children:e}=r.props;o="string"==typeof e?e:Array.isArray(e)?e.join(""):""}o!==document.title&&(document.title=o),["meta","base","link","style","script"].forEach(e=>{n(e,t[e]||[])})}}}n=(e,t)=>{let n=document.getElementsByTagName("head")[0],r=n.querySelector("meta[name=next-head-count]"),u=Number(r.content),l=[];for(let t=0,n=r.previousElementSibling;t<u;t++,n=(null==n?void 0:n.previousElementSibling)||null){var i;(null==n?void 0:null==(i=n.tagName)?void 0:i.toLowerCase())===e&&l.push(n)}let c=t.map(o).filter(e=>{for(let t=0,n=l.length;t<n;t++)if(a(l[t],e))return l.splice(t,1),!1;return!0});l.forEach(e=>{var t;return null==(t=e.parentNode)?void 0:t.removeChild(e)}),c.forEach(e=>n.insertBefore(e,r)),r.content=(u-l.length+c.length).toString()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},88003:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return _},handleClientScriptLoad:function(){return v},initScriptLoader:function(){return m}});let r=n(47043),o=n(53099),a=n(57437),u=r._(n(54887)),l=o._(n(2265)),i=n(48701),c=n(8221),s=n(63515),d=new Map,f=new Set,p=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"],h=e=>{if(u.default.preinit){e.forEach(e=>{u.default.preinit(e,{as:"style"})});return}if("undefined"!=typeof window){let t=document.head;e.forEach(e=>{let n=document.createElement("link");n.type="text/css",n.rel="stylesheet",n.href=e,t.appendChild(n)})}},y=e=>{let{src:t,id:n,onLoad:r=()=>{},onReady:o=null,dangerouslySetInnerHTML:a,children:u="",strategy:l="afterInteractive",onError:i,stylesheets:s}=e,y=n||t;if(y&&f.has(y))return;if(d.has(t)){f.add(y),d.get(t).then(r,i);return}let v=()=>{o&&o(),f.add(y)},m=document.createElement("script"),g=new Promise((e,t)=>{m.addEventListener("load",function(t){e(),r&&r.call(this,t),v()}),m.addEventListener("error",function(e){t(e)})}).catch(function(e){i&&i(e)});for(let[n,r]of(a?(m.innerHTML=a.__html||"",v()):u?(m.textContent="string"==typeof u?u:Array.isArray(u)?u.join(""):"",v()):t&&(m.src=t,d.set(t,g)),Object.entries(e))){if(void 0===r||p.includes(n))continue;let e=c.DOMAttributeNames[n]||n.toLowerCase();m.setAttribute(e,r)}"worker"===l&&m.setAttribute("type","text/partytown"),m.setAttribute("data-nscript",l),s&&h(s),document.body.appendChild(m)};function v(e){let{strategy:t="afterInteractive"}=e;"lazyOnload"===t?window.addEventListener("load",()=>{(0,s.requestIdleCallback)(()=>y(e))}):y(e)}function m(e){e.forEach(v),[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')].forEach(e=>{let t=e.id||e.getAttribute("src");f.add(t)})}function g(e){let{id:t,src:n="",onLoad:r=()=>{},onReady:o=null,strategy:c="afterInteractive",onError:d,stylesheets:p,...h}=e,{updateScripts:v,scripts:m,getIsSsr:g,appDir:_,nonce:b}=(0,l.useContext)(i.HeadManagerContext),C=(0,l.useRef)(!1);(0,l.useEffect)(()=>{let e=t||n;C.current||(o&&e&&f.has(e)&&o(),C.current=!0)},[o,t,n]);let E=(0,l.useRef)(!1);if((0,l.useEffect)(()=>{!E.current&&("afterInteractive"===c?y(e):"lazyOnload"===c&&("complete"===document.readyState?(0,s.requestIdleCallback)(()=>y(e)):window.addEventListener("load",()=>{(0,s.requestIdleCallback)(()=>y(e))})),E.current=!0)},[e,c]),("beforeInteractive"===c||"worker"===c)&&(v?(m[c]=(m[c]||[]).concat([{id:t,src:n,onLoad:r,onReady:o,onError:d,...h}]),v(m)):g&&g()?f.add(t||n):g&&!g()&&y(e)),_){if(p&&p.forEach(e=>{u.default.preinit(e,{as:"style"})}),"beforeInteractive"===c)return n?(u.default.preload(n,h.integrity?{as:"script",integrity:h.integrity,nonce:b,crossOrigin:h.crossOrigin}:{as:"script",nonce:b,crossOrigin:h.crossOrigin}),(0,a.jsx)("script",{nonce:b,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([n,{...h,id:t}])+")"}})):(h.dangerouslySetInnerHTML&&(h.children=h.dangerouslySetInnerHTML.__html,delete h.dangerouslySetInnerHTML),(0,a.jsx)("script",{nonce:b,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{...h,id:t}])+")"}}));"afterInteractive"===c&&n&&u.default.preload(n,h.integrity?{as:"script",integrity:h.integrity,nonce:b,crossOrigin:h.crossOrigin}:{as:"script",nonce:b,crossOrigin:h.crossOrigin})}return null}Object.defineProperty(g,"__nextScript",{value:!0});let _=g;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},73966:function(e,t,n){n.d(t,{b:function(){return a}});var r=n(2265),o=n(57437);function a(e,t=[]){let n=[],a=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return a.scopeName=e,[function(t,a){let u=r.createContext(a),l=n.length;function i(t){let{scope:n,children:a,...i}=t,c=n?.[e][l]||u,s=r.useMemo(()=>i,Object.values(i));return(0,o.jsx)(c.Provider,{value:s,children:a})}return n=[...n,a],i.displayName=t+"Provider",[i,function(n,o){let i=o?.[e][l]||u,c=r.useContext(i);if(c)return c;if(void 0!==a)return a;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(a,...t)]}},22248:function(e,t,n){n.d(t,{ReCaptcha:function(){return a}});var r=n(2265),o=n(16471);let a=e=>{let{action:t,onValidate:n,validate:a=!0,reCaptchaKey:u}=e,{loaded:l,executeRecaptcha:i}=(0,o.useReCaptcha)(u);return(0,r.useEffect)(()=>{a&&l&&"function"==typeof n&&(async()=>{n(await i(t))})()},[t,n,a,l,i]),null}},93753:function(e,t,n){n.d(t,{ReCaptchaContext:function(){return i},ReCaptchaProvider:function(){return s},useReCaptchaContext:function(){return c}});var r=n(2265),o=n(88003),a=n.n(o),u=n(35977),l=n(25566);let i=(0,r.createContext)({reCaptchaKey:null,grecaptcha:null,loaded:!1,error:!1}),c=()=>{let e=(0,r.useContext)(i);return(0,r.useDebugValue)("grecaptcha available: ".concat((null==e?void 0:e.loaded)?"Yes":"No")),(0,r.useDebugValue)("ReCaptcha Script: ".concat((null==e?void 0:e.loaded)?"Loaded":"Not Loaded")),(0,r.useDebugValue)("Failed to load Script: ".concat((null==e?void 0:e.error)?"Yes":"No")),e},s=e=>{let{reCaptchaKey:t,useEnterprise:n=!1,useRecaptchaNet:o=!1,language:c,children:s,id:d="google-recaptcha-v3",strategy:f="afterInteractive",src:p,onLoad:h,onError:y,...v}=e,[m,g]=(0,r.useState)(null),[_,b]=(0,r.useState)(!1),[C,E]=(0,r.useState)(!1),w=t||l.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY||null,L=p||(0,u.J)({reCaptchaKey:w,language:c,useRecaptchaNet:o,useEnterprise:n})||null,S=(0,r.useRef)(!1);(0,r.useEffect)(()=>{S.current&&(b(!1),E(!1)),S.current=!0},[L]);let x=(0,r.useCallback)(e=>{var t,r,o;let a=n?null===(r=window)||void 0===r?void 0:null===(t=r.grecaptcha)||void 0===t?void 0:t.enterprise:null===(o=window)||void 0===o?void 0:o.grecaptcha;a&&a.ready(()=>{g(a),b(!0),null==h||h(a,e)})},[h,n]);(0,r.useEffect)(()=>x(),[x]);let M=(0,r.useCallback)(e=>{E(!0),null==y||y(e)},[y]),N=(0,r.useMemo)(()=>({reCaptchaKey:w,grecaptcha:m,loaded:_,error:C}),[w,m,_,C]);return r.createElement(i.Provider,{value:N},s,r.createElement(a(),{id:d,src:L,strategy:f,onLoad:x,onError:M,...v}))}},16471:function(e,t,n){n.d(t,{useReCaptcha:function(){return u}});var r=n(2265),o=n(93753),a=n(35977);let u=e=>{let{grecaptcha:t,loaded:n,reCaptchaKey:u,...l}=(0,o.useReCaptchaContext)(),i=e||u,c=(0,r.useRef)(null==t?void 0:t.execute);(0,a.L)(()=>{c.current=null==t?void 0:t.execute},[n,null==t?void 0:t.execute]);let s=(0,r.useCallback)(async e=>{if("function"!=typeof c.current)throw Error("Recaptcha has not been loaded");if(!i)throw Error("ReCaptcha sitekey is not defined");return await c.current(i,{action:e})},[i]);return{...l,grecaptcha:t,loaded:n,reCaptchaKey:i,executeRecaptcha:s}}},35977:function(e,t,n){n.d(t,{J:function(){return o},L:function(){return a}});var r=n(2265);let o=function(){let{reCaptchaKey:e,language:t,useRecaptchaNet:n=!1,useEnterprise:r=!1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},o="https://www.".concat(n?"recaptcha.net":"google.com","/recaptcha/").concat(r?"enterprise.js":"api.js","?");return e&&(o+="render=".concat(e)),t&&(o+="&hl=".concat(t)),o},a="undefined"!=typeof window?r.useLayoutEffect:r.useEffect},56302:function(e,t,n){n.d(t,{withReCaptcha:function(){return a}});var r=n(2265),o=n(16471);function a(e){let t=e.displayName||e.name||"Component",n=t=>{let n=(0,o.useReCaptcha)();return r.createElement(e,{...n,...t})};return n.displayName="withReCaptcha(".concat(t,")"),n}}}]);