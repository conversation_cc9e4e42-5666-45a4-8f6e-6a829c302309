(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3877],{88226:function(e,t,n){"use strict";n.d(t,{Z:function(){return l}});let l=(0,n(79205).Z)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},92735:function(e,t,n){"use strict";n.d(t,{Z:function(){return l}});let l=(0,n(79205).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},10575:function(e,t,n){"use strict";n.d(t,{default:function(){return r}});var l=n(49988),o=n(2265),i=n(69362);function r(e){let{locale:t,...n}=e;if(!t)throw Error("Failed to determine locale in `NextIntlClientProvider`, please provide the `locale` prop explicitly.\n\nSee https://next-intl-docs.vercel.app/docs/configuration#locale");return o.createElement(i.IntlProvider,(0,l.g)({locale:t},n))}},12119:function(e,t,n){"use strict";Object.defineProperty(t,"$",{enumerable:!0,get:function(){return o}});let l=n(83079);function o(e){let{createServerReference:t}=n(6671);return t(e,l.callServer)}},71318:function(e,t,n){var l,o;void 0!==(o="function"==typeof(l=function(){var e,t,n,l={};l.version="0.2.0";var o=l.settings={minimum:.08,easing:"ease",positionUsing:"",speed:200,trickle:!0,trickleRate:.02,trickleSpeed:800,showSpinner:!0,barSelector:'[role="bar"]',spinnerSelector:'[role="spinner"]',parent:"body",template:'<div class="bar" role="bar"><div class="peg"></div></div><div class="spinner" role="spinner"><div class="spinner-icon"></div></div>'};function i(e,t,n){return e<t?t:e>n?n:e}l.configure=function(e){var t,n;for(t in e)void 0!==(n=e[t])&&e.hasOwnProperty(t)&&(o[t]=n);return this},l.status=null,l.set=function(e){var t=l.isStarted();e=i(e,o.minimum,1),l.status=1===e?null:e;var n=l.render(!t),a=n.querySelector(o.barSelector),s=o.speed,g=o.easing;return n.offsetWidth,r(function(t){var i,r;""===o.positionUsing&&(o.positionUsing=l.getPositioningCSS()),u(a,(i=e,(r="translate3d"===o.positionUsing?{transform:"translate3d("+(-1+i)*100+"%,0,0)"}:"translate"===o.positionUsing?{transform:"translate("+(-1+i)*100+"%,0)"}:{"margin-left":(-1+i)*100+"%"}).transition="all "+s+"ms "+g,r)),1===e?(u(n,{transition:"none",opacity:1}),n.offsetWidth,setTimeout(function(){u(n,{transition:"all "+s+"ms linear",opacity:0}),setTimeout(function(){l.remove(),t()},s)},s)):setTimeout(t,s)}),this},l.isStarted=function(){return"number"==typeof l.status},l.start=function(){l.status||l.set(0);var e=function(){setTimeout(function(){l.status&&(l.trickle(),e())},o.trickleSpeed)};return o.trickle&&e(),this},l.done=function(e){return e||l.status?l.inc(.3+.5*Math.random()).set(1):this},l.inc=function(e){var t=l.status;return t?("number"!=typeof e&&(e=(1-t)*i(Math.random()*t,.1,.95)),t=i(t+e,0,.994),l.set(t)):l.start()},l.trickle=function(){return l.inc(Math.random()*o.trickleRate)},e=0,t=0,l.promise=function(n){return n&&"resolved"!==n.state()&&(0===t&&l.start(),e++,t++,n.always(function(){0==--t?(e=0,l.done()):l.set((e-t)/e)})),this},l.render=function(e){if(l.isRendered())return document.getElementById("nprogress");s(document.documentElement,"nprogress-busy");var t=document.createElement("div");t.id="nprogress",t.innerHTML=o.template;var n,i=t.querySelector(o.barSelector),r=e?"-100":(-1+(l.status||0))*100,a=document.querySelector(o.parent);return u(i,{transition:"all 0 linear",transform:"translate3d("+r+"%,0,0)"}),!o.showSpinner&&(n=t.querySelector(o.spinnerSelector))&&c(n),a!=document.body&&s(a,"nprogress-custom-parent"),a.appendChild(t),t},l.remove=function(){g(document.documentElement,"nprogress-busy"),g(document.querySelector(o.parent),"nprogress-custom-parent");var e=document.getElementById("nprogress");e&&c(e)},l.isRendered=function(){return!!document.getElementById("nprogress")},l.getPositioningCSS=function(){var e=document.body.style,t="WebkitTransform"in e?"Webkit":"MozTransform"in e?"Moz":"msTransform"in e?"ms":"OTransform"in e?"O":"";return t+"Perspective" in e?"translate3d":t+"Transform" in e?"translate":"margin"};var r=(n=[],function(e){n.push(e),1==n.length&&function e(){var t=n.shift();t&&t(e)}()}),u=function(){var e=["Webkit","O","Moz","ms"],t={};function n(n,l,o){var i;l=t[i=(i=l).replace(/^-ms-/,"ms-").replace(/-([\da-z])/gi,function(e,t){return t.toUpperCase()})]||(t[i]=function(t){var n=document.body.style;if(t in n)return t;for(var l,o=e.length,i=t.charAt(0).toUpperCase()+t.slice(1);o--;)if((l=e[o]+i)in n)return l;return t}(i)),n.style[l]=o}return function(e,t){var l,o,i=arguments;if(2==i.length)for(l in t)void 0!==(o=t[l])&&t.hasOwnProperty(l)&&n(e,l,o);else n(e,i[1],i[2])}}();function a(e,t){return("string"==typeof e?e:d(e)).indexOf(" "+t+" ")>=0}function s(e,t){var n=d(e),l=n+t;a(n,t)||(e.className=l.substring(1))}function g(e,t){var n,l=d(e);a(e,t)&&(n=l.replace(" "+t+" "," "),e.className=n.substring(1,n.length-1))}function d(e){return(" "+(e.className||"")+" ").replace(/\s+/gi," ")}function c(e){e&&e.parentNode&&e.parentNode.removeChild(e)}return l})?l.call(t,n,t,e):l)&&(e.exports=o)},73966:function(e,t,n){"use strict";n.d(t,{b:function(){return i}});var l=n(2265),o=n(57437);function i(e,t=[]){let n=[],i=()=>{let t=n.map(e=>l.createContext(e));return function(n){let o=n?.[e]||t;return l.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return i.scopeName=e,[function(t,i){let r=l.createContext(i),u=n.length;function a(t){let{scope:n,children:i,...a}=t,s=n?.[e][u]||r,g=l.useMemo(()=>a,Object.values(a));return(0,o.jsx)(s.Provider,{value:g,children:i})}return n=[...n,i],a.displayName=t+"Provider",[a,function(n,o){let a=o?.[e][u]||r,s=l.useContext(a);if(s)return s;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:l})=>{let o=n(e)[`__scope${l}`];return{...t,...o}},{});return l.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(i,...t)]}},71594:function(e,t,n){"use strict";n.d(t,{b7:function(){return r},ie:function(){return i}});var l=n(2265),o=n(24525);function i(e,t){return e?"function"==typeof e&&(()=>{let t=Object.getPrototypeOf(e);return t.prototype&&t.prototype.isReactComponent})()||"function"==typeof e||"object"==typeof e&&"symbol"==typeof e.$$typeof&&["react.memo","react.forward_ref"].includes(e.$$typeof.description)?l.createElement(e,t):e:null}function r(e){let t={state:{},onStateChange:()=>{},renderFallbackValue:null,...e},[n]=l.useState(()=>({current:(0,o.W_)(t)})),[i,r]=l.useState(()=>n.current.initialState);return n.current.setOptions(t=>({...t,...e,state:{...i,...e.state},onStateChange:t=>{r(t),null==e.onStateChange||e.onStateChange(t)}})),n.current}},24525:function(e,t,n){"use strict";function l(e,t){return"function"==typeof e?e(t):e}function o(e,t){return n=>{t.setState(t=>({...t,[e]:l(n,t[e])}))}}function i(e){return e instanceof Function}function r(e,t,n){let l,o=[];return i=>{let r,u;n.key&&n.debug&&(r=Date.now());let a=e(i);if(!(a.length!==o.length||a.some((e,t)=>o[t]!==e)))return l;if(o=a,n.key&&n.debug&&(u=Date.now()),l=t(...a),null==n||null==n.onChange||n.onChange(l),n.key&&n.debug&&null!=n&&n.debug()){let e=Math.round((Date.now()-r)*100)/100,t=Math.round((Date.now()-u)*100)/100,l=t/16,o=(e,t)=>{for(e=String(e);e.length<t;)e=" "+e;return e};console.info(`%c⏱ ${o(t,5)} /${o(e,5)} ms`,`
            font-size: .6rem;
            font-weight: bold;
            color: hsl(${Math.max(0,Math.min(120-120*l,120))}deg 100% 31%);`,null==n?void 0:n.key)}return l}}function u(e,t,n,l){return{debug:()=>{var n;return null!=(n=null==e?void 0:e.debugAll)?n:e[t]},key:!1,onChange:l}}n.d(t,{G_:function(){return $},W_:function(){return q},sC:function(){return j},tj:function(){return X},vL:function(){return U}});let a="debugHeaders";function s(e,t,n){var l;let o={id:null!=(l=n.id)?l:t.id,column:t,index:n.index,isPlaceholder:!!n.isPlaceholder,placeholderId:n.placeholderId,depth:n.depth,subHeaders:[],colSpan:0,rowSpan:0,headerGroup:null,getLeafHeaders:()=>{let e=[],t=n=>{n.subHeaders&&n.subHeaders.length&&n.subHeaders.map(t),e.push(n)};return t(o),e},getContext:()=>({table:e,header:o,column:t})};return e._features.forEach(t=>{null==t.createHeader||t.createHeader(o,e)}),o}function g(e,t,n,l){var o,i;let r=0,u=function(e,t){void 0===t&&(t=1),r=Math.max(r,t),e.filter(e=>e.getIsVisible()).forEach(e=>{var n;null!=(n=e.columns)&&n.length&&u(e.columns,t+1)},0)};u(e);let a=[],g=(e,t)=>{let o={depth:t,id:[l,`${t}`].filter(Boolean).join("_"),headers:[]},i=[];e.forEach(e=>{let r;let u=[...i].reverse()[0],a=e.column.depth===o.depth,g=!1;if(a&&e.column.parent?r=e.column.parent:(r=e.column,g=!0),u&&(null==u?void 0:u.column)===r)u.subHeaders.push(e);else{let o=s(n,r,{id:[l,t,r.id,null==e?void 0:e.id].filter(Boolean).join("_"),isPlaceholder:g,placeholderId:g?`${i.filter(e=>e.column===r).length}`:void 0,depth:t,index:i.length});o.subHeaders.push(e),i.push(o)}o.headers.push(e),e.headerGroup=o}),a.push(o),t>0&&g(i,t-1)};g(t.map((e,t)=>s(n,e,{depth:r,index:t})),r-1),a.reverse();let d=e=>e.filter(e=>e.column.getIsVisible()).map(e=>{let t=0,n=0,l=[0];return e.subHeaders&&e.subHeaders.length?(l=[],d(e.subHeaders).forEach(e=>{let{colSpan:n,rowSpan:o}=e;t+=n,l.push(o)})):t=1,n+=Math.min(...l),e.colSpan=t,e.rowSpan=n,{colSpan:t,rowSpan:n}});return d(null!=(o=null==(i=a[0])?void 0:i.headers)?o:[]),a}let d=(e,t,n,l,o,i,a)=>{let s={id:t,index:l,original:n,depth:o,parentId:a,_valuesCache:{},_uniqueValuesCache:{},getValue:t=>{if(s._valuesCache.hasOwnProperty(t))return s._valuesCache[t];let n=e.getColumn(t);if(null!=n&&n.accessorFn)return s._valuesCache[t]=n.accessorFn(s.original,l),s._valuesCache[t]},getUniqueValues:t=>{if(s._uniqueValuesCache.hasOwnProperty(t))return s._uniqueValuesCache[t];let n=e.getColumn(t);return null!=n&&n.accessorFn?(n.columnDef.getUniqueValues?s._uniqueValuesCache[t]=n.columnDef.getUniqueValues(s.original,l):s._uniqueValuesCache[t]=[s.getValue(t)],s._uniqueValuesCache[t]):void 0},renderValue:t=>{var n;return null!=(n=s.getValue(t))?n:e.options.renderFallbackValue},subRows:null!=i?i:[],getLeafRows:()=>(function(e,t){let n=[],l=e=>{e.forEach(e=>{n.push(e);let o=t(e);null!=o&&o.length&&l(o)})};return l(e),n})(s.subRows,e=>e.subRows),getParentRow:()=>s.parentId?e.getRow(s.parentId,!0):void 0,getParentRows:()=>{let e=[],t=s;for(;;){let n=t.getParentRow();if(!n)break;e.push(n),t=n}return e.reverse()},getAllCells:r(()=>[e.getAllLeafColumns()],t=>t.map(t=>(function(e,t,n,l){let o={id:`${t.id}_${n.id}`,row:t,column:n,getValue:()=>t.getValue(l),renderValue:()=>{var t;return null!=(t=o.getValue())?t:e.options.renderFallbackValue},getContext:r(()=>[e,n,t,o],(e,t,n,l)=>({table:e,column:t,row:n,cell:l,getValue:l.getValue,renderValue:l.renderValue}),u(e.options,"debugCells","cell.getContext"))};return e._features.forEach(l=>{null==l.createCell||l.createCell(o,n,t,e)},{}),o})(e,s,t,t.id)),u(e.options,"debugRows","getAllCells")),_getAllCellsByColumnId:r(()=>[s.getAllCells()],e=>e.reduce((e,t)=>(e[t.column.id]=t,e),{}),u(e.options,"debugRows","getAllCellsByColumnId"))};for(let t=0;t<e._features.length;t++){let n=e._features[t];null==n||null==n.createRow||n.createRow(s,e)}return s},c=(e,t,n)=>{var l;let o=n.toLowerCase();return!!(null==(l=e.getValue(t))||null==(l=l.toString())||null==(l=l.toLowerCase())?void 0:l.includes(o))};c.autoRemove=e=>R(e);let p=(e,t,n)=>{var l;return!!(null==(l=e.getValue(t))||null==(l=l.toString())?void 0:l.includes(n))};p.autoRemove=e=>R(e);let f=(e,t,n)=>{var l;return(null==(l=e.getValue(t))||null==(l=l.toString())?void 0:l.toLowerCase())===(null==n?void 0:n.toLowerCase())};f.autoRemove=e=>R(e);let m=(e,t,n)=>{var l;return null==(l=e.getValue(t))?void 0:l.includes(n)};m.autoRemove=e=>R(e)||!(null!=e&&e.length);let v=(e,t,n)=>!n.some(n=>{var l;return!(null!=(l=e.getValue(t))&&l.includes(n))});v.autoRemove=e=>R(e)||!(null!=e&&e.length);let C=(e,t,n)=>n.some(n=>{var l;return null==(l=e.getValue(t))?void 0:l.includes(n)});C.autoRemove=e=>R(e)||!(null!=e&&e.length);let h=(e,t,n)=>e.getValue(t)===n;h.autoRemove=e=>R(e);let w=(e,t,n)=>e.getValue(t)==n;w.autoRemove=e=>R(e);let S=(e,t,n)=>{let[l,o]=n,i=e.getValue(t);return i>=l&&i<=o};S.resolveFilterValue=e=>{let[t,n]=e,l="number"!=typeof t?parseFloat(t):t,o="number"!=typeof n?parseFloat(n):n,i=null===t||Number.isNaN(l)?-1/0:l,r=null===n||Number.isNaN(o)?1/0:o;if(i>r){let e=i;i=r,r=e}return[i,r]},S.autoRemove=e=>R(e)||R(e[0])&&R(e[1]);let b={includesString:c,includesStringSensitive:p,equalsString:f,arrIncludes:m,arrIncludesAll:v,arrIncludesSome:C,equals:h,weakEquals:w,inNumberRange:S};function R(e){return null==e||""===e}function F(e,t,n){return!!e&&!!e.autoRemove&&e.autoRemove(t,n)||void 0===t||"string"==typeof t&&!t}let M={sum:(e,t,n)=>n.reduce((t,n)=>{let l=n.getValue(e);return t+("number"==typeof l?l:0)},0),min:(e,t,n)=>{let l;return n.forEach(t=>{let n=t.getValue(e);null!=n&&(l>n||void 0===l&&n>=n)&&(l=n)}),l},max:(e,t,n)=>{let l;return n.forEach(t=>{let n=t.getValue(e);null!=n&&(l<n||void 0===l&&n>=n)&&(l=n)}),l},extent:(e,t,n)=>{let l,o;return n.forEach(t=>{let n=t.getValue(e);null!=n&&(void 0===l?n>=n&&(l=o=n):(l>n&&(l=n),o<n&&(o=n)))}),[l,o]},mean:(e,t)=>{let n=0,l=0;if(t.forEach(t=>{let o=t.getValue(e);null!=o&&(o=+o)>=o&&(++n,l+=o)}),n)return l/n},median:(e,t)=>{if(!t.length)return;let n=t.map(t=>t.getValue(e));if(!(Array.isArray(n)&&n.every(e=>"number"==typeof e)))return;if(1===n.length)return n[0];let l=Math.floor(n.length/2),o=n.sort((e,t)=>e-t);return n.length%2!=0?o[l]:(o[l-1]+o[l])/2},unique:(e,t)=>Array.from(new Set(t.map(t=>t.getValue(e))).values()),uniqueCount:(e,t)=>new Set(t.map(t=>t.getValue(e))).size,count:(e,t)=>t.length},V=()=>({left:[],right:[]}),P={size:150,minSize:20,maxSize:Number.MAX_SAFE_INTEGER},I=()=>({startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,isResizingColumn:!1,columnSizingStart:[]}),x=null;function y(e){return"touchstart"===e.type}function _(e,t){return t?"center"===t?e.getCenterVisibleLeafColumns():"left"===t?e.getLeftVisibleLeafColumns():e.getRightVisibleLeafColumns():e.getVisibleLeafColumns()}let E=()=>({pageIndex:0,pageSize:10}),G=()=>({top:[],bottom:[]}),L=(e,t,n,l,o)=>{var i;let r=o.getRow(t,!0);n?(r.getCanMultiSelect()||Object.keys(e).forEach(t=>delete e[t]),r.getCanSelect()&&(e[t]=!0)):delete e[t],l&&null!=(i=r.subRows)&&i.length&&r.getCanSelectSubRows()&&r.subRows.forEach(t=>L(e,t.id,n,l,o))};function A(e,t){let n=e.getState().rowSelection,l=[],o={},i=function(e,t){return e.map(e=>{var t;let r=H(e,n);if(r&&(l.push(e),o[e.id]=e),null!=(t=e.subRows)&&t.length&&(e={...e,subRows:i(e.subRows)}),r)return e}).filter(Boolean)};return{rows:i(t.rows),flatRows:l,rowsById:o}}function H(e,t){var n;return null!=(n=t[e.id])&&n}function D(e,t,n){var l;if(!(null!=(l=e.subRows)&&l.length))return!1;let o=!0,i=!1;return e.subRows.forEach(e=>{if((!i||o)&&(e.getCanSelect()&&(H(e,t)?i=!0:o=!1),e.subRows&&e.subRows.length)){let n=D(e,t);"all"===n?i=!0:("some"===n&&(i=!0),o=!1)}}),o?"all":!!i&&"some"}let z=/([0-9]+)/gm;function O(e,t){return e===t?0:e>t?1:-1}function T(e){return"number"==typeof e?isNaN(e)||e===1/0||e===-1/0?"":String(e):"string"==typeof e?e:""}function k(e,t){let n=e.split(z).filter(Boolean),l=t.split(z).filter(Boolean);for(;n.length&&l.length;){let e=n.shift(),t=l.shift(),o=parseInt(e,10),i=parseInt(t,10),r=[o,i].sort();if(isNaN(r[0])){if(e>t)return 1;if(t>e)return -1;continue}if(isNaN(r[1]))return isNaN(o)?-1:1;if(o>i)return 1;if(i>o)return -1}return n.length-l.length}let B={alphanumeric:(e,t,n)=>k(T(e.getValue(n)).toLowerCase(),T(t.getValue(n)).toLowerCase()),alphanumericCaseSensitive:(e,t,n)=>k(T(e.getValue(n)),T(t.getValue(n))),text:(e,t,n)=>O(T(e.getValue(n)).toLowerCase(),T(t.getValue(n)).toLowerCase()),textCaseSensitive:(e,t,n)=>O(T(e.getValue(n)),T(t.getValue(n))),datetime:(e,t,n)=>{let l=e.getValue(n),o=t.getValue(n);return l>o?1:l<o?-1:0},basic:(e,t,n)=>O(e.getValue(n),t.getValue(n))},N=[{createTable:e=>{e.getHeaderGroups=r(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,l,o)=>{var i,r;let u=null!=(i=null==l?void 0:l.map(e=>n.find(t=>t.id===e)).filter(Boolean))?i:[],a=null!=(r=null==o?void 0:o.map(e=>n.find(t=>t.id===e)).filter(Boolean))?r:[];return g(t,[...u,...n.filter(e=>!(null!=l&&l.includes(e.id))&&!(null!=o&&o.includes(e.id))),...a],e)},u(e.options,a,"getHeaderGroups")),e.getCenterHeaderGroups=r(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(t,n,l,o)=>g(t,n=n.filter(e=>!(null!=l&&l.includes(e.id))&&!(null!=o&&o.includes(e.id))),e,"center"),u(e.options,a,"getCenterHeaderGroups")),e.getLeftHeaderGroups=r(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.left],(t,n,l)=>{var o;return g(t,null!=(o=null==l?void 0:l.map(e=>n.find(t=>t.id===e)).filter(Boolean))?o:[],e,"left")},u(e.options,a,"getLeftHeaderGroups")),e.getRightHeaderGroups=r(()=>[e.getAllColumns(),e.getVisibleLeafColumns(),e.getState().columnPinning.right],(t,n,l)=>{var o;return g(t,null!=(o=null==l?void 0:l.map(e=>n.find(t=>t.id===e)).filter(Boolean))?o:[],e,"right")},u(e.options,a,"getRightHeaderGroups")),e.getFooterGroups=r(()=>[e.getHeaderGroups()],e=>[...e].reverse(),u(e.options,a,"getFooterGroups")),e.getLeftFooterGroups=r(()=>[e.getLeftHeaderGroups()],e=>[...e].reverse(),u(e.options,a,"getLeftFooterGroups")),e.getCenterFooterGroups=r(()=>[e.getCenterHeaderGroups()],e=>[...e].reverse(),u(e.options,a,"getCenterFooterGroups")),e.getRightFooterGroups=r(()=>[e.getRightHeaderGroups()],e=>[...e].reverse(),u(e.options,a,"getRightFooterGroups")),e.getFlatHeaders=r(()=>[e.getHeaderGroups()],e=>e.map(e=>e.headers).flat(),u(e.options,a,"getFlatHeaders")),e.getLeftFlatHeaders=r(()=>[e.getLeftHeaderGroups()],e=>e.map(e=>e.headers).flat(),u(e.options,a,"getLeftFlatHeaders")),e.getCenterFlatHeaders=r(()=>[e.getCenterHeaderGroups()],e=>e.map(e=>e.headers).flat(),u(e.options,a,"getCenterFlatHeaders")),e.getRightFlatHeaders=r(()=>[e.getRightHeaderGroups()],e=>e.map(e=>e.headers).flat(),u(e.options,a,"getRightFlatHeaders")),e.getCenterLeafHeaders=r(()=>[e.getCenterFlatHeaders()],e=>e.filter(e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}),u(e.options,a,"getCenterLeafHeaders")),e.getLeftLeafHeaders=r(()=>[e.getLeftFlatHeaders()],e=>e.filter(e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}),u(e.options,a,"getLeftLeafHeaders")),e.getRightLeafHeaders=r(()=>[e.getRightFlatHeaders()],e=>e.filter(e=>{var t;return!(null!=(t=e.subHeaders)&&t.length)}),u(e.options,a,"getRightLeafHeaders")),e.getLeafHeaders=r(()=>[e.getLeftHeaderGroups(),e.getCenterHeaderGroups(),e.getRightHeaderGroups()],(e,t,n)=>{var l,o,i,r,u,a;return[...null!=(l=null==(o=e[0])?void 0:o.headers)?l:[],...null!=(i=null==(r=t[0])?void 0:r.headers)?i:[],...null!=(u=null==(a=n[0])?void 0:a.headers)?u:[]].map(e=>e.getLeafHeaders()).flat()},u(e.options,a,"getLeafHeaders"))}},{getInitialState:e=>({columnVisibility:{},...e}),getDefaultOptions:e=>({onColumnVisibilityChange:o("columnVisibility",e)}),createColumn:(e,t)=>{e.toggleVisibility=n=>{e.getCanHide()&&t.setColumnVisibility(t=>({...t,[e.id]:null!=n?n:!e.getIsVisible()}))},e.getIsVisible=()=>{var n,l;let o=e.columns;return null==(n=o.length?o.some(e=>e.getIsVisible()):null==(l=t.getState().columnVisibility)?void 0:l[e.id])||n},e.getCanHide=()=>{var n,l;return(null==(n=e.columnDef.enableHiding)||n)&&(null==(l=t.options.enableHiding)||l)},e.getToggleVisibilityHandler=()=>t=>{null==e.toggleVisibility||e.toggleVisibility(t.target.checked)}},createRow:(e,t)=>{e._getAllVisibleCells=r(()=>[e.getAllCells(),t.getState().columnVisibility],e=>e.filter(e=>e.column.getIsVisible()),u(t.options,"debugRows","_getAllVisibleCells")),e.getVisibleCells=r(()=>[e.getLeftVisibleCells(),e.getCenterVisibleCells(),e.getRightVisibleCells()],(e,t,n)=>[...e,...t,...n],u(t.options,"debugRows","getVisibleCells"))},createTable:e=>{let t=(t,n)=>r(()=>[n(),n().filter(e=>e.getIsVisible()).map(e=>e.id).join("_")],e=>e.filter(e=>null==e.getIsVisible?void 0:e.getIsVisible()),u(e.options,"debugColumns",t));e.getVisibleFlatColumns=t("getVisibleFlatColumns",()=>e.getAllFlatColumns()),e.getVisibleLeafColumns=t("getVisibleLeafColumns",()=>e.getAllLeafColumns()),e.getLeftVisibleLeafColumns=t("getLeftVisibleLeafColumns",()=>e.getLeftLeafColumns()),e.getRightVisibleLeafColumns=t("getRightVisibleLeafColumns",()=>e.getRightLeafColumns()),e.getCenterVisibleLeafColumns=t("getCenterVisibleLeafColumns",()=>e.getCenterLeafColumns()),e.setColumnVisibility=t=>null==e.options.onColumnVisibilityChange?void 0:e.options.onColumnVisibilityChange(t),e.resetColumnVisibility=t=>{var n;e.setColumnVisibility(t?{}:null!=(n=e.initialState.columnVisibility)?n:{})},e.toggleAllColumnsVisible=t=>{var n;t=null!=(n=t)?n:!e.getIsAllColumnsVisible(),e.setColumnVisibility(e.getAllLeafColumns().reduce((e,n)=>({...e,[n.id]:t||!(null!=n.getCanHide&&n.getCanHide())}),{}))},e.getIsAllColumnsVisible=()=>!e.getAllLeafColumns().some(e=>!(null!=e.getIsVisible&&e.getIsVisible())),e.getIsSomeColumnsVisible=()=>e.getAllLeafColumns().some(e=>null==e.getIsVisible?void 0:e.getIsVisible()),e.getToggleAllColumnsVisibilityHandler=()=>t=>{var n;e.toggleAllColumnsVisible(null==(n=t.target)?void 0:n.checked)}}},{getInitialState:e=>({columnOrder:[],...e}),getDefaultOptions:e=>({onColumnOrderChange:o("columnOrder",e)}),createColumn:(e,t)=>{e.getIndex=r(e=>[_(t,e)],t=>t.findIndex(t=>t.id===e.id),u(t.options,"debugColumns","getIndex")),e.getIsFirstColumn=n=>{var l;return(null==(l=_(t,n)[0])?void 0:l.id)===e.id},e.getIsLastColumn=n=>{var l;let o=_(t,n);return(null==(l=o[o.length-1])?void 0:l.id)===e.id}},createTable:e=>{e.setColumnOrder=t=>null==e.options.onColumnOrderChange?void 0:e.options.onColumnOrderChange(t),e.resetColumnOrder=t=>{var n;e.setColumnOrder(t?[]:null!=(n=e.initialState.columnOrder)?n:[])},e._getOrderColumnsFn=r(()=>[e.getState().columnOrder,e.getState().grouping,e.options.groupedColumnMode],(e,t,n)=>l=>{let o=[];if(null!=e&&e.length){let t=[...e],n=[...l];for(;n.length&&t.length;){let e=t.shift(),l=n.findIndex(t=>t.id===e);l>-1&&o.push(n.splice(l,1)[0])}o=[...o,...n]}else o=l;return function(e,t,n){if(!(null!=t&&t.length)||!n)return e;let l=e.filter(e=>!t.includes(e.id));return"remove"===n?l:[...t.map(t=>e.find(e=>e.id===t)).filter(Boolean),...l]}(o,t,n)},u(e.options,"debugTable","_getOrderColumnsFn"))}},{getInitialState:e=>({columnPinning:V(),...e}),getDefaultOptions:e=>({onColumnPinningChange:o("columnPinning",e)}),createColumn:(e,t)=>{e.pin=n=>{let l=e.getLeafColumns().map(e=>e.id).filter(Boolean);t.setColumnPinning(e=>{var t,o,i,r,u,a;return"right"===n?{left:(null!=(i=null==e?void 0:e.left)?i:[]).filter(e=>!(null!=l&&l.includes(e))),right:[...(null!=(r=null==e?void 0:e.right)?r:[]).filter(e=>!(null!=l&&l.includes(e))),...l]}:"left"===n?{left:[...(null!=(u=null==e?void 0:e.left)?u:[]).filter(e=>!(null!=l&&l.includes(e))),...l],right:(null!=(a=null==e?void 0:e.right)?a:[]).filter(e=>!(null!=l&&l.includes(e)))}:{left:(null!=(t=null==e?void 0:e.left)?t:[]).filter(e=>!(null!=l&&l.includes(e))),right:(null!=(o=null==e?void 0:e.right)?o:[]).filter(e=>!(null!=l&&l.includes(e)))}})},e.getCanPin=()=>e.getLeafColumns().some(e=>{var n,l,o;return(null==(n=e.columnDef.enablePinning)||n)&&(null==(l=null!=(o=t.options.enableColumnPinning)?o:t.options.enablePinning)||l)}),e.getIsPinned=()=>{let n=e.getLeafColumns().map(e=>e.id),{left:l,right:o}=t.getState().columnPinning,i=n.some(e=>null==l?void 0:l.includes(e)),r=n.some(e=>null==o?void 0:o.includes(e));return i?"left":!!r&&"right"},e.getPinnedIndex=()=>{var n,l;let o=e.getIsPinned();return o?null!=(n=null==(l=t.getState().columnPinning)||null==(l=l[o])?void 0:l.indexOf(e.id))?n:-1:0}},createRow:(e,t)=>{e.getCenterVisibleCells=r(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left,t.getState().columnPinning.right],(e,t,n)=>{let l=[...null!=t?t:[],...null!=n?n:[]];return e.filter(e=>!l.includes(e.column.id))},u(t.options,"debugRows","getCenterVisibleCells")),e.getLeftVisibleCells=r(()=>[e._getAllVisibleCells(),t.getState().columnPinning.left],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.column.id===t)).filter(Boolean).map(e=>({...e,position:"left"})),u(t.options,"debugRows","getLeftVisibleCells")),e.getRightVisibleCells=r(()=>[e._getAllVisibleCells(),t.getState().columnPinning.right],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.column.id===t)).filter(Boolean).map(e=>({...e,position:"right"})),u(t.options,"debugRows","getRightVisibleCells"))},createTable:e=>{e.setColumnPinning=t=>null==e.options.onColumnPinningChange?void 0:e.options.onColumnPinningChange(t),e.resetColumnPinning=t=>{var n,l;return e.setColumnPinning(t?V():null!=(n=null==(l=e.initialState)?void 0:l.columnPinning)?n:V())},e.getIsSomeColumnsPinned=t=>{var n,l,o;let i=e.getState().columnPinning;return t?!!(null==(n=i[t])?void 0:n.length):!!((null==(l=i.left)?void 0:l.length)||(null==(o=i.right)?void 0:o.length))},e.getLeftLeafColumns=r(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.id===t)).filter(Boolean),u(e.options,"debugColumns","getLeftLeafColumns")),e.getRightLeafColumns=r(()=>[e.getAllLeafColumns(),e.getState().columnPinning.right],(e,t)=>(null!=t?t:[]).map(t=>e.find(e=>e.id===t)).filter(Boolean),u(e.options,"debugColumns","getRightLeafColumns")),e.getCenterLeafColumns=r(()=>[e.getAllLeafColumns(),e.getState().columnPinning.left,e.getState().columnPinning.right],(e,t,n)=>{let l=[...null!=t?t:[],...null!=n?n:[]];return e.filter(e=>!l.includes(e.id))},u(e.options,"debugColumns","getCenterLeafColumns"))}},{createColumn:(e,t)=>{e._getFacetedRowModel=t.options.getFacetedRowModel&&t.options.getFacetedRowModel(t,e.id),e.getFacetedRowModel=()=>e._getFacetedRowModel?e._getFacetedRowModel():t.getPreFilteredRowModel(),e._getFacetedUniqueValues=t.options.getFacetedUniqueValues&&t.options.getFacetedUniqueValues(t,e.id),e.getFacetedUniqueValues=()=>e._getFacetedUniqueValues?e._getFacetedUniqueValues():new Map,e._getFacetedMinMaxValues=t.options.getFacetedMinMaxValues&&t.options.getFacetedMinMaxValues(t,e.id),e.getFacetedMinMaxValues=()=>{if(e._getFacetedMinMaxValues)return e._getFacetedMinMaxValues()}}},{getDefaultColumnDef:()=>({filterFn:"auto"}),getInitialState:e=>({columnFilters:[],...e}),getDefaultOptions:e=>({onColumnFiltersChange:o("columnFilters",e),filterFromLeafRows:!1,maxLeafRowFilterDepth:100}),createColumn:(e,t)=>{e.getAutoFilterFn=()=>{let n=t.getCoreRowModel().flatRows[0],l=null==n?void 0:n.getValue(e.id);return"string"==typeof l?b.includesString:"number"==typeof l?b.inNumberRange:"boolean"==typeof l||null!==l&&"object"==typeof l?b.equals:Array.isArray(l)?b.arrIncludes:b.weakEquals},e.getFilterFn=()=>{var n,l;return i(e.columnDef.filterFn)?e.columnDef.filterFn:"auto"===e.columnDef.filterFn?e.getAutoFilterFn():null!=(n=null==(l=t.options.filterFns)?void 0:l[e.columnDef.filterFn])?n:b[e.columnDef.filterFn]},e.getCanFilter=()=>{var n,l,o;return(null==(n=e.columnDef.enableColumnFilter)||n)&&(null==(l=t.options.enableColumnFilters)||l)&&(null==(o=t.options.enableFilters)||o)&&!!e.accessorFn},e.getIsFiltered=()=>e.getFilterIndex()>-1,e.getFilterValue=()=>{var n;return null==(n=t.getState().columnFilters)||null==(n=n.find(t=>t.id===e.id))?void 0:n.value},e.getFilterIndex=()=>{var n,l;return null!=(n=null==(l=t.getState().columnFilters)?void 0:l.findIndex(t=>t.id===e.id))?n:-1},e.setFilterValue=n=>{t.setColumnFilters(t=>{var o,i;let r=e.getFilterFn(),u=null==t?void 0:t.find(t=>t.id===e.id),a=l(n,u?u.value:void 0);if(F(r,a,e))return null!=(o=null==t?void 0:t.filter(t=>t.id!==e.id))?o:[];let s={id:e.id,value:a};return u?null!=(i=null==t?void 0:t.map(t=>t.id===e.id?s:t))?i:[]:null!=t&&t.length?[...t,s]:[s]})}},createRow:(e,t)=>{e.columnFilters={},e.columnFiltersMeta={}},createTable:e=>{e.setColumnFilters=t=>{let n=e.getAllLeafColumns();null==e.options.onColumnFiltersChange||e.options.onColumnFiltersChange(e=>{var o;return null==(o=l(t,e))?void 0:o.filter(e=>{let t=n.find(t=>t.id===e.id);return!(t&&F(t.getFilterFn(),e.value,t))})})},e.resetColumnFilters=t=>{var n,l;e.setColumnFilters(t?[]:null!=(n=null==(l=e.initialState)?void 0:l.columnFilters)?n:[])},e.getPreFilteredRowModel=()=>e.getCoreRowModel(),e.getFilteredRowModel=()=>(!e._getFilteredRowModel&&e.options.getFilteredRowModel&&(e._getFilteredRowModel=e.options.getFilteredRowModel(e)),e.options.manualFiltering||!e._getFilteredRowModel)?e.getPreFilteredRowModel():e._getFilteredRowModel()}},{createTable:e=>{e._getGlobalFacetedRowModel=e.options.getFacetedRowModel&&e.options.getFacetedRowModel(e,"__global__"),e.getGlobalFacetedRowModel=()=>e.options.manualFiltering||!e._getGlobalFacetedRowModel?e.getPreFilteredRowModel():e._getGlobalFacetedRowModel(),e._getGlobalFacetedUniqueValues=e.options.getFacetedUniqueValues&&e.options.getFacetedUniqueValues(e,"__global__"),e.getGlobalFacetedUniqueValues=()=>e._getGlobalFacetedUniqueValues?e._getGlobalFacetedUniqueValues():new Map,e._getGlobalFacetedMinMaxValues=e.options.getFacetedMinMaxValues&&e.options.getFacetedMinMaxValues(e,"__global__"),e.getGlobalFacetedMinMaxValues=()=>{if(e._getGlobalFacetedMinMaxValues)return e._getGlobalFacetedMinMaxValues()}}},{getInitialState:e=>({globalFilter:void 0,...e}),getDefaultOptions:e=>({onGlobalFilterChange:o("globalFilter",e),globalFilterFn:"auto",getColumnCanGlobalFilter:t=>{var n;let l=null==(n=e.getCoreRowModel().flatRows[0])||null==(n=n._getAllCellsByColumnId()[t.id])?void 0:n.getValue();return"string"==typeof l||"number"==typeof l}}),createColumn:(e,t)=>{e.getCanGlobalFilter=()=>{var n,l,o,i;return(null==(n=e.columnDef.enableGlobalFilter)||n)&&(null==(l=t.options.enableGlobalFilter)||l)&&(null==(o=t.options.enableFilters)||o)&&(null==(i=null==t.options.getColumnCanGlobalFilter?void 0:t.options.getColumnCanGlobalFilter(e))||i)&&!!e.accessorFn}},createTable:e=>{e.getGlobalAutoFilterFn=()=>b.includesString,e.getGlobalFilterFn=()=>{var t,n;let{globalFilterFn:l}=e.options;return i(l)?l:"auto"===l?e.getGlobalAutoFilterFn():null!=(t=null==(n=e.options.filterFns)?void 0:n[l])?t:b[l]},e.setGlobalFilter=t=>{null==e.options.onGlobalFilterChange||e.options.onGlobalFilterChange(t)},e.resetGlobalFilter=t=>{e.setGlobalFilter(t?void 0:e.initialState.globalFilter)}}},{getInitialState:e=>({sorting:[],...e}),getDefaultColumnDef:()=>({sortingFn:"auto",sortUndefined:1}),getDefaultOptions:e=>({onSortingChange:o("sorting",e),isMultiSortEvent:e=>e.shiftKey}),createColumn:(e,t)=>{e.getAutoSortingFn=()=>{let n=t.getFilteredRowModel().flatRows.slice(10),l=!1;for(let t of n){let n=null==t?void 0:t.getValue(e.id);if("[object Date]"===Object.prototype.toString.call(n))return B.datetime;if("string"==typeof n&&(l=!0,n.split(z).length>1))return B.alphanumeric}return l?B.text:B.basic},e.getAutoSortDir=()=>{let n=t.getFilteredRowModel().flatRows[0];return"string"==typeof(null==n?void 0:n.getValue(e.id))?"asc":"desc"},e.getSortingFn=()=>{var n,l;if(!e)throw Error();return i(e.columnDef.sortingFn)?e.columnDef.sortingFn:"auto"===e.columnDef.sortingFn?e.getAutoSortingFn():null!=(n=null==(l=t.options.sortingFns)?void 0:l[e.columnDef.sortingFn])?n:B[e.columnDef.sortingFn]},e.toggleSorting=(n,l)=>{let o=e.getNextSortingOrder(),i=null!=n;t.setSorting(r=>{let u;let a=null==r?void 0:r.find(t=>t.id===e.id),s=null==r?void 0:r.findIndex(t=>t.id===e.id),g=[],d=i?n:"desc"===o;if("toggle"!=(u=null!=r&&r.length&&e.getCanMultiSort()&&l?a?"toggle":"add":null!=r&&r.length&&s!==r.length-1?"replace":a?"toggle":"replace")||i||o||(u="remove"),"add"===u){var c;(g=[...r,{id:e.id,desc:d}]).splice(0,g.length-(null!=(c=t.options.maxMultiSortColCount)?c:Number.MAX_SAFE_INTEGER))}else g="toggle"===u?r.map(t=>t.id===e.id?{...t,desc:d}:t):"remove"===u?r.filter(t=>t.id!==e.id):[{id:e.id,desc:d}];return g})},e.getFirstSortDir=()=>{var n,l;return(null!=(n=null!=(l=e.columnDef.sortDescFirst)?l:t.options.sortDescFirst)?n:"desc"===e.getAutoSortDir())?"desc":"asc"},e.getNextSortingOrder=n=>{var l,o;let i=e.getFirstSortDir(),r=e.getIsSorted();return r?(r===i||null!=(l=t.options.enableSortingRemoval)&&!l||!!n&&null!=(o=t.options.enableMultiRemove)&&!o)&&("desc"===r?"asc":"desc"):i},e.getCanSort=()=>{var n,l;return(null==(n=e.columnDef.enableSorting)||n)&&(null==(l=t.options.enableSorting)||l)&&!!e.accessorFn},e.getCanMultiSort=()=>{var n,l;return null!=(n=null!=(l=e.columnDef.enableMultiSort)?l:t.options.enableMultiSort)?n:!!e.accessorFn},e.getIsSorted=()=>{var n;let l=null==(n=t.getState().sorting)?void 0:n.find(t=>t.id===e.id);return!!l&&(l.desc?"desc":"asc")},e.getSortIndex=()=>{var n,l;return null!=(n=null==(l=t.getState().sorting)?void 0:l.findIndex(t=>t.id===e.id))?n:-1},e.clearSorting=()=>{t.setSorting(t=>null!=t&&t.length?t.filter(t=>t.id!==e.id):[])},e.getToggleSortingHandler=()=>{let n=e.getCanSort();return l=>{n&&(null==l.persist||l.persist(),null==e.toggleSorting||e.toggleSorting(void 0,!!e.getCanMultiSort()&&(null==t.options.isMultiSortEvent?void 0:t.options.isMultiSortEvent(l))))}}},createTable:e=>{e.setSorting=t=>null==e.options.onSortingChange?void 0:e.options.onSortingChange(t),e.resetSorting=t=>{var n,l;e.setSorting(t?[]:null!=(n=null==(l=e.initialState)?void 0:l.sorting)?n:[])},e.getPreSortedRowModel=()=>e.getGroupedRowModel(),e.getSortedRowModel=()=>(!e._getSortedRowModel&&e.options.getSortedRowModel&&(e._getSortedRowModel=e.options.getSortedRowModel(e)),e.options.manualSorting||!e._getSortedRowModel)?e.getPreSortedRowModel():e._getSortedRowModel()}},{getDefaultColumnDef:()=>({aggregatedCell:e=>{var t,n;return null!=(t=null==(n=e.getValue())||null==n.toString?void 0:n.toString())?t:null},aggregationFn:"auto"}),getInitialState:e=>({grouping:[],...e}),getDefaultOptions:e=>({onGroupingChange:o("grouping",e),groupedColumnMode:"reorder"}),createColumn:(e,t)=>{e.toggleGrouping=()=>{t.setGrouping(t=>null!=t&&t.includes(e.id)?t.filter(t=>t!==e.id):[...null!=t?t:[],e.id])},e.getCanGroup=()=>{var n,l;return(null==(n=e.columnDef.enableGrouping)||n)&&(null==(l=t.options.enableGrouping)||l)&&(!!e.accessorFn||!!e.columnDef.getGroupingValue)},e.getIsGrouped=()=>{var n;return null==(n=t.getState().grouping)?void 0:n.includes(e.id)},e.getGroupedIndex=()=>{var n;return null==(n=t.getState().grouping)?void 0:n.indexOf(e.id)},e.getToggleGroupingHandler=()=>{let t=e.getCanGroup();return()=>{t&&e.toggleGrouping()}},e.getAutoAggregationFn=()=>{let n=t.getCoreRowModel().flatRows[0],l=null==n?void 0:n.getValue(e.id);return"number"==typeof l?M.sum:"[object Date]"===Object.prototype.toString.call(l)?M.extent:void 0},e.getAggregationFn=()=>{var n,l;if(!e)throw Error();return i(e.columnDef.aggregationFn)?e.columnDef.aggregationFn:"auto"===e.columnDef.aggregationFn?e.getAutoAggregationFn():null!=(n=null==(l=t.options.aggregationFns)?void 0:l[e.columnDef.aggregationFn])?n:M[e.columnDef.aggregationFn]}},createTable:e=>{e.setGrouping=t=>null==e.options.onGroupingChange?void 0:e.options.onGroupingChange(t),e.resetGrouping=t=>{var n,l;e.setGrouping(t?[]:null!=(n=null==(l=e.initialState)?void 0:l.grouping)?n:[])},e.getPreGroupedRowModel=()=>e.getFilteredRowModel(),e.getGroupedRowModel=()=>(!e._getGroupedRowModel&&e.options.getGroupedRowModel&&(e._getGroupedRowModel=e.options.getGroupedRowModel(e)),e.options.manualGrouping||!e._getGroupedRowModel)?e.getPreGroupedRowModel():e._getGroupedRowModel()},createRow:(e,t)=>{e.getIsGrouped=()=>!!e.groupingColumnId,e.getGroupingValue=n=>{if(e._groupingValuesCache.hasOwnProperty(n))return e._groupingValuesCache[n];let l=t.getColumn(n);return null!=l&&l.columnDef.getGroupingValue?(e._groupingValuesCache[n]=l.columnDef.getGroupingValue(e.original),e._groupingValuesCache[n]):e.getValue(n)},e._groupingValuesCache={}},createCell:(e,t,n,l)=>{e.getIsGrouped=()=>t.getIsGrouped()&&t.id===n.groupingColumnId,e.getIsPlaceholder=()=>!e.getIsGrouped()&&t.getIsGrouped(),e.getIsAggregated=()=>{var t;return!e.getIsGrouped()&&!e.getIsPlaceholder()&&!!(null!=(t=n.subRows)&&t.length)}}},{getInitialState:e=>({expanded:{},...e}),getDefaultOptions:e=>({onExpandedChange:o("expanded",e),paginateExpandedRows:!0}),createTable:e=>{let t=!1,n=!1;e._autoResetExpanded=()=>{var l,o;if(!t){e._queue(()=>{t=!0});return}if(null!=(l=null!=(o=e.options.autoResetAll)?o:e.options.autoResetExpanded)?l:!e.options.manualExpanding){if(n)return;n=!0,e._queue(()=>{e.resetExpanded(),n=!1})}},e.setExpanded=t=>null==e.options.onExpandedChange?void 0:e.options.onExpandedChange(t),e.toggleAllRowsExpanded=t=>{(null!=t?t:!e.getIsAllRowsExpanded())?e.setExpanded(!0):e.setExpanded({})},e.resetExpanded=t=>{var n,l;e.setExpanded(t?{}:null!=(n=null==(l=e.initialState)?void 0:l.expanded)?n:{})},e.getCanSomeRowsExpand=()=>e.getPrePaginationRowModel().flatRows.some(e=>e.getCanExpand()),e.getToggleAllRowsExpandedHandler=()=>t=>{null==t.persist||t.persist(),e.toggleAllRowsExpanded()},e.getIsSomeRowsExpanded=()=>{let t=e.getState().expanded;return!0===t||Object.values(t).some(Boolean)},e.getIsAllRowsExpanded=()=>{let t=e.getState().expanded;return"boolean"==typeof t?!0===t:!(!Object.keys(t).length||e.getRowModel().flatRows.some(e=>!e.getIsExpanded()))},e.getExpandedDepth=()=>{let t=0;return(!0===e.getState().expanded?Object.keys(e.getRowModel().rowsById):Object.keys(e.getState().expanded)).forEach(e=>{let n=e.split(".");t=Math.max(t,n.length)}),t},e.getPreExpandedRowModel=()=>e.getSortedRowModel(),e.getExpandedRowModel=()=>(!e._getExpandedRowModel&&e.options.getExpandedRowModel&&(e._getExpandedRowModel=e.options.getExpandedRowModel(e)),e.options.manualExpanding||!e._getExpandedRowModel)?e.getPreExpandedRowModel():e._getExpandedRowModel()},createRow:(e,t)=>{e.toggleExpanded=n=>{t.setExpanded(l=>{var o;let i=!0===l||!!(null!=l&&l[e.id]),r={};if(!0===l?Object.keys(t.getRowModel().rowsById).forEach(e=>{r[e]=!0}):r=l,n=null!=(o=n)?o:!i,!i&&n)return{...r,[e.id]:!0};if(i&&!n){let{[e.id]:t,...n}=r;return n}return l})},e.getIsExpanded=()=>{var n;let l=t.getState().expanded;return!!(null!=(n=null==t.options.getIsRowExpanded?void 0:t.options.getIsRowExpanded(e))?n:!0===l||(null==l?void 0:l[e.id]))},e.getCanExpand=()=>{var n,l,o;return null!=(n=null==t.options.getRowCanExpand?void 0:t.options.getRowCanExpand(e))?n:(null==(l=t.options.enableExpanding)||l)&&!!(null!=(o=e.subRows)&&o.length)},e.getIsAllParentsExpanded=()=>{let n=!0,l=e;for(;n&&l.parentId;)n=(l=t.getRow(l.parentId,!0)).getIsExpanded();return n},e.getToggleExpandedHandler=()=>{let t=e.getCanExpand();return()=>{t&&e.toggleExpanded()}}}},{getInitialState:e=>({...e,pagination:{...E(),...null==e?void 0:e.pagination}}),getDefaultOptions:e=>({onPaginationChange:o("pagination",e)}),createTable:e=>{let t=!1,n=!1;e._autoResetPageIndex=()=>{var l,o;if(!t){e._queue(()=>{t=!0});return}if(null!=(l=null!=(o=e.options.autoResetAll)?o:e.options.autoResetPageIndex)?l:!e.options.manualPagination){if(n)return;n=!0,e._queue(()=>{e.resetPageIndex(),n=!1})}},e.setPagination=t=>null==e.options.onPaginationChange?void 0:e.options.onPaginationChange(e=>l(t,e)),e.resetPagination=t=>{var n;e.setPagination(t?E():null!=(n=e.initialState.pagination)?n:E())},e.setPageIndex=t=>{e.setPagination(n=>{let o=l(t,n.pageIndex);return o=Math.max(0,Math.min(o,void 0===e.options.pageCount||-1===e.options.pageCount?Number.MAX_SAFE_INTEGER:e.options.pageCount-1)),{...n,pageIndex:o}})},e.resetPageIndex=t=>{var n,l;e.setPageIndex(t?0:null!=(n=null==(l=e.initialState)||null==(l=l.pagination)?void 0:l.pageIndex)?n:0)},e.resetPageSize=t=>{var n,l;e.setPageSize(t?10:null!=(n=null==(l=e.initialState)||null==(l=l.pagination)?void 0:l.pageSize)?n:10)},e.setPageSize=t=>{e.setPagination(e=>{let n=Math.max(1,l(t,e.pageSize)),o=e.pageSize*e.pageIndex;return{...e,pageIndex:Math.floor(o/n),pageSize:n}})},e.setPageCount=t=>e.setPagination(n=>{var o;let i=l(t,null!=(o=e.options.pageCount)?o:-1);return"number"==typeof i&&(i=Math.max(-1,i)),{...n,pageCount:i}}),e.getPageOptions=r(()=>[e.getPageCount()],e=>{let t=[];return e&&e>0&&(t=[...Array(e)].fill(null).map((e,t)=>t)),t},u(e.options,"debugTable","getPageOptions")),e.getCanPreviousPage=()=>e.getState().pagination.pageIndex>0,e.getCanNextPage=()=>{let{pageIndex:t}=e.getState().pagination,n=e.getPageCount();return -1===n||0!==n&&t<n-1},e.previousPage=()=>e.setPageIndex(e=>e-1),e.nextPage=()=>e.setPageIndex(e=>e+1),e.firstPage=()=>e.setPageIndex(0),e.lastPage=()=>e.setPageIndex(e.getPageCount()-1),e.getPrePaginationRowModel=()=>e.getExpandedRowModel(),e.getPaginationRowModel=()=>(!e._getPaginationRowModel&&e.options.getPaginationRowModel&&(e._getPaginationRowModel=e.options.getPaginationRowModel(e)),e.options.manualPagination||!e._getPaginationRowModel)?e.getPrePaginationRowModel():e._getPaginationRowModel(),e.getPageCount=()=>{var t;return null!=(t=e.options.pageCount)?t:Math.ceil(e.getRowCount()/e.getState().pagination.pageSize)},e.getRowCount=()=>{var t;return null!=(t=e.options.rowCount)?t:e.getPrePaginationRowModel().rows.length}}},{getInitialState:e=>({rowPinning:G(),...e}),getDefaultOptions:e=>({onRowPinningChange:o("rowPinning",e)}),createRow:(e,t)=>{e.pin=(n,l,o)=>{let i=l?e.getLeafRows().map(e=>{let{id:t}=e;return t}):[],r=new Set([...o?e.getParentRows().map(e=>{let{id:t}=e;return t}):[],e.id,...i]);t.setRowPinning(e=>{var t,l,o,i,u,a;return"bottom"===n?{top:(null!=(o=null==e?void 0:e.top)?o:[]).filter(e=>!(null!=r&&r.has(e))),bottom:[...(null!=(i=null==e?void 0:e.bottom)?i:[]).filter(e=>!(null!=r&&r.has(e))),...Array.from(r)]}:"top"===n?{top:[...(null!=(u=null==e?void 0:e.top)?u:[]).filter(e=>!(null!=r&&r.has(e))),...Array.from(r)],bottom:(null!=(a=null==e?void 0:e.bottom)?a:[]).filter(e=>!(null!=r&&r.has(e)))}:{top:(null!=(t=null==e?void 0:e.top)?t:[]).filter(e=>!(null!=r&&r.has(e))),bottom:(null!=(l=null==e?void 0:e.bottom)?l:[]).filter(e=>!(null!=r&&r.has(e)))}})},e.getCanPin=()=>{var n;let{enableRowPinning:l,enablePinning:o}=t.options;return"function"==typeof l?l(e):null==(n=null!=l?l:o)||n},e.getIsPinned=()=>{let n=[e.id],{top:l,bottom:o}=t.getState().rowPinning,i=n.some(e=>null==l?void 0:l.includes(e)),r=n.some(e=>null==o?void 0:o.includes(e));return i?"top":!!r&&"bottom"},e.getPinnedIndex=()=>{var n,l;let o=e.getIsPinned();if(!o)return -1;let i=null==(n="top"===o?t.getTopRows():t.getBottomRows())?void 0:n.map(e=>{let{id:t}=e;return t});return null!=(l=null==i?void 0:i.indexOf(e.id))?l:-1}},createTable:e=>{e.setRowPinning=t=>null==e.options.onRowPinningChange?void 0:e.options.onRowPinningChange(t),e.resetRowPinning=t=>{var n,l;return e.setRowPinning(t?G():null!=(n=null==(l=e.initialState)?void 0:l.rowPinning)?n:G())},e.getIsSomeRowsPinned=t=>{var n,l,o;let i=e.getState().rowPinning;return t?!!(null==(n=i[t])?void 0:n.length):!!((null==(l=i.top)?void 0:l.length)||(null==(o=i.bottom)?void 0:o.length))},e._getPinnedRows=(t,n,l)=>{var o;return(null==(o=e.options.keepPinnedRows)||o?(null!=n?n:[]).map(t=>{let n=e.getRow(t,!0);return n.getIsAllParentsExpanded()?n:null}):(null!=n?n:[]).map(e=>t.find(t=>t.id===e))).filter(Boolean).map(e=>({...e,position:l}))},e.getTopRows=r(()=>[e.getRowModel().rows,e.getState().rowPinning.top],(t,n)=>e._getPinnedRows(t,n,"top"),u(e.options,"debugRows","getTopRows")),e.getBottomRows=r(()=>[e.getRowModel().rows,e.getState().rowPinning.bottom],(t,n)=>e._getPinnedRows(t,n,"bottom"),u(e.options,"debugRows","getBottomRows")),e.getCenterRows=r(()=>[e.getRowModel().rows,e.getState().rowPinning.top,e.getState().rowPinning.bottom],(e,t,n)=>{let l=new Set([...null!=t?t:[],...null!=n?n:[]]);return e.filter(e=>!l.has(e.id))},u(e.options,"debugRows","getCenterRows"))}},{getInitialState:e=>({rowSelection:{},...e}),getDefaultOptions:e=>({onRowSelectionChange:o("rowSelection",e),enableRowSelection:!0,enableMultiRowSelection:!0,enableSubRowSelection:!0}),createTable:e=>{e.setRowSelection=t=>null==e.options.onRowSelectionChange?void 0:e.options.onRowSelectionChange(t),e.resetRowSelection=t=>{var n;return e.setRowSelection(t?{}:null!=(n=e.initialState.rowSelection)?n:{})},e.toggleAllRowsSelected=t=>{e.setRowSelection(n=>{t=void 0!==t?t:!e.getIsAllRowsSelected();let l={...n},o=e.getPreGroupedRowModel().flatRows;return t?o.forEach(e=>{e.getCanSelect()&&(l[e.id]=!0)}):o.forEach(e=>{delete l[e.id]}),l})},e.toggleAllPageRowsSelected=t=>e.setRowSelection(n=>{let l=void 0!==t?t:!e.getIsAllPageRowsSelected(),o={...n};return e.getRowModel().rows.forEach(t=>{L(o,t.id,l,!0,e)}),o}),e.getPreSelectedRowModel=()=>e.getCoreRowModel(),e.getSelectedRowModel=r(()=>[e.getState().rowSelection,e.getCoreRowModel()],(t,n)=>Object.keys(t).length?A(e,n):{rows:[],flatRows:[],rowsById:{}},u(e.options,"debugTable","getSelectedRowModel")),e.getFilteredSelectedRowModel=r(()=>[e.getState().rowSelection,e.getFilteredRowModel()],(t,n)=>Object.keys(t).length?A(e,n):{rows:[],flatRows:[],rowsById:{}},u(e.options,"debugTable","getFilteredSelectedRowModel")),e.getGroupedSelectedRowModel=r(()=>[e.getState().rowSelection,e.getSortedRowModel()],(t,n)=>Object.keys(t).length?A(e,n):{rows:[],flatRows:[],rowsById:{}},u(e.options,"debugTable","getGroupedSelectedRowModel")),e.getIsAllRowsSelected=()=>{let t=e.getFilteredRowModel().flatRows,{rowSelection:n}=e.getState(),l=!!(t.length&&Object.keys(n).length);return l&&t.some(e=>e.getCanSelect()&&!n[e.id])&&(l=!1),l},e.getIsAllPageRowsSelected=()=>{let t=e.getPaginationRowModel().flatRows.filter(e=>e.getCanSelect()),{rowSelection:n}=e.getState(),l=!!t.length;return l&&t.some(e=>!n[e.id])&&(l=!1),l},e.getIsSomeRowsSelected=()=>{var t;let n=Object.keys(null!=(t=e.getState().rowSelection)?t:{}).length;return n>0&&n<e.getFilteredRowModel().flatRows.length},e.getIsSomePageRowsSelected=()=>{let t=e.getPaginationRowModel().flatRows;return!e.getIsAllPageRowsSelected()&&t.filter(e=>e.getCanSelect()).some(e=>e.getIsSelected()||e.getIsSomeSelected())},e.getToggleAllRowsSelectedHandler=()=>t=>{e.toggleAllRowsSelected(t.target.checked)},e.getToggleAllPageRowsSelectedHandler=()=>t=>{e.toggleAllPageRowsSelected(t.target.checked)}},createRow:(e,t)=>{e.toggleSelected=(n,l)=>{let o=e.getIsSelected();t.setRowSelection(i=>{var r;if(n=void 0!==n?n:!o,e.getCanSelect()&&o===n)return i;let u={...i};return L(u,e.id,n,null==(r=null==l?void 0:l.selectChildren)||r,t),u})},e.getIsSelected=()=>{let{rowSelection:n}=t.getState();return H(e,n)},e.getIsSomeSelected=()=>{let{rowSelection:n}=t.getState();return"some"===D(e,n)},e.getIsAllSubRowsSelected=()=>{let{rowSelection:n}=t.getState();return"all"===D(e,n)},e.getCanSelect=()=>{var n;return"function"==typeof t.options.enableRowSelection?t.options.enableRowSelection(e):null==(n=t.options.enableRowSelection)||n},e.getCanSelectSubRows=()=>{var n;return"function"==typeof t.options.enableSubRowSelection?t.options.enableSubRowSelection(e):null==(n=t.options.enableSubRowSelection)||n},e.getCanMultiSelect=()=>{var n;return"function"==typeof t.options.enableMultiRowSelection?t.options.enableMultiRowSelection(e):null==(n=t.options.enableMultiRowSelection)||n},e.getToggleSelectedHandler=()=>{let t=e.getCanSelect();return n=>{var l;t&&e.toggleSelected(null==(l=n.target)?void 0:l.checked)}}}},{getDefaultColumnDef:()=>P,getInitialState:e=>({columnSizing:{},columnSizingInfo:I(),...e}),getDefaultOptions:e=>({columnResizeMode:"onEnd",columnResizeDirection:"ltr",onColumnSizingChange:o("columnSizing",e),onColumnSizingInfoChange:o("columnSizingInfo",e)}),createColumn:(e,t)=>{e.getSize=()=>{var n,l,o;let i=t.getState().columnSizing[e.id];return Math.min(Math.max(null!=(n=e.columnDef.minSize)?n:P.minSize,null!=(l=null!=i?i:e.columnDef.size)?l:P.size),null!=(o=e.columnDef.maxSize)?o:P.maxSize)},e.getStart=r(e=>[e,_(t,e),t.getState().columnSizing],(t,n)=>n.slice(0,e.getIndex(t)).reduce((e,t)=>e+t.getSize(),0),u(t.options,"debugColumns","getStart")),e.getAfter=r(e=>[e,_(t,e),t.getState().columnSizing],(t,n)=>n.slice(e.getIndex(t)+1).reduce((e,t)=>e+t.getSize(),0),u(t.options,"debugColumns","getAfter")),e.resetSize=()=>{t.setColumnSizing(t=>{let{[e.id]:n,...l}=t;return l})},e.getCanResize=()=>{var n,l;return(null==(n=e.columnDef.enableResizing)||n)&&(null==(l=t.options.enableColumnResizing)||l)},e.getIsResizing=()=>t.getState().columnSizingInfo.isResizingColumn===e.id},createHeader:(e,t)=>{e.getSize=()=>{let t=0,n=e=>{if(e.subHeaders.length)e.subHeaders.forEach(n);else{var l;t+=null!=(l=e.column.getSize())?l:0}};return n(e),t},e.getStart=()=>{if(e.index>0){let t=e.headerGroup.headers[e.index-1];return t.getStart()+t.getSize()}return 0},e.getResizeHandler=n=>{let l=t.getColumn(e.column.id),o=null==l?void 0:l.getCanResize();return i=>{if(!l||!o||(null==i.persist||i.persist(),y(i)&&i.touches&&i.touches.length>1))return;let r=e.getSize(),u=e?e.getLeafHeaders().map(e=>[e.column.id,e.column.getSize()]):[[l.id,l.getSize()]],a=y(i)?Math.round(i.touches[0].clientX):i.clientX,s={},g=(e,n)=>{"number"==typeof n&&(t.setColumnSizingInfo(e=>{var l,o;let i="rtl"===t.options.columnResizeDirection?-1:1,r=(n-(null!=(l=null==e?void 0:e.startOffset)?l:0))*i,u=Math.max(r/(null!=(o=null==e?void 0:e.startSize)?o:0),-.999999);return e.columnSizingStart.forEach(e=>{let[t,n]=e;s[t]=Math.round(100*Math.max(n+n*u,0))/100}),{...e,deltaOffset:r,deltaPercentage:u}}),("onChange"===t.options.columnResizeMode||"end"===e)&&t.setColumnSizing(e=>({...e,...s})))},d=e=>g("move",e),c=e=>{g("end",e),t.setColumnSizingInfo(e=>({...e,isResizingColumn:!1,startOffset:null,startSize:null,deltaOffset:null,deltaPercentage:null,columnSizingStart:[]}))},p=n||"undefined"!=typeof document?document:null,f={moveHandler:e=>d(e.clientX),upHandler:e=>{null==p||p.removeEventListener("mousemove",f.moveHandler),null==p||p.removeEventListener("mouseup",f.upHandler),c(e.clientX)}},m={moveHandler:e=>(e.cancelable&&(e.preventDefault(),e.stopPropagation()),d(e.touches[0].clientX),!1),upHandler:e=>{var t;null==p||p.removeEventListener("touchmove",m.moveHandler),null==p||p.removeEventListener("touchend",m.upHandler),e.cancelable&&(e.preventDefault(),e.stopPropagation()),c(null==(t=e.touches[0])?void 0:t.clientX)}},v=!!function(){if("boolean"==typeof x)return x;let e=!1;try{let t=()=>{};window.addEventListener("test",t,{get passive(){return e=!0,!1}}),window.removeEventListener("test",t)}catch(t){e=!1}return x=e}()&&{passive:!1};y(i)?(null==p||p.addEventListener("touchmove",m.moveHandler,v),null==p||p.addEventListener("touchend",m.upHandler,v)):(null==p||p.addEventListener("mousemove",f.moveHandler,v),null==p||p.addEventListener("mouseup",f.upHandler,v)),t.setColumnSizingInfo(e=>({...e,startOffset:a,startSize:r,deltaOffset:0,deltaPercentage:0,columnSizingStart:u,isResizingColumn:l.id}))}}},createTable:e=>{e.setColumnSizing=t=>null==e.options.onColumnSizingChange?void 0:e.options.onColumnSizingChange(t),e.setColumnSizingInfo=t=>null==e.options.onColumnSizingInfoChange?void 0:e.options.onColumnSizingInfoChange(t),e.resetColumnSizing=t=>{var n;e.setColumnSizing(t?{}:null!=(n=e.initialState.columnSizing)?n:{})},e.resetHeaderSizeInfo=t=>{var n;e.setColumnSizingInfo(t?I():null!=(n=e.initialState.columnSizingInfo)?n:I())},e.getTotalSize=()=>{var t,n;return null!=(t=null==(n=e.getHeaderGroups()[0])?void 0:n.headers.reduce((e,t)=>e+t.getSize(),0))?t:0},e.getLeftTotalSize=()=>{var t,n;return null!=(t=null==(n=e.getLeftHeaderGroups()[0])?void 0:n.headers.reduce((e,t)=>e+t.getSize(),0))?t:0},e.getCenterTotalSize=()=>{var t,n;return null!=(t=null==(n=e.getCenterHeaderGroups()[0])?void 0:n.headers.reduce((e,t)=>e+t.getSize(),0))?t:0},e.getRightTotalSize=()=>{var t,n;return null!=(t=null==(n=e.getRightHeaderGroups()[0])?void 0:n.headers.reduce((e,t)=>e+t.getSize(),0))?t:0}}}];function q(e){var t,n;let o=[...N,...null!=(t=e._features)?t:[]],i={_features:o},a=i._features.reduce((e,t)=>Object.assign(e,null==t.getDefaultOptions?void 0:t.getDefaultOptions(i)),{}),s=e=>i.options.mergeOptions?i.options.mergeOptions(a,e):{...a,...e},g={...null!=(n=e.initialState)?n:{}};i._features.forEach(e=>{var t;g=null!=(t=null==e.getInitialState?void 0:e.getInitialState(g))?t:g});let d=[],c=!1,p={_features:o,options:{...a,...e},initialState:g,_queue:e=>{d.push(e),c||(c=!0,Promise.resolve().then(()=>{for(;d.length;)d.shift()();c=!1}).catch(e=>setTimeout(()=>{throw e})))},reset:()=>{i.setState(i.initialState)},setOptions:e=>{let t=l(e,i.options);i.options=s(t)},getState:()=>i.options.state,setState:e=>{null==i.options.onStateChange||i.options.onStateChange(e)},_getRowId:(e,t,n)=>{var l;return null!=(l=null==i.options.getRowId?void 0:i.options.getRowId(e,t,n))?l:`${n?[n.id,t].join("."):t}`},getCoreRowModel:()=>(i._getCoreRowModel||(i._getCoreRowModel=i.options.getCoreRowModel(i)),i._getCoreRowModel()),getRowModel:()=>i.getPaginationRowModel(),getRow:(e,t)=>{let n=(t?i.getPrePaginationRowModel():i.getRowModel()).rowsById[e];if(!n&&!(n=i.getCoreRowModel().rowsById[e]))throw Error();return n},_getDefaultColumnDef:r(()=>[i.options.defaultColumn],e=>{var t;return e=null!=(t=e)?t:{},{header:e=>{let t=e.header.column.columnDef;return t.accessorKey?t.accessorKey:t.accessorFn?t.id:null},cell:e=>{var t,n;return null!=(t=null==(n=e.renderValue())||null==n.toString?void 0:n.toString())?t:null},...i._features.reduce((e,t)=>Object.assign(e,null==t.getDefaultColumnDef?void 0:t.getDefaultColumnDef()),{}),...e}},u(e,"debugColumns","_getDefaultColumnDef")),_getColumnDefs:()=>i.options.columns,getAllColumns:r(()=>[i._getColumnDefs()],e=>{let t=function(e,n,l){return void 0===l&&(l=0),e.map(e=>{let o=function(e,t,n,l){var o,i;let a;let s={...e._getDefaultColumnDef(),...t},g=s.accessorKey,d=null!=(o=null!=(i=s.id)?i:g?"function"==typeof String.prototype.replaceAll?g.replaceAll(".","_"):g.replace(/\./g,"_"):void 0)?o:"string"==typeof s.header?s.header:void 0;if(s.accessorFn?a=s.accessorFn:g&&(a=g.includes(".")?e=>{let t=e;for(let e of g.split(".")){var n;t=null==(n=t)?void 0:n[e]}return t}:e=>e[s.accessorKey]),!d)throw Error();let c={id:`${String(d)}`,accessorFn:a,parent:l,depth:n,columnDef:s,columns:[],getFlatColumns:r(()=>[!0],()=>{var e;return[c,...null==(e=c.columns)?void 0:e.flatMap(e=>e.getFlatColumns())]},u(e.options,"debugColumns","column.getFlatColumns")),getLeafColumns:r(()=>[e._getOrderColumnsFn()],e=>{var t;return null!=(t=c.columns)&&t.length?e(c.columns.flatMap(e=>e.getLeafColumns())):[c]},u(e.options,"debugColumns","column.getLeafColumns"))};for(let t of e._features)null==t.createColumn||t.createColumn(c,e);return c}(i,e,l,n);return o.columns=e.columns?t(e.columns,o,l+1):[],o})};return t(e)},u(e,"debugColumns","getAllColumns")),getAllFlatColumns:r(()=>[i.getAllColumns()],e=>e.flatMap(e=>e.getFlatColumns()),u(e,"debugColumns","getAllFlatColumns")),_getAllFlatColumnsById:r(()=>[i.getAllFlatColumns()],e=>e.reduce((e,t)=>(e[t.id]=t,e),{}),u(e,"debugColumns","getAllFlatColumnsById")),getAllLeafColumns:r(()=>[i.getAllColumns(),i._getOrderColumnsFn()],(e,t)=>t(e.flatMap(e=>e.getLeafColumns())),u(e,"debugColumns","getAllLeafColumns")),getColumn:e=>i._getAllFlatColumnsById()[e]};Object.assign(i,p);for(let e=0;e<i._features.length;e++){let t=i._features[e];null==t||null==t.createTable||t.createTable(i)}return i}function j(){return e=>r(()=>[e.options.data],t=>{let n={rows:[],flatRows:[],rowsById:{}},l=function(t,o,i){void 0===o&&(o=0);let r=[];for(let a=0;a<t.length;a++){let s=d(e,e._getRowId(t[a],a,i),t[a],a,o,void 0,null==i?void 0:i.id);if(n.flatRows.push(s),n.rowsById[s.id]=s,r.push(s),e.options.getSubRows){var u;s.originalSubRows=e.options.getSubRows(t[a],a),null!=(u=s.originalSubRows)&&u.length&&(s.subRows=l(s.originalSubRows,o+1,s))}}return r};return n.rows=l(t),n},u(e.options,"debugTable","getRowModel",()=>e._autoResetPageIndex()))}function U(){return e=>r(()=>[e.getPreFilteredRowModel(),e.getState().columnFilters,e.getState().globalFilter],(t,n,l)=>{var o,i;let r,u;if(!t.rows.length||!(null!=n&&n.length)&&!l){for(let e=0;e<t.flatRows.length;e++)t.flatRows[e].columnFilters={},t.flatRows[e].columnFiltersMeta={};return t}let a=[],s=[];(null!=n?n:[]).forEach(t=>{var n;let l=e.getColumn(t.id);if(!l)return;let o=l.getFilterFn();o&&a.push({id:t.id,filterFn:o,resolvedValue:null!=(n=null==o.resolveFilterValue?void 0:o.resolveFilterValue(t.value))?n:t.value})});let g=(null!=n?n:[]).map(e=>e.id),c=e.getGlobalFilterFn(),p=e.getAllLeafColumns().filter(e=>e.getCanGlobalFilter());l&&c&&p.length&&(g.push("__global__"),p.forEach(e=>{var t;s.push({id:e.id,filterFn:c,resolvedValue:null!=(t=null==c.resolveFilterValue?void 0:c.resolveFilterValue(l))?t:l})}));for(let e=0;e<t.flatRows.length;e++){let n=t.flatRows[e];if(n.columnFilters={},a.length)for(let e=0;e<a.length;e++){let t=(r=a[e]).id;n.columnFilters[t]=r.filterFn(n,t,r.resolvedValue,e=>{n.columnFiltersMeta[t]=e})}if(s.length){for(let e=0;e<s.length;e++){let t=(u=s[e]).id;if(u.filterFn(n,t,u.resolvedValue,e=>{n.columnFiltersMeta[t]=e})){n.columnFilters.__global__=!0;break}}!0!==n.columnFilters.__global__&&(n.columnFilters.__global__=!1)}}return o=t.rows,i=e=>{for(let t=0;t<g.length;t++)if(!1===e.columnFilters[g[t]])return!1;return!0},e.options.filterFromLeafRows?function(e,t,n){var l;let o=[],i={},r=null!=(l=n.options.maxLeafRowFilterDepth)?l:100,u=function(e,l){void 0===l&&(l=0);let a=[];for(let g=0;g<e.length;g++){var s;let c=e[g],p=d(n,c.id,c.original,c.index,c.depth,void 0,c.parentId);if(p.columnFilters=c.columnFilters,null!=(s=c.subRows)&&s.length&&l<r){if(p.subRows=u(c.subRows,l+1),t(c=p)&&!p.subRows.length||t(c)||p.subRows.length){a.push(c),i[c.id]=c,o.push(c);continue}}else t(c=p)&&(a.push(c),i[c.id]=c,o.push(c))}return a};return{rows:u(e),flatRows:o,rowsById:i}}(o,i,e):function(e,t,n){var l;let o=[],i={},r=null!=(l=n.options.maxLeafRowFilterDepth)?l:100,u=function(e,l){void 0===l&&(l=0);let a=[];for(let g=0;g<e.length;g++){let c=e[g];if(t(c)){var s;if(null!=(s=c.subRows)&&s.length&&l<r){let e=d(n,c.id,c.original,c.index,c.depth,void 0,c.parentId);e.subRows=u(c.subRows,l+1),c=e}a.push(c),o.push(c),i[c.id]=c}}return a};return{rows:u(e),flatRows:o,rowsById:i}}(o,i,e)},u(e.options,"debugTable","getFilteredRowModel",()=>e._autoResetPageIndex()))}function $(e){return e=>r(()=>[e.getState().pagination,e.getPrePaginationRowModel(),e.options.paginateExpandedRows?void 0:e.getState().expanded],(t,n)=>{let l;if(!n.rows.length)return n;let{pageSize:o,pageIndex:i}=t,{rows:r,flatRows:u,rowsById:a}=n,s=o*i;r=r.slice(s,s+o),(l=e.options.paginateExpandedRows?{rows:r,flatRows:u,rowsById:a}:function(e){let t=[],n=e=>{var l;t.push(e),null!=(l=e.subRows)&&l.length&&e.getIsExpanded()&&e.subRows.forEach(n)};return e.rows.forEach(n),{rows:t,flatRows:e.flatRows,rowsById:e.rowsById}}({rows:r,flatRows:u,rowsById:a})).flatRows=[];let g=e=>{l.flatRows.push(e),e.subRows.length&&e.subRows.forEach(g)};return l.rows.forEach(g),l},u(e.options,"debugTable","getPaginationRowModel"))}function X(){return e=>r(()=>[e.getState().sorting,e.getPreSortedRowModel()],(t,n)=>{if(!n.rows.length||!(null!=t&&t.length))return n;let l=e.getState().sorting,o=[],i=l.filter(t=>{var n;return null==(n=e.getColumn(t.id))?void 0:n.getCanSort()}),r={};i.forEach(t=>{let n=e.getColumn(t.id);n&&(r[t.id]={sortUndefined:n.columnDef.sortUndefined,invertSorting:n.columnDef.invertSorting,sortingFn:n.getSortingFn()})});let u=e=>{let t=e.map(e=>({...e}));return t.sort((e,t)=>{for(let l=0;l<i.length;l+=1){var n;let o=i[l],u=r[o.id],a=u.sortUndefined,s=null!=(n=null==o?void 0:o.desc)&&n,g=0;if(a){let n=e.getValue(o.id),l=t.getValue(o.id),i=void 0===n,r=void 0===l;if(i||r){if("first"===a)return i?-1:1;if("last"===a)return i?1:-1;g=i&&r?0:i?a:-a}}if(0===g&&(g=u.sortingFn(e,t,o.id)),0!==g)return s&&(g*=-1),u.invertSorting&&(g*=-1),g}return e.index-t.index}),t.forEach(e=>{var t;o.push(e),null!=(t=e.subRows)&&t.length&&(e.subRows=u(e.subRows))}),t};return{rows:u(n.rows),flatRows:o,rowsById:n.rowsById}},u(e.options,"debugTable","getSortedRowModel",()=>e._autoResetPageIndex()))}}}]);