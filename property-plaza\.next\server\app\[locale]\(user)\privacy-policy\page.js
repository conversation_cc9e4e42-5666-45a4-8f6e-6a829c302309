"use strict";(()=>{var e={};e.id=6899,e.ids=[6899],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},61282:e=>{e.exports=require("child_process")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},19801:e=>{e.exports=require("os")},55315:e=>{e.exports=require("path")},86624:e=>{e.exports=require("querystring")},76162:e=>{e.exports=require("stream")},82452:e=>{e.exports=require("tls")},74175:e=>{e.exports=require("tty")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},6005:e=>{e.exports=require("node:crypto")},88667:(e,r,t)=>{t.r(r),t.d(r,{GlobalError:()=>s.a,__next_app__:()=>u,originalPathname:()=>d,pages:()=>c,routeModule:()=>m,tree:()=>p}),t(94757),t(52250),t(7505),t(84448),t(81729),t(90996);var a=t(30170),i=t(45002),o=t(83876),s=t.n(o),l=t(66299),n={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);t.d(r,n);let p=["",{children:["[locale]",{children:["(user)",{children:["privacy-policy",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,94757)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user)\\privacy-policy\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,52250)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,7505)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user)\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,80603))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,84448)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,81729)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,90996,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user)\\privacy-policy\\page.tsx"],d="/[locale]/(user)/privacy-policy/page",u={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/[locale]/(user)/privacy-policy/page",pathname:"/[locale]/privacy-policy",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},94757:(e,r,t)=>{t.r(r),t.d(r,{default:()=>P,generateMetadata:()=>h});var a=t(72051),i=t(52845),o=t(69385),s=t(79438),l=t(695);function n(){let e=(0,o.Z)("seeker");return a.jsx(s.Z,{id:"privacy-policy",className:"mt-12",children:a.jsx(l.Z,{title:e("privacyPolicy.title")})})}var p=t(29507),c=t(83266),d=t(38785),u=t(94975);function m({content:e}){return a.jsx(s.Z,{children:a.jsx("article",{className:"prose prose-big max-w-3xl text-seekers-text mb-4",children:a.jsx(u.YI,{value:e.body,components:{block:{h1:({children:e})=>a.jsx("h2",{className:"text-2xl font-semibold text-seekers-text mt-4",children:e}),h2:({children:e})=>a.jsx("h3",{className:"text-xl font-semibold mt-4",children:e}),h3:({children:e})=>a.jsx("h3",{className:"text-lg font-semibold mt-4",children:e}),h4:({children:e})=>a.jsx("h3",{className:"",children:e}),normal:({children:e})=>a.jsx("p",{className:" leading-relaxed mt-2",children:e})},list:{number:({children:e})=>a.jsx("ol",{className:"list-decimal list-inside mt-2",children:e}),bullet:({children:e})=>a.jsx("ul",{className:"list-disc pl-4 mt-2",children:e})}}})})})}var x=t(92898),y=t(93844);async function h(){let e=await (0,p.Z)("seeker"),r=process.env.USER_DOMAIN||"https://www.property-plaza.com/",t=await (0,c.Z)()||y.DI.defaultLocale;return{title:e("metadata.privacyPolicy.title"),description:e("metadata.privacyPolicy.description"),alternates:{canonical:r+t+x.Ph,languages:{id:r+`id${x.Ph}`,en:r+`en${x.Ph}`,"x-default":r+x.Ph.replace("/","")}},openGraph:{title:e("metadata.privacyPolicy.title"),description:e("metadata.privacyPolicy.description"),images:[{url:r+"og.png",width:1200,height:630,alt:"Property Plaza"}],type:"website",url:r+x.Ph.replace("/",""),countryName:"Indonesia",emails:"<EMAIL>",locale:t,alternateLocale:y.DI.locales,siteName:"Property plaza"},applicationName:"Property plaza",icons:["favicon.ico"],twitter:{card:"summary_large_image",title:e("metadata.privacyPolicy.description"),description:e("metadata.privacyPolicy.description"),images:[r+"og.jpg"]},robots:{index:!0,follow:!0}}}async function P(){let e=(0,i.cookies)();e.get("NEXT_LOCALE")?.value,await (0,p.Z)("seeker");let r=await (0,d.YY)("en");return(0,a.jsxs)(a.Fragment,{children:[a.jsx(n,{}),a.jsx(m,{content:r[0]})]})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[9379,5063,4916,9467,4859,5268,3327,8530,6136,7146,4975,6666,9965,595,2232],()=>t(88667));module.exports=a})();