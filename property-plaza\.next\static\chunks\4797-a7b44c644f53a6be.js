"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4797],{74797:function(e,t,r){r.d(t,{VY:function(){return eL},ZA:function(){return eH},JO:function(){return eW},ck:function(){return eO},wU:function(){return eK},eT:function(){return eF},__:function(){return eB},h_:function(){return e_},fC:function(){return eP},$G:function(){return ez},u_:function(){return eU},Z0:function(){return eZ},xz:function(){return eD},B4:function(){return eV},l_:function(){return eA}});var n=r(2265),l=r(54887),o=r(62484),i=r(6741),a=r(71605),s=r(98575),u=r(73966),d=r(29114),c=r(22308),p=r(86097),f=r(99103),v=r(99255),h=r(21107),m=r(83832),g=r(82912),w=r(57437),x=n.forwardRef((e,t)=>{let{children:r,...l}=e,o=n.Children.toArray(r),i=o.find(S);if(i){let e=i.props.children,r=o.map(t=>t!==i?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,w.jsx)(y,{...l,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,w.jsx)(y,{...l,ref:t,children:r})});x.displayName="Slot";var y=n.forwardRef((e,t)=>{let{children:r,...l}=e;if(n.isValidElement(r)){let e,o;let i=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref;return n.cloneElement(r,{...function(e,t){let r={...t};for(let n in t){let l=e[n],o=t[n];/^on[A-Z]/.test(n)?l&&o?r[n]=(...e)=>{o(...e),l(...e)}:l&&(r[n]=l):"style"===n?r[n]={...l,...o}:"className"===n&&(r[n]=[l,o].filter(Boolean).join(" "))}return{...e,...r}}(l,r.props),ref:t?(0,s.F)(t,i):i})}return n.Children.count(r)>1?n.Children.only(null):null});y.displayName="SlotClone";var b=({children:e})=>(0,w.jsx)(w.Fragment,{children:e});function S(e){return n.isValidElement(e)&&e.type===b}var C=r(26606),j=r(80886),R=r(61188),E=r(6718),M=r(95098),T=r(5478),k=r(60703),I=[" ","Enter","ArrowUp","ArrowDown"],N=[" ","Enter"],P="Select",[D,V,W]=(0,a.B)(P),[_,L]=(0,u.b)(P,[W,h.D7]),A=(0,h.D7)(),[H,B]=_(P),[O,F]=_(P),K=e=>{let{__scopeSelect:t,children:r,open:l,defaultOpen:o,onOpenChange:i,value:a,defaultValue:s,onValueChange:u,dir:c,name:p,autoComplete:f,disabled:m,required:g}=e,x=A(t),[y,b]=n.useState(null),[S,C]=n.useState(null),[R,E]=n.useState(!1),M=(0,d.gm)(c),[T=!1,k]=(0,j.T)({prop:l,defaultProp:o,onChange:i}),[I,N]=(0,j.T)({prop:a,defaultProp:s,onChange:u}),P=n.useRef(null),V=!y||!!y.closest("form"),[W,_]=n.useState(new Set),L=Array.from(W).map(e=>e.props.value).join(";");return(0,w.jsx)(h.fC,{...x,children:(0,w.jsxs)(H,{required:g,scope:t,trigger:y,onTriggerChange:b,valueNode:S,onValueNodeChange:C,valueNodeHasChildren:R,onValueNodeHasChildrenChange:E,contentId:(0,v.M)(),value:I,onValueChange:N,open:T,onOpenChange:k,dir:M,triggerPointerDownPosRef:P,disabled:m,children:[(0,w.jsx)(D.Provider,{scope:t,children:(0,w.jsx)(O,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{_(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{_(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),V?(0,w.jsxs)(ek,{"aria-hidden":!0,required:g,tabIndex:-1,name:p,autoComplete:f,value:I,onChange:e=>N(e.target.value),disabled:m,children:[void 0===I?(0,w.jsx)("option",{value:""}):null,Array.from(W)]},L):null]})})};K.displayName=P;var U="SelectTrigger",z=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:n=!1,...l}=e,o=A(r),a=B(U,r),u=a.disabled||n,d=(0,s.e)(t,a.onTriggerChange),c=V(r),[p,f,v]=eI(e=>{let t=c().filter(e=>!e.disabled),r=t.find(e=>e.value===a.value),n=eN(t,e,r);void 0!==n&&a.onValueChange(n.value)}),m=()=>{u||(a.onOpenChange(!0),v())};return(0,w.jsx)(h.ee,{asChild:!0,...o,children:(0,w.jsx)(g.WV.button,{type:"button",role:"combobox","aria-controls":a.contentId,"aria-expanded":a.open,"aria-required":a.required,"aria-autocomplete":"none",dir:a.dir,"data-state":a.open?"open":"closed",disabled:u,"data-disabled":u?"":void 0,"data-placeholder":eT(a.value)?"":void 0,...l,ref:d,onClick:(0,i.M)(l.onClick,e=>{e.currentTarget.focus()}),onPointerDown:(0,i.M)(l.onPointerDown,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&(m(),a.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)},e.preventDefault())}),onKeyDown:(0,i.M)(l.onKeyDown,e=>{let t=""!==p.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||f(e.key),(!t||" "!==e.key)&&I.includes(e.key)&&(m(),e.preventDefault())})})})});z.displayName=U;var Z="SelectValue",Y=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:l,children:o,placeholder:i="",...a}=e,u=B(Z,r),{onValueNodeHasChildrenChange:d}=u,c=void 0!==o,p=(0,s.e)(t,u.onValueNodeChange);return(0,R.b)(()=>{d(c)},[d,c]),(0,w.jsx)(g.WV.span,{...a,ref:p,style:{pointerEvents:"none"},children:eT(u.value)?(0,w.jsx)(w.Fragment,{children:i}):o})});Y.displayName=Z;var q=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...l}=e;return(0,w.jsx)(g.WV.span,{"aria-hidden":!0,...l,ref:t,children:n||"▼"})});q.displayName="SelectIcon";var X=e=>(0,w.jsx)(m.h,{asChild:!0,...e});X.displayName="SelectPortal";var G="SelectContent",J=n.forwardRef((e,t)=>{let r=B(G,e.__scopeSelect),[o,i]=n.useState();return((0,R.b)(()=>{i(new DocumentFragment)},[]),r.open)?(0,w.jsx)(ee,{...e,ref:t}):o?l.createPortal((0,w.jsx)($,{scope:e.__scopeSelect,children:(0,w.jsx)(D.Slot,{scope:e.__scopeSelect,children:(0,w.jsx)("div",{children:e.children})})}),o):null});J.displayName=G;var[$,Q]=_(G),ee=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:l="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:a,onPointerDownOutside:u,side:d,sideOffset:v,align:h,alignOffset:m,arrowPadding:g,collisionBoundary:y,collisionPadding:b,sticky:S,hideWhenDetached:C,avoidCollisions:j,...R}=e,E=B(G,r),[M,I]=n.useState(null),[N,P]=n.useState(null),D=(0,s.e)(t,e=>I(e)),[W,_]=n.useState(null),[L,A]=n.useState(null),H=V(r),[O,F]=n.useState(!1),K=n.useRef(!1);n.useEffect(()=>{if(M)return(0,T.Ry)(M)},[M]),(0,p.EW)();let U=n.useCallback(e=>{let[t,...r]=H().map(e=>e.ref.current),[n]=r.slice(-1),l=document.activeElement;for(let r of e)if(r===l||(null==r||r.scrollIntoView({block:"nearest"}),r===t&&N&&(N.scrollTop=0),r===n&&N&&(N.scrollTop=N.scrollHeight),null==r||r.focus(),document.activeElement!==l))return},[H,N]),z=n.useCallback(()=>U([W,M]),[U,W,M]);n.useEffect(()=>{O&&z()},[O,z]);let{onOpenChange:Z,triggerPointerDownPosRef:Y}=E;n.useEffect(()=>{if(M){let e={x:0,y:0},t=t=>{var r,n,l,o;e={x:Math.abs(Math.round(t.pageX)-(null!==(l=null===(r=Y.current)||void 0===r?void 0:r.x)&&void 0!==l?l:0)),y:Math.abs(Math.round(t.pageY)-(null!==(o=null===(n=Y.current)||void 0===n?void 0:n.y)&&void 0!==o?o:0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():M.contains(r.target)||Z(!1),document.removeEventListener("pointermove",t),Y.current=null};return null!==Y.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[M,Z,Y]),n.useEffect(()=>{let e=()=>Z(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[Z]);let[q,X]=eI(e=>{let t=H().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eN(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),J=n.useCallback((e,t,r)=>{let n=!K.current&&!r;(void 0!==E.value&&E.value===t||n)&&(_(e),n&&(K.current=!0))},[E.value]),Q=n.useCallback(()=>null==M?void 0:M.focus(),[M]),ee=n.useCallback((e,t,r)=>{let n=!K.current&&!r;(void 0!==E.value&&E.value===t||n)&&A(e)},[E.value]),en="popper"===l?er:et,el=en===er?{side:d,sideOffset:v,align:h,alignOffset:m,arrowPadding:g,collisionBoundary:y,collisionPadding:b,sticky:S,hideWhenDetached:C,avoidCollisions:j}:{};return(0,w.jsx)($,{scope:r,content:M,viewport:N,onViewportChange:P,itemRefCallback:J,selectedItem:W,onItemLeave:Q,itemTextRefCallback:ee,focusSelectedItem:z,selectedItemText:L,position:l,isPositioned:O,searchRef:q,children:(0,w.jsx)(k.Z,{as:x,allowPinchZoom:!0,children:(0,w.jsx)(f.M,{asChild:!0,trapped:E.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,i.M)(o,e=>{var t;null===(t=E.trigger)||void 0===t||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,w.jsx)(c.XB,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:a,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>E.onOpenChange(!1),children:(0,w.jsx)(en,{role:"listbox",id:E.contentId,"data-state":E.open?"open":"closed",dir:E.dir,onContextMenu:e=>e.preventDefault(),...R,...el,onPlaced:()=>F(!0),ref:D,style:{display:"flex",flexDirection:"column",outline:"none",...R.style},onKeyDown:(0,i.M)(R.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||X(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=H().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>U(t)),e.preventDefault()}})})})})})})});ee.displayName="SelectContentImpl";var et=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:l,...i}=e,a=B(G,r),u=Q(G,r),[d,c]=n.useState(null),[p,f]=n.useState(null),v=(0,s.e)(t,e=>f(e)),h=V(r),m=n.useRef(!1),x=n.useRef(!0),{viewport:y,selectedItem:b,selectedItemText:S,focusSelectedItem:C}=u,j=n.useCallback(()=>{if(a.trigger&&a.valueNode&&d&&p&&y&&b&&S){let e=a.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=a.valueNode.getBoundingClientRect(),n=S.getBoundingClientRect();if("rtl"!==a.dir){let l=n.left-t.left,i=r.left-l,a=e.left-i,s=e.width+a,u=Math.max(s,t.width),c=window.innerWidth-10,p=(0,o.u)(i,[10,c-u]);d.style.minWidth=s+"px",d.style.left=p+"px"}else{let l=t.right-n.right,i=window.innerWidth-r.right-l,a=window.innerWidth-e.right-i,s=e.width+a,u=Math.max(s,t.width),c=window.innerWidth-10,p=(0,o.u)(i,[10,c-u]);d.style.minWidth=s+"px",d.style.right=p+"px"}let i=h(),s=window.innerHeight-20,u=y.scrollHeight,c=window.getComputedStyle(p),f=parseInt(c.borderTopWidth,10),v=parseInt(c.paddingTop,10),g=parseInt(c.borderBottomWidth,10),w=f+v+u+parseInt(c.paddingBottom,10)+g,x=Math.min(5*b.offsetHeight,w),C=window.getComputedStyle(y),j=parseInt(C.paddingTop,10),R=parseInt(C.paddingBottom,10),E=e.top+e.height/2-10,M=b.offsetHeight/2,T=f+v+(b.offsetTop+M);if(T<=E){let e=b===i[i.length-1].ref.current;d.style.bottom="0px";let t=p.clientHeight-y.offsetTop-y.offsetHeight;d.style.height=T+Math.max(s-E,M+(e?R:0)+t+g)+"px"}else{let e=b===i[0].ref.current;d.style.top="0px";let t=Math.max(E,f+y.offsetTop+(e?j:0)+M);d.style.height=t+(w-T)+"px",y.scrollTop=T-E+y.offsetTop}d.style.margin="".concat(10,"px 0"),d.style.minHeight=x+"px",d.style.maxHeight=s+"px",null==l||l(),requestAnimationFrame(()=>m.current=!0)}},[h,a.trigger,a.valueNode,d,p,y,b,S,a.dir,l]);(0,R.b)(()=>j(),[j]);let[E,M]=n.useState();(0,R.b)(()=>{p&&M(window.getComputedStyle(p).zIndex)},[p]);let T=n.useCallback(e=>{e&&!0===x.current&&(j(),null==C||C(),x.current=!1)},[j,C]);return(0,w.jsx)(en,{scope:r,contentWrapper:d,shouldExpandOnScrollRef:m,onScrollButtonChange:T,children:(0,w.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:E},children:(0,w.jsx)(g.WV.div,{...i,ref:v,style:{boxSizing:"border-box",maxHeight:"100%",...i.style}})})})});et.displayName="SelectItemAlignedPosition";var er=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:l=10,...o}=e,i=A(r);return(0,w.jsx)(h.VY,{...i,...o,ref:t,align:n,collisionPadding:l,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});er.displayName="SelectPopperPosition";var[en,el]=_(G,{}),eo="SelectViewport",ei=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:l,...o}=e,a=Q(eo,r),u=el(eo,r),d=(0,s.e)(t,a.onViewportChange),c=n.useRef(0);return(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,w.jsx)(D.Slot,{scope:r,children:(0,w.jsx)(g.WV.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:d,style:{position:"relative",flex:1,overflow:"auto",...o.style},onScroll:(0,i.M)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=u;if((null==n?void 0:n.current)&&r){let e=Math.abs(c.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,l=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(l<n){let o=l+e,i=Math.min(n,o),a=o-i;r.style.height=i+"px","0px"===r.style.bottom&&(t.scrollTop=a>0?a:0,r.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});ei.displayName=eo;var ea="SelectGroup",[es,eu]=_(ea),ed=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=(0,v.M)();return(0,w.jsx)(es,{scope:r,id:l,children:(0,w.jsx)(g.WV.div,{role:"group","aria-labelledby":l,...n,ref:t})})});ed.displayName=ea;var ec="SelectLabel",ep=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=eu(ec,r);return(0,w.jsx)(g.WV.div,{id:l.id,...n,ref:t})});ep.displayName=ec;var ef="SelectItem",[ev,eh]=_(ef),em=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,disabled:o=!1,textValue:a,...u}=e,d=B(ef,r),c=Q(ef,r),p=d.value===l,[f,h]=n.useState(null!=a?a:""),[m,x]=n.useState(!1),y=(0,s.e)(t,e=>{var t;return null===(t=c.itemRefCallback)||void 0===t?void 0:t.call(c,e,l,o)}),b=(0,v.M)(),S=()=>{o||(d.onValueChange(l),d.onOpenChange(!1))};if(""===l)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,w.jsx)(ev,{scope:r,value:l,disabled:o,textId:b,isSelected:p,onItemTextChange:n.useCallback(e=>{h(t=>{var r;return t||(null!==(r=null==e?void 0:e.textContent)&&void 0!==r?r:"").trim()})},[]),children:(0,w.jsx)(D.ItemSlot,{scope:r,value:l,disabled:o,textValue:f,children:(0,w.jsx)(g.WV.div,{role:"option","aria-labelledby":b,"data-highlighted":m?"":void 0,"aria-selected":p&&m,"data-state":p?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...u,ref:y,onFocus:(0,i.M)(u.onFocus,()=>x(!0)),onBlur:(0,i.M)(u.onBlur,()=>x(!1)),onPointerUp:(0,i.M)(u.onPointerUp,S),onPointerMove:(0,i.M)(u.onPointerMove,e=>{if(o){var t;null===(t=c.onItemLeave)||void 0===t||t.call(c)}else e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,i.M)(u.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null===(t=c.onItemLeave)||void 0===t||t.call(c)}}),onKeyDown:(0,i.M)(u.onKeyDown,e=>{var t;(null===(t=c.searchRef)||void 0===t?void 0:t.current)!==""&&" "===e.key||(N.includes(e.key)&&S()," "===e.key&&e.preventDefault())})})})})});em.displayName=ef;var eg="SelectItemText",ew=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:o,style:i,...a}=e,u=B(eg,r),d=Q(eg,r),c=eh(eg,r),p=F(eg,r),[f,v]=n.useState(null),h=(0,s.e)(t,e=>v(e),c.onItemTextChange,e=>{var t;return null===(t=d.itemTextRefCallback)||void 0===t?void 0:t.call(d,e,c.value,c.disabled)}),m=null==f?void 0:f.textContent,x=n.useMemo(()=>(0,w.jsx)("option",{value:c.value,disabled:c.disabled,children:m},c.value),[c.disabled,c.value,m]),{onNativeOptionAdd:y,onNativeOptionRemove:b}=p;return(0,R.b)(()=>(y(x),()=>b(x)),[y,b,x]),(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(g.WV.span,{id:c.textId,...a,ref:h}),c.isSelected&&u.valueNode&&!u.valueNodeHasChildren?l.createPortal(a.children,u.valueNode):null]})});ew.displayName=eg;var ex="SelectItemIndicator",ey=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return eh(ex,r).isSelected?(0,w.jsx)(g.WV.span,{"aria-hidden":!0,...n,ref:t}):null});ey.displayName=ex;var eb="SelectScrollUpButton",eS=n.forwardRef((e,t)=>{let r=Q(eb,e.__scopeSelect),l=el(eb,e.__scopeSelect),[o,i]=n.useState(!1),a=(0,s.e)(t,l.onScrollButtonChange);return(0,R.b)(()=>{if(r.viewport&&r.isPositioned){let e=function(){i(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,w.jsx)(eR,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});eS.displayName=eb;var eC="SelectScrollDownButton",ej=n.forwardRef((e,t)=>{let r=Q(eC,e.__scopeSelect),l=el(eC,e.__scopeSelect),[o,i]=n.useState(!1),a=(0,s.e)(t,l.onScrollButtonChange);return(0,R.b)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;i(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,w.jsx)(eR,{...e,ref:a,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});ej.displayName=eC;var eR=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:l,...o}=e,a=Q("SelectScrollButton",r),s=n.useRef(null),u=V(r),d=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>d(),[d]),(0,R.b)(()=>{var e;let t=u().find(e=>e.ref.current===document.activeElement);null==t||null===(e=t.ref.current)||void 0===e||e.scrollIntoView({block:"nearest"})},[u]),(0,w.jsx)(g.WV.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,i.M)(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(l,50))}),onPointerMove:(0,i.M)(o.onPointerMove,()=>{var e;null===(e=a.onItemLeave)||void 0===e||e.call(a),null===s.current&&(s.current=window.setInterval(l,50))}),onPointerLeave:(0,i.M)(o.onPointerLeave,()=>{d()})})}),eE=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,w.jsx)(g.WV.div,{"aria-hidden":!0,...n,ref:t})});eE.displayName="SelectSeparator";var eM="SelectArrow";function eT(e){return""===e||void 0===e}n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=A(r),o=B(eM,r),i=Q(eM,r);return o.open&&"popper"===i.position?(0,w.jsx)(h.Eh,{...l,...n,ref:t}):null}).displayName=eM;var ek=n.forwardRef((e,t)=>{let{value:r,...l}=e,o=n.useRef(null),i=(0,s.e)(t,o),a=(0,E.D)(r);return n.useEffect(()=>{let e=o.current,t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(a!==r&&t){let n=new Event("change",{bubbles:!0});t.call(e,r),e.dispatchEvent(n)}},[a,r]),(0,w.jsx)(M.T,{asChild:!0,children:(0,w.jsx)("select",{...l,ref:i,defaultValue:r})})});function eI(e){let t=(0,C.W)(e),r=n.useRef(""),l=n.useRef(0),o=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(l.current),""!==t&&(l.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),i=n.useCallback(()=>{r.current="",window.clearTimeout(l.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(l.current),[]),[r,o,i]}function eN(e,t,r){var n;let l=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,o=(n=Math.max(r?e.indexOf(r):-1,0),e.map((t,r)=>e[(n+r)%e.length]));1===l.length&&(o=o.filter(e=>e!==r));let i=o.find(e=>e.textValue.toLowerCase().startsWith(l.toLowerCase()));return i!==r?i:void 0}ek.displayName="BubbleSelect";var eP=K,eD=z,eV=Y,eW=q,e_=X,eL=J,eA=ei,eH=ed,eB=ep,eO=em,eF=ew,eK=ey,eU=eS,ez=ej,eZ=eE},6718:function(e,t,r){r.d(t,{D:function(){return l}});var n=r(2265);function l(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},95098:function(e,t,r){r.d(t,{T:function(){return i}});var n=r(2265),l=r(82912),o=r(57437),i=n.forwardRef((e,t)=>(0,o.jsx)(l.WV.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));i.displayName="VisuallyHidden"}}]);