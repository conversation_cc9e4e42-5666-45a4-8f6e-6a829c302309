"use strict";exports.id=595,exports.ids=[595],exports.modules={84059:(e,t,s)=>{s.d(t,{default:()=>p});var l=s(97247),r=s(34178),a=s(22288),o=s(84879),n=s(90532),i=s(71714),c=s(21770),d=s(99824);function p(){let e=(0,i.Z)({delay:3e3,stopOnInteraction:!1}),t=(0,o.useTranslations)("seeker"),s=(0,r.usePathname)();return(0,l.jsxs)(l.Fragment,{children:[(s.includes("/s/"),l.jsx(l.Fragment,{})),(0,l.jsxs)("div",{className:"w-full py-3 bg-[#F7ECDC]",children:[l.jsx(a.Z,{className:"md:hidden",children:l.jsx("div",{className:"w-full md:hidden",children:l.jsx(n.lr,{opts:{active:!0,loop:!0},plugins:[e],children:(0,l.jsxs)(n.KI,{className:"text-seekers-primary",children:[(0,l.jsxs)(n.d$,{className:"inline-flex gap-2 items-center",children:[l.jsx(c.Z,{className:"!w-5 !h-5"}),l.jsx("p",{className:"font-semibold text-xs",children:t("banner.seekers.discoverDreamHome.title")})]}),(0,l.jsxs)(n.d$,{className:"inline-flex gap-2 items-center",children:[l.jsx(d.Z,{className:"!w-5 !h-5"}),l.jsx("p",{className:" font-semibold text-xs",children:t("banner.seekers.connectToPropertyOwner.title")})]})]})})})}),l.jsx(a.Z,{className:"hidden md:block text-seekers-primary",children:(0,l.jsxs)("div",{className:"hidden md:flex gap-4 md:justify-between",children:[(0,l.jsxs)("div",{className:"inline-flex gap-2 items-center",children:[l.jsx("p",{className:"font-semibold text-xs",children:t("banner.seekers.discoverDreamHome.title")}),l.jsx(c.Z,{className:"!w-4 !h-4"})]}),(0,l.jsxs)("div",{className:"inline-flex gap-2 items-center",children:[l.jsx(d.Z,{className:"!w-4 !h-4"}),l.jsx("p",{className:"font-semibold text-xs",children:t("banner.seekers.connectToPropertyOwner.title")})]})]})})]})]})}},90532:(e,t,s)=>{s.d(t,{A0:()=>w,KI:()=>u,Pz:()=>g,am:()=>h,d$:()=>f,lr:()=>m});var l=s(97247),r=s(28964),a=s(33327),o=s(25008),n=s(58053),i=s(30938),c=s(67636),d=s(84879);let p=r.createContext(null);function x(){let e=r.useContext(p);if(!e)throw Error("useCarousel must be used within a <Carousel />");return e}let m=r.forwardRef(({orientation:e="horizontal",opts:t,setApi:s,plugins:n,className:i,children:c,...d},x)=>{let[m,u]=(0,a.Z)({...t,axis:"horizontal"===e?"x":"y"},n),[f,h]=r.useState(!1),[g,w]=r.useState(!1),[j,A]=r.useState(0),N=r.useCallback(e=>{e&&(h(e.canScrollPrev()),w(e.canScrollNext()),A(e.selectedScrollSnap()))},[]),b=r.useCallback(()=>{u?.scrollPrev()},[u]),y=r.useCallback(()=>{u?.scrollNext()},[u]),v=r.useCallback(e=>{"ArrowLeft"===e.key?(e.preventDefault(),b()):"ArrowRight"===e.key&&(e.preventDefault(),y())},[b,y]),k=r.useCallback(e=>{u?.scrollTo(e)},[u]);return r.useEffect(()=>{u&&s&&s(u)},[u,s]),r.useEffect(()=>{if(u)return N(u),u.on("reInit",N),u.on("select",N),()=>{u?.off("select",N)}},[u,N]),l.jsx(p.Provider,{value:{carouselRef:m,api:u,opts:t,orientation:e||(t?.axis==="y"?"vertical":"horizontal"),scrollPrev:b,scrollNext:y,canScrollPrev:f,canScrollNext:g,selectedIndex:j,scrollTo:k},children:l.jsx("div",{...d,ref:x,onKeyDownCapture:v,className:(0,o.cn)("relative",i),role:"region","aria-roledescription":"carousel",children:c})})});m.displayName="Carousel";let u=r.forwardRef(({className:e,...t},s)=>{let{carouselRef:r,orientation:a}=x();return l.jsx("div",{ref:r,className:"overflow-hidden",children:l.jsx("div",{...t,ref:s,className:(0,o.cn)("flex","horizontal"===a?"-ml-4":"-mt-4 flex-col",e)})})});u.displayName="CarouselContent";let f=r.forwardRef(({className:e,...t},s)=>{let{orientation:r}=x();return l.jsx("div",{...t,ref:s,role:"group","aria-roledescription":"slide",className:(0,o.cn)("min-w-0 shrink-0 grow-0 basis-full","horizontal"===r?"pl-4":"pt-4",e)})});f.displayName="CarouselItem";let h=r.forwardRef(({iconClassName:e,className:t,variant:s="outline",size:r="icon",...a},c)=>{let{orientation:p,scrollPrev:m,canScrollPrev:u}=x(),f=(0,d.useTranslations)("universal");return(0,l.jsxs)(n.z,{...a,ref:c,variant:s,size:r,className:(0,o.cn)("absolute  h-6 w-6 rounded-full","horizontal"===p?"-left-12 top-1/2 -translate-y-1/2":"-top-12 left-1/2 -translate-x-1/2 rotate-90",t),disabled:!u,onClick:m,children:[l.jsx(i.Z,{className:(0,o.cn)("h-4 w-4",e)}),l.jsx("span",{className:"sr-only",children:f("cta.previous")})]})});h.displayName="CarouselPrevious";let g=r.forwardRef(({iconClassName:e,className:t,variant:s="outline",size:r="icon",...a},i)=>{let{orientation:p,scrollNext:m,canScrollNext:u}=x(),f=(0,d.useTranslations)("seeker");return(0,l.jsxs)(n.z,{...a,ref:i,variant:s,size:r,className:(0,o.cn)("absolute h-6 w-6 rounded-full","horizontal"===p?"-right-12 top-1/2 -translate-y-1/2":"-bottom-12 left-1/2 -translate-x-1/2 rotate-90",t),disabled:!u,onClick:m,children:[l.jsx(c.Z,{className:(0,o.cn)("h-4 w-4",e)}),l.jsx("span",{className:"sr-only",children:f("cta.next")})]})});g.displayName="CarouselNext";let w=r.forwardRef(({className:e,carouselDotClassName:t,...s},r)=>{let{selectedIndex:a,scrollTo:i,api:c}=x();return l.jsx("div",{ref:r,className:(0,o.cn)("embla__dots absolute z-50 flex w-fit items-center justify-center gap-2",e),...s,children:c?.scrollSnapList().map((e,s)=>l.jsx(n.z,{size:"icon",className:o.cn(t,"embla__dot h-2 w-2 rounded-full ",s===a?"bg-white/90 ":"bg-black/10"),onClick:()=>i?.(s)},s))})});w.displayName="CarouselDots"},81413:(e,t,s)=>{s.d(t,{Z:()=>u});var l=s(72051),r=s(69385),a=s(79438),o=s(59624);let n={src:"/_next/static/media/property-seekers-main-logo.2a8a0666.png",height:128,width:473,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAMAAABSSm3fAAAADFBMVEW1i1SxiVK1jFW1jFUX2lE4AAAABHRSTlM+F2AiCpN2vgAAAAlwSFlzAAALEwAACxMBAJqcGAAAABhJREFUeJwFwQEBAAAIwyDm+3cWoFrOYT0AhwAQ9FQy9wAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:2};var i=s(37170),c=s(93844);let d=(0,s(45347).createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\components\ui\separator.tsx#Separator`);var p=s(78666),x=s(92898),m=s(46043);function u(){let e=(0,r.Z)("seeker"),t=[{id:"villas",url:`/s/all?t=${m.yJ.villa}`,content:e("footer.tabsOne.content.optionOne.title")},{id:"apartment",url:`/s/all?t=${m.yJ.apartment}`,content:e("footer.tabsOne.content.optionTwo.title")},{id:"guesthouse",url:`/s/all?t=${m.yJ.rooms}`,content:e("footer.tabsOne.content.optionThree.title")},{id:"homestay",url:`/s/all?t=${m.yJ.rooms}`,content:e("footer.tabsOne.content.optionFour.title")},{id:"shops",url:`/s/all?t=${m.yJ.shops}`,content:e("footer.tabsOne.content.optionFive.title")},{id:"offices",url:`/s/all?t=${m.yJ.offices}`,content:e("footer.tabsOne.content.optionSix.title")},{id:"restaurant",url:`/s/all?t=${m.yJ.cafeOrRestaurants}`,content:e("footer.tabsOne.content.optionSeven.title")}],s=[{id:"get-your-property-listed",url:process.env.ADMIN_DOMAIN||"",content:e("footer.tabsTwo.content.optionOne.title")},{id:"faq-for-owner",url:(process.env.ADMIN_DOMAIN||"")+"/#faq",content:e("footer.tabsTwo.content.optionTwo.title")}],i=[{id:"faq",url:`${x.mU}#faq`,content:e("footer.tabsFour.content.optionOne.title")},{id:"contact-support",url:x.bY,content:e("footer.tabsFour.content.optionTwo.title")},{id:"about-us",url:"/about-us",content:e("footer.tabsFour.content.optionThree.title")},{id:"terms-of-use",url:x.Fk,content:e("footer.tabsFour.content.optionFour.title"),target:"_blank"},{id:"privacy-policy",url:x.Ph,content:e("footer.tabsFour.content.optionFive.title"),target:"_blank"}];return l.jsx(l.Fragment,{children:l.jsx("footer",{className:" bg-seekers-foreground/50 space-y-8 w-full py-6",children:(0,l.jsxs)(a.Z,{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"w-full grid grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 gap-2 md:gap-x-6 gap-y-8  py-6",children:[(0,l.jsxs)("div",{className:"space-y-1  col-span-2 lg:col-span-4 xl:col-span-2 ",children:[l.jsx("div",{className:"flex gap-2 items-center",children:l.jsx(o.default,{src:n,alt:"Properti-Plaza",width:164,height:48})}),l.jsx("p",{className:"text-body-variant text-xs font-normal text-seekers-text-light leading-6 tracking-[0.06px]",children:e("footer.slogan")})]}),l.jsx("div",{className:"max-sm:hidden"}),l.jsx(f,{title:e("footer.exploreProperties.title"),children:t.map(e=>l.jsx(c.rU,{href:e.url,children:e.content},e.id))}),l.jsx(f,{title:e("footer.properyOwner.title"),children:s.map(e=>l.jsx(c.rU,{href:e.url,children:e.content},e.id))}),l.jsx(f,{title:e("footer.help.title"),children:i.map(e=>l.jsx(c.rU,{href:e.url,target:e.target||"",children:e.content},e.id))})]}),l.jsx(d,{}),(0,l.jsxs)("div",{className:"grid grid-cols-2 lg:grid-cols-4 xl:grid-cols-6 items-center gap-2 md:gap-x-6",children:[(0,l.jsxs)("p",{className:"text-xs font-semibold text-seekers-text-light lg:col-span-3 xl:col-span-5",children:["\xa9 ",e("footer.copyright")]}),(0,l.jsxs)("div",{className:"flex gap-4",children:[l.jsx(c.rU,{href:"https://www.facebook.com/join.propertyplaza",target:"_blank",className:"grid grid-flow-col auto-cols-max",children:l.jsx(p.Am9,{className:"w-5 h-5"})}),l.jsx(c.rU,{href:"https://www.instagram.com/join.propertyplaza/",target:"_blank",className:"grid grid-flow-col auto-cols-max",children:l.jsx(p.Zf_,{className:"w-5 h-5"})}),l.jsx(c.rU,{href:"https://www.tiktok.com/@join.propertyplaza",target:"_blank",className:"grid grid-flow-col auto-cols-max",children:l.jsx(p.nTm,{className:"w-5 h-5"})}),l.jsx(c.rU,{href:"https://www.linkedin.com/company/property-plaza-indonesia",target:"_blank",className:"grid grid-flow-col auto-cols-max",children:l.jsx(p.ltd,{className:"w-5 h-5"})})]})]})]})})})}function f({children:e,title:t,...s}){return(0,l.jsxs)("div",{className:(0,i.cn)("space-y-4 w-48",s),children:[l.jsx("h2",{className:"font-semibold  text-seekers-text",children:t}),l.jsx("div",{className:"flex flex-col gap-2 text-seekers-text-light text-xs",children:e})]})}},98798:(e,t,s)=>{s.d(t,{Z:()=>l});let l=(0,s(45347).createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\components\navbar\seekers-banner.tsx#default`)},46043:(e,t,s)=>{s.d(t,{yJ:()=>l});let l={villas:"VILLA",apartment:"APARTMENT",rooms:"ROOM",commercialSpace:"COMMERCIAL_SPACE",cafeOrRestaurants:"CAFE_RESTAURANT",offices:"OFFICE",shops:"SHOP",shellAndCore:"SHELL_CORE",lands:"LAND",guestHouse:"GUESTHOUSE",homestay:"HOMESTAY",ruko:"RUKO",villa:"VILLA"}}};