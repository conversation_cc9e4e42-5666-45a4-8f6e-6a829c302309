(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5685],{52144:function(e,t,r){Promise.resolve().then(r.bind(r,15168)),Promise.resolve().then(r.bind(r,97867)),Promise.resolve().then(r.bind(r,31085))},84308:function(e,t,r){"use strict";r.d(t,{E:function(){return a},i:function(){return o}});var n=r(6404),s=r(42586),i=r(31229);let o=/^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[\W_])[A-Za-z\d\W_]{8,}$/;function a(){let e=(0,s.useTranslations)("seeker");return i.z.object({firstName:i.z.string().min(n.nM,{message:e("form.utility.minimumLength",{field:e("form.field.firstName"),length:n.nM})}).max(n.ac,{message:e("form.utility.maximumLength",{field:e("form.field.firstName"),length:n.ac})}),lastName:i.z.string().min(n.nM,{message:e("form.utility.minimumLength",{field:e("form.field.lastName"),length:n.nM})}).max(n.ac,{message:e("form.utility.maximumLength",{field:e("form.field.lastName"),length:n.ac})}),contact:i.z.string().email({message:e("form.utility.enterValidField",{field:" ".concat(e("form.field.email"))})}),password:i.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.password")})}),confirmPassword:i.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.confirmPassword")})})}).refine(e=>e.password==e.confirmPassword,{message:e("form.utility.fieldNotMatch",{field:"".concat(e("form.field.password"))}),path:["confirmPassword"]})}},15168:function(e,t,r){"use strict";r.d(t,{default:function(){return y}});var n=r(57437),s=r(35153),i=r(42586),o=r(6404),a=r(31229),l=r(84308),c=r(29501),u=r(13590),d=r(15681),f=r(19249),m=r(62869),p=r(75189),h=r(70633),g=r(21770),x=r(2265),w=r(94508),v=r(30401),b=r(32489);function y(e){let{email:t,token:r}=e,y=(0,i.useTranslations)("universal"),{toast:N}=(0,s.pm)(),j=(0,p.useRouter)(),A=(0,i.useLocale)(),S=function(){let e=(0,i.useTranslations)("universal");return a.z.object({password:a.z.string().min(1,{message:e("form.utility.fieldRequired",{field:e("form.field.password")})}).min(o.Z9,{message:e("form.utility.minimumLength",{length:o.Z9,field:e("form.field.password")})}).refine(e=>l.i.test(e),{message:e("form.utility.passwordWeak")}),confirmPassword:a.z.string().min(1,{message:e("form.utility.fieldRequired",{field:e("form.field.confirmPassword")})})}).refine(e=>e.password==e.confirmPassword,{message:e("form.utility.fieldNotMatch",{field:"".concat(e("form.field.password")," ").concat(e("conjuntion.and")," ").concat(e("form.field.confirmPassword"))}),path:["confirmPassword"]})}(),P=(0,g.D)({mutationFn:e=>(0,h.rb)(e,{headers:{Authorization:e.token}})}),[k,C]=(0,x.useState)({length:!1,number:!1,special:!1,notCommon:!0,uppercase:!1}),z=(0,c.cI)({resolver:(0,u.F)(S),defaultValues:{password:"",confirmPassword:""}}),Z=z.watch("password");async function T(e){let n={email:t,token:r,password:e.password,confirm_password:e.confirmPassword,locale:A};try{await P.mutateAsync(n),N({title:y("universal.success.createPassword.title"),description:y("success.createPassword.description")}),j.push("/")}catch(e){N({title:y("error.resetPassword.title"),description:e.response.data.message,variant:"destructive"})}}return(0,x.useEffect)(()=>{Z&&C({length:Z.length>=8,number:/[0-9]/.test(Z),special:/[!@#$%^&*()_+]/.test(Z),notCommon:!["123456","password","qwerty"].includes(Z.toLowerCase()),uppercase:/[A-Z]/.test(Z),lowercase:/[a-z]/.test(Z)})},[Z]),(0,n.jsx)(d.l0,{...z,children:(0,n.jsxs)("form",{onSubmit:z.handleSubmit(T),className:"space-y-8",children:[(0,n.jsx)("div",{className:"space-y-2 text-center",children:(0,n.jsx)("h1",{className:"text-2xl font-semibold text-center",children:y("form.title.createPassword")})}),(0,n.jsxs)("div",{className:"space-y-2 md:min-w-80",children:[(0,n.jsx)(f.Z,{form:z,name:"password",label:y("form.label.password"),placeholder:y("form.placeholder.basePlaceholder",{field:"".concat(y("form.field.password"))})}),(0,n.jsx)(f.Z,{form:z,name:"confirmPassword",label:y("form.label.confirmPassword"),placeholder:y("form.placeholder.basePlaceholder",{field:"".concat(y("form.field.confirmPassword"))})})]}),Z&&(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-xs",children:[(0,n.jsxs)("div",{className:(0,w.cn)(k.length?"text-green-500":"text-red-500"),children:[k.length?(0,n.jsx)(v.Z,{className:"inline w-3 h-3 mr-1"}):(0,n.jsx)(b.Z,{className:"inline w-3 h-3 mr-1"}),y("form.utility.password.minimumLength")]}),(0,n.jsxs)("div",{className:(0,w.cn)(k.number?"text-green-500":"text-red-500"),children:[k.number?(0,n.jsx)(v.Z,{className:"inline w-3 h-3 mr-1"}):(0,n.jsx)(b.Z,{className:"inline w-3 h-3 mr-1"}),y("form.utility.password.numberRequired")]}),(0,n.jsxs)("div",{className:(0,w.cn)(k.special?"text-green-500":"text-red-500"),children:[k.special?(0,n.jsx)(v.Z,{className:"inline w-3 h-3 mr-1"}):(0,n.jsx)(b.Z,{className:"inline w-3 h-3 mr-1"}),y("form.utility.password.specialCharacter")]}),(0,n.jsxs)("div",{className:(0,w.cn)(k.notCommon?"text-green-500":"text-red-500"),children:[k.notCommon?(0,n.jsx)(v.Z,{className:"inline w-3 h-3 mr-1"}):(0,n.jsx)(b.Z,{className:"inline w-3 h-3 mr-1"}),y("form.utility.password.notCommonWord")]}),(0,n.jsxs)("div",{className:(0,w.cn)(k.uppercase?"text-green-500":"text-red-500"),children:[k.uppercase?(0,n.jsx)(v.Z,{className:"inline w-3 h-3 mr-1"}):(0,n.jsx)(b.Z,{className:"inline w-3 h-3 mr-1"}),y("form.utility.password.uppercaseRequired")]}),(0,n.jsxs)("div",{className:(0,w.cn)(k.lowercase?"text-green-500":"text-red-500"),children:[k.lowercase?(0,n.jsx)(v.Z,{className:"inline w-3 h-3 mr-1"}):(0,n.jsx)(b.Z,{className:"inline w-3 h-3 mr-1"}),y("form.utility.password.lowercaseRequired")]})]}),(0,n.jsx)(m.z,{className:"w-full",variant:"default-seekers",loading:P.isPending,children:y("cta.changePassword")})]})})}},75422:function(e,t,r){"use strict";r.d(t,{Z:function(){return o}});var n=r(57437),s=r(15681),i=r(94508);function o(e){let{children:t,description:r,label:o,containerClassName:a,labelClassName:l,variant:c="default"}=e;return(0,n.jsxs)("div",{className:"w-full",children:[(0,n.jsxs)(s.xJ,{className:(0,i.cn)("w-full relative","float"==c?"border rounded-xl focus-within:border-neutral-light px-4 py-1 !space-y-0 focus-visible:outline-none focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 disabled:cursor-not-allowed":"",a),onClick:e=>e.stopPropagation(),children:[o&&(0,n.jsx)(s.lX,{className:l,children:o}),(0,n.jsx)(s.NI,{className:"group relative w-full",children:t}),r&&(0,n.jsx)(s.pf,{children:r}),"default"==c&&(0,n.jsx)(s.zG,{})]}),"float"==c&&(0,n.jsx)(s.zG,{})]})}},19249:function(e,t,r){"use strict";r.d(t,{Z:function(){return f}});var n=r(57437),s=r(15681),i=r(95186),o=r(75422),a=r(2265),l=r(62869),c=r(87769),u=r(42208),d=r(94508);function f(e){let{form:t,label:r,name:f,placeholder:m,description:p,inputProps:h,labelClassName:g,containerClassName:x,inputContainer:w,variant:v="default"}=e,[b,y]=(0,a.useState)(!1);return(0,n.jsx)(s.Wi,{control:t.control,name:f,render:e=>{let{field:t}=e;return(0,n.jsx)(o.Z,{label:r,description:p,labelClassName:(0,d.cn)("float"==v?"absolute -top-2 left-2 px-1 text-xs bg-background z-10":"",g),containerClassName:x,variant:v,children:(0,n.jsxs)("div",{className:(0,d.cn)("flex gap-2 w-full overflow-hidden","float"==v?"":"border rounded-sm focus-within:border-neutral-light",w),children:[(0,n.jsx)(i.I,{type:b?"text":"password",placeholder:m,...t,...h,className:(0,d.cn)("border-none focus:outline-none shadow-none focus-visible:ring-0 w-full","float"==v?"px-0":"",null==h?void 0:h.className)}),(0,n.jsx)(l.z,{size:"icon",variant:"ghost",className:"bg-transparent hover:bg-transparent text-seekers-text-light",type:"button",onClick:e=>{e.preventDefault(),e.stopPropagation(),y(e=>!e)},children:b?(0,n.jsx)(c.Z,{className:"w-4 h-4"}):(0,n.jsx)(u.Z,{className:"w-4 h-4"})})]})})}})}},62869:function(e,t,r){"use strict";r.d(t,{z:function(){return u}});var n=r(57437),s=r(2265),i=r(98482),o=r(90535),a=r(94508),l=r(51817);let c=(0,o.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-seekers-primary text-white shadow hover:bg-seekers-primary/90","default-seekers":"bg-seekers-primary text-white shadow hover:bg-seekers-primary-light/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),u=s.forwardRef((e,t)=>{let{className:r,variant:s,size:o,asChild:u=!1,loading:d=!1,...f}=e,m=u?i.g7:"button";return(0,n.jsx)(m,{className:(0,a.cn)(c({variant:s,size:o,className:r})),ref:t,disabled:d||f.disabled,...f,children:d?(0,n.jsx)(l.Z,{className:(0,a.cn)("h-4 w-4 animate-spin")}):f.children})});u.displayName="Button"},15681:function(e,t,r){"use strict";r.d(t,{NI:function(){return g},Wi:function(){return d},l0:function(){return c},lX:function(){return h},pf:function(){return x},xJ:function(){return p},zG:function(){return w}});var n=r(57437),s=r(2265),i=r(98482),o=r(29501),a=r(94508),l=r(26815);let c=o.RV,u=s.createContext({}),d=e=>{let{...t}=e;return(0,n.jsx)(u.Provider,{value:{name:t.name},children:(0,n.jsx)(o.Qr,{...t})})},f=()=>{let e=s.useContext(u),t=s.useContext(m),{getFieldState:r,formState:n}=(0,o.Gc)(),i=r(e.name,n);if(!e)throw Error("useFormField should be used within <FormField>");let{id:a}=t;return{id:a,name:e.name,formItemId:"".concat(a,"-form-item"),formDescriptionId:"".concat(a,"-form-item-description"),formMessageId:"".concat(a,"-form-item-message"),...i}},m=s.createContext({}),p=s.forwardRef((e,t)=>{let{className:r,...i}=e,o=s.useId();return(0,n.jsx)(m.Provider,{value:{id:o},children:(0,n.jsx)("div",{ref:t,className:(0,a.cn)("space-y-2",r),...i})})});p.displayName="FormItem";let h=s.forwardRef((e,t)=>{let{className:r,...s}=e,{error:i,formItemId:o}=f();return(0,n.jsx)(l._,{ref:t,className:(0,a.cn)(i&&"text-destructive",r),htmlFor:o,...s})});h.displayName="FormLabel";let g=s.forwardRef((e,t)=>{let{...r}=e,{error:s,formItemId:o,formDescriptionId:a,formMessageId:l}=f();return(0,n.jsx)(i.g7,{ref:t,id:o,"aria-describedby":s?"".concat(a," ").concat(l):"".concat(a),"aria-invalid":!!s,...r})});g.displayName="FormControl";let x=s.forwardRef((e,t)=>{let{className:r,...s}=e,{formDescriptionId:i}=f();return(0,n.jsx)("p",{ref:t,id:i,className:(0,a.cn)("text-[0.8rem] text-muted-foreground",r),...s})});x.displayName="FormDescription";let w=s.forwardRef((e,t)=>{let{className:r,children:s,...i}=e,{error:o,formMessageId:l}=f(),c=o?String(null==o?void 0:o.message):s;return c?(0,n.jsx)("p",{ref:t,id:l,className:(0,a.cn)("text-[0.8rem] font-medium text-destructive",r),...i,children:c}):null});w.displayName="FormMessage"},95186:function(e,t,r){"use strict";r.d(t,{I:function(){return o}});var n=r(57437),s=r(2265),i=r(94508);let o=s.forwardRef((e,t)=>{let{className:r,type:s,...o}=e;return(0,n.jsx)("input",{type:s,className:(0,i.cn)("flex max-sm:h-8 h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-inset focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...o})});o.displayName="Input"},26815:function(e,t,r){"use strict";r.d(t,{_:function(){return c}});var n=r(57437),s=r(2265),i=r(6394),o=r(90535),a=r(94508);let l=(0,o.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)(i.f,{ref:t,className:(0,a.cn)(l(),r),...s})});c.displayName=i.f.displayName},49607:function(e,t,r){"use strict";r.d(t,{apiClient:function(){return l},v:function(){return c}});var n=r(6404),s=r(83464),i=r(64131),o=r(51983);let a=new(r.n(o)()).Agent({rejectUnauthorized:!1}),l=s.Z.create({baseURL:"https://dev.property-plaza.id/api/v1",headers:{Authorization:i.Z.get(n.LA)?"Bearer "+i.Z.get(n.LA):""},httpsAgent:a}),c=s.Z.create({baseURL:"/api/",httpsAgent:a})},31599:function(e,t,r){"use strict";r.d(t,{AS:function(){return c},Af:function(){return m},Ew:function(){return f},PQ:function(){return u},kS:function(){return i},rb:function(){return d},u8:function(){return o},vJ:function(){return l},x4:function(){return s},zl:function(){return a}});var n=r(49607);let s=(e,t)=>n.apiClient.post("auth/login",e,{headers:{"g-token":t||""}}),i=()=>n.apiClient.post("auth/logout"),o=e=>n.apiClient.post("notifications/email",e),a=e=>n.apiClient.post("auth/otp-verification",e),l=e=>n.apiClient.post("auth/forgot-password",e),c=e=>n.apiClient.get("auth/verify-reset-password?email=".concat(e.email,"&token=").concat(e.token)),u=e=>n.apiClient.post("auth/reset-password",e),d=(e,t)=>n.apiClient.post("auth/create-password",e,t),f=e=>n.apiClient.post("users/security",e),m=e=>n.apiClient.post("auth/totp-verification",e)},70633:function(e,t,r){"use strict";r.d(t,{AS:function(){return n.AS},Af:function(){return n.Af},Ew:function(){return n.Ew},PQ:function(){return n.PQ},kS:function(){return n.kS},rb:function(){return n.rb},u8:function(){return n.u8},vJ:function(){return n.vJ},x4:function(){return n.x4},zl:function(){return n.zl}});var n=r(31599)},35153:function(e,t,r){"use strict";r.d(t,{pm:function(){return f}});var n=r(2265);let s=0,i=new Map,o=e=>{if(i.has(e))return;let t=setTimeout(()=>{i.delete(e),u({type:"REMOVE_TOAST",toastId:e})},1e6);i.set(e,t)},a=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?o(r):e.toasts.forEach(e=>{o(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],c={toasts:[]};function u(e){c=a(c,e),l.forEach(e=>{e(c)})}function d(e){let{...t}=e,r=(s=(s+1)%Number.MAX_SAFE_INTEGER).toString(),n=()=>u({type:"DISMISS_TOAST",toastId:r});return u({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||n()}}}),{id:r,dismiss:n,update:e=>u({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function f(){let[e,t]=n.useState(c);return n.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:d,dismiss:e=>u({type:"DISMISS_TOAST",toastId:e})}}},6404:function(e,t,r){"use strict";r.d(t,{$_:function(){return p},Ge:function(){return f},K6:function(){return d},LA:function(){return n},QY:function(){return m},Y:function(){return h},Z9:function(){return i},ac:function(){return a},gr:function(){return s},nM:function(){return o},t8:function(){return u},vQ:function(){return c},xm:function(){return l}});let n="tkn",s="SEEKER",i=8,o=1,a=30,l=300,c=10,u="cookies-collection-status",d="necessary-cookies-collection-status",f="functional-cookies-collection-status",m="analytic-cookies-collection-status",p="marketing-cookies-collection-status",h={type:"t",minPrice:"minp",maxPrice:"maxp",landLargest:"landl",landSmallest:"lands",buildingLargest:"buildl",buildingSmallest:"builds",gardenLargest:"gardenl",gardenSmallest:"gardens",yearsOfBuild:"yob",bedroomTotal:"bedt",bathroomTotal:"batht",rentalOffer:"ro",propertyCondition:"pc",electircity:"el",parking:"pk",swimmingPool:"sp",typeLiving:"tl",furnished:"fs",view:"v",minimumContract:"minc",category:"c",subCategory:"sc",feature:"feat",propertyLocation:"pl",zoom:"z",viewMode:"viewMode"}},94508:function(e,t,r){"use strict";r.d(t,{E6:function(){return d},ET:function(){return p},Fg:function(){return m},cn:function(){return a},g6:function(){return f},pl:function(){return h},uf:function(){return u},xG:function(){return c},yT:function(){return g}});var n=r(61994),s=r(77398),i=r.n(s),o=r(53335);function a(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,o.m6)((0,n.W)(t))}r(25566);let l=e=>{switch(e){case"USD":default:return"en-us";case"IDR":return"id-ID";case"GBP":return"en-GB";case"AUD":return"en-AU";case"EUR":return"nl-NL"}},c=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return arguments.length>2&&void 0!==arguments[2]&&arguments[2],new Intl.NumberFormat(l(t),{style:"currency",currency:t,minimumFractionDigits:0,maximumFractionDigits:"IDR"==t?0:2}).format(e)},u=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"en-US";return new Intl.NumberFormat(t,{style:"decimal",minimumFractionDigits:0,maximumFractionDigits:0}).format(e)};function d(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}function f(e){let t=i()(e),r=i()();return t.isSame(r,"day")?t.format("HH:mm"):t.format("DD/MM/YY")}function m(e){return e.replaceAll(/[^a-zA-Z0-9\s]/g,"-").replaceAll(/\s/g,"--")}let p=(e,t)=>e.includes(t)?e.filter(e=>e!==t):[...e,t];function h(e,t){return e.some(e=>t.includes(e))}let g=e=>e.charAt(0).toUpperCase()+e.slice(1)}},function(e){e.O(0,[6990,6290,8094,2586,2957,4956,3448,8658,9553,2971,2117,1744],function(){return e(e.s=52144)}),_N_E=e.O()}]);