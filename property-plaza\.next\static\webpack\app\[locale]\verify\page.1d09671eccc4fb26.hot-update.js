"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/verify/page",{

/***/ "(app-pages-browser)/./components/ui/optimized-video.tsx":
/*!*******************************************!*\
  !*** ./components/ui/optimized-video.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OptimizedVideo; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction OptimizedVideo(param) {\n    let { src, poster, className = \"\", autoPlay = true, muted = true, loop = true, playsInline = true, controls = false, preload = \"metadata\", lazy = true, fallbackContent, showCustomControls = false } = param;\n    _s();\n    const [shouldLoad, setShouldLoad] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!lazy);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [canPlay, setCanPlay] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasError, setHasError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const videoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const videoElementRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!lazy) return;\n        const observer = new IntersectionObserver((param)=>{\n            let [entry] = param;\n            if (entry.isIntersecting) {\n                setShouldLoad(true);\n                observer.disconnect();\n            }\n        }, {\n            threshold: 0.1,\n            rootMargin: \"50px\" // Start loading 50px before video comes into view\n        });\n        if (videoRef.current) {\n            observer.observe(videoRef.current);\n        }\n        return ()=>observer.disconnect();\n    }, [\n        lazy\n    ]);\n    const handleLoadStart = ()=>{\n        setIsLoading(true);\n        setCanPlay(false);\n        setHasError(false);\n    };\n    const handleCanPlay = ()=>{\n        setIsLoading(false);\n        setCanPlay(true);\n    };\n    const handleError = ()=>{\n        setIsLoading(false);\n        setCanPlay(false);\n        setHasError(true);\n    };\n    const handlePlay = ()=>{\n        setIsPlaying(true);\n    };\n    const handlePause = ()=>{\n        setIsPlaying(false);\n    };\n    const handlePosterClick = ()=>{\n        if (videoElementRef.current && canPlay) {\n            videoElementRef.current.play();\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: videoRef,\n        className: \"relative \".concat(className),\n        children: [\n            isLoading && shouldLoad && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gray-100 flex items-center justify-center z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 border-2 border-seekers-primary border-t-transparent rounded-full animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                            lineNumber: 102,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: \"Loading video...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                    lineNumber: 101,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                lineNumber: 100,\n                columnNumber: 9\n            }, this),\n            hasError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gray-100 flex items-center justify-center z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-4xl\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: \"Video could not be loaded\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                            lineNumber: 113,\n                            columnNumber: 13\n                        }, this),\n                        fallbackContent\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                lineNumber: 110,\n                columnNumber: 9\n            }, this),\n            poster && (!shouldLoad || !canPlay) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: poster,\n                        alt: \"Video preview\",\n                        fill: true,\n                        className: \"object-cover rounded-lg\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                        lineNumber: 122,\n                        columnNumber: 11\n                    }, this),\n                    shouldLoad && !isLoading && !hasError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex items-center justify-center bg-black/20 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-16 h-16 bg-white/90 rounded-full flex items-center justify-center shadow-lg hover:bg-white transition-colors duration-200 cursor-pointer\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-0 h-0 border-l-[12px] border-l-black border-t-[8px] border-t-transparent border-b-[8px] border-b-transparent ml-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                            lineNumber: 132,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                lineNumber: 121,\n                columnNumber: 9\n            }, this),\n            shouldLoad && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                autoPlay: autoPlay,\n                muted: muted,\n                loop: loop,\n                playsInline: playsInline,\n                controls: controls,\n                preload: preload,\n                poster: poster,\n                className: \"w-full h-full object-cover rounded-lg \".concat(controls ? \"cursor-pointer\" : \"\"),\n                onLoadStart: handleLoadStart,\n                onCanPlay: handleCanPlay,\n                onError: handleError,\n                style: {\n                    outline: \"none\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                        src: src,\n                        type: \"video/mp4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                        lineNumber: 158,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                        src: src.replace(\".mp4\", \".webm\"),\n                        type: \"video/webm\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                        lineNumber: 159,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-full bg-gray-100\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl\",\n                                    children: \"\\uD83D\\uDCF9\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: \"Your browser doesn't support video playback\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                                    lineNumber: 165,\n                                    columnNumber: 15\n                                }, this),\n                                fallbackContent\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                            lineNumber: 163,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                        lineNumber: 162,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                lineNumber: 142,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n        lineNumber: 97,\n        columnNumber: 5\n    }, this);\n}\n_s(OptimizedVideo, \"2+NRLGhOTNElo7frBYe+APqk+Kg=\");\n_c = OptimizedVideo;\nvar _c;\n$RefreshReg$(_c, \"OptimizedVideo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/optimized-video.tsx\n"));

/***/ })

});