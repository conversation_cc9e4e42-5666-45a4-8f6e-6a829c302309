"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8100],{29051:function(e,t,n){n.d(t,{p:function(){return a}});var r=n(42586),s=n(31229);function a(){let e=(0,r.useTranslations)("seeker");return s.z.object({otp:s.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.otp")})}).min(5,{message:e("form.utility.enterValidField",{field:e("form.field.otp")})})})}},84308:function(e,t,n){n.d(t,{E:function(){return o},i:function(){return i}});var r=n(6404),s=n(42586),a=n(31229);let i=/^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[\W_])[A-Za-z\d\W_]{8,}$/;function o(){let e=(0,s.useTranslations)("seeker");return a.z.object({firstName:a.z.string().min(r.nM,{message:e("form.utility.minimumLength",{field:e("form.field.firstName"),length:r.nM})}).max(r.ac,{message:e("form.utility.maximumLength",{field:e("form.field.firstName"),length:r.ac})}),lastName:a.z.string().min(r.nM,{message:e("form.utility.minimumLength",{field:e("form.field.lastName"),length:r.nM})}).max(r.ac,{message:e("form.utility.maximumLength",{field:e("form.field.lastName"),length:r.ac})}),contact:a.z.string().email({message:e("form.utility.enterValidField",{field:" ".concat(e("form.field.email"))})}),password:a.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.password")})}),confirmPassword:a.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.confirmPassword")})})}).refine(e=>e.password==e.confirmPassword,{message:e("form.utility.fieldNotMatch",{field:"".concat(e("form.field.password"))}),path:["confirmPassword"]})}},97496:function(e,t,n){n.d(t,{default:function(){return Q}});var r=n(57437),s=n(79318),a=n(42586),i=n(2265),o=n(61296),l=n(93166),c=n(13590),u=n(15681),d=n(29501),f=n(61729),m=n(62869),p=n(6404),g=n(31229),x=n(21770),h=n(70633),v=n(35153),w=n(64131),y=n(71517),N=n(25367),b=n(67620),j=n(94508),k=n(19249),S=n(49089),E=n(71118);function A(){let e=(0,x.D)({mutationFn:async()=>{window.location.href="https://dev.property-plaza.id/api/v1/auth/google?origin=DEFAULT"}}),t=(0,x.D)({mutationFn:async()=>{window.location.href="https://dev.property-plaza.id/api/v1/auth/facebook?origin=DEFAULT"}}),n=async()=>{await e.mutateAsync()},s=async()=>{await t.mutateAsync()};return(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,r.jsxs)(m.z,{className:"inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border bg-background shadow-sm hover:text-accent-foreground h-9 px-4 py-2 w-full rounded-xl border-gray-200 hover:bg-gray-50",variant:"outline",onClick:n,type:"button",children:[(0,r.jsx)(E.JM8,{className:"mr-2 h-4 w-4"}),"Google"]}),(0,r.jsxs)(m.z,{className:"inline-flex w-full items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border bg-background shadow-sm hover:text-accent-foreground h-9 px-4 py-2 rounded-xl border-gray-200 hover:bg-gray-50",variant:"outline",onClick:s,type:"button",children:[(0,r.jsx)(S.Am9,{className:"mr-2 h-4 w-4"}),"Facebook"]})]})}var C=n(6512);function _(e){let{isDialog:t,onClickSignUp:n,onClickResetPassword:s}=e,i=(0,a.useTranslations)("seeker"),o=function(){let e=(0,a.useTranslations)("seeker");return g.z.object({contact:g.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.email")})}).email({message:e("form.utility.enterValidField",{field:" ".concat(e("form.field.email"))})}),password:g.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.password")})}).min(p.Z9,{message:e("form.utility.minimumLength",{field:e("form.field.password"),length:p.Z9})})})}(),l=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"seekers",t=(0,a.useTranslations)("universal"),{toast:n}=(0,v.pm)(),{executeRecaptcha:r}=(0,b.CL)();return(0,x.D)({mutationFn:async e=>{try{let t=await r("form_submit");return(0,h.x4)(e,t)}catch(e){return console.log(e),{data:null}}},onSuccess:async n=>{let r=n.data,s=await (0,N.O4)({headers:{Authorization:"Bearer ".concat(r.data.access_token)}}),a=s.type;if(!s)throw Error(t("misc.userNotFound"));if("owner"===e||"middleman"==e){if("SEEKER"==a)throw Error(t("misc.userNotFound"));if(w.Z.set(p.LA,r.data.access_token,{expires:7}),"OWNER"==a)return window.location.assign(y.Kc);if("MIDDLEMAN"==a)return window.location.assign(y.ej)}else{if("OWNER"==s.type||"MIDDLEMAN"==s.type)throw Error(t("misc.userNotFound"));w.Z.set(p.LA,r.data.access_token,{expires:7}),window.location.reload()}},onError:e=>{var r;let s=null===(r=e.response)||void 0===r?void 0:r.data;w.Z.remove(p.LA),n({title:t("misc.foundError"),description:(null==s?void 0:s.message)||"",variant:"destructive"})}})}("seekers"),{toast:S}=(0,v.pm)(),E=(0,d.cI)({resolver:(0,c.F)(o),defaultValues:{contact:"",password:""}});async function _(e){let t=(0,j.E6)(e.contact.replaceAll(/\s+/g,"")),n={username:e.contact.trim(),password:e.password,login_with:t?"DEFAULT":"PHONE_NUMBER"};try{await l.mutateAsync(n)}catch(e){var r;S({title:i("error.failedLogin.title"),description:(null==e?void 0:null===(r=e.response)||void 0===r?void 0:r.data.message)||"",variant:"destructive"})}}return(0,r.jsx)("div",{className:"grid gap-4",children:(0,r.jsx)(u.l0,{...E,children:(0,r.jsxs)("form",{onSubmit:E.handleSubmit(_),className:"grid gap-4",children:[(0,r.jsxs)("div",{className:"grid gap-4",children:[(0,r.jsx)(f.Z,{form:E,label:i("form.label.email"),name:"contact",placeholder:"",type:"email",variant:"float",labelClassName:"text-xs text-seekers-text-light font-normal"}),(0,r.jsx)(k.Z,{form:E,label:i("form.label.password"),name:"password",placeholder:"",variant:"float",labelClassName:"text-xs text-seekers-text-light font-normal"}),(0,r.jsxs)("div",{className:"text-xs text-neutral space-x-1 -mt-5",children:[(0,r.jsx)("span",{className:"ml-3",children:i("form.utility.forgotField",{field:i("form.field.password")})}),(0,r.jsx)(m.z,{variant:"link",type:"button",onClick:s,className:"p-0 text-seekers-primary font-medium hover:underline text-xs",children:i("form.utility.resetField",{field:i("form.field.password")})})]})]}),(0,r.jsx)(m.z,{className:"w-full",variant:"default-seekers",loading:l.isPending,children:i("cta.login")}),(0,r.jsx)("div",{className:"mt-4 text-center",children:(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:[i("auth.createAccount")," ",(0,r.jsx)(m.z,{variant:"link",onClick:n,className:"p-0 h-9 text-seekers-primary hover:underline",children:i("cta.createAccount")})]})}),(0,r.jsxs)("div",{className:"relative my-6",children:[(0,r.jsx)(C.Separator,{}),(0,r.jsxs)("span",{className:"absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 bg-background px-2 text-sm text-muted-foreground",children:[i("conjuntion.or")," ",i("misc.continueWith")]})]}),(0,r.jsx)(A,{})]})})})}var z=n(84308),Z=n(60668),F=n(63168),R=n(77398),T=n.n(R),I=n(30401),P=n(32489);let L=e=>{let{onClickLogin:t,onSuccess:n}=e,s=(0,a.useTranslations)("seeker"),o=(0,z.E)(),{setRegister:l,setValidFormUntil:g,register:x}=(0,Z.I)(),[h,w]=(0,i.useState)({length:!1,number:!1,special:!1,notCommon:!0,uppercase:!1}),{toast:y}=(0,v.pm)(),N=(0,F.X)(e=>{var t,r;(null==e?void 0:null===(r=e.response)||void 0===r?void 0:null===(t=r.data)||void 0===t?void 0:t.message)!=="Email verification code is already sent. Please check your email"&&y({title:s("success.sendVerification.title")+" "+x.email}),n()}),b=(0,d.cI)({resolver:(0,c.F)(o),defaultValues:{confirmPassword:x.confirm_password||"",contact:x.email||"",firstName:x.first_name||"",lastName:x.last_name||"",password:x.password||""}}),S=b.watch("password");async function E(e){let t=T()().add(30,"minutes"),n={email:e.contact||"",password:e.password,confirm_password:e.confirmPassword,first_name:e.firstName,last_name:e.lastName,type:p.gr,otp:"00000"};l(n),g(t);try{await N.mutateAsync({email:n.email,category:"REGISTRATION"})}catch(e){var r,a,i,o;if((null==e?void 0:null===(a=e.response)||void 0===a?void 0:null===(r=a.data)||void 0===r?void 0:r.message)=="Email verification code is already sent. Please check your email")return;y({title:s("message.otpRequest.failedToast.title"),description:(null==e?void 0:null===(o=e.response)||void 0===o?void 0:null===(i=o.data)||void 0===i?void 0:i.message)||"",variant:"destructive"})}}return(0,i.useEffect)(()=>{S&&w({length:S.length>=8,number:/[0-9]/.test(S),special:/[!@#$%^&*()_+]/.test(S),notCommon:!["123456","password","qwerty"].includes(S.toLowerCase()),uppercase:/[A-Z]/.test(S),lowercase:/[a-z]/.test(S)})},[S]),(0,r.jsx)(u.l0,{...b,children:(0,r.jsxs)("form",{onSubmit:b.handleSubmit(E),className:"grid gap-4",children:[(0,r.jsxs)("div",{className:"grid gap-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsx)(f.Z,{form:b,label:s("form.label.firstName"),name:"firstName",placeholder:"",type:"text",variant:"float",labelClassName:"text-xs text-seekers-text-light font-normal"}),(0,r.jsx)(f.Z,{form:b,label:s("form.label.lastName"),name:"lastName",placeholder:"",type:"text",variant:"float",labelClassName:"text-xs text-seekers-text-light font-normal"})]}),(0,r.jsx)(f.Z,{form:b,label:s("form.label.email"),name:"contact",placeholder:"",type:"email",variant:"float",labelClassName:"text-xs text-seekers-text-light font-normal"}),(0,r.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,r.jsx)(k.Z,{form:b,name:"password",variant:"float",label:s("form.label.password"),placeholder:"",inputProps:{required:!0},labelClassName:"text-xs text-seekers-text-light font-normal"}),(0,r.jsx)(k.Z,{form:b,name:"confirmPassword",variant:"float",label:s("form.label.confirmPassword"),placeholder:"",inputProps:{required:!0},labelClassName:"text-xs text-seekers-text-light font-normal"})]}),S&&(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-xs",children:[(0,r.jsxs)("div",{className:(0,j.cn)(h.length?"text-green-500":"text-red-500"),children:[h.length?(0,r.jsx)(I.Z,{className:"inline w-3 h-3 mr-1"}):(0,r.jsx)(P.Z,{className:"inline w-3 h-3 mr-1"}),s("form.utility.password.minimumLength")]}),(0,r.jsxs)("div",{className:(0,j.cn)(h.number?"text-green-500":"text-red-500"),children:[h.number?(0,r.jsx)(I.Z,{className:"inline w-3 h-3 mr-1"}):(0,r.jsx)(P.Z,{className:"inline w-3 h-3 mr-1"}),s("form.utility.password.numberRequired")]}),(0,r.jsxs)("div",{className:(0,j.cn)(h.special?"text-green-500":"text-red-500"),children:[h.special?(0,r.jsx)(I.Z,{className:"inline w-3 h-3 mr-1"}):(0,r.jsx)(P.Z,{className:"inline w-3 h-3 mr-1"}),s("form.utility.password.specialCharacter")]}),(0,r.jsxs)("div",{className:(0,j.cn)(h.notCommon?"text-green-500":"text-red-500"),children:[h.notCommon?(0,r.jsx)(I.Z,{className:"inline w-3 h-3 mr-1"}):(0,r.jsx)(P.Z,{className:"inline w-3 h-3 mr-1"}),s("form.utility.password.notCommonWord")]}),(0,r.jsxs)("div",{className:(0,j.cn)(h.uppercase?"text-green-500":"text-red-500"),children:[h.uppercase?(0,r.jsx)(I.Z,{className:"inline w-3 h-3 mr-1"}):(0,r.jsx)(P.Z,{className:"inline w-3 h-3 mr-1"}),s("form.utility.password.uppercaseRequired")]}),(0,r.jsxs)("div",{className:(0,j.cn)(h.lowercase?"text-green-500":"text-red-500"),children:[h.lowercase?(0,r.jsx)(I.Z,{className:"inline w-3 h-3 mr-1"}):(0,r.jsx)(P.Z,{className:"inline w-3 h-3 mr-1"}),s("form.utility.password.lowercaseRequired")]})]})]}),(0,r.jsx)(m.z,{className:"w-full",variant:"default-seekers",loading:N.isPending,children:s("cta.createAccount")}),(0,r.jsx)("div",{className:"mt-4 text-center",children:(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:[s("auth.alreadyHaveAccount")," ",(0,r.jsx)(m.z,{variant:"link",onClick:t,className:"p-0 h-9 text-seekers-primary hover:underline",children:s("cta.login")})]})}),(0,r.jsxs)("div",{className:"relative my-6",children:[(0,r.jsx)(C.Separator,{}),(0,r.jsx)("span",{className:"absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 bg-background px-2 text-sm text-muted-foreground",children:s("conjuntion.or")})]}),(0,r.jsx)(A,{})]})})};var D=n(92451),O=n(35934),U=n(31389),q=n(87128),M=n(75422),B=n(26371),V=n(29051);function G(){let{register:e,reset:t,setSuccessSignUp:n}=(0,Z.I)(),{toast:s}=(0,v.pm)(),o=(0,a.useTranslations)("seeker"),l=(0,V.p)(),f=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"owner",{toast:n}=(0,v.pm)(),r=(0,a.useTranslations)("universal"),{executeRecaptcha:s}=(0,b.CL)();return(0,x.D)({mutationFn:async e=>{try{let t=await s("form_submit");return(0,U.a$)(e,t)}catch(e){return console.log(e),{data:null}}},onSuccess:n=>{let r=n.data;w.Z.set(p.LA,r.data.access_token,{expires:7}),null==e||e(),"owner"==t?window.location.assign(y.Kc):window.location.reload()},onError:e=>{let t=e.response.data;n({title:r("misc.foundError"),description:t.message,variant:"destructive"})}})}(()=>t(),"seekers"),g=(0,q.G)(async()=>{try{await f.mutateAsync({...e,otp:N.getValues("otp"),register_with:"EMAIL"}),n(!0)}catch(e){}}),h=(0,F.X)(t=>{if("Email verification code is already sent. Please check your email"===t.response.data.message){s({title:o("message.otpRequest.failedToast.title"),description:t.response.data.message||"",variant:"destructive"});return}s({title:o("success.sendVerification.title")+" "+e.email})}),N=(0,d.cI)({resolver:(0,c.F)(l),defaultValues:{otp:""}});async function j(t){let n={otp:t.otp,requested_by:e.email||"",type:"EMAIL"};try{await g.mutateAsync(n)}catch(e){s({title:o("error.signUp.title"),description:e.response.data.message,variant:"destructive"})}}async function k(){h.mutate({email:e.email,category:"REGISTRATION"})}return(0,i.useEffect)(()=>{let e=N.getValues("otp").length,t=document.getElementById("otp-button");e>=5&&(null==t||t.click())},[N.getValues("otp")]),(0,r.jsx)(u.l0,{...N,children:(0,r.jsxs)("form",{onSubmit:N.handleSubmit(j),className:"space-y-8",children:[(0,r.jsx)(u.Wi,{control:N.control,name:"otp",render:e=>{let{field:t}=e;return(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsx)(M.Z,{label:"",children:(0,r.jsx)(B.Zn,{maxLength:5,...t,pattern:O.Ww,required:!0,containerClassName:"flex justify-center max-sm:justify-between",children:(0,r.jsx)(B.hf,{children:Array.from({length:5},(e,t)=>(0,r.jsx)(B.cY,{index:t,className:"w-16 h-20 text-2xl"},t))})})})})}}),(0,r.jsxs)("div",{className:"space-y-2 flex flex-col items-center",children:[(0,r.jsxs)(m.z,{id:"otp-button",className:"w-full",variant:"default-seekers",loading:g.isPending,children:[o("cta.verify")," ",o("user.account")]}),(0,r.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),k()},className:"mx-auto text-xs text-seekers-text-light",children:o("otp.resendVerificationCode")})]})]})})}var J=n(69428),K=n(90801);function W(e){let{onBack:t}=e,n=(0,a.useTranslations)("universal"),{toast:s}=(0,v.pm)(),i=(0,K.t)(),o=(0,J.N)(),l=(0,d.cI)({resolver:(0,c.F)(i),defaultValues:{email:""}});async function p(e){let r={email:e.email};try{await o.mutateAsync(r),s({title:n("success.requestForgotPassword.title"),description:n("success.requestForgotPassword.description")}),t()}catch(e){s({title:n("error.requestForgetPassword.title"),description:e.response.data.message,variant:"destructive"})}}return(0,r.jsx)(u.l0,{...l,children:(0,r.jsxs)("form",{onSubmit:l.handleSubmit(p),className:"grid gap-4 ",children:[(0,r.jsx)(f.Z,{form:l,label:n("form.label.email"),name:"email",placeholder:"",type:"email",variant:"float",labelClassName:"text-xs text-seekers-text-light"}),(0,r.jsx)(m.z,{className:"w-full",variant:"default-seekers",loading:o.isPending,children:n("cta.requestChangePassword")}),(0,r.jsx)("div",{className:"mt-4 text-center",children:(0,r.jsx)(m.z,{variant:"link",onClick:t,className:"p-0 h-9 text-seekers-primary hover:underline",children:n("cta.goBack")})})]})})}let $={signUp:"SIGN_UP",login:"LOGIN",otp:"OTP",resetPassword:"RESET_PASSWORD"};function Q(e){let{triggerClassName:t,customTrigger:n}=e,c=(0,a.useTranslations)("seeker"),[u,d]=(0,i.useState)(!1),[f,p]=(0,i.useState)(""),[g,x]=(0,i.useState)($.signUp);return(0,i.useEffect)(()=>{switch(g){case $.signUp:p(c("form.title.signUp"));return;case $.login:p(c("form.title.login"));return;case $.otp:p(c("form.title.enterOtpCode"));return;case $.resetPassword:p(c("form.title.resetPassword"));return;default:return}},[g,c]),(0,r.jsxs)(s.Z,{open:u,setOpen:d,openTrigger:n||(0,r.jsx)("button",{className:"border relative border-seekers-text-lighter shadow-md rounded-full h-10 w-14 !bg-seekers-text-light ".concat(t),children:(0,r.jsx)(o.Z,{url:""})}),dialogClassName:"w-full sm:max-w-[500px] p-6",children:[(0,r.jsxs)(l.Z,{className:"flex flex-col space-y-1.5 text-center mb-6",children:[(g==$.otp||g==$.resetPassword)&&(0,r.jsx)(m.z,{variant:"ghost",size:"icon",className:"absolute top-4 left-4",onClick:()=>x(g==$.otp?$.signUp:$.login),children:(0,r.jsx)(D.Z,{className:"h-4 w-4"})}),(0,r.jsxs)("section",{className:"space-y-1.5",children:[(0,r.jsx)("h2",{className:"tracking-tight text-center text-2xl font-bold max-w-xs mx-auto",children:f}),(0,r.jsx)("p",{className:(0,j.cn)(g===$.otp?"hidden":"","text-sm text-muted-foreground text-center"),children:g===$.login?c("auth.login.subtitle"):g===$.signUp?c("auth.register.subtitle"):g===$.resetPassword?c("auth.resetPassword.subtitle"):""})]})]}),g==$.login?(0,r.jsx)(_,{onClickSignUp:()=>x($.signUp),onClickResetPassword:()=>x($.resetPassword)}):g==$.signUp?(0,r.jsx)(L,{onSuccess:()=>x($.otp),onClickLogin:()=>x($.login)}):g==$.otp?(0,r.jsxs)("section",{children:[g===$.otp&&(0,r.jsxs)("div",{className:"text-seekers-text-light",children:[(0,r.jsx)("p",{children:c("auth.otp.content.title")}),(0,r.jsxs)("ul",{className:"list-disc list-inside",children:[(0,r.jsx)("li",{children:c("auth.otp.item.one")}),(0,r.jsx)("li",{children:c("auth.otp.item.two")}),(0,r.jsx)("li",{children:c("auth.otp.item.three")})]}),(0,r.jsx)("p",{children:c("auth.otp.content.cantFindEmail")})]}),(0,r.jsx)(G,{})]}):g==$.resetPassword?(0,r.jsx)(W,{onBack:()=>x($.login)}):null]})}},90801:function(e,t,n){n.d(t,{t:function(){return a}});var r=n(42586),s=n(31229);function a(){let e=(0,r.useTranslations)("universal");return s.z.object({email:s.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.email")})}).email()})}},93166:function(e,t,n){n.d(t,{Z:function(){return o}});var r=n(57437),s=n(57860),a=n(26110),i=n(17814);function o(e){let{children:t,className:n}=e;return(0,s.a)("(min-width:1024px)")?(0,r.jsx)(a.fK,{className:n,children:t}):(0,r.jsx)(i.OX,{className:n,children:t})}},79318:function(e,t,n){n.d(t,{Z:function(){return l}});var r=n(57437),s=n(26110),a=n(57860),i=n(94508);n(2265);var o=n(17814);function l(e){let{children:t,openTrigger:n,open:l,setOpen:c,dialogClassName:u,drawerClassName:d,dialogOverlayClassName:f}=e;return(0,a.a)("(min-width:1024px)")?(0,r.jsxs)(s.Vq,{open:l,onOpenChange:c,children:[(0,r.jsx)(s.hg,{asChild:!0,children:n}),(0,r.jsxs)(s.PK,{children:[(0,r.jsx)(s.t9,{className:f}),(0,r.jsx)(s.cZ,{className:(0,i.cn)("max-sm:w-screen md:w-[450px]  md:min-w-[450px] max-sm:h-screen flex flex-col justify-start",u),children:t})]})]}):(0,r.jsxs)(o.dy,{open:l,onOpenChange:c,children:[(0,r.jsx)(o.Qz,{asChild:!0,children:n}),(0,r.jsx)(o.sc,{children:(0,r.jsx)("div",{className:(0,i.cn)("p-4 overflow-auto",d),children:t})})]})}},75422:function(e,t,n){n.d(t,{Z:function(){return i}});var r=n(57437),s=n(15681),a=n(94508);function i(e){let{children:t,description:n,label:i,containerClassName:o,labelClassName:l,variant:c="default"}=e;return(0,r.jsxs)("div",{className:"w-full",children:[(0,r.jsxs)(s.xJ,{className:(0,a.cn)("w-full relative","float"==c?"border rounded-xl focus-within:border-neutral-light px-4 py-1 !space-y-0 focus-visible:outline-none focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 disabled:cursor-not-allowed":"",o),onClick:e=>e.stopPropagation(),children:[i&&(0,r.jsx)(s.lX,{className:l,children:i}),(0,r.jsx)(s.NI,{className:"group relative w-full",children:t}),n&&(0,r.jsx)(s.pf,{children:n}),"default"==c&&(0,r.jsx)(s.zG,{})]}),"float"==c&&(0,r.jsx)(s.zG,{})]})}},61729:function(e,t,n){n.d(t,{Z:function(){return l}});var r=n(57437),s=n(15681),a=n(95186),i=n(75422),o=n(94508);function l(e){let{form:t,label:n,name:l,placeholder:c,description:u,type:d,inputProps:f,children:m,labelClassName:p,containerClassName:g,inputContainer:x,variant:h="default"}=e;return(0,r.jsx)(s.Wi,{control:t.control,name:l,render:e=>{let{field:t}=e;return(0,r.jsx)(i.Z,{label:n,description:u,labelClassName:(0,o.cn)("float"==h?"absolute -top-2 left-2 px-1 text-xs bg-background z-10":"",p),containerClassName:g,variant:h,children:(0,r.jsxs)("div",{className:(0,o.cn)("flex gap-2 w-full overflow-hidden","float"==h?"":"border rounded-sm focus-within:border-neutral-light",x),children:[(0,r.jsx)(a.I,{type:d,placeholder:c,...t,...f,className:(0,o.cn)("border-none focus:outline-none shadow-none focus-visible:ring-0 w-full","float"==h?"px-0":"",null==f?void 0:f.className)}),m]})})}})}},19249:function(e,t,n){n.d(t,{Z:function(){return f}});var r=n(57437),s=n(15681),a=n(95186),i=n(75422),o=n(2265),l=n(62869),c=n(87769),u=n(42208),d=n(94508);function f(e){let{form:t,label:n,name:f,placeholder:m,description:p,inputProps:g,labelClassName:x,containerClassName:h,inputContainer:v,variant:w="default"}=e,[y,N]=(0,o.useState)(!1);return(0,r.jsx)(s.Wi,{control:t.control,name:f,render:e=>{let{field:t}=e;return(0,r.jsx)(i.Z,{label:n,description:p,labelClassName:(0,d.cn)("float"==w?"absolute -top-2 left-2 px-1 text-xs bg-background z-10":"",x),containerClassName:h,variant:w,children:(0,r.jsxs)("div",{className:(0,d.cn)("flex gap-2 w-full overflow-hidden","float"==w?"":"border rounded-sm focus-within:border-neutral-light",v),children:[(0,r.jsx)(a.I,{type:y?"text":"password",placeholder:m,...t,...g,className:(0,d.cn)("border-none focus:outline-none shadow-none focus-visible:ring-0 w-full","float"==w?"px-0":"",null==g?void 0:g.className)}),(0,r.jsx)(l.z,{size:"icon",variant:"ghost",className:"bg-transparent hover:bg-transparent text-seekers-text-light",type:"button",onClick:e=>{e.preventDefault(),e.stopPropagation(),N(e=>!e)},children:y?(0,r.jsx)(c.Z,{className:"w-4 h-4"}):(0,r.jsx)(u.Z,{className:"w-4 h-4"})})]})})}})}},61296:function(e,t,n){n.d(t,{Z:function(){return u}});var r=n(57437),s=n(16831),a=n(92369),i=n(30078),o=n(77647),l=n(94508),c=n(42586);function u(e){var t;let{url:n,className:u}=e;(0,o.l)();let{seekers:d}=(0,i.L)(),f=(0,c.useTranslations)("universal");return(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)(s.qE,{className:(0,l.cn)("w-full rounded-full bg-seekers-text-lighter flex justify-center items-center",u),children:[(0,r.jsx)(s.F$,{src:null===(t=d.accounts)||void 0===t?void 0:t.image,alt:f("misc.profileImageAlt")}),(0,r.jsx)(s.Q5,{className:"bg-transparent text-white",children:d.code?(0,r.jsxs)("span",{children:[d.accounts.firstName[0],d.accounts.lastName[0]]}):(0,r.jsx)(a.Z,{})})]})})}},16831:function(e,t,n){n.d(t,{F$:function(){return l},Q5:function(){return c},qE:function(){return o}});var r=n(57437),s=n(2265),a=n(70650),i=n(94508);let o=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)(a.fC,{ref:t,className:(0,i.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",n),...s})});o.displayName=a.fC.displayName;let l=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)(a.Ee,{ref:t,className:(0,i.cn)("aspect-square h-full w-full",n),...s})});l.displayName=a.Ee.displayName;let c=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)(a.NY,{ref:t,className:(0,i.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",n),...s})});c.displayName=a.NY.displayName},62869:function(e,t,n){n.d(t,{z:function(){return u}});var r=n(57437),s=n(2265),a=n(98482),i=n(90535),o=n(94508),l=n(51817);let c=(0,i.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-seekers-primary text-white shadow hover:bg-seekers-primary/90","default-seekers":"bg-seekers-primary text-white shadow hover:bg-seekers-primary-light/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),u=s.forwardRef((e,t)=>{let{className:n,variant:s,size:i,asChild:u=!1,loading:d=!1,...f}=e,m=u?a.g7:"button";return(0,r.jsx)(m,{className:(0,o.cn)(c({variant:s,size:i,className:n})),ref:t,disabled:d||f.disabled,...f,children:d?(0,r.jsx)(l.Z,{className:(0,o.cn)("h-4 w-4 animate-spin")}):f.children})});u.displayName="Button"},26110:function(e,t,n){n.d(t,{$N:function(){return g},Be:function(){return x},PK:function(){return u},Vq:function(){return l},cN:function(){return p},cZ:function(){return f},fK:function(){return m},hg:function(){return c},t9:function(){return d}});var r=n(57437),s=n(2265),a=n(92360),i=n(20653),o=n(94508);let l=a.fC,c=a.xz,u=a.h_;a.x8;let d=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)(a.aV,{ref:t,className:(0,o.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",n),...s})});d.displayName=a.aV.displayName;let f=s.forwardRef((e,t)=>{let{className:n,children:s,...l}=e;return(0,r.jsxs)(u,{children:[(0,r.jsx)(d,{}),(0,r.jsxs)(a.VY,{ref:t,className:(0,o.cn)("fixed left-[50%] top-[50%] w-full z-50 grid translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",n),...l,children:[s,(0,r.jsxs)(a.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,r.jsx)(i.Pxu,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});f.displayName=a.VY.displayName;let m=e=>{let{className:t,...n}=e;return(0,r.jsx)("div",{className:(0,o.cn)("flex flex-col space-y-1.5 text-start sm:text-left",t),...n})};m.displayName="DialogHeader";let p=e=>{let{className:t,...n}=e;return(0,r.jsx)("div",{className:(0,o.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...n})};p.displayName="DialogFooter";let g=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)(a.Dx,{ref:t,className:(0,o.cn)("text-lg font-semibold leading-none tracking-tight",n),...s})});g.displayName=a.Dx.displayName;let x=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)(a.dk,{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",n),...s})});x.displayName=a.dk.displayName},17814:function(e,t,n){n.d(t,{OX:function(){return f},Qz:function(){return l},dy:function(){return o},iI:function(){return p},sc:function(){return d},u6:function(){return g},ze:function(){return m}});var r=n(57437),s=n(2265),a=n(4216),i=n(94508);let o=e=>{let{shouldScaleBackground:t=!0,...n}=e;return(0,r.jsx)(a.d.Root,{shouldScaleBackground:t,...n})};o.displayName="Drawer";let l=a.d.Trigger,c=a.d.Portal;a.d.Close;let u=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)(a.d.Overlay,{ref:t,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80",n),...s})});u.displayName=a.d.Overlay.displayName;let d=s.forwardRef((e,t)=>{let{className:n,children:s,...o}=e;return(0,r.jsxs)(c,{children:[(0,r.jsx)(u,{}),(0,r.jsxs)(a.d.Content,{ref:t,className:(0,i.cn)("fixed inset-x-0 bottom-0 z-50 mt-24 flex h-auto max-h-[97%] flex-col rounded-t-[10px] border bg-background",n),...o,children:[(0,r.jsx)("div",{className:"mx-auto h-2 w-[100px] rounded-full bg-muted"}),s]})]})});d.displayName="DrawerContent";let f=e=>{let{className:t,...n}=e;return(0,r.jsx)("div",{className:(0,i.cn)("grid gap-1.5 p-4 text-center sm:text-left",t),...n})};f.displayName="DrawerHeader";let m=e=>{let{className:t,...n}=e;return(0,r.jsx)("div",{className:(0,i.cn)("mt-auto flex flex-col gap-2 p-4",t),...n})};m.displayName="DrawerFooter";let p=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)(a.d.Title,{ref:t,className:(0,i.cn)("font-semibold leading-none tracking-tight",n),...s})});p.displayName=a.d.Title.displayName;let g=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)(a.d.Description,{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",n),...s})});g.displayName=a.d.Description.displayName},15681:function(e,t,n){n.d(t,{NI:function(){return x},Wi:function(){return d},l0:function(){return c},lX:function(){return g},pf:function(){return h},xJ:function(){return p},zG:function(){return v}});var r=n(57437),s=n(2265),a=n(98482),i=n(29501),o=n(94508),l=n(26815);let c=i.RV,u=s.createContext({}),d=e=>{let{...t}=e;return(0,r.jsx)(u.Provider,{value:{name:t.name},children:(0,r.jsx)(i.Qr,{...t})})},f=()=>{let e=s.useContext(u),t=s.useContext(m),{getFieldState:n,formState:r}=(0,i.Gc)(),a=n(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=t;return{id:o,name:e.name,formItemId:"".concat(o,"-form-item"),formDescriptionId:"".concat(o,"-form-item-description"),formMessageId:"".concat(o,"-form-item-message"),...a}},m=s.createContext({}),p=s.forwardRef((e,t)=>{let{className:n,...a}=e,i=s.useId();return(0,r.jsx)(m.Provider,{value:{id:i},children:(0,r.jsx)("div",{ref:t,className:(0,o.cn)("space-y-2",n),...a})})});p.displayName="FormItem";let g=s.forwardRef((e,t)=>{let{className:n,...s}=e,{error:a,formItemId:i}=f();return(0,r.jsx)(l._,{ref:t,className:(0,o.cn)(a&&"text-destructive",n),htmlFor:i,...s})});g.displayName="FormLabel";let x=s.forwardRef((e,t)=>{let{...n}=e,{error:s,formItemId:i,formDescriptionId:o,formMessageId:l}=f();return(0,r.jsx)(a.g7,{ref:t,id:i,"aria-describedby":s?"".concat(o," ").concat(l):"".concat(o),"aria-invalid":!!s,...n})});x.displayName="FormControl";let h=s.forwardRef((e,t)=>{let{className:n,...s}=e,{formDescriptionId:a}=f();return(0,r.jsx)("p",{ref:t,id:a,className:(0,o.cn)("text-[0.8rem] text-muted-foreground",n),...s})});h.displayName="FormDescription";let v=s.forwardRef((e,t)=>{let{className:n,children:s,...a}=e,{error:i,formMessageId:l}=f(),c=i?String(null==i?void 0:i.message):s;return c?(0,r.jsx)("p",{ref:t,id:l,className:(0,o.cn)("text-[0.8rem] font-medium text-destructive",n),...a,children:c}):null});v.displayName="FormMessage"},26371:function(e,t,n){n.d(t,{Zn:function(){return l},cY:function(){return u},hf:function(){return c}});var r=n(57437),s=n(2265),a=n(20653),i=n(35934),o=n(94508);let l=s.forwardRef((e,t)=>{let{className:n,containerClassName:s,...a}=e;return(0,r.jsx)(i.uZ,{ref:t,containerClassName:(0,o.cn)("flex items-center gap-2 has-[:disabled]:opacity-50",s),className:(0,o.cn)("disabled:cursor-not-allowed",n),...a})});l.displayName="InputOTP";let c=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)("div",{ref:t,className:(0,o.cn)("flex items-center",n),...s})});c.displayName="InputOTPGroup";let u=s.forwardRef((e,t)=>{let{index:n,className:a,...l}=e,{char:c,hasFakeCaret:u,isActive:d}=s.useContext(i.VM).slots[n];return(0,r.jsxs)("div",{ref:t,className:(0,o.cn)("relative flex h-9 w-9 items-center justify-center border-y border-r border-input text-sm shadow-sm transition-all first:rounded-l-md first:border-l last:rounded-r-md",d&&"z-10 ring-1 ring-ring",a),...l,children:[c,u&&(0,r.jsx)("div",{className:"pointer-events-none absolute inset-0 flex items-center justify-center",children:(0,r.jsx)("div",{className:"h-4 w-px animate-caret-blink bg-foreground duration-1000"})})]})});u.displayName="InputOTPSlot",s.forwardRef((e,t)=>{let{...n}=e;return(0,r.jsx)("div",{ref:t,role:"separator",...n,children:(0,r.jsx)(a.yhV,{})})}).displayName="InputOTPSeparator"},95186:function(e,t,n){n.d(t,{I:function(){return i}});var r=n(57437),s=n(2265),a=n(94508);let i=s.forwardRef((e,t)=>{let{className:n,type:s,...i}=e;return(0,r.jsx)("input",{type:s,className:(0,a.cn)("flex max-sm:h-8 h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-inset focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",n),ref:t,...i})});i.displayName="Input"},26815:function(e,t,n){n.d(t,{_:function(){return c}});var r=n(57437),s=n(2265),a=n(6394),i=n(90535),o=n(94508);let l=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,r.jsx)(a.f,{ref:t,className:(0,o.cn)(l(),n),...s})});c.displayName=a.f.displayName},6512:function(e,t,n){n.d(t,{Separator:function(){return o}});var r=n(57437),s=n(2265),a=n(90759),i=n(94508);let o=s.forwardRef((e,t)=>{let{className:n,orientation:s="horizontal",decorative:o=!0,...l}=e;return(0,r.jsx)(a.f,{ref:t,decorative:o,orientation:s,className:(0,i.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",n),...l})});o.displayName=a.f.displayName},63168:function(e,t,n){n.d(t,{X:function(){return o}});var r=n(70633),s=n(35153),a=n(21770),i=n(42586);function o(e){let{toast:t}=(0,s.pm)(),n=(0,i.useTranslations)("universal");return(0,a.D)({mutationFn:e=>(0,r.u8)(e),onSuccess:t=>{e(t)},onError:r=>{let s=r.response.data;if(s.message.includes("is already sent")){e(r);return}t({title:n("misc.foundError"),description:s.message,variant:"destructive"})}})}},69428:function(e,t,n){n.d(t,{N:function(){return a}});var r=n(70633),s=n(21770);function a(){return(0,s.D)({mutationFn:e=>(0,r.vJ)(e)})}},87128:function(e,t,n){n.d(t,{G:function(){return o}});var r=n(70633),s=n(35153),a=n(21770),i=n(42586);function o(e){let{toast:t}=(0,s.pm)(),n=(0,i.useTranslations)("universal");return(0,a.D)({mutationFn:e=>(0,r.zl)(e),onSuccess:async t=>{await e()},onError:e=>{let r=e.response.data;return t({title:n("misc.foundError"),description:r.message,variant:"destructive"}),r}})}},77647:function(e,t,n){n.d(t,{J:function(){return l},l:function(){return c}});var r=n(25367),s=n(6404),a=n(30078),i=n(16593),o=n(64131);let l="my-detail";function c(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],{setSeekers:t,clearUser:n,setRole:c}=(0,a.L)(e=>e),u=o.Z.get(s.LA);return(0,i.a)({queryKey:[l,u||"0"],queryFn:async()=>{if(!u)return a._;try{let e=await (0,r.O4)();return t(e),c("SEEKER"),e}catch(e){return n(),a._}},refetchOnWindowFocus:!1,retry:!1,enabled:e})}},49607:function(e,t,n){n.d(t,{apiClient:function(){return l},v:function(){return c}});var r=n(6404),s=n(83464),a=n(64131),i=n(51983);let o=new(n.n(i)()).Agent({rejectUnauthorized:!1}),l=s.Z.create({baseURL:"https://dev.property-plaza.id/api/v1",headers:{Authorization:a.Z.get(r.LA)?"Bearer "+a.Z.get(r.LA):""},httpsAgent:o}),c=s.Z.create({baseURL:"/api/",httpsAgent:o})},89047:function(e,t,n){n.d(t,{B9:function(){return r},Dn:function(){return s}});let r={archiver:"Achiever",finder:"Finder",free:"Free"},s={contactOwner:"contact-owner",photos:"photos",mapLocation:"map-location",advanceAndSaveFilter:"advance-and-save-filter",savedListing:"saved-listings",favoriteProperties:"favorite-properties"}},45558:function(e,t,n){n.d(t,{$8:function(){return o},ek:function(){return s},lJ:function(){return i},nH:function(){return a},pb:function(){return l}});var r=n(89047);function s(e){return r.B9.free.includes(e)?r.B9.free:r.B9.finder.includes(e)?r.B9.finder:r.B9.archiver.includes(e)?r.B9.archiver:r.B9.free}function a(e){return e==r.B9.free?0:e==r.B9.finder?5:e==r.B9.archiver?10:0}let i=10,o={max:13,min:10};function l(e){return e==r.B9.free?o:e==r.B9.finder?{max:14,min:i}:e==r.B9.archiver?{max:15,min:i}:o}},31599:function(e,t,n){n.d(t,{AS:function(){return c},Af:function(){return m},Ew:function(){return f},PQ:function(){return u},kS:function(){return a},rb:function(){return d},u8:function(){return i},vJ:function(){return l},x4:function(){return s},zl:function(){return o}});var r=n(49607);let s=(e,t)=>r.apiClient.post("auth/login",e,{headers:{"g-token":t||""}}),a=()=>r.apiClient.post("auth/logout"),i=e=>r.apiClient.post("notifications/email",e),o=e=>r.apiClient.post("auth/otp-verification",e),l=e=>r.apiClient.post("auth/forgot-password",e),c=e=>r.apiClient.get("auth/verify-reset-password?email=".concat(e.email,"&token=").concat(e.token)),u=e=>r.apiClient.post("auth/reset-password",e),d=(e,t)=>r.apiClient.post("auth/create-password",e,t),f=e=>r.apiClient.post("users/security",e),m=e=>r.apiClient.post("auth/totp-verification",e)},70633:function(e,t,n){n.d(t,{AS:function(){return r.AS},Af:function(){return r.Af},Ew:function(){return r.Ew},PQ:function(){return r.PQ},kS:function(){return r.kS},rb:function(){return r.rb},u8:function(){return r.u8},vJ:function(){return r.vJ},x4:function(){return r.x4},zl:function(){return r.zl}});var r=n(31599)},31389:function(e,t,n){n.d(t,{a$:function(){return s},f_:function(){return a},jo:function(){return i}});var r=n(49607);let s=async(e,t)=>r.apiClient.post("auth/register",e,{headers:{"g-token":t||""}}),a=async e=>r.apiClient.put("users/update",e),i=async e=>r.apiClient.get("auth/me",e)},25367:function(e,t,n){n.d(t,{O4:function(){return i}});var r=n(31389),s=n(45558);n(6404);var a=n(74442);async function i(e){try{let t=await (0,r.jo)(e);return function(e){var t,n,r,a,i,o,l,c,u,d;let f=(0,s.ek)((null===(t=e.accounts.subscription)||void 0===t?void 0:t.detail.name)||"");return{accounts:{about:e.accounts.about,citizenship:e.accounts.citizenship||"",credit:{amount:(null===(n=e.accounts.credit)||void 0===n?void 0:n.amount)||0,updatedAt:(null===(r=e.accounts.credit)||void 0===r?void 0:r.updated_at)||""},facebookSocial:e.accounts.facebook_social||"",firstName:e.accounts.first_name,image:e.accounts.image,isSubscriber:e.accounts.is_subscriber,language:e.accounts.language,lastName:e.accounts.last_name,membership:f,twitterSocial:e.accounts.twitter_social||"",address:e.accounts.address||"",chat:{current:0,max:(0,s.nH)(f)},zoomFeature:(0,s.pb)(f)},has2FA:e.is_2fa,email:e.email,code:e.code,isActive:e.is_active,phoneNumber:e.phone_number,phoneCode:e.phone_code,type:e.type,setting:{messageNotif:null===(a=e.accounts.settings)||void 0===a?void 0:a.message_notif,newsletterNotif:null===(i=e.accounts.settings)||void 0===i?void 0:i.newsletter_notif,priceAlertNotif:null===(o=e.accounts.settings)||void 0===o?void 0:o.price_alert_notif,propertyNotif:null===(l=e.accounts.settings)||void 0===l?void 0:l.property_notif,soundNotif:null===(c=e.accounts.settings)||void 0===c?void 0:c.sound_notif,specialOfferNotif:null===(u=e.accounts.settings)||void 0===u?void 0:u.special_offer_notif,surveyNotif:null===(d=e.accounts.settings)||void 0===d?void 0:d.survey_notif}}}(t.data.data)}catch(e){throw Error((0,a.q)(e))}}},74442:function(e,t,n){n.d(t,{q:function(){return s}});var r=n(83464);function s(e){if(r.Z.isAxiosError(e)){var t,n;if((null===(t=e.response)||void 0===t?void 0:t.status)===401)throw Error("Unauthorized: Invalid token or missing credentials");if((null===(n=e.response)||void 0===n?void 0:n.status)===404)throw Error("Not Found: The requested resource could not be found");if(e.response)throw Error("Request failed with status code ".concat(e.response.status,": ").concat(e.response.statusText));if(e.request)throw Error("No response received: Possible network error or wrong endpoint");else throw Error("Error during request setup: ".concat(e.message))}throw Error(e)}},55102:function(e,t,n){n(83079),(0,n(12119).$)("ace39bf07124e0ba39e8486050a53bc79b0621b3")},56083:function(e,t,n){},57860:function(e,t,n){n.d(t,{a:function(){return s}});var r=n(2265);function s(e){let[t,n]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{function t(e){n(e.matches)}let r=matchMedia(e);return r.addEventListener("change",t),n(r.matches),()=>r.removeEventListener("change",t)},[e]),t}},35153:function(e,t,n){n.d(t,{pm:function(){return f}});var r=n(2265);let s=0,a=new Map,i=e=>{if(a.has(e))return;let t=setTimeout(()=>{a.delete(e),u({type:"REMOVE_TOAST",toastId:e})},1e6);a.set(e,t)},o=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:n}=t;return n?i(n):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===n||void 0===n?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],c={toasts:[]};function u(e){c=o(c,e),l.forEach(e=>{e(c)})}function d(e){let{...t}=e,n=(s=(s+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>u({type:"DISMISS_TOAST",toastId:n});return u({type:"ADD_TOAST",toast:{...t,id:n,open:!0,onOpenChange:e=>{e||r()}}}),{id:n,dismiss:r,update:e=>u({type:"UPDATE_TOAST",toast:{...e,id:n}})}}function f(){let[e,t]=r.useState(c);return r.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:d,dismiss:e=>u({type:"DISMISS_TOAST",toastId:e})}}},6404:function(e,t,n){n.d(t,{$_:function(){return p},Ge:function(){return f},K6:function(){return d},LA:function(){return r},QY:function(){return m},Y:function(){return g},Z9:function(){return a},ac:function(){return o},gr:function(){return s},nM:function(){return i},t8:function(){return u},vQ:function(){return c},xm:function(){return l}});let r="tkn",s="SEEKER",a=8,i=1,o=30,l=300,c=10,u="cookies-collection-status",d="necessary-cookies-collection-status",f="functional-cookies-collection-status",m="analytic-cookies-collection-status",p="marketing-cookies-collection-status",g={type:"t",minPrice:"minp",maxPrice:"maxp",landLargest:"landl",landSmallest:"lands",buildingLargest:"buildl",buildingSmallest:"builds",gardenLargest:"gardenl",gardenSmallest:"gardens",yearsOfBuild:"yob",bedroomTotal:"bedt",bathroomTotal:"batht",rentalOffer:"ro",propertyCondition:"pc",electircity:"el",parking:"pk",swimmingPool:"sp",typeLiving:"tl",furnished:"fs",view:"v",minimumContract:"minc",category:"c",subCategory:"sc",feature:"feat",propertyLocation:"pl",zoom:"z",viewMode:"viewMode"}},71517:function(e,t,n){n.d(t,{Fq:function(){return o},GA:function(){return f},Kc:function(){return i},OM:function(){return d},Qk:function(){return p},Y8:function(){return c},ej:function(){return x},in:function(){return u},rr:function(){return l},rv:function(){return m},s0:function(){return g}}),n(57437);var r=n(55736),s=n(45886),a=n(24336);let i="/owner/account",o="/profile",l="/s",c="/favorites",u="/message",d="/subscription",f="/plan",m="/billing",p="/notification",g="/security",x="/representative/account";r.Z,s.Z,a.Z,r.Z,s.Z},94508:function(e,t,n){n.d(t,{E6:function(){return d},ET:function(){return p},Fg:function(){return m},cn:function(){return o},g6:function(){return f},pl:function(){return g},uf:function(){return u},xG:function(){return c},yT:function(){return x}});var r=n(61994),s=n(77398),a=n.n(s),i=n(53335);function o(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,i.m6)((0,r.W)(t))}n(25566);let l=e=>{switch(e){case"USD":default:return"en-us";case"IDR":return"id-ID";case"GBP":return"en-GB";case"AUD":return"en-AU";case"EUR":return"nl-NL"}},c=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return arguments.length>2&&void 0!==arguments[2]&&arguments[2],new Intl.NumberFormat(l(t),{style:"currency",currency:t,minimumFractionDigits:0,maximumFractionDigits:"IDR"==t?0:2}).format(e)},u=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"en-US";return new Intl.NumberFormat(t,{style:"decimal",minimumFractionDigits:0,maximumFractionDigits:0}).format(e)};function d(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}function f(e){let t=a()(e),n=a()();return t.isSame(n,"day")?t.format("HH:mm"):t.format("DD/MM/YY")}function m(e){return e.replaceAll(/[^a-zA-Z0-9\s]/g,"-").replaceAll(/\s/g,"--")}let p=(e,t)=>e.includes(t)?e.filter(e=>e!==t):[...e,t];function g(e,t){return e.some(e=>t.includes(e))}let x=e=>e.charAt(0).toUpperCase()+e.slice(1)},60668:function(e,t,n){n.d(t,{I:function(){return l}});var r=n(77398),s=n.n(r),a=n(59625),i=n(89134);let o={confirm_password:"",first_name:"",last_name:"",otp:"",password:"",type:"SEEKER",email:"",phone_code:"+62",phone_number:""},l=(0,a.Ue)()((0,i.tJ)(e=>({register:o,setRegister:t=>e({register:t}),verifyOtpType:"",setVerifyOtpType:t=>e({verifyOtpType:t}),reset:()=>{l.persist.clearStorage(),e({register:o})},validFormUntil:void 0,setValidFormUntil:t=>e({validFormUntil:t}),successSignUp:!1,setSuccessSignUp:t=>e({successSignUp:t}),loading:!0,setLoading:t=>e({loading:t})}),{name:"register-user",storage:(0,i.FL)(()=>localStorage),onRehydrateStorage:()=>e=>{if(null==e?void 0:e.validFormUntil){let t=s()(null==e?void 0:e.validFormUntil);s()().isAfter(t)&&e.setRegister(o)}null==e||e.setLoading(!1)}}))},28959:function(e,t,n){n.d(t,{R:function(){return i}});var r=n(59625),s=n(89134),a=n(64131);let i=(0,r.Ue)()((0,s.tJ)(e=>({currency:"IDR",setCurrency:t=>e({currency:t}),isLoading:!0,setIsLoading:t=>e({isLoading:t})}),{name:"seekers-settings",storage:{getItem:e=>{let t=a.Z.get(e);return t?JSON.parse(t):void 0},setItem:(e,t)=>{a.Z.set(e,JSON.stringify(t),{expires:7,path:"/"})},removeItem:e=>{a.Z.remove(e)}},onRehydrateStorage:()=>e=>{e&&e.setIsLoading(!1)}}))},30078:function(e,t,n){n.d(t,{L:function(){return u},_:function(){return c}});var r=n(45558),s=n(59625),a=n(89134),i=n(64131),o=n(89047);let l={getItem:e=>{let t=i.Z.get(e);return t?JSON.parse(t):null},setItem:(e,t)=>{i.Z.set(e,JSON.stringify(t),{expires:7})},removeItem:e=>{i.Z.remove(e)}},c={accounts:{about:"",citizenship:"",credit:{amount:0,updatedAt:""},facebookSocial:"",firstName:"",image:"",isSubscriber:!1,language:"",lastName:"",membership:o.B9.free,twitterSocial:"",address:"",chat:{current:0,max:0},zoomFeature:r.$8},has2FA:!1,email:"",code:"",isActive:!1,phoneNumber:"",phoneCode:"",type:"SEEKER",setting:{messageNotif:!1,newsletterNotif:!1,priceAlertNotif:!1,propertyNotif:!1,soundNotif:!1,specialOfferNotif:!1,surveyNotif:!1}},u=(0,s.Ue)()((0,a.tJ)(e=>({role:void 0,setRole:t=>e({role:t}),seekers:c,setSeekers:t=>e({seekers:t}),tempSubscribtionLevel:0,setTempSubscribtionLevel:t=>e({tempSubscribtionLevel:t}),clearUser:()=>e(()=>({seekers:c}))}),{name:"user",storage:(0,a.FL)(()=>l)}))}}]);