(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6166],{14528:function(e,t,r){Promise.resolve().then(r.bind(r,44134)),Promise.resolve().then(r.bind(r,79244)),Promise.resolve().then(r.bind(r,12545)),Promise.resolve().then(r.bind(r,97867)),Promise.resolve().then(r.bind(r,31085)),Promise.resolve().then(r.bind(r,10575))},44134:function(e,t,r){"use strict";r.d(t,{default:function(){return d}});var n=r(57437),s=r(62869),a=r(66070),i=r(69428),o=r(35153),l=r(30078),c=r(88906),u=r(42586);function d(){let e=(0,u.useTranslations)("seeker"),t=(0,i.N)(),r=(0,l.L)(e=>e.seekers.email),{toast:d}=(0,o.pm)(),f=async()=>{try{await t.mutateAsync({email:r}),d({title:e("success.requestForgetPassword.title"),description:e("success.requestForgetPassword.description",{email:r})})}catch(t){d({title:e("error.requestForgetPassword.title")})}};return(0,n.jsxs)(a.Zb,{children:[(0,n.jsxs)(a.Ol,{className:"flex max-sm:flex-col max-sm:items-start max-sm:gap-4 max-sm:pb-4 flex-row  items-center justify-between space-y-0  pb-2",children:[(0,n.jsxs)("div",{className:"space-y-1",children:[(0,n.jsxs)(a.ll,{className:"text-seekers-primary flex items-center",children:[(0,n.jsx)(c.Z,{className:"mr-2 h-4 w-4"}),e("settings.profile.security.password.title")]}),(0,n.jsx)(a.SZ,{children:e("settings.profile.security.password.description")})]}),(0,n.jsx)(s.z,{size:"sm",variant:"outline",className:"border-seekers-primary text-seekers-primary hover:text-seekers-primary hover:bg-seekers-primary/10",onClick:()=>f(),children:e("cta.changePassword")})]}),(0,n.jsx)(a.aY,{children:(0,n.jsxs)("div",{className:"text-sm text-muted-foreground",children:[e("setting.profile.security.password.lastChanged")," January 15, 2025"]})})]})}},79244:function(e,t,r){"use strict";r.d(t,{default:function(){return _}});var n=r(57437),s=r(35974),a=r(66070),i=r(94508),o=r(33388),l=r(42586),c=r(43184),u=r(93166),d=r(84002),f=r(79318),m=r(62869),p=r(31599);async function x(e){try{return{data:(await (0,p.Ew)(e)).data.data}}catch(e){var t;return{error:null!==(t=e.data.error)&&void 0!==t?t:"An unknown error occurred"}}}var h=r(16593),g=r(33145),v=r(2265),b=r(31229),y=r(29501),N=r(13590),w=r(15681),j=r(75422),A=r(26371),k=r(35934),S=r(70633),F=r(21770),C=r(35153),T=r(30078);function E(e){let{setOpenDialog:t}=e,r=(0,l.useTranslations)("seeker"),s=b.z.object({otp:b.z.string().max(6)}),{seekers:a}=(0,T.L)(),i=(0,F.D)({mutationFn:e=>(0,S.Af)(e)}),o=(0,F.D)({mutationFn:e=>(0,S.Ew)({...e,request_setting:"ACTIVE_2FA"})}),c=(0,y.cI)({resolver:(0,N.F)(s),defaultValues:{otp:""}}),{toast:u}=(0,C.pm)(),d=c.watch("otp"),f=async e=>{try{a.has2FA?await i.mutateAsync({otp:e.otp,requested_by:a.code}):await o.mutateAsync({otp:+e.otp,request_setting:"ACTIVE_2FA"}),u({title:r("success.activateTotp")}),null==t||t(!1),window.location.reload()}catch(t){let e=t.response.data;u({variant:"destructive",title:r("error.failedEnablingTwoFA"),description:e.message})}};return(0,v.useEffect)(()=>{if(6==d.length){var e;null===(e=document.getElementById("submit-form"))||void 0===e||e.dispatchEvent(new Event("submit",{cancelable:!0,bubbles:!0}))}},[d,c]),(0,n.jsx)(w.l0,{...c,children:(0,n.jsx)("form",{id:"submit-form",onSubmit:c.handleSubmit(f),children:(0,n.jsx)(w.Wi,{control:c.control,name:"otp",render:e=>{let{field:t}=e;return(0,n.jsx)("div",{className:"flex justify-start",children:(0,n.jsx)(j.Z,{label:"",children:(0,n.jsx)(A.Zn,{maxLength:6,...t,pattern:k.Ww,required:!0,containerClassName:"flex justify-start",children:(0,n.jsx)(A.hf,{children:Array.from({length:6},(e,t)=>(0,n.jsx)(A.cY,{index:t,className:"w-10 h-10 text-xl"},t))})})})})}})})})}var I=r(19249);function z(e){let{onSubmit:t,isLoading:r}=e,s=(0,l.useTranslations)("seeker"),a=((0,l.useTranslations)("seeker"),b.z.object({password:b.z.string()})),{seekers:i}=(0,T.L)(),o=(0,y.cI)({resolver:(0,N.F)(a),defaultValues:{password:""}});return(0,n.jsx)(w.l0,{...o,children:(0,n.jsxs)("form",{onSubmit:o.handleSubmit(e=>{t(e.password)}),className:"space-y-4",children:[(0,n.jsx)(I.Z,{label:s("form.label.password"),form:o,name:"password",placeholder:""}),(0,n.jsx)(m.z,{className:"w-full",variant:"default-seekers",loading:r,children:s("cta.get2FACode")})]})})}function R(e){var t;let{}=e,r=(0,l.useTranslations)("seeker"),[s,a]=(0,v.useState)(!1),[i,o]=(0,v.useState)(!1),{seekers:p}=(0,T.L)(),[b,y]=(0,v.useState)(!1),[N,w]=(0,v.useState)("ACTIVE_2FA"),[j,A]=(0,v.useState)({request_setting:"ACTIVE_2FA",password:""}),k=function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return(0,h.a)({queryKey:["two-fa-otp",e],queryFn:async()=>await x(e),retry:0,enabled:t})}(j,s&&i);(0,v.useEffect)(()=>{p.has2FA&&(A(e=>({...e,request_setting:"INACTIVE_2FA"})),w("INACTIVE_2FA")),p.has2FA||(A(e=>({...e,request_setting:"REQUEST_2FA"})),w("REQUEST_2FA"))},[p.has2FA,s]),(0,v.useEffect)(()=>{if(k.isFetched&&k.isSuccess)return(o(!1),"INACTIVE_2FA"==N)?window.location.reload():void y(!0)},[k.isFetched,k.isSuccess,N]);let S=e=>{A(t=>({...t,password:e})),o(!0)};return(0,n.jsxs)(f.Z,{open:s,setOpen:a,dialogClassName:"!max-w-fit !w-fit",openTrigger:(0,n.jsx)(m.z,{variant:"outline",size:"sm",className:p.has2FA?"border-red-500 text-red-500 hover:text-red-500 hover:bg-red-50":"border-seekers-primary text-seekers-primary hover:text-seekers-primary hover:bg-seekers-primary/10",onClick:()=>{},children:r(p.has2FA?"cta.disable":"cta.enable")}),children:[(0,n.jsxs)(u.Z,{className:"text-seekers-text text-center flex flex-col items-center",children:[(0,n.jsx)(d.Z,{children:r("setting.profile.security.twoFA.title")}),(0,n.jsx)(c.Z,{className:"text-seekers-text-light",children:r("setting.profile.security.twoFA.description")})]}),b?(0,n.jsxs)("div",{className:"flex max-sm:flex-col gap-4",children:[(0,n.jsx)("div",{className:"relative aspect-square",style:{margin:"0 auto",height:200,width:200},children:(0,n.jsx)(g.default,{src:(null===(t=k.data)||void 0===t?void 0:t.data.qr_image)||"",alt:"",fill:!0,style:{objectFit:"cover"}})}),(0,n.jsxs)("div",{className:"md:max-w-xs space-y-4",children:[(0,n.jsx)("h2",{className:"max-sm:text-sm max-sm:text-center text-xl font-bold",children:r("setting.profile.security.twoFA.useAuthenticatorApp")}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("p",{children:[(0,n.jsxs)("span",{className:"font-bold",children:[r("misc.step",{count:1})," "]})," ",r("setting.profile.security.twoFA.scanQRCodeWithAuthenticatorApp")]}),(0,n.jsxs)("p",{children:[(0,n.jsx)("span",{className:"font-bold",children:r("misc.step",{count:2})})," ",r("setting.profile.security.twoFA.enterAuthenticatorCode")]})]}),(0,n.jsx)(E,{setOpenDialog:e=>a(e)})]})]}):(0,n.jsx)("div",{children:(0,n.jsx)(z,{isLoading:k.isLoading,onSubmit:e=>S(e)})})]})}function _(){let e=(0,l.useTranslations)("seeker"),{seekers:t}=(0,T.L)();return(0,n.jsxs)(a.Zb,{children:[(0,n.jsxs)(a.Ol,{className:"flex max-sm:flex-col max-sm:items-start max-sm:gap-4 max-sm:pb-4 flex-row items-center justify-between space-y-0 pb-2",children:[(0,n.jsxs)("div",{className:"space-y-1",children:[(0,n.jsxs)(a.ll,{className:"text-seekers-primary flex items-center",children:[(0,n.jsx)(o.Z,{className:"mr-2 h-4 w-4"}),e("setting.profile.security.twoFA.title")]}),(0,n.jsx)(a.SZ,{children:e("setting.profile.security.twoFA.description")})]}),(0,n.jsx)(R,{enableTwoFA:t.has2FA})]}),(0,n.jsx)(a.aY,{children:(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("span",{className:"text-sm text-muted-foreground",children:e("misc.status")}),(0,n.jsx)(s.C,{variant:t.has2FA?"default":"destructive",className:(0,i.cn)(t.has2FA?"bg-green-500/10 text-green-500":"bg-red-500/10 text-red-500"),children:e(t.has2FA?"cta.enable":"cta.disable")})]})})]})}},43184:function(e,t,r){"use strict";r.d(t,{Z:function(){return o}});var n=r(57437),s=r(57860),a=r(17814),i=r(26110);function o(e){let{children:t,className:r}=e;return(0,s.a)("(min-width:768px)")?(0,n.jsx)(i.Be,{className:r,children:t}):(0,n.jsx)(a.u6,{className:r,children:t})}},93166:function(e,t,r){"use strict";r.d(t,{Z:function(){return o}});var n=r(57437),s=r(57860),a=r(26110),i=r(17814);function o(e){let{children:t,className:r}=e;return(0,s.a)("(min-width:1024px)")?(0,n.jsx)(a.fK,{className:r,children:t}):(0,n.jsx)(i.OX,{className:r,children:t})}},84002:function(e,t,r){"use strict";r.d(t,{Z:function(){return o}});var n=r(57437),s=r(57860),a=r(17814),i=r(26110);function o(e){let{children:t,className:r}=e;return(0,s.a)("(min-width:1024px)")?(0,n.jsx)(i.$N,{className:r,children:t}):(0,n.jsx)(a.iI,{className:r,children:t})}},79318:function(e,t,r){"use strict";r.d(t,{Z:function(){return l}});var n=r(57437),s=r(26110),a=r(57860),i=r(94508);r(2265);var o=r(17814);function l(e){let{children:t,openTrigger:r,open:l,setOpen:c,dialogClassName:u,drawerClassName:d,dialogOverlayClassName:f}=e;return(0,a.a)("(min-width:1024px)")?(0,n.jsxs)(s.Vq,{open:l,onOpenChange:c,children:[(0,n.jsx)(s.hg,{asChild:!0,children:r}),(0,n.jsxs)(s.PK,{children:[(0,n.jsx)(s.t9,{className:f}),(0,n.jsx)(s.cZ,{className:(0,i.cn)("max-sm:w-screen md:w-[450px]  md:min-w-[450px] max-sm:h-screen flex flex-col justify-start",u),children:t})]})]}):(0,n.jsxs)(o.dy,{open:l,onOpenChange:c,children:[(0,n.jsx)(o.Qz,{asChild:!0,children:r}),(0,n.jsx)(o.sc,{children:(0,n.jsx)("div",{className:(0,i.cn)("p-4 overflow-auto",d),children:t})})]})}},75422:function(e,t,r){"use strict";r.d(t,{Z:function(){return i}});var n=r(57437),s=r(15681),a=r(94508);function i(e){let{children:t,description:r,label:i,containerClassName:o,labelClassName:l,variant:c="default"}=e;return(0,n.jsxs)("div",{className:"w-full",children:[(0,n.jsxs)(s.xJ,{className:(0,a.cn)("w-full relative","float"==c?"border rounded-xl focus-within:border-neutral-light px-4 py-1 !space-y-0 focus-visible:outline-none focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 disabled:cursor-not-allowed":"",o),onClick:e=>e.stopPropagation(),children:[i&&(0,n.jsx)(s.lX,{className:l,children:i}),(0,n.jsx)(s.NI,{className:"group relative w-full",children:t}),r&&(0,n.jsx)(s.pf,{children:r}),"default"==c&&(0,n.jsx)(s.zG,{})]}),"float"==c&&(0,n.jsx)(s.zG,{})]})}},19249:function(e,t,r){"use strict";r.d(t,{Z:function(){return f}});var n=r(57437),s=r(15681),a=r(95186),i=r(75422),o=r(2265),l=r(62869),c=r(87769),u=r(42208),d=r(94508);function f(e){let{form:t,label:r,name:f,placeholder:m,description:p,inputProps:x,labelClassName:h,containerClassName:g,inputContainer:v,variant:b="default"}=e,[y,N]=(0,o.useState)(!1);return(0,n.jsx)(s.Wi,{control:t.control,name:f,render:e=>{let{field:t}=e;return(0,n.jsx)(i.Z,{label:r,description:p,labelClassName:(0,d.cn)("float"==b?"absolute -top-2 left-2 px-1 text-xs bg-background z-10":"",h),containerClassName:g,variant:b,children:(0,n.jsxs)("div",{className:(0,d.cn)("flex gap-2 w-full overflow-hidden","float"==b?"":"border rounded-sm focus-within:border-neutral-light",v),children:[(0,n.jsx)(a.I,{type:y?"text":"password",placeholder:m,...t,...x,className:(0,d.cn)("border-none focus:outline-none shadow-none focus-visible:ring-0 w-full","float"==b?"px-0":"",null==x?void 0:x.className)}),(0,n.jsx)(l.z,{size:"icon",variant:"ghost",className:"bg-transparent hover:bg-transparent text-seekers-text-light",type:"button",onClick:e=>{e.preventDefault(),e.stopPropagation(),N(e=>!e)},children:y?(0,n.jsx)(c.Z,{className:"w-4 h-4"}):(0,n.jsx)(u.Z,{className:"w-4 h-4"})})]})})}})}},35974:function(e,t,r){"use strict";r.d(t,{C:function(){return o}});var n=r(57437);r(2265);var s=r(90535),a=r(94508);let i=(0,s.j)("cursor-pointer inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs h-fit font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground  hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground  hover:bg-destructive/80",outline:"text-foreground",seekers:"border-transparent bg-seekers-primary/30 text-seekers-primary hover:bg-seekers-primary/80 rounded-full px-4 py-1.5"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:r,...s}=e;return(0,n.jsx)("div",{className:(0,a.cn)(i({variant:r}),t,"pointer-events-none"),...s})}},62869:function(e,t,r){"use strict";r.d(t,{z:function(){return u}});var n=r(57437),s=r(2265),a=r(98482),i=r(90535),o=r(94508),l=r(51817);let c=(0,i.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-seekers-primary text-white shadow hover:bg-seekers-primary/90","default-seekers":"bg-seekers-primary text-white shadow hover:bg-seekers-primary-light/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),u=s.forwardRef((e,t)=>{let{className:r,variant:s,size:i,asChild:u=!1,loading:d=!1,...f}=e,m=u?a.g7:"button";return(0,n.jsx)(m,{className:(0,o.cn)(c({variant:s,size:i,className:r})),ref:t,disabled:d||f.disabled,...f,children:d?(0,n.jsx)(l.Z,{className:(0,o.cn)("h-4 w-4 animate-spin")}):f.children})});u.displayName="Button"},66070:function(e,t,r){"use strict";r.d(t,{Ol:function(){return o},SZ:function(){return c},Zb:function(){return i},aY:function(){return u},ll:function(){return l}});var n=r(57437),s=r(2265),a=r(94508);let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,a.cn)("rounded-xl border bg-card text-card-foreground shadow",r),...s})});i.displayName="Card";let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",r),...s})});o.displayName="CardHeader";let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("h3",{ref:t,className:(0,a.cn)("font-semibold leading-none tracking-tight",r),...s})});l.displayName="CardTitle";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("p",{ref:t,className:(0,a.cn)("text-sm text-muted-foreground",r),...s})});c.displayName="CardDescription";let u=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,a.cn)("p-6 pt-0",r),...s})});u.displayName="CardContent",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,a.cn)("flex items-center p-6 pt-0",r),...s})}).displayName="CardFooter"},26110:function(e,t,r){"use strict";r.d(t,{$N:function(){return x},Be:function(){return h},PK:function(){return u},Vq:function(){return l},cN:function(){return p},cZ:function(){return f},fK:function(){return m},hg:function(){return c},t9:function(){return d}});var n=r(57437),s=r(2265),a=r(92360),i=r(20653),o=r(94508);let l=a.fC,c=a.xz,u=a.h_;a.x8;let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)(a.aV,{ref:t,className:(0,o.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",r),...s})});d.displayName=a.aV.displayName;let f=s.forwardRef((e,t)=>{let{className:r,children:s,...l}=e;return(0,n.jsxs)(u,{children:[(0,n.jsx)(d,{}),(0,n.jsxs)(a.VY,{ref:t,className:(0,o.cn)("fixed left-[50%] top-[50%] w-full z-50 grid translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",r),...l,children:[s,(0,n.jsxs)(a.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,n.jsx)(i.Pxu,{className:"h-4 w-4"}),(0,n.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});f.displayName=a.VY.displayName;let m=e=>{let{className:t,...r}=e;return(0,n.jsx)("div",{className:(0,o.cn)("flex flex-col space-y-1.5 text-start sm:text-left",t),...r})};m.displayName="DialogHeader";let p=e=>{let{className:t,...r}=e;return(0,n.jsx)("div",{className:(0,o.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...r})};p.displayName="DialogFooter";let x=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)(a.Dx,{ref:t,className:(0,o.cn)("text-lg font-semibold leading-none tracking-tight",r),...s})});x.displayName=a.Dx.displayName;let h=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)(a.dk,{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",r),...s})});h.displayName=a.dk.displayName},17814:function(e,t,r){"use strict";r.d(t,{OX:function(){return f},Qz:function(){return l},dy:function(){return o},iI:function(){return p},sc:function(){return d},u6:function(){return x},ze:function(){return m}});var n=r(57437),s=r(2265),a=r(4216),i=r(94508);let o=e=>{let{shouldScaleBackground:t=!0,...r}=e;return(0,n.jsx)(a.d.Root,{shouldScaleBackground:t,...r})};o.displayName="Drawer";let l=a.d.Trigger,c=a.d.Portal;a.d.Close;let u=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)(a.d.Overlay,{ref:t,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80",r),...s})});u.displayName=a.d.Overlay.displayName;let d=s.forwardRef((e,t)=>{let{className:r,children:s,...o}=e;return(0,n.jsxs)(c,{children:[(0,n.jsx)(u,{}),(0,n.jsxs)(a.d.Content,{ref:t,className:(0,i.cn)("fixed inset-x-0 bottom-0 z-50 mt-24 flex h-auto max-h-[97%] flex-col rounded-t-[10px] border bg-background",r),...o,children:[(0,n.jsx)("div",{className:"mx-auto h-2 w-[100px] rounded-full bg-muted"}),s]})]})});d.displayName="DrawerContent";let f=e=>{let{className:t,...r}=e;return(0,n.jsx)("div",{className:(0,i.cn)("grid gap-1.5 p-4 text-center sm:text-left",t),...r})};f.displayName="DrawerHeader";let m=e=>{let{className:t,...r}=e;return(0,n.jsx)("div",{className:(0,i.cn)("mt-auto flex flex-col gap-2 p-4",t),...r})};m.displayName="DrawerFooter";let p=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)(a.d.Title,{ref:t,className:(0,i.cn)("font-semibold leading-none tracking-tight",r),...s})});p.displayName=a.d.Title.displayName;let x=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)(a.d.Description,{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",r),...s})});x.displayName=a.d.Description.displayName},15681:function(e,t,r){"use strict";r.d(t,{NI:function(){return h},Wi:function(){return d},l0:function(){return c},lX:function(){return x},pf:function(){return g},xJ:function(){return p},zG:function(){return v}});var n=r(57437),s=r(2265),a=r(98482),i=r(29501),o=r(94508),l=r(26815);let c=i.RV,u=s.createContext({}),d=e=>{let{...t}=e;return(0,n.jsx)(u.Provider,{value:{name:t.name},children:(0,n.jsx)(i.Qr,{...t})})},f=()=>{let e=s.useContext(u),t=s.useContext(m),{getFieldState:r,formState:n}=(0,i.Gc)(),a=r(e.name,n);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=t;return{id:o,name:e.name,formItemId:"".concat(o,"-form-item"),formDescriptionId:"".concat(o,"-form-item-description"),formMessageId:"".concat(o,"-form-item-message"),...a}},m=s.createContext({}),p=s.forwardRef((e,t)=>{let{className:r,...a}=e,i=s.useId();return(0,n.jsx)(m.Provider,{value:{id:i},children:(0,n.jsx)("div",{ref:t,className:(0,o.cn)("space-y-2",r),...a})})});p.displayName="FormItem";let x=s.forwardRef((e,t)=>{let{className:r,...s}=e,{error:a,formItemId:i}=f();return(0,n.jsx)(l._,{ref:t,className:(0,o.cn)(a&&"text-destructive",r),htmlFor:i,...s})});x.displayName="FormLabel";let h=s.forwardRef((e,t)=>{let{...r}=e,{error:s,formItemId:i,formDescriptionId:o,formMessageId:l}=f();return(0,n.jsx)(a.g7,{ref:t,id:i,"aria-describedby":s?"".concat(o," ").concat(l):"".concat(o),"aria-invalid":!!s,...r})});h.displayName="FormControl";let g=s.forwardRef((e,t)=>{let{className:r,...s}=e,{formDescriptionId:a}=f();return(0,n.jsx)("p",{ref:t,id:a,className:(0,o.cn)("text-[0.8rem] text-muted-foreground",r),...s})});g.displayName="FormDescription";let v=s.forwardRef((e,t)=>{let{className:r,children:s,...a}=e,{error:i,formMessageId:l}=f(),c=i?String(null==i?void 0:i.message):s;return c?(0,n.jsx)("p",{ref:t,id:l,className:(0,o.cn)("text-[0.8rem] font-medium text-destructive",r),...a,children:c}):null});v.displayName="FormMessage"},26371:function(e,t,r){"use strict";r.d(t,{Zn:function(){return l},cY:function(){return u},hf:function(){return c}});var n=r(57437),s=r(2265),a=r(20653),i=r(35934),o=r(94508);let l=s.forwardRef((e,t)=>{let{className:r,containerClassName:s,...a}=e;return(0,n.jsx)(i.uZ,{ref:t,containerClassName:(0,o.cn)("flex items-center gap-2 has-[:disabled]:opacity-50",s),className:(0,o.cn)("disabled:cursor-not-allowed",r),...a})});l.displayName="InputOTP";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,o.cn)("flex items-center",r),...s})});c.displayName="InputOTPGroup";let u=s.forwardRef((e,t)=>{let{index:r,className:a,...l}=e,{char:c,hasFakeCaret:u,isActive:d}=s.useContext(i.VM).slots[r];return(0,n.jsxs)("div",{ref:t,className:(0,o.cn)("relative flex h-9 w-9 items-center justify-center border-y border-r border-input text-sm shadow-sm transition-all first:rounded-l-md first:border-l last:rounded-r-md",d&&"z-10 ring-1 ring-ring",a),...l,children:[c,u&&(0,n.jsx)("div",{className:"pointer-events-none absolute inset-0 flex items-center justify-center",children:(0,n.jsx)("div",{className:"h-4 w-px animate-caret-blink bg-foreground duration-1000"})})]})});u.displayName="InputOTPSlot",s.forwardRef((e,t)=>{let{...r}=e;return(0,n.jsx)("div",{ref:t,role:"separator",...r,children:(0,n.jsx)(a.yhV,{})})}).displayName="InputOTPSeparator"},95186:function(e,t,r){"use strict";r.d(t,{I:function(){return i}});var n=r(57437),s=r(2265),a=r(94508);let i=s.forwardRef((e,t)=>{let{className:r,type:s,...i}=e;return(0,n.jsx)("input",{type:s,className:(0,a.cn)("flex max-sm:h-8 h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-inset focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...i})});i.displayName="Input"},26815:function(e,t,r){"use strict";r.d(t,{_:function(){return c}});var n=r(57437),s=r(2265),a=r(6394),i=r(90535),o=r(94508);let l=(0,i.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)(a.f,{ref:t,className:(0,o.cn)(l(),r),...s})});c.displayName=a.f.displayName},6512:function(e,t,r){"use strict";r.d(t,{Separator:function(){return o}});var n=r(57437),s=r(2265),a=r(90759),i=r(94508);let o=s.forwardRef((e,t)=>{let{className:r,orientation:s="horizontal",decorative:o=!0,...l}=e;return(0,n.jsx)(a.f,{ref:t,decorative:o,orientation:s,className:(0,i.cn)("shrink-0 bg-border","horizontal"===s?"h-[1px] w-full":"h-full w-[1px]",r),...l})});o.displayName=a.f.displayName},69428:function(e,t,r){"use strict";r.d(t,{N:function(){return a}});var n=r(70633),s=r(21770);function a(){return(0,s.D)({mutationFn:e=>(0,n.vJ)(e)})}},49607:function(e,t,r){"use strict";r.d(t,{apiClient:function(){return l},v:function(){return c}});var n=r(6404),s=r(83464),a=r(64131),i=r(51983);let o=new(r.n(i)()).Agent({rejectUnauthorized:!1}),l=s.Z.create({baseURL:"https://dev.property-plaza.id/api/v1",headers:{Authorization:a.Z.get(n.LA)?"Bearer "+a.Z.get(n.LA):""},httpsAgent:o}),c=s.Z.create({baseURL:"/api/",httpsAgent:o})},89047:function(e,t,r){"use strict";r.d(t,{B9:function(){return n},Dn:function(){return s}});let n={archiver:"Achiever",finder:"Finder",free:"Free"},s={contactOwner:"contact-owner",photos:"photos",mapLocation:"map-location",advanceAndSaveFilter:"advance-and-save-filter",savedListing:"saved-listings",favoriteProperties:"favorite-properties"}},45558:function(e,t,r){"use strict";r.d(t,{$8:function(){return o},ek:function(){return s},lJ:function(){return i},nH:function(){return a},pb:function(){return l}});var n=r(89047);function s(e){return n.B9.free.includes(e)?n.B9.free:n.B9.finder.includes(e)?n.B9.finder:n.B9.archiver.includes(e)?n.B9.archiver:n.B9.free}function a(e){return e==n.B9.free?0:e==n.B9.finder?5:e==n.B9.archiver?10:0}let i=10,o={max:13,min:10};function l(e){return e==n.B9.free?o:e==n.B9.finder?{max:14,min:i}:e==n.B9.archiver?{max:15,min:i}:o}},31599:function(e,t,r){"use strict";r.d(t,{AS:function(){return c},Af:function(){return m},Ew:function(){return f},PQ:function(){return u},kS:function(){return a},rb:function(){return d},u8:function(){return i},vJ:function(){return l},x4:function(){return s},zl:function(){return o}});var n=r(49607);let s=(e,t)=>n.apiClient.post("auth/login",e,{headers:{"g-token":t||""}}),a=()=>n.apiClient.post("auth/logout"),i=e=>n.apiClient.post("notifications/email",e),o=e=>n.apiClient.post("auth/otp-verification",e),l=e=>n.apiClient.post("auth/forgot-password",e),c=e=>n.apiClient.get("auth/verify-reset-password?email=".concat(e.email,"&token=").concat(e.token)),u=e=>n.apiClient.post("auth/reset-password",e),d=(e,t)=>n.apiClient.post("auth/create-password",e,t),f=e=>n.apiClient.post("users/security",e),m=e=>n.apiClient.post("auth/totp-verification",e)},70633:function(e,t,r){"use strict";r.d(t,{AS:function(){return n.AS},Af:function(){return n.Af},Ew:function(){return n.Ew},PQ:function(){return n.PQ},kS:function(){return n.kS},rb:function(){return n.rb},u8:function(){return n.u8},vJ:function(){return n.vJ},x4:function(){return n.x4},zl:function(){return n.zl}});var n=r(31599)},57860:function(e,t,r){"use strict";r.d(t,{a:function(){return s}});var n=r(2265);function s(e){let[t,r]=(0,n.useState)(!1);return(0,n.useEffect)(()=>{function t(e){r(e.matches)}let n=matchMedia(e);return n.addEventListener("change",t),r(n.matches),()=>n.removeEventListener("change",t)},[e]),t}},35153:function(e,t,r){"use strict";r.d(t,{pm:function(){return f}});var n=r(2265);let s=0,a=new Map,i=e=>{if(a.has(e))return;let t=setTimeout(()=>{a.delete(e),u({type:"REMOVE_TOAST",toastId:e})},1e6);a.set(e,t)},o=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?i(r):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],c={toasts:[]};function u(e){c=o(c,e),l.forEach(e=>{e(c)})}function d(e){let{...t}=e,r=(s=(s+1)%Number.MAX_SAFE_INTEGER).toString(),n=()=>u({type:"DISMISS_TOAST",toastId:r});return u({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||n()}}}),{id:r,dismiss:n,update:e=>u({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function f(){let[e,t]=n.useState(c);return n.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:d,dismiss:e=>u({type:"DISMISS_TOAST",toastId:e})}}},6404:function(e,t,r){"use strict";r.d(t,{$_:function(){return p},Ge:function(){return f},K6:function(){return d},LA:function(){return n},QY:function(){return m},Y:function(){return x},Z9:function(){return a},ac:function(){return o},gr:function(){return s},nM:function(){return i},t8:function(){return u},vQ:function(){return c},xm:function(){return l}});let n="tkn",s="SEEKER",a=8,i=1,o=30,l=300,c=10,u="cookies-collection-status",d="necessary-cookies-collection-status",f="functional-cookies-collection-status",m="analytic-cookies-collection-status",p="marketing-cookies-collection-status",x={type:"t",minPrice:"minp",maxPrice:"maxp",landLargest:"landl",landSmallest:"lands",buildingLargest:"buildl",buildingSmallest:"builds",gardenLargest:"gardenl",gardenSmallest:"gardens",yearsOfBuild:"yob",bedroomTotal:"bedt",bathroomTotal:"batht",rentalOffer:"ro",propertyCondition:"pc",electircity:"el",parking:"pk",swimmingPool:"sp",typeLiving:"tl",furnished:"fs",view:"v",minimumContract:"minc",category:"c",subCategory:"sc",feature:"feat",propertyLocation:"pl",zoom:"z",viewMode:"viewMode"}},94508:function(e,t,r){"use strict";r.d(t,{E6:function(){return d},ET:function(){return p},Fg:function(){return m},cn:function(){return o},g6:function(){return f},pl:function(){return x},uf:function(){return u},xG:function(){return c},yT:function(){return h}});var n=r(61994),s=r(77398),a=r.n(s),i=r(53335);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,i.m6)((0,n.W)(t))}r(25566);let l=e=>{switch(e){case"USD":default:return"en-us";case"IDR":return"id-ID";case"GBP":return"en-GB";case"AUD":return"en-AU";case"EUR":return"nl-NL"}},c=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return arguments.length>2&&void 0!==arguments[2]&&arguments[2],new Intl.NumberFormat(l(t),{style:"currency",currency:t,minimumFractionDigits:0,maximumFractionDigits:"IDR"==t?0:2}).format(e)},u=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"en-US";return new Intl.NumberFormat(t,{style:"decimal",minimumFractionDigits:0,maximumFractionDigits:0}).format(e)};function d(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}function f(e){let t=a()(e),r=a()();return t.isSame(r,"day")?t.format("HH:mm"):t.format("DD/MM/YY")}function m(e){return e.replaceAll(/[^a-zA-Z0-9\s]/g,"-").replaceAll(/\s/g,"--")}let p=(e,t)=>e.includes(t)?e.filter(e=>e!==t):[...e,t];function x(e,t){return e.some(e=>t.includes(e))}let h=e=>e.charAt(0).toUpperCase()+e.slice(1)},30078:function(e,t,r){"use strict";r.d(t,{L:function(){return u},_:function(){return c}});var n=r(45558),s=r(59625),a=r(89134),i=r(64131),o=r(89047);let l={getItem:e=>{let t=i.Z.get(e);return t?JSON.parse(t):null},setItem:(e,t)=>{i.Z.set(e,JSON.stringify(t),{expires:7})},removeItem:e=>{i.Z.remove(e)}},c={accounts:{about:"",citizenship:"",credit:{amount:0,updatedAt:""},facebookSocial:"",firstName:"",image:"",isSubscriber:!1,language:"",lastName:"",membership:o.B9.free,twitterSocial:"",address:"",chat:{current:0,max:0},zoomFeature:n.$8},has2FA:!1,email:"",code:"",isActive:!1,phoneNumber:"",phoneCode:"",type:"SEEKER",setting:{messageNotif:!1,newsletterNotif:!1,priceAlertNotif:!1,propertyNotif:!1,soundNotif:!1,specialOfferNotif:!1,surveyNotif:!1}},u=(0,s.Ue)()((0,a.tJ)(e=>({role:void 0,setRole:t=>e({role:t}),seekers:c,setSeekers:t=>e({seekers:t}),tempSubscribtionLevel:0,setTempSubscribtionLevel:t=>e({tempSubscribtionLevel:t}),clearUser:()=>e(()=>({seekers:c}))}),{name:"user",storage:(0,a.FL)(()=>l)}))}},function(e){e.O(0,[6990,8310,6290,8094,2586,2957,4956,3448,8658,6088,9623,3398,6205,4216,3682,3145,6379,2545,2971,2117,1744],function(){return e(e.s=14528)}),_N_E=e.O()}]);