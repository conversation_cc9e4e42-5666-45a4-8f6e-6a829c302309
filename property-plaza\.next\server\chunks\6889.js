"use strict";exports.id=6889,exports.ids=[6889],exports.modules={6889:(e,t,r)=>{r.r(t),r.d(t,{default:()=>u});var s=r(72051),a=r(45347);let o=(0,a.createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\components\subscribe\subscribe-dialog.tsx#default`),i=(0,a.createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\app\[locale]\(user)\(auth)\seekers-auth-dialog.tsx#default`);function u(){return(0,s.jsxs)(s.<PERSON>ag<PERSON>,{children:[s.jsx(i,{customTrigger:s.jsx("button",{type:"button",id:"auth-id",className:"hidden"})}),s.jsx(o,{trigger:s.jsx("button",{type:"button",id:"subscription-button-id",className:"hidden"})})]})}}};