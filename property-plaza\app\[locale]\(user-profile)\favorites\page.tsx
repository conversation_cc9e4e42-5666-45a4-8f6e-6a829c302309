import FavoriteBreadCrumb from "./bread-crumb";
import { Metadata } from "next";
import { getLocale, getTranslations } from "next-intl/server";
import FavoriteListingContent from "./content";
import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import FilterHeader from "../../(user)/s/filter-header";
import { BaseProps } from "@/types/base";
import { getCurrencyConversion } from "@/core/infrastructures/currency-converter/api";
import { filterTitles } from "@/lib/constanta/constant";
import { favoriteUrl } from "@/lib/constanta/route";
import { routing } from "@/lib/locale/routing";


export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations("seeker")
  const locale = await getLocale() || routing.defaultLocale
  const baseUrl = process.env.USER_DOMAIN || "https://www.property-plaza.com/"
  return {
    title: t("metadata.favorite.title"),
    description: t("metadata.favorite.description"),
    alternates: {
      canonical: baseUrl + locale + favoriteUrl,
      languages: {
        en: baseUrl + "en" + favoriteUrl,
        id: baseUrl + "id" + favoriteUrl,
        "x-default": baseUrl + favoriteUrl.replace("/", ""),
      }
    },
    openGraph: {
      title: t("metadata.favorite.title"),
      description: t("metadata.favorite.description"),
      images: [
        {
          url: baseUrl + "og.png",
          width: 1200,
          height: 630,
          alt: "Property Plaza",
        }
      ],
      type: "website",
      url: baseUrl + locale + favoriteUrl,
      countryName: "Indonesia",
      emails: "<EMAIL>",
      locale: locale,
      alternateLocale: routing.locales,
      siteName: "Property plaza"
    },
    applicationName: "Property plaza",
    twitter: {
      card: "summary_large_image",
      title: t("metadata.favorite.title"),
      description: t("metadata.favorite.description"),
      images: [baseUrl + "og.jpg"],
    },

    robots: {
      index: false,
      follow: true,
      nocache: false,
    },

  }
}




export default async function FavoritePage({ params, searchParams }: BaseProps) {
  const t = await getTranslations("seeker")
  const conversionRates = await getCurrencyConversion()
  return <>
    <FavoriteBreadCrumb />
    <MainContentLayout className="space-y-8 my-8 max-sm:px-0">
      <div className="flex max-sm:flex-col justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">{t("setting.favorites.savedItems.title")}</h1>
          <h2 className="text-muted-foreground mt-2">{t("setting.favorites.savedItems.description")}</h2>
        </div>
        <FilterHeader showFilter={false} conversions={conversionRates.data || []} />
      </div>
      <FavoriteListingContent
        page={searchParams.page as string}
        types="all"
        query="all"
        sortBy={searchParams.sortBy as string || undefined}
        conversions={conversionRates.data}
      />
    </MainContentLayout>

  </>
}