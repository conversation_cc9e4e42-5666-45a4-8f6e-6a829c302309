(()=>{var e={};e.id=831,e.ids=[831],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},50022:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>d,pages:()=>u,routeModule:()=>m,tree:()=>c}),r(63805),r(52250),r(7505),r(84448),r(81729),r(90996);var s=r(30170),a=r(45002),n=r(83876),i=r.n(n),l=r(66299),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let c=["",{children:["[locale]",{children:["(user)",{children:["contact-us",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,63805)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user)\\contact-us\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,52250)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,7505)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user)\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,80603))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,84448)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,81729)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,90996,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user)\\contact-us\\page.tsx"],d="/[locale]/(user)/contact-us/page",p={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/[locale]/(user)/contact-us/page",pathname:"/[locale]/contact-us",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},89449:(e,t,r)=>{let s={ace39bf07124e0ba39e8486050a53bc79b0621b3:()=>Promise.resolve().then(r.bind(r,18714)).then(e=>e.default)};async function a(e,...t){return(await s[e]()).apply(null,t)}e.exports={ace39bf07124e0ba39e8486050a53bc79b0621b3:a.bind(null,"ace39bf07124e0ba39e8486050a53bc79b0621b3")}},4092:(e,t,r)=>{Promise.resolve().then(r.bind(r,76540)),Promise.resolve().then(r.bind(r,26793)),Promise.resolve().then(r.bind(r,70697))},75588:(e,t,r)=>{Promise.resolve().then(r.bind(r,38819)),Promise.resolve().then(r.bind(r,81578)),Promise.resolve().then(r.bind(r,84059)),Promise.resolve().then(r.bind(r,78781)),Promise.resolve().then(r.bind(r,91860)),Promise.resolve().then(r.bind(r,33626)),Promise.resolve().then(r.bind(r,26793)),Promise.resolve().then(r.bind(r,70697)),Promise.resolve().then(r.bind(r,92941)),Promise.resolve().then(r.t.bind(r,15889,23)),Promise.resolve().then(r.bind(r,62648))},76540:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var s=r(97247),a=r(28964),n=r(84879),i=r(58053),l=r(44597),o=r(10906),c=r(84033);function u(){let e=(0,n.useTranslations)("ContactUs"),{toast:t}=(0,o.pm)(),[r,u]=(0,a.useState)({name:"",email:"",subject:"",message:""}),[d,p]=(0,a.useState)(!1),m=async r=>{r.preventDefault(),p(!0);try{await new Promise(e=>setTimeout(e,1e3)),t({description:e("messageSent")}),u({name:"",email:"",subject:"",message:""})}catch(e){t({variant:"destructive",description:"Er is iets misgegaan. Probeer het later opnieuw."})}finally{p(!1)}};return(0,s.jsxs)("div",{className:"w-full bg-white",children:[(0,s.jsxs)("section",{"aria-label":"Contact Hero",className:"relative w-full h-[300px]",children:[s.jsx(l.default,{src:c.default,alt:"Contact Property Plaza",fill:!0,className:"object-cover",priority:!0}),s.jsx("div",{className:"absolute inset-0 bg-black/40 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center px-4",children:[s.jsx("h1",{className:"text-4xl md:text-5xl font-bold text-white mb-4",children:e("title")}),s.jsx("p",{className:"text-xl text-white max-w-3xl mx-auto",children:e("subtitle")})]})})]}),s.jsx("div",{className:"max-w-7xl mx-auto px-4 py-12",children:s.jsx("div",{className:"max-w-3xl mx-auto",children:(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-8",children:[s.jsx("h2",{className:"text-2xl font-semibold mb-6",children:e("sendMessage")}),(0,s.jsxs)("form",{onSubmit:m,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[s.jsx("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:e("nameField")}),s.jsx("input",{type:"text",id:"name",name:"name",required:!0,className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-seekers-primary focus:border-transparent",value:r.name,onChange:e=>u(t=>({...t,name:e.target.value}))})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:e("emailField")}),s.jsx("input",{type:"email",id:"email",name:"email",required:!0,className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-seekers-primary focus:border-transparent",value:r.email,onChange:e=>u(t=>({...t,email:e.target.value}))})]})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{htmlFor:"subject",className:"block text-sm font-medium text-gray-700 mb-2",children:e("subjectField")}),s.jsx("input",{type:"text",id:"subject",name:"subject",required:!0,className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-seekers-primary focus:border-transparent",value:r.subject,onChange:e=>u(t=>({...t,subject:e.target.value}))})]}),(0,s.jsxs)("div",{children:[s.jsx("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-2",children:e("messageField")}),s.jsx("textarea",{id:"message",name:"message",rows:6,required:!0,className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-seekers-primary focus:border-transparent",value:r.message,onChange:e=>u(t=>({...t,message:e.target.value}))})]}),s.jsx("div",{children:s.jsx(i.z,{type:"submit",variant:"default-seekers",className:"w-full md:w-auto",disabled:d,children:d?"Verzenden...":e("submitButton")})})]})]})})})]})}},38819:(e,t,r)=>{"use strict";r.d(t,{default:()=>m});var s=r(97247),a=r(75476),n=r(55961),i=r(15238),l=r(50555),o=r(58053),c=r(84879);function u({open:e,setOpen:t,trigger:r}){let u=(0,c.useTranslations)("universal");return(0,s.jsxs)(l.Z,{open:e,setOpen:t,openTrigger:r,children:[s.jsx(i.Z,{children:s.jsx("h3",{className:"text-base font-bold text-seekers-text",children:u("popup.followInstagram.title")})}),s.jsx("div",{children:s.jsx("p",{children:u("popup.followInstagram.description")})}),s.jsx(n.Z,{children:s.jsx(o.z,{asChild:!0,className:"w-full",variant:"default-seekers",children:s.jsx(a.rU,{href:"https://www.instagram.com/join.propertyplaza/",children:u("cta.followUsOnInstagram")})})})]})}var d=r(92199),p=r(28964);function m(){let{successSignUp:e,setSuccessSignUp:t,loading:r}=(0,d.I)(),[a,n]=(0,p.useState)(!1),[i,l]=(0,p.useState)(!0);return s.jsx(s.Fragment,{children:s.jsx(u,{open:a,setOpen:e=>{t(e),n(e)},trigger:s.jsx(s.Fragment,{})})})}},81578:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var s=r(97247),a=r(23866),n=r(92894);function i(){let{setSeekers:e,setRole:t}=(0,n.L)(e=>e);return(0,a.l)(),s.jsx(s.Fragment,{})}r(28964)},63805:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,generateMetadata:()=>l});var s=r(72051),a=r(29507);let n=(0,r(45347).createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\app\[locale]\(user)\contact-us\contact-us-content.tsx#default`);var i=r(92898);async function l({params:{locale:e}}){let t=await (0,a.Z)({locale:e,namespace:"ContactUs"});return{title:t("pageTitle"),description:t("pageDescription"),alternates:{languages:{en:process.env.USER_DOMAIN+"/en"+i.bY,id:process.env.USER_DOMAIN+"/id"+i.bY}}}}function o(){return(0,s.jsxs)(s.Fragment,{children:[s.jsx(n,{}),";"]})}},52250:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(72051),a=r(81413),n=r(98798),i=r(56886);r(26269);var l=r(35254),o=r(52845);let c=(0,r(45347).createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\app\[locale]\(user)\pop-up.tsx#default`);var u=r(86677);async function d({children:e}){let t=await (0,o.cookies)(),r=t.get("seekers-settings")?.value||"",d=r?JSON.parse(r):void 0,p=t.get("NEXT_LOCALE")?.value;return(0,s.jsxs)(s.Fragment,{children:[s.jsx(u.Z,{isSeeker:!0}),s.jsx(l.Z,{}),s.jsx(n.Z,{}),s.jsx("div",{className:"w-full sticky top-0 z-10 bg-white !mt-0",children:s.jsx(i.Z,{currency_:d?.state?.currency,localeId:p})}),s.jsx("div",{className:"!mt-0 relative min-h-screen max-sm:max-w-screen-sm",children:e}),s.jsx("div",{className:"!mt-0",children:s.jsx(a.Z,{})}),s.jsx(c,{})]})}},7505:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(41288);function a(){(0,s.redirect)("/")}},35254:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});let s=(0,r(45347).createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\app\[locale]\(user)\setup-seekers.tsx#default`)},18714:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(28713);r(9640);var a=r(53020);async function n(e,t,r){let s=(0,a.cookies)(),n=s.get("tkn")?.value;try{let s=await fetch(e,{method:t,headers:{Authorization:`Bearer ${n}`,"Content-Type":"application/json"},...r});if(!s.ok)return{data:null,meta:void 0,error:{status:s.status,name:s.statusText,message:await s.text()||"Unexpected error",details:{}}};let a=await s.json();if(a.error)return{data:null,meta:void 0,error:a.error};return{data:a.data,meta:a.meta,error:void 0}}catch(e){return{data:null,meta:void 0,error:{status:500,details:{cause:e.cause},message:e.message,name:e.name}}}}(0,r(83557).h)([n]),(0,s.j)("ace39bf07124e0ba39e8486050a53bc79b0621b3",n)},29507:(e,t,r)=>{"use strict";r.d(t,{Z:()=>i});var s=r(26269),a=r(95817),n=r(60434),i=(0,s.cache)(async function(e){let t,r;"string"==typeof e?t=e:e&&(r=e.locale,t=e.namespace);let s=await (0,n.Z)(r);return(0,a.eX)({...s,namespace:t,messages:s.messages})})},84033:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s={src:"/_next/static/media/about-us-image.267ed102.webp",height:1279,width:1920,blurDataURL:"data:image/webp;base64,UklGRj4AAABXRUJQVlA4IDIAAACQAQCdASoIAAUAAkA4JZgAAudZtgAA/uYL+8l5/VVhaFlCK8//pnoGQ+Tg9E15n5nAAA==",blurWidth:8,blurHeight:5}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[9379,5063,4916,9467,4859,5268,3327,8530,6666,9965,595],()=>r(50022));module.exports=s})();