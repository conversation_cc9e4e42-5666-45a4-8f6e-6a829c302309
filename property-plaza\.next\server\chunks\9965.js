exports.id=9965,exports.ids=[9965],exports.modules={91401:(e,t,a)=>{var s={"./en.json":[96842,6842],"./id.json":[8201,8201],"./nl.json":[56945,6945]};function r(e){if(!a.o(s,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=s[e],r=t[0];return a.e(t[1]).then(()=>a.t(r,19))}r.keys=()=>Object.keys(s),r.id=91401,e.exports=r},28023:(e,t,a)=>{"use strict";a.d(t,{p:()=>i});var s=a(84879),r=a(47751);function i(){let e=(0,s.useTranslations)("seeker");return r.z.object({otp:r.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.otp")})}).min(5,{message:e("form.utility.enterValidField",{field:e("form.field.otp")})})})}},84244:(e,t,a)=>{"use strict";a.d(t,{E:()=>l,i:()=>n});var s=a(16718),r=a(84879),i=a(47751);let n=/^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[\W_])[A-Za-z\d\W_]{8,}$/;function l(){let e=(0,r.useTranslations)("seeker");return i.z.object({firstName:i.z.string().min(s.nM,{message:e("form.utility.minimumLength",{field:e("form.field.firstName"),length:s.nM})}).max(s.ac,{message:e("form.utility.maximumLength",{field:e("form.field.firstName"),length:s.ac})}),lastName:i.z.string().min(s.nM,{message:e("form.utility.minimumLength",{field:e("form.field.lastName"),length:s.nM})}).max(s.ac,{message:e("form.utility.maximumLength",{field:e("form.field.lastName"),length:s.ac})}),contact:i.z.string().email({message:e("form.utility.enterValidField",{field:` ${e("form.field.email")}`})}),password:i.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.password")})}),confirmPassword:i.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.confirmPassword")})})}).refine(e=>e.password==e.confirmPassword,{message:e("form.utility.fieldNotMatch",{field:`${e("form.field.password")}`}),path:["confirmPassword"]})}},98056:(e,t,a)=>{"use strict";a.d(t,{default:()=>Q});var s=a(97247),r=a(50555),i=a(84879),n=a(28964),l=a(93137),o=a(15238),c=a(34631),d=a(52208),u=a(2704),m=a(4955),p=a(58053),f=a(16718),h=a(47751),x=a(88111),g=a(6649),A=a(10906),y=a(72266),w=a(54033),v=a(83482),N=a(28050),j=a(25008),b=a(82328),k=a(53803),C=a(74290);function S(){let e=(0,x.D)({mutationFn:async()=>{window.location.href="https://dev.property-plaza.id/api/v1/auth/google?origin=DEFAULT"}}),t=(0,x.D)({mutationFn:async()=>{window.location.href="https://dev.property-plaza.id/api/v1/auth/facebook?origin=DEFAULT"}}),a=async()=>{await e.mutateAsync()},r=async()=>{await t.mutateAsync()};return(0,s.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,s.jsxs)(p.z,{className:"inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border bg-background shadow-sm hover:text-accent-foreground h-9 px-4 py-2 w-full rounded-xl border-gray-200 hover:bg-gray-50",variant:"outline",onClick:a,type:"button",children:[s.jsx(C.JM8,{className:"mr-2 h-4 w-4"}),"Google"]}),(0,s.jsxs)(p.z,{className:"inline-flex w-full items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border bg-background shadow-sm hover:text-accent-foreground h-9 px-4 py-2 rounded-xl border-gray-200 hover:bg-gray-50",variant:"outline",onClick:r,type:"button",children:[s.jsx(k.Am9,{className:"mr-2 h-4 w-4"}),"Facebook"]})]})}var E=a(33626);function R({isDialog:e,onClickSignUp:t,onClickResetPassword:a}){let r=(0,i.useTranslations)("seeker"),n=function(){let e=(0,i.useTranslations)("seeker");return h.z.object({contact:h.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.email")})}).email({message:e("form.utility.enterValidField",{field:` ${e("form.field.email")}`})}),password:h.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.password")})}).min(f.Z9,{message:e("form.utility.minimumLength",{field:e("form.field.password"),length:f.Z9})})})}(),l=function(e="seekers"){let t=(0,i.useTranslations)("universal"),{toast:a}=(0,A.pm)(),{executeRecaptcha:s}=(0,N.CL)();return(0,x.D)({mutationFn:async e=>{try{let t=await s("form_submit");return(0,g.x4)(e,t)}catch(e){return console.log(e),{data:null}}},onSuccess:async a=>{let s=a.data,r=await (0,v.O4)({headers:{Authorization:`Bearer ${s.data.access_token}`}}),i=r.type;if(!r)throw Error(t("misc.userNotFound"));if("owner"===e||"middleman"==e){if("SEEKER"==i)throw Error(t("misc.userNotFound"));if(y.Z.set(f.LA,s.data.access_token,{expires:7}),"OWNER"==i)return window.location.assign(w.Kc);if("MIDDLEMAN"==i)return window.location.assign(w.ej)}else{if("OWNER"==r.type||"MIDDLEMAN"==r.type)throw Error(t("misc.userNotFound"));y.Z.set(f.LA,s.data.access_token,{expires:7}),window.location.reload()}},onError:e=>{let s=e.response?.data;y.Z.remove(f.LA),a({title:t("misc.foundError"),description:s?.message||"",variant:"destructive"})}})}("seekers"),{toast:o}=(0,A.pm)(),k=(0,u.cI)({resolver:(0,c.F)(n),defaultValues:{contact:"",password:""}});async function C(e){let t=(0,j.E6)(e.contact.replaceAll(/\s+/g,"")),a={username:e.contact.trim(),password:e.password,login_with:t?"DEFAULT":"PHONE_NUMBER"};try{await l.mutateAsync(a)}catch(e){o({title:r("error.failedLogin.title"),description:e?.response?.data.message||"",variant:"destructive"})}}return s.jsx("div",{className:"grid gap-4",children:s.jsx(d.l0,{...k,children:(0,s.jsxs)("form",{onSubmit:k.handleSubmit(C),className:"grid gap-4",children:[(0,s.jsxs)("div",{className:"grid gap-4",children:[s.jsx(m.Z,{form:k,label:r("form.label.email"),name:"contact",placeholder:"",type:"email",variant:"float",labelClassName:"text-xs text-seekers-text-light font-normal"}),s.jsx(b.Z,{form:k,label:r("form.label.password"),name:"password",placeholder:"",variant:"float",labelClassName:"text-xs text-seekers-text-light font-normal"}),(0,s.jsxs)("div",{className:"text-xs text-neutral space-x-1 -mt-5",children:[s.jsx("span",{className:"ml-3",children:r("form.utility.forgotField",{field:r("form.field.password")})}),s.jsx(p.z,{variant:"link",type:"button",onClick:a,className:"p-0 text-seekers-primary font-medium hover:underline text-xs",children:r("form.utility.resetField",{field:r("form.field.password")})})]})]}),s.jsx(p.z,{className:"w-full",variant:"default-seekers",loading:l.isPending,children:r("cta.login")}),s.jsx("div",{className:"mt-4 text-center",children:(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:[r("auth.createAccount")," ",s.jsx(p.z,{variant:"link",onClick:t,className:"p-0 h-9 text-seekers-primary hover:underline",children:r("cta.createAccount")})]})}),(0,s.jsxs)("div",{className:"relative my-6",children:[s.jsx(E.Separator,{}),(0,s.jsxs)("span",{className:"absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 bg-background px-2 text-sm text-muted-foreground",children:[r("conjuntion.or")," ",r("misc.continueWith")]})]}),s.jsx(S,{})]})})})}var Z=a(84244),_=a(92199),z=a(27727),T=a(34523),F=a.n(T),U=a(48799),P=a(37013);let L=({onClickLogin:e,onSuccess:t})=>{let a=(0,i.useTranslations)("seeker"),r=(0,Z.E)(),{setRegister:l,setValidFormUntil:o,register:h}=(0,_.I)(),[x,g]=(0,n.useState)({length:!1,number:!1,special:!1,notCommon:!0,uppercase:!1}),{toast:y}=(0,A.pm)(),w=(0,z.X)(e=>{e?.response?.data?.message!=="Email verification code is already sent. Please check your email"&&y({title:a("success.sendVerification.title")+" "+h.email}),t()}),v=(0,u.cI)({resolver:(0,c.F)(r),defaultValues:{confirmPassword:h.confirm_password||"",contact:h.email||"",firstName:h.first_name||"",lastName:h.last_name||"",password:h.password||""}}),N=v.watch("password");async function k(e){let t=F()().add(30,"minutes"),s={email:e.contact||"",password:e.password,confirm_password:e.confirmPassword,first_name:e.firstName,last_name:e.lastName,type:f.gr,otp:"00000"};l(s),o(t);try{await w.mutateAsync({email:s.email,category:"REGISTRATION"})}catch(e){if(e?.response?.data?.message=="Email verification code is already sent. Please check your email")return;y({title:a("message.otpRequest.failedToast.title"),description:e?.response?.data?.message||"",variant:"destructive"})}}return(0,n.useEffect)(()=>{N&&g({length:N.length>=8,number:/[0-9]/.test(N),special:/[!@#$%^&*()_+]/.test(N),notCommon:!["123456","password","qwerty"].includes(N.toLowerCase()),uppercase:/[A-Z]/.test(N),lowercase:/[a-z]/.test(N)})},[N]),s.jsx(d.l0,{...v,children:(0,s.jsxs)("form",{onSubmit:v.handleSubmit(k),className:"grid gap-4",children:[(0,s.jsxs)("div",{className:"grid gap-4",children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[s.jsx(m.Z,{form:v,label:a("form.label.firstName"),name:"firstName",placeholder:"",type:"text",variant:"float",labelClassName:"text-xs text-seekers-text-light font-normal"}),s.jsx(m.Z,{form:v,label:a("form.label.lastName"),name:"lastName",placeholder:"",type:"text",variant:"float",labelClassName:"text-xs text-seekers-text-light font-normal"})]}),s.jsx(m.Z,{form:v,label:a("form.label.email"),name:"contact",placeholder:"",type:"email",variant:"float",labelClassName:"text-xs text-seekers-text-light font-normal"}),(0,s.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[s.jsx(b.Z,{form:v,name:"password",variant:"float",label:a("form.label.password"),placeholder:"",inputProps:{required:!0},labelClassName:"text-xs text-seekers-text-light font-normal"}),s.jsx(b.Z,{form:v,name:"confirmPassword",variant:"float",label:a("form.label.confirmPassword"),placeholder:"",inputProps:{required:!0},labelClassName:"text-xs text-seekers-text-light font-normal"})]}),N&&(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-xs",children:[(0,s.jsxs)("div",{className:(0,j.cn)(x.length?"text-green-500":"text-red-500"),children:[x.length?s.jsx(U.Z,{className:"inline w-3 h-3 mr-1"}):s.jsx(P.Z,{className:"inline w-3 h-3 mr-1"}),a("form.utility.password.minimumLength")]}),(0,s.jsxs)("div",{className:(0,j.cn)(x.number?"text-green-500":"text-red-500"),children:[x.number?s.jsx(U.Z,{className:"inline w-3 h-3 mr-1"}):s.jsx(P.Z,{className:"inline w-3 h-3 mr-1"}),a("form.utility.password.numberRequired")]}),(0,s.jsxs)("div",{className:(0,j.cn)(x.special?"text-green-500":"text-red-500"),children:[x.special?s.jsx(U.Z,{className:"inline w-3 h-3 mr-1"}):s.jsx(P.Z,{className:"inline w-3 h-3 mr-1"}),a("form.utility.password.specialCharacter")]}),(0,s.jsxs)("div",{className:(0,j.cn)(x.notCommon?"text-green-500":"text-red-500"),children:[x.notCommon?s.jsx(U.Z,{className:"inline w-3 h-3 mr-1"}):s.jsx(P.Z,{className:"inline w-3 h-3 mr-1"}),a("form.utility.password.notCommonWord")]}),(0,s.jsxs)("div",{className:(0,j.cn)(x.uppercase?"text-green-500":"text-red-500"),children:[x.uppercase?s.jsx(U.Z,{className:"inline w-3 h-3 mr-1"}):s.jsx(P.Z,{className:"inline w-3 h-3 mr-1"}),a("form.utility.password.uppercaseRequired")]}),(0,s.jsxs)("div",{className:(0,j.cn)(x.lowercase?"text-green-500":"text-red-500"),children:[x.lowercase?s.jsx(U.Z,{className:"inline w-3 h-3 mr-1"}):s.jsx(P.Z,{className:"inline w-3 h-3 mr-1"}),a("form.utility.password.lowercaseRequired")]})]})]}),s.jsx(p.z,{className:"w-full",variant:"default-seekers",loading:w.isPending,children:a("cta.createAccount")}),s.jsx("div",{className:"mt-4 text-center",children:(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:[a("auth.alreadyHaveAccount")," ",s.jsx(p.z,{variant:"link",onClick:e,className:"p-0 h-9 text-seekers-primary hover:underline",children:a("cta.login")})]})}),(0,s.jsxs)("div",{className:"relative my-6",children:[s.jsx(E.Separator,{}),s.jsx("span",{className:"absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 bg-background px-2 text-sm text-muted-foreground",children:a("conjuntion.or")})]}),s.jsx(S,{})]})})};var B=a(30938),D=a(12961),I=a(43189),O=a(73028),V=a(93572),q=a(69693),J=a(28023);function M(){let{register:e,reset:t,setSuccessSignUp:a}=(0,_.I)(),{toast:r}=(0,A.pm)(),l=(0,i.useTranslations)("seeker"),o=(0,J.p)(),m=function(e,t="owner"){let{toast:a}=(0,A.pm)(),s=(0,i.useTranslations)("universal"),{executeRecaptcha:r}=(0,N.CL)();return(0,x.D)({mutationFn:async e=>{try{let t=await r("form_submit");return(0,I.a$)(e,t)}catch(e){return console.log(e),{data:null}}},onSuccess:a=>{let s=a.data;y.Z.set(f.LA,s.data.access_token,{expires:7}),e?.(),"owner"==t?window.location.assign(w.Kc):window.location.reload()},onError:e=>{let t=e.response.data;a({title:s("misc.foundError"),description:t.message,variant:"destructive"})}})}(()=>t(),"seekers"),h=(0,O.G)(async()=>{try{await m.mutateAsync({...e,otp:v.getValues("otp"),register_with:"EMAIL"}),a(!0)}catch(e){}}),g=(0,z.X)(t=>{if("Email verification code is already sent. Please check your email"===t.response.data.message){r({title:l("message.otpRequest.failedToast.title"),description:t.response.data.message||"",variant:"destructive"});return}r({title:l("success.sendVerification.title")+" "+e.email})}),v=(0,u.cI)({resolver:(0,c.F)(o),defaultValues:{otp:""}});async function j(t){let a={otp:t.otp,requested_by:e.email||"",type:"EMAIL"};try{await h.mutateAsync(a)}catch(e){r({title:l("error.signUp.title"),description:e.response.data.message,variant:"destructive"})}}async function b(){g.mutate({email:e.email,category:"REGISTRATION"})}return(0,n.useEffect)(()=>{let e=v.getValues("otp").length,t=document.getElementById("otp-button");e>=5&&t?.click()},[v.getValues("otp")]),s.jsx(d.l0,{...v,children:(0,s.jsxs)("form",{onSubmit:v.handleSubmit(j),className:"space-y-8",children:[s.jsx(d.Wi,{control:v.control,name:"otp",render:({field:e})=>s.jsx("div",{className:"flex justify-center",children:s.jsx(V.Z,{label:"",children:s.jsx(q.Zn,{maxLength:5,...e,pattern:D.Ww,required:!0,containerClassName:"flex justify-center max-sm:justify-between",children:s.jsx(q.hf,{children:Array.from({length:5},(e,t)=>s.jsx(q.cY,{index:t,className:"w-16 h-20 text-2xl"},t))})})})})}),(0,s.jsxs)("div",{className:"space-y-2 flex flex-col items-center",children:[(0,s.jsxs)(p.z,{id:"otp-button",className:"w-full",variant:"default-seekers",loading:h.isPending,children:[l("cta.verify")," ",l("user.account")]}),s.jsx("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),b()},className:"mx-auto text-xs text-seekers-text-light",children:l("otp.resendVerificationCode")})]})]})})}var H=a(37129),G=a(84262);function $({onBack:e}){let t=(0,i.useTranslations)("universal"),{toast:a}=(0,A.pm)(),r=(0,G.t)(),n=(0,H.N)(),l=(0,u.cI)({resolver:(0,c.F)(r),defaultValues:{email:""}});async function o(s){let r={email:s.email};try{await n.mutateAsync(r),a({title:t("success.requestForgotPassword.title"),description:t("success.requestForgotPassword.description")}),e()}catch(e){a({title:t("error.requestForgetPassword.title"),description:e.response.data.message,variant:"destructive"})}}return s.jsx(d.l0,{...l,children:(0,s.jsxs)("form",{onSubmit:l.handleSubmit(o),className:"grid gap-4 ",children:[s.jsx(m.Z,{form:l,label:t("form.label.email"),name:"email",placeholder:"",type:"email",variant:"float",labelClassName:"text-xs text-seekers-text-light"}),s.jsx(p.z,{className:"w-full",variant:"default-seekers",loading:n.isPending,children:t("cta.requestChangePassword")}),s.jsx("div",{className:"mt-4 text-center",children:s.jsx(p.z,{variant:"link",onClick:e,className:"p-0 h-9 text-seekers-primary hover:underline",children:t("cta.goBack")})})]})})}let Y={signUp:"SIGN_UP",login:"LOGIN",otp:"OTP",resetPassword:"RESET_PASSWORD"};function Q({triggerClassName:e,customTrigger:t}){let a=(0,i.useTranslations)("seeker"),[c,d]=(0,n.useState)(!1),[u,m]=(0,n.useState)(""),[f,h]=(0,n.useState)(Y.signUp);return(0,s.jsxs)(r.Z,{open:c,setOpen:d,openTrigger:t||s.jsx("button",{className:`border relative border-seekers-text-lighter shadow-md rounded-full h-10 w-14 !bg-seekers-text-light ${e}`,children:s.jsx(l.Z,{url:""})}),dialogClassName:"w-full sm:max-w-[500px] p-6",children:[(0,s.jsxs)(o.Z,{className:"flex flex-col space-y-1.5 text-center mb-6",children:[(f==Y.otp||f==Y.resetPassword)&&s.jsx(p.z,{variant:"ghost",size:"icon",className:"absolute top-4 left-4",onClick:()=>h(f==Y.otp?Y.signUp:Y.login),children:s.jsx(B.Z,{className:"h-4 w-4"})}),(0,s.jsxs)("section",{className:"space-y-1.5",children:[s.jsx("h2",{className:"tracking-tight text-center text-2xl font-bold max-w-xs mx-auto",children:u}),s.jsx("p",{className:(0,j.cn)(f===Y.otp?"hidden":"","text-sm text-muted-foreground text-center"),children:f===Y.login?a("auth.login.subtitle"):f===Y.signUp?a("auth.register.subtitle"):f===Y.resetPassword?a("auth.resetPassword.subtitle"):""})]})]}),f==Y.login?s.jsx(R,{onClickSignUp:()=>h(Y.signUp),onClickResetPassword:()=>h(Y.resetPassword)}):f==Y.signUp?s.jsx(L,{onSuccess:()=>h(Y.otp),onClickLogin:()=>h(Y.login)}):f==Y.otp?(0,s.jsxs)("section",{children:[f===Y.otp&&(0,s.jsxs)("div",{className:"text-seekers-text-light",children:[s.jsx("p",{children:a("auth.otp.content.title")}),(0,s.jsxs)("ul",{className:"list-disc list-inside",children:[s.jsx("li",{children:a("auth.otp.item.one")}),s.jsx("li",{children:a("auth.otp.item.two")}),s.jsx("li",{children:a("auth.otp.item.three")})]}),s.jsx("p",{children:a("auth.otp.content.cantFindEmail")})]}),s.jsx(M,{})]}):f==Y.resetPassword?s.jsx($,{onBack:()=>h(Y.login)}):null]})}},17328:(e,t,a)=>{"use strict";a.d(t,{Z:()=>x});var s=a(97247),r=a(79984),i=a(25008),n=a(39623),l=a(36593),o=a(56460),c=a(93587),d=a(80032),u=a(12036),m=a(54636),p=a(25470),f=a(83001),h=a(74974);function x({category:e,className:t}){switch(e){case r.yJ.villa:case r.yJ.villas:return s.jsx(n.Z,{className:(0,i.cn)("!w-6 !h-6",t)});case r.yJ.apartment:return s.jsx(l.Z,{className:(0,i.cn)("!w-6 !h-6",t)});case r.yJ.homestay:case r.yJ.guestHouse:case r.yJ.rooms:return s.jsx(o.Z,{className:(0,i.cn)("!w-6 !h-6",t)});case r.yJ.ruko:case r.yJ.commercialSpace:return s.jsx(c.Z,{className:(0,i.cn)("!w-6 !h-6",t)});case r.yJ.cafeOrRestaurants:return s.jsx(d.Z,{className:(0,i.cn)("!w-6 !h-6",t)});case r.yJ.offices:return s.jsx(u.Z,{className:(0,i.cn)("!w-6 !h-6",t)});case r.yJ.shops:return s.jsx(m.Z,{className:(0,i.cn)("!w-6 !h-6",t)});case r.yJ.shellAndCore:return s.jsx(p.Z,{className:(0,i.cn)("!w-6 !h-6",t)});case r.yJ.lands:return s.jsx(f.Z,{className:(0,i.cn)("!w-6 !h-6",t)});default:return s.jsx(h.Z,{className:(0,i.cn)("!w-6 !h-6",t)})}}},84262:(e,t,a)=>{"use strict";a.d(t,{t:()=>i});var s=a(84879),r=a(47751);function i(){let e=(0,s.useTranslations)("universal");return r.z.object({email:r.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.email")})}).email()})}},55961:(e,t,a)=>{"use strict";a.d(t,{Z:()=>o});var s=a(97247),r=a(93009),i=a(98969),n=a(27387),l=a(25008);function o({children:e,className:t}){return(0,r.a)("(min-width:1024px)")?s.jsx(i.cN,{className:(0,l.cn)("px-0",t),children:e}):s.jsx(n.ze,{className:(0,l.cn)("px-0",t),children:e})}},15238:(e,t,a)=>{"use strict";a.d(t,{Z:()=>l});var s=a(97247),r=a(93009),i=a(98969),n=a(27387);function l({children:e,className:t}){return(0,r.a)("(min-width:1024px)")?s.jsx(i.fK,{className:t,children:e}):s.jsx(n.OX,{className:t,children:e})}},81441:(e,t,a)=>{"use strict";a.d(t,{Z:()=>l});var s=a(97247),r=a(93009),i=a(27387),n=a(98969);function l({children:e,className:t}){return(0,r.a)("(min-width:1024px)")?s.jsx(n.$N,{className:t,children:e}):s.jsx(i.iI,{className:t,children:e})}},50555:(e,t,a)=>{"use strict";a.d(t,{Z:()=>o});var s=a(97247),r=a(98969),i=a(93009),n=a(25008);a(28964);var l=a(27387);function o({children:e,openTrigger:t,open:a,setOpen:o,dialogClassName:c,drawerClassName:d,dialogOverlayClassName:u}){return(0,i.a)("(min-width:1024px)")?(0,s.jsxs)(r.Vq,{open:a,onOpenChange:o,children:[s.jsx(r.hg,{asChild:!0,children:t}),(0,s.jsxs)(r.PK,{children:[s.jsx(r.t9,{className:u}),s.jsx(r.cZ,{className:(0,n.cn)("max-sm:w-screen md:w-[450px]  md:min-w-[450px] max-sm:h-screen flex flex-col justify-start",c),children:e})]})]}):(0,s.jsxs)(l.dy,{open:a,onOpenChange:o,children:[s.jsx(l.Qz,{asChild:!0,children:t}),s.jsx(l.sc,{children:s.jsx("div",{className:(0,n.cn)("p-4 overflow-auto",d),children:e})})]})}},93572:(e,t,a)=>{"use strict";a.d(t,{Z:()=>n});var s=a(97247),r=a(52208),i=a(25008);function n({children:e,description:t,label:a,containerClassName:n,labelClassName:l,variant:o="default"}){return(0,s.jsxs)("div",{className:"w-full",children:[(0,s.jsxs)(r.xJ,{className:(0,i.cn)("w-full relative","float"==o?"border rounded-xl focus-within:border-neutral-light px-4 py-1 !space-y-0 focus-visible:outline-none focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 disabled:cursor-not-allowed":"",n),onClick:e=>e.stopPropagation(),children:[a&&s.jsx(r.lX,{className:l,children:a}),s.jsx(r.NI,{className:"group relative w-full",children:e}),t&&s.jsx(r.pf,{children:t}),"default"==o&&s.jsx(r.zG,{})]}),"float"==o&&s.jsx(r.zG,{})]})}},4955:(e,t,a)=>{"use strict";a.d(t,{Z:()=>o});var s=a(97247),r=a(52208),i=a(70170),n=a(93572),l=a(25008);function o({form:e,label:t,name:a,placeholder:o,description:c,type:d,inputProps:u,children:m,labelClassName:p,containerClassName:f,inputContainer:h,variant:x="default"}){return s.jsx(r.Wi,{control:e.control,name:a,render:({field:e})=>s.jsx(n.Z,{label:t,description:c,labelClassName:(0,l.cn)("float"==x?"absolute -top-2 left-2 px-1 text-xs bg-background z-10":"",p),containerClassName:f,variant:x,children:(0,s.jsxs)("div",{className:(0,l.cn)("flex gap-2 w-full overflow-hidden","float"==x?"":"border rounded-sm focus-within:border-neutral-light",h),children:[s.jsx(i.I,{type:d,placeholder:o,...e,...u,className:(0,l.cn)("border-none focus:outline-none shadow-none focus-visible:ring-0 w-full","float"==x?"px-0":"",u?.className)}),m]})})})}},82328:(e,t,a)=>{"use strict";a.d(t,{Z:()=>m});var s=a(97247),r=a(52208),i=a(70170),n=a(93572),l=a(28964),o=a(58053),c=a(58406),d=a(70457),u=a(25008);function m({form:e,label:t,name:a,placeholder:m,description:p,inputProps:f,labelClassName:h,containerClassName:x,inputContainer:g,variant:A="default"}){let[y,w]=(0,l.useState)(!1);return s.jsx(r.Wi,{control:e.control,name:a,render:({field:e})=>s.jsx(n.Z,{label:t,description:p,labelClassName:(0,u.cn)("float"==A?"absolute -top-2 left-2 px-1 text-xs bg-background z-10":"",h),containerClassName:x,variant:A,children:(0,s.jsxs)("div",{className:(0,u.cn)("flex gap-2 w-full overflow-hidden","float"==A?"":"border rounded-sm focus-within:border-neutral-light",g),children:[s.jsx(i.I,{type:y?"text":"password",placeholder:m,...e,...f,className:(0,u.cn)("border-none focus:outline-none shadow-none focus-visible:ring-0 w-full","float"==A?"px-0":"",f?.className)}),s.jsx(o.z,{size:"icon",variant:"ghost",className:"bg-transparent hover:bg-transparent text-seekers-text-light",type:"button",onClick:e=>{e.preventDefault(),e.stopPropagation(),w(e=>!e)},children:y?s.jsx(c.Z,{className:"w-4 h-4"}):s.jsx(d.Z,{className:"w-4 h-4"})})]})})})}},78781:(e,t,a)=>{"use strict";a.d(t,{default:()=>ek});var s=a(97247),r=a(40708),i=a(22288),n=a(44597),l=a(62648),o=a(58053),c=a(49256),d=a(6683),u=a(28964),m=a(97078),p=a(165),f=a(79935),h=a(25008),x=a(94049),g=a(98563);let A=[{id:"1",content:"IDR",value:"IDR"},{id:"2",content:"EUR",value:"EUR"},{id:"3",content:"GBP",value:"GBP"},{id:"4",content:"AUD",value:"AUD"},{id:"5",content:"USD",value:"USD"}],y=(0,u.forwardRef)(({triggerClassName:e,showCaret:t=!1,defaultCurrency:a="IDR",onClick:r},i)=>{let{currency:n,setCurrency:l,isLoading:o}=(0,g.R)(),[c,d]=(0,u.useState)(a),[m,p]=(0,u.useState)(!1);return(0,u.useEffect)(()=>{if(o)return d(a);d(n)},[n,o,a]),s.jsx("div",{className:"max-sm:w-fit w-full",children:(0,s.jsxs)(x.Ph,{defaultValue:a,value:c,onValueChange:l,open:m,onOpenChange:e=>{p(e),e&&r&&r(new MouseEvent("click",{bubbles:!0,cancelable:!0}))},children:[s.jsx(x.i4,{ref:i,showCaret:t,className:`rounded-full border flex items-center justify-center border-seekers-text-lighter shadow-md h-10 px-2 !w-full ${e}`,onClick:e=>{e.stopPropagation(),r?.(e)},children:s.jsx(x.ki,{className:"text-xs text-center"})}),s.jsx(x.Bw,{children:A.map(e=>s.jsx(x.Ql,{value:e.value,children:e.content},e.id))})]})})});y.displayName="CurrencyForm";var w=a(93269);function v({code:e}){let t=w[e];return s.jsx(t,{className:"border border-colortext-foreground rounded-full w-4 h-4 aspect-square my-auto"})}var N=a(84879),j=a(12392),b=a(59948),k=a(75476);(0,b.cF)(async({requestLocale:e})=>{let t=await e;return{locale:await e,messages:(await a(91401)(`./${t}.json`)).default,defaultLocale:k.DI.defaultLocale,locales:k.DI.locales}});let{Link:C,redirect:S,usePathname:E,useRouter:R}=(0,j.os)({defaultLocale:"en",locales:["en","id"],pathnames:{"/":"/","/pathnames":{en:"/pathnames",de:"/pfadnamen",id:"/nama-jalur"}},localePrefix:"as-needed"});var Z=a(40896);let _=(0,u.forwardRef)((e,t)=>{let{triggerClassName:a,showCaret:r=!1,defaultValue:i="en",onClick:n}=e,{changeLanguage:l,locale:o}=function(){let e=(0,N.useLocale)(),t=R(),a=E(),{generateQueryString:s}=(0,Z.Z)();return{changeLanguage:e=>{let r=s("","");(0,u.startTransition)(()=>{t.replace(a+"?"+r,{locale:e}),t.refresh()})},locale:e}}(),c=[{id:"2",content:s.jsx(v,{code:"US"}),value:"EN"},{id:"1",content:s.jsx(v,{code:"ID"}),value:"ID"}],[d,m]=(0,u.useState)(!1);return s.jsx("div",{className:"max-sm:w-fit w-full",onClick:e=>e.stopPropagation(),children:(0,s.jsxs)(x.Ph,{defaultValue:i,onValueChange:l,value:o.toUpperCase(),open:d,onOpenChange:e=>{m(e),e&&n&&n(new MouseEvent("click",{bubbles:!0,cancelable:!0}))},children:[s.jsx(x.i4,{ref:t,showCaret:r,className:`rounded-full border border-seekers-text-lighter shadow-md h-10 w-14 px-2 flex items-center justify-center ${a}`,onClick:e=>{e.stopPropagation(),n?.(e)},children:s.jsx(x.ki,{className:"text-xs"})}),s.jsx(x.Bw,{children:c.map(e=>s.jsx(x.Ql,{className:"",value:e.value,children:e.content},e.id))})]})})});_.displayName="SeekersLocaleForm";var z=a(72266),T=a(16718),F=a(92894),U=a(6649),P=a(41755),L=a(88111),B=a(23866),D=a(50555),I=a(15238),O=a(98969),V=a(6047),q=a(93137),J=a(98056),M=a(54033);function H({localeId:e="EN",currency_:t="EUR"}){let a=(0,u.useRef)(null),r=(0,u.useRef)(null),i=(0,u.useRef)(null),n=(0,F.L)(e=>e.role),[l,o]=(0,u.useState)(!1),[c,d]=(0,u.useState)(0),[m,f]=(0,u.useState)(null);return(0,s.jsxs)("div",{ref:a,className:"flex gap-2 items-center w-[166px] justify-between overflow-hidden h-[52px]",children:[s.jsx("div",{className:"w-fit",children:s.jsx(p.E.div,{className:"overflow-hidden shadow-md rounded-full border border-seekers-text-lighter flex",initial:{width:"110px"},animate:{width:l?"166px":"112px"},transition:{duration:.1},children:(0,s.jsxs)("div",{className:"flex items-center justify-center gap-2 py-2 w-full",children:[s.jsx(y,{triggerClassName:(0,h.cn)("rounded-full border-none shadow-none !justify-between flex-grow !min-w-8 py-0 pr-0 !h-5 focus:ring-0",l?"w-full":"pl-3 max-w-[48px]"),defaultCurrency:t,ref:i,onClick:e=>{e.stopPropagation(),f("currency"),o(!0)},showCaret:l&&"currency"===m}),s.jsx("div",{className:"w-[2px] h-[24px] bg-seekers-text-lighter"}),s.jsx(_,{triggerClassName:(0,h.cn)("rounded-full flex-grow !justify-between !min-w-8 border-none shadow-none py-0 pl-0 !h-5 focus:ring-0",l?"w-full":"pl-2 max-w-[32px]"),defaultValue:e,ref:r,onClick:e=>{e.stopPropagation(),f("language"),o(!0)},showCaret:l&&"language"===m})]})})}),s.jsx(s.Fragment,{children:z.Z.get(T.LA)&&"SEEKER"==n?s.jsx(G,{trigger:s.jsx("button",{className:"border relative border-seekers-text-lighter shadow-md rounded-full overflow-hidden !h-10 !w-10",children:s.jsx(q.Z,{url:""})})}):s.jsx("div",{children:s.jsx(J.default,{triggerClassName:(0,h.cn)("!w-10 rounded-full overflow-hidden")})})})]})}function G({trigger:e}){let t=(0,N.useTranslations)("seeker"),a=()=>{let e=document.getElementById("open-logout-dialog");e?.click()};return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(V.h_,{modal:!1,children:[s.jsx(V.$F,{asChild:!0,children:e}),(0,s.jsxs)(V.AW,{align:"end",className:"!w-[256px]",children:[s.jsx(V.Xi,{asChild:!0,children:s.jsx(k.rU,{href:M.Fq,children:t("accountAndProfile.profile")})}),s.jsx(V.Xi,{asChild:!0,children:s.jsx(k.rU,{href:M.Y8,children:t("accountAndProfile.favorite")})}),s.jsx(V.Xi,{className:"w-full",asChild:!0,children:s.jsx(k.rU,{href:M.in,children:s.jsx("div",{className:"flex justify-between items-center w-full ",children:t("accountAndProfile.message")})})}),s.jsx(V.Xi,{onClick:e=>{e.preventDefault(),a()},children:t("accountAndProfile.logout.title")})]})]}),s.jsx($,{trigger:s.jsx("button",{id:"open-logout-dialog"})})]})}function $({trigger:e}){let[t,a]=(0,u.useState)(!1),r=function(e="owner"){let t=(0,P.NL)();return(0,L.D)({mutationFn:()=>(0,U.kS)(),onSuccess:()=>{z.Z.remove(T.LA),z.Z.remove("user"),t.invalidateQueries({queryKey:[B.J],refetchType:"none"}),window.location.assign("/")},onError:e=>{z.Z.remove(T.LA),z.Z.remove("user"),window.location.assign("/")}})}("seekers"),i=(0,N.useTranslations)("seeker");return(0,s.jsxs)(D.Z,{open:t,setOpen:a,openTrigger:e,dialogClassName:"max-w-md",children:[(0,s.jsxs)(I.Z,{className:"text-start px-0",children:[s.jsx("h2",{className:"max-sm:text-center font-semibold",children:i("accountAndProfile.logout.title")}),s.jsx("p",{className:"max-sm:text-center max-sm:mb-4",children:i("owner.accountAndProfile.logout.description")})]}),(0,s.jsxs)(O.cN,{children:[s.jsx(o.z,{variant:"default-seekers",loading:r.isPending,className:"min-w-20 max-sm:order-last",onClick:()=>a(!1),children:i("cta.cancel")}),s.jsx(o.z,{variant:"ghost",onClick:()=>{if(z.Z.get(T.LA))r.mutate();else{window.location.assign("");return}},loading:r.isPending,className:"min-w-20",children:i("cta.logout")})]})]})}var Y=a(96954);let Q=Y.fC;Y.xz;let K=Y.ee,W=u.forwardRef(({className:e,align:t="center",sideOffset:a=4,...r},i)=>s.jsx(Y.h_,{children:s.jsx(Y.VY,{ref:i,align:t,sideOffset:a,className:(0,h.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...r})}));W.displayName=Y.VY.displayName;var X=a(22394),ee=a(70170),et=a(37013),ea=a(59683),es=a(9190),er=a(15916),ei=a(67636),en=a(77940),el=a(48799),eo=a(9527);let ec={src:"/_next/static/media/canggu.84e6fbe6.png",height:512,width:512,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAG1BMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACUUeIgAAAACXRSTlMBCyAwTkBmWH4H4C9lAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGXRFWHRTb2Z0d2FyZQB3d3cuaW5rc2NhcGUub3Jnm+48GgAAADJJREFUeJwly7kNADAMw0Ba8rf/xIER1keAiOBSVhrwZE8B5e6yTuymdGZHpbOO8H3/fRYyAJhuEV+lAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},ed={src:"/_next/static/media/ubud.81668090.png",height:512,width:512,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAIVBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABt0UjBAAAAC3RSTlNrAVtEKhuADk09jhuwZCAAAAAJcEhZcwAADsQAAA7EAZUrDhsAAAAZdEVYdFNvZnR3YXJlAHd3dy5pbmtzY2FwZS5vcmeb7jwaAAAAMklEQVR4nAXBBwEAMAzDMCf94w94EiqPJDFOK2MoqsNxFNnYgQ43JNrkNWLtOD8I2AR9HbEA0czfdCsAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},eu={src:"/_next/static/media/nusa-dua.9acfd1fe.png",height:512,width:512,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAG1BMVEUAAAAAAAAAAABMaXEAAAAAAAAAAAAAAAAAAABReBoRAAAACXRSTlNsU34AN11hPRckhWnFAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAAGXRFWHRTb2Z0d2FyZQB3d3cuaW5rc2NhcGUub3Jnm+48GgAAADJJREFUeJwdyUESwDAMArElgJ3/v7jT6CpsJ7bxTZtrzGN0CkVsJYnFGWny18Ii5jzzARpnALRddCEHAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},em={src:"/_next/static/media/uluwatu.71df2404.png",height:512,width:512,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAHlBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAABMaXEAAAAAAAAKNf92AAAACnRSTlM5b3xVi2MwACERUm+ZFgAAAAlwSFlzAAAOxAAADsQBlSsOGwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAAzSURBVHicBcGHAQAwDMIwQwbp/w9XIhEoIde7fSGlXVV4nt3xAzW0oMwdLtQGt5jyjGs+Je8A7/g673gAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},ep={src:"/_next/static/media/seminyak.639fb2f5.png",height:512,width:512,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAIVBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABt0UjBAAAAC3RSTlMCQTd4Wx9LESZpobATef0AAAAJcEhZcwAADsQAAA7EAZUrDhsAAAAZdEVYdFNvZnR3YXJlAHd3dy5pbmtzY2FwZS5vcmeb7jwaAAAANUlEQVR4nBXJtxHAQBADsSXPq/+CNY8UgKcbQBHxAd7NiwKXIi6BsVKql26VYaTufVeZp/kBHFQA0r0TCjUAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8};function ef({locationName:e}){switch(e){case"canggu":return s.jsx(n.default,{src:ec,alt:"canggu",width:36,className:"aspect-square "});case"ubud":return s.jsx(n.default,{src:ed,alt:"ubud",width:32,className:"aspect-square "});case"seminyak":return s.jsx(n.default,{src:ep,alt:"Seminyak",width:32,className:"aspect-square"});case"uluwatu":return s.jsx(n.default,{src:em,alt:"uluwatu",width:32,className:"aspect-square "});case"Nusa Dua":return s.jsx(n.default,{src:eu,alt:"nusa dua",width:32,className:"aspect-square "});default:return s.jsx(s.Fragment,{})}}function eh({showContent:e}){let t=(0,N.useTranslations)("seeker"),{query:a}=(0,f.V)(e=>e),i=function(e){let{search:t}=e;return(0,es.a)({queryKey:["location-suggestion",t],queryFn:async()=>await (0,ea.IW)(e),retry:!1})}({search:(0,er.N)(a,500)}),{handleSetQuery:n,seekersSearch:l,banjars:o,showBanjars:c,selectedLocation:d,handleSelectLocation:u,handleBackToLocations:m,handleSetBanjar:p,filteredLocations:h,getMatchingBanjars:x}=(0,r.Z)();return(0,s.jsxs)(s.Fragment,{children:[c?s.jsx(s.Fragment,{children:(0,s.jsxs)("div",{className:"mt-4",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-4  px-3 max-sm:px-0",children:[s.jsx("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),m()},className:"text-seekers-text-light hover:text-seekers-text",children:s.jsx(en.Z,{className:"h-4 w-4"})}),s.jsx("span",{className:"font-medium capitalize",children:d})]}),s.jsx("div",{className:"grid grid-cols-2 gap-4 px-3 max-sm:px-0",children:o[d].map(e=>s.jsx("div",{className:"relative ",children:(0,s.jsxs)("button",{onClick:t=>{t.preventDefault(),t.stopPropagation(),p(e)},className:`w-full border border-gray-200 rounded-full py-3 px-4 flex items-center gap-3
                                      ${l.query.includes(e)?"bg-gray-50":"bg-white"}`,children:[s.jsx("div",{className:`w-4 h-4 rounded-full border flex items-center justify-center
                                      ${l.query.includes(e)?"border-seekers-primary bg-seekers-primary":"border-gray-300"}`,children:l.query.includes(e)&&s.jsx(el.Z,{className:"h-3 w-3 text-white"})}),s.jsx("span",{className:"text-sm text-seekers-text-light",children:e})]})},e))})]})}):s.jsx(s.Fragment,{children:h.length>0&&(0,s.jsxs)("div",{children:[s.jsx("h2",{children:t("misc.region")}),h.map(e=>{let t=e.name.replace(", Bali",""),a=o[e.value]?.length>0;return s.jsx("div",{children:(0,s.jsxs)("button",{className:"w-full flex items-center justify-between p-3 hover:bg-gray-100 transition-colors rounded-lg",onClick:t=>{t.preventDefault(),t.stopPropagation(),a?u(e.value):n(e.value)},children:[(0,s.jsxs)("div",{className:"flex items-center gap-3",children:[s.jsx(ef,{locationName:e.value}),(0,s.jsxs)("div",{className:"text-left",children:[s.jsx("div",{className:"font-medium",children:t}),s.jsx("div",{className:"text-sm text-gray-500",children:e.description})]})]}),a&&s.jsx(ei.Z,{className:"h-4 w-4 text-gray-400"})]})},e.value)})]})}),a.length>=3&&!c&&(h.some(e=>x(e.value).length>0)||(i.data?.data?.length||0)>0)&&(0,s.jsxs)(s.Fragment,{children:[s.jsx("div",{className:"text-xs text-gray-500 font-medium mt-4 mb-2 px-3",children:t("misc.areas")}),(0,s.jsxs)("div",{children:[h.map(e=>{let t=x(e.value);return 0===t.length?null:t.map(t=>(0,s.jsxs)("button",{className:"w-full flex items-center gap-3 p-3 hover:bg-gray-50 transition-colors rounded-lg",onClick:()=>{p(t,!0)},children:[s.jsx("div",{className:"w-8 h-8 flex items-center justify-center",children:s.jsx(eo.Z,{className:"w-4 h-4 text-seekers-primary"})}),(0,s.jsxs)("div",{className:"text-left",children:[s.jsx("div",{className:"font-medium",children:t}),s.jsx("div",{className:"text-sm text-gray-500",children:e.name})]})]},`${e.name}-${t}`))}),i.data?.data?.map((e,t)=>s.jsxs("button",{className:"w-full flex items-center gap-3 p-3 hover:bg-gray-50 transition-colors rounded-lg",onClick:()=>{p(e)},children:[s.jsx("div",{className:"w-8 h-8 flex items-center justify-center",children:s.jsx(eo.Z,{className:"w-4 h-4 text-seekers-primary"})}),s.jsx("div",{className:"text-left",children:s.jsx("div",{className:"font-medium",children:e})})]},t))]})]})]})}function ex({customTrigger:e,isUseAnimation:t=!0}){let a=(0,N.useTranslations)("seeker"),[i,n]=(0,u.useState)(!1),{isOpen:l,setLocationInputFocused:c,query:d}=(0,f.V)(e=>e),[m,x]=(0,u.useState)(!0),{handleSetQuery:g,seekersSearch:A,handleSearch:y}=(0,r.Z)(),w=(0,u.useRef)(null),v=e=>{n(e),c(e)};return s.jsx("div",{className:(0,h.cn)(t?l?"w-full":"w-fit":"w-full"),onClick:()=>{v(!0),w.current?.focus()},children:(0,s.jsxs)(Q,{open:i&&m,onOpenChange:v,children:[e?s.jsx(K,{asChild:!0,children:e}):s.jsx(K,{className:"w-full px-4",children:(0,s.jsxs)(s.Fragment,{children:[s.jsx(X._,{className:"text-xs font-medium text-seekers-text",children:a("navbar.search.locationTitle")}),(0,s.jsxs)(p.E.div,{animate:{height:l?20:0,opacity:l?100:0,width:l?"100%":0},transition:{duration:.3},className:"flex justify-between",children:[s.jsx(ee.I,{ref:w,onFocus:e=>v(!0),onChange:e=>{g(e.target.value),x(!0)},value:d,placeholder:a("form.placeholder.seekersFindPropertyLocation"),onKeyDown:e=>{e.stopPropagation(),"Enter"===e.key&&(y(),n(!1))},className:"border-0 placeholder:text-seekers-text-lighter focus:outline-none shadow-none focus-visible:ring-0 focus-visible:border-b w-full rounded-none pb-2 !p-0 h-fit"}),s.jsx(o.z,{variant:"ghost",onClick:e=>{e.stopPropagation(),e.preventDefault(),A.setQuery("")},size:"icon",className:(0,h.cn)("-mt-2",A.query.length>0?"":"hidden"),children:s.jsx(et.Z,{})})]})]})}),s.jsx(W,{className:"w-full border-seekers-text-lighter/20",align:"start",onOpenAutoFocus:e=>e.preventDefault(),children:s.jsx(eh,{showContent:x})})]})})}var eg=a(62513),eA=a(55961),ey=a(81441),ew=a(17328);function ev(){let e=(0,N.useTranslations)("seeker"),{handleSearch:t}=(0,r.Z)(),[a,i]=(0,u.useState)(!1),[n,l]=(0,u.useState)("location"),{query:d}=(0,f.V)(e=>e),{handleSetType:m,seekersSearch:p,propertyType:x,handleSetQuery:g}=(0,r.Z)();return(0,er.N)(d,500),(0,s.jsxs)(D.Z,{open:a,setOpen:i,drawerClassName:"relative",openTrigger:(0,s.jsxs)("div",{className:"w-full border h-10 pl-4 pr-1 flex items-center justify-between text-seekers-text-light text-xs rounded-full border-seekers-text-lighter shadow-md",children:[s.jsx("span",{className:"line-clamp-1",children:e("listing.search.placeholder")}),s.jsx(o.z,{variant:"default-seekers",className:"rounded-full !h-8 !w-[2.25rem]",size:"icon",children:s.jsx(c.Z,{className:"!w-4 !h-4",strokeWidth:3})})]}),children:[(0,s.jsxs)("div",{className:"flex flex-col h-[calc(80vh-24px)] pb-16",children:[(0,s.jsxs)("div",{className:"flex-shrink-0 bg-white z-10 border-b",children:[(0,s.jsxs)(I.Z,{className:"px-0 !text-center",children:[s.jsx(ey.Z,{className:"font-semibold p-0",children:e("listing.search.title")}),s.jsx(O.Be,{children:e("misc.findYourPerfectProperty")})]}),(0,s.jsxs)("div",{className:"px-4 mb-4 relative",children:[s.jsx(ee.I,{type:"text",placeholder:"Search destinations",className:"w-full px-3 py-2 !text-sm !h-10",value:d,onChange:e=>{g(e.target.value)},onKeyDown:e=>{e.stopPropagation(),"Enter"===e.key&&(t(),i(!1))}}),s.jsx(et.Z,{className:"w-4 h-4 absolute right-7 top-1/2 -translate-y-1/2 text-seekers-text-light",onClick:()=>g("")})]})]}),s.jsx("div",{className:"flex-grow overflow-y-auto",children:(0,s.jsxs)("div",{className:"p-4 space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",onClick:()=>l("location"),children:[s.jsx("div",{className:"text-[#B88E57] font-medium mb-2",children:e("navbar.search.locationTitle")}),s.jsx(o.z,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0",children:s.jsx(eg.Z,{className:(0,h.cn)("h-4 w-4 transition-transform","location"==n?"transform rotate-180":"")})})]}),"location"==n&&s.jsx(eh,{})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",onClick:()=>l("category"),children:[s.jsx("div",{className:"text-[#B88E57] font-medium mb-2",children:e("navbar.search.category")}),s.jsx(o.z,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0",children:s.jsx(eg.Z,{className:(0,h.cn)("h-4 w-4 transition-transform","category"==n?"transform rotate-180":"")})})]}),"category"==n&&s.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2",children:x.map(e=>(0,s.jsxs)("div",{className:"   border-seekers-text-lighter   hover:bg-seekers-background    gap-2 text-xs    text-seekers-text-light    flex flex-col    justify-center   items-center   p-4   relative   border   rounded-lg   ",onClick:t=>{t.preventDefault(),t.stopPropagation(),m(e.value)},children:[s.jsx("div",{className:(0,h.cn)("absolute top-3 left-3 rounded-full w-4 h-4 flex items-center justify-center",p.propertyType.includes(e.value)?"bg-seekers-primary":"border border-seekers-text-lighter"),children:s.jsx(el.Z,{className:(0,h.cn)(p.propertyType.includes(e.value)?"w-3 h-3 text-white":"hidden")})}),s.jsx(ew.Z,{category:e.value,className:"!w-6 !h-6"}),s.jsx("span",{className:"text-center",children:e.content})]},e.id))})]})]})})]}),s.jsx(eA.Z,{className:"absolute bottom-0 w-[calc(100%-32px)]",children:(0,s.jsxs)("div",{className:"flex justify-between items-center gap-4",children:[s.jsx(o.z,{variant:"link",className:"px-0 text-seekers-primary",onClick:()=>i(!1),children:e("cta.clearAll")}),(0,s.jsxs)(o.z,{className:"flex gap-2",variant:"default-seekers",onClick:()=>{t(),i(!1)},children:[s.jsx(c.Z,{}),e("cta.search")]})]})})]})}var eN=a(88459);function ej({value:e,textOnly:t=!1}){let a=(0,N.useTranslations)("seeker"),{propertyTypeFormatHelper:i}=(0,r.Z)(),n=i(e.split(","));return e.includes("all")?t?a("listing.filter.category.all.title"):s.jsx("p",{children:a("listing.filter.category.all.title")}):t?n.toString().replace(",",` ${a("conjuntion.and")} `):s.jsx(s.Fragment,{children:n.length>2?s.jsx(eN.Z,{trigger:s.jsx("div",{children:(0,s.jsxs)("p",{children:[n[0]," ",a("conjuntion.and")," ",(0,s.jsxs)("span",{children:["+ ",n.length-1," ",a("misc.more")]})]})}),content:n.toString().replaceAll(",",", "),contentClassName:"text-seekers-text"}):s.jsx("div",{children:s.jsx("p",{children:n.toString().replace(",",` ${a("conjuntion.and")} `)})})})}function eb({customTrigger:e}){let t=(0,N.useTranslations)(),[a,i]=(0,u.useState)(!1),{isOpen:n,setCategoryInputFocused:l}=(0,f.V)(e=>e),{handleSetType:c,seekersSearch:d,propertyType:m}=(0,r.Z)();return(0,s.jsxs)(V.h_,{modal:!1,open:a,onOpenChange:e=>{i(e),l(e)},children:[s.jsx(V.$F,{asChild:!0,children:e||(0,s.jsxs)("div",{className:(0,h.cn)("px-2",n?"w-full":"w-0"),children:[s.jsx(X._,{className:"text-xs font-medium text-seekers-text",children:t("seeker.navbar.search.category")}),(0,s.jsxs)(p.E.div,{animate:{height:n?20:0,opacity:n?100:0,width:n?"100%":0},transition:{duration:.3},className:"flex justify-between",children:[s.jsx(o.z,{variant:"ghost",className:"w-full h-fit font-normal p-0 overflow-hidden justify-start hover:bg-transparent",children:d.propertyType.length<1?s.jsx("p",{className:"text-seekers-text-lighter",children:t("seeker.navbar.search.propertyType")}):s.jsx(ej,{value:d.propertyType.toString()})}),s.jsx(o.z,{variant:"ghost",onClick:e=>{e.stopPropagation(),e.preventDefault(),d.clearCategory()},size:"icon",className:(0,h.cn)("-mt-2",d.propertyType.length>0?"":"hidden"),children:s.jsx(et.Z,{})})]})]})}),s.jsx(V.AW,{className:(0,h.cn)("border-seekers-text-lighter/20 grid grid-cols-2 sm:grid-cols-3 gap-3 p-4",n?"w-fit":"w-0"),align:"start",children:m.map(e=>(0,s.jsxs)("div",{className:"   border-seekers-text-lighter   hover:bg-seekers-background    gap-2 text-xs    text-seekers-text-light    flex flex-col    justify-center   items-center   md:w-28 p-4   relative   border   rounded-lg   ",onClick:t=>{t.preventDefault(),t.stopPropagation(),c(e.value)},"data-inside-dropdown":!0,children:[s.jsx("div",{className:(0,h.cn)("absolute top-3 left-3 rounded-full w-4 h-4 flex items-center justify-center",d.propertyType.includes(e.value)?"bg-seekers-primary":"border border-seekers-text-lighter"),children:s.jsx(el.Z,{className:(0,h.cn)(d.propertyType.includes(e.value)?"w-3 h-3 text-white":"hidden")})}),s.jsx(ew.Z,{category:e.value,className:"!w-6 !h-6"}),s.jsx("span",{className:"text-center",children:e.content})]},e.id))})]})}function ek({localeId:e="EN",currency_:t="EUR"}){let{handleSearch:a}=(0,r.Z)(),[x,g]=(0,u.useState)(!1),A=(0,u.useRef)(null),y=(0,u.useRef)(null),{isOpen:w,setIsOpen:v,categoryInputFocused:N,locationInputFocused:j}=(0,f.V)(e=>e);return s.jsx(m.M,{children:(0,s.jsxs)("nav",{ref:y,className:"w-full max-xl:space-y-4 border-b shadow-sm shadow-neutral-600/20 bg-white md:h-[90px] lg:h-[114px]",children:[s.jsx(i.Z,{className:"!h-full relative py-4 max-lg:space-y-4 xl:py-6 space-y-8 ",children:(0,s.jsxs)("div",{className:"w-full flex justify-between items-center flex-wrap gap-y-6",children:[s.jsx(C,{href:"/",children:s.jsx(n.default,{src:l.default,alt:"Property-Plaza",width:164,height:24})}),(0,s.jsxs)(p.E.div,{className:"flex gap-2 rounded-full p-2 border border-seekers-text-lighter shadow-md items-center max-lg:hidden pl-4",initial:{opacity:1,width:"60%"},animate:{width:w?"60%":"30%"},transition:{duration:.3},children:[(0,s.jsxs)("div",{className:"flex flex-grow items-center overflow-hidden divide-x-2 divide-seekers-text-lighter",children:[s.jsx("div",{className:"flex-grow min-w-[49%] max-w-[50%] pr-8",children:s.jsx(ex,{})}),s.jsx("div",{className:"flex-grow min-w-[49%] max-w-[50%] pl-8",children:s.jsx(eb,{})})]}),s.jsx(p.E.div,{initial:{height:48,width:48},animate:{height:w?48:36,width:w?48:36},transition:{duration:.3},children:s.jsx(o.z,{variant:"default-seekers",onClick:()=>a(),className:"rounded-full w-full h-full !aspect-square",size:"icon",children:s.jsx(c.Z,{className:"!w-5 !h-5",strokeWidth:3})})})]}),s.jsx("div",{className:"lg:hidden max-sm:w-full md:max-lg:w-[50%] max-sm:order-last flex gap-2",children:s.jsx(ev,{})}),s.jsx("div",{className:"md:hidden flex gap-1 w-[164px] justify-end",children:s.jsx(o.z,{variant:"ghost",className:"px-0 pl-4",onClick:()=>g(e=>!e),children:s.jsx(d.Z,{className:"!h-6 !w-6"})})}),s.jsx("div",{className:"max-md:hidden flex gap-2 items-center w-fit justify-end min-w-[136px]",children:s.jsx(H,{currency_:t,localeId:e})})]})}),s.jsx("div",{className:(0,h.cn)(x?"fixed w-screen h-full bg-seekers-text/30 top-0 left-0 -z-10 !mt-0":"hidden")}),s.jsx("div",{ref:A,className:`absolute top-12 z-30 bg-background left-0 w-full flex gap-2 items-center justify-end  ${x?"h-fit  py-4 px-4":"h-0"} overflow-hidden transition-all ease-linear duration-75 transform`,children:s.jsx(H,{currency_:t,localeId:e})})]})})}},93137:(e,t,a)=>{"use strict";a.d(t,{Z:()=>d});var s=a(97247),r=a(61600),i=a(5271),n=a(92894),l=a(23866),o=a(25008),c=a(84879);function d({url:e,className:t}){(0,l.l)();let{seekers:a}=(0,n.L)(),d=(0,c.useTranslations)("universal");return s.jsx(s.Fragment,{children:(0,s.jsxs)(r.qE,{className:(0,o.cn)("w-full rounded-full bg-seekers-text-lighter flex justify-center items-center",t),children:[s.jsx(r.F$,{src:a.accounts?.image,alt:d("misc.profileImageAlt")}),s.jsx(r.Q5,{className:"bg-transparent text-white",children:a.code?(0,s.jsxs)("span",{children:[a.accounts.firstName[0],a.accounts.lastName[0]]}):s.jsx(i.Z,{})})]})})}},22288:(e,t,a)=>{"use strict";a.d(t,{Z:()=>i});var s=a(97247),r=a(25008);function i(e){return s.jsx("div",{...e,ref:e.ref,className:(0,r.cn)("xl:max-w-screen-2xl max-md:px-4 px-8 mx-auto space-y-12",e.className),children:e.children})}},88459:(e,t,a)=>{"use strict";a.d(t,{Z:()=>i});var s=a(97247);a(28964);var r=a(92363);function i({content:e,trigger:t,contentClassName:a}){return s.jsx(r.TooltipProvider,{delayDuration:100,children:(0,s.jsxs)(r.Tooltip,{children:[s.jsx(r.TooltipTrigger,{asChild:!0,children:t}),s.jsx(r.TooltipContent,{className:a,children:e})]})})}},61600:(e,t,a)=>{"use strict";a.d(t,{F$:()=>o,Q5:()=>c,qE:()=>l});var s=a(97247),r=a(28964),i=a(51313),n=a(25008);let l=r.forwardRef(({className:e,...t},a)=>s.jsx(i.fC,{ref:a,className:(0,n.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...t}));l.displayName=i.fC.displayName;let o=r.forwardRef(({className:e,...t},a)=>s.jsx(i.Ee,{ref:a,className:(0,n.cn)("aspect-square h-full w-full",e),...t}));o.displayName=i.Ee.displayName;let c=r.forwardRef(({className:e,...t},a)=>s.jsx(i.NY,{ref:a,className:(0,n.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...t}));c.displayName=i.NY.displayName},98969:(e,t,a)=>{"use strict";a.d(t,{$N:()=>h,Be:()=>x,PK:()=>d,Vq:()=>o,cN:()=>f,cZ:()=>m,fK:()=>p,hg:()=>c,t9:()=>u});var s=a(97247),r=a(28964),i=a(69311),n=a(2095),l=a(25008);let o=i.fC,c=i.xz,d=i.h_;i.x8;let u=r.forwardRef(({className:e,...t},a)=>s.jsx(i.aV,{ref:a,className:(0,l.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));u.displayName=i.aV.displayName;let m=r.forwardRef(({className:e,children:t,...a},r)=>(0,s.jsxs)(d,{children:[s.jsx(u,{}),(0,s.jsxs)(i.VY,{ref:r,className:(0,l.cn)("fixed left-[50%] top-[50%] w-full z-50 grid translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...a,children:[t,(0,s.jsxs)(i.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[s.jsx(n.Pxu,{className:"h-4 w-4"}),s.jsx("span",{className:"sr-only",children:"Close"})]})]})]}));m.displayName=i.VY.displayName;let p=({className:e,...t})=>s.jsx("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-start sm:text-left",e),...t});p.displayName="DialogHeader";let f=({className:e,...t})=>s.jsx("div",{className:(0,l.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});f.displayName="DialogFooter";let h=r.forwardRef(({className:e,...t},a)=>s.jsx(i.Dx,{ref:a,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));h.displayName=i.Dx.displayName;let x=r.forwardRef(({className:e,...t},a)=>s.jsx(i.dk,{ref:a,className:(0,l.cn)("text-sm text-muted-foreground",e),...t}));x.displayName=i.dk.displayName},27387:(e,t,a)=>{"use strict";a.d(t,{OX:()=>m,Qz:()=>o,dy:()=>l,iI:()=>f,sc:()=>u,u6:()=>h,ze:()=>p});var s=a(97247),r=a(28964),i=a(94645),n=a(25008);let l=({shouldScaleBackground:e=!0,...t})=>s.jsx(i.d.Root,{shouldScaleBackground:e,...t});l.displayName="Drawer";let o=i.d.Trigger,c=i.d.Portal;i.d.Close;let d=r.forwardRef(({className:e,...t},a)=>s.jsx(i.d.Overlay,{ref:a,className:(0,n.cn)("fixed inset-0 z-50 bg-black/80",e),...t}));d.displayName=i.d.Overlay.displayName;let u=r.forwardRef(({className:e,children:t,...a},r)=>(0,s.jsxs)(c,{children:[s.jsx(d,{}),(0,s.jsxs)(i.d.Content,{ref:r,className:(0,n.cn)("fixed inset-x-0 bottom-0 z-50 mt-24 flex h-auto max-h-[97%] flex-col rounded-t-[10px] border bg-background",e),...a,children:[s.jsx("div",{className:"mx-auto h-2 w-[100px] rounded-full bg-muted"}),t]})]}));u.displayName="DrawerContent";let m=({className:e,...t})=>s.jsx("div",{className:(0,n.cn)("grid gap-1.5 p-4 text-center sm:text-left",e),...t});m.displayName="DrawerHeader";let p=({className:e,...t})=>s.jsx("div",{className:(0,n.cn)("mt-auto flex flex-col gap-2 p-4",e),...t});p.displayName="DrawerFooter";let f=r.forwardRef(({className:e,...t},a)=>s.jsx(i.d.Title,{ref:a,className:(0,n.cn)("font-semibold leading-none tracking-tight",e),...t}));f.displayName=i.d.Title.displayName;let h=r.forwardRef(({className:e,...t},a)=>s.jsx(i.d.Description,{ref:a,className:(0,n.cn)("text-sm text-muted-foreground",e),...t}));h.displayName=i.d.Description.displayName},6047:(e,t,a)=>{"use strict";a.d(t,{$F:()=>c,AW:()=>d,Ju:()=>p,VD:()=>f,Xi:()=>u,bO:()=>m,h_:()=>o});var s=a(97247),r=a(28964),i=a(50525),n=a(2095),l=a(25008);let o=i.fC,c=i.xz;i.ZA,i.Uv,i.Tr,i.Ee,r.forwardRef(({className:e,inset:t,children:a,...r},o)=>(0,s.jsxs)(i.fF,{ref:o,className:(0,l.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",t&&"pl-8",e),...r,children:[a,s.jsx(n.XCv,{className:"ml-auto h-4 w-4"})]})).displayName=i.fF.displayName,r.forwardRef(({className:e,...t},a)=>s.jsx(i.tu,{ref:a,className:(0,l.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t})).displayName=i.tu.displayName;let d=r.forwardRef(({className:e,sideOffset:t=4,...a},r)=>s.jsx(i.Uv,{children:s.jsx(i.VY,{ref:r,sideOffset:t,className:(0,l.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...a})}));d.displayName=i.VY.displayName;let u=r.forwardRef(({className:e,inset:t,...a},r)=>s.jsx(i.ck,{ref:r,className:(0,l.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t&&"pl-8",e),...a}));u.displayName=i.ck.displayName;let m=r.forwardRef(({className:e,children:t,checked:a,checkboxPosition:r="start",...o},c)=>(0,s.jsxs)(i.oC,{ref:c,className:(0,l.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5  text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50","start"==r?"pl-8 pr-2":"pl-2 pr-8",e),checked:a,...o,children:[s.jsx("span",{className:(0,l.cn)("absolute flex h-3.5 w-3.5 items-center justify-center","start"==r?"left-2":"right-2"),children:s.jsx(i.wU,{children:s.jsx(n.nQG,{className:"h-4 w-4"})})}),t]}));m.displayName=i.oC.displayName,r.forwardRef(({className:e,children:t,...a},r)=>(0,s.jsxs)(i.Rk,{ref:r,className:(0,l.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[s.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:s.jsx(i.wU,{children:s.jsx(n.jXb,{className:"h-4 w-4 fill-current"})})}),t]})).displayName=i.Rk.displayName;let p=r.forwardRef(({className:e,inset:t,...a},r)=>s.jsx(i.__,{ref:r,className:(0,l.cn)("px-2 py-1.5 text-sm font-semibold",t&&"pl-8",e),...a}));p.displayName=i.__.displayName;let f=r.forwardRef(({className:e,...t},a)=>s.jsx(i.Z0,{ref:a,className:(0,l.cn)("-mx-1 my-1 h-px bg-muted",e),...t}));f.displayName=i.Z0.displayName},52208:(e,t,a)=>{"use strict";a.d(t,{NI:()=>x,Wi:()=>u,l0:()=>c,lX:()=>h,pf:()=>g,xJ:()=>f,zG:()=>A});var s=a(97247),r=a(28964),i=a(12341),n=a(2704),l=a(25008),o=a(22394);let c=n.RV,d=r.createContext({}),u=({...e})=>s.jsx(d.Provider,{value:{name:e.name},children:s.jsx(n.Qr,{...e})}),m=()=>{let e=r.useContext(d),t=r.useContext(p),{getFieldState:a,formState:s}=(0,n.Gc)(),i=a(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=t;return{id:l,name:e.name,formItemId:`${l}-form-item`,formDescriptionId:`${l}-form-item-description`,formMessageId:`${l}-form-item-message`,...i}},p=r.createContext({}),f=r.forwardRef(({className:e,...t},a)=>{let i=r.useId();return s.jsx(p.Provider,{value:{id:i},children:s.jsx("div",{ref:a,className:(0,l.cn)("space-y-2",e),...t})})});f.displayName="FormItem";let h=r.forwardRef(({className:e,...t},a)=>{let{error:r,formItemId:i}=m();return s.jsx(o._,{ref:a,className:(0,l.cn)(r&&"text-destructive",e),htmlFor:i,...t})});h.displayName="FormLabel";let x=r.forwardRef(({...e},t)=>{let{error:a,formItemId:r,formDescriptionId:n,formMessageId:l}=m();return s.jsx(i.g7,{ref:t,id:r,"aria-describedby":a?`${n} ${l}`:`${n}`,"aria-invalid":!!a,...e})});x.displayName="FormControl";let g=r.forwardRef(({className:e,...t},a)=>{let{formDescriptionId:r}=m();return s.jsx("p",{ref:a,id:r,className:(0,l.cn)("text-[0.8rem] text-muted-foreground",e),...t})});g.displayName="FormDescription";let A=r.forwardRef(({className:e,children:t,...a},r)=>{let{error:i,formMessageId:n}=m(),o=i?String(i?.message):t;return o?s.jsx("p",{ref:r,id:n,className:(0,l.cn)("text-[0.8rem] font-medium text-destructive",e),...a,children:o}):null});A.displayName="FormMessage"},69693:(e,t,a)=>{"use strict";a.d(t,{Zn:()=>o,cY:()=>d,hf:()=>c});var s=a(97247),r=a(28964),i=a(2095),n=a(12961),l=a(25008);let o=r.forwardRef(({className:e,containerClassName:t,...a},r)=>s.jsx(n.uZ,{ref:r,containerClassName:(0,l.cn)("flex items-center gap-2 has-[:disabled]:opacity-50",t),className:(0,l.cn)("disabled:cursor-not-allowed",e),...a}));o.displayName="InputOTP";let c=r.forwardRef(({className:e,...t},a)=>s.jsx("div",{ref:a,className:(0,l.cn)("flex items-center",e),...t}));c.displayName="InputOTPGroup";let d=r.forwardRef(({index:e,className:t,...a},i)=>{let{char:o,hasFakeCaret:c,isActive:d}=r.useContext(n.VM).slots[e];return(0,s.jsxs)("div",{ref:i,className:(0,l.cn)("relative flex h-9 w-9 items-center justify-center border-y border-r border-input text-sm shadow-sm transition-all first:rounded-l-md first:border-l last:rounded-r-md",d&&"z-10 ring-1 ring-ring",t),...a,children:[o,c&&s.jsx("div",{className:"pointer-events-none absolute inset-0 flex items-center justify-center",children:s.jsx("div",{className:"h-4 w-px animate-caret-blink bg-foreground duration-1000"})})]})});d.displayName="InputOTPSlot",r.forwardRef(({...e},t)=>s.jsx("div",{ref:t,role:"separator",...e,children:s.jsx(i.yhV,{})})).displayName="InputOTPSeparator"},70170:(e,t,a)=>{"use strict";a.d(t,{I:()=>n});var s=a(97247),r=a(28964),i=a(25008);let n=r.forwardRef(({className:e,type:t,...a},r)=>s.jsx("input",{type:t,className:(0,i.cn)("flex max-sm:h-8 h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-inset focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...a}));n.displayName="Input"},22394:(e,t,a)=>{"use strict";a.d(t,{_:()=>c});var s=a(97247),r=a(28964),i=a(40768),n=a(87972),l=a(25008);let o=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=r.forwardRef(({className:e,...t},a)=>s.jsx(i.f,{ref:a,className:(0,l.cn)(o(),e),...t}));c.displayName=i.f.displayName},94049:(e,t,a)=>{"use strict";a.d(t,{Bw:()=>p,Ph:()=>o,Ql:()=>f,i4:()=>d,ki:()=>c});var s=a(97247),r=a(28964),i=a(2095),n=a(201),l=a(25008);let o=n.fC;n.ZA;let c=n.B4,d=r.forwardRef(({className:e,children:t,showCaret:a=!0,...r},o)=>(0,s.jsxs)(n.xz,{ref:o,className:(0,l.cn)("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...r,children:[t,a&&s.jsx(n.JO,{asChild:!0,children:s.jsx(i.jnn,{className:"h-4 w-4 opacity-50"})})]}));d.displayName=n.xz.displayName;let u=r.forwardRef(({className:e,...t},a)=>s.jsx(n.u_,{ref:a,className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:s.jsx(i.g8U,{})}));u.displayName=n.u_.displayName;let m=r.forwardRef(({className:e,...t},a)=>s.jsx(n.$G,{ref:a,className:(0,l.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:s.jsx(i.v4q,{})}));m.displayName=n.$G.displayName;let p=r.forwardRef(({className:e,children:t,position:a="popper",...r},i)=>s.jsx(n.h_,{children:(0,s.jsxs)(n.VY,{ref:i,className:(0,l.cn)("relative z-50 max-h-96 w-fit overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...r,children:[s.jsx(u,{}),s.jsx(n.l_,{className:(0,l.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),s.jsx(m,{})]})}));p.displayName=n.VY.displayName,r.forwardRef(({className:e,...t},a)=>s.jsx(n.__,{ref:a,className:(0,l.cn)("px-2 py-1.5 text-sm font-semibold",e),...t})).displayName=n.__.displayName;let f=r.forwardRef(({className:e,children:t,...a},r)=>(0,s.jsxs)(n.ck,{ref:r,className:(0,l.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[s.jsx("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:s.jsx(n.wU,{children:s.jsx(i.nQG,{className:"h-4 w-4"})})}),s.jsx(n.eT,{children:t})]}));f.displayName=n.ck.displayName,r.forwardRef(({className:e,...t},a)=>s.jsx(n.Z0,{ref:a,className:(0,l.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=n.Z0.displayName},33626:(e,t,a)=>{"use strict";a.d(t,{Separator:()=>l});var s=a(97247),r=a(28964),i=a(52073),n=a(25008);let l=r.forwardRef(({className:e,orientation:t="horizontal",decorative:a=!0,...r},l)=>s.jsx(i.f,{ref:l,decorative:a,orientation:t,className:(0,n.cn)("shrink-0 bg-border","horizontal"===t?"h-[1px] w-full":"h-full w-[1px]",e),...r}));l.displayName=i.f.displayName},92363:(e,t,a)=>{"use strict";a.d(t,{Tooltip:()=>o,TooltipContent:()=>d,TooltipProvider:()=>l,TooltipTrigger:()=>c});var s=a(97247),r=a(28964),i=a(567),n=a(25008);let l=i.zt,o=i.fC,c=i.xz,d=r.forwardRef(({className:e,sideOffset:t=4,...a},r)=>s.jsx(i.VY,{ref:r,sideOffset:t,className:(0,n.cn)("z-50 overflow-hidden rounded-md bg-background px-3 py-1.5 text-xs text-primary border-primary animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...a}));d.displayName=i.VY.displayName},27727:(e,t,a)=>{"use strict";a.d(t,{X:()=>l});var s=a(6649),r=a(10906),i=a(88111),n=a(84879);function l(e){let{toast:t}=(0,r.pm)(),a=(0,n.useTranslations)("universal");return(0,i.D)({mutationFn:e=>(0,s.u8)(e),onSuccess:t=>{e(t)},onError:s=>{let r=s.response.data;if(r.message.includes("is already sent")){e(s);return}t({title:a("misc.foundError"),description:r.message,variant:"destructive"})}})}},37129:(e,t,a)=>{"use strict";a.d(t,{N:()=>i});var s=a(6649),r=a(88111);function i(){return(0,r.D)({mutationFn:e=>(0,s.vJ)(e)})}},73028:(e,t,a)=>{"use strict";a.d(t,{G:()=>l});var s=a(6649),r=a(10906),i=a(88111),n=a(84879);function l(e){let{toast:t}=(0,r.pm)(),a=(0,n.useTranslations)("universal");return(0,i.D)({mutationFn:e=>(0,s.zl)(e),onSuccess:async t=>{await e()},onError:e=>{let s=e.response.data;return t({title:a("misc.foundError"),description:s.message,variant:"destructive"}),s}})}},23866:(e,t,a)=>{"use strict";a.d(t,{J:()=>o,l:()=>c});var s=a(83482),r=a(16718),i=a(92894),n=a(9190),l=a(72266);let o="my-detail";function c(e=!0){let{setSeekers:t,clearUser:a,setRole:c}=(0,i.L)(e=>e),d=l.Z.get(r.LA);return(0,n.a)({queryKey:[o,d||"0"],queryFn:async()=>{if(!d)return i._;try{let e=await (0,s.O4)();return t(e),c("SEEKER"),e}catch(e){return a(),i._}},refetchOnWindowFocus:!1,retry:!1,enabled:e})}},74993:(e,t,a)=>{"use strict";a.d(t,{apiClient:()=>o,v:()=>c});var s=a(16718),r=a(10863),i=a(72266),n=a(35240);let l=new(a.n(n)()).Agent({rejectUnauthorized:!1}),o=r.Z.create({baseURL:"https://dev.property-plaza.id/api/v1",headers:{Authorization:i.Z.get(s.LA)?"Bearer "+i.Z.get(s.LA):""},httpsAgent:l}),c=r.Z.create({baseURL:"/api/",httpsAgent:l})},79984:(e,t,a)=>{"use strict";a.d(t,{JS:()=>n,e:()=>r,i6:()=>i,p5:()=>l,yJ:()=>s});let s={villas:"VILLA",apartment:"APARTMENT",rooms:"ROOM",commercialSpace:"COMMERCIAL_SPACE",cafeOrRestaurants:"CAFE_RESTAURANT",offices:"OFFICE",shops:"SHOP",shellAndCore:"SHELL_CORE",lands:"LAND",guestHouse:"GUESTHOUSE",homestay:"HOMESTAY",ruko:"RUKO",villa:"VILLA"},r={all:"ANY",mountain:"MOUNTAIN",ocean:"OCEAN",ricefield:"RICEFIELD",jungle:"JUNGLE"},i={anything:"ANY",placeToLive:"PLACE_TO_LIVE",business:"BUSINESS",land:"LAND"},n={plumbing:"PLUMBING",subleaseAllowed:"SUBLEASE_ALLOWED",balcony:"BALCONY",gazebo:"GAZEBO",recentlyRenovated:"RECENTLY_RENOVATED",airCondition:"AIR_CONDITION",constructionNearby:"CONSTRUCTION_NEARBY",rooftopTerrace:"ROOFTOP_TERRACE",terrace:"TERRACE",petAllowed:"PET_ALLOWED",garden:"GARDEN_BACKYARD",bathub:"BATHUB"},l={small:{min:1,max:300,key:"small"},medium:{min:301,max:1e3,key:"medium"},large:{min:1001,max:1e5,key:"large"}}},84650:(e,t,a)=>{"use strict";a.d(t,{D4:()=>r,FH:()=>s});let s=e=>e.toLowerCase().includes("day")?"DAY":e.toLowerCase().includes("week")?"WEEK":e.toLowerCase().includes("month")?"MONTH":e.toLowerCase().includes("year")?"YEAR":"MONTH",r=e=>"ONLINE"==e},97482:(e,t,a)=>{"use strict";a.d(t,{B9:()=>s,Dn:()=>r});let s={archiver:"Achiever",finder:"Finder",free:"Free"},r={contactOwner:"contact-owner",photos:"photos",mapLocation:"map-location",advanceAndSaveFilter:"advance-and-save-filter",savedListing:"saved-listings",favoriteProperties:"favorite-properties"}},34200:(e,t,a)=>{"use strict";a.d(t,{$8:()=>l,ek:()=>r,lJ:()=>n,nH:()=>i,pb:()=>o});var s=a(97482);function r(e){return s.B9.free.includes(e)?s.B9.free:s.B9.finder.includes(e)?s.B9.finder:s.B9.archiver.includes(e)?s.B9.archiver:s.B9.free}function i(e){return e==s.B9.free?0:e==s.B9.finder?5:e==s.B9.archiver?10:0}let n=10,l={max:13,min:10};function o(e){return e==s.B9.free?l:e==s.B9.finder?{max:14,min:n}:e==s.B9.archiver?{max:15,min:n}:l}},87721:(e,t,a)=>{"use strict";a.d(t,{AS:()=>c,Af:()=>p,Ew:()=>m,PQ:()=>d,kS:()=>i,rb:()=>u,u8:()=>n,vJ:()=>o,x4:()=>r,zl:()=>l});var s=a(74993);let r=(e,t)=>s.apiClient.post("auth/login",e,{headers:{"g-token":t||""}}),i=()=>s.apiClient.post("auth/logout"),n=e=>s.apiClient.post("notifications/email",e),l=e=>s.apiClient.post("auth/otp-verification",e),o=e=>s.apiClient.post("auth/forgot-password",e),c=e=>s.apiClient.get(`auth/verify-reset-password?email=${e.email}&token=${e.token}`),d=e=>s.apiClient.post("auth/reset-password",e),u=(e,t)=>s.apiClient.post("auth/create-password",e,t),m=e=>s.apiClient.post("users/security",e),p=e=>s.apiClient.post("auth/totp-verification",e)},6649:(e,t,a)=>{"use strict";a.d(t,{AS:()=>s.AS,Af:()=>s.Af,Ew:()=>s.Ew,PQ:()=>s.PQ,kS:()=>s.kS,rb:()=>s.rb,u8:()=>s.u8,vJ:()=>s.vJ,x4:()=>s.x4,zl:()=>s.zl});var s=a(87721)},69591:(e,t,a)=>{"use strict";a.d(t,{F4:()=>o,JG:()=>d,KC:()=>n,Mz:()=>c,R6:()=>l,T_:()=>i,x0:()=>r});var s=a(74993);a(97244),a(84006);let r=e=>s.apiClient.post("properties/favorite",e),i=e=>(0,s.apiClient)(`/properties/filter-location?search=${e.search}`),n=(e,t)=>s.apiClient.post("properties/filter",e,{headers:{"g-token":t||""}}),l=()=>s.apiClient.get("filter-parameter"),o=({page:e,per_page:t,search:a,sort_by:r})=>s.apiClient.get(`users/favorite?page=${e}&per_page=${t}&search=${a}&sort_by=${r}`),c=e=>s.apiClient.put("users/filter-setting",e),d=e=>s.apiClient.post("properties/batch-property",e)},59683:(e,t,a)=>{"use strict";a.d(t,{f4:()=>d,sK:()=>f,_o:()=>p,p:()=>u,IW:()=>m});var s=a(96643),r=a(69591);a(84650);var i=a(32430),n=a.n(i),l=a(95259);function o(e,t="en"){return e.map(e=>{var a;return{code:e.code,geolocation:c(e.location.latitude,e.location.longitude),location:e.location.district+", "+e.location.city+", "+e.location.province,price:e.availability.price,thumbnail:(a=e.code,e.images.map((e,t)=>({id:a+t,image:e.image,isHighlight:e.is_highlight})).sort((e,t)=>+t.isHighlight-+e.isHighlight)),title:(0,s.P)(e.title,t),listingDetail:{bathRoom:e.detail.bathroom_total,bedRoom:e.detail.bedroom_total,buildingSize:e.detail.building_size,landSize:e.detail.land_size,cascoStatus:e.detail.casco_status,gardenSize:e.detail.garden_size},availability:{availableAt:e.availability.available_at||"",maxDuration:e.availability.duration_max_unit?.value&&e.availability.duration_max?{value:e.availability.duration_max||1,suffix:e.availability.duration_max_unit?.value}:null,minDuration:e.availability.duration_min_unit?.value&&e.availability.duration_min?{value:e.availability.duration_min||1,suffix:e.availability.duration_min_unit?.value}:null,type:e.availability.type.value||""},sellingPoint:e.features.selling_points,category:e.detail.option.type,isFavorite:e?._count?.favorites>0,status:e.status}})}let c=(e,t,a=10)=>{let s=1/111320*a;return[e+.4*s,t+.4*s]};async function d(e,t="en"){try{let a=await (0,r.JG)({property_list:e});return{data:o(a.data.data,t),locale:t}}catch(e){return{error:e.data.error??"An unknown error occurred"}}}async function u(e,t="en"){try{let a=await (0,r.KC)(e);try{let t=Object.fromEntries(Object.entries(e).filter(([e,t])=>void 0!==t));2!==Object.keys(t).length&&await (0,r.Mz)(e)}catch(e){}return{data:o(a.data.data.items,t),meta:(0,s.N)(a.data.data.meta)}}catch(e){return{error:e.data.error??"An unknown error occurred"}}}async function m(e){if(e.search.length<3)return{data:[]};try{let t=await (0,r.T_)(e);return{data:function(e,t){let a=[];return t.forEach(t=>{Object.values(t).forEach(t=>{(function(e,t){let a=(0,l.Z)(e.toLowerCase(),t.toLowerCase());return 1-a/Math.max(e.length,t.length)})(t,e)>0&&a.push(t)})}),n().uniq(a)}(e.search,t.data.data)}}catch(e){return{error:e.data.error??"An unknown error occurred"}}}async function p(){try{var e;return{data:{priceRange:{min:(e=(await (0,r.R6)()).data.data).price_range._min.price,max:e.price_range._max.price},buildingSizeRange:{max:e.size_range._max.building_size,min:e.size_range._min.building_size},gardenSizeRange:{max:e.size_range._max.garden_size,min:e.size_range._min.garden_size},landSizeRange:{max:e.size_range._max.land_size,min:e.size_range._min.land_size},furnishingOptions:e.furnishing_options[0].childrens.map(e=>({title:e.title,value:e.value})),livingOptions:e.living_options[0].childrens.map(e=>({title:e.title,value:e.value})),parkingOptions:e.parking_options[0].childrens.map(e=>({title:e.title,value:e.value})),poolOptions:e.pool_options[0].childrens.map(e=>({title:e.title,value:e.value}))},meta:void 0}}catch(e){return{error:e.data.error??"An unknown error occurred"}}}async function f(e,t="en"){try{let a=await (0,r.F4)({page:+e.page,per_page:+e.per_page,search:e.search||"",sort_by:e.sort_by});return{data:o(a.data.data.items,t),meta:(0,s.N)(a.data.data.meta)}}catch(e){return{error:e.data.error??"An unknown error occurred"}}}},43189:(e,t,a)=>{"use strict";a.d(t,{a$:()=>r,f_:()=>i,jo:()=>n});var s=a(74993);let r=async(e,t)=>s.apiClient.post("auth/register",e,{headers:{"g-token":t||""}}),i=async e=>s.apiClient.put("users/update",e),n=async e=>s.apiClient.get("auth/me",e)},83482:(e,t,a)=>{"use strict";a.d(t,{O4:()=>n});var s=a(43189),r=a(34200);a(16718);var i=a(29178);async function n(e){try{let t=await (0,s.jo)(e);return function(e){let t=(0,r.ek)(e.accounts.subscription?.detail.name||"");return{accounts:{about:e.accounts.about,citizenship:e.accounts.citizenship||"",credit:{amount:e.accounts.credit?.amount||0,updatedAt:e.accounts.credit?.updated_at||""},facebookSocial:e.accounts.facebook_social||"",firstName:e.accounts.first_name,image:e.accounts.image,isSubscriber:e.accounts.is_subscriber,language:e.accounts.language,lastName:e.accounts.last_name,membership:t,twitterSocial:e.accounts.twitter_social||"",address:e.accounts.address||"",chat:{current:0,max:(0,r.nH)(t)},zoomFeature:(0,r.pb)(t)},has2FA:e.is_2fa,email:e.email,code:e.code,isActive:e.is_active,phoneNumber:e.phone_number,phoneCode:e.phone_code,type:e.type,setting:{messageNotif:e.accounts.settings?.message_notif,newsletterNotif:e.accounts.settings?.newsletter_notif,priceAlertNotif:e.accounts.settings?.price_alert_notif,propertyNotif:e.accounts.settings?.property_notif,soundNotif:e.accounts.settings?.sound_notif,specialOfferNotif:e.accounts.settings?.special_offer_notif,surveyNotif:e.accounts.settings?.survey_notif}}}(t.data.data)}catch(e){throw Error((0,i.q)(e))}}},29178:(e,t,a)=>{"use strict";a.d(t,{q:()=>r});var s=a(10863);function r(e){if(s.Z.isAxiosError(e)){if(e.response?.status===401)throw Error("Unauthorized: Invalid token or missing credentials");if(e.response?.status===404)throw Error("Not Found: The requested resource could not be found");if(e.response)throw Error(`Request failed with status code ${e.response.status}: ${e.response.statusText}`);if(e.request)throw Error("No response received: Possible network error or wrong endpoint");else throw Error(`Error during request setup: ${e.message}`)}throw Error(e)}},84006:(e,t,a)=>{"use strict";a(70689),(0,a(11294).$)("ace39bf07124e0ba39e8486050a53bc79b0621b3")},97244:(e,t,a)=>{},15916:(e,t,a)=>{"use strict";a.d(t,{N:()=>r});var s=a(28964);let r=(e,t=500)=>{let[a,r]=(0,s.useState)(e);return(0,s.useEffect)(()=>{let a=setTimeout(()=>{r(e)},t);return()=>{clearTimeout(a)}},[e,t]),a}},93009:(e,t,a)=>{"use strict";a.d(t,{a:()=>r});var s=a(28964);function r(e){let[t,a]=(0,s.useState)(!1);return t}},40896:(e,t,a)=>{"use strict";a.d(t,{Z:()=>n});var s=a(34178),r=a(28964),i=a(80818);function n(){let e=(0,i.useRouter)(),t=(0,s.usePathname)(),a=(0,s.useSearchParams)(),n=(0,r.useCallback)(s=>{let r=new URLSearchParams(a.toString());s.forEach(e=>r.set(e.name,e.value)),e.push(t+"?"+r.toString())},[a,e,t]),l=(0,r.useCallback)((e,t)=>{let s=new URLSearchParams(a.toString());return s.set(e,t),s.toString()},[a]);return{searchParams:a,createQueryString:(s,r)=>{let i=new URLSearchParams(a.toString());i.set(s,r),e.push(t+"?"+i.toString())},generateQueryString:l,removeQueryParam:(t,s)=>{let r=new URLSearchParams(a.toString());t.forEach(e=>{r.delete(e)});let i=`${window.location.pathname}?${r.toString()}`;if(s)return window.location.href=i;e.push(i)},createMultipleQueryString:n,pathname:t,updateQuery:(s,r)=>{let i=new URLSearchParams(a.toString());i.set(s,r),e.push(t+"?"+i.toString())}}}},40708:(e,t,a)=>{"use strict";a.d(t,{Z:()=>u});var s=a(79935),r=a(28964),i=a(54033),n=a(25008),l=a(80818),o=a(84879),c=a(79984),d=a(16718);function u(){let e=(0,o.useTranslations)(),t=(0,s.V)(e=>e),[a,u]=(0,r.useState)(!1),[m,p]=(0,r.useState)(null),f=(0,l.useRouter)(),h=[{content:e("seeker.listing.category.villa"),id:"1",value:c.yJ.villas},{content:e("seeker.listing.category.apartment"),id:"2",value:c.yJ.apartment},{content:e("seeker.listing.category.guestHouse"),id:"3",value:c.yJ.rooms},{content:e("seeker.listing.category.commercial"),id:"4",value:c.yJ.commercialSpace},{content:e("seeker.listing.category.cafeAndRestaurent"),id:"5",value:c.yJ.cafeOrRestaurants},{content:e("seeker.listing.category.office"),id:"6",value:c.yJ.offices},{content:e("seeker.listing.category.shops"),id:"7",value:c.yJ.shops},{content:e("seeker.listing.category.shellAndCore"),id:"8",value:c.yJ.shellAndCore},{content:e("seeker.listing.category.land"),id:"9",value:c.yJ.lands}],x=[{name:"Canggu, Bali",description:"Popular surf spot & digital nomad hub",icon:"Canggu",value:"canggu"},{name:"Ubud, Bali",description:"Cultural heart with rice terraces",value:"ubud",icon:"Ubud"},{name:"Seminyak, Bali",description:"Upscale beach resort area",icon:"Seminyak",value:"seminyak"},{name:"Uluwatu, Bali",description:"Clifftop temples & luxury resorts",icon:"Uluwatu",value:"uluwatu"},{name:"Nusa Dua, Bali",description:"Gated resort area with pristine beaches",icon:"NusaDua",value:"Nusa Dua"}],g={canggu:["Babakan","Batu Bolong","Berawa","Cemagi","Cempaka","Echo Beach","Kayu Tulang","Munggu","Nelayan","North Canggu","Nyanyi","Padonan","Pantai Lima","Pererenan","Seseh","Tiying Tutul","Tumbak Bayuh"].sort(),ubud:["Bentuyung","Junjungan","Kedewatan","Nyuh Kuning","Penestanan","Sambahan","Sanggingan","Taman Kaja","Tegallantang","Ubud Center"],uluwatu:["Balangan","Bingin","Green Bowl","Karang Boma","Nyang Nyang","Padang Padang","Pecatu","Suluban"],nusaDua:["Benoa","BTDC Area","Bualu","Kampial","Peminge","Sawangan","Tanjung Benoa"],seminyak:[]},A=(0,r.useMemo)(()=>!t.query||a?x:x.filter(e=>{let a=e.name.replace(", Bali","").toLowerCase(),s=t.query.toLowerCase();return!!a.includes(s)||(g[e.value]||[]).some(e=>e.toLowerCase().includes(s))}),[t.query,a]);return{seekersSearch:t,handleSetQuery:e=>{let a=e.split(","),s=e.length;a.length>3&&","==e.charAt(s-1)||t.setQuery(e)},handleSetType:e=>{(!(t.propertyType.length>=3)||t.propertyType.includes(e))&&t.setPropertyType(e)},propertyType:h,handleSearch:(e,a)=>{e&&t.setQuery(e),a&&t.setPropertyTypeFromArray(a);let s=e||t.query,r=a||t.propertyType;""!==t.activeSearch.query&&t.setSearchHistory({propertyType:t.activeSearch.propertyType,query:t.activeSearch.query}),t.setActiveSearch({query:s,propertyType:r});let l=(0,n.Fg)(s);f.push(i.rr+"/"+(l||"all")+"?"+d.Y.type+"="+(r.toString()||"all"))},propertyTypeFormatHelper:e=>e.map(e=>{let t=h.find(t=>t.value==e);return t?.content}),locations:x,banjars:g,getMatchingBanjars:e=>{let a=t.query;return a?(g[e]||[]).filter(e=>e.toLowerCase().includes(a.toLowerCase())):[]},showBanjars:a,setShowBanjars:u,selectedLocation:m,setSelectedLocation:p,handleSelectLocation:e=>{u(!0),p(e),t.setQuery(e)},handleBackToLocations:()=>{u(!1);let e=t.query.replace(m||"","");t.setQuery(e)},handleSetBanjar:(e,a=!1)=>{let s=t.query.split(",").filter(e=>""!==e.trim()&&e!==m);if(s.includes(e)){let a=s.filter(t=>t!==e);t.setQuery(a.toString());return}if(!(s.length>=3)||""===s[s.length-1]){if(a){let t=s.length;s[t-1]=e}else s.push(e);t.setQuery(s.toString())}},filteredLocations:A}}},54033:(e,t,a)=>{"use strict";a.d(t,{Fq:()=>l,GA:()=>m,Kc:()=>n,OM:()=>u,Qk:()=>f,Y8:()=>c,ej:()=>x,in:()=>d,rr:()=>o,rv:()=>p,s0:()=>h}),a(97247);var s=a(90209),r=a(97046),i=a(92814);let n="/owner/account",l="/profile",o="/s",c="/favorites",d="/message",u="/subscription",m="/plan",p="/billing",f="/notification",h="/security",x="/representative/account";s.Z,r.Z,i.Z,s.Z,r.Z},75476:(e,t,a)=>{"use strict";a.d(t,{DI:()=>r,jD:()=>l,rU:()=>i});var s=a(12392);let r={locales:["en","id"],defaultLocale:"en"},{Link:i,redirect:n,usePathname:l,useRouter:o}=(0,s.os)(r)},92199:(e,t,a)=>{"use strict";a.d(t,{I:()=>o});var s=a(34523),r=a.n(s),i=a(69133),n=a(92377);let l={confirm_password:"",first_name:"",last_name:"",otp:"",password:"",type:"SEEKER",email:"",phone_code:"+62",phone_number:""},o=(0,i.Ue)()((0,n.tJ)(e=>({register:l,setRegister:t=>e({register:t}),verifyOtpType:"",setVerifyOtpType:t=>e({verifyOtpType:t}),reset:()=>{o.persist.clearStorage(),e({register:l})},validFormUntil:void 0,setValidFormUntil:t=>e({validFormUntil:t}),successSignUp:!1,setSuccessSignUp:t=>e({successSignUp:t}),loading:!0,setLoading:t=>e({loading:t})}),{name:"register-user",storage:(0,n.FL)(()=>localStorage),onRehydrateStorage:()=>e=>{if(e?.validFormUntil){let t=r()(e?.validFormUntil);r()().isAfter(t)&&e.setRegister(l)}e?.setLoading(!1)}}))},79935:(e,t,a)=>{"use strict";a.d(t,{V:()=>o});var s=a(25008),r=a(34523),i=a.n(r),n=a(69133),l=a(92377);let o=(0,n.Ue)()((0,l.tJ)(e=>({activeSearch:{propertyType:[],query:""},propertyType:[],query:"",searchHistory:[],isOpen:!0,locationInputFocused:!1,categoryInputFocused:!1,setActiveSearch:t=>e({activeSearch:t}),setPropertyType:t=>e(e=>({propertyType:(0,s.ET)(e.propertyType,t)})),setQuery:t=>e({query:t}),setSearchHistory:t=>e(e=>{let a={...t,validUntil:i()().add(7,"days").format("DD-MMM-YYYY")};if(e.searchHistory.findIndex(e=>e.query==a.query)>=0)return e;let s=[...e.searchHistory,a];if(e.searchHistory.length<5)return e.searchHistory=s,e;let r=s.slice(1,4);return e.searchHistory=[...r,a],e}),setIsOpen:t=>e({isOpen:t}),setCategoryInputFocused:t=>e({categoryInputFocused:t}),setLocationInputFocused:t=>e({locationInputFocused:t}),clearSearch:()=>e({query:"",propertyType:[]}),setPropertyTypeFromArray:t=>e({propertyType:t}),clearCategory:()=>e({propertyType:[]})}),{name:"seeker-search",storage:(0,l.FL)(()=>localStorage),onRehydrateStorage(e){if(!e)return;let t=e.searchHistory.filter(e=>{let t=i()(e.validUntil);return i()().isSameOrBefore(t)});e.searchHistory=t}}))},98563:(e,t,a)=>{"use strict";a.d(t,{R:()=>n});var s=a(69133),r=a(92377),i=a(72266);let n=(0,s.Ue)()((0,r.tJ)(e=>({currency:"IDR",setCurrency:t=>e({currency:t}),isLoading:!0,setIsLoading:t=>e({isLoading:t})}),{name:"seekers-settings",storage:{getItem:e=>{let t=i.Z.get(e);return t?JSON.parse(t):void 0},setItem:(e,t)=>{i.Z.set(e,JSON.stringify(t),{expires:7,path:"/"})},removeItem:e=>{i.Z.remove(e)}},onRehydrateStorage:()=>e=>{e&&e.setIsLoading(!1)}}))},92894:(e,t,a)=>{"use strict";a.d(t,{L:()=>d,_:()=>c});var s=a(34200),r=a(69133),i=a(92377),n=a(72266),l=a(97482);let o={getItem:e=>{let t=n.Z.get(e);return t?JSON.parse(t):null},setItem:(e,t)=>{n.Z.set(e,JSON.stringify(t),{expires:7})},removeItem:e=>{n.Z.remove(e)}},c={accounts:{about:"",citizenship:"",credit:{amount:0,updatedAt:""},facebookSocial:"",firstName:"",image:"",isSubscriber:!1,language:"",lastName:"",membership:l.B9.free,twitterSocial:"",address:"",chat:{current:0,max:0},zoomFeature:s.$8},has2FA:!1,email:"",code:"",isActive:!1,phoneNumber:"",phoneCode:"",type:"SEEKER",setting:{messageNotif:!1,newsletterNotif:!1,priceAlertNotif:!1,propertyNotif:!1,soundNotif:!1,specialOfferNotif:!1,surveyNotif:!1}},d=(0,r.Ue)()((0,i.tJ)(e=>({role:void 0,setRole:t=>e({role:t}),seekers:c,setSeekers:t=>e({seekers:t}),tempSubscribtionLevel:0,setTempSubscribtionLevel:t=>e({tempSubscribtionLevel:t}),clearUser:()=>e(()=>({seekers:c}))}),{name:"user",storage:(0,i.FL)(()=>o)}))},56886:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(45347).createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\components\navbar\seekers-navbar-2.tsx#default`)},79438:(e,t,a)=>{"use strict";a.d(t,{Z:()=>i});var s=a(72051),r=a(37170);function i(e){return s.jsx("div",{...e,ref:e.ref,className:(0,r.cn)("xl:max-w-screen-2xl max-md:px-4 px-8 mx-auto space-y-12",e.className),children:e.children})}},92898:(e,t,a)=>{"use strict";a.d(t,{$U:()=>y,Fk:()=>g,Fq:()=>l,GA:()=>m,OM:()=>u,Ph:()=>x,Qk:()=>f,W5:()=>w,Y8:()=>c,bY:()=>A,in:()=>d,mU:()=>n,o2:()=>v,rr:()=>o,rv:()=>p,s0:()=>h}),a(72051);var s=a(12067),r=a(68349),i=a(76400);let n="/",l="/profile",o="/s",c="/favorites",d="/message",u="/subscription",m="/plan",p="/billing",f="/notification",h="/security",x="/privacy-policy",g="/terms-of-use",A="/contact-us",y="/user-data-deletion",w="/about-us",v="/posts";s.Z,r.Z,i.Z,s.Z,r.Z},37170:(e,t,a)=>{"use strict";a.d(t,{cn:()=>i,yT:()=>n});var s=a(36272);a(26767);var r=a(51472);function i(...e){return(0,r.m6)((0,s.W)(e))}let n=e=>e.charAt(0).toUpperCase()+e.slice(1)},62648:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s={src:"/_next/static/media/property-seekers-main-logo.2a8a0666.png",height:128,width:473,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAMAAABSSm3fAAAADFBMVEW1i1SxiVK1jFW1jFUX2lE4AAAABHRSTlM+F2AiCpN2vgAAAAlwSFlzAAALEwAACxMBAJqcGAAAABhJREFUeJwFwQEBAAAIwyDm+3cWoFrOYT0AhwAQ9FQy9wAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:2}}};