"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7060],{53795:function(e,t,n){var r=n(33910),o=n(99138),a=n(14814);r.default,o.default,t.os=a.default},50628:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var r=n(52680),o=n(2265),a=n(12579),l=n(99448),i=n(92417),u=o&&o.__esModule?o:{default:o};let c=o.forwardRef(function(e,t){let{locale:n,localePrefix:o,...c}=e,f=a.default(),d=n||f,s=l.getLocalePrefix(d,o);return u.default.createElement(i.default,r.extends({ref:t,locale:d,localePrefixMode:o.mode,prefix:s},c))});c.displayName="ClientLink",t.default=c},99138:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var r=n(52680),o=n(2265),a=n(12579),l=n(99415),i=n(42571),u=n(50628),c=n(92219),f=n(6188),d=n(26900),s=o&&o.__esModule?o:{default:o};t.default=function(e){let t=l.receiveRoutingConfig(e),n=l.receiveLocaleCookie(e.localeCookie);function p(){let e=a.default();if(!t.locales.includes(e))throw Error(void 0);return e}let m=o.forwardRef(function(e,o){let{href:a,locale:l,...c}=e,f=p(),d=l||f;return s.default.createElement(u.default,r.extends({ref:o,href:i.compileLocalizedPathname({locale:d,pathname:a,params:"object"==typeof a?a.params:void 0,pathnames:t.pathnames}),locale:l,localeCookie:n,localePrefix:t.localePrefix},c))});function v(e){let{href:n,locale:r}=e;return i.compileLocalizedPathname({...i.normalizeNameOrNameWithParams(n),locale:r,pathnames:t.pathnames})}return m.displayName="Link",{Link:m,redirect:function(e){let n=v({href:e,locale:p()});for(var r=arguments.length,o=Array(r>1?r-1:0),a=1;a<r;a++)o[a-1]=arguments[a];return c.clientRedirect({pathname:n,localePrefix:t.localePrefix},...o)},permanentRedirect:function(e){let n=v({href:e,locale:p()});for(var r=arguments.length,o=Array(r>1?r-1:0),a=1;a<r;a++)o[a-1]=arguments[a];return c.clientPermanentRedirect({pathname:n,localePrefix:t.localePrefix},...o)},usePathname:function(){let e=f.default(t.localePrefix),n=p();return o.useMemo(()=>e?i.getRoute(n,e,t.pathnames):e,[n,e])},useRouter:function(){let e=d.default(t.localePrefix,n),r=p();return o.useMemo(()=>({...e,push(t){for(var n,o=arguments.length,a=Array(o>1?o-1:0),l=1;l<o;l++)a[l-1]=arguments[l];let i=v({href:t,locale:(null===(n=a[0])||void 0===n?void 0:n.locale)||r});return e.push(i,...a)},replace(t){for(var n,o=arguments.length,a=Array(o>1?o-1:0),l=1;l<o;l++)a[l-1]=arguments[l];let i=v({href:t,locale:(null===(n=a[0])||void 0===n?void 0:n.locale)||r});return e.replace(i,...a)},prefetch(t){for(var n,o=arguments.length,a=Array(o>1?o-1:0),l=1;l<o;l++)a[l-1]=arguments[l];let i=v({href:t,locale:(null===(n=a[0])||void 0===n?void 0:n.locale)||r});return e.prefetch(i,...a)}}),[e,r])},getPathname:v}}},14814:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var r=n(99376),o=n(2265),a=n(12579),l=n(44986),i=n(56146),u=n(42571),c=n(6188);t.default=function(e){function t(){return a.default()}let{Link:n,config:f,getPathname:d,...s}=l.default(t,e);return{...s,Link:n,usePathname:function(){let e=c.default(f.localePrefix),n=t();return o.useMemo(()=>e&&f.pathnames?u.getRoute(n,e,f.pathnames):e,[n,e])},useRouter:function(){let e=r.useRouter(),n=t(),a=r.usePathname();return o.useMemo(()=>{function t(e){return function(t,r){let{locale:o,...l}=r||{},u=[d({href:t,locale:o||n,domain:window.location.host})];Object.keys(l).length>0&&u.push(l),e(...u),i.default(f.localeCookie,a,n,o)}}return{...e,push:t(e.push),replace:t(e.replace),prefetch:t(e.prefetch)}},[n,a,e])},getPathname:d}}},33910:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var r=n(52680),o=n(2265),a=n(99415),l=n(50628),i=n(92219),u=n(6188),c=n(26900),f=o&&o.__esModule?o:{default:o};t.default=function(e){let t=a.receiveLocalePrefixConfig(null==e?void 0:e.localePrefix),n=a.receiveLocaleCookie(null==e?void 0:e.localeCookie),d=o.forwardRef(function(e,o){return f.default.createElement(l.default,r.extends({ref:o,localeCookie:n,localePrefix:t},e))});return d.displayName="Link",{Link:d,redirect:function(e){for(var n=arguments.length,r=Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return i.clientRedirect({pathname:e,localePrefix:t},...r)},permanentRedirect:function(e){for(var n=arguments.length,r=Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return i.clientPermanentRedirect({pathname:e,localePrefix:t},...r)},usePathname:function(){return u.default(t)},useRouter:function(){return c.default(t,n)}}}},92219:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var r=n(12579),o=n(90050);function a(e){return function(t){let n;try{n=r.default()}catch(e){throw e}for(var o=arguments.length,a=Array(o>1?o-1:0),l=1;l<o;l++)a[l-1]=arguments[l];return e({...t,locale:n},...a)}}let l=a(o.baseRedirect),i=a(o.basePermanentRedirect);t.clientPermanentRedirect=i,t.clientRedirect=l},6188:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var r=n(99376),o=n(2265),a=n(12579),l=n(99448);t.default=function(e){let t=r.usePathname(),n=a.default();return o.useMemo(()=>{if(!t)return t;let r=l.getLocalePrefix(n,e);return l.hasPathnamePrefixed(r,t)?l.unprefixPathname(t,r):t},[n,e,t])}},26900:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var r=n(99376),o=n(2265),a=n(12579),l=n(99448),i=n(56146),u=n(42571);t.default=function(e,t){let n=r.useRouter(),c=a.default(),f=r.usePathname();return o.useMemo(()=>{function r(n){return function(r,o){let{locale:a,...d}=o||{};i.default(t,f,c,a);let s=[function(t,n){let r=window.location.pathname,o=u.getBasePath(f);o&&(r=r.replace(o,""));let a=n||c,i=l.getLocalePrefix(a,e);return l.localizeHref(t,a,c,r,i)}(r,a)];return Object.keys(d).length>0&&s.push(d),n(...s)}}return{...n,push:r(n.push),replace:r(n.replace),prefetch:r(n.prefetch)}},[c,t,e,f,n])}},23740:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var r=n(52680),o=n(27648),a=n(99376),l=n(2265),i=n(12579),u=n(56146);function c(e){return e&&e.__esModule?e:{default:e}}var f=c(o),d=c(l),s=l.forwardRef(function(e,t){let{defaultLocale:n,href:o,locale:c,localeCookie:s,onClick:p,prefetch:m,unprefixed:v,...h}=e,y=i.default(),g=c!==y,b=c||y,P=function(){let[e,t]=l.useState();return l.useEffect(()=>{t(window.location.host)},[]),e}(),x=P&&v&&(v.domains[P]===b||!Object.keys(v.domains).includes(P)&&y===n&&!c)?v.pathname:o,w=a.usePathname();return g&&(m=!1),d.default.createElement(f.default,r.extends({ref:t,href:x,hrefLang:g?c:void 0,onClick:function(e){u.default(s,w,y,c),p&&p(e)},prefetch:m},h))});t.default=s},92417:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var r=n(52680),o=n(99376),a=n(2265),l=n(12579),i=n(99448),u=n(23740),c=a&&a.__esModule?a:{default:a};let f=a.forwardRef(function(e,t){let{href:n,locale:f,localeCookie:d,localePrefixMode:s,prefix:p,...m}=e,v=o.usePathname(),h=l.default(),y=f!==h,[g,b]=a.useState(()=>i.isLocalizableHref(n)&&("never"!==s||y)?i.prefixHref(n,p):n);return a.useEffect(()=>{v&&b(i.localizeHref(n,f,h,v,p))},[h,n,f,v,p]),c.default.createElement(u.default,r.extends({ref:t,href:g,locale:f,localeCookie:d},m))});f.displayName="ClientLink",t.default=f},44986:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var r=n(52680),o=n(99376),a=n(2265),l=n(99415),i=n(99448),u=n(23740),c=n(42571),f=a&&a.__esModule?a:{default:a};t.default=function(e,t){let n=l.receiveRoutingConfig(t||{}),d=n.pathnames,s="as-needed"===n.localePrefix.mode&&n.domains||void 0,p=a.forwardRef(function(t,o){let l,c,{href:p,locale:v,...h}=t;"object"==typeof p?(l=p.pathname,c=p.params):l=p;let y=i.isLocalizableHref(p),g=e(),b=g instanceof Promise?a.use(g):g,P=y?m({locale:v||b,href:null==d?l:{pathname:l,params:c}},null!=v||s||void 0):l;return f.default.createElement(u.default,r.extends({ref:o,defaultLocale:n.defaultLocale,href:"object"==typeof p?{...p,pathname:P}:P,locale:v,localeCookie:n.localeCookie,unprefixed:s&&y?{domains:n.domains.reduce((e,t)=>(e[t.domain]=t.defaultLocale,e),{}),pathname:m({locale:b,href:null==d?l:{pathname:l,params:c}},!1)}:void 0},h))});function m(e,t){let r;let{href:o,locale:a}=e;return null==d?"object"==typeof o?(r=o.pathname,o.query&&(r+=c.serializeSearchParams(o.query))):r=o:r=c.compileLocalizedPathname({locale:a,...c.normalizeNameOrNameWithParams(o),pathnames:n.pathnames}),c.applyPathnamePrefix(r,a,n,e.domain,t)}function v(e){return function(t){for(var n=arguments.length,r=Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return e(m(t,t.domain?void 0:s),...r)}}return{config:n,Link:p,redirect:v(o.redirect),permanentRedirect:v(o.permanentRedirect),getPathname:m}}},90050:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var r=n(99376),o=n(99448);function a(e){return function(t){let n=o.getLocalePrefix(t.locale,t.localePrefix),r="never"!==t.localePrefix.mode&&o.isLocalizableHref(t.pathname)?o.prefixPathname(n,t.pathname):t.pathname;for(var a=arguments.length,l=Array(a>1?a-1:0),i=1;i<a;i++)l[i-1]=arguments[i];return e(r,...l)}}let l=a(r.redirect),i=a(r.permanentRedirect);t.basePermanentRedirect=i,t.baseRedirect=l},56146:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var r=n(42571);t.default=function(e,t,n,o){if(!e||!(o!==n&&null!=o)||!t)return;let a=r.getBasePath(t),{name:l,...i}=e;i.path||(i.path=""!==a?a:"/");let u="".concat(l,"=").concat(o,";");for(let[e,t]of Object.entries(i))u+="".concat("maxAge"===e?"max-age":e),"boolean"!=typeof t&&(u+="="+t),u+=";";document.cookie=u}},42571:function(e,t,n){Object.defineProperty(t,"__esModule",{value:!0});var r=n(99448);function o(e){let t=new URLSearchParams;for(let[n,r]of Object.entries(e))Array.isArray(r)?r.forEach(e=>{t.append(n,String(e))}):t.set(n,String(r));return"?"+t.toString()}t.applyPathnamePrefix=function(e,t,n,o,a){let l;let{mode:i}=n.localePrefix;if(void 0!==a)l=a;else if(r.isLocalizableHref(e)){if("always"===i)l=!0;else if("as-needed"===i){let e=n.defaultLocale;if(n.domains){let t=n.domains.find(e=>e.domain===o);t&&(e=t.defaultLocale)}l=e!==t}}return l?r.prefixPathname(r.getLocalePrefix(t,n.localePrefix),e):e},t.compileLocalizedPathname=function(e){let{pathname:t,locale:n,params:a,pathnames:l,query:i}=e;function u(e){let t=l[e];return t||(t=e),t}function c(e){let t="string"==typeof e?e:e[n];return a&&Object.entries(a).forEach(e=>{let n,r,[o,a]=e;Array.isArray(a)?(n="(\\[)?\\[...".concat(o,"\\](\\])?"),r=a.map(e=>String(e)).join("/")):(n="\\[".concat(o,"\\]"),r=String(a)),t=t.replace(RegExp(n,"g"),r)}),t=t.replace(/\[\[\.\.\..+\]\]/g,""),t=r.normalizeTrailingSlash(t),i&&(t+=o(i)),t}if("string"==typeof t)return c(u(t));{let{pathname:e,...n}=t;return{...n,pathname:c(u(e))}}},t.getBasePath=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window.location.pathname;return"/"===e?t:t.replace(e,"")},t.getRoute=function(e,t,n){let o=r.getSortedPathnames(Object.keys(n)),a=decodeURI(t);for(let t of o){let o=n[t];if("string"==typeof o){if(r.matchesPathname(o,a))return t}else if(r.matchesPathname(o[e],a))return t}return t},t.normalizeNameOrNameWithParams=function(e){return"string"==typeof e?{pathname:e}:e},t.serializeSearchParams=o},99415:function(e,t){function n(e){return!(null!=e&&!e)&&{name:"NEXT_LOCALE",maxAge:31536e3,sameSite:"lax",..."object"==typeof e&&e}}function r(e){return"object"==typeof e?e:{mode:e||"always"}}Object.defineProperty(t,"__esModule",{value:!0}),t.receiveLocaleCookie=n,t.receiveLocalePrefixConfig=r,t.receiveRoutingConfig=function(e){var t,o;return{...e,localePrefix:r(e.localePrefix),localeCookie:n(e.localeCookie),localeDetection:null===(t=e.localeDetection)||void 0===t||t,alternateLinks:null===(o=e.alternateLinks)||void 0===o||o}}},99448:function(e,t,n){var r=n(25566);function o(e){return("object"==typeof e?null==e.host&&null==e.hostname:!/^[a-z]+:/i.test(e))&&!function(e){let t="object"==typeof e?e.pathname:e;return null!=t&&!t.startsWith("/")}(e)}function a(e,t){let n;return"string"==typeof e?n=l(t,e):(n={...e},e.pathname&&(n.pathname=l(t,e.pathname))),n}function l(e,t){let n=e;return/^\/(\?.*)?$/.test(t)&&(t=t.slice(1)),n+=t}function i(e,t){return t===e||t.startsWith("".concat(e,"/"))}function u(e){let t=function(){try{return"true"===r.env._next_intl_trailing_slash}catch(e){return!1}}();if("/"!==e){let n=e.endsWith("/");t&&!n?e+="/":!t&&n&&(e=e.slice(0,-1))}return e}function c(e){let t=e.replace(/\[\[(\.\.\.[^\]]+)\]\]/g,"?(.*)").replace(/\[(\.\.\.[^\]]+)\]/g,"(.+)").replace(/\[([^\]]+)\]/g,"([^/]+)");return new RegExp("^".concat(t,"$"))}function f(e){return e.includes("[[...")}function d(e){return e.includes("[...")}function s(e){return e.includes("[")}function p(e,t){let n=e.split("/"),r=t.split("/"),o=Math.max(n.length,r.length);for(let e=0;e<o;e++){let t=n[e],o=r[e];if(!t&&o)return -1;if(t&&!o)return 1;if(t||o){if(!s(t)&&s(o))return -1;if(s(t)&&!s(o))return 1;if(!d(t)&&d(o))return -1;if(d(t)&&!d(o))return 1;if(!f(t)&&f(o))return -1;if(f(t)&&!f(o))return 1}}return 0}Object.defineProperty(t,"__esModule",{value:!0}),t.getLocalePrefix=function(e,t){var n;return"never"!==t.mode&&(null===(n=t.prefixes)||void 0===n?void 0:n[e])||"/"+e},t.getSortedPathnames=function(e){return e.sort(p)},t.hasPathnamePrefixed=i,t.isLocalizableHref=o,t.localizeHref=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t,r=arguments.length>3?arguments[3]:void 0,l=arguments.length>4?arguments[4]:void 0;if(!o(e))return e;let u=i(l,r);return(t!==n||u)&&null!=l?a(e,l):e},t.matchesPathname=function(e,t){let n=u(e),r=u(t);return c(n).test(r)},t.normalizeTrailingSlash=u,t.prefixHref=a,t.prefixPathname=l,t.templateToRegex=c,t.unprefixPathname=function(e,t){return e.replace(new RegExp("^".concat(t)),"")||"/"}},60703:function(e,t,n){n.d(t,{Z:function(){return k}});var r=n(5853),o=n(2265),a=n(85770),l=n(17325),i=(0,n(31412)._)(),u=function(){},c=o.forwardRef(function(e,t){var n=o.useRef(null),a=o.useState({onScrollCapture:u,onWheelCapture:u,onTouchMoveCapture:u}),c=a[0],f=a[1],d=e.forwardProps,s=e.children,p=e.className,m=e.removeScrollBar,v=e.enabled,h=e.shards,y=e.sideCar,g=e.noIsolation,b=e.inert,P=e.allowPinchZoom,x=e.as,w=e.gapMode,E=(0,r._T)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),C=(0,l.q)([n,t]),R=(0,r.pi)((0,r.pi)({},E),c);return o.createElement(o.Fragment,null,v&&o.createElement(y,{sideCar:i,removeScrollBar:m,shards:h,noIsolation:g,inert:b,setCallbacks:f,allowPinchZoom:!!P,lockRef:n,gapMode:w}),d?o.cloneElement(o.Children.only(s),(0,r.pi)((0,r.pi)({},R),{ref:C})):o.createElement(void 0===x?"div":x,(0,r.pi)({},R,{className:p,ref:C}),s))});c.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},c.classNames={fullWidth:a.zi,zeroRight:a.pF};var f=n(49085),d=n(5517),s=n(18704),p=!1;if("undefined"!=typeof window)try{var m=Object.defineProperty({},"passive",{get:function(){return p=!0,!0}});window.addEventListener("test",m,m),window.removeEventListener("test",m,m)}catch(e){p=!1}var v=!!p&&{passive:!1},h=function(e,t){var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===n[t])},y=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),g(e,r)){var o=b(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},g=function(e,t){return"v"===e?h(t,"overflowY"):h(t,"overflowX")},b=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},P=function(e,t,n,r,o){var a,l=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),i=l*r,u=n.target,c=t.contains(u),f=!1,d=i>0,s=0,p=0;do{var m=b(e,u),v=m[0],h=m[1]-m[2]-l*v;(v||h)&&g(e,u)&&(s+=h,p+=v),u instanceof ShadowRoot?u=u.host:u=u.parentNode}while(!c&&u!==document.body||c&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(s)||!o&&i>s)?f=!0:!d&&(o&&1>Math.abs(p)||!o&&-i>p)&&(f=!0),f},x=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},w=function(e){return[e.deltaX,e.deltaY]},E=function(e){return e&&"current"in e?e.current:e},C=0,R=[],L=(0,f.L)(i,function(e){var t=o.useRef([]),n=o.useRef([0,0]),a=o.useRef(),l=o.useState(C++)[0],i=o.useState(s.Ws)[0],u=o.useRef(e);o.useEffect(function(){u.current=e},[e]),o.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(l));var t=(0,r.ev)([e.lockRef.current],(e.shards||[]).map(E),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(l))}),function(){document.body.classList.remove("block-interactivity-".concat(l)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(l))})}}},[e.inert,e.lockRef.current,e.shards]);var c=o.useCallback(function(e,t){if("touches"in e&&2===e.touches.length)return!u.current.allowPinchZoom;var r,o=x(e),l=n.current,i="deltaX"in e?e.deltaX:l[0]-o[0],c="deltaY"in e?e.deltaY:l[1]-o[1],f=e.target,d=Math.abs(i)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===f.type)return!1;var s=y(d,f);if(!s)return!0;if(s?r=d:(r="v"===d?"h":"v",s=y(d,f)),!s)return!1;if(!a.current&&"changedTouches"in e&&(i||c)&&(a.current=r),!r)return!0;var p=a.current||r;return P(p,t,e,"h"===p?i:c,!0)},[]),f=o.useCallback(function(e){if(R.length&&R[R.length-1]===i){var n="deltaY"in e?w(e):x(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(u.current.shards||[]).map(E).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?c(e,o[0]):!u.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),p=o.useCallback(function(e,n,r,o){var a={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),m=o.useCallback(function(e){n.current=x(e),a.current=void 0},[]),h=o.useCallback(function(t){p(t.type,w(t),t.target,c(t,e.lockRef.current))},[]),g=o.useCallback(function(t){p(t.type,x(t),t.target,c(t,e.lockRef.current))},[]);o.useEffect(function(){return R.push(i),e.setCallbacks({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:g}),document.addEventListener("wheel",f,v),document.addEventListener("touchmove",f,v),document.addEventListener("touchstart",m,v),function(){R=R.filter(function(e){return e!==i}),document.removeEventListener("wheel",f,v),document.removeEventListener("touchmove",f,v),document.removeEventListener("touchstart",m,v)}},[]);var b=e.removeScrollBar,L=e.inert;return o.createElement(o.Fragment,null,L?o.createElement(i,{styles:"\n  .block-interactivity-".concat(l," {pointer-events: none;}\n  .allow-interactivity-").concat(l," {pointer-events: all;}\n")}):null,b?o.createElement(d.jp,{gapMode:e.gapMode}):null)}),S=o.forwardRef(function(e,t){return o.createElement(c,(0,r.pi)({},e,{ref:t,sideCar:L}))});S.classNames=c.classNames;var k=S},62484:function(e,t,n){n.d(t,{u:function(){return r}});function r(e,[t,n]){return Math.min(n,Math.max(t,e))}},6741:function(e,t,n){n.d(t,{M:function(){return r}});function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}},71605:function(e,t,n){n.d(t,{B:function(){return d}});var r=n(2265),o=n(73966),a=n(98575),l=n(57437),i=r.forwardRef((e,t)=>{let{children:n,...o}=e,a=r.Children.toArray(n),i=a.find(f);if(i){let e=i.props.children,n=a.map(t=>t!==i?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,l.jsx)(u,{...o,ref:t,children:r.isValidElement(e)?r.cloneElement(e,void 0,n):null})}return(0,l.jsx)(u,{...o,ref:t,children:n})});i.displayName="Slot";var u=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){let e,l;let i=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref;return r.cloneElement(n,{...function(e,t){let n={...t};for(let r in t){let o=e[r],a=t[r];/^on[A-Z]/.test(r)?o&&a?n[r]=(...e)=>{a(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...a}:"className"===r&&(n[r]=[o,a].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props),ref:t?(0,a.F)(t,i):i})}return r.Children.count(n)>1?r.Children.only(null):null});u.displayName="SlotClone";var c=({children:e})=>(0,l.jsx)(l.Fragment,{children:e});function f(e){return r.isValidElement(e)&&e.type===c}function d(e){let t=e+"CollectionProvider",[n,u]=(0,o.b)(t),[c,f]=n(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:n}=e,o=r.useRef(null),a=r.useRef(new Map).current;return(0,l.jsx)(c,{scope:t,itemMap:a,collectionRef:o,children:n})};d.displayName=t;let s=e+"CollectionSlot",p=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=f(s,n),u=(0,a.e)(t,o.collectionRef);return(0,l.jsx)(i,{ref:u,children:r})});p.displayName=s;let m=e+"CollectionItemSlot",v="data-radix-collection-item",h=r.forwardRef((e,t)=>{let{scope:n,children:o,...u}=e,c=r.useRef(null),d=(0,a.e)(t,c),s=f(m,n);return r.useEffect(()=>(s.itemMap.set(c,{ref:c,...u}),()=>void s.itemMap.delete(c))),(0,l.jsx)(i,{[v]:"",ref:d,children:o})});return h.displayName=m,[{Provider:d,Slot:p,ItemSlot:h},function(t){let n=f(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(v,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},u]}},29114:function(e,t,n){n.d(t,{gm:function(){return a}});var r=n(2265);n(57437);var o=r.createContext(void 0);function a(e){let t=r.useContext(o);return e||t||"ltr"}},22308:function(e,t,n){n.d(t,{I0:function(){return g},XB:function(){return p},fC:function(){return y}});var r,o=n(2265),a=n(6741),l=n(82912),i=n(98575),u=n(26606),c=n(91096),f=n(57437),d="dismissableLayer.update",s=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),p=o.forwardRef((e,t)=>{var n,p;let{disableOutsidePointerEvents:m=!1,onEscapeKeyDown:y,onPointerDownOutside:g,onFocusOutside:b,onInteractOutside:P,onDismiss:x,...w}=e,E=o.useContext(s),[C,R]=o.useState(null),L=null!==(p=null==C?void 0:C.ownerDocument)&&void 0!==p?p:null===(n=globalThis)||void 0===n?void 0:n.document,[,S]=o.useState({}),k=(0,i.e)(t,e=>R(e)),M=Array.from(E.layers),[j]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),_=M.indexOf(j),O=C?M.indexOf(C):-1,A=E.layersWithOutsidePointerEventsDisabled.size>0,N=O>=_,W=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,u.W)(e),a=o.useRef(!1),l=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!a.current){let t=function(){h("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);a.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>a.current=!0}}(e=>{let t=e.target,n=[...E.branches].some(e=>e.contains(t));!N||n||(null==g||g(e),null==P||P(e),e.defaultPrevented||null==x||x())},L),T=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,u.W)(e),a=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!a.current&&h("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>a.current=!0,onBlurCapture:()=>a.current=!1}}(e=>{let t=e.target;[...E.branches].some(e=>e.contains(t))||(null==b||b(e),null==P||P(e),e.defaultPrevented||null==x||x())},L);return(0,c.e)(e=>{O!==E.layers.size-1||(null==y||y(e),!e.defaultPrevented&&x&&(e.preventDefault(),x()))},L),o.useEffect(()=>{if(C)return m&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(r=L.body.style.pointerEvents,L.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(C)),E.layers.add(C),v(),()=>{m&&1===E.layersWithOutsidePointerEventsDisabled.size&&(L.body.style.pointerEvents=r)}},[C,L,m,E]),o.useEffect(()=>()=>{C&&(E.layers.delete(C),E.layersWithOutsidePointerEventsDisabled.delete(C),v())},[C,E]),o.useEffect(()=>{let e=()=>S({});return document.addEventListener(d,e),()=>document.removeEventListener(d,e)},[]),(0,f.jsx)(l.WV.div,{...w,ref:k,style:{pointerEvents:A?N?"auto":"none":void 0,...e.style},onFocusCapture:(0,a.M)(e.onFocusCapture,T.onFocusCapture),onBlurCapture:(0,a.M)(e.onBlurCapture,T.onBlurCapture),onPointerDownCapture:(0,a.M)(e.onPointerDownCapture,W.onPointerDownCapture)})});p.displayName="DismissableLayer";var m=o.forwardRef((e,t)=>{let n=o.useContext(s),r=o.useRef(null),a=(0,i.e)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,f.jsx)(l.WV.div,{...e,ref:a})});function v(){let e=new CustomEvent(d);document.dispatchEvent(e)}function h(e,t,n,r){let{discrete:o}=r,a=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&a.addEventListener(e,t,{once:!0}),o?(0,l.jH)(a,i):a.dispatchEvent(i)}m.displayName="DismissableLayerBranch";var y=p,g=m},86097:function(e,t,n){n.d(t,{EW:function(){return a}});var r=n(2265),o=0;function a(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:l()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:l()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function l(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.cssText="outline: none; opacity: 0; position: fixed; pointer-events: none",e}},99103:function(e,t,n){let r;n.d(t,{M:function(){return s}});var o=n(2265),a=n(98575),l=n(82912),i=n(26606),u=n(57437),c="focusScope.autoFocusOnMount",f="focusScope.autoFocusOnUnmount",d={bubbles:!1,cancelable:!0},s=o.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:s,onUnmountAutoFocus:y,...g}=e,[b,P]=o.useState(null),x=(0,i.W)(s),w=(0,i.W)(y),E=o.useRef(null),C=(0,a.e)(t,e=>P(e)),R=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(r){let e=function(e){if(R.paused||!b)return;let t=e.target;b.contains(t)?E.current=t:v(E.current,{select:!0})},t=function(e){if(R.paused||!b)return;let t=e.relatedTarget;null===t||b.contains(t)||v(E.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&v(b)});return b&&n.observe(b,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,b,R.paused]),o.useEffect(()=>{if(b){h.add(R);let e=document.activeElement;if(!b.contains(e)){let t=new CustomEvent(c,d);b.addEventListener(c,x),b.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(v(r,{select:t}),document.activeElement!==n)return}(p(b).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&v(b))}return()=>{b.removeEventListener(c,x),setTimeout(()=>{let t=new CustomEvent(f,d);b.addEventListener(f,w),b.dispatchEvent(t),t.defaultPrevented||v(null!=e?e:document.body,{select:!0}),b.removeEventListener(f,w),h.remove(R)},0)}}},[b,x,w,R]);let L=o.useCallback(e=>{if(!n&&!r||R.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,a]=function(e){let t=p(e);return[m(t,e),m(t.reverse(),e)]}(t);r&&a?e.shiftKey||o!==a?e.shiftKey&&o===r&&(e.preventDefault(),n&&v(a,{select:!0})):(e.preventDefault(),n&&v(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,R.paused]);return(0,u.jsx)(l.WV.div,{tabIndex:-1,...g,ref:C,onKeyDown:L})});function p(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function m(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function v(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}s.displayName="FocusScope";var h=(r=[],{add(e){let t=r[0];e!==t&&(null==t||t.pause()),(r=y(r,e)).unshift(e)},remove(e){var t;null===(t=(r=y(r,e))[0])||void 0===t||t.resume()}});function y(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},21107:function(e,t,n){n.d(t,{ee:function(){return A},Eh:function(){return W},VY:function(){return N},fC:function(){return O},D7:function(){return h}});var r=n(2265),o=n(97859),a=n(50032),l=n(82912),i=n(57437),u=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...a}=e;return(0,i.jsx)(l.WV.svg,{...a,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,i.jsx)("polygon",{points:"0,0 30,0 15,10"})})});u.displayName="Arrow";var c=n(98575),f=n(73966),d=n(26606),s=n(61188),p=n(90420),m="Popper",[v,h]=(0,f.b)(m),[y,g]=v(m),b=e=>{let{__scopePopper:t,children:n}=e,[o,a]=r.useState(null);return(0,i.jsx)(y,{scope:t,anchor:o,onAnchorChange:a,children:n})};b.displayName=m;var P="PopperAnchor",x=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...a}=e,u=g(P,n),f=r.useRef(null),d=(0,c.e)(t,f);return r.useEffect(()=>{u.onAnchorChange((null==o?void 0:o.current)||f.current)}),o?null:(0,i.jsx)(l.WV.div,{...a,ref:d})});x.displayName=P;var w="PopperContent",[E,C]=v(w),R=r.forwardRef((e,t)=>{var n,u,f,m,v,h,y,b;let{__scopePopper:P,side:x="bottom",sideOffset:C=0,align:R="center",alignOffset:L=0,arrowPadding:S=0,avoidCollisions:k=!0,collisionBoundary:O=[],collisionPadding:A=0,sticky:N="partial",hideWhenDetached:W=!1,updatePositionStrategy:T="optimized",onPlaced:z,...D}=e,F=g(w,P),[B,H]=r.useState(null),I=(0,c.e)(t,e=>H(e)),[Y,V]=r.useState(null),X=(0,p.t)(Y),K=null!==(y=null==X?void 0:X.width)&&void 0!==y?y:0,q=null!==(b=null==X?void 0:X.height)&&void 0!==b?b:0,Z="number"==typeof A?A:{top:0,right:0,bottom:0,left:0,...A},U=Array.isArray(O)?O:[O],$=U.length>0,G={padding:Z,boundary:U.filter(M),altBoundary:$},{refs:J,floatingStyles:Q,placement:ee,isPositioned:et,middlewareData:en}=(0,o.YF)({strategy:"fixed",placement:x+("center"!==R?"-"+R:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,a.Me)(...t,{animationFrame:"always"===T})},elements:{reference:F.anchor},middleware:[(0,o.cv)({mainAxis:C+q,alignmentAxis:L}),k&&(0,o.uY)({mainAxis:!0,crossAxis:!1,limiter:"partial"===N?(0,o.dr)():void 0,...G}),k&&(0,o.RR)({...G}),(0,o.dp)({...G,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:a,height:l}=n.reference,i=t.floating.style;i.setProperty("--radix-popper-available-width","".concat(r,"px")),i.setProperty("--radix-popper-available-height","".concat(o,"px")),i.setProperty("--radix-popper-anchor-width","".concat(a,"px")),i.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),Y&&(0,o.x7)({element:Y,padding:S}),j({arrowWidth:K,arrowHeight:q}),W&&(0,o.Cp)({strategy:"referenceHidden",...G})]}),[er,eo]=_(ee),ea=(0,d.W)(z);(0,s.b)(()=>{et&&(null==ea||ea())},[et,ea]);let el=null===(n=en.arrow)||void 0===n?void 0:n.x,ei=null===(u=en.arrow)||void 0===u?void 0:u.y,eu=(null===(f=en.arrow)||void 0===f?void 0:f.centerOffset)!==0,[ec,ef]=r.useState();return(0,s.b)(()=>{B&&ef(window.getComputedStyle(B).zIndex)},[B]),(0,i.jsx)("div",{ref:J.setFloating,"data-radix-popper-content-wrapper":"",style:{...Q,transform:et?Q.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:ec,"--radix-popper-transform-origin":[null===(m=en.transformOrigin)||void 0===m?void 0:m.x,null===(v=en.transformOrigin)||void 0===v?void 0:v.y].join(" "),...(null===(h=en.hide)||void 0===h?void 0:h.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,i.jsx)(E,{scope:P,placedSide:er,onArrowChange:V,arrowX:el,arrowY:ei,shouldHideArrow:eu,children:(0,i.jsx)(l.WV.div,{"data-side":er,"data-align":eo,...D,ref:I,style:{...D.style,animation:et?void 0:"none"}})})})});R.displayName=w;var L="PopperArrow",S={top:"bottom",right:"left",bottom:"top",left:"right"},k=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=C(L,n),a=S[o.placedSide];return(0,i.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[a]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,i.jsx)(u,{...r,ref:t,style:{...r.style,display:"block"}})})});function M(e){return null!==e}k.displayName=L;var j=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,a,l;let{placement:i,rects:u,middlewareData:c}=t,f=(null===(n=c.arrow)||void 0===n?void 0:n.centerOffset)!==0,d=f?0:e.arrowWidth,s=f?0:e.arrowHeight,[p,m]=_(i),v={start:"0%",center:"50%",end:"100%"}[m],h=(null!==(a=null===(r=c.arrow)||void 0===r?void 0:r.x)&&void 0!==a?a:0)+d/2,y=(null!==(l=null===(o=c.arrow)||void 0===o?void 0:o.y)&&void 0!==l?l:0)+s/2,g="",b="";return"bottom"===p?(g=f?v:"".concat(h,"px"),b="".concat(-s,"px")):"top"===p?(g=f?v:"".concat(h,"px"),b="".concat(u.floating.height+s,"px")):"right"===p?(g="".concat(-s,"px"),b=f?v:"".concat(y,"px")):"left"===p&&(g="".concat(u.floating.width+s,"px"),b=f?v:"".concat(y,"px")),{data:{x:g,y:b}}}});function _(e){let[t,n="center"]=e.split("-");return[t,n]}var O=b,A=x,N=R,W=k},83832:function(e,t,n){n.d(t,{h:function(){return u}});var r=n(2265),o=n(54887),a=n(82912),l=n(61188),i=n(57437),u=r.forwardRef((e,t)=>{var n,u;let{container:c,...f}=e,[d,s]=r.useState(!1);(0,l.b)(()=>s(!0),[]);let p=c||d&&(null===(u=globalThis)||void 0===u?void 0:null===(n=u.document)||void 0===n?void 0:n.body);return p?o.createPortal((0,i.jsx)(a.WV.div,{...f,ref:t}),p):null});u.displayName="Portal"}}]);