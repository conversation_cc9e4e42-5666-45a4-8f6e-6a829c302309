"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6379],{87769:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},42208:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("<PERSON>",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},88906:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]])},33388:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]])},49988:function(e,t,n){n.d(t,{g:function(){return r}});function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}},97867:function(e,t,n){n.d(t,{default:function(){return u}});var r=n(49988),l=n(27648),o=n(99376),a=n(2265),i=n(48706),u=(0,a.forwardRef)(function(e,t){let{defaultLocale:n,href:u,locale:c,localeCookie:s,onClick:p,prefetch:d,unprefixed:f,...h}=e,m=(0,i.Z)(),g=c!==m,v=c||m,b=function(){let[e,t]=(0,a.useState)();return(0,a.useEffect)(()=>{t(window.location.host)},[]),e}(),y=b&&f&&(f.domains[b]===v||!Object.keys(f.domains).includes(b)&&m===n&&!c)?f.pathname:u,w=(0,o.usePathname)();return g&&(d&&console.error("The `prefetch` prop is currently not supported when using the `locale` prop on `Link` to switch the locale.`"),d=!1),a.createElement(l.default,(0,r.g)({ref:t,href:y,hrefLang:g?c:void 0,onClick:function(e){(function(e,t,n,r){if(!e||!(r!==n&&null!=r)||!t)return;let l=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window.location.pathname;return"/"===e?t:t.replace(e,"")}(t),{name:o,...a}=e;a.path||(a.path=""!==l?l:"/");let i="".concat(o,"=").concat(r,";");for(let[e,t]of Object.entries(a))i+="".concat("maxAge"===e?"max-age":e),"boolean"!=typeof t&&(i+="="+t),i+=";";document.cookie=i})(s,w,m,c),p&&p(e)},prefetch:d},h))})},31085:function(e,t,n){n.d(t,{default:function(){return p}});var r=n(49988),l=n(99376),o=n(2265),a=n(48706);function i(e){return("object"==typeof e?null==e.host&&null==e.hostname:!/^[a-z]+:/i.test(e))&&!function(e){let t="object"==typeof e?e.pathname:e;return null!=t&&!t.startsWith("/")}(e)}function u(e,t){let n;return"string"==typeof e?n=c(t,e):(n={...e},e.pathname&&(n.pathname=c(t,e.pathname))),n}function c(e,t){let n=e;return/^\/(\?.*)?$/.test(t)&&(t=t.slice(1)),n+=t}n(25566);var s=n(97867);let p=(0,o.forwardRef)(function(e,t){let{href:n,locale:c,localeCookie:p,localePrefixMode:d,prefix:f,...h}=e,m=(0,l.usePathname)(),g=(0,a.Z)(),v=c!==g,[b,y]=(0,o.useState)(()=>i(n)&&("never"!==d||v)?u(n,f):n);return(0,o.useEffect)(()=>{m&&y(function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t,r=arguments.length>3?arguments[3]:void 0,l=arguments.length>4?arguments[4]:void 0;if(!i(e))return e;let o=r===l||r.startsWith("".concat(l,"/"));return(t!==n||o)&&null!=l?u(e,l):e}(n,c,g,m,f))},[g,n,c,m,f]),o.createElement(s.default,(0,r.g)({ref:t,href:b,locale:c,localeCookie:p},h))});p.displayName="ClientLink"},48706:function(e,t,n){n.d(t,{Z:function(){return a}});var r=n(99376),l=n(526);let o="locale";function a(){let e;let t=(0,r.useParams)();try{e=(0,l.useLocale)()}catch(n){if("string"!=typeof(null==t?void 0:t[o]))throw n;e=t[o]}return e}},10575:function(e,t,n){n.d(t,{default:function(){return a}});var r=n(49988),l=n(2265),o=n(69362);function a(e){let{locale:t,...n}=e;if(!t)throw Error("Failed to determine locale in `NextIntlClientProvider`, please provide the `locale` prop explicitly.\n\nSee https://next-intl-docs.vercel.app/docs/configuration#locale");return l.createElement(o.IntlProvider,(0,r.g)({locale:t},n))}},35934:function(e,t,n){n.d(t,{VM:function(){return m},Ww:function(){return h},uZ:function(){return g}});var r=n(2265),l=Object.defineProperty,o=Object.defineProperties,a=Object.getOwnPropertyDescriptors,i=Object.getOwnPropertySymbols,u=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable,s=(e,t,n)=>t in e?l(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,p=(e,t)=>{for(var n in t||(t={}))u.call(t,n)&&s(e,n,t[n]);if(i)for(var n of i(t))c.call(t,n)&&s(e,n,t[n]);return e},d=(e,t)=>o(e,a(t)),f=(e,t)=>{var n={};for(var r in e)u.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&i)for(var r of i(e))0>t.indexOf(r)&&c.call(e,r)&&(n[r]=e[r]);return n},h="^\\d+$",m=r.createContext({}),g=r.forwardRef((e,t)=>{let n;var l,o,a,i,u,{value:c,onChange:s,maxLength:g,textAlign:y="left",pattern:w=h,inputMode:E="numeric",onComplete:S,pushPasswordManagerStrategy:k="increase-width",containerClassName:C,noScriptCSSFallback:P=b,render:x,children:M}=e,R=f(e,["value","onChange","maxLength","textAlign","pattern","inputMode","onComplete","pushPasswordManagerStrategy","containerClassName","noScriptCSSFallback","render","children"]);let[O,j]=r.useState("string"==typeof R.defaultValue?R.defaultValue:""),W=null!=c?c:O,D=(n=r.useRef(),r.useEffect(()=>{n.current=W}),n.current),T=r.useCallback(e=>{null==s||s(e),j(e)},[s]),_=r.useMemo(()=>w?"string"==typeof w?new RegExp(w):w:null,[w]),A=r.useRef(null),B=r.useRef(null),I=r.useRef({value:W,onChange:T,isIOS:"undefined"!=typeof window&&(null==(o=null==(l=null==window?void 0:window.CSS)?void 0:l.supports)?void 0:o.call(l,"-webkit-touch-callout","none"))}),L=r.useRef({prev:[null==(a=A.current)?void 0:a.selectionStart,null==(i=A.current)?void 0:i.selectionEnd,null==(u=A.current)?void 0:u.selectionDirection]});r.useImperativeHandle(t,()=>A.current,[]),r.useEffect(()=>{let e=A.current,t=B.current;if(!e||!t)return;function n(){if(document.activeElement!==e){z(null),G(null);return}let t=e.selectionStart,n=e.selectionEnd,r=e.selectionDirection,l=e.maxLength,o=e.value,a=L.current.prev,i=-1,u=-1,c;if(0!==o.length&&null!==t&&null!==n){let e=t===n,r=t===o.length&&o.length<l;if(e&&!r){if(0===t)i=0,u=1,c="forward";else if(t===l)i=t-1,u=t,c="backward";else if(l>1&&o.length>1){let e=0;if(null!==a[0]&&null!==a[1]){c=t<a[1]?"backward":"forward";let n=a[0]===a[1]&&a[0]<l;"backward"!==c||n||(e=-1)}i=e+t,u=e+t+1}}-1!==i&&-1!==u&&i!==u&&A.current.setSelectionRange(i,u,c)}let s=-1!==i?i:t,p=-1!==u?u:n,d=null!=c?c:r;z(s),G(p),L.current.prev=[s,p,d]}if(I.current.value!==e.value&&I.current.onChange(e.value),L.current.prev=[e.selectionStart,e.selectionEnd,e.selectionDirection],document.addEventListener("selectionchange",n,{capture:!0}),n(),document.activeElement===e&&N(!0),!document.getElementById("input-otp-style")){let e=document.createElement("style");if(e.id="input-otp-style",document.head.appendChild(e),e.sheet){let t="background: transparent !important; color: transparent !important; border-color: transparent !important; opacity: 0 !important; box-shadow: none !important; -webkit-box-shadow: none !important; -webkit-text-fill-color: transparent !important;";v(e.sheet,"[data-input-otp]::selection { background: transparent !important; color: transparent !important; }"),v(e.sheet,`[data-input-otp]:autofill { ${t} }`),v(e.sheet,`[data-input-otp]:-webkit-autofill { ${t} }`),v(e.sheet,"@supports (-webkit-touch-callout: none) { [data-input-otp] { letter-spacing: -.6em !important; font-weight: 100 !important; font-stretch: ultra-condensed; font-optical-sizing: none !important; left: -1px !important; right: 1px !important; } }"),v(e.sheet,"[data-input-otp] + * { pointer-events: all !important; }")}}let r=()=>{t&&t.style.setProperty("--root-height",`${e.clientHeight}px`)};r();let l=new ResizeObserver(r);return l.observe(e),()=>{document.removeEventListener("selectionchange",n,{capture:!0}),l.disconnect()}},[]);let[F,Z]=r.useState(!1),[H,N]=r.useState(!1),[$,z]=r.useState(null),[V,G]=r.useState(null);r.useEffect(()=>{var e;setTimeout(e=()=>{var e,t,n,r;null==(e=A.current)||e.dispatchEvent(new Event("input"));let l=null==(t=A.current)?void 0:t.selectionStart,o=null==(n=A.current)?void 0:n.selectionEnd,a=null==(r=A.current)?void 0:r.selectionDirection;null!==l&&null!==o&&(z(l),G(o),L.current.prev=[l,o,a])},0),setTimeout(e,10),setTimeout(e,50)},[W,H]),r.useEffect(()=>{void 0!==D&&W!==D&&D.length<g&&W.length===g&&(null==S||S(W))},[g,S,D,W]);let q=function({containerRef:e,inputRef:t,pushPasswordManagerStrategy:n,isFocused:l}){let o=r.useRef({done:!1,refocused:!1}),[a,i]=r.useState(!1),[u,c]=r.useState(!1),[s,p]=r.useState(!1),d=r.useMemo(()=>"none"!==n&&("increase-width"===n||"experimental-no-flickering"===n)&&a&&u,[a,u,n]),f=r.useCallback(()=>{let r=e.current,l=t.current;if(!r||!l||s||"none"===n)return;let a=r.getBoundingClientRect().left+r.offsetWidth,u=r.getBoundingClientRect().top+r.offsetHeight/2;if(!(0===document.querySelectorAll('[data-lastpass-icon-root],com-1password-button,[data-dashlanecreated],[style$="2147483647 !important;"]').length&&document.elementFromPoint(a-18,u)===r)&&(i(!0),p(!0),!o.current.refocused&&document.activeElement===l)){let e=[l.selectionStart,l.selectionEnd];l.blur(),l.focus(),l.setSelectionRange(e[0],e[1]),o.current.refocused=!0}},[e,t,s,n]);return r.useEffect(()=>{let t=e.current;if(!t||"none"===n)return;function r(){c(window.innerWidth-t.getBoundingClientRect().right>=40)}r();let l=setInterval(r,1e3);return()=>{clearInterval(l)}},[e,n]),r.useEffect(()=>{let e=l||document.activeElement===t.current;if("none"===n||!e)return;let r=setTimeout(f,0),o=setTimeout(f,2e3),a=setTimeout(f,5e3),i=setTimeout(()=>{p(!0)},6e3);return()=>{clearTimeout(r),clearTimeout(o),clearTimeout(a),clearTimeout(i)}},[t,l,n,f]),{hasPWMBadge:a,willPushPWMBadge:d,PWM_BADGE_SPACE_WIDTH:"40px"}}({containerRef:B,inputRef:A,pushPasswordManagerStrategy:k,isFocused:H}),U=r.useCallback(e=>{let t=e.currentTarget.value.slice(0,g);if(t.length>0&&_&&!_.test(t)){e.preventDefault();return}"string"==typeof D&&t.length<D.length&&document.dispatchEvent(new Event("selectionchange")),T(t)},[g,T,D,_]),J=r.useCallback(()=>{var e;if(A.current){let t=Math.min(A.current.value.length,g-1),n=A.current.value.length;null==(e=A.current)||e.setSelectionRange(t,n),z(t),G(n)}N(!0)},[g]),K=r.useCallback(e=>{var t,n;let r=A.current;if(!I.current.isIOS||!e.clipboardData||!r)return;let l=e.clipboardData.getData("text/plain");e.preventDefault();let o=null==(t=A.current)?void 0:t.selectionStart,a=null==(n=A.current)?void 0:n.selectionEnd,i=(o!==a?W.slice(0,o)+l+W.slice(a):W.slice(0,o)+l+W.slice(o)).slice(0,g);if(i.length>0&&_&&!_.test(i))return;r.value=i,T(i);let u=Math.min(i.length,g-1),c=i.length;r.setSelectionRange(u,c),z(u),G(c)},[g,T,_,W]),Q=r.useMemo(()=>({position:"relative",cursor:R.disabled?"default":"text",userSelect:"none",WebkitUserSelect:"none",pointerEvents:"none"}),[R.disabled]),X=r.useMemo(()=>({position:"absolute",inset:0,width:q.willPushPWMBadge?`calc(100% + ${q.PWM_BADGE_SPACE_WIDTH})`:"100%",clipPath:q.willPushPWMBadge?`inset(0 ${q.PWM_BADGE_SPACE_WIDTH} 0 0)`:void 0,height:"100%",display:"flex",textAlign:y,opacity:"1",color:"transparent",pointerEvents:"all",background:"transparent",caretColor:"transparent",border:"0 solid transparent",outline:"0 solid transparent",boxShadow:"none",lineHeight:"1",letterSpacing:"-.5em",fontSize:"var(--root-height)",fontFamily:"monospace",fontVariantNumeric:"tabular-nums"}),[q.PWM_BADGE_SPACE_WIDTH,q.willPushPWMBadge,y]),Y=r.useMemo(()=>r.createElement("input",d(p({autoComplete:R.autoComplete||"one-time-code"},R),{"data-input-otp":!0,"data-input-otp-mss":$,"data-input-otp-mse":V,inputMode:E,pattern:null==_?void 0:_.source,style:X,maxLength:g,value:W,ref:A,onPaste:e=>{var t;K(e),null==(t=R.onPaste)||t.call(R,e)},onChange:U,onMouseOver:e=>{var t;Z(!0),null==(t=R.onMouseOver)||t.call(R,e)},onMouseLeave:e=>{var t;Z(!1),null==(t=R.onMouseLeave)||t.call(R,e)},onFocus:e=>{var t;J(),null==(t=R.onFocus)||t.call(R,e)},onBlur:e=>{var t;N(!1),null==(t=R.onBlur)||t.call(R,e)}})),[U,J,K,E,X,g,V,$,R,null==_?void 0:_.source,W]),ee=r.useMemo(()=>({slots:Array.from({length:g}).map((e,t)=>{let n=H&&null!==$&&null!==V&&($===V&&t===$||t>=$&&t<V),r=void 0!==W[t]?W[t]:null;return{char:r,isActive:n,hasFakeCaret:n&&null===r}}),isFocused:H,isHovering:!R.disabled&&F}),[H,F,g,V,$,R.disabled,W]),et=r.useMemo(()=>x?x(ee):r.createElement(m.Provider,{value:ee},M),[M,ee,x]);return r.createElement(r.Fragment,null,null!==P&&r.createElement("noscript",null,r.createElement("style",null,P)),r.createElement("div",{ref:B,"data-input-otp-container":!0,style:Q,className:C},et,r.createElement("div",{style:{position:"absolute",inset:0,pointerEvents:"none"}},Y)))});function v(e,t){try{e.insertRule(t)}catch(e){console.error("input-otp could not insert CSS rule:",t)}}g.displayName="Input";var b=`
[data-input-otp] {
  --nojs-bg: white !important;
  --nojs-fg: black !important;

  background-color: var(--nojs-bg) !important;
  color: var(--nojs-fg) !important;
  caret-color: var(--nojs-fg) !important;
  letter-spacing: .25em !important;
  text-align: center !important;
  border: 1px solid var(--nojs-fg) !important;
  border-radius: 4px !important;
  width: 100% !important;
}
@media (prefers-color-scheme: dark) {
  [data-input-otp] {
    --nojs-bg: black !important;
    --nojs-fg: white !important;
  }
}`}}]);