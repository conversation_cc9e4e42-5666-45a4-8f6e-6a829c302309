"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7296],{49988:function(t,e,n){n.d(e,{g:function(){return r}});function r(){return(r=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)({}).hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t}).apply(null,arguments)}},97867:function(t,e,n){n.d(e,{default:function(){return a}});var r=n(49988),o=n(27648),c=n(99376),u=n(2265),i=n(48706),a=(0,u.forwardRef)(function(t,e){let{defaultLocale:n,href:a,locale:l,localeCookie:f,onClick:s,prefetch:p,unprefixed:y,...h}=t,b=(0,i.Z)(),d=l!==b,v=l||b,m=function(){let[t,e]=(0,u.useState)();return(0,u.useEffect)(()=>{e(window.location.host)},[]),t}(),w=m&&y&&(y.domains[m]===v||!Object.keys(y.domains).includes(m)&&b===n&&!l)?y.pathname:a,g=(0,c.usePathname)();return d&&(p&&console.error("The `prefetch` prop is currently not supported when using the `locale` prop on `Link` to switch the locale.`"),p=!1),u.createElement(o.default,(0,r.g)({ref:e,href:w,hrefLang:d?l:void 0,onClick:function(t){(function(t,e,n,r){if(!t||!(r!==n&&null!=r)||!e)return;let o=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window.location.pathname;return"/"===t?e:e.replace(t,"")}(e),{name:c,...u}=t;u.path||(u.path=""!==o?o:"/");let i="".concat(c,"=").concat(r,";");for(let[t,e]of Object.entries(u))i+="".concat("maxAge"===t?"max-age":t),"boolean"!=typeof e&&(i+="="+e),i+=";";document.cookie=i})(f,g,b,l),s&&s(t)},prefetch:p},h))})},31085:function(t,e,n){n.d(e,{default:function(){return s}});var r=n(49988),o=n(99376),c=n(2265),u=n(48706);function i(t){return("object"==typeof t?null==t.host&&null==t.hostname:!/^[a-z]+:/i.test(t))&&!function(t){let e="object"==typeof t?t.pathname:t;return null!=e&&!e.startsWith("/")}(t)}function a(t,e){let n;return"string"==typeof t?n=l(e,t):(n={...t},t.pathname&&(n.pathname=l(e,t.pathname))),n}function l(t,e){let n=t;return/^\/(\?.*)?$/.test(e)&&(e=e.slice(1)),n+=e}n(25566);var f=n(97867);let s=(0,c.forwardRef)(function(t,e){let{href:n,locale:l,localeCookie:s,localePrefixMode:p,prefix:y,...h}=t,b=(0,o.usePathname)(),d=(0,u.Z)(),v=l!==d,[m,w]=(0,c.useState)(()=>i(n)&&("never"!==p||v)?a(n,y):n);return(0,c.useEffect)(()=>{b&&w(function(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,r=arguments.length>3?arguments[3]:void 0,o=arguments.length>4?arguments[4]:void 0;if(!i(t))return t;let c=r===o||r.startsWith("".concat(o,"/"));return(e!==n||c)&&null!=o?a(t,o):t}(n,l,d,b,y))},[d,n,l,b,y]),c.createElement(f.default,(0,r.g)({ref:e,href:m,locale:l,localeCookie:s},h))});s.displayName="ClientLink"},48706:function(t,e,n){n.d(e,{Z:function(){return u}});var r=n(99376),o=n(526);let c="locale";function u(){let t;let e=(0,r.useParams)();try{t=(0,o.useLocale)()}catch(n){if("string"!=typeof(null==e?void 0:e[c]))throw n;t=e[c]}return t}},12119:function(t,e,n){Object.defineProperty(e,"$",{enumerable:!0,get:function(){return o}});let r=n(83079);function o(t){let{createServerReference:e}=n(6671);return e(t,r.callServer)}},5853:function(t,e,n){n.d(e,{CR:function(){return f},FC:function(){return y},Jh:function(){return a},KL:function(){return h},XA:function(){return l},ZT:function(){return o},_T:function(){return u},ev:function(){return s},mG:function(){return i},pi:function(){return c},qq:function(){return p}});var r=function(t,e){return(r=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])})(t,e)};function o(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}r(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}var c=function(){return(c=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t}).apply(this,arguments)};function u(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&0>e.indexOf(r)&&(n[r]=t[r]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(t);o<r.length;o++)0>e.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(t,r[o])&&(n[r[o]]=t[r[o]]);return n}function i(t,e,n,r){return new(n||(n=Promise))(function(o,c){function u(t){try{a(r.next(t))}catch(t){c(t)}}function i(t){try{a(r.throw(t))}catch(t){c(t)}}function a(t){var e;t.done?o(t.value):((e=t.value)instanceof n?e:new n(function(t){t(e)})).then(u,i)}a((r=r.apply(t,e||[])).next())})}function a(t,e){var n,r,o,c={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]},u=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return u.next=i(0),u.throw=i(1),u.return=i(2),"function"==typeof Symbol&&(u[Symbol.iterator]=function(){return this}),u;function i(i){return function(a){return function(i){if(n)throw TypeError("Generator is already executing.");for(;u&&(u=0,i[0]&&(c=0)),c;)try{if(n=1,r&&(o=2&i[0]?r.return:i[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,i[1])).done)return o;switch(r=0,o&&(i=[2&i[0],o.value]),i[0]){case 0:case 1:o=i;break;case 4:return c.label++,{value:i[1],done:!1};case 5:c.label++,r=i[1],i=[0];continue;case 7:i=c.ops.pop(),c.trys.pop();continue;default:if(!(o=(o=c.trys).length>0&&o[o.length-1])&&(6===i[0]||2===i[0])){c=0;continue}if(3===i[0]&&(!o||i[1]>o[0]&&i[1]<o[3])){c.label=i[1];break}if(6===i[0]&&c.label<o[1]){c.label=o[1],o=i;break}if(o&&c.label<o[2]){c.label=o[2],c.ops.push(i);break}o[2]&&c.ops.pop(),c.trys.pop();continue}i=e.call(t,c)}catch(t){i=[6,t],r=0}finally{n=o=0}if(5&i[0])throw i[1];return{value:i[0]?i[1]:void 0,done:!0}}([i,a])}}}function l(t){var e="function"==typeof Symbol&&Symbol.iterator,n=e&&t[e],r=0;if(n)return n.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function f(t,e){var n="function"==typeof Symbol&&t[Symbol.iterator];if(!n)return t;var r,o,c=n.call(t),u=[];try{for(;(void 0===e||e-- >0)&&!(r=c.next()).done;)u.push(r.value)}catch(t){o={error:t}}finally{try{r&&!r.done&&(n=c.return)&&n.call(c)}finally{if(o)throw o.error}}return u}function s(t,e,n){if(n||2==arguments.length)for(var r,o=0,c=e.length;o<c;o++)!r&&o in e||(r||(r=Array.prototype.slice.call(e,0,o)),r[o]=e[o]);return t.concat(r||Array.prototype.slice.call(e))}function p(t){return this instanceof p?(this.v=t,this):new p(t)}function y(t,e,n){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var r,o=n.apply(t,e||[]),c=[];return r=Object.create(("function"==typeof AsyncIterator?AsyncIterator:Object).prototype),u("next"),u("throw"),u("return",function(t){return function(e){return Promise.resolve(e).then(t,l)}}),r[Symbol.asyncIterator]=function(){return this},r;function u(t,e){o[t]&&(r[t]=function(e){return new Promise(function(n,r){c.push([t,e,n,r])>1||i(t,e)})},e&&(r[t]=e(r[t])))}function i(t,e){try{var n;(n=o[t](e)).value instanceof p?Promise.resolve(n.value.v).then(a,l):f(c[0][2],n)}catch(t){f(c[0][3],t)}}function a(t){i("next",t)}function l(t){i("throw",t)}function f(t,e){t(e),c.shift(),c.length&&i(c[0][0],c[0][1])}}function h(t){if(!Symbol.asyncIterator)throw TypeError("Symbol.asyncIterator is not defined.");var e,n=t[Symbol.asyncIterator];return n?n.call(t):(t=l(t),e={},r("next"),r("throw"),r("return"),e[Symbol.asyncIterator]=function(){return this},e);function r(n){e[n]=t[n]&&function(e){return new Promise(function(r,o){!function(t,e,n,r){Promise.resolve(r).then(function(e){t({value:e,done:n})},e)}(r,o,(e=t[n](e)).done,e.value)})}}}"function"==typeof SuppressedError&&SuppressedError}}]);