(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4413],{71318:function(t,e,i){var n,s;void 0!==(s="function"==typeof(n=function(){var t,e,i,n={};n.version="0.2.0";var s=n.settings={minimum:.08,easing:"ease",positionUsing:"",speed:200,trickle:!0,trickleRate:.02,trickleSpeed:800,showSpinner:!0,barSelector:'[role="bar"]',spinnerSelector:'[role="spinner"]',parent:"body",template:'<div class="bar" role="bar"><div class="peg"></div></div><div class="spinner" role="spinner"><div class="spinner-icon"></div></div>'};function r(t,e,i){return t<e?e:t>i?i:t}n.configure=function(t){var e,i;for(e in t)void 0!==(i=t[e])&&t.hasOwnProperty(e)&&(s[e]=i);return this},n.status=null,n.set=function(t){var e=n.isStarted();t=r(t,s.minimum,1),n.status=1===t?null:t;var i=n.render(!e),l=i.querySelector(s.barSelector),u=s.speed,h=s.easing;return i.offsetWidth,o(function(e){var r,o;""===s.positionUsing&&(s.positionUsing=n.getPositioningCSS()),a(l,(r=t,(o="translate3d"===s.positionUsing?{transform:"translate3d("+(-1+r)*100+"%,0,0)"}:"translate"===s.positionUsing?{transform:"translate("+(-1+r)*100+"%,0)"}:{"margin-left":(-1+r)*100+"%"}).transition="all "+u+"ms "+h,o)),1===t?(a(i,{transition:"none",opacity:1}),i.offsetWidth,setTimeout(function(){a(i,{transition:"all "+u+"ms linear",opacity:0}),setTimeout(function(){n.remove(),e()},u)},u)):setTimeout(e,u)}),this},n.isStarted=function(){return"number"==typeof n.status},n.start=function(){n.status||n.set(0);var t=function(){setTimeout(function(){n.status&&(n.trickle(),t())},s.trickleSpeed)};return s.trickle&&t(),this},n.done=function(t){return t||n.status?n.inc(.3+.5*Math.random()).set(1):this},n.inc=function(t){var e=n.status;return e?("number"!=typeof t&&(t=(1-e)*r(Math.random()*e,.1,.95)),e=r(e+t,0,.994),n.set(e)):n.start()},n.trickle=function(){return n.inc(Math.random()*s.trickleRate)},t=0,e=0,n.promise=function(i){return i&&"resolved"!==i.state()&&(0===e&&n.start(),t++,e++,i.always(function(){0==--e?(t=0,n.done()):n.set((t-e)/t)})),this},n.render=function(t){if(n.isRendered())return document.getElementById("nprogress");u(document.documentElement,"nprogress-busy");var e=document.createElement("div");e.id="nprogress",e.innerHTML=s.template;var i,r=e.querySelector(s.barSelector),o=t?"-100":(-1+(n.status||0))*100,l=document.querySelector(s.parent);return a(r,{transition:"all 0 linear",transform:"translate3d("+o+"%,0,0)"}),!s.showSpinner&&(i=e.querySelector(s.spinnerSelector))&&c(i),l!=document.body&&u(l,"nprogress-custom-parent"),l.appendChild(e),e},n.remove=function(){h(document.documentElement,"nprogress-busy"),h(document.querySelector(s.parent),"nprogress-custom-parent");var t=document.getElementById("nprogress");t&&c(t)},n.isRendered=function(){return!!document.getElementById("nprogress")},n.getPositioningCSS=function(){var t=document.body.style,e="WebkitTransform"in t?"Webkit":"MozTransform"in t?"Moz":"msTransform"in t?"ms":"OTransform"in t?"O":"";return e+"Perspective" in t?"translate3d":e+"Transform" in t?"translate":"margin"};var o=(i=[],function(t){i.push(t),1==i.length&&function t(){var e=i.shift();e&&e(t)}()}),a=function(){var t=["Webkit","O","Moz","ms"],e={};function i(i,n,s){var r;n=e[r=(r=n).replace(/^-ms-/,"ms-").replace(/-([\da-z])/gi,function(t,e){return e.toUpperCase()})]||(e[r]=function(e){var i=document.body.style;if(e in i)return e;for(var n,s=t.length,r=e.charAt(0).toUpperCase()+e.slice(1);s--;)if((n=t[s]+r)in i)return n;return e}(r)),i.style[n]=s}return function(t,e){var n,s,r=arguments;if(2==r.length)for(n in e)void 0!==(s=e[n])&&e.hasOwnProperty(n)&&i(t,n,s);else i(t,r[1],r[2])}}();function l(t,e){return("string"==typeof t?t:d(t)).indexOf(" "+e+" ")>=0}function u(t,e){var i=d(t),n=i+e;l(i,e)||(t.className=n.substring(1))}function h(t,e){var i,n=d(t);l(t,e)&&(i=n.replace(" "+e+" "," "),t.className=i.substring(1,i.length-1))}function d(t){return(" "+(t.className||"")+" ").replace(/\s+/gi," ")}function c(t){t&&t.parentNode&&t.parentNode.removeChild(t)}return n})?n.call(e,i,e,t):n)&&(t.exports=s)},49637:function(t,e,i){"use strict";i.d(e,{oO:function(){return r}});var n=i(2265),s=i(64252);function r(t=!0){let e=(0,n.useContext)(s.O);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:r,register:o}=e,a=(0,n.useId)();(0,n.useEffect)(()=>{t&&o(a)},[t]);let l=(0,n.useCallback)(()=>t&&r&&r(a),[a,r,t]);return!i&&r?[!1,l]:[!0]}},58881:function(t,e,i){"use strict";i.d(e,{p:function(){return n}});let n=(0,i(2265).createContext)({})},45750:function(t,e,i){"use strict";i.d(e,{_:function(){return n}});let n=(0,i(2265).createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"})},64252:function(t,e,i){"use strict";i.d(e,{O:function(){return n}});let n=(0,i(2265).createContext)(null)},40521:function(t,e,i){"use strict";let n;function s(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}i.d(e,{E:function(){return rv}});let r=t=>Array.isArray(t);function o(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let n=0;n<i;n++)if(e[n]!==t[n])return!1;return!0}function a(t){return"string"==typeof t||Array.isArray(t)}function l(t){let e=[{},{}];return null==t||t.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function u(t,e,i,n){if("function"==typeof e){let[s,r]=l(n);e=e(void 0!==i?i:t.custom,s,r)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[s,r]=l(n);e=e(void 0!==i?i:t.custom,s,r)}return e}function h(t,e,i){let n=t.getProps();return u(n,e,void 0!==i?i:n.custom,t)}let d=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],c=["initial",...d];function p(t){let e;return()=>(void 0===e&&(e=t()),e)}let m=p(()=>void 0!==window.ScrollTimeline);class f{constructor(t){this.stop=()=>this.runAll("stop"),this.animations=t.filter(Boolean)}get finished(){return Promise.all(this.animations.map(t=>"finished"in t?t.finished:t))}getAll(t){return this.animations[0][t]}setAll(t,e){for(let i=0;i<this.animations.length;i++)this.animations[i][t]=e}attachTimeline(t,e){let i=this.animations.map(i=>m()&&i.attachTimeline?i.attachTimeline(t):"function"==typeof e?e(i):void 0);return()=>{i.forEach((t,e)=>{t&&t(),this.animations[e].stop()})}}get time(){return this.getAll("time")}set time(t){this.setAll("time",t)}get speed(){return this.getAll("speed")}set speed(t){this.setAll("speed",t)}get startTime(){return this.getAll("startTime")}get duration(){let t=0;for(let e=0;e<this.animations.length;e++)t=Math.max(t,this.animations[e].duration);return t}runAll(t){this.animations.forEach(e=>e[t]())}flatten(){this.runAll("flatten")}play(){this.runAll("play")}pause(){this.runAll("pause")}cancel(){this.runAll("cancel")}complete(){this.runAll("complete")}}class v extends f{then(t,e){return Promise.all(this.animations).then(t).catch(e)}}function g(t,e){return t?t[e]||t.default||t:void 0}function y(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function x(t){return"function"==typeof t}function w(t,e){t.timeline=e,t.onfinish=null}let P=t=>Array.isArray(t)&&"number"==typeof t[0],T={linearEasing:void 0},b=function(t,e){let i=p(t);return()=>{var t;return null!==(t=T[e])&&void 0!==t?t:i()}}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),S=(t,e,i)=>{let n=e-t;return 0===n?1:(i-t)/n},A=(t,e,i=10)=>{let n="",s=Math.max(Math.round(e/i),2);for(let e=0;e<s;e++)n+=t(S(0,s-1,e))+", ";return`linear(${n.substring(0,n.length-2)})`},E=([t,e,i,n])=>`cubic-bezier(${t}, ${e}, ${i}, ${n})`,M={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:E([0,.65,.55,1]),circOut:E([.55,0,1,.45]),backIn:E([.31,.01,.66,-.59]),backOut:E([.33,1.53,.69,.99])},V={x:!1,y:!1};function C(t,e){let i=function(t,e,i){if(t instanceof Element)return[t];if("string"==typeof t){let e=document.querySelectorAll(t);return e?Array.from(e):[]}return Array.from(t)}(t),n=new AbortController;return[i,{passive:!0,...e,signal:n.signal},()=>n.abort()]}function D(t){return e=>{"touch"===e.pointerType||V.x||V.y||t(e)}}let k=(t,e)=>!!e&&(t===e||k(t,e.parentElement)),R=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary,j=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),L=new WeakSet;function F(t){return e=>{"Enter"===e.key&&t(e)}}function B(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let O=(t,e)=>{let i=t.currentTarget;if(!i)return;let n=F(()=>{if(L.has(i))return;B(i,"down");let t=F(()=>{B(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>B(i,"cancel"),e)});i.addEventListener("keydown",n,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",n),e)};function U(t){return R(t)&&!(V.x||V.y)}let I=t=>1e3*t,N=t=>t/1e3,$=t=>t,W=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],z=new Set(W),H=new Set(["width","height","top","left","right","bottom",...W]),Y=t=>!!(t&&"object"==typeof t&&t.mix&&t.toValue),X=t=>r(t)?t[t.length-1]||0:t,q={skipAnimations:!1,useManualTiming:!1},K=["read","resolveKeyframes","update","preRender","render","postRender"];function _(t,e){let i=!1,n=!0,s={delta:0,timestamp:0,isProcessing:!1},r=()=>i=!0,o=K.reduce((t,e)=>(t[e]=function(t){let e=new Set,i=new Set,n=!1,s=!1,r=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1};function a(e){r.has(e)&&(l.schedule(e),t()),e(o)}let l={schedule:(t,s=!1,o=!1)=>{let a=o&&n?e:i;return s&&r.add(t),a.has(t)||a.add(t),t},cancel:t=>{i.delete(t),r.delete(t)},process:t=>{if(o=t,n){s=!0;return}n=!0,[e,i]=[i,e],e.forEach(a),e.clear(),n=!1,s&&(s=!1,l.process(t))}};return l}(r),t),{}),{read:a,resolveKeyframes:l,update:u,preRender:h,render:d,postRender:c}=o,p=()=>{let r=q.useManualTiming?s.timestamp:performance.now();i=!1,s.delta=n?1e3/60:Math.max(Math.min(r-s.timestamp,40),1),s.timestamp=r,s.isProcessing=!0,a.process(s),l.process(s),u.process(s),h.process(s),d.process(s),c.process(s),s.isProcessing=!1,i&&e&&(n=!1,t(p))},m=()=>{i=!0,n=!0,s.isProcessing||t(p)};return{schedule:K.reduce((t,e)=>{let n=o[e];return t[e]=(t,e=!1,s=!1)=>(i||m(),n.schedule(t,e,s)),t},{}),cancel:t=>{for(let e=0;e<K.length;e++)o[K[e]].cancel(t)},state:s,steps:o}}let{schedule:G,cancel:Z,state:J,steps:Q}=_("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:$,!0);function tt(){n=void 0}let te={now:()=>(void 0===n&&te.set(J.isProcessing||q.useManualTiming?J.timestamp:performance.now()),n),set:t=>{n=t,queueMicrotask(tt)}};function ti(t,e){-1===t.indexOf(e)&&t.push(e)}function tn(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class ts{constructor(){this.subscriptions=[]}add(t){return ti(this.subscriptions,t),()=>tn(this.subscriptions,t)}notify(t,e,i){let n=this.subscriptions.length;if(n){if(1===n)this.subscriptions[0](t,e,i);else for(let s=0;s<n;s++){let n=this.subscriptions[s];n&&n(t,e,i)}}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}let tr=t=>!isNaN(parseFloat(t)),to={current:void 0};class ta{constructor(t,e={}){this.version="11.18.2",this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=te.now();this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&this.events.change&&this.events.change.notify(this.current),e&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=te.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=tr(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new ts);let i=this.events[t].add(e);return"change"===t?()=>{i(),G.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return to.current&&to.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=te.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function tl(t,e){return new ta(t,e)}let tu=t=>!!(t&&t.getVelocity);function th(t,e){let i=t.getValue("willChange");if(tu(i)&&i.add)return i.add(e)}let td=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),tc="data-"+td("framerAppearId"),tp={current:!1},tm=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function tf(t,e,i,n){if(t===e&&i===n)return $;let s=e=>(function(t,e,i,n,s){let r,o;let a=0;do(r=tm(o=e+(i-e)/2,n,s)-t)>0?i=o:e=o;while(Math.abs(r)>1e-7&&++a<12);return o})(e,0,1,t,i);return t=>0===t||1===t?t:tm(s(t),e,n)}let tv=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,tg=t=>e=>1-t(1-e),ty=tf(.33,1.53,.69,.99),tx=tg(ty),tw=tv(tx),tP=t=>(t*=2)<1?.5*tx(t):.5*(2-Math.pow(2,-10*(t-1))),tT=t=>1-Math.sin(Math.acos(t)),tb=tg(tT),tS=tv(tT),tA=t=>/^0[^.\s]+$/u.test(t),tE=(t,e,i)=>i>e?e:i<t?t:i,tM={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},tV={...tM,transform:t=>tE(0,1,t)},tC={...tM,default:1},tD=t=>Math.round(1e5*t)/1e5,tk=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,tR=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tj=(t,e)=>i=>!!("string"==typeof i&&tR.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),tL=(t,e,i)=>n=>{if("string"!=typeof n)return n;let[s,r,o,a]=n.match(tk);return{[t]:parseFloat(s),[e]:parseFloat(r),[i]:parseFloat(o),alpha:void 0!==a?parseFloat(a):1}},tF=t=>tE(0,255,t),tB={...tM,transform:t=>Math.round(tF(t))},tO={test:tj("rgb","red"),parse:tL("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:n=1})=>"rgba("+tB.transform(t)+", "+tB.transform(e)+", "+tB.transform(i)+", "+tD(tV.transform(n))+")"},tU={test:tj("#"),parse:function(t){let e="",i="",n="",s="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),n=t.substring(5,7),s=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),n=t.substring(3,4),s=t.substring(4,5),e+=e,i+=i,n+=n,s+=s),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(n,16),alpha:s?parseInt(s,16)/255:1}},transform:tO.transform},tI=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),tN=tI("deg"),t$=tI("%"),tW=tI("px"),tz=tI("vh"),tH=tI("vw"),tY={...t$,parse:t=>t$.parse(t)/100,transform:t=>t$.transform(100*t)},tX={test:tj("hsl","hue"),parse:tL("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:n=1})=>"hsla("+Math.round(t)+", "+t$.transform(tD(e))+", "+t$.transform(tD(i))+", "+tD(tV.transform(n))+")"},tq={test:t=>tO.test(t)||tU.test(t)||tX.test(t),parse:t=>tO.test(t)?tO.parse(t):tX.test(t)?tX.parse(t):tU.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tO.transform(t):tX.transform(t)},tK=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,t_="number",tG="color",tZ=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tJ(t){let e=t.toString(),i=[],n={color:[],number:[],var:[]},s=[],r=0,o=e.replace(tZ,t=>(tq.test(t)?(n.color.push(r),s.push(tG),i.push(tq.parse(t))):t.startsWith("var(")?(n.var.push(r),s.push("var"),i.push(t)):(n.number.push(r),s.push(t_),i.push(parseFloat(t))),++r,"${}")).split("${}");return{values:i,split:o,indexes:n,types:s}}function tQ(t){return tJ(t).values}function t0(t){let{split:e,types:i}=tJ(t),n=e.length;return t=>{let s="";for(let r=0;r<n;r++)if(s+=e[r],void 0!==t[r]){let e=i[r];e===t_?s+=tD(t[r]):e===tG?s+=tq.transform(t[r]):s+=t[r]}return s}}let t1=t=>"number"==typeof t?0:t,t5={test:function(t){var e,i;return isNaN(t)&&"string"==typeof t&&((null===(e=t.match(tk))||void 0===e?void 0:e.length)||0)+((null===(i=t.match(tK))||void 0===i?void 0:i.length)||0)>0},parse:tQ,createTransformer:t0,getAnimatableNone:function(t){let e=tQ(t);return t0(t)(e.map(t1))}},t2=new Set(["brightness","contrast","saturate","opacity"]);function t3(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[n]=i.match(tk)||[];if(!n)return t;let s=i.replace(n,""),r=t2.has(e)?1:0;return n!==i&&(r*=100),e+"("+r+s+")"}let t9=/\b([a-z-]*)\(.*?\)/gu,t4={...t5,getAnimatableNone:t=>{let e=t.match(t9);return e?e.map(t3).join(" "):t}},t6={...tM,transform:Math.round},t8={borderWidth:tW,borderTopWidth:tW,borderRightWidth:tW,borderBottomWidth:tW,borderLeftWidth:tW,borderRadius:tW,radius:tW,borderTopLeftRadius:tW,borderTopRightRadius:tW,borderBottomRightRadius:tW,borderBottomLeftRadius:tW,width:tW,maxWidth:tW,height:tW,maxHeight:tW,top:tW,right:tW,bottom:tW,left:tW,padding:tW,paddingTop:tW,paddingRight:tW,paddingBottom:tW,paddingLeft:tW,margin:tW,marginTop:tW,marginRight:tW,marginBottom:tW,marginLeft:tW,backgroundPositionX:tW,backgroundPositionY:tW,rotate:tN,rotateX:tN,rotateY:tN,rotateZ:tN,scale:tC,scaleX:tC,scaleY:tC,scaleZ:tC,skew:tN,skewX:tN,skewY:tN,distance:tW,translateX:tW,translateY:tW,translateZ:tW,x:tW,y:tW,z:tW,perspective:tW,transformPerspective:tW,opacity:tV,originX:tY,originY:tY,originZ:tW,zIndex:t6,size:tW,fillOpacity:tV,strokeOpacity:tV,numOctaves:t6},t7={...t8,color:tq,backgroundColor:tq,outlineColor:tq,fill:tq,stroke:tq,borderColor:tq,borderTopColor:tq,borderRightColor:tq,borderBottomColor:tq,borderLeftColor:tq,filter:t4,WebkitFilter:t4},et=t=>t7[t];function ee(t,e){let i=et(t);return i!==t4&&(i=t5),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let ei=new Set(["auto","none","0"]),en=t=>t===tM||t===tW,es=(t,e)=>parseFloat(t.split(", ")[e]),er=(t,e)=>(i,{transform:n})=>{if("none"===n||!n)return 0;let s=n.match(/^matrix3d\((.+)\)$/u);if(s)return es(s[1],e);{let e=n.match(/^matrix\((.+)\)$/u);return e?es(e[1],t):0}},eo=new Set(["x","y","z"]),ea=W.filter(t=>!eo.has(t)),el={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:er(4,13),y:er(5,14)};el.translateX=el.x,el.translateY=el.y;let eu=new Set,eh=!1,ed=!1;function ec(){if(ed){let t=Array.from(eu).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return ea.forEach(i=>{let n=t.getValue(i);void 0!==n&&(e.push([i,n.get()]),n.set(i.startsWith("scale")?1:0))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{var n;null===(n=t.getValue(e))||void 0===n||n.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}ed=!1,eh=!1,eu.forEach(t=>t.complete()),eu.clear()}function ep(){eu.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(ed=!0)})}class em{constructor(t,e,i,n,s,r=!1){this.isComplete=!1,this.isAsync=!1,this.needsMeasurement=!1,this.isScheduled=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=n,this.element=s,this.isAsync=r}scheduleResolve(){this.isScheduled=!0,this.isAsync?(eu.add(this),eh||(eh=!0,G.read(ep),G.resolveKeyframes(ec))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:n}=this;for(let s=0;s<t.length;s++)if(null===t[s]){if(0===s){let s=null==n?void 0:n.get(),r=t[t.length-1];if(void 0!==s)t[0]=s;else if(i&&e){let n=i.readValue(e,r);null!=n&&(t[0]=n)}void 0===t[0]&&(t[0]=r),n&&void 0===s&&n.set(t[0])}else t[s]=t[s-1]}}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(){this.isComplete=!0,this.onComplete(this.unresolvedKeyframes,this.finalKeyframe),eu.delete(this)}cancel(){this.isComplete||(this.isScheduled=!1,eu.delete(this))}resume(){this.isComplete||this.scheduleResolve()}}let ef=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),ev=t=>e=>"string"==typeof e&&e.startsWith(t),eg=ev("--"),ey=ev("var(--"),ex=t=>!!ey(t)&&ew.test(t.split("/*")[0].trim()),ew=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,eP=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,eT=t=>e=>e.test(t),eb=[tM,tW,t$,tN,tH,tz,{test:t=>"auto"===t,parse:t=>t}],eS=t=>eb.find(eT(t));class eA extends em{constructor(t,e,i,n,s){super(t,e,i,n,s,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let n=t[i];if("string"==typeof n&&ex(n=n.trim())){let s=function t(e,i,n=1){$(n<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[s,r]=function(t){let e=eP.exec(t);if(!e)return[,];let[,i,n,s]=e;return[`--${null!=i?i:n}`,s]}(e);if(!s)return;let o=window.getComputedStyle(i).getPropertyValue(s);if(o){let t=o.trim();return ef(t)?parseFloat(t):t}return ex(r)?t(r,i,n+1):r}(n,e.current);void 0!==s&&(t[i]=s),i===t.length-1&&(this.finalKeyframe=n)}}if(this.resolveNoneKeyframes(),!H.has(i)||2!==t.length)return;let[n,s]=t,r=eS(n),o=eS(s);if(r!==o){if(en(r)&&en(o))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else this.needsMeasurement=!0}}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var n;("number"==typeof(n=t[e])?0===n:null===n||"none"===n||"0"===n||tA(n))&&i.push(e)}i.length&&function(t,e,i){let n,s=0;for(;s<t.length&&!n;){let e=t[s];"string"==typeof e&&!ei.has(e)&&tJ(e).values.length&&(n=t[s]),s++}if(n&&i)for(let s of e)t[s]=ee(i,n)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=el[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let n=e[e.length-1];void 0!==n&&t.getValue(i,n).jump(n,!1)}measureEndState(){var t;let{element:e,name:i,unresolvedKeyframes:n}=this;if(!e||!e.current)return;let s=e.getValue(i);s&&s.jump(this.measuredOrigin,!1);let r=n.length-1,o=n[r];n[r]=el[i](e.measureViewportBox(),window.getComputedStyle(e.current)),null!==o&&void 0===this.finalKeyframe&&(this.finalKeyframe=o),(null===(t=this.removedTransforms)||void 0===t?void 0:t.length)&&this.removedTransforms.forEach(([t,i])=>{e.getValue(t).set(i)}),this.resolveNoneKeyframes()}}let eE=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(t5.test(t)||"0"===t)&&!t.startsWith("url(")),eM=t=>null!==t;function eV(t,{repeat:e,repeatType:i="loop"},n){let s=t.filter(eM),r=e&&"loop"!==i&&e%2==1?0:s.length-1;return r&&void 0!==n?n:s[r]}class eC{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:n=0,repeatDelay:s=0,repeatType:r="loop",...o}){this.isStopped=!1,this.hasAttemptedResolve=!1,this.createdAt=te.now(),this.options={autoplay:t,delay:e,type:i,repeat:n,repeatDelay:s,repeatType:r,...o},this.updateFinishedPromise()}calcStartTime(){return this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt}get resolved(){return this._resolved||this.hasAttemptedResolve||(ep(),ec()),this._resolved}onKeyframesResolved(t,e){this.resolvedAt=te.now(),this.hasAttemptedResolve=!0;let{name:i,type:n,velocity:s,delay:r,onComplete:o,onUpdate:a,isGenerator:l}=this.options;if(!l&&!function(t,e,i,n){let s=t[0];if(null===s)return!1;if("display"===e||"visibility"===e)return!0;let r=t[t.length-1],o=eE(s,e),a=eE(r,e);return $(o===a,`You are trying to animate ${e} from "${s}" to "${r}". ${s} is not an animatable value - to enable this animation set ${s} to a value animatable to ${r} via the \`style\` property.`),!!o&&!!a&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||x(i))&&n)}(t,i,n,s)){if(tp.current||!r){a&&a(eV(t,this.options,e)),o&&o(),this.resolveFinishedPromise();return}this.options.duration=0}let u=this.initPlayback(t,e);!1!==u&&(this._resolved={keyframes:t,finalKeyframe:e,...u},this.onPostResolved())}onPostResolved(){}then(t,e){return this.currentFinishedPromise.then(t,e)}flatten(){this.options.type="keyframes",this.options.ease="linear"}updateFinishedPromise(){this.currentFinishedPromise=new Promise(t=>{this.resolveFinishedPromise=t})}}let eD=(t,e,i)=>t+(e-t)*i;function ek(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function eR(t,e){return i=>i>0?e:t}let ej=(t,e,i)=>{let n=t*t,s=i*(e*e-n)+n;return s<0?0:Math.sqrt(s)},eL=[tU,tO,tX],eF=t=>eL.find(e=>e.test(t));function eB(t){let e=eF(t);if($(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===tX&&(i=function({hue:t,saturation:e,lightness:i,alpha:n}){t/=360,i/=100;let s=0,r=0,o=0;if(e/=100){let n=i<.5?i*(1+e):i+e-i*e,a=2*i-n;s=ek(a,n,t+1/3),r=ek(a,n,t),o=ek(a,n,t-1/3)}else s=r=o=i;return{red:Math.round(255*s),green:Math.round(255*r),blue:Math.round(255*o),alpha:n}}(i)),i}let eO=(t,e)=>{let i=eB(t),n=eB(e);if(!i||!n)return eR(t,e);let s={...i};return t=>(s.red=ej(i.red,n.red,t),s.green=ej(i.green,n.green,t),s.blue=ej(i.blue,n.blue,t),s.alpha=eD(i.alpha,n.alpha,t),tO.transform(s))},eU=(t,e)=>i=>e(t(i)),eI=(...t)=>t.reduce(eU),eN=new Set(["none","hidden"]);function e$(t,e){return i=>eD(t,e,i)}function eW(t){return"number"==typeof t?e$:"string"==typeof t?ex(t)?eR:tq.test(t)?eO:eY:Array.isArray(t)?ez:"object"==typeof t?tq.test(t)?eO:eH:eR}function ez(t,e){let i=[...t],n=i.length,s=t.map((t,i)=>eW(t)(t,e[i]));return t=>{for(let e=0;e<n;e++)i[e]=s[e](t);return i}}function eH(t,e){let i={...t,...e},n={};for(let s in i)void 0!==t[s]&&void 0!==e[s]&&(n[s]=eW(t[s])(t[s],e[s]));return t=>{for(let e in n)i[e]=n[e](t);return i}}let eY=(t,e)=>{let i=t5.createTransformer(e),n=tJ(t),s=tJ(e);return n.indexes.var.length===s.indexes.var.length&&n.indexes.color.length===s.indexes.color.length&&n.indexes.number.length>=s.indexes.number.length?eN.has(t)&&!s.values.length||eN.has(e)&&!n.values.length?eN.has(t)?i=>i<=0?t:e:i=>i>=1?e:t:eI(ez(function(t,e){var i;let n=[],s={color:0,var:0,number:0};for(let r=0;r<e.values.length;r++){let o=e.types[r],a=t.indexes[o][s[o]],l=null!==(i=t.values[a])&&void 0!==i?i:0;n[r]=l,s[o]++}return n}(n,s),s.values),i):($(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),eR(t,e))};function eX(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?eD(t,e,i):eW(t)(t,e)}function eq(t,e,i){var n,s;let r=Math.max(e-5,0);return n=i-t(r),(s=e-r)?1e3/s*n:0}let eK={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function e_(t,e){return t*Math.sqrt(1-e*e)}let eG=["duration","bounce"],eZ=["stiffness","damping","mass"];function eJ(t,e){return e.some(e=>void 0!==t[e])}function eQ(t=eK.visualDuration,e=eK.bounce){let i;let n="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:s,restDelta:r}=n,o=n.keyframes[0],a=n.keyframes[n.keyframes.length-1],l={done:!1,value:o},{stiffness:u,damping:h,mass:d,duration:c,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:eK.velocity,stiffness:eK.stiffness,damping:eK.damping,mass:eK.mass,isResolvedFromDuration:!1,...t};if(!eJ(t,eZ)&&eJ(t,eG)){if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),n=i*i,s=2*tE(.05,1,1-(t.bounce||0))*Math.sqrt(n);e={...e,mass:eK.mass,stiffness:n,damping:s}}else{let i=function({duration:t=eK.duration,bounce:e=eK.bounce,velocity:i=eK.velocity,mass:n=eK.mass}){let s,r;$(t<=I(eK.maxDuration),"Spring duration must be 10 seconds or less");let o=1-e;o=tE(eK.minDamping,eK.maxDamping,o),t=tE(eK.minDuration,eK.maxDuration,N(t)),o<1?(s=e=>{let n=e*o,s=n*t;return .001-(n-i)/e_(e,o)*Math.exp(-s)},r=e=>{let n=e*o*t,r=Math.pow(o,2)*Math.pow(e,2)*t,a=e_(Math.pow(e,2),o);return(n*i+i-r)*Math.exp(-n)*(-s(e)+.001>0?-1:1)/a}):(s=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),r=e=>t*t*(i-e)*Math.exp(-e*t));let a=function(t,e,i){let n=i;for(let i=1;i<12;i++)n-=t(n)/e(n);return n}(s,r,5/t);if(t=I(t),isNaN(a))return{stiffness:eK.stiffness,damping:eK.damping,duration:t};{let e=Math.pow(a,2)*n;return{stiffness:e,damping:2*o*Math.sqrt(n*e),duration:t}}}(t);(e={...e,...i,mass:eK.mass}).isResolvedFromDuration=!0}}return e}({...n,velocity:-N(n.velocity||0)}),f=p||0,v=h/(2*Math.sqrt(u*d)),g=a-o,x=N(Math.sqrt(u/d)),w=5>Math.abs(g);if(s||(s=w?eK.restSpeed.granular:eK.restSpeed.default),r||(r=w?eK.restDelta.granular:eK.restDelta.default),v<1){let t=e_(x,v);i=e=>a-Math.exp(-v*x*e)*((f+v*x*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}else if(1===v)i=t=>a-Math.exp(-x*t)*(g+(f+x*g)*t);else{let t=x*Math.sqrt(v*v-1);i=e=>{let i=Math.exp(-v*x*e),n=Math.min(t*e,300);return a-i*((f+v*x*g)*Math.sinh(n)+t*g*Math.cosh(n))/t}}let P={calculatedDuration:m&&c||null,next:t=>{let e=i(t);if(m)l.done=t>=c;else{let n=0;v<1&&(n=0===t?I(f):eq(i,t,e));let o=Math.abs(n)<=s,u=Math.abs(a-e)<=r;l.done=o&&u}return l.value=l.done?a:e,l},toString:()=>{let t=Math.min(y(P),2e4),e=A(e=>P.next(t*e).value,t,30);return t+"ms "+e}};return P}function e0({keyframes:t,velocity:e=0,power:i=.8,timeConstant:n=325,bounceDamping:s=10,bounceStiffness:r=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:h}){let d,c;let p=t[0],m={done:!1,value:p},f=t=>void 0!==a&&t<a||void 0!==l&&t>l,v=t=>void 0===a?l:void 0===l?a:Math.abs(a-t)<Math.abs(l-t)?a:l,g=i*e,y=p+g,x=void 0===o?y:o(y);x!==y&&(g=x-p);let w=t=>-g*Math.exp(-t/n),P=t=>x+w(t),T=t=>{let e=w(t),i=P(t);m.done=Math.abs(e)<=u,m.value=m.done?x:i},b=t=>{f(m.value)&&(d=t,c=eQ({keyframes:[m.value,v(m.value)],velocity:eq(P,t,m.value),damping:s,stiffness:r,restDelta:u,restSpeed:h}))};return b(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,T(t),b(t)),void 0!==d&&t>=d)?c.next(t-d):(e||T(t),m)}}}let e1=tf(.42,0,1,1),e5=tf(0,0,.58,1),e2=tf(.42,0,.58,1),e3=t=>Array.isArray(t)&&"number"!=typeof t[0],e9={linear:$,easeIn:e1,easeInOut:e2,easeOut:e5,circIn:tT,circInOut:tS,circOut:tb,backIn:tx,backInOut:tw,backOut:ty,anticipate:tP},e4=t=>{if(P(t)){$(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,n,s]=t;return tf(e,i,n,s)}return"string"==typeof t?($(void 0!==e9[t],`Invalid easing type '${t}'`),e9[t]):t};function e6({duration:t=300,keyframes:e,times:i,ease:n="easeInOut"}){let s=e3(n)?n.map(e4):e4(n),r={done:!1,value:e[0]},o=function(t,e,{clamp:i=!0,ease:n,mixer:s}={}){let r=t.length;if($(r===e.length,"Both input and output ranges must be the same length"),1===r)return()=>e[0];if(2===r&&e[0]===e[1])return()=>e[1];let o=t[0]===t[1];t[0]>t[r-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=function(t,e,i){let n=[],s=i||eX,r=t.length-1;for(let i=0;i<r;i++){let r=s(t[i],t[i+1]);e&&(r=eI(Array.isArray(e)?e[i]||$:e,r)),n.push(r)}return n}(e,n,s),l=a.length,u=i=>{if(o&&i<t[0])return e[0];let n=0;if(l>1)for(;n<t.length-2&&!(i<t[n+1]);n++);let s=S(t[n],t[n+1],i);return a[n](s)};return i?e=>u(tE(t[0],t[r-1],e)):u}((i&&i.length===e.length?i:function(t){let e=[0];return function(t,e){let i=t[t.length-1];for(let n=1;n<=e;n++){let s=S(0,e,n);t.push(eD(i,1,s))}}(e,t.length-1),e}(e)).map(e=>e*t),e,{ease:Array.isArray(s)?s:e.map(()=>s||e2).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(r.value=o(e),r.done=e>=t,r)}}let e8=t=>{let e=({timestamp:e})=>t(e);return{start:()=>G.update(e,!0),stop:()=>Z(e),now:()=>J.isProcessing?J.timestamp:te.now()}},e7={decay:e0,inertia:e0,tween:e6,keyframes:e6,spring:eQ},it=t=>t/100;class ie extends eC{constructor(t){super(t),this.holdTime=null,this.cancelTime=null,this.currentTime=0,this.playbackSpeed=1,this.pendingPlayState="running",this.startTime=null,this.state="idle",this.stop=()=>{if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.teardown();let{onStop:t}=this.options;t&&t()};let{name:e,motionValue:i,element:n,keyframes:s}=this.options,r=(null==n?void 0:n.KeyframeResolver)||em;this.resolver=new r(s,(t,e)=>this.onKeyframesResolved(t,e),e,i,n),this.resolver.scheduleResolve()}flatten(){super.flatten(),this._resolved&&Object.assign(this._resolved,this.initPlayback(this._resolved.keyframes))}initPlayback(t){let e,i;let{type:n="keyframes",repeat:s=0,repeatDelay:r=0,repeatType:o,velocity:a=0}=this.options,l=x(n)?n:e7[n]||e6;l!==e6&&"number"!=typeof t[0]&&(e=eI(it,eX(t[0],t[1])),t=[0,100]);let u=l({...this.options,keyframes:t});"mirror"===o&&(i=l({...this.options,keyframes:[...t].reverse(),velocity:-a})),null===u.calculatedDuration&&(u.calculatedDuration=y(u));let{calculatedDuration:h}=u,d=h+r;return{generator:u,mirroredGenerator:i,mapPercentToKeyframes:e,calculatedDuration:h,resolvedDuration:d,totalDuration:d*(s+1)-r}}onPostResolved(){let{autoplay:t=!0}=this.options;this.play(),"paused"!==this.pendingPlayState&&t?this.state=this.pendingPlayState:this.pause()}tick(t,e=!1){let{resolved:i}=this;if(!i){let{keyframes:t}=this.options;return{done:!0,value:t[t.length-1]}}let{finalKeyframe:n,generator:s,mirroredGenerator:r,mapPercentToKeyframes:o,keyframes:a,calculatedDuration:l,totalDuration:u,resolvedDuration:h}=i;if(null===this.startTime)return s.next(0);let{delay:d,repeat:c,repeatType:p,repeatDelay:m,onUpdate:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-u/this.speed,this.startTime)),e?this.currentTime=t:null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=Math.round(t-this.startTime)*this.speed;let v=this.currentTime-d*(this.speed>=0?1:-1),g=this.speed>=0?v<0:v>u;this.currentTime=Math.max(v,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=u);let y=this.currentTime,x=s;if(c){let t=Math.min(this.currentTime,u)/h,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,c+1))%2&&("reverse"===p?(i=1-i,m&&(i-=m/h)):"mirror"===p&&(x=r)),y=tE(0,1,i)*h}let w=g?{done:!1,value:a[0]}:x.next(y);o&&(w.value=o(w.value));let{done:P}=w;g||null===l||(P=this.speed>=0?this.currentTime>=u:this.currentTime<=0);let T=null===this.holdTime&&("finished"===this.state||"running"===this.state&&P);return T&&void 0!==n&&(w.value=eV(a,this.options,n)),f&&f(w.value),T&&this.finish(),w}get duration(){let{resolved:t}=this;return t?N(t.calculatedDuration):0}get time(){return N(this.currentTime)}set time(t){t=I(t),this.currentTime=t,null!==this.holdTime||0===this.speed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.speed)}get speed(){return this.playbackSpeed}set speed(t){let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=N(this.currentTime))}play(){if(this.resolver.isScheduled||this.resolver.resume(),!this._resolved){this.pendingPlayState="running";return}if(this.isStopped)return;let{driver:t=e8,onPlay:e,startTime:i}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),e&&e();let n=this.driver.now();null!==this.holdTime?this.startTime=n-this.holdTime:this.startTime?"finished"===this.state&&(this.startTime=n):this.startTime=null!=i?i:this.calcStartTime(),"finished"===this.state&&this.updateFinishedPromise(),this.cancelTime=this.startTime,this.holdTime=null,this.state="running",this.driver.start()}pause(){var t;if(!this._resolved){this.pendingPlayState="paused";return}this.state="paused",this.holdTime=null!==(t=this.currentTime)&&void 0!==t?t:0}complete(){"running"!==this.state&&this.play(),this.pendingPlayState=this.state="finished",this.holdTime=null}finish(){this.teardown(),this.state="finished";let{onComplete:t}=this.options;t&&t()}cancel(){null!==this.cancelTime&&this.tick(this.cancelTime),this.teardown(),this.updateFinishedPromise()}teardown(){this.state="idle",this.stopDriver(),this.resolveFinishedPromise(),this.updateFinishedPromise(),this.startTime=this.cancelTime=null,this.resolver.cancel()}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}}let ii=new Set(["opacity","clipPath","filter","transform"]),is=p(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),ir={anticipate:tP,backInOut:tw,circInOut:tS};class io extends eC{constructor(t){super(t);let{name:e,motionValue:i,element:n,keyframes:s}=this.options;this.resolver=new eA(s,(t,e)=>this.onKeyframesResolved(t,e),e,i,n),this.resolver.scheduleResolve()}initPlayback(t,e){var i;let{duration:n=300,times:s,ease:r,type:o,motionValue:a,name:l,startTime:u}=this.options;if(!a.owner||!a.owner.current)return!1;if("string"==typeof r&&b()&&r in ir&&(r=ir[r]),x((i=this.options).type)||"spring"===i.type||!function t(e){return!!("function"==typeof e&&b()||!e||"string"==typeof e&&(e in M||b())||P(e)||Array.isArray(e)&&e.every(t))}(i.ease)){let{onComplete:e,onUpdate:i,motionValue:a,element:l,...u}=this.options,h=function(t,e){let i=new ie({...e,keyframes:t,repeat:0,delay:0,isGenerator:!0}),n={done:!1,value:t[0]},s=[],r=0;for(;!n.done&&r<2e4;)s.push((n=i.sample(r)).value),r+=10;return{times:void 0,keyframes:s,duration:r-10,ease:"linear"}}(t,u);1===(t=h.keyframes).length&&(t[1]=t[0]),n=h.duration,s=h.times,r=h.ease,o="keyframes"}let h=function(t,e,i,{delay:n=0,duration:s=300,repeat:r=0,repeatType:o="loop",ease:a="easeInOut",times:l}={}){let u={[e]:i};l&&(u.offset=l);let h=function t(e,i){if(e)return"function"==typeof e&&b()?A(e,i):P(e)?E(e):Array.isArray(e)?e.map(e=>t(e,i)||M.easeOut):M[e]}(a,s);return Array.isArray(h)&&(u.easing=h),t.animate(u,{delay:n,duration:s,easing:Array.isArray(h)?"linear":h,fill:"both",iterations:r+1,direction:"reverse"===o?"alternate":"normal"})}(a.owner.current,l,t,{...this.options,duration:n,times:s,ease:r});return h.startTime=null!=u?u:this.calcStartTime(),this.pendingTimeline?(w(h,this.pendingTimeline),this.pendingTimeline=void 0):h.onfinish=()=>{let{onComplete:i}=this.options;a.set(eV(t,this.options,e)),i&&i(),this.cancel(),this.resolveFinishedPromise()},{animation:h,duration:n,times:s,type:o,ease:r,keyframes:t}}get duration(){let{resolved:t}=this;if(!t)return 0;let{duration:e}=t;return N(e)}get time(){let{resolved:t}=this;if(!t)return 0;let{animation:e}=t;return N(e.currentTime||0)}set time(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.currentTime=I(t)}get speed(){let{resolved:t}=this;if(!t)return 1;let{animation:e}=t;return e.playbackRate}set speed(t){let{resolved:e}=this;if(!e)return;let{animation:i}=e;i.playbackRate=t}get state(){let{resolved:t}=this;if(!t)return"idle";let{animation:e}=t;return e.playState}get startTime(){let{resolved:t}=this;if(!t)return null;let{animation:e}=t;return e.startTime}attachTimeline(t){if(this._resolved){let{resolved:e}=this;if(!e)return $;let{animation:i}=e;w(i,t)}else this.pendingTimeline=t;return $}play(){if(this.isStopped)return;let{resolved:t}=this;if(!t)return;let{animation:e}=t;"finished"===e.playState&&this.updateFinishedPromise(),e.play()}pause(){let{resolved:t}=this;if(!t)return;let{animation:e}=t;e.pause()}stop(){if(this.resolver.cancel(),this.isStopped=!0,"idle"===this.state)return;this.resolveFinishedPromise(),this.updateFinishedPromise();let{resolved:t}=this;if(!t)return;let{animation:e,keyframes:i,duration:n,type:s,ease:r,times:o}=t;if("idle"===e.playState||"finished"===e.playState)return;if(this.time){let{motionValue:t,onUpdate:e,onComplete:a,element:l,...u}=this.options,h=new ie({...u,keyframes:i,duration:n,type:s,ease:r,times:o,isGenerator:!0}),d=I(this.time);t.setWithVelocity(h.sample(d-10).value,h.sample(d).value,10)}let{onStop:a}=this.options;a&&a(),this.cancel()}complete(){let{resolved:t}=this;t&&t.animation.finish()}cancel(){let{resolved:t}=this;t&&t.animation.cancel()}static supports(t){let{motionValue:e,name:i,repeatDelay:n,repeatType:s,damping:r,type:o}=t;if(!e||!e.owner||!(e.owner.current instanceof HTMLElement))return!1;let{onUpdate:a,transformTemplate:l}=e.owner.getProps();return is()&&i&&ii.has(i)&&!a&&!l&&!n&&"mirror"!==s&&0!==r&&"inertia"!==o}}let ia={type:"spring",stiffness:500,damping:25,restSpeed:10},il=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),iu={type:"keyframes",duration:.8},ih={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},id=(t,{keyframes:e})=>e.length>2?iu:z.has(t)?t.startsWith("scale")?il(e[1]):ia:ih,ic=(t,e,i,n={},s,r)=>o=>{let a=g(n,t)||{},l=a.delay||n.delay||0,{elapsed:u=0}=n;u-=I(l);let h={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...a,delay:-u,onUpdate:t=>{e.set(t),a.onUpdate&&a.onUpdate(t)},onComplete:()=>{o(),a.onComplete&&a.onComplete()},name:t,motionValue:e,element:r?void 0:s};!function({when:t,delay:e,delayChildren:i,staggerChildren:n,staggerDirection:s,repeat:r,repeatType:o,repeatDelay:a,from:l,elapsed:u,...h}){return!!Object.keys(h).length}(a)&&(h={...h,...id(t,h)}),h.duration&&(h.duration=I(h.duration)),h.repeatDelay&&(h.repeatDelay=I(h.repeatDelay)),void 0!==h.from&&(h.keyframes[0]=h.from);let d=!1;if(!1!==h.type&&(0!==h.duration||h.repeatDelay)||(h.duration=0,0!==h.delay||(d=!0)),(tp.current||q.skipAnimations)&&(d=!0,h.duration=0,h.delay=0),d&&!r&&void 0!==e.get()){let t=eV(h.keyframes,a);if(void 0!==t)return G.update(()=>{h.onUpdate(t),h.onComplete()}),new v([])}return!r&&io.supports(h)?new io(h):new ie(h)};function ip(t,e,{delay:i=0,transitionOverride:n,type:s}={}){var r;let{transition:o=t.getDefaultTransition(),transitionEnd:a,...l}=e;n&&(o=n);let u=[],d=s&&t.animationState&&t.animationState.getState()[s];for(let e in l){let n=t.getValue(e,null!==(r=t.latestValues[e])&&void 0!==r?r:null),s=l[e];if(void 0===s||d&&function({protectedKeys:t,needsAnimating:e},i){let n=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,n}(d,e))continue;let a={delay:i,...g(o||{},e)},h=!1;if(window.MotionHandoffAnimation){let i=t.props[tc];if(i){let t=window.MotionHandoffAnimation(i,e,G);null!==t&&(a.startTime=t,h=!0)}}th(t,e),n.start(ic(e,n,s,t.shouldReduceMotion&&H.has(e)?{type:!1}:a,t,h));let c=n.animation;c&&u.push(c)}return a&&Promise.all(u).then(()=>{G.update(()=>{a&&function(t,e){let{transitionEnd:i={},transition:n={},...s}=h(t,e)||{};for(let e in s={...s,...i}){let i=X(s[e]);t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,tl(i))}}(t,a)})}),u}function im(t,e,i={}){var n;let s=h(t,e,"exit"===i.type?null===(n=t.presenceContext)||void 0===n?void 0:n.custom:void 0),{transition:r=t.getDefaultTransition()||{}}=s||{};i.transitionOverride&&(r=i.transitionOverride);let o=s?()=>Promise.all(ip(t,s,i)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(n=0)=>{let{delayChildren:s=0,staggerChildren:o,staggerDirection:a}=r;return function(t,e,i=0,n=0,s=1,r){let o=[],a=(t.variantChildren.size-1)*n,l=1===s?(t=0)=>t*n:(t=0)=>a-t*n;return Array.from(t.variantChildren).sort(iv).forEach((t,n)=>{t.notify("AnimationStart",e),o.push(im(t,e,{...r,delay:i+l(n)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(o)}(t,e,s+n,o,a,i)}:()=>Promise.resolve(),{when:l}=r;if(!l)return Promise.all([o(),a(i.delay)]);{let[t,e]="beforeChildren"===l?[o,a]:[a,o];return t().then(()=>e())}}function iv(t,e){return t.sortNodePosition(e)}let ig=c.length,iy=[...d].reverse(),ix=d.length;function iw(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function iP(){return{animate:iw(!0),whileInView:iw(),whileHover:iw(),whileTap:iw(),whileDrag:iw(),whileFocus:iw(),exit:iw()}}class iT{constructor(t){this.isMounted=!1,this.node=t}update(){}}class ib extends iT{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let n;if(t.notify("AnimationStart",e),Array.isArray(e))n=Promise.all(e.map(e=>im(t,e,i)));else if("string"==typeof e)n=im(t,e,i);else{let s="function"==typeof e?h(t,e,i.custom):e;n=Promise.all(ip(t,s,i))}return n.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=iP(),n=!0,l=e=>(i,n)=>{var s;let r=h(t,n,"exit"===e?null===(s=t.presenceContext)||void 0===s?void 0:s.custom:void 0);if(r){let{transition:t,transitionEnd:e,...n}=r;i={...i,...n,...e}}return i};function u(u){let{props:h}=t,d=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<ig;t++){let n=c[t],s=e.props[n];(a(s)||!1===s)&&(i[n]=s)}return i}(t.parent)||{},p=[],m=new Set,f={},v=1/0;for(let e=0;e<ix;e++){var g;let c=iy[e],y=i[c],x=void 0!==h[c]?h[c]:d[c],w=a(x),P=c===u?y.isActive:null;!1===P&&(v=e);let T=x===d[c]&&x!==h[c]&&w;if(T&&n&&t.manuallyAnimateOnMount&&(T=!1),y.protectedKeys={...f},!y.isActive&&null===P||!x&&!y.prevProp||s(x)||"boolean"==typeof x)continue;let b=(g=y.prevProp,"string"==typeof x?x!==g:!!Array.isArray(x)&&!o(x,g)),S=b||c===u&&y.isActive&&!T&&w||e>v&&w,A=!1,E=Array.isArray(x)?x:[x],M=E.reduce(l(c),{});!1===P&&(M={});let{prevResolvedValues:V={}}=y,C={...V,...M},D=e=>{S=!0,m.has(e)&&(A=!0,m.delete(e)),y.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in C){let e=M[t],i=V[t];if(!f.hasOwnProperty(t))(r(e)&&r(i)?o(e,i):e===i)?void 0!==e&&m.has(t)?D(t):y.protectedKeys[t]=!0:null!=e?D(t):m.add(t)}y.prevProp=x,y.prevResolvedValues=M,y.isActive&&(f={...f,...M}),n&&t.blockInitialAnimation&&(S=!1);let k=!(T&&b)||A;S&&k&&p.push(...E.map(t=>({animation:t,options:{type:c}})))}if(m.size){let e={};m.forEach(i=>{let n=t.getBaseTarget(i),s=t.getValue(i);s&&(s.liveStyle=!0),e[i]=null!=n?n:null}),p.push({animation:e})}let y=!!p.length;return n&&(!1===h.initial||h.initial===h.animate)&&!t.manuallyAnimateOnMount&&(y=!1),n=!1,y?e(p):Promise.resolve()}return{animateChanges:u,setActive:function(e,n){var s;if(i[e].isActive===n)return Promise.resolve();null===(s=t.variantChildren)||void 0===s||s.forEach(t=>{var i;return null===(i=t.animationState)||void 0===i?void 0:i.setActive(e,n)}),i[e].isActive=n;let r=u(e);for(let t in i)i[t].protectedKeys={};return r},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=iP(),n=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();s(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){var t;this.node.animationState.reset(),null===(t=this.unmountControls)||void 0===t||t.call(this)}}let iS=0;class iA extends iT{constructor(){super(...arguments),this.id=iS++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let n=this.node.animationState.setActive("exit",!t);e&&!t&&n.then(()=>e(this.id))}mount(){let{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}function iE(t,e,i,n={passive:!0}){return t.addEventListener(e,i,n),()=>t.removeEventListener(e,i)}function iM(t){return{point:{x:t.pageX,y:t.pageY}}}let iV=t=>e=>R(e)&&t(e,iM(e));function iC(t,e,i,n){return iE(t,e,iV(i),n)}let iD=(t,e)=>Math.abs(t-e);class ik{constructor(t,e,{transformPagePoint:i,contextWindow:n,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{var t,e;if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let i=iL(this.lastMoveEventInfo,this.history),n=null!==this.startEvent,s=(t=i.offset,e={x:0,y:0},Math.sqrt(iD(t.x,e.x)**2+iD(t.y,e.y)**2)>=3);if(!n&&!s)return;let{point:r}=i,{timestamp:o}=J;this.history.push({...r,timestamp:o});let{onStart:a,onMove:l}=this.handlers;n||(a&&a(this.lastMoveEvent,i),this.startEvent=this.lastMoveEvent),l&&l(this.lastMoveEvent,i)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=iR(e,this.transformPagePoint),G.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:n,resumeAnimation:s}=this.handlers;if(this.dragSnapToOrigin&&s&&s(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let r=iL("pointercancel"===t.type?this.lastMoveEventInfo:iR(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,r),n&&n(t,r)},!R(t))return;this.dragSnapToOrigin=s,this.handlers=e,this.transformPagePoint=i,this.contextWindow=n||window;let r=iR(iM(t),this.transformPagePoint),{point:o}=r,{timestamp:a}=J;this.history=[{...o,timestamp:a}];let{onSessionStart:l}=e;l&&l(t,iL(r,this.history)),this.removeListeners=eI(iC(this.contextWindow,"pointermove",this.handlePointerMove),iC(this.contextWindow,"pointerup",this.handlePointerUp),iC(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),Z(this.updatePoint)}}function iR(t,e){return e?{point:e(t.point)}:t}function ij(t,e){return{x:t.x-e.x,y:t.y-e.y}}function iL({point:t},e){return{point:t,delta:ij(t,iF(e)),offset:ij(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,n=null,s=iF(t);for(;i>=0&&(n=t[i],!(s.timestamp-n.timestamp>I(.1)));)i--;if(!n)return{x:0,y:0};let r=N(s.timestamp-n.timestamp);if(0===r)return{x:0,y:0};let o={x:(s.x-n.x)/r,y:(s.y-n.y)/r};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}(e,0)}}function iF(t){return t[t.length-1]}function iB(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function iO(t){return t.max-t.min}function iU(t,e,i,n=.5){t.origin=n,t.originPoint=eD(e.min,e.max,t.origin),t.scale=iO(i)/iO(e),t.translate=eD(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function iI(t,e,i,n){iU(t.x,e.x,i.x,n?n.originX:void 0),iU(t.y,e.y,i.y,n?n.originY:void 0)}function iN(t,e,i){t.min=i.min+e.min,t.max=t.min+iO(e)}function i$(t,e,i){t.min=e.min-i.min,t.max=t.min+iO(e)}function iW(t,e,i){i$(t.x,e.x,i.x),i$(t.y,e.y,i.y)}function iz(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function iH(t,e){let i=e.min-t.min,n=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,n]=[n,i]),{min:i,max:n}}function iY(t,e,i){return{min:iX(t,e),max:iX(t,i)}}function iX(t,e){return"number"==typeof t?t:t[e]||0}let iq=()=>({translate:0,scale:1,origin:0,originPoint:0}),iK=()=>({x:iq(),y:iq()}),i_=()=>({min:0,max:0}),iG=()=>({x:i_(),y:i_()});function iZ(t){return[t("x"),t("y")]}function iJ({top:t,left:e,right:i,bottom:n}){return{x:{min:e,max:i},y:{min:t,max:n}}}function iQ(t){return void 0===t||1===t}function i0({scale:t,scaleX:e,scaleY:i}){return!iQ(t)||!iQ(e)||!iQ(i)}function i1(t){return i0(t)||i5(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function i5(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function i2(t,e,i,n,s){return void 0!==s&&(t=n+s*(t-n)),n+i*(t-n)+e}function i3(t,e=0,i=1,n,s){t.min=i2(t.min,e,i,n,s),t.max=i2(t.max,e,i,n,s)}function i9(t,{x:e,y:i}){i3(t.x,e.translate,e.scale,e.originPoint),i3(t.y,i.translate,i.scale,i.originPoint)}function i4(t,e){t.min=t.min+e,t.max=t.max+e}function i6(t,e,i,n,s=.5){let r=eD(t.min,t.max,s);i3(t,e,i,r,n)}function i8(t,e){i6(t.x,e.x,e.scaleX,e.scale,e.originX),i6(t.y,e.y,e.scaleY,e.scale,e.originY)}function i7(t,e){return iJ(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),n=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:n.y,right:n.x}}(t.getBoundingClientRect(),e))}let nt=({current:t})=>t?t.ownerDocument.defaultView:null,ne=new WeakMap;class ni{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iG(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:n}=this.getProps();this.panSession=new ik(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(iM(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:n,onDragStart:s}=this.getProps();if(i&&!n&&(this.openDragLock&&this.openDragLock(),this.openDragLock="x"===i||"y"===i?V[i]?null:(V[i]=!0,()=>{V[i]=!1}):V.x||V.y?null:(V.x=V.y=!0,()=>{V.x=V.y=!1}),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iZ(t=>{let e=this.getAxisMotionValue(t).get()||0;if(t$.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let n=i.layout.layoutBox[t];if(n){let t=iO(n);e=parseFloat(e)/100*t}}}this.originPoint[t]=e}),s&&G.postRender(()=>s(t,e)),th(this.visualElement,"transform");let{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:n,onDirectionLock:s,onDrag:r}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:o}=e;if(n&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(o),null!==this.currentDirection&&s&&s(this.currentDirection);return}this.updateAxis("x",e.point,o),this.updateAxis("y",e.point,o),this.visualElement.render(),r&&r(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>iZ(t=>{var e;return"paused"===this.getAnimationState(t)&&(null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.play())})},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:n,contextWindow:nt(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:n}=e;this.startAnimation(n);let{onDragEnd:s}=this.getProps();s&&G.postRender(()=>s(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:n}=this.getProps();if(!i||!nn(t,n,this.currentDirection))return;let s=this.getAxisMotionValue(t),r=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(r=function(t,{min:e,max:i},n){return void 0!==e&&t<e?t=n?eD(e,t,n.min):Math.max(t,e):void 0!==i&&t>i&&(t=n?eD(i,t,n.max):Math.min(t,i)),t}(r,this.constraints[t],this.elastic[t])),s.set(r)}resolveConstraints(){var t;let{dragConstraints:e,dragElastic:i}=this.getProps(),n=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):null===(t=this.visualElement.projection)||void 0===t?void 0:t.layout,s=this.constraints;e&&iB(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&n?this.constraints=function(t,{top:e,left:i,bottom:n,right:s}){return{x:iz(t.x,i,s),y:iz(t.y,e,n)}}(n.layoutBox,e):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:iY(t,"left","right"),y:iY(t,"top","bottom")}}(i),s!==this.constraints&&n&&this.constraints&&!this.hasMutatedConstraints&&iZ(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(n.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!iB(e))return!1;let n=e.current;$(null!==n,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:s}=this.visualElement;if(!s||!s.layout)return!1;let r=function(t,e,i){let n=i7(t,i),{scroll:s}=e;return s&&(i4(n.x,s.offset.x),i4(n.y,s.offset.y)),n}(n,s.root,this.visualElement.getTransformPagePoint()),o={x:iH((t=s.layout.layoutBox).x,r.x),y:iH(t.y,r.y)};if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(o));this.hasMutatedConstraints=!!t,t&&(o=iJ(t))}return o}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:n,dragTransition:s,dragSnapToOrigin:r,onDragTransitionEnd:o}=this.getProps(),a=this.constraints||{};return Promise.all(iZ(o=>{if(!nn(o,e,this.currentDirection))return;let l=a&&a[o]||{};r&&(l={min:0,max:0});let u={type:"inertia",velocity:i?t[o]:0,bounceStiffness:n?200:1e6,bounceDamping:n?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...s,...l};return this.startAxisValueAnimation(o,u)})).then(o)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return th(this.visualElement,t),i.start(ic(t,i,0,e,this.visualElement,!1))}stopAnimation(){iZ(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){iZ(t=>{var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.pause()})}getAnimationState(t){var e;return null===(e=this.getAxisMotionValue(t).animation)||void 0===e?void 0:e.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){iZ(e=>{let{drag:i}=this.getProps();if(!nn(e,i,this.currentDirection))return;let{projection:n}=this.visualElement,s=this.getAxisMotionValue(e);if(n&&n.layout){let{min:i,max:r}=n.layout.layoutBox[e];s.set(t[e]-eD(i,r,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!iB(e)||!i||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};iZ(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();n[t]=function(t,e){let i=.5,n=iO(t),s=iO(e);return s>n?i=S(e.min,e.max-n,t.min):n>s&&(i=S(t.min,t.max-s,e.min)),tE(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iZ(e=>{if(!nn(e,t,null))return;let i=this.getAxisMotionValue(e),{min:s,max:r}=this.constraints[e];i.set(eD(s,r,n[e]))})}addListeners(){if(!this.visualElement.current)return;ne.set(this.visualElement,this);let t=iC(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();iB(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,n=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),G.read(e);let s=iE(window,"resize",()=>this.scalePositionWithinConstraints()),r=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(iZ(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{s(),t(),n(),r&&r()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:n=!1,dragConstraints:s=!1,dragElastic:r=.35,dragMomentum:o=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:n,dragConstraints:s,dragElastic:r,dragMomentum:o}}}function nn(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class ns extends iT{constructor(t){super(t),this.removeGroupControls=$,this.removeListeners=$,this.controls=new ni(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||$}unmount(){this.removeGroupControls(),this.removeListeners()}}let nr=t=>(e,i)=>{t&&G.postRender(()=>t(e,i))};class no extends iT{constructor(){super(...arguments),this.removePointerDownListener=$}onPointerDown(t){this.session=new ik(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:nt(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:n}=this.node.getProps();return{onSessionStart:nr(t),onStart:nr(e),onMove:i,onEnd:(t,e)=>{delete this.session,n&&G.postRender(()=>n(t,e))}}}mount(){this.removePointerDownListener=iC(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var na,nl,nu,nh=i(57437),nd=i(2265),nc=i(49637),np=i(58881);let nm=(0,nd.createContext)({}),nf={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function nv(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let ng={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t){if(!tW.test(t))return t;t=parseFloat(t)}let i=nv(t,e.target.x),n=nv(t,e.target.y);return`${i}% ${n}%`}},ny={},{schedule:nx,cancel:nw}=_(queueMicrotask,!1);class nP extends nd.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:n}=this.props,{projection:s}=t;Object.assign(ny,nb),s&&(e.group&&e.group.add(s),i&&i.register&&n&&i.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),nf.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:n,isPresent:s}=this.props,r=i.projection;return r&&(r.isPresent=s,n||t.layoutDependency!==e||void 0===e?r.willUpdate():this.safeToRemove(),t.isPresent===s||(s?r.promote():r.relegate()||G.postRender(()=>{let t=r.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),nx.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:n}=t;n&&(n.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(n),i&&i.deregister&&i.deregister(n))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function nT(t){let[e,i]=(0,nc.oO)(),n=(0,nd.useContext)(np.p);return(0,nh.jsx)(nP,{...t,layoutGroup:n,switchLayoutGroup:(0,nd.useContext)(nm),isPresent:e,safeToRemove:i})}let nb={borderRadius:{...ng,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ng,borderTopRightRadius:ng,borderBottomLeftRadius:ng,borderBottomRightRadius:ng,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let n=t5.parse(t);if(n.length>5)return t;let s=t5.createTransformer(t),r="number"!=typeof n[0]?1:0,o=i.x.scale*e.x,a=i.y.scale*e.y;n[0+r]/=o,n[1+r]/=a;let l=eD(o,a,.5);return"number"==typeof n[2+r]&&(n[2+r]/=l),"number"==typeof n[3+r]&&(n[3+r]/=l),s(n)}}},nS=(t,e)=>t.depth-e.depth;class nA{constructor(){this.children=[],this.isDirty=!1}add(t){ti(this.children,t),this.isDirty=!0}remove(t){tn(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(nS),this.isDirty=!1,this.children.forEach(t)}}function nE(t){let e=tu(t)?t.get():t;return Y(e)?e.toValue():e}let nM=["TopLeft","TopRight","BottomLeft","BottomRight"],nV=nM.length,nC=t=>"string"==typeof t?parseFloat(t):t,nD=t=>"number"==typeof t||tW.test(t);function nk(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let nR=nL(0,.5,tb),nj=nL(.5,.95,$);function nL(t,e,i){return n=>n<t?0:n>e?1:i(S(t,e,n))}function nF(t,e){t.min=e.min,t.max=e.max}function nB(t,e){nF(t.x,e.x),nF(t.y,e.y)}function nO(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function nU(t,e,i,n,s){return t-=e,t=n+1/i*(t-n),void 0!==s&&(t=n+1/s*(t-n)),t}function nI(t,e,[i,n,s],r,o){!function(t,e=0,i=1,n=.5,s,r=t,o=t){if(t$.test(e)&&(e=parseFloat(e),e=eD(o.min,o.max,e/100)-o.min),"number"!=typeof e)return;let a=eD(r.min,r.max,n);t===r&&(a-=e),t.min=nU(t.min,e,i,a,s),t.max=nU(t.max,e,i,a,s)}(t,e[i],e[n],e[s],e.scale,r,o)}let nN=["x","scaleX","originX"],n$=["y","scaleY","originY"];function nW(t,e,i,n){nI(t.x,e,nN,i?i.x:void 0,n?n.x:void 0),nI(t.y,e,n$,i?i.y:void 0,n?n.y:void 0)}function nz(t){return 0===t.translate&&1===t.scale}function nH(t){return nz(t.x)&&nz(t.y)}function nY(t,e){return t.min===e.min&&t.max===e.max}function nX(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function nq(t,e){return nX(t.x,e.x)&&nX(t.y,e.y)}function nK(t){return iO(t.x)/iO(t.y)}function n_(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class nG{constructor(){this.members=[]}add(t){ti(this.members,t),t.scheduleRender()}remove(t){if(tn(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e;let i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:n}=t.options;!1===n&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let nZ={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0},nJ="undefined"!=typeof window&&void 0!==window.MotionDebug,nQ=["","X","Y","Z"],n0={visibility:"hidden"},n1=0;function n5(t,e,i,n){let{latestValues:s}=e;s[t]&&(i[t]=s[t],e.setStaticValue(t,0),n&&(n[t]=0))}function n2({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:n,resetTransform:s}){return class{constructor(t={},i=null==e?void 0:e()){this.id=n1++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,nJ&&(nZ.totalNodes=nZ.resolvedTargetDeltas=nZ.recalculatedProjection=0),this.nodes.forEach(n4),this.nodes.forEach(sn),this.nodes.forEach(ss),this.nodes.forEach(n6),nJ&&window.MotionDebug.record(nZ)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new nA)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new ts),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e,i=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=e instanceof SVGElement&&"svg"!==e.tagName,this.instance=e;let{layoutId:n,layout:s,visualElement:r}=this.options;if(r&&!r.current&&r.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),i&&(s||n)&&(this.isLayoutDirty=!0),t){let i;let n=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=te.now(),n=({timestamp:e})=>{let s=e-i;s>=250&&(Z(n),t(s-250))};return G.read(n,!0),()=>Z(n)}(n,0),nf.hasAnimatedSinceResize&&(nf.hasAnimatedSinceResize=!1,this.nodes.forEach(si))})}n&&this.root.registerSharedNode(n,this),!1!==this.options.animate&&r&&(n||s)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeTargetChanged:i,layout:n})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let s=this.options.transition||r.getDefaultTransition()||sh,{onLayoutAnimationStart:o,onLayoutAnimationComplete:a}=r.getProps(),l=!this.targetLayout||!nq(this.targetLayout,n)||i,u=!e&&i;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||u||e&&(l||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(t,u);let e={...g(s,"layout"),onPlay:o,onComplete:a};(r.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e)}else e||si(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=n})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,Z(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(sr),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let n=i.props[tc];if(window.MotionHasOptimisedAnimation(n,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(n,"transform",G,!(t||i))}let{parent:s}=e;s&&!s.hasCheckedOptimisedAppear&&t(s)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let n=this.getTransformTemplate();this.prevTransformTemplateValue=n?n(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(n7);return}this.isUpdating||this.nodes.forEach(st),this.isUpdating=!1,this.nodes.forEach(se),this.nodes.forEach(n3),this.nodes.forEach(n9),this.clearAllSnapshots();let t=te.now();J.delta=tE(0,1e3/60,t-J.timestamp),J.timestamp=t,J.isProcessing=!0,Q.update.process(J),Q.preRender.process(J),Q.render.process(J),J.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,nx.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(n8),this.sharedNodes.forEach(so)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,G.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){G.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iG(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e){let e=n(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!s)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!nH(this.projectionDelta),i=this.getTransformTemplate(),n=i?i(this.latestValues,""):void 0,r=n!==this.prevTransformTemplateValue;t&&(e||i1(this.latestValues)||r)&&(s(this.instance,n),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),n=this.removeElementScroll(i);return t&&(n=this.removeTransform(n)),sp((e=n).x),sp(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:n,latestValues:{},source:this.id}}measurePageBox(){var t;let{visualElement:e}=this.options;if(!e)return iG();let i=e.measureViewportBox();if(!((null===(t=this.scroll)||void 0===t?void 0:t.wasRoot)||this.path.some(sf))){let{scroll:t}=this.root;t&&(i4(i.x,t.offset.x),i4(i.y,t.offset.y))}return i}removeElementScroll(t){var e;let i=iG();if(nB(i,t),null===(e=this.scroll)||void 0===e?void 0:e.wasRoot)return i;for(let e=0;e<this.path.length;e++){let n=this.path[e],{scroll:s,options:r}=n;n!==this.root&&s&&r.layoutScroll&&(s.wasRoot&&nB(i,t),i4(i.x,s.offset.x),i4(i.y,s.offset.y))}return i}applyTransform(t,e=!1){let i=iG();nB(i,t);for(let t=0;t<this.path.length;t++){let n=this.path[t];!e&&n.options.layoutScroll&&n.scroll&&n!==n.root&&i8(i,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),i1(n.latestValues)&&i8(i,n.latestValues)}return i1(this.latestValues)&&i8(i,this.latestValues),i}removeTransform(t){let e=iG();nB(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!i1(i.latestValues))continue;i0(i.latestValues)&&i.updateSnapshot();let n=iG();nB(n,i.measurePageBox()),nW(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,n)}return i1(this.latestValues)&&nW(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==J.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){var e,i,n,s;let r=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=r.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=r.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=r.isSharedProjectionDirty);let o=!!this.resumingFrom||this!==r;if(!(t||o&&this.isSharedProjectionDirty||this.isProjectionDirty||(null===(e=this.parent)||void 0===e?void 0:e.isProjectionDirty)||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:a,layoutId:l}=this.options;if(this.layout&&(a||l)){if(this.resolvedRelativeTargetAt=J.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iG(),this.relativeTargetOrigin=iG(),iW(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),nB(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if((this.target||(this.target=iG(),this.targetWithTransforms=iG()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target)?(this.forceRelativeParentToResolveTarget(),i=this.target,n=this.relativeTarget,s=this.relativeParent.target,iN(i.x,n.x,s.x),iN(i.y,n.y,s.y)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):nB(this.target,this.layout.layoutBox),i9(this.target,this.targetDelta)):nB(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iG(),this.relativeTargetOrigin=iG(),iW(this.relativeTargetOrigin,this.target,t.target),nB(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}nJ&&nZ.resolvedTargetDeltas++}}}getClosestProjectingParent(){return!this.parent||i0(this.parent.latestValues)||i5(this.parent.latestValues)?void 0:this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var t;let e=this.getLead(),i=!!this.resumingFrom||this!==e,n=!0;if((this.isProjectionDirty||(null===(t=this.parent)||void 0===t?void 0:t.isProjectionDirty))&&(n=!1),i&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(n=!1),this.resolvedRelativeTargetAt===J.timestamp&&(n=!1),n)return;let{layout:s,layoutId:r}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(s||r))return;nB(this.layoutCorrected,this.layout.layoutBox);let o=this.treeScale.x,a=this.treeScale.y;!function(t,e,i,n=!1){let s,r;let o=i.length;if(o){e.x=e.y=1;for(let a=0;a<o;a++){r=(s=i[a]).projectionDelta;let{visualElement:o}=s.options;(!o||!o.props.style||"contents"!==o.props.style.display)&&(n&&s.options.layoutScroll&&s.scroll&&s!==s.root&&i8(t,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,i9(t,r)),n&&i1(s.latestValues)&&i8(t,s.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,i),e.layout&&!e.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(e.target=e.layout.layoutBox,e.targetWithTransforms=iG());let{target:l}=e;if(!l){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(nO(this.prevProjectionDelta.x,this.projectionDelta.x),nO(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iI(this.projectionDelta,this.layoutCorrected,l,this.latestValues),this.treeScale.x===o&&this.treeScale.y===a&&n_(this.projectionDelta.x,this.prevProjectionDelta.x)&&n_(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",l)),nJ&&nZ.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){var e;if(null===(e=this.options.visualElement)||void 0===e||e.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=iK(),this.projectionDelta=iK(),this.projectionDeltaWithTransform=iK()}setAnimationOrigin(t,e=!1){let i;let n=this.snapshot,s=n?n.latestValues:{},r={...this.latestValues},o=iK();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let a=iG(),l=(n?n.source:void 0)!==(this.layout?this.layout.source:void 0),u=this.getStack(),h=!u||u.members.length<=1,d=!!(l&&!h&&!0===this.options.crossfade&&!this.path.some(su));this.animationProgress=0,this.mixTargetDelta=e=>{let n=e/1e3;if(sa(o.x,t.x,n),sa(o.y,t.y,n),this.setTargetDelta(o),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var u,c,p,m;iW(a,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,sl(p.x,m.x,a.x,n),sl(p.y,m.y,a.y,n),i&&(u=this.relativeTarget,c=i,nY(u.x,c.x)&&nY(u.y,c.y))&&(this.isProjectionDirty=!1),i||(i=iG()),nB(i,this.relativeTarget)}l&&(this.animationValues=r,function(t,e,i,n,s,r){s?(t.opacity=eD(0,void 0!==i.opacity?i.opacity:1,nR(n)),t.opacityExit=eD(void 0!==e.opacity?e.opacity:1,0,nj(n))):r&&(t.opacity=eD(void 0!==e.opacity?e.opacity:1,void 0!==i.opacity?i.opacity:1,n));for(let s=0;s<nV;s++){let r=`border${nM[s]}Radius`,o=nk(e,r),a=nk(i,r);(void 0!==o||void 0!==a)&&(o||(o=0),a||(a=0),0===o||0===a||nD(o)===nD(a)?(t[r]=Math.max(eD(nC(o),nC(a),n),0),(t$.test(a)||t$.test(o))&&(t[r]+="%")):t[r]=a)}(e.rotate||i.rotate)&&(t.rotate=eD(e.rotate||0,i.rotate||0,n))}(r,s,this.latestValues,n,d,h)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=n},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(Z(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=G.update(()=>{nf.hasAnimatedSinceResize=!0,this.currentAnimation=function(t,e,i){let n=tu(0)?0:tl(0);return n.start(ic("",n,1e3,i)),n.animation}(0,0,{...t,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onComplete:()=>{t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:n,latestValues:s}=t;if(e&&i&&n){if(this!==t&&this.layout&&n&&sm(this.options.animationType,this.layout.layoutBox,n.layoutBox)){i=this.target||iG();let e=iO(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let n=iO(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+n}nB(e,i),i8(e,s),iI(this.projectionDeltaWithTransform,this.layoutCorrected,e,s)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new nG),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){var t;let{layoutId:e}=this.options;return e&&(null===(t=this.getStack())||void 0===t?void 0:t.lead)||this}getPrevLead(){var t;let{layoutId:e}=this.options;return e?null===(t=this.getStack())||void 0===t?void 0:t.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let n=this.getStack();n&&n.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let n={};i.z&&n5("z",t,n,this.animationValues);for(let e=0;e<nQ.length;e++)n5(`rotate${nQ[e]}`,t,n,this.animationValues),n5(`skew${nQ[e]}`,t,n,this.animationValues);for(let e in t.render(),n)t.setStaticValue(e,n[e]),this.animationValues&&(this.animationValues[e]=n[e]);t.scheduleRender()}getProjectionStyles(t){var e,i;if(!this.instance||this.isSVG)return;if(!this.isVisible)return n0;let n={visibility:""},s=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,n.opacity="",n.pointerEvents=nE(null==t?void 0:t.pointerEvents)||"",n.transform=s?s(this.latestValues,""):"none",n;let r=this.getLead();if(!this.projectionDelta||!this.layout||!r.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=nE(null==t?void 0:t.pointerEvents)||""),this.hasProjected&&!i1(this.latestValues)&&(e.transform=s?s({},""):"none",this.hasProjected=!1),e}let o=r.animationValues||r.latestValues;this.applyTransformsToTarget(),n.transform=function(t,e,i){let n="",s=t.x.translate/e.x,r=t.y.translate/e.y,o=(null==i?void 0:i.z)||0;if((s||r||o)&&(n=`translate3d(${s}px, ${r}px, ${o}px) `),(1!==e.x||1!==e.y)&&(n+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:s,rotateY:r,skewX:o,skewY:a}=i;t&&(n=`perspective(${t}px) ${n}`),e&&(n+=`rotate(${e}deg) `),s&&(n+=`rotateX(${s}deg) `),r&&(n+=`rotateY(${r}deg) `),o&&(n+=`skewX(${o}deg) `),a&&(n+=`skewY(${a}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==a||1!==l)&&(n+=`scale(${a}, ${l})`),n||"none"}(this.projectionDeltaWithTransform,this.treeScale,o),s&&(n.transform=s(o,n.transform));let{x:a,y:l}=this.projectionDelta;for(let t in n.transformOrigin=`${100*a.origin}% ${100*l.origin}% 0`,r.animationValues?n.opacity=r===this?null!==(i=null!==(e=o.opacity)&&void 0!==e?e:this.latestValues.opacity)&&void 0!==i?i:1:this.preserveOpacity?this.latestValues.opacity:o.opacityExit:n.opacity=r===this?void 0!==o.opacity?o.opacity:"":void 0!==o.opacityExit?o.opacityExit:0,ny){if(void 0===o[t])continue;let{correct:e,applyTo:i}=ny[t],s="none"===n.transform?o[t]:e(o[t],r);if(i){let t=i.length;for(let e=0;e<t;e++)n[i[e]]=s}else n[t]=s}return this.options.layoutId&&(n.pointerEvents=r===this?nE(null==t?void 0:t.pointerEvents)||"":"none"),n}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>{var e;return null===(e=t.currentAnimation)||void 0===e?void 0:e.stop()}),this.root.nodes.forEach(n7),this.root.sharedNodes.clear()}}}function n3(t){t.updateLayout()}function n9(t){var e;let i=(null===(e=t.resumeFrom)||void 0===e?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&i&&t.hasListeners("didUpdate")){let{layoutBox:e,measuredBox:n}=t.layout,{animationType:s}=t.options,r=i.source!==t.layout.source;"size"===s?iZ(t=>{let n=r?i.measuredBox[t]:i.layoutBox[t],s=iO(n);n.min=e[t].min,n.max=n.min+s}):sm(s,i.layoutBox,e)&&iZ(n=>{let s=r?i.measuredBox[n]:i.layoutBox[n],o=iO(e[n]);s.max=s.min+o,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[n].max=t.relativeTarget[n].min+o)});let o=iK();iI(o,e,i.layoutBox);let a=iK();r?iI(a,t.applyTransform(n,!0),i.measuredBox):iI(a,e,i.layoutBox);let l=!nH(o),u=!1;if(!t.resumeFrom){let n=t.getClosestProjectingParent();if(n&&!n.resumeFrom){let{snapshot:s,layout:r}=n;if(s&&r){let o=iG();iW(o,i.layoutBox,s.layoutBox);let a=iG();iW(a,e,r.layoutBox),nq(o,a)||(u=!0),n.options.layoutRoot&&(t.relativeTarget=a,t.relativeTargetOrigin=o,t.relativeParent=n)}}}t.notifyListeners("didUpdate",{layout:e,snapshot:i,delta:a,layoutDelta:o,hasLayoutChanged:l,hasRelativeTargetChanged:u})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function n4(t){nJ&&nZ.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function n6(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function n8(t){t.clearSnapshot()}function n7(t){t.clearMeasurements()}function st(t){t.isLayoutDirty=!1}function se(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function si(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function sn(t){t.resolveTargetDelta()}function ss(t){t.calcProjection()}function sr(t){t.resetSkewAndRotation()}function so(t){t.removeLeadSnapshot()}function sa(t,e,i){t.translate=eD(e.translate,0,i),t.scale=eD(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function sl(t,e,i,n){t.min=eD(e.min,i.min,n),t.max=eD(e.max,i.max,n)}function su(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let sh={duration:.45,ease:[.4,0,.1,1]},sd=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),sc=sd("applewebkit/")&&!sd("chrome/")?Math.round:$;function sp(t){t.min=sc(t.min),t.max=sc(t.max)}function sm(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(nK(e)-nK(i)))}function sf(t){var e;return t!==t.root&&(null===(e=t.scroll)||void 0===e?void 0:e.wasRoot)}let sv=n2({attachResizeListener:(t,e)=>iE(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),sg={current:void 0},sy=n2({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!sg.current){let t=new sv({});t.mount(window),t.setOptions({layoutScroll:!0}),sg.current=t}return sg.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function sx(t,e,i){let{props:n}=t;t.animationState&&n.whileHover&&t.animationState.setActive("whileHover","Start"===i);let s=n["onHover"+i];s&&G.postRender(()=>s(e,iM(e)))}class sw extends iT{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,s,r]=C(t,i),o=D(t=>{let{target:i}=t,n=e(t);if("function"!=typeof n||!i)return;let r=D(t=>{n(t),i.removeEventListener("pointerleave",r)});i.addEventListener("pointerleave",r,s)});return n.forEach(t=>{t.addEventListener("pointerenter",o,s)}),r}(t,t=>(sx(this.node,t,"Start"),t=>sx(this.node,t,"End"))))}unmount(){}}class sP extends iT{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=eI(iE(this.node.current,"focus",()=>this.onFocus()),iE(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}function sT(t,e,i){let{props:n}=t;t.animationState&&n.whileTap&&t.animationState.setActive("whileTap","Start"===i);let s=n["onTap"+("End"===i?"":i)];s&&G.postRender(()=>s(e,iM(e)))}class sb extends iT{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[n,s,r]=C(t,i),o=t=>{let n=t.currentTarget;if(!U(t)||L.has(n))return;L.add(n);let r=e(t),o=(t,e)=>{window.removeEventListener("pointerup",a),window.removeEventListener("pointercancel",l),U(t)&&L.has(n)&&(L.delete(n),"function"==typeof r&&r(t,{success:e}))},a=t=>{o(t,i.useGlobalTarget||k(n,t.target))},l=t=>{o(t,!1)};window.addEventListener("pointerup",a,s),window.addEventListener("pointercancel",l,s)};return n.forEach(t=>{j.has(t.tagName)||-1!==t.tabIndex||null!==t.getAttribute("tabindex")||(t.tabIndex=0),(i.useGlobalTarget?window:t).addEventListener("pointerdown",o,s),t.addEventListener("focus",t=>O(t,s),s)}),r}(t,t=>(sT(this.node,t,"Start"),(t,{success:e})=>sT(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let sS=new WeakMap,sA=new WeakMap,sE=t=>{let e=sS.get(t.target);e&&e(t)},sM=t=>{t.forEach(sE)},sV={some:0,all:1};class sC extends iT{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:n="some",once:s}=t,r={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof n?n:sV[n]};return function(t,e,i){let n=function({root:t,...e}){let i=t||document;sA.has(i)||sA.set(i,{});let n=sA.get(i),s=JSON.stringify(e);return n[s]||(n[s]=new IntersectionObserver(sM,{root:t,...e})),n[s]}(e);return sS.set(t,i),n.observe(t),()=>{sS.delete(t),n.unobserve(t)}}(this.node.current,r,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,s&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:n}=this.node.getProps(),r=e?i:n;r&&r(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let sD=(0,nd.createContext)({strict:!1});var sk=i(45750);let sR=(0,nd.createContext)({});function sj(t){return s(t.animate)||c.some(e=>a(t[e]))}function sL(t){return!!(sj(t)||t.variants)}function sF(t){return Array.isArray(t)?t.join(" "):t}var sB=i(44563);let sO={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},sU={};for(let t in sO)sU[t]={isEnabled:e=>sO[t].some(t=>!!e[t])};let sI=Symbol.for("motionComponentSymbol");var sN=i(64252),s$=i(11534);let sW=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function sz(t){if("string"!=typeof t||t.includes("-"));else if(sW.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}var sH=i(53576);let sY=t=>(e,i)=>{let n=(0,nd.useContext)(sR),r=(0,nd.useContext)(sN.O),o=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e,onUpdate:i},n,r,o){let a={latestValues:function(t,e,i,n){let r={},o=n(t,{});for(let t in o)r[t]=nE(o[t]);let{initial:a,animate:l}=t,h=sj(t),d=sL(t);e&&d&&!h&&!1!==t.inherit&&(void 0===a&&(a=e.initial),void 0===l&&(l=e.animate));let c=!!i&&!1===i.initial,p=(c=c||!1===a)?l:a;if(p&&"boolean"!=typeof p&&!s(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let n=u(t,e[i]);if(n){let{transitionEnd:t,transition:e,...i}=n;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=c?e.length-1:0;e=e[t]}null!==e&&(r[t]=e)}for(let e in t)r[e]=t[e]}}}return r}(n,r,o,t),renderState:e()};return i&&(a.onMount=t=>i({props:n,current:t,...a}),a.onUpdate=t=>i(t)),a})(t,e,n,r);return i?o():(0,sH.h)(o)},sX=(t,e)=>e&&"number"==typeof t?e.transform(t):t,sq={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},sK=W.length;function s_(t,e,i){let{style:n,vars:s,transformOrigin:r}=t,o=!1,a=!1;for(let t in e){let i=e[t];if(z.has(t)){o=!0;continue}if(eg(t)){s[t]=i;continue}{let e=sX(i,t8[t]);t.startsWith("origin")?(a=!0,r[t]=e):n[t]=e}}if(!e.transform&&(o||i?n.transform=function(t,e,i){let n="",s=!0;for(let r=0;r<sK;r++){let o=W[r],a=t[o];if(void 0===a)continue;let l=!0;if(!(l="number"==typeof a?a===(o.startsWith("scale")?1:0):0===parseFloat(a))||i){let t=sX(a,t8[o]);if(!l){s=!1;let e=sq[o]||o;n+=`${e}(${t}) `}i&&(e[o]=t)}}return n=n.trim(),i?n=i(e,s?"":n):s&&(n="none"),n}(e,t.transform,i):n.transform&&(n.transform="none")),a){let{originX:t="50%",originY:e="50%",originZ:i=0}=r;n.transformOrigin=`${t} ${e} ${i}`}}let sG={offset:"stroke-dashoffset",array:"stroke-dasharray"},sZ={offset:"strokeDashoffset",array:"strokeDasharray"};function sJ(t,e,i){return"string"==typeof t?t:tW.transform(e+i*t)}function sQ(t,{attrX:e,attrY:i,attrScale:n,originX:s,originY:r,pathLength:o,pathSpacing:a=1,pathOffset:l=0,...u},h,d){if(s_(t,u,d),h){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:c,style:p,dimensions:m}=t;c.transform&&(m&&(p.transform=c.transform),delete c.transform),m&&(void 0!==s||void 0!==r||p.transform)&&(p.transformOrigin=function(t,e,i){let n=sJ(e,t.x,t.width),s=sJ(i,t.y,t.height);return`${n} ${s}`}(m,void 0!==s?s:.5,void 0!==r?r:.5)),void 0!==e&&(c.x=e),void 0!==i&&(c.y=i),void 0!==n&&(c.scale=n),void 0!==o&&function(t,e,i=1,n=0,s=!0){t.pathLength=1;let r=s?sG:sZ;t[r.offset]=tW.transform(-n);let o=tW.transform(e),a=tW.transform(i);t[r.array]=`${o} ${a}`}(c,o,a,l,!1)}let s0=()=>({style:{},transform:{},transformOrigin:{},vars:{}}),s1=()=>({...s0(),attrs:{}}),s5=t=>"string"==typeof t&&"svg"===t.toLowerCase();function s2(t,{style:e,vars:i},n,s){for(let r in Object.assign(t.style,e,s&&s.getProjectionStyles(n)),i)t.style.setProperty(r,i[r])}let s3=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function s9(t,e,i,n){for(let i in s2(t,e,void 0,n),e.attrs)t.setAttribute(s3.has(i)?i:td(i),e.attrs[i])}function s4(t,{layout:e,layoutId:i}){return z.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!ny[t]||"opacity"===t)}function s6(t,e,i){var n;let{style:s}=t,r={};for(let o in s)(tu(s[o])||e.style&&tu(e.style[o])||s4(o,t)||(null===(n=null==i?void 0:i.getValue(o))||void 0===n?void 0:n.liveStyle)!==void 0)&&(r[o]=s[o]);return r}function s8(t,e,i){let n=s6(t,e,i);for(let i in t)(tu(t[i])||tu(e[i]))&&(n[-1!==W.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return n}let s7=["x","y","width","height","cx","cy","r"],rt={useVisualState:sY({scrapeMotionValuesFromProps:s8,createRenderState:s1,onUpdate:({props:t,prevProps:e,current:i,renderState:n,latestValues:s})=>{if(!i)return;let r=!!t.drag;if(!r){for(let t in s)if(z.has(t)){r=!0;break}}if(!r)return;let o=!e;if(e)for(let i=0;i<s7.length;i++){let n=s7[i];t[n]!==e[n]&&(o=!0)}o&&G.read(()=>{!function(t,e){try{e.dimensions="function"==typeof t.getBBox?t.getBBox():t.getBoundingClientRect()}catch(t){e.dimensions={x:0,y:0,width:0,height:0}}}(i,n),G.render(()=>{sQ(n,s,s5(i.tagName),t.transformTemplate),s9(i,n)})})}})},re={useVisualState:sY({scrapeMotionValuesFromProps:s6,createRenderState:s0})};function ri(t,e,i){for(let n in e)tu(e[n])||s4(n,i)||(t[n]=e[n])}let rn=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function rs(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||rn.has(t)}let rr=t=>!rs(t);try{(na=require("@emotion/is-prop-valid").default)&&(rr=t=>t.startsWith("on")?!rs(t):na(t))}catch(t){}let ro={current:null},ra={current:!1},rl=[...eb,tq,t5],ru=t=>rl.find(eT(t)),rh=new WeakMap,rd=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class rc{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:n,blockInitialAnimation:s,visualState:r},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=em,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=te.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,G.render(this.render,!1,!0))};let{latestValues:a,renderState:l,onUpdate:u}=r;this.onUpdate=u,this.latestValues=a,this.baseTarget={...a},this.initialValues=e.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=n,this.options=o,this.blockInitialAnimation=!!s,this.isControllingVariants=sj(e),this.isVariantNode=sL(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:h,...d}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in d){let e=d[t];void 0!==a[t]&&tu(e)&&e.set(a[t],!1)}}mount(t){this.current=t,rh.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),ra.current||function(){if(ra.current=!0,sB.j){if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>ro.current=t.matches;t.addListener(e),e()}else ro.current=!1}}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||ro.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in rh.delete(this.current),this.projection&&this.projection.unmount(),Z(this.notifyUpdate),Z(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let n=z.has(t),s=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&G.preRender(this.notifyUpdate),n&&this.projection&&(this.projection.isTransformDirty=!0)}),r=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{s(),r(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in sU){let e=sU[t];if(!e)continue;let{isEnabled:i,Feature:n}=e;if(!this.features[t]&&n&&i(this.props)&&(this.features[t]=new n(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iG()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<rd.length;e++){let i=rd[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let n=t["on"+i];n&&(this.propEventSubscriptions[i]=this.on(i,n))}this.prevMotionValues=function(t,e,i){for(let n in e){let s=e[n],r=i[n];if(tu(s))t.addValue(n,s);else if(tu(r))t.addValue(n,tl(s,{owner:t}));else if(r!==s){if(t.hasValue(n)){let e=t.getValue(n);!0===e.liveStyle?e.jump(s):e.hasAnimated||e.set(s)}else{let e=t.getStaticValue(n);t.addValue(n,tl(void 0!==e?e:s,{owner:t}))}}}for(let n in i)void 0===e[n]&&t.removeValue(n);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue(),this.onUpdate&&this.onUpdate(this)}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=tl(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){var i;let n=void 0===this.latestValues[t]&&this.current?null!==(i=this.getBaseTargetFromProps(this.props,t))&&void 0!==i?i:this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=n&&("string"==typeof n&&(ef(n)||tA(n))?n=parseFloat(n):!ru(n)&&t5.test(e)&&(n=ee(t,e)),this.setBaseTarget(t,tu(n)?n.get():n)),tu(n)?n.get():n}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){var e;let i;let{initial:n}=this.props;if("string"==typeof n||"object"==typeof n){let s=u(this.props,n,null===(e=this.presenceContext)||void 0===e?void 0:e.custom);s&&(i=s[t])}if(n&&void 0!==i)return i;let s=this.getBaseTargetFromProps(this.props,t);return void 0===s||tu(s)?void 0!==this.initialValues[t]&&void 0===i?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new ts),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class rp extends rc{constructor(){super(...arguments),this.KeyframeResolver=eA}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;tu(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}class rm extends rp{constructor(){super(...arguments),this.type="html",this.renderInstance=s2}readValueFromInstance(t,e){if(z.has(e)){let t=et(e);return t&&t.default||0}{let i=window.getComputedStyle(t),n=(eg(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof n?n.trim():n}}measureInstanceViewportBox(t,{transformPagePoint:e}){return i7(t,e)}build(t,e,i){s_(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return s6(t,e,i)}}class rf extends rp{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iG}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(z.has(e)){let t=et(e);return t&&t.default||0}return e=s3.has(e)?e:td(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return s8(t,e,i)}build(t,e,i){sQ(t,e,this.isSVGTag,i.transformTemplate)}renderInstance(t,e,i,n){s9(t,e,i,n)}mount(t){this.isSVGTag=s5(t.tagName),super.mount(t)}}let rv=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,n)=>"create"===n?t:(e.has(n)||e.set(n,t(n)),e.get(n))})}((nl={animation:{Feature:ib},exit:{Feature:iA},inView:{Feature:sC},tap:{Feature:sb},focus:{Feature:sP},hover:{Feature:sw},pan:{Feature:no},drag:{Feature:ns,ProjectionNode:sy,MeasureLayout:nT},layout:{ProjectionNode:sy,MeasureLayout:nT}},nu=(t,e)=>sz(t)?new rf(e):new rm(e,{allowProjection:t!==nd.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function(t){var e,i;let{preloadedFeatures:n,createVisualElement:s,useRender:r,useVisualState:o,Component:l}=t;function u(t,e){var i;let n;let u={...(0,nd.useContext)(sk._),...t,layoutId:function(t){let{layoutId:e}=t,i=(0,nd.useContext)(np.p).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:h}=u,d=function(t){let{initial:e,animate:i}=function(t,e){if(sj(t)){let{initial:e,animate:i}=t;return{initial:!1===e||a(e)?e:void 0,animate:a(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,nd.useContext)(sR));return(0,nd.useMemo)(()=>({initial:e,animate:i}),[sF(e),sF(i)])}(t),c=o(t,h);if(!h&&sB.j){(0,nd.useContext)(sD).strict;let t=function(t){let{drag:e,layout:i}=sU;if(!e&&!i)return{};let n={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?n.MeasureLayout:void 0,ProjectionNode:n.ProjectionNode}}(u);n=t.MeasureLayout,d.visualElement=function(t,e,i,n,s){var r,o;let{visualElement:a}=(0,nd.useContext)(sR),l=(0,nd.useContext)(sD),u=(0,nd.useContext)(sN.O),h=(0,nd.useContext)(sk._).reducedMotion,d=(0,nd.useRef)(null);n=n||l.renderer,!d.current&&n&&(d.current=n(t,{visualState:e,parent:a,props:i,presenceContext:u,blockInitialAnimation:!!u&&!1===u.initial,reducedMotionConfig:h}));let c=d.current,p=(0,nd.useContext)(nm);c&&!c.projection&&s&&("html"===c.type||"svg"===c.type)&&function(t,e,i,n){let{layoutId:s,layout:r,drag:o,dragConstraints:a,layoutScroll:l,layoutRoot:u}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:s,layout:r,alwaysMeasureLayout:!!o||a&&iB(a),visualElement:t,animationType:"string"==typeof r?r:"both",initialPromotionConfig:n,layoutScroll:l,layoutRoot:u})}(d.current,i,s,p);let m=(0,nd.useRef)(!1);(0,nd.useInsertionEffect)(()=>{c&&m.current&&c.update(i,u)});let f=i[tc],v=(0,nd.useRef)(!!f&&!(null===(r=window.MotionHandoffIsComplete)||void 0===r?void 0:r.call(window,f))&&(null===(o=window.MotionHasOptimisedAnimation)||void 0===o?void 0:o.call(window,f)));return(0,s$.L)(()=>{c&&(m.current=!0,window.MotionIsMounted=!0,c.updateFeatures(),nx.render(c.render),v.current&&c.animationState&&c.animationState.animateChanges())}),(0,nd.useEffect)(()=>{c&&(!v.current&&c.animationState&&c.animationState.animateChanges(),v.current&&(queueMicrotask(()=>{var t;null===(t=window.MotionHandoffMarkAsComplete)||void 0===t||t.call(window,f)}),v.current=!1))}),c}(l,c,u,s,t.ProjectionNode)}return(0,nh.jsxs)(sR.Provider,{value:d,children:[n&&d.visualElement?(0,nh.jsx)(n,{visualElement:d.visualElement,...u}):null,r(l,t,(i=d.visualElement,(0,nd.useCallback)(t=>{t&&c.onMount&&c.onMount(t),i&&(t?i.mount(t):i.unmount()),e&&("function"==typeof e?e(t):iB(e)&&(e.current=t))},[i])),c,h,d.visualElement)]})}n&&function(t){for(let e in t)sU[e]={...sU[e],...t[e]}}(n),u.displayName="motion.".concat("string"==typeof l?l:"create(".concat(null!==(i=null!==(e=l.displayName)&&void 0!==e?e:l.name)&&void 0!==i?i:"",")"));let h=(0,nd.forwardRef)(u);return h[sI]=l,h}({...sz(t)?rt:re,preloadedFeatures:nl,useRender:function(t=!1){return(e,i,n,{latestValues:s},r)=>{let o=(sz(e)?function(t,e,i,n){let s=(0,nd.useMemo)(()=>{let i=s1();return sQ(i,e,s5(n),t.transformTemplate),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};ri(e,t.style,t),s.style={...e,...s.style}}return s}:function(t,e){let i={},n=function(t,e){let i=t.style||{},n={};return ri(n,i,t),Object.assign(n,function({transformTemplate:t},e){return(0,nd.useMemo)(()=>{let i=s0();return s_(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),n}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=n,i})(i,s,r,e),a=function(t,e,i){let n={};for(let s in t)("values"!==s||"object"!=typeof t.values)&&(rr(s)||!0===i&&rs(s)||!e&&!rs(s)||t.draggable&&s.startsWith("onDrag"))&&(n[s]=t[s]);return n}(i,"string"==typeof e,t),l=e!==nd.Fragment?{...a,...o,ref:n}:{},{children:u}=i,h=(0,nd.useMemo)(()=>tu(u)?u.get():u,[u]);return(0,nd.createElement)(e,{...l,children:h})}}(e),createVisualElement:nu,Component:t})}))},44563:function(t,e,i){"use strict";i.d(e,{j:function(){return n}});let n="undefined"!=typeof window},53576:function(t,e,i){"use strict";i.d(e,{h:function(){return s}});var n=i(2265);function s(t){let e=(0,n.useRef)(null);return null===e.current&&(e.current=t()),e.current}},11534:function(t,e,i){"use strict";i.d(e,{L:function(){return s}});var n=i(2265);let s=i(44563).j?n.useLayoutEffect:n.useEffect}}]);