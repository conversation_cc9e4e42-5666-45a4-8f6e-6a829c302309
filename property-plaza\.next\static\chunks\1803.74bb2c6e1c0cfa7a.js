"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1803],{11803:function(e,n,a){a.r(n),a.d(n,{default:function(){return v}});var t=a(65069),o=a(25096),s=a(99376),i=a(2265),r=a(52141),l=a(35992);let c={"handshake/syn":t.bK,"handshake/syn-ack":t.Ol,"handshake/ack":t.ei,"channel/response":t.Ag,"channel/heartbeat":t.UE,"channel/disconnect":t.Hq,"overlay/focus":"visual-editing/focus","overlay/navigate":"visual-editing/navigate","overlay/toggle":"visual-editing/toggle","presentation/toggleOverlay":"presentation/toggle-overlay"},d={[t.bK]:"handshake/syn",[t.Ol]:"handshake/syn-ack",[t.ei]:"handshake/ack",[t.Ag]:"channel/response",[t.UE]:"channel/heartbeat",[t.Hq]:"channel/disconnect","visual-editing/focus":"overlay/focus","visual-editing/navigate":"overlay/navigate","visual-editing/toggle":"overlay/toggle","presentation/toggle-overlay":"presentation/toggleOverlay"},h=e=>{let{data:n}=e;return n&&"object"==typeof n&&"domain"in n&&"type"in n&&"from"in n&&"to"in n&&("sanity/channels"===n.domain&&(n.domain=t.yK),"overlays"===n.to&&(n.to="visual-editing"),"overlays"===n.from&&(n.from="visual-editing"),n.channelId=n.connectionId,delete n.connectionId,n.type=c[n.type]??n.type),e},p=e=>{let{channelId:n,...a}=e,o={...a,connectionId:n};return o.domain===t.yK&&(o.domain="sanity/channels"),"visual-editing"===o.to&&(o.to="overlays"),"visual-editing"===o.from&&(o.from="overlays"),o.type=d[o.type]??o.type,"channel/response"===o.type&&o.responseTo&&!o.data&&(o.data={responseTo:o.responseTo}),("handshake/syn"===o.type||"handshake/syn-ack"===o.type||"handshake/ack"===o.type)&&(o.data={id:o.connectionId}),o},y=({context:e},n)=>{let{sources:a,targetOrigin:t}=e,o=p(n.message);a.forEach(e=>{e.postMessage(o,{targetOrigin:t})})},g=()=>({listen:(0,t.F8)(h),requestMachine:(0,t.lB)().provide({actions:{"send message":y}})});function v(e){let{draftModeEnabled:n,draftModePerspective:a}=e,c=(0,s.useRouter)(),d=(0,r.i)((e,t)=>{n&&e!==a&&(0,o.N)(e).then(()=>{t.aborted||c.refresh()}).catch(e=>console.error("Failed to set the preview perspective cookie",e))});return(0,i.useEffect)(()=>{let e;let n=(0,t.dS)({name:"loaders",connectTo:"presentation"},(0,t.zB)().provide({actors:g()}));n.on("loader/perspective",n=>{e?.abort(),e=new AbortController,d(n.perspective,e.signal)});let a=n.start();return(0,l.re)(n),()=>{a()}},[d]),null}v.displayName="PresentationComlink"}}]);