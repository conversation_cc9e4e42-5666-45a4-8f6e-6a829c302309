(()=>{var e={};e.id=2278,e.ids=[2278],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},31164:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>l.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>c,routeModule:()=>x,tree:()=>d}),t(96056),t(84448),t(81729),t(90996);var s=t(30170),a=t(45002),i=t(83876),l=t.n(i),o=t(66299),n={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);t.d(r,n);let d=["",{children:["[locale]",{children:["stripe-test",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,96056)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\stripe-test\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,80603))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,84448)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,81729)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,90996,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\stripe-test\\page.tsx"],p="/[locale]/stripe-test/page",u={require:t,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/[locale]/stripe-test/page",pathname:"/[locale]/stripe-test",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},76099:(e,r,t)=>{Promise.resolve().then(t.bind(t,14826))},14826:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>o});var s=t(97247),a=t(28964),i=t(58053),l=t(27757);function o(){let[e,r]=(0,a.useState)(!1),[t,o]=(0,a.useState)(null),n=async()=>{r(!0),o(null);try{let e=await fetch("/api/verify-booking-checkout",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({firstName:"Test",lastName:"User",email:"<EMAIL>",whatsappNumber:"+62812345678",villaAddress:"Test Villa Address, Bali",preferredDate:"2024-02-01",tier:"basic",recaptchaToken:"test-token"})}),r=await e.json();if(e.ok&&r.url)window.location.href=r.url;else throw Error(r.error||"Failed to create checkout session")}catch(e){console.error("Checkout error:",e),o(e.message)}finally{r(!1)}};return s.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 p-4",children:(0,s.jsxs)(l.Zb,{className:"w-full max-w-md",children:[(0,s.jsxs)(l.Ol,{children:[s.jsx(l.ll,{children:"\uD83E\uDDEA Stripe Test"}),s.jsx(l.SZ,{children:"Test de Stripe integratie met een eenvoudige checkout"})]}),(0,s.jsxs)(l.aY,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"text-sm text-gray-600",children:[s.jsx("p",{children:s.jsx("strong",{children:"Test Data:"})}),(0,s.jsxs)("ul",{className:"list-disc list-inside space-y-1 mt-2",children:[s.jsx("li",{children:"Naam: Test User"}),s.jsx("li",{children:"Email: <EMAIL>"}),s.jsx("li",{children:"Tier: Basic (IDR 4,500,000)"}),s.jsx("li",{children:"Villa: Test Villa Address, Bali"})]})]}),t&&s.jsx("div",{className:"p-3 bg-red-50 border border-red-200 rounded-md",children:s.jsx("p",{className:"text-red-600 text-sm",children:t})}),s.jsx(i.z,{onClick:n,disabled:e,className:"w-full",children:e?"Creating Checkout...":"\uD83D\uDE80 Test Stripe Checkout"}),(0,s.jsxs)("div",{className:"text-xs text-gray-500 space-y-1",children:[s.jsx("p",{children:s.jsx("strong",{children:"Test Credit Cards:"})}),s.jsx("p",{children:"• 4242 4242 4242 4242 (Visa)"}),s.jsx("p",{children:"• 5555 5555 5555 4444 (Mastercard)"}),s.jsx("p",{children:"• Expiry: Any future date"}),s.jsx("p",{children:"• CVC: Any 3 digits"})]})]})]})})}},27757:(e,r,t)=>{"use strict";t.d(r,{Ol:()=>o,SZ:()=>d,Zb:()=>l,aY:()=>c,ll:()=>n});var s=t(97247),a=t(28964),i=t(25008);let l=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,i.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...r}));l.displayName="Card";let o=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...r}));o.displayName="CardHeader";let n=a.forwardRef(({className:e,...r},t)=>s.jsx("h3",{ref:t,className:(0,i.cn)("font-semibold leading-none tracking-tight",e),...r}));n.displayName="CardTitle";let d=a.forwardRef(({className:e,...r},t)=>s.jsx("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...r}));d.displayName="CardDescription";let c=a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,i.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent",a.forwardRef(({className:e,...r},t)=>s.jsx("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter"},96056:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(45347).createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\app\[locale]\stripe-test\page.tsx#default`)}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[9379,5063,4916,6666],()=>t(31164));module.exports=s})();