(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4617],{1591:function(e,t,r){Promise.resolve().then(r.bind(r,25776)),Promise.resolve().then(r.bind(r,97299)),Promise.resolve().then(r.bind(r,97867)),Promise.resolve().then(r.bind(r,31085)),Promise.resolve().then(r.t.bind(r,88003,23))},25776:function(e,t,r){"use strict";r.d(t,{default:function(){return d}});var s=r(57437),i=r(27668),a=r(62869),n=r(42586),l=r(91430),o=r(2265);function c(e){let{src:t,poster:r,className:i="",autoPlay:a=!0,muted:n=!0,loop:l=!0,playsInline:c=!0,controls:d=!1,preload:m="metadata",lazy:u=!0,fallbackContent:x}=e,[f,p]=(0,o.useState)(!u),[h,g]=(0,o.useState)(!0),[b,v]=(0,o.useState)(!1),y=(0,o.useRef)(null);return(0,o.useEffect)(()=>{if(!u)return;let e=new IntersectionObserver(t=>{let[r]=t;r.isIntersecting&&(p(!0),e.disconnect())},{threshold:.1,rootMargin:"50px"});return y.current&&e.observe(y.current),()=>e.disconnect()},[u]),(0,s.jsxs)("div",{ref:y,className:"relative ".concat(i),children:[h&&f&&(0,s.jsx)("div",{className:"absolute inset-0 bg-gray-100 flex items-center justify-center z-10",children:(0,s.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[(0,s.jsx)("div",{className:"w-8 h-8 border-2 border-seekers-primary border-t-transparent rounded-full animate-spin"}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Loading video..."})]})}),b&&(0,s.jsx)("div",{className:"absolute inset-0 bg-gray-100 flex items-center justify-center z-10",children:(0,s.jsxs)("div",{className:"text-center space-y-2",children:[(0,s.jsx)("div",{className:"text-4xl",children:"⚠️"}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Video could not be loaded"}),x]})}),r&&!f&&(0,s.jsx)("img",{src:r,alt:"Video preview",className:"w-full h-full object-cover rounded-lg"}),f&&(0,s.jsxs)("video",{autoPlay:a,muted:n,loop:l,playsInline:c,controls:d,preload:m,poster:r,className:"w-full h-full object-cover rounded-lg",onLoadStart:()=>{g(!0),v(!1)},onCanPlay:()=>{g(!1)},onError:()=>{g(!1),v(!0)},children:[(0,s.jsx)("source",{src:t,type:"video/mp4"}),(0,s.jsx)("source",{src:t.replace(".mp4",".webm"),type:"video/webm"}),(0,s.jsx)("div",{className:"flex items-center justify-center h-full bg-gray-100",children:(0,s.jsxs)("div",{className:"text-center space-y-2",children:[(0,s.jsx)("div",{className:"text-4xl",children:"\uD83D\uDCF9"}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:"Your browser doesn't support video playback"}),x]})})]})]})}function d(){let e=(0,n.useTranslations)("verify");return(0,s.jsx)("section",{className:"bg-gradient-to-br from-seekers-primary/5 to-seekers-primary/10 py-6 md:py-10 lg:py-16","aria-labelledby":"hero-title",children:(0,s.jsx)(i.Z,{children:(0,s.jsxs)("div",{className:"flex flex-col lg:grid lg:grid-cols-2 gap-4 md:gap-6 lg:gap-8 items-center",children:[(0,s.jsxs)("div",{className:"space-y-3 md:space-y-4 lg:space-y-5 order-2 lg:order-1 text-center lg:text-left",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center lg:justify-start gap-2 text-red-600 font-semibold",children:[(0,s.jsx)("span",{className:"text-xl md:text-2xl",children:"\uD83D\uDEA8"}),(0,s.jsx)("span",{className:"text-sm md:text-base",children:e("hero.badge")})]}),(0,s.jsx)("h1",{id:"hero-title",className:"text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold text-seekers-text leading-tight",children:e("hero.title")}),(0,s.jsx)("p",{className:"text-base md:text-lg text-seekers-text-light leading-relaxed",children:e("hero.subtitle")}),(0,s.jsx)("div",{className:"space-y-3 md:space-y-4",children:[e("hero.benefits.0"),e("hero.benefits.1"),e("hero.benefits.2"),e("hero.benefits.3")].map((e,t)=>(0,s.jsxs)("div",{className:"flex items-start lg:items-center gap-3 justify-center lg:justify-start",children:[(0,s.jsx)("span",{className:"text-seekers-primary text-lg mt-0.5 lg:mt-0",children:"•"}),(0,s.jsx)("span",{className:"text-sm md:text-base text-seekers-text font-medium text-left",children:e})]},t))}),(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 md:p-4",children:(0,s.jsxs)("p",{className:"text-red-800 font-medium text-sm md:text-base text-center lg:text-left",children:[e("hero.warning")," ",e("hero.cta")]})}),(0,s.jsxs)("div",{className:"pt-4 md:pt-6 text-center",children:[(0,s.jsx)(a.z,{size:"lg",className:"bg-seekers-primary hover:bg-seekers-primary/90 text-white px-6 md:px-8 py-3 md:py-4 text-base md:text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-200 w-full sm:w-auto",asChild:!0,children:(0,s.jsx)(l.rU,{href:"#booking-form",children:e("cta.bookInspection")})}),(0,s.jsx)("p",{className:"text-xs md:text-sm text-seekers-text-light mt-2 md:mt-3",children:e("footnote")})]})]}),(0,s.jsx)("div",{className:"relative flex justify-center order-1 lg:order-2 w-full",children:(0,s.jsx)("div",{className:"w-full max-w-xs sm:max-w-sm md:max-w-md lg:max-w-xs xl:max-w-sm",style:{aspectRatio:"9/16"},children:(0,s.jsx)(c,{src:"https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",poster:"https://images.pexels.com/photos/1396122/pexels-photo-1396122.jpeg?auto=compress&cs=tinysrgb&w=400&h=711",className:"w-full h-full shadow-lg",autoPlay:!0,muted:!0,loop:!0,playsInline:!0,controls:!1,preload:"metadata",lazy:!1,fallbackContent:(0,s.jsxs)("div",{className:"text-center space-y-2 p-4",children:[(0,s.jsx)("div",{className:"text-4xl",children:"\uD83D\uDCF1"}),(0,s.jsx)("p",{className:"text-neutral-500 font-medium",children:"Villa Inspection Video"}),(0,s.jsxs)("p",{className:"text-sm text-neutral-400 leading-relaxed",children:["Professional villa inspection",(0,s.jsx)("br",{}),"process and red flags",(0,s.jsx)("br",{}),"identification"]})]})})})})]})})})}},97299:function(e,t,r){"use strict";r.d(t,{default:function(){return E}});var s=r(57437),i=r(2265),a=r(27668),n=r(42586),l=r(79205);let o=(0,l.Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);var c=r(73247);let d=(0,l.Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);function m(){let e=(0,n.useTranslations)("verify"),t=[{icon:(0,s.jsx)(o,{className:"w-6 h-6"}),title:e("howItWorks.steps.book.title"),description:e("howItWorks.steps.book.description"),result:e("howItWorks.steps.book.result")},{icon:(0,s.jsx)(c.Z,{className:"w-6 h-6"}),title:e("howItWorks.steps.inspect.title"),description:e("howItWorks.steps.inspect.description"),result:[e("howItWorks.steps.inspect.result.basic"),e("howItWorks.steps.inspect.result.standard"),e("howItWorks.steps.inspect.result.premium")]},{icon:(0,s.jsx)(d,{className:"w-6 h-6"}),title:e("howItWorks.steps.report.title"),description:e("howItWorks.steps.report.description"),result:[e("howItWorks.steps.report.result.basic"),e("howItWorks.steps.report.result.standard"),e("howItWorks.steps.report.result.premium")]}];return(0,s.jsx)("section",{className:"bg-seekers-foreground/50 py-12",children:(0,s.jsxs)(a.Z,{children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[(0,s.jsx)("h2",{className:"text-3xl md:text-4xl font-bold text-seekers-text mb-4",children:e("howItWorks.title")}),(0,s.jsx)("p",{className:"text-lg text-seekers-text-light",children:e("howItWorks.subtitle")})]}),(0,s.jsx)("div",{className:"relative max-w-[1200px] mx-auto px-4",children:(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 relative",children:t.map((e,t)=>(0,s.jsxs)("div",{className:"group relative bg-white p-6 rounded-2xl border border-gray-100   hover:border-seekers-primary/20 transition-all duration-300 shadow-sm hover:shadow-md   flex flex-col",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,s.jsxs)("div",{className:"relative shrink-0",children:[(0,s.jsx)("div",{className:"w-12 h-12 bg-seekers-primary/10 rounded-xl flex items-center justify-center   text-seekers-primary group-hover:scale-110 transition-transform duration-300",children:e.icon}),(0,s.jsx)("div",{className:"absolute inset-0 bg-seekers-primary/5 blur-xl rounded-full   group-hover:blur-2xl transition-all duration-300"})]}),(0,s.jsx)("h4",{className:"text-lg font-semibold text-gray-900   group-hover:text-seekers-primary transition-colors duration-300",children:e.title})]}),(0,s.jsx)("p",{className:"text-gray-600 text-sm leading-relaxed flex-1 whitespace-pre-line text-left",children:e.description}),(0,s.jsx)("div",{className:"absolute inset-0 border-2 border-transparent   group-hover:border-seekers-primary/20 rounded-2xl transition-colors duration-300"})]},t))})}),(0,s.jsxs)("div",{className:"mt-12 bg-seekers-primary/5 rounded-lg p-6 md:p-8 text-center max-w-4xl mx-auto",children:[(0,s.jsx)("h3",{className:"text-lg md:text-xl font-semibold text-seekers-text mb-4",children:e("howItWorks.whyChoose.title")}),(0,s.jsx)("p",{className:"text-base text-seekers-text-light",children:e("howItWorks.whyChoose.description")})]})]})})}var u=r(62869),x=r(94508),f=r(28959);function p(e){let{conversions:t,onSelectTier:r}=e,{currency:l,isLoading:o}=(0,f.R)(),[c,d]=(0,i.useState)("IDR"),m=(0,n.useLocale)(),p=(0,n.useTranslations)("verify"),h=[{id:"basic",name:p("pricing.tiers.basic.name"),price:19e5,features:[p("pricing.tiers.basic.features.0"),p("pricing.tiers.basic.features.1"),p("pricing.tiers.basic.features.2"),p("pricing.tiers.basic.features.3"),p("pricing.tiers.basic.features.4")]},{id:"standard",name:p("pricing.tiers.standard.name"),price:45e5,popular:!0,features:[p("pricing.tiers.standard.features.0"),p("pricing.tiers.standard.features.1"),p("pricing.tiers.standard.features.2"),p("pricing.tiers.standard.features.3"),p("pricing.tiers.standard.features.4")]},{id:"premium",name:p("pricing.tiers.premium.name"),price:7e6,features:[p("pricing.tiers.premium.features.0"),p("pricing.tiers.premium.features.1"),p("pricing.tiers.premium.features.2"),p("pricing.tiers.premium.features.3")]}];(0,i.useEffect)(()=>{!o&&l&&d(l)},[l,o]);let g=e=>{let r=e*(t[c]||1);return(0,x.xG)(r,c,m)};return(0,s.jsx)("section",{className:"py-16 bg-white","aria-labelledby":"pricing-title",children:(0,s.jsxs)(a.Z,{children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[(0,s.jsx)("h2",{id:"pricing-title",className:"text-3xl md:text-4xl font-bold text-seekers-text mb-4",children:p("pricing.title")}),(0,s.jsx)("p",{className:"text-lg text-seekers-text-light",children:p("pricing.subtitle")})]}),(0,s.jsx)("div",{className:"grid sm:grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8 max-w-5xl mx-auto items-stretch",children:h.map(e=>(0,s.jsxs)("div",{className:"relative bg-white rounded-xl border-2 p-6 lg:p-8 transition-all duration-200 hover:shadow-lg flex flex-col h-full ".concat(e.popular?"border-seekers-primary shadow-lg":"border-neutral-200"),itemScope:!0,itemType:"https://schema.org/Offer",children:[e.popular&&(0,s.jsx)("div",{className:"absolute -top-3 left-1/2 transform -translate-x-1/2",children:(0,s.jsx)("span",{className:"bg-seekers-primary text-white px-4 py-1 rounded-full text-sm font-semibold",children:p("pricing.tiers.standard.popular")})}),(0,s.jsxs)("div",{className:"text-center mb-6",children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-seekers-text mb-1",itemProp:"name",children:e.name}),(0,s.jsx)("p",{className:"text-sm text-seekers-text-light mb-3 whitespace-pre-line",itemProp:"description",children:p("pricing.tiers.".concat(e.id,".subtitle"))}),(0,s.jsxs)("div",{className:"text-3xl font-bold text-seekers-primary",itemProp:"price",content:e.price.toString(),children:[g(e.price),(0,s.jsx)("meta",{itemProp:"priceCurrency",content:"IDR"})]})]}),(0,s.jsxs)("div",{className:"flex-grow",children:[(0,s.jsx)("ul",{className:"space-y-3",children:e.features.map((e,t)=>(0,s.jsxs)("li",{className:"flex items-start gap-3",children:[(0,s.jsx)("span",{className:"text-seekers-primary mt-1",children:"✓"}),(0,s.jsx)("span",{className:"text-seekers-text-light",children:e})]},t))}),(0,s.jsx)(u.z,{onClick:()=>r(e),className:"w-full py-3 font-semibold transition-all duration-200 ".concat("premium"===e.id?"mt-9":"mt-8"," ").concat(e.popular?"bg-seekers-primary hover:bg-seekers-primary/90 text-white":"bg-neutral-100 hover:bg-neutral-200 text-seekers-text border border-neutral-300"),variant:e.popular?"default":"outline",children:p("pricing.cta",{tierName:e.name})}),(0,s.jsx)("div",{className:"text-xs text-gray-500 italic text-center min-h-[1.5rem] pt-3 flex items-center justify-center",children:"premium"===e.id&&p("pricing.tiers.".concat(e.id,".footnote"))})]}),(0,s.jsx)("div",{className:"flex-grow"})]},e.id))})]})})}var h=r(29501),g=r(13590),b=r(67620),v=r(15681),y=r(35153),j=r(61729),N=r(83037),w=r.n(N),k=r(31229),I=r(75422),T=r(53647);function S(e){let{form:t,label:r,name:i,placeholder:a,description:n,selectList:l,children:o,disabled:c,containerClassName:d,inputContainer:m,inputProps:u,labelClassName:f,variant:p}=e;return(0,s.jsx)(v.Wi,{control:t.control,name:i,render:e=>{let{field:t}=e;return(0,s.jsx)(I.Z,{label:r,description:n,labelClassName:(0,x.cn)("float"==p?"absolute -top-2 left-2 px-1 text-xs bg-background z-10":"",f),containerClassName:d,variant:p,children:(0,s.jsxs)(T.Ph,{onValueChange:t.onChange,name:t.name,value:t.value,disabled:t.disabled||c,children:[(0,s.jsx)(v.NI,{children:(0,s.jsx)(T.i4,{className:(0,x.cn)("border-none focus:outline-none shadow-none focus-visible:ring-0 w-full","float"==p?"px-0":"",null==u?void 0:u.className),children:(0,s.jsx)(T.ki,{placeholder:a})})}),(0,s.jsxs)(T.Bw,{onClick:e=>{e.stopPropagation()},children:[Array.isArray(l)&&l.map(e=>(0,s.jsx)(T.Ql,{onClick:e=>{e.stopPropagation()},value:e.value,children:e.content},e.id)),o]})]})})}})}var D=r(49607);let C=e=>D.v.post("/verify-booking-checkout",e);var A=r(21770);function R(e){let{selectedTier:t,conversions:r}=e,{executeRecaptcha:l}=(0,b.CL)(),{toast:o}=(0,y.pm)(),c=(0,n.useTranslations)("verify"),d=function(){let e=(0,n.useTranslations)("seeker");return k.z.object({firstName:k.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.firstName")})}).min(2,e("form.utility.minimumLength",{field:e("form.field.firstName"),length:2})),lastName:k.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.lastName")})}).min(2,e("form.utility.minimumLength",{field:e("form.field.lastName"),length:2})),email:k.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.email")})}).email({message:e("form.utility.invalidFormat",{field:e("form.field.email")})}),whatsappNumber:k.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.phoneOrWhatsappNumber")})}).refine(e=>w()(e).isValid,e("form.utility.invalidFormat",{field:e("form.field.phoneOrWhatsappNumber")})),villaAddress:k.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.villaAddress")})}).min(10,e("form.utility.minimumLength",{field:e("form.field.villaAddress"),length:10})),preferredDate:k.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.preferredDate")})}).date(e("form.utility.invalidFormat",{field:e("form.field.preferredDate")})),tier:k.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.tier")})})})}(),m=(0,A.D)({mutationFn:async e=>await C(e)}),x=[{id:"basic",content:c("booking.form.tier.options.basic"),value:"basic"},{id:"smart",content:c("booking.form.tier.options.smart"),value:"standard"},{id:"full-shield",content:c("booking.form.tier.options.fullShield"),value:"premium"}],f=(0,h.cI)({resolver:(0,g.F)(d),defaultValues:{firstName:"",lastName:"",email:"",whatsappNumber:"",villaAddress:"",preferredDate:"",tier:(null==t?void 0:t.id)||""}});(0,i.useEffect)(()=>{t&&f.setValue("tier",t.id)},[t,f]);let p=async e=>{try{var t,r,s;let i=await l("verify_booking"),a={...e,recaptchaToken:i},n=await m.mutateAsync(a);if(console.log(n),null===(t=n.data)||void 0===t?void 0:t.url)window.location.href=null===(r=n.data)||void 0===r?void 0:r.url;else throw Error((null===(s=n.data)||void 0===s?void 0:s.url)||"Failed to create checkout session")}catch(e){console.error("Checkout error:",e),o({title:c("booking.form.error.title"),description:c("booking.form.error.message"),variant:"destructive"})}},N=new Date;N.setDate(N.getDate()+1);let I=N.toISOString().split("T")[0];return(0,s.jsx)("section",{id:"booking-form",className:"py-16 bg-white isolate","aria-labelledby":"booking-title",children:(0,s.jsx)(a.Z,{children:(0,s.jsxs)("div",{className:"max-w-lg mx-auto",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("h2",{id:"booking-title",className:"text-3xl md:text-4xl font-bold text-seekers-text mb-4",children:c("booking.title")}),(0,s.jsx)("p",{className:"text-lg text-seekers-text-light",children:c("booking.subtitle")})]}),(0,s.jsx)("div",{className:"w-full space-y-6",children:(0,s.jsx)(v.l0,{...f,children:(0,s.jsxs)("form",{onSubmit:f.handleSubmit(p),className:"space-y-8",children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsx)(j.Z,{form:f,label:c("booking.form.firstName.label"),name:"firstName",placeholder:"",type:"text",variant:"float",labelClassName:"text-xs text-seekers-text-light font-normal"}),(0,s.jsx)(j.Z,{form:f,label:c("booking.form.lastName.label"),name:"lastName",placeholder:"",type:"text",variant:"float",labelClassName:"text-xs text-seekers-text-light font-normal"})]}),(0,s.jsx)(j.Z,{form:f,label:c("booking.form.email.label"),name:"email",placeholder:"",type:"email",variant:"float",labelClassName:"text-xs text-seekers-text-light font-normal"}),(0,s.jsx)(j.Z,{form:f,label:c("booking.form.whatsappNumber.label"),name:"whatsappNumber",placeholder:"",type:"tel",variant:"float",labelClassName:"text-xs text-seekers-text-light font-normal"}),(0,s.jsx)(j.Z,{form:f,label:c("booking.form.villaAddress.label"),name:"villaAddress",placeholder:"",type:"text",variant:"float",labelClassName:"text-xs text-seekers-text-light font-normal"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsx)(j.Z,{form:f,label:c("booking.form.preferredDate.label"),name:"preferredDate",placeholder:"",type:"date",variant:"float",labelClassName:"text-xs text-seekers-text-light font-normal",inputProps:{min:I}}),(0,s.jsx)(S,{form:f,label:c("booking.form.tier.label"),name:"tier",placeholder:c("booking.form.tier.placeholder"),selectList:x,variant:"float",labelClassName:"text-xs text-seekers-text-light font-normal"})]}),(0,s.jsx)(u.z,{type:"submit",className:"w-full",loading:m.isPending,children:c("booking.form.cta")}),(0,s.jsx)("div",{className:"text-xs text-neutral space-x-1 !mt-2 text-center",children:(0,s.jsx)("span",{children:c("booking.form.disclaimer")})})]})})})]})})})}var Z=r(86595);function z(){let e=(0,n.useTranslations)("verify.socialProof"),t=[{name:e("reviews.review1.name"),location:e("reviews.review1.location"),rating:5,text:e("reviews.review1.text"),propertyType:e("reviews.review1.propertyType")},{name:e("reviews.review2.name"),location:e("reviews.review2.location"),rating:5,text:e("reviews.review2.text"),propertyType:e("reviews.review2.propertyType")},{name:e("reviews.review3.name"),location:e("reviews.review3.location"),rating:5,text:e("reviews.review3.text"),propertyType:e("reviews.review3.propertyType")}],r=e=>Array.from({length:5},(t,r)=>(0,s.jsx)(Z.Z,{className:"w-4 h-4 ".concat(r<e?"fill-yellow-400 text-yellow-400":"fill-gray-200 text-gray-200")},r));return(0,s.jsx)("section",{className:"py-16 bg-gray-50",children:(0,s.jsxs)(a.Z,{children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[(0,s.jsx)("h2",{className:"text-3xl font-bold text-seekers-text mb-4",children:e("title")}),(0,s.jsx)("p",{className:"text-lg text-seekers-text-light max-w-3xl mx-auto",children:e("subtitle")})]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:t.map((e,t)=>(0,s.jsx)("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow duration-300 flex flex-col h-full",children:(0,s.jsxs)("div",{className:"flex items-start gap-4 flex-1",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-seekers-primary to-seekers-primary/80 rounded-full flex items-center justify-center text-white font-semibold text-base",children:e.name.charAt(0)})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0 flex flex-col",children:[(0,s.jsx)("div",{className:"flex items-center mb-3",children:(0,s.jsx)("div",{className:"flex space-x-1",children:r(e.rating)})}),(0,s.jsxs)("blockquote",{className:"text-seekers-text mb-4 leading-relaxed text-sm flex-1",children:['"',e.text,'"']}),(0,s.jsxs)("div",{className:"space-y-1 mt-auto",children:[(0,s.jsx)("p",{className:"text-sm font-semibold text-seekers-text",children:e.name}),(0,s.jsxs)("p",{className:"text-xs text-seekers-text-light",children:[e.location," • ",e.propertyType]})]})]})]})},t))})]})})}function _(){let e=(0,n.useTranslations)("verify");return(0,s.jsx)("section",{className:"py-12 bg-gray-50 border-t border-gray-200",children:(0,s.jsx)(a.Z,{children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:e("disclaimer.title")}),(0,s.jsx)("p",{className:"text-sm text-gray-600 leading-relaxed",children:e("disclaimer.content")})]})})})}function E(e){let{conversions:t}=e,[r,a]=(0,i.useState)();return(0,s.jsxs)("main",{className:"min-h-screen",children:[(0,s.jsx)(m,{}),(0,s.jsx)(p,{conversions:t,onSelectTier:e=>{a(e),setTimeout(()=>{!function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:80,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:20,s=document.getElementById(e);if(s){let e=s.offsetTop-t-r;window.scrollTo({top:Math.max(0,e),behavior:"smooth"})}}("booking-title",80,20)},100)}}),(0,s.jsx)(R,{selectedTier:r,conversions:t}),(0,s.jsx)(z,{}),(0,s.jsx)(_,{})]})}},75422:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});var s=r(57437),i=r(15681),a=r(94508);function n(e){let{children:t,description:r,label:n,containerClassName:l,labelClassName:o,variant:c="default"}=e;return(0,s.jsxs)("div",{className:"w-full",children:[(0,s.jsxs)(i.xJ,{className:(0,a.cn)("w-full relative","float"==c?"border rounded-xl focus-within:border-neutral-light px-4 py-1 !space-y-0 focus-visible:outline-none focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 disabled:cursor-not-allowed":"",l),onClick:e=>e.stopPropagation(),children:[n&&(0,s.jsx)(i.lX,{className:o,children:n}),(0,s.jsx)(i.NI,{className:"group relative w-full",children:t}),r&&(0,s.jsx)(i.pf,{children:r}),"default"==c&&(0,s.jsx)(i.zG,{})]}),"float"==c&&(0,s.jsx)(i.zG,{})]})}},61729:function(e,t,r){"use strict";r.d(t,{Z:function(){return o}});var s=r(57437),i=r(15681),a=r(95186),n=r(75422),l=r(94508);function o(e){let{form:t,label:r,name:o,placeholder:c,description:d,type:m,inputProps:u,children:x,labelClassName:f,containerClassName:p,inputContainer:h,variant:g="default"}=e;return(0,s.jsx)(i.Wi,{control:t.control,name:o,render:e=>{let{field:t}=e;return(0,s.jsx)(n.Z,{label:r,description:d,labelClassName:(0,l.cn)("float"==g?"absolute -top-2 left-2 px-1 text-xs bg-background z-10":"",f),containerClassName:p,variant:g,children:(0,s.jsxs)("div",{className:(0,l.cn)("flex gap-2 w-full overflow-hidden","float"==g?"":"border rounded-sm focus-within:border-neutral-light",h),children:[(0,s.jsx)(a.I,{type:m,placeholder:c,...t,...u,className:(0,l.cn)("border-none focus:outline-none shadow-none focus-visible:ring-0 w-full","float"==g?"px-0":"",null==u?void 0:u.className)}),x]})})}})}},27668:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});var s=r(57437),i=r(94508);function a(e){return(0,s.jsx)("div",{...e,ref:e.ref,className:(0,i.cn)("xl:max-w-screen-2xl max-md:px-4 px-8 mx-auto space-y-12",e.className),children:e.children})}},62869:function(e,t,r){"use strict";r.d(t,{z:function(){return d}});var s=r(57437),i=r(2265),a=r(98482),n=r(90535),l=r(94508),o=r(51817);let c=(0,n.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-seekers-primary text-white shadow hover:bg-seekers-primary/90","default-seekers":"bg-seekers-primary text-white shadow hover:bg-seekers-primary-light/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),d=i.forwardRef((e,t)=>{let{className:r,variant:i,size:n,asChild:d=!1,loading:m=!1,...u}=e,x=d?a.g7:"button";return(0,s.jsx)(x,{className:(0,l.cn)(c({variant:i,size:n,className:r})),ref:t,disabled:m||u.disabled,...u,children:m?(0,s.jsx)(o.Z,{className:(0,l.cn)("h-4 w-4 animate-spin")}):u.children})});d.displayName="Button"},15681:function(e,t,r){"use strict";r.d(t,{NI:function(){return h},Wi:function(){return m},l0:function(){return c},lX:function(){return p},pf:function(){return g},xJ:function(){return f},zG:function(){return b}});var s=r(57437),i=r(2265),a=r(98482),n=r(29501),l=r(94508),o=r(26815);let c=n.RV,d=i.createContext({}),m=e=>{let{...t}=e;return(0,s.jsx)(d.Provider,{value:{name:t.name},children:(0,s.jsx)(n.Qr,{...t})})},u=()=>{let e=i.useContext(d),t=i.useContext(x),{getFieldState:r,formState:s}=(0,n.Gc)(),a=r(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=t;return{id:l,name:e.name,formItemId:"".concat(l,"-form-item"),formDescriptionId:"".concat(l,"-form-item-description"),formMessageId:"".concat(l,"-form-item-message"),...a}},x=i.createContext({}),f=i.forwardRef((e,t)=>{let{className:r,...a}=e,n=i.useId();return(0,s.jsx)(x.Provider,{value:{id:n},children:(0,s.jsx)("div",{ref:t,className:(0,l.cn)("space-y-2",r),...a})})});f.displayName="FormItem";let p=i.forwardRef((e,t)=>{let{className:r,...i}=e,{error:a,formItemId:n}=u();return(0,s.jsx)(o._,{ref:t,className:(0,l.cn)(a&&"text-destructive",r),htmlFor:n,...i})});p.displayName="FormLabel";let h=i.forwardRef((e,t)=>{let{...r}=e,{error:i,formItemId:n,formDescriptionId:l,formMessageId:o}=u();return(0,s.jsx)(a.g7,{ref:t,id:n,"aria-describedby":i?"".concat(l," ").concat(o):"".concat(l),"aria-invalid":!!i,...r})});h.displayName="FormControl";let g=i.forwardRef((e,t)=>{let{className:r,...i}=e,{formDescriptionId:a}=u();return(0,s.jsx)("p",{ref:t,id:a,className:(0,l.cn)("text-[0.8rem] text-muted-foreground",r),...i})});g.displayName="FormDescription";let b=i.forwardRef((e,t)=>{let{className:r,children:i,...a}=e,{error:n,formMessageId:o}=u(),c=n?String(null==n?void 0:n.message):i;return c?(0,s.jsx)("p",{ref:t,id:o,className:(0,l.cn)("text-[0.8rem] font-medium text-destructive",r),...a,children:c}):null});b.displayName="FormMessage"},95186:function(e,t,r){"use strict";r.d(t,{I:function(){return n}});var s=r(57437),i=r(2265),a=r(94508);let n=i.forwardRef((e,t)=>{let{className:r,type:i,...n}=e;return(0,s.jsx)("input",{type:i,className:(0,a.cn)("flex max-sm:h-8 h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-inset focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...n})});n.displayName="Input"},26815:function(e,t,r){"use strict";r.d(t,{_:function(){return c}});var s=r(57437),i=r(2265),a=r(6394),n=r(90535),l=r(94508);let o=(0,n.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,s.jsx)(a.f,{ref:t,className:(0,l.cn)(o(),r),...i})});c.displayName=a.f.displayName},53647:function(e,t,r){"use strict";r.d(t,{Bw:function(){return x},Ph:function(){return o},Ql:function(){return f},i4:function(){return d},ki:function(){return c}});var s=r(57437),i=r(2265),a=r(20653),n=r(74797),l=r(94508);let o=n.fC;n.ZA;let c=n.B4,d=i.forwardRef((e,t)=>{let{className:r,children:i,showCaret:o=!0,...c}=e;return(0,s.jsxs)(n.xz,{ref:t,className:(0,l.cn)("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",r),...c,children:[i,o&&(0,s.jsx)(n.JO,{asChild:!0,children:(0,s.jsx)(a.jnn,{className:"h-4 w-4 opacity-50"})})]})});d.displayName=n.xz.displayName;let m=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,s.jsx)(n.u_,{ref:t,className:(0,l.cn)("flex cursor-default items-center justify-center py-1",r),...i,children:(0,s.jsx)(a.g8U,{})})});m.displayName=n.u_.displayName;let u=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,s.jsx)(n.$G,{ref:t,className:(0,l.cn)("flex cursor-default items-center justify-center py-1",r),...i,children:(0,s.jsx)(a.v4q,{})})});u.displayName=n.$G.displayName;let x=i.forwardRef((e,t)=>{let{className:r,children:i,position:a="popper",...o}=e;return(0,s.jsx)(n.h_,{children:(0,s.jsxs)(n.VY,{ref:t,className:(0,l.cn)("relative z-50 max-h-96 w-fit overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",r),position:a,...o,children:[(0,s.jsx)(m,{}),(0,s.jsx)(n.l_,{className:(0,l.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:i}),(0,s.jsx)(u,{})]})})});x.displayName=n.VY.displayName,i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,s.jsx)(n.__,{ref:t,className:(0,l.cn)("px-2 py-1.5 text-sm font-semibold",r),...i})}).displayName=n.__.displayName;let f=i.forwardRef((e,t)=>{let{className:r,children:i,...o}=e;return(0,s.jsxs)(n.ck,{ref:t,className:(0,l.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),...o,children:[(0,s.jsx)("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(n.wU,{children:(0,s.jsx)(a.nQG,{className:"h-4 w-4"})})}),(0,s.jsx)(n.eT,{children:i})]})});f.displayName=n.ck.displayName,i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,s.jsx)(n.Z0,{ref:t,className:(0,l.cn)("-mx-1 my-1 h-px bg-muted",r),...i})}).displayName=n.Z0.displayName},49607:function(e,t,r){"use strict";r.d(t,{apiClient:function(){return o},v:function(){return c}});var s=r(6404),i=r(83464),a=r(64131),n=r(51983);let l=new(r.n(n)()).Agent({rejectUnauthorized:!1}),o=i.Z.create({baseURL:"https://dev.property-plaza.id/api/v1",headers:{Authorization:a.Z.get(s.LA)?"Bearer "+a.Z.get(s.LA):""},httpsAgent:l}),c=i.Z.create({baseURL:"/api/",httpsAgent:l})},35153:function(e,t,r){"use strict";r.d(t,{pm:function(){return u}});var s=r(2265);let i=0,a=new Map,n=e=>{if(a.has(e))return;let t=setTimeout(()=>{a.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);a.set(e,t)},l=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?n(r):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},o=[],c={toasts:[]};function d(e){c=l(c,e),o.forEach(e=>{e(c)})}function m(e){let{...t}=e,r=(i=(i+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>d({type:"DISMISS_TOAST",toastId:r});return d({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||s()}}}),{id:r,dismiss:s,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function u(){let[e,t]=s.useState(c);return s.useEffect(()=>(o.push(t),()=>{let e=o.indexOf(t);e>-1&&o.splice(e,1)}),[e]),{...e,toast:m,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},6404:function(e,t,r){"use strict";r.d(t,{$_:function(){return f},Ge:function(){return u},K6:function(){return m},LA:function(){return s},QY:function(){return x},Y:function(){return p},Z9:function(){return a},ac:function(){return l},gr:function(){return i},nM:function(){return n},t8:function(){return d},vQ:function(){return c},xm:function(){return o}});let s="tkn",i="SEEKER",a=8,n=1,l=30,o=300,c=10,d="cookies-collection-status",m="necessary-cookies-collection-status",u="functional-cookies-collection-status",x="analytic-cookies-collection-status",f="marketing-cookies-collection-status",p={type:"t",minPrice:"minp",maxPrice:"maxp",landLargest:"landl",landSmallest:"lands",buildingLargest:"buildl",buildingSmallest:"builds",gardenLargest:"gardenl",gardenSmallest:"gardens",yearsOfBuild:"yob",bedroomTotal:"bedt",bathroomTotal:"batht",rentalOffer:"ro",propertyCondition:"pc",electircity:"el",parking:"pk",swimmingPool:"sp",typeLiving:"tl",furnished:"fs",view:"v",minimumContract:"minc",category:"c",subCategory:"sc",feature:"feat",propertyLocation:"pl",zoom:"z",viewMode:"viewMode"}},91430:function(e,t,r){"use strict";r.d(t,{DI:function(){return i},jD:function(){return l},rU:function(){return a}});var s=r(53795);let i={locales:["en","id"],defaultLocale:"en"},{Link:a,redirect:n,usePathname:l,useRouter:o}=(0,s.os)(i)},94508:function(e,t,r){"use strict";r.d(t,{E6:function(){return m},ET:function(){return f},Fg:function(){return x},cn:function(){return l},g6:function(){return u},pl:function(){return p},uf:function(){return d},xG:function(){return c},yT:function(){return h}});var s=r(61994),i=r(77398),a=r.n(i),n=r(53335);function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.m6)((0,s.W)(t))}r(25566);let o=e=>{switch(e){case"USD":default:return"en-us";case"IDR":return"id-ID";case"GBP":return"en-GB";case"AUD":return"en-AU";case"EUR":return"nl-NL"}},c=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return arguments.length>2&&void 0!==arguments[2]&&arguments[2],new Intl.NumberFormat(o(t),{style:"currency",currency:t,minimumFractionDigits:0,maximumFractionDigits:"IDR"==t?0:2}).format(e)},d=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"en-US";return new Intl.NumberFormat(t,{style:"decimal",minimumFractionDigits:0,maximumFractionDigits:0}).format(e)};function m(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}function u(e){let t=a()(e),r=a()();return t.isSame(r,"day")?t.format("HH:mm"):t.format("DD/MM/YY")}function x(e){return e.replaceAll(/[^a-zA-Z0-9\s]/g,"-").replaceAll(/\s/g,"--")}let f=(e,t)=>e.includes(t)?e.filter(e=>e!==t):[...e,t];function p(e,t){return e.some(e=>t.includes(e))}let h=e=>e.charAt(0).toUpperCase()+e.slice(1)},73247:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(79205).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},86595:function(e,t,r){"use strict";r.d(t,{Z:function(){return s}});let s=(0,r(79205).Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]])},28959:function(e,t,r){"use strict";r.d(t,{R:function(){return n}});var s=r(59625),i=r(89134),a=r(64131);let n=(0,s.Ue)()((0,i.tJ)(e=>({currency:"IDR",setCurrency:t=>e({currency:t}),isLoading:!0,setIsLoading:t=>e({isLoading:t})}),{name:"seekers-settings",storage:{getItem:e=>{let t=a.Z.get(e);return t?JSON.parse(t):void 0},setItem:(e,t)=>{a.Z.set(e,JSON.stringify(t),{expires:7,path:"/"})},removeItem:e=>{a.Z.remove(e)}},onRehydrateStorage:()=>e=>{e&&e.setIsLoading(!1)}}))},67620:function(e,t,r){"use strict";r.d(t,{CL:function(){return s.useReCaptcha}}),r(22248),r(93753);var s=r(16471);r(56302)}},function(e){e.O(0,[6990,8310,6290,8094,2586,2957,4956,3448,8658,6088,9623,6205,1298,7060,4797,1456,2971,2117,1744],function(){return e(e.s=1591)}),_N_E=e.O()}]);