"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5069],{68851:function(t,e,n){n.d(e,{P:function(){return i}});var s=n(78743),r=n(96750);function i(t){return new s.y(function(e){(0,r.Xf)(t()).subscribe(e)})}},66692:function(t,e,n){n.d(e,{E:function(){return s}});var s=new(n(78743)).y(function(t){return t.complete()})},85315:function(t,e,n){n.d(e,{z:function(){return function t(e,n,u){return(void 0===u&&(u=1/0),(0,a.m)(n))?t(function(t,i){return(0,s.U)(function(e,s){return n(t,e,i,s)})((0,r.Xf)(e(t,i)))},u):("number"==typeof n&&(u=n),(0,i.e)(function(t,n){var s,i,a,c,l,f,d,h,p;return s=u,a=[],c=0,l=0,f=!1,d=function(){!f||a.length||c||n.complete()},h=function(t){return c<s?p(t):a.push(t)},p=function(t){c++;var u=!1;(0,r.Xf)(e(t,l++)).subscribe((0,o.x)(n,function(t){i?h(t):n.next(t)},function(){u=!0},void 0,function(){if(u)try{for(c--;a.length&&c<s;)!function(){var t=a.shift();p(t)}();d()}catch(t){n.error(t)}}))},t.subscribe((0,o.x)(n,h,function(){f=!0,d()})),function(){}}))}}});var s=n(7449),r=n(96750),i=n(17753),o=(n(42903),n(4382)),a=n(2070)},40211:function(t,e,n){n.d(e,{E9:function(){return a},Y$:function(){return l},ah:function(){return g},p4:function(){return p}});var s,r,i=n(48769);let o=new WeakMap;function a(t){return{config:t,start:(e,n)=>{let{self:s,system:r,emit:i}=n,a={receivers:void 0,dispose:void 0};o.set(s,a),a.dispose=t({input:e.input,system:r,self:s,sendBack:t=>{"stopped"!==s.getSnapshot().status&&s._parent&&r._relay(s,s._parent,t)},receive:t=>{a.receivers??=new Set,a.receivers.add(t)},emit:i})},transition:(t,e,n)=>{let s=o.get(n.self);return e.type===i.X?(t={...t,status:"stopped",error:void 0},s.dispose?.()):s.receivers?.forEach(t=>t(e)),t},getInitialSnapshot:(t,e)=>({status:"active",output:void 0,error:void 0,input:e}),getPersistedSnapshot:t=>t,restoreSnapshot:t=>t}}let u="xstate.observable.error",c="xstate.observable.complete";function l(t){return{config:t,transition:(t,e)=>{if("active"!==t.status)return t;switch(e.type){case u:return{...t,status:"error",error:e.data,input:void 0,_subscription:void 0};case c:return{...t,status:"done",input:void 0,_subscription:void 0};case i.X:return t._subscription.unsubscribe(),{...t,status:"stopped",input:void 0,_subscription:void 0};default:return t}},getInitialSnapshot:(t,e)=>({status:"active",output:void 0,error:void 0,context:void 0,input:e,_subscription:void 0}),start:(e,{self:n,system:s,emit:r})=>{"done"!==e.status&&(e._subscription=t({input:e.input,system:s,self:n,emit:r}).subscribe({next:t=>{n._parent&&s._relay(n,n._parent,t)},error:t=>{s._relay(n,n,{type:u,data:t})},complete:()=>{s._relay(n,n,{type:c})}}))},getPersistedSnapshot:({_subscription:t,...e})=>e,restoreSnapshot:t=>({...t,_subscription:void 0})}}let f="xstate.promise.resolve",d="xstate.promise.reject",h=new WeakMap;function p(t){return{config:t,transition:(t,e,n)=>{if("active"!==t.status)return t;switch(e.type){case f:{let n=e.data;return{...t,status:"done",output:n,input:void 0}}case d:return{...t,status:"error",error:e.data,input:void 0};case i.X:return h.get(n.self)?.abort(),{...t,status:"stopped",input:void 0};default:return t}},start:(e,{self:n,system:s,emit:r})=>{if("active"!==e.status)return;let i=new AbortController;h.set(n,i),Promise.resolve(t({input:e.input,system:s,self:n,signal:i.signal,emit:r})).then(t=>{"active"===n.getSnapshot().status&&(h.delete(n),s._relay(n,n,{type:f,data:t}))},t=>{"active"===n.getSnapshot().status&&(h.delete(n),s._relay(n,n,{type:d,data:t}))})},getInitialSnapshot:(t,e)=>({status:"active",output:void 0,error:void 0,input:e}),getPersistedSnapshot:t=>t,restoreSnapshot:t=>t}}let y=(r=void 0,{config:s=t=>void 0,transition:(t,e,n)=>({...t,context:s(t.context,e,n)}),getInitialSnapshot:(t,e)=>({status:"active",output:void 0,error:void 0,context:"function"==typeof r?r({input:e}):r}),getPersistedSnapshot:t=>t,restoreSnapshot:t=>t});function g(){return(0,i.A)(y)}},87746:function(t,e,n){n.d(e,{a:function(){return o},b:function(){return v},c:function(){return p},e:function(){return c},s:function(){return y}});var s,r=n(48769);function i(t,e,n,s,{assignment:i}){if(!e.context)throw Error("Cannot assign to undefined `context`. Ensure that `context` is defined in the machine config.");let o={},a={context:e.context,event:n.event,spawn:function(t,{machine:e,context:n},s,i){let o=(o,a)=>{if("string"!=typeof o)return(0,r.A)(o,{id:a?.id,parent:t.self,syncSnapshot:a?.syncSnapshot,input:a?.input,src:o,systemId:a?.systemId});{let u=(0,r.z)(e,o);if(!u)throw Error(`Actor logic '${o}' not implemented in machine '${e.id}'`);let c=(0,r.A)(u,{id:a?.id,parent:t.self,syncSnapshot:a?.syncSnapshot,input:"function"==typeof a?.input?a.input({context:n,event:s,self:t.self}):a?.input,src:o,systemId:a?.systemId});return i[c.id]=c,c}};return(e,n)=>{let s=o(e,n);return i[s.id]=s,t.defer(()=>{s._processingStatus!==r.T.Stopped&&s.start()}),s}}(t,e,n.event,o),self:t.self,system:t.system},u={};if("function"==typeof i)u=i(a,s);else for(let t of Object.keys(i)){let e=i[t];u[t]="function"==typeof e?e(a,s):e}let c=Object.assign({},e.context,u);return[(0,r.U)(e,{context:c,children:Object.keys(o).length?{...e.children,...o}:e.children}),void 0,void 0]}function o(t){function e(t,e){}return e.type="xstate.assign",e.assignment=t,e.resolve=i,e}function a(t,e,n,s,{event:r}){return[e,{event:"function"==typeof r?r(n,s):r},void 0]}function u(t,{event:e}){t.defer(()=>t.emit(e))}function c(t){function e(t,e){}return e.type="xstate.emit",e.event=t,e.resolve=a,e.execute=u,e}let l=((s={}).Parent="#_parent",s.Internal="#_internal",s);function f(t,e,n,s,{to:r,event:i,id:o,delay:a},u){let c,f;let d=e.machine.implementations.delays;if("string"==typeof i)throw Error(`Only event objects may be used with sendTo; use sendTo({ type: "${i}" }) instead`);let h="function"==typeof i?i(n,s):i;if("string"==typeof a){let t=d&&d[a];c="function"==typeof t?t(n,s):t}else c="function"==typeof a?a(n,s):a;let p="function"==typeof r?r(n,s):r;if("string"==typeof p){if(!(f=p===l.Parent?t.self._parent:p===l.Internal?t.self:p.startsWith("#_")?e.children[p.slice(2)]:u.deferredActorIds?.includes(p)?p:e.children[p]))throw Error(`Unable to send event to actor '${p}' from machine '${e.machine.id}'.`)}else f=p||t.self;return[e,{to:f,targetId:"string"==typeof p?p:void 0,event:h,id:o,delay:c},void 0]}function d(t,e,n){"string"==typeof n.to&&(n.to=e.children[n.to])}function h(t,e){t.defer(()=>{let{to:n,event:s,delay:i,id:o}=e;if("number"==typeof i){t.system.scheduler.schedule(t.self,n,s,i,o);return}t.system._relay(t.self,n,s.type===r.V?(0,r.W)(t.self.id,s.data):s)})}function p(t,e,n){function s(t,e){}return s.type="xstate.sendTo",s.to=t,s.event=e,s.id=n?.id,s.delay=n?.delay,s.resolve=f,s.retryResolve=d,s.execute=h,s}function y(t,e){return p(l.Parent,t,e)}function g(t,e,n,s,{collect:i}){let a=[],u=function(t){a.push(t)};return u.assign=(...t)=>{a.push(o(...t))},u.cancel=(...t)=>{a.push((0,r.M)(...t))},u.raise=(...t)=>{a.push((0,r.O)(...t))},u.sendTo=(...t)=>{a.push(p(...t))},u.sendParent=(...t)=>{a.push(y(...t))},u.spawnChild=(...t)=>{a.push((0,r.P)(...t))},u.stopChild=(...t)=>{a.push((0,r.R)(...t))},u.emit=(...t)=>{a.push(c(...t))},i({context:n.context,event:n.event,enqueue:u,check:t=>(0,r.e)(t,e.context,n.event,e),self:t.self,system:t.system},s),[e,void 0,a]}function v(t){function e(t,e){}return e.type="xstate.enqueueActions",e.collect=t,e.resolve=g,e}},48769:function(t,e,n){var s;n.d(e,{$:function(){return R},A:function(){return D},M:function(){return P},N:function(){return a},O:function(){return tD},P:function(){return C},R:function(){return L},S:function(){return o},T:function(){return O},U:function(){return tR},V:function(){return c},W:function(){return d},X:function(){return l},a:function(){return S},b:function(){return te},c:function(){return I},d:function(){return ts},e:function(){return W},f:function(){return tn},g:function(){return tt},h:function(){return Q},i:function(){return Y},j:function(){return tl},k:function(){return tq},l:function(){return G},m:function(){return m},n:function(){return tb},o:function(){return function t(e,n,s,r){return"string"==typeof n?function(t,e,n,s){let r=tu(t,e).next(n,s);return r&&r.length?r:t.next(n,s)}(e,n,s,r):1===Object.keys(n).length?function(e,n,s,r){let i=Object.keys(n),o=t(tu(e,i[0]),n[i[0]],s,r);return o&&o.length?o:e.next(s,r)}(e,n,s,r):function(e,n,s,r){let i=[];for(let o of Object.keys(n)){let a=n[o];if(!a)continue;let u=t(tu(e,o),a,s,r);u&&i.push(...u)}return i.length?i:e.next(s,r)}(e,n,s,r)}},p:function(){return t_},q:function(){return h},r:function(){return tw},s:function(){return tg},t:function(){return _},u:function(){return ta},v:function(){return g},w:function(){return H},x:function(){return tc},y:function(){return tO},z:function(){return E}});let r=t=>{if("undefined"==typeof window)return;let e=function(){let t="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:void 0!==n.g?n.g:void 0;if(t.__xstate__)return t.__xstate__}();e&&e.register(t)};class i{constructor(t){this._process=t,this._active=!1,this._current=null,this._last=null}start(){this._active=!0,this.flush()}clear(){this._current&&(this._current.next=null,this._last=this._current)}enqueue(t){let e={value:t,next:null};if(this._current){this._last.next=e,this._last=e;return}this._current=e,this._last=e,this._active&&this.flush()}flush(){for(;this._current;){let t=this._current;this._process(t.value),this._current=t.next}this._last=null}}let o=".",a="",u="xstate.init",c="xstate.error",l="xstate.stop";function f(t,e){return{type:`xstate.done.state.${t}`,output:e}}function d(t,e){return{type:`xstate.error.actor.${t}`,error:e,actorId:t}}function h(t){return{type:u,input:t}}function p(t){setTimeout(()=>{throw t})}let y="function"==typeof Symbol&&Symbol.observable||"@@observable";function g(t){if(x(t))return t;let e=[],n="";for(let s=0;s<t.length;s++){switch(t.charCodeAt(s)){case 92:n+=t[s+1],s++;continue;case 46:e.push(n),n="";continue}n+=t[s]}return e.push(n),e}function v(t){return t&&"object"==typeof t&&"machine"in t&&"value"in t?t.value:"string"!=typeof t?t:function(t){if(1===t.length)return t[0];let e={},n=e;for(let e=0;e<t.length-1;e++)if(e===t.length-2)n[t[e]]=t[e+1];else{let s=n;n={},s[t[e]]=n}return e}(g(t))}function m(t,e){let n={},s=Object.keys(t);for(let r=0;r<s.length;r++){let i=s[r];n[i]=e(t[i],i,t,r)}return n}function _(t){return void 0===t?[]:x(t)?t:[t]}function b(t,e,n,s){return"function"==typeof t?t({context:e,event:n,self:s}):t}function x(t){return Array.isArray(t)}function S(t){return(x(t)?t:[t]).map(t=>void 0===t||"string"==typeof t?{target:t}:t)}function w(t){if(void 0!==t&&""!==t)return _(t)}function k(t,e,n){let s="object"==typeof t,r=s?t:void 0;return{next:(s?t.next:t)?.bind(r),error:(s?t.error:e)?.bind(r),complete:(s?t.complete:n)?.bind(r)}}function I(t,e){return`${e}.${t}`}function E(t,e){let n=e.match(/^xstate\.invoke\.(\d+)\.(.*)/);if(!n)return t.implementations.actors[e];let[,s,r]=n,i=t.getStateNodeById(r).config.invoke;return(Array.isArray(i)?i[s]:i).src}function $(t,e){return`${t.sessionId}.${e}`}let T=0,q=!1,R=1,O=((s={})[s.NotStarted=0]="NotStarted",s[s.Running=1]="Running",s[s.Stopped=2]="Stopped",s),A={clock:{setTimeout:(t,e)=>setTimeout(t,e),clearTimeout:t=>clearTimeout(t)},logger:console.log.bind(console),devTools:!1};class j{constructor(t,e){this.logic=t,this._snapshot=void 0,this.clock=void 0,this.options=void 0,this.id=void 0,this.mailbox=new i(this._process.bind(this)),this.observers=new Set,this.eventListeners=new Map,this.logger=void 0,this._processingStatus=O.NotStarted,this._parent=void 0,this._syncSnapshot=void 0,this.ref=void 0,this._actorScope=void 0,this._systemId=void 0,this.sessionId=void 0,this.system=void 0,this._doneEvent=void 0,this.src=void 0,this._deferred=[];let n={...A,...e},{clock:s,logger:r,parent:o,syncSnapshot:a,id:u,systemId:c,inspect:l}=n;this.system=o?o.system:function(t,e){let n=new Map,s=new Map,r=new WeakMap,i=new Set,o={},{clock:a,logger:u}=e,c={schedule:(t,e,n,s,r=Math.random().toString(36).slice(2))=>{let i={source:t,target:e,event:n,delay:s,id:r,startedAt:Date.now()},u=$(t,r);l._snapshot._scheduledEvents[u]=i;let c=a.setTimeout(()=>{delete o[u],delete l._snapshot._scheduledEvents[u],l._relay(t,e,n)},s);o[u]=c},cancel:(t,e)=>{let n=$(t,e),s=o[n];delete o[n],delete l._snapshot._scheduledEvents[n],void 0!==s&&a.clearTimeout(s)},cancelAll:t=>{for(let e in l._snapshot._scheduledEvents){let n=l._snapshot._scheduledEvents[e];n.source===t&&c.cancel(t,n.id)}}},l={_snapshot:{_scheduledEvents:(e?.snapshot&&e.snapshot.scheduler)??{}},_bookId:()=>`x:${T++}`,_register:(t,e)=>(n.set(t,e),t),_unregister:t=>{n.delete(t.sessionId);let e=r.get(t);void 0!==e&&(s.delete(e),r.delete(t))},get:t=>s.get(t),_set:(t,e)=>{let n=s.get(t);if(n&&n!==e)throw Error(`Actor with system ID '${t}' already exists.`);s.set(t,e),r.set(e,t)},inspect:t=>{let e=k(t);return i.add(e),{unsubscribe(){i.delete(e)}}},_sendInspectionEvent:e=>{if(!i.size)return;let n={...e,rootId:t.sessionId};i.forEach(t=>t.next?.(n))},_relay:(t,e,n)=>{l._sendInspectionEvent({type:"@xstate.event",sourceRef:t,actorRef:e,event:n}),e._send(n)},scheduler:c,getSnapshot:()=>({_scheduledEvents:{...l._snapshot._scheduledEvents}}),start:()=>{let t=l._snapshot._scheduledEvents;for(let e in l._snapshot._scheduledEvents={},t){let{source:n,target:s,event:r,delay:i,id:o}=t[e];c.schedule(n,s,r,i,o)}},_clock:a,_logger:u};return l}(this,{clock:s,logger:r}),l&&!o&&this.system.inspect(k(l)),this.sessionId=this.system._bookId(),this.id=u??this.sessionId,this.logger=e?.logger??this.system._logger,this.clock=e?.clock??this.system._clock,this._parent=o,this._syncSnapshot=a,this.options=n,this.src=n.src??t,this.ref=this,this._actorScope={self:this,id:this.id,sessionId:this.sessionId,logger:this.logger,defer:t=>{this._deferred.push(t)},system:this.system,stopChild:t=>{if(t._parent!==this)throw Error(`Cannot stop child actor ${t.id} of ${this.id} because it is not a child`);t._stop()},emit:t=>{let e=this.eventListeners.get(t.type),n=this.eventListeners.get("*");if(e||n)for(let s of[...e?e.values():[],...n?n.values():[]])s(t)},actionExecutor:t=>{let e=()=>{if(this._actorScope.system._sendInspectionEvent({type:"@xstate.action",actorRef:this,action:{type:t.type,params:t.params}}),!t.exec)return;let e=q;try{q=!0,t.exec(t.info,t.params)}finally{q=e}};this._processingStatus===O.Running?e():this._deferred.push(e)}},this.send=this.send.bind(this),this.system._sendInspectionEvent({type:"@xstate.actor",actorRef:this}),c&&(this._systemId=c,this.system._set(c,this)),this._initState(e?.snapshot??e?.state),c&&"active"!==this._snapshot.status&&this.system._unregister(this)}_initState(t){try{this._snapshot=t?this.logic.restoreSnapshot?this.logic.restoreSnapshot(t,this._actorScope):t:this.logic.getInitialSnapshot(this._actorScope,this.options?.input)}catch(t){this._snapshot={status:"error",output:void 0,error:t}}}update(t,e){let n;for(this._snapshot=t;n=this._deferred.shift();)try{n()}catch(e){this._deferred.length=0,this._snapshot={...t,status:"error",error:e}}switch(this._snapshot.status){case"active":for(let e of this.observers)try{e.next?.(t)}catch(t){p(t)}break;case"done":var s;for(let e of this.observers)try{e.next?.(t)}catch(t){p(t)}this._stopProcedure(),this._complete(),this._doneEvent=(s=this.id,{type:`xstate.done.actor.${s}`,output:this._snapshot.output,actorId:s}),this._parent&&this.system._relay(this,this._parent,this._doneEvent);break;case"error":this._error(this._snapshot.error)}this.system._sendInspectionEvent({type:"@xstate.snapshot",actorRef:this,event:e,snapshot:t})}subscribe(t,e,n){let s=k(t,e,n);if(this._processingStatus!==O.Stopped)this.observers.add(s);else switch(this._snapshot.status){case"done":try{s.complete?.()}catch(t){p(t)}break;case"error":{let t=this._snapshot.error;if(s.error)try{s.error(t)}catch(t){p(t)}else p(t)}}return{unsubscribe:()=>{this.observers.delete(s)}}}on(t,e){let n=this.eventListeners.get(t);n||(n=new Set,this.eventListeners.set(t,n));let s=e.bind(void 0);return n.add(s),{unsubscribe:()=>{n.delete(s)}}}start(){if(this._processingStatus===O.Running)return this;this._syncSnapshot&&this.subscribe({next:t=>{"active"===t.status&&this.system._relay(this,this._parent,{type:`xstate.snapshot.${this.id}`,snapshot:t})},error:()=>{}}),this.system._register(this.sessionId,this),this._systemId&&this.system._set(this._systemId,this),this._processingStatus=O.Running;let t=h(this.options.input);switch(this.system._sendInspectionEvent({type:"@xstate.event",sourceRef:this._parent,actorRef:this,event:t}),this._snapshot.status){case"done":return this.update(this._snapshot,t),this;case"error":return this._error(this._snapshot.error),this}if(this._parent||this.system.start(),this.logic.start)try{this.logic.start(this._snapshot,this._actorScope)}catch(t){return this._snapshot={...this._snapshot,status:"error",error:t},this._error(t),this}return this.update(this._snapshot,t),this.options.devTools&&this.attachDevTools(),this.mailbox.start(),this}_process(t){let e,n;try{e=this.logic.transition(this._snapshot,t,this._actorScope)}catch(t){n={err:t}}if(n){let{err:t}=n;this._snapshot={...this._snapshot,status:"error",error:t},this._error(t);return}this.update(e,t),t.type===l&&(this._stopProcedure(),this._complete())}_stop(){return this._processingStatus===O.Stopped||((this.mailbox.clear(),this._processingStatus===O.NotStarted)?this._processingStatus=O.Stopped:this.mailbox.enqueue({type:l})),this}stop(){if(this._parent)throw Error("A non-root actor cannot be stopped directly.");return this._stop()}_complete(){for(let t of this.observers)try{t.complete?.()}catch(t){p(t)}this.observers.clear()}_reportError(t){if(!this.observers.size){this._parent||p(t);return}let e=!1;for(let n of this.observers){let s=n.error;e||=!s;try{s?.(t)}catch(t){p(t)}}this.observers.clear(),e&&p(t)}_error(t){this._stopProcedure(),this._reportError(t),this._parent&&this.system._relay(this,this._parent,d(this.id,t))}_stopProcedure(){return this._processingStatus!==O.Running||(this.system.scheduler.cancelAll(this),this.mailbox.clear(),this.mailbox=new i(this._process.bind(this)),this._processingStatus=O.Stopped,this.system._unregister(this)),this}_send(t){this._processingStatus!==O.Stopped&&this.mailbox.enqueue(t)}send(t){this.system._relay(void 0,this,t)}attachDevTools(){let{devTools:t}=this.options;t&&("function"==typeof t?t:r)(this)}toJSON(){return{xstate$$type:R,id:this.id}}getPersistedSnapshot(t){return this.logic.getPersistedSnapshot(this._snapshot,t)}[y](){return this}getSnapshot(){return this._snapshot}}function D(t,...[e]){return new j(t,e)}function M(t,e,n,s,{sendId:r}){return[e,{sendId:"function"==typeof r?r(n,s):r},void 0]}function N(t,e){t.defer(()=>{t.system.scheduler.cancel(t.self,e.sendId)})}function P(t){function e(t,e){}return e.type="xstate.cancel",e.sendId=t,e.resolve=M,e.execute=N,e}function U(t,e,n,s,{id:r,systemId:i,src:o,input:a,syncSnapshot:u}){let c,l;let f="string"==typeof o?E(e.machine,o):o,d="function"==typeof r?r(n):r;return f&&(l="function"==typeof a?a({context:e.context,event:n.event,self:t.self}):a,c=D(f,{id:d,src:o,parent:t.self,syncSnapshot:u,systemId:i,input:l})),[tR(e,{children:{...e.children,[d]:c}}),{id:r,systemId:i,actorRef:c,src:o,input:l},void 0]}function z(t,{actorRef:e}){e&&t.defer(()=>{e._processingStatus!==O.Stopped&&e.start()})}function C(...[t,{id:e,systemId:n,input:s,syncSnapshot:r=!1}={}]){function i(t,e){}return i.type="xstate.spawnChild",i.id=e,i.systemId=n,i.src=t,i.input=s,i.syncSnapshot=r,i.resolve=U,i.execute=z,i}function Z(t,e,n,s,{actorRef:r}){let i="function"==typeof r?r(n,s):r,o="string"==typeof i?e.children[i]:i,a=e.children;return o&&(a={...a},delete a[o.id]),[tR(e,{children:a}),o,void 0]}function B(t,e){if(e){if(t.system._unregister(e),e._processingStatus!==O.Running){t.stopChild(e);return}t.defer(()=>{t.stopChild(e)})}}function L(t){function e(t,e){}return e.type="xstate.stopChild",e.actorRef=t,e.resolve=Z,e.execute=B,e}function W(t,e,n,s){let{machine:r}=s,i="function"==typeof t,o=i?t:r.implementations.guards["string"==typeof t?t:t.type];if(!i&&!o)throw Error(`Guard '${"string"==typeof t?t:t.type}' is not implemented.'.`);if("function"!=typeof o)return W(o,e,n,s);let a={context:e,event:n},u=i||"string"==typeof t?void 0:"params"in t?"function"==typeof t.params?t.params({context:e,event:n}):t.params:void 0;return"check"in o?o.check(s,a,o):o(a,u)}let V=t=>"atomic"===t.type||"final"===t.type;function X(t){return Object.values(t.states).filter(t=>"history"!==t.type)}function J(t,e){let n=[];if(e===t)return n;let s=t.parent;for(;s&&s!==e;)n.push(s),s=s.parent;return n}function Y(t){let e=new Set(t),n=K(e);for(let t of e)if("compound"!==t.type||n.get(t)&&n.get(t).length){if("parallel"===t.type){for(let n of X(t))if("history"!==n.type&&!e.has(n))for(let t of to(n))e.add(t)}}else to(t).forEach(t=>e.add(t));for(let t of e){let n=t.parent;for(;n;)e.add(n),n=n.parent}return e}function K(t){let e=new Map;for(let n of t)e.has(n)||e.set(n,[]),n.parent&&(e.has(n.parent)||e.set(n.parent,[]),e.get(n.parent).push(n));return e}function F(t,e){return function t(e,n){let s=n.get(e);if(!s)return{};if("compound"===e.type){let t=s[0];if(!t)return{};if(V(t))return t.key}let r={};for(let e of s)r[e.key]=t(e,n);return r}(t,K(Y(e)))}function G(t,e){return"compound"===e.type?X(e).some(e=>"final"===e.type&&t.has(e)):"parallel"===e.type?X(e).every(e=>G(t,e)):"final"===e.type}let H=t=>"#"===t[0];function Q(t,e){return t.transitions.get(e)||[...t.transitions.keys()].filter(t=>{if("*"===t)return!0;if(!t.endsWith(".*"))return!1;let n=t.split("."),s=e.split(".");for(let t=0;t<n.length;t++){let e=n[t],r=s[t];if("*"===e)return t===n.length-1;if(e!==r)return!1}return!0}).sort((t,e)=>e.length-t.length).flatMap(e=>t.transitions.get(e))}function tt(t){let e=t.config.after;if(!e)return[];let n=e=>{var n;let s=(n=t.id,{type:`xstate.after.${e}.${n}`}),r=s.type;return t.entry.push(tD(s,{id:r,delay:e})),t.exit.push(P(r)),r};return Object.keys(e).flatMap(t=>{let s=e[t],r=Number.isNaN(+t)?t:+t,i=n(r);return _("string"==typeof s?{target:s}:s).map(t=>({...t,event:i,delay:r}))}).map(e=>{let{delay:n}=e;return{...te(t,e.event,e),delay:n}})}function te(t,e,n){let s=w(n.target),r=n.reenter??!1,i=function(t,e){if(void 0!==e)return e.map(e=>{if("string"!=typeof e)return e;if(H(e))return t.machine.getStateNodeById(e);let n=e[0]===o;if(n&&!t.parent)return tc(t,e.slice(1));let s=n?t.key+e:e;if(t.parent)try{return tc(t.parent,s)}catch(e){throw Error(`Invalid transition definition for state node '${t.id}':
${e.message}`)}else throw Error(`Invalid target: "${e}" is not a valid target from the root node. Did you mean ".${e}"?`)})}(t,s),a={...n,actions:_(n.actions),guard:n.guard,target:i,source:t,reenter:r,eventType:e,toJSON:()=>({...a,source:`#${t.id}`,target:i?i.map(t=>`#${t.id}`):void 0})};return a}function tn(t){let e=new Map;if(t.config.on)for(let n of Object.keys(t.config.on)){if(n===a)throw Error('Null events ("") cannot be specified as a transition key. Use `always: { ... }` instead.');let s=t.config.on[n];e.set(n,S(s).map(e=>te(t,n,e)))}if(t.config.onDone){let n=`xstate.done.state.${t.id}`;e.set(n,S(t.config.onDone).map(e=>te(t,n,e)))}for(let n of t.invoke){if(n.onDone){let s=`xstate.done.actor.${n.id}`;e.set(s,S(n.onDone).map(e=>te(t,s,e)))}if(n.onError){let s=`xstate.error.actor.${n.id}`;e.set(s,S(n.onError).map(e=>te(t,s,e)))}if(n.onSnapshot){let s=`xstate.snapshot.${n.id}`;e.set(s,S(n.onSnapshot).map(e=>te(t,s,e)))}}for(let n of t.after){let t=e.get(n.eventType);t||(t=[],e.set(n.eventType,t)),t.push(n)}return e}function ts(t,e){let n="string"==typeof e?t.states[e]:e?t.states[e.target]:void 0;if(!n&&e)throw Error(`Initial state node "${e}" not found on parent state node #${t.id}`);let s={source:t,actions:e&&"string"!=typeof e?_(e.actions):[],eventType:null,reenter:!1,target:n?[n]:[],toJSON:()=>({...s,source:`#${t.id}`,target:n?[`#${n.id}`]:[]})};return s}function tr(t){let e=w(t.config.target);return e?{target:e.map(e=>"string"==typeof e?tc(t.parent,e):e)}:t.parent.initial}function ti(t){return"history"===t.type}function to(t){let e=ta(t);for(let n of e)for(let s of J(n,t))e.add(s);return e}function ta(t){let e=new Set;return!function t(n){if(!e.has(n)){if(e.add(n),"compound"===n.type)t(n.initial.target[0]);else if("parallel"===n.type)for(let e of X(n))t(e)}}(t),e}function tu(t,e){if(H(e))return t.machine.getStateNodeById(e);if(!t.states)throw Error(`Unable to retrieve child state '${e}' from '${t.id}'; no child states exist.`);let n=t.states[e];if(!n)throw Error(`Child state '${e}' does not exist on '${t.id}'`);return n}function tc(t,e){if("string"==typeof e&&H(e))try{return t.machine.getStateNodeById(e)}catch{}let n=g(e).slice(),s=t;for(;n.length;){let t=n.shift();if(!t.length)break;s=tu(s,t)}return s}function tl(t,e){if("string"==typeof e){let n=t.states[e];if(!n)throw Error(`State '${e}' does not exist on '${t.id}'`);return[t,n]}let n=Object.keys(e),s=n.map(e=>tu(t,e)).filter(Boolean);return[t.machine.root,t].concat(s,n.reduce((n,s)=>{let r=tu(t,s);if(!r)return n;let i=tl(r,e[s]);return n.concat(i)},[]))}function tf(t,e){let n=t;for(;n.parent&&n.parent!==e;)n=n.parent;return n.parent===e}function td(t,e,n){let s=new Set;for(let r of t){let t=!1,i=new Set;for(let o of s)if(function(t,e){let n=new Set(t),s=new Set(e);for(let t of n)if(s.has(t))return!0;for(let t of s)if(n.has(t))return!0;return!1}(ty([r],e,n),ty([o],e,n))){if(tf(r.source,o.source))i.add(o);else{t=!0;break}}if(!t){for(let t of i)s.delete(t);s.add(r)}}return Array.from(s)}function th(t,e){if(!t.target)return[];let n=new Set;for(let s of t.target)if(ti(s)){if(e[s.id])for(let t of e[s.id])n.add(t);else for(let t of th(tr(s),e))n.add(t)}else n.add(s);return[...n]}function tp(t,e){let n=th(t,e);if(!n)return;if(!t.reenter&&n.every(e=>e===t.source||tf(e,t.source)))return t.source;let s=function(t){let[e,...n]=t;for(let t of J(e,void 0))if(n.every(e=>tf(e,t)))return t}(n.concat(t.source));return s||(t.reenter?void 0:t.source.machine.root)}function ty(t,e,n){let s=new Set;for(let r of t)if(r.target?.length){let t=tp(r,n);for(let n of(r.reenter&&r.source===t&&s.add(t),e))tf(n,t)&&s.add(n)}return[...s]}function tg(t,e,n,s,r,i){if(!t.length)return e;let o=new Set(e._nodes),a=e.historyValue,u=td(t,o,a),c=e;r||([c,a]=function(t,e,n,s,r,i,o,a){let u,c=t,l=ty(s,r,i);for(let t of(l.sort((t,e)=>e.order-t.order),l))for(let e of function(t){return Object.keys(t.states).map(e=>t.states[e]).filter(t=>"history"===t.type)}(t)){let n;n="deep"===e.history?e=>V(e)&&tf(e,t):e=>e.parent===t,(u??={...i})[e.id]=Array.from(r).filter(n)}for(let t of l)c=t_(c,e,n,[...t.exit,...t.invoke.map(t=>L(t.id))],o,void 0),r.delete(t);return[c,u||i]}(c,s,n,u,o,a,i,n.actionExecutor)),c=function(t,e,n,s,r,i,o,a){let u=t,c=new Set,l=new Set;(function(t,e,n,s){for(let r of t){let t=tp(r,e);for(let i of r.target||[])!ti(i)&&(r.source!==i||r.source!==t||r.reenter)&&(s.add(i),n.add(i)),tv(i,e,n,s);for(let i of th(r,e)){let o=J(i,t);t?.type==="parallel"&&o.push(t),tm(s,e,n,o,!r.source.parent&&r.reenter?void 0:t)}}})(s,o,l,c),a&&l.add(t.machine.root);let d=new Set;for(let t of[...c].sort((t,e)=>t.order-e.order)){r.add(t);let s=[];for(let e of(s.push(...t.entry),t.invoke))s.push(C(e.src,{...e,syncSnapshot:!!e.onSnapshot}));if(l.has(t)){let e=t.initial.actions;s.push(...e)}if(u=t_(u,e,n,s,i,t.invoke.map(t=>t.id)),"final"===t.type){let s=t.parent,o=s?.type==="parallel"?s:s?.parent,a=o||t;for(s?.type==="compound"&&i.push(f(s.id,void 0!==t.output?b(t.output,u.context,e,n.self):void 0));o?.type==="parallel"&&!d.has(o)&&G(r,o);)d.add(o),i.push(f(o.id)),a=o,o=o.parent;if(o)continue;u=tR(u,{status:"done",output:function(t,e,n,s,r){if(void 0===s.output)return;let i=f(r.id,void 0!==r.output&&r.parent?b(r.output,t.context,e,n.self):void 0);return b(s.output,t.context,i,n.self)}(u,e,n,u.machine.root,a)})}}return u}(c=t_(c,s,n,u.flatMap(t=>t.actions),i,void 0),s,n,u,o,i,a,r);let l=[...o];"done"===c.status&&(c=t_(c,s,n,l.sort((t,e)=>e.order-t.order).flatMap(t=>t.exit),i,void 0));try{if(a===e.historyValue&&function(t,e){if(t.length!==e.size)return!1;for(let n of t)if(!e.has(n))return!1;return!0}(e._nodes,o))return c;return tR(c,{_nodes:l,historyValue:a})}catch(t){throw t}}function tv(t,e,n,s){if(ti(t)){if(e[t.id]){let r=e[t.id];for(let t of r)s.add(t),tv(t,e,n,s);for(let i of r)tm(s,e,n,J(i,t.parent))}else{let r=tr(t);for(let i of r.target)s.add(i),r===t.parent?.initial&&n.add(t.parent),tv(i,e,n,s);for(let i of r.target)tm(s,e,n,J(i,t.parent))}}else if("compound"===t.type){let[r]=t.initial.target;ti(r)||(s.add(r),n.add(r)),tv(r,e,n,s),tm(s,e,n,J(r,t))}else if("parallel"===t.type)for(let r of X(t).filter(t=>!ti(t)))[...s].some(t=>tf(t,r))||(ti(r)||(s.add(r),n.add(r)),tv(r,e,n,s))}function tm(t,e,n,s,r){for(let i of s)if((!r||tf(i,r))&&t.add(i),"parallel"===i.type)for(let s of X(i).filter(t=>!ti(t)))[...t].some(t=>tf(t,s))||(t.add(s),tv(s,e,n,t))}function t_(t,e,n,s,r,i){let o=i?[]:void 0,a=function t(e,n,s,r,i,o){let{machine:a}=e,u=e;for(let e of r){var c;let r="function"==typeof e,l=r?e:(c="string"==typeof e?e:e.type,a.implementations.actions[c]),f={context:u.context,event:n,self:s.self,system:s.system},d=r||"string"==typeof e?void 0:"params"in e?"function"==typeof e.params?e.params({context:u.context,event:n}):e.params:void 0;if(!l||!("resolve"in l)){s.actionExecutor({type:"string"==typeof e?e:"object"==typeof e?e.type:e.name||"(anonymous)",info:f,params:d,exec:l});continue}let[h,p,y]=l.resolve(s,u,f,d,l,i);u=h,"retryResolve"in l&&o?.push([l,p]),"execute"in l&&s.actionExecutor({type:l.type,info:f,params:p,exec:l.execute.bind(null,s,p)}),y&&(u=t(u,n,s,y,i,o))}return u}(t,e,n,s,{internalQueue:r,deferredActorIds:i},o);return o?.forEach(([t,e])=>{t.retryResolve(n,a,e)}),a}function tb(t,e,n,s){let r=t,i=[];function o(t,e,s){n.system._sendInspectionEvent({type:"@xstate.microstep",actorRef:n.self,event:e,snapshot:t,_transitions:s}),i.push(t)}if(e.type===l)return o(r=tR(tx(r,e,n),{status:"stopped"}),e,[]),{snapshot:r,microstates:i};let a=e;if(a.type!==u){let e=a,u=e.type.startsWith("xstate.error.actor"),c=tS(e,r);if(u&&!c.length)return o(r=tR(t,{status:"error",error:e.error}),e,[]),{snapshot:r,microstates:i};o(r=tg(c,t,n,a,!1,s),e,c)}let c=!0;for(;"active"===r.status;){let t=c?function(t,e){let n=new Set;for(let s of t._nodes.filter(V))t:for(let r of[s].concat(J(s,void 0)))if(r.always){for(let s of r.always)if(void 0===s.guard||W(s.guard,t.context,e,t)){n.add(s);break t}}return td(Array.from(n),new Set(t._nodes),t.historyValue)}(r,a):[],e=t.length?r:void 0;if(!t.length){if(!s.length)break;t=tS(a=s.shift(),r)}c=(r=tg(t,r,n,a,!1,s))!==e,o(r,a,t)}return"active"!==r.status&&tx(r,a,n),{snapshot:r,microstates:i}}function tx(t,e,n){return t_(t,e,n,Object.values(t.children).map(t=>L(t)),[],void 0)}function tS(t,e){return e.machine.getTransitionData(e,t)}function tw(t,e){let n=Y(tl(t,e));return F(t,[...n])}let tk=function(t){return function t(e,n){let s=v(e),r=v(n);return"string"==typeof r?"string"==typeof s&&r===s:"string"==typeof s?s in r:Object.keys(s).every(e=>e in r&&t(s[e],r[e]))}(t,this.value)},tI=function(t){return this.tags.has(t)},tE=function(t){let e=this.machine.getTransitionData(this,t);return!!e?.length&&e.some(t=>void 0!==t.target||t.actions.length)},t$=function(){let{_nodes:t,tags:e,machine:n,getMeta:s,toJSON:r,can:i,hasTag:o,matches:a,...u}=this;return{...u,tags:Array.from(e)}},tT=function(){return this._nodes.reduce((t,e)=>(void 0!==e.meta&&(t[e.id]=e.meta),t),{})};function tq(t,e){return{status:t.status,output:t.output,error:t.error,machine:e,context:t.context,_nodes:t._nodes,value:F(e.root,t._nodes),tags:new Set(t._nodes.flatMap(t=>t.tags)),children:t.children,historyValue:t.historyValue||{},matches:tk,hasTag:tI,can:tE,getMeta:tT,toJSON:t$}}function tR(t,e={}){return tq({...t,...e},t.machine)}function tO(t,e){let{_nodes:n,tags:s,machine:r,children:i,context:o,can:a,hasTag:u,matches:c,getMeta:l,toJSON:f,...d}=t,h={};for(let t in i){let n=i[t];h[t]={snapshot:n.getPersistedSnapshot(e),src:n.src,systemId:n._systemId,syncSnapshot:n._syncSnapshot}}return{...d,context:function t(e){let n;for(let s in e){let r=e[s];if(r&&"object"==typeof r){if("sessionId"in r&&"send"in r&&"ref"in r)(n??=Array.isArray(e)?e.slice():{...e})[s]={xstate$$type:R,id:r.id};else{let i=t(r);i!==r&&((n??=Array.isArray(e)?e.slice():{...e})[s]=i)}}}return n??e}(o),children:h}}function tA(t,e,n,s,{event:r,id:i,delay:o},{internalQueue:a}){let u;let c=e.machine.implementations.delays;if("string"==typeof r)throw Error(`Only event objects may be used with raise; use raise({ type: "${r}" }) instead`);let l="function"==typeof r?r(n,s):r;if("string"==typeof o){let t=c&&c[o];u="function"==typeof t?t(n,s):t}else u="function"==typeof o?o(n,s):o;return"number"!=typeof u&&a.push(l),[e,{event:l,id:i,delay:u},void 0]}function tj(t,e){let{event:n,delay:s,id:r}=e;if("number"==typeof s){t.defer(()=>{let e=t.self;t.system.scheduler.schedule(e,e,n,s,r)});return}}function tD(t,e){function n(t,e){}return n.type="xstate.raise",n.event=t,n.id=e?.id,n.delay=e?.delay,n.resolve=tA,n.execute=tj,n}},25181:function(t,e,n){n.d(e,{ZD:function(){return i},cY:function(){return d}}),n(40211);var s=n(48769),r=n(87746);function i(t,e){let n=(0,s.t)(e);if(!n.includes(t.type)){let e=1===n.length?`type "${n[0]}"`:`one of types "${n.join('", "')}"`;throw Error(`Expected event ${JSON.stringify(t)} to have ${e}`)}}let o=new WeakMap;function a(t,e,n){let s=o.get(t);return s?e in s||(s[e]=n()):(s={[e]:n()},o.set(t,s)),s[e]}let u={},c=t=>"string"==typeof t?{type:t}:"function"==typeof t?"resolve"in t?{type:t.type}:{type:t.name}:t;class l{constructor(t,e){if(this.config=t,this.key=void 0,this.id=void 0,this.type=void 0,this.path=void 0,this.states=void 0,this.history=void 0,this.entry=void 0,this.exit=void 0,this.parent=void 0,this.machine=void 0,this.meta=void 0,this.output=void 0,this.order=-1,this.description=void 0,this.tags=[],this.transitions=void 0,this.always=void 0,this.parent=e._parent,this.key=e._key,this.machine=e._machine,this.path=this.parent?this.parent.path.concat(this.key):[],this.id=this.config.id||[this.machine.id,...this.path].join(s.S),this.type=this.config.type||(this.config.states&&Object.keys(this.config.states).length?"compound":this.config.history?"history":"atomic"),this.description=this.config.description,this.order=this.machine.idMap.size,this.machine.idMap.set(this.id,this),this.states=this.config.states?(0,s.m)(this.config.states,(t,e)=>new l(t,{_parent:this,_key:e,_machine:this.machine})):u,"compound"===this.type&&!this.config.initial)throw Error(`No initial state specified for compound state node "#${this.id}". Try adding { initial: "${Object.keys(this.states)[0]}" } to the state config.`);this.history=!0===this.config.history?"shallow":this.config.history||!1,this.entry=(0,s.t)(this.config.entry).slice(),this.exit=(0,s.t)(this.config.exit).slice(),this.meta=this.config.meta,this.output="final"!==this.type&&this.parent?void 0:this.config.output,this.tags=(0,s.t)(t.tags).slice()}_initialize(){this.transitions=(0,s.f)(this),this.config.always&&(this.always=(0,s.a)(this.config.always).map(t=>(0,s.b)(this,s.N,t))),Object.keys(this.states).forEach(t=>{this.states[t]._initialize()})}get definition(){return{id:this.id,key:this.key,version:this.machine.version,type:this.type,initial:this.initial?{target:this.initial.target,source:this,actions:this.initial.actions.map(c),eventType:null,reenter:!1,toJSON:()=>({target:this.initial.target.map(t=>`#${t.id}`),source:`#${this.id}`,actions:this.initial.actions.map(c),eventType:null})}:void 0,history:this.history,states:(0,s.m)(this.states,t=>t.definition),on:this.on,transitions:[...this.transitions.values()].flat().map(t=>({...t,actions:t.actions.map(c)})),entry:this.entry.map(c),exit:this.exit.map(c),meta:this.meta,order:this.order||-1,output:this.output,invoke:this.invoke,description:this.description,tags:this.tags}}toJSON(){return this.definition}get invoke(){return a(this,"invoke",()=>(0,s.t)(this.config.invoke).map((t,e)=>{let{src:n,systemId:r}=t,i=t.id??(0,s.c)(this.id,e),o="string"==typeof n?n:`xstate.invoke.${(0,s.c)(this.id,e)}`;return{...t,src:o,id:i,systemId:r,toJSON(){let{onDone:e,onError:n,...s}=t;return{...s,type:"xstate.invoke",src:o,id:i}}}}))}get on(){return a(this,"on",()=>[...this.transitions].flatMap(([t,e])=>e.map(e=>[t,e])).reduce((t,[e,n])=>(t[e]=t[e]||[],t[e].push(n),t),{}))}get after(){return a(this,"delayedTransitions",()=>(0,s.g)(this))}get initial(){return a(this,"initial",()=>(0,s.d)(this,this.config.initial))}next(t,e){let n;let r=e.type,i=[];for(let o of a(this,`candidates-${r}`,()=>(0,s.h)(this,r))){let{guard:a}=o,u=t.context,c=!1;try{c=!a||(0,s.e)(a,u,e,t)}catch(e){let t="string"==typeof a?a:"object"==typeof a?a.type:void 0;throw Error(`Unable to evaluate guard ${t?`'${t}' `:""}in transition for event '${r}' in state node '${this.id}':
${e.message}`)}if(c){i.push(...o.actions),n=o;break}}return n?[n]:void 0}get events(){return a(this,"events",()=>{let{states:t}=this,e=new Set(this.ownEvents);if(t)for(let n of Object.keys(t)){let s=t[n];if(s.states)for(let t of s.events)e.add(`${t}`)}return Array.from(e)})}get ownEvents(){return Array.from(new Set([...this.transitions.keys()].filter(t=>this.transitions.get(t).some(t=>!(!t.target&&!t.actions.length&&!t.reenter)))))}}class f{constructor(t,e){this.config=t,this.version=void 0,this.schemas=void 0,this.implementations=void 0,this.__xstatenode=!0,this.idMap=new Map,this.root=void 0,this.id=void 0,this.states=void 0,this.events=void 0,this.id=t.id||"(machine)",this.implementations={actors:e?.actors??{},actions:e?.actions??{},delays:e?.delays??{},guards:e?.guards??{}},this.version=this.config.version,this.schemas=this.config.schemas,this.transition=this.transition.bind(this),this.getInitialSnapshot=this.getInitialSnapshot.bind(this),this.getPersistedSnapshot=this.getPersistedSnapshot.bind(this),this.restoreSnapshot=this.restoreSnapshot.bind(this),this.start=this.start.bind(this),this.root=new l(t,{_key:this.id,_machine:this}),this.root._initialize(),this.states=this.root.states,this.events=this.root.events}provide(t){let{actions:e,guards:n,actors:s,delays:r}=this.implementations;return new f(this.config,{actions:{...e,...t.actions},guards:{...n,...t.guards},actors:{...s,...t.actors},delays:{...r,...t.delays}})}resolveState(t){let e=(0,s.r)(this.root,t.value),n=(0,s.i)((0,s.j)(this.root,e));return(0,s.k)({_nodes:[...n],context:t.context||{},children:{},status:(0,s.l)(n,this.root)?"done":t.status||"active",output:t.output,error:t.error,historyValue:t.historyValue},this)}transition(t,e,n){return(0,s.n)(t,e,n,[]).snapshot}microstep(t,e,n){return(0,s.n)(t,e,n,[]).microstates}getTransitionData(t,e){return(0,s.o)(this.root,t.value,t,e)||[]}getPreInitialState(t,e,n){let{context:i}=this.config,o=(0,s.k)({context:"function"!=typeof i&&i?i:{},_nodes:[this.root],children:{},status:"active"},this);return"function"==typeof i?(0,s.p)(o,e,t,[(0,r.a)(({spawn:t,event:e,self:n})=>i({spawn:t,input:e.input,self:n}))],n,void 0):o}getInitialSnapshot(t,e){let n=(0,s.q)(e),r=[],i=this.getPreInitialState(t,n,r),o=(0,s.s)([{target:[...(0,s.u)(this.root)],source:this.root,reenter:!0,actions:[],eventType:null,toJSON:null}],i,t,n,!0,r),{snapshot:a}=(0,s.n)(o,n,t,r);return a}start(t){Object.values(t.children).forEach(t=>{"active"===t.getSnapshot().status&&t.start()})}getStateNodeById(t){let e=(0,s.v)(t),n=e.slice(1),r=(0,s.w)(e[0])?e[0].slice(1):e[0],i=this.idMap.get(r);if(!i)throw Error(`Child state node '#${r}' does not exist on machine '${this.id}'`);return(0,s.x)(i,n)}get definition(){return this.root.definition}toJSON(){return this.definition}getPersistedSnapshot(t,e){return(0,s.y)(t,e)}restoreSnapshot(t,e){let n={},r=t.children;Object.keys(r).forEach(t=>{let i=r[t],o=i.snapshot,a=i.src,u="string"==typeof a?(0,s.z)(this,a):a;if(!u)return;let c=(0,s.A)(u,{id:t,parent:e.self,syncSnapshot:i.syncSnapshot,snapshot:o,src:a,systemId:i.systemId});n[t]=c});let i=(0,s.k)({...t,children:n,_nodes:Array.from((0,s.i)((0,s.j)(this.root,t.value)))},this),o=new Set;return!function t(e,n){if(!o.has(e))for(let r in o.add(e),e){let i=e[r];if(i&&"object"==typeof i){if("xstate$$type"in i&&i.xstate$$type===s.$){e[r]=n[i.id];continue}t(i,n)}}}(i.context,n),i}}function d({schemas:t,actors:e,actions:n,guards:s,delays:r}){return{createMachine:i=>new f({...i,schemas:t},{actors:e,actions:n,guards:s,delays:r})}}},65069:function(t,e,n){let s;n.d(e,{yK:function(){return z},Hq:function(){return B},ei:function(){return V},bK:function(){return L},Ol:function(){return W},UE:function(){return Z},Ag:function(){return C},F8:function(){return U},dS:function(){return K},zB:function(){return Y},lB:function(){return J}});var r={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};let i=new Uint8Array(16),o=[];for(let t=0;t<256;++t)o.push((t+256).toString(16).slice(1));var a=function(t,e,n){if(r.randomUUID&&!e&&!t)return r.randomUUID();let a=(t=t||{}).random||(t.rng||function(){if(!s){if("undefined"==typeof crypto||!crypto.getRandomValues)throw Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");s=crypto.getRandomValues.bind(crypto)}return s(i)})();if(a[6]=15&a[6]|64,a[8]=63&a[8]|128,e){n=n||0;for(let t=0;t<16;++t)e[n+t]=a[t];return e}return function(t,e=0){return(o[t[e+0]]+o[t[e+1]]+o[t[e+2]]+o[t[e+3]]+"-"+o[t[e+4]]+o[t[e+5]]+"-"+o[t[e+6]]+o[t[e+7]]+"-"+o[t[e+8]]+o[t[e+9]]+"-"+o[t[e+10]]+o[t[e+11]]+o[t[e+12]]+o[t[e+13]]+o[t[e+14]]+o[t[e+15]]).toLowerCase()}(a)},u=n(40211),c=n(25181),l=n(87746),f=n(48769),d=n(68851),h=n(5853),p=n(96750),y=n(78743),g=n(85315),v=n(76613),m=n(2070),_=n(59326),b=["addListener","removeListener"],x=["addEventListener","removeEventListener"],S=["on","off"];function w(t,e,n,s){if((0,m.m)(n)&&(s=n,n=void 0),s)return w(t,e,n).pipe((0,_.Z)(s));var r=(0,h.CR)((0,m.m)(t.addEventListener)&&(0,m.m)(t.removeEventListener)?x.map(function(s){return function(r){return t[s](e,r,n)}}):(0,m.m)(t.addListener)&&(0,m.m)(t.removeListener)?b.map(k(t,e)):(0,m.m)(t.on)&&(0,m.m)(t.off)?S.map(k(t,e)):[],2),i=r[0],o=r[1];if(!i&&(0,v.z)(t))return(0,g.z)(function(t){return w(t,e,n)})((0,p.Xf)(t));if(!i)throw TypeError("Invalid event target");return new y.y(function(t){var e=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return t.next(1<e.length?e:e[0])};return i(e),function(){return o(e)}})}function k(t,e){return function(n){return function(s){return t[n](e,s)}}}var I=n(7449),E=n(95767),$=n(44465),T=n(17753),q=n(4382),R=n(30325),O=n(66692);function A(t){return t<=0?function(){return O.E}:(0,T.e)(function(e,n){var s=0;e.subscribe((0,q.x)(n,function(e){++s<=t&&(n.next(e),t<=s&&n.complete())}))})}var j=n(8365);let D=t=>({context:e})=>{let{count:n,include:s,exclude:r,responseType:i="message.received"}=t;return{count:n,domain:e.domain,from:e.connectTo,include:s?Array.isArray(s)?s:[s]:[],exclude:r?Array.isArray(r)?r:[r]:[],responseType:i,target:e.target,to:e.name}},M=t=>e=>{let{data:n}=e;return(!t.include.length||t.include.includes(n.type))&&(!t.exclude.length||!t.exclude.includes(n.type))&&n.domain===t.domain&&n.from===t.from&&n.to===t.to&&(!t.target||e.source===t.target)},N=t=>e=>({type:t,message:e}),P=(0,d.P)(()=>w(window,"message")),U=t=>(0,u.Y$)(({input:e})=>{var n,s,r,i;return P.pipe(t?(0,I.U)(t):(0,E.z)(),(0,$.h)(M(e)),(0,I.U)(N(e.responseType)),e.count?(0,E.z)((n=e.count,void 0===s&&(s=null),s=null!=s?s:n,(0,T.e)(function(t,e){var r=[],i=0;t.subscribe((0,q.x)(e,function(t){var o,a,u,c,l=null;i++%s==0&&r.push([]);try{for(var f=(0,h.XA)(r),d=f.next();!d.done;d=f.next()){var p=d.value;p.push(t),n<=p.length&&(l=null!=l?l:[]).push(p)}}catch(t){o={error:t}}finally{try{d&&!d.done&&(a=f.return)&&a.call(f)}finally{if(o)throw o.error}}if(l)try{for(var y=(0,h.XA)(l),g=y.next();!g.done;g=y.next()){var p=g.value;(0,R.P)(r,p),e.next(p)}}catch(t){u={error:t}}finally{try{g&&!g.done&&(c=y.return)&&c.call(y)}finally{if(u)throw u.error}}},function(){var t,n;try{for(var s=(0,h.XA)(r),i=s.next();!i.done;i=s.next()){var o=i.value;e.next(o)}}catch(e){t={error:e}}finally{try{i&&!i.done&&(n=s.return)&&n.call(s)}finally{if(t)throw t.error}}e.complete()},void 0,function(){r=null}))})),(r=t=>t,(0,m.m)(void 0)?(0,g.z)(r,i,1):(0,g.z)(r,1)),A(e.count)):(0,E.z)())}),z="sanity/comlink",C="comlink/response",Z="comlink/heartbeat",B="comlink/disconnect",L="comlink/handshake/syn",W="comlink/handshake/syn-ack",V="comlink/handshake/ack",X=t=>e=>e.pipe(A(1),(0,I.U)(()=>{throw Error(t)})),J=()=>(0,c.cY)({types:{},actors:{listen:(0,u.Y$)(({input:t})=>{let e=t.signal?w(t.signal,"abort").pipe(X(`Request ${t.requestId} aborted`)):O.E;return w(window,"message").pipe((0,$.h)(e=>e.data?.type===C&&e.data?.responseTo===t.requestId&&!!e.source&&t.sources.has(e.source)),A(t.sources.size),(0,T.e)(function(t,n){(0,p.Xf)(e).subscribe((0,q.x)(n,function(){return n.complete()},j.Z)),n.closed||t.subscribe(n)}))})},actions:{"send message":({context:t},e)=>{let{sources:n,targetOrigin:s}=t,{message:r}=e;n.forEach(t=>{t.postMessage(r,{targetOrigin:s})})},"on success":(0,l.c)(({context:t})=>t.parentRef,({context:t,self:e})=>(t.response&&t.resolvable?.resolve(t.response),{type:"request.success",requestId:e.id,response:t.response,responseTo:t.responseTo})),"on fail":(0,l.c)(({context:t})=>t.parentRef,({context:t,self:e})=>(t.suppressWarnings||console.warn(`[@sanity/comlink] Received no response to message '${t.type}' on client '${t.from}' (ID: '${t.id}').`),t.resolvable?.reject(Error("No response received")),{type:"request.failed",requestId:e.id})),"on abort":(0,l.c)(({context:t})=>t.parentRef,({context:t,self:e})=>(t.resolvable?.reject(Error("Request aborted")),{type:"request.aborted",requestId:e.id}))},guards:{expectsResponse:({context:t})=>t.expectResponse},delays:{initialTimeout:0,responseTimeout:({context:t})=>t.responseTimeout??3e3}}).createMachine({context:({input:t})=>({channelId:t.channelId,data:t.data,domain:t.domain,expectResponse:t.expectResponse??!1,from:t.from,id:`msg-${a()}`,parentRef:t.parentRef,resolvable:t.resolvable,response:null,responseTimeout:t.responseTimeout,responseTo:t.responseTo,signal:t.signal,sources:t.sources instanceof Set?t.sources:new Set([t.sources]),suppressWarnings:t.suppressWarnings,targetOrigin:t.targetOrigin,to:t.to,type:t.type}),initial:"idle",on:{abort:".aborted"},states:{idle:{after:{initialTimeout:[{target:"sending"}]}},sending:{entry:{type:"send message",params:({context:t})=>{let{channelId:e,data:n,domain:s,from:r,id:i,responseTo:o,to:a,type:u}=t;return{message:{channelId:e,data:n,domain:s,from:r,id:i,to:a,type:u,responseTo:o}}}},always:[{guard:"expectsResponse",target:"awaiting"},"success"]},awaiting:{invoke:{id:"listen for response",src:"listen",input:({context:t})=>({requestId:t.id,sources:t.sources,signal:t.signal}),onError:"aborted"},after:{responseTimeout:"failed"},on:{message:{actions:(0,l.a)({response:({event:t})=>t.data.data,responseTo:({event:t})=>t.data.responseTo}),target:"success"}}},failed:{type:"final",entry:"on fail"},success:{type:"final",entry:"on success"},aborted:{type:"final",entry:"on abort"}},output:({context:t,self:e})=>({requestId:e.id,response:t.response,responseTo:t.responseTo})}),Y=((0,u.E9)(({sendBack:t,input:e})=>{let n=()=>{t(e.event)};e.immediate&&n();let s=setInterval(n,e.interval);return()=>{clearInterval(s)}}),()=>(0,c.cY)({types:{},actors:{requestMachine:J(),listen:U()},actions:{"buffer incoming message":(0,l.a)({handshakeBuffer:({event:t,context:e})=>((0,c.ZD)(t,"message.received"),[...e.handshakeBuffer,t])}),"buffer message":(0,l.b)(({enqueue:t})=>{t.assign({buffer:({event:t,context:e})=>((0,c.ZD)(t,"post"),[...e.buffer,{data:t.data,resolvable:t.resolvable,options:t.options}])}),t.emit(({event:t})=>((0,c.ZD)(t,"post"),{type:"_buffer.added",message:t.data}))}),"create request":(0,l.a)({requests:({context:t,event:e,self:n,spawn:s})=>{(0,c.ZD)(e,"request");let r=(Array.isArray(e.data)?e.data:[e.data]).map(e=>s("requestMachine",{id:`req-${a()}`,input:{channelId:t.channelId,data:e.data,domain:t.domain,expectResponse:e.expectResponse,from:t.name,parentRef:n,resolvable:e.resolvable,responseTimeout:e.options?.responseTimeout,responseTo:e.responseTo,signal:e.options?.signal,sources:t.target,suppressWarnings:e.options?.suppressWarnings,targetOrigin:t.targetOrigin,to:t.connectTo,type:e.type}}));return[...t.requests,...r]}}),"emit heartbeat":(0,l.e)(()=>({type:"_heartbeat"})),"emit received message":(0,l.b)(({enqueue:t})=>{t.emit(({event:t})=>((0,c.ZD)(t,"message.received"),{type:"_message",message:t.message.data})),t.emit(({event:t})=>((0,c.ZD)(t,"message.received"),{type:t.message.data.type,message:t.message.data}))}),"emit status":(0,l.e)((t,e)=>({type:"_status",status:e.status})),"flush buffer":(0,l.b)(({enqueue:t})=>{t.raise(({context:t})=>({type:"request",data:t.buffer.map(({data:t,resolvable:e,options:n})=>({data:t.data,type:t.type,expectResponse:!!e,resolvable:e,options:n}))})),t.emit(({context:t})=>({type:"_buffer.flushed",messages:t.buffer.map(({data:t})=>t)})),t.assign({buffer:[]})}),"flush handshake buffer":(0,l.b)(({context:t,enqueue:e})=>{t.handshakeBuffer.forEach(t=>e.raise(t)),e.assign({handshakeBuffer:[]})}),post:(0,f.O)(({event:t})=>((0,c.ZD)(t,"post"),{type:"request",data:{data:t.data.data,expectResponse:!!t.resolvable,type:t.data.type,resolvable:t.resolvable,options:t.options}})),"remove request":(0,l.b)(({context:t,enqueue:e,event:n})=>{(0,c.ZD)(n,["request.success","request.failed","request.aborted"]),(0,f.R)(n.requestId),e.assign({requests:t.requests.filter(({id:t})=>t!==n.requestId)})}),"send response":(0,f.O)(({event:t})=>((0,c.ZD)(t,["message.received","heartbeat.received"]),{type:"request",data:{type:C,responseTo:t.message.data.id,data:void 0}})),"send handshake syn ack":(0,f.O)({type:"request",data:{type:W}}),"set connection config":(0,l.a)({channelId:({event:t})=>((0,c.ZD)(t,"handshake.syn"),t.message.data.channelId),target:({event:t})=>((0,c.ZD)(t,"handshake.syn"),t.message.source||void 0),targetOrigin:({event:t})=>((0,c.ZD)(t,"handshake.syn"),t.message.origin)})},guards:{hasSource:({context:t})=>null!==t.target}}).createMachine({id:"node",context:({input:t})=>({buffer:[],channelId:null,connectTo:t.connectTo,domain:t.domain??z,handshakeBuffer:[],name:t.name,requests:[],target:void 0,targetOrigin:null}),invoke:{id:"listen for handshake syn",src:"listen",input:D({include:L,responseType:"handshake.syn"})},on:{"request.success":{actions:"remove request"},"request.failed":{actions:"remove request"},"request.aborted":{actions:"remove request"},"handshake.syn":{actions:"set connection config",target:".handshaking"}},initial:"idle",states:{idle:{entry:[{type:"emit status",params:{status:"idle"}}],on:{post:{actions:"buffer message"}}},handshaking:{guard:"hasSource",entry:["send handshake syn ack",{type:"emit status",params:{status:"handshaking"}}],invoke:[{id:"listen for handshake ack",src:"listen",input:D({include:V,count:1,responseType:"handshake.complete"}),onDone:"connected"},{id:"listen for disconnect",src:"listen",input:D({include:B,count:1,responseType:"disconnect"})},{id:"listen for messages",src:"listen",input:D({exclude:[B,L,V,Z,C]})}],on:{request:{actions:"create request"},post:{actions:"buffer message"},"message.received":{actions:"buffer incoming message"},disconnect:{target:"idle"}}},connected:{entry:["flush handshake buffer","flush buffer",{type:"emit status",params:{status:"connected"}}],invoke:[{id:"listen for messages",src:"listen",input:D({exclude:[B,L,V,Z,C]})},{id:"listen for heartbeat",src:"listen",input:D({include:Z,responseType:"heartbeat.received"})},{id:"listen for disconnect",src:"listen",input:D({include:B,count:1,responseType:"disconnect"})}],on:{request:{actions:"create request"},post:{actions:"post"},disconnect:{target:"idle"},"message.received":{actions:["send response","emit received message"]},"heartbeat.received":{actions:["send response","emit heartbeat"]}}}}})),K=(t,e=Y())=>{let n;let s=(0,f.A)(e,{input:t}),r=()=>{s.stop()};return{actor:s,fetch:(t,e,n)=>{let{responseTimeout:r=1e4,signal:i,suppressWarnings:o}=n||{},a=Promise.withResolvers();return s.send({type:"post",data:{type:t,data:e},resolvable:a,options:{responseTimeout:r,signal:i,suppressWarnings:o}}),a.promise},machine:e,on:(t,e)=>{let{unsubscribe:n}=s.on(t,t=>{e(t.message.data)});return n},onStatus:(t,e)=>{let{unsubscribe:r}=s.on("_status",s=>{n=s.status,e&&s.status!==e||t(s.status)});return n&&t(n),r},post:(t,e)=>{s.send({type:"post",data:{type:t,data:e}})},start:()=>(s.start(),r),stop:r}}}}]);