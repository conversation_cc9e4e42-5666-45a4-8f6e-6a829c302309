"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9623],{5478:function(n,t,e){e.d(t,{Ry:function(){return d}});var r=new WeakMap,o=new WeakMap,i={},a=0,c=function(n){return n&&(n.host||c(n.parentNode))},u=function(n,t,e,u){var d=(Array.isArray(n)?n:[n]).map(function(n){if(t.contains(n))return n;var e=c(n);return e&&t.contains(e)?e:(console.error("aria-hidden",n,"in not contained inside",t,". Doing nothing"),null)}).filter(function(n){return!!n});i[e]||(i[e]=new WeakMap);var f=i[e],l=[],s=new Set,p=new Set(d),h=function(n){!n||s.has(n)||(s.add(n),h(n.parentNode))};d.forEach(h);var v=function(n){!n||p.has(n)||Array.prototype.forEach.call(n.children,function(n){if(s.has(n))v(n);else try{var t=n.getAttribute(u),i=null!==t&&"false"!==t,a=(r.get(n)||0)+1,c=(f.get(n)||0)+1;r.set(n,a),f.set(n,c),l.push(n),1===a&&i&&o.set(n,!0),1===c&&n.setAttribute(e,"true"),i||n.setAttribute(u,"true")}catch(t){console.error("aria-hidden: cannot operate on ",n,t)}})};return v(t),s.clear(),a++,function(){l.forEach(function(n){var t=r.get(n)-1,i=f.get(n)-1;r.set(n,t),f.set(n,i),t||(o.has(n)||n.removeAttribute(u),o.delete(n)),i||n.removeAttribute(e)}),--a||(r=new WeakMap,r=new WeakMap,o=new WeakMap,i={})}},d=function(n,t,e){void 0===e&&(e="data-aria-hidden");var r=Array.from(Array.isArray(n)?n:[n]),o=t||("undefined"==typeof document?null:(Array.isArray(n)?n[0]:n).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),u(r,o,e,"aria-hidden")):function(){return null}}},85770:function(n,t,e){e.d(t,{Av:function(){return a},pF:function(){return r},xv:function(){return i},zi:function(){return o}});var r="right-scroll-bar-position",o="width-before-scroll-bar",i="with-scroll-bars-hidden",a="--removed-body-scroll-bar-size"},5517:function(n,t,e){e.d(t,{jp:function(){return v}});var r=e(2265),o=e(18704),i=e(85770),a={left:0,top:0,right:0,gap:0},c=function(n){return parseInt(n||"",10)||0},u=function(n){var t=window.getComputedStyle(document.body),e=t["padding"===n?"paddingLeft":"marginLeft"],r=t["padding"===n?"paddingTop":"marginTop"],o=t["padding"===n?"paddingRight":"marginRight"];return[c(e),c(r),c(o)]},d=function(n){if(void 0===n&&(n="margin"),"undefined"==typeof window)return a;var t=u(n),e=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-e+t[2]-t[0])}},f=(0,o.Ws)(),l="data-scroll-locked",s=function(n,t,e,r){var o=n.left,a=n.top,c=n.right,u=n.gap;return void 0===e&&(e="margin"),"\n  .".concat(i.xv," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(u,"px ").concat(r,";\n  }\n  body[").concat(l,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===e&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(c,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(r,";\n    "),"padding"===e&&"padding-right: ".concat(u,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(i.pF," {\n    right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(i.zi," {\n    margin-right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(i.pF," .").concat(i.pF," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(i.zi," .").concat(i.zi," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(l,"] {\n    ").concat(i.Av,": ").concat(u,"px;\n  }\n")},p=function(){var n=parseInt(document.body.getAttribute(l)||"0",10);return isFinite(n)?n:0},h=function(){r.useEffect(function(){return document.body.setAttribute(l,(p()+1).toString()),function(){var n=p()-1;n<=0?document.body.removeAttribute(l):document.body.setAttribute(l,n.toString())}},[])},v=function(n){var t=n.noRelative,e=n.noImportant,o=n.gapMode,i=void 0===o?"margin":o;h();var a=r.useMemo(function(){return d(i)},[i]);return r.createElement(f,{styles:s(a,!t,i,e?"":"!important")})}},18704:function(n,t,e){e.d(t,{Ws:function(){return c}});var r,o=e(2265),i=function(){var n=0,t=null;return{add:function(o){if(0==n&&(t=function(){if(!document)return null;var n=document.createElement("style");n.type="text/css";var t=r||e.nc;return t&&n.setAttribute("nonce",t),n}())){var i,a;(i=t).styleSheet?i.styleSheet.cssText=o:i.appendChild(document.createTextNode(o)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}n++},remove:function(){--n||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},a=function(){var n=i();return function(t,e){o.useEffect(function(){return n.add(t),function(){n.remove()}},[t&&e])}},c=function(){var n=a();return function(t){return n(t.styles,t.dynamic),null}}},17325:function(n,t,e){e.d(t,{q:function(){return c}});var r=e(2265);function o(n,t){return"function"==typeof n?n(t):n&&(n.current=t),n}var i="undefined"!=typeof window?r.useLayoutEffect:r.useEffect,a=new WeakMap;function c(n,t){var e,c,u,d=(e=t||null,c=function(t){return n.forEach(function(n){return o(n,t)})},(u=(0,r.useState)(function(){return{value:e,callback:c,facade:{get current(){return u.value},set current(value){var n=u.value;n!==value&&(u.value=value,u.callback(value,n))}}}})[0]).callback=c,u.facade);return i(function(){var t=a.get(d);if(t){var e=new Set(t),r=new Set(n),i=d.current;e.forEach(function(n){r.has(n)||o(n,null)}),r.forEach(function(n){e.has(n)||o(n,i)})}a.set(d,n)},[n]),d}},49085:function(n,t,e){e.d(t,{L:function(){return a}});var r=e(5853),o=e(2265),i=function(n){var t=n.sideCar,e=(0,r._T)(n,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var i=t.read();if(!i)throw Error("Sidecar medium not found");return o.createElement(i,(0,r.pi)({},e))};function a(n,t){return n.useMedium(t),i}i.isSideCarExport=!0},31412:function(n,t,e){e.d(t,{_:function(){return i}});var r=e(5853);function o(n){return n}function i(n){void 0===n&&(n={});var t,e,i,a=(void 0===t&&(t=o),e=[],i=!1,{read:function(){if(i)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return e.length?e[e.length-1]:null},useMedium:function(n){var r=t(n,i);return e.push(r),function(){e=e.filter(function(n){return n!==r})}},assignSyncMedium:function(n){for(i=!0;e.length;){var t=e;e=[],t.forEach(n)}e={push:function(t){return n(t)},filter:function(){return e}}},assignMedium:function(n){i=!0;var t=[];if(e.length){var r=e;e=[],r.forEach(n),t=e}var o=function(){var e=t;t=[],e.forEach(n)},a=function(){return Promise.resolve().then(o)};a(),e={push:function(n){t.push(n),a()},filter:function(n){return t=t.filter(n),e}}}});return a.options=(0,r.pi)({async:!0,ssr:!1},n),a}},99255:function(n,t,e){e.d(t,{M:function(){return u}});var r,o=e(2265),i=e(61188),a=(r||(r=e.t(o,2)))["useId".toString()]||(()=>void 0),c=0;function u(n){let[t,e]=o.useState(a());return(0,i.b)(()=>{n||e(n=>n??String(c++))},[n]),n||(t?`radix-${t}`:"")}}}]);