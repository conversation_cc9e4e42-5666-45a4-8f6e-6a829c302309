import { NextFetchEvent, NextMiddleware, NextRequest } from "next/server";
import { MiddlewareFactory } from "./types";
import createMiddleware from "next-intl/middleware";
import { defaultLocale, locales } from "@/lib/constanta/constant";
import { defineRouting } from "next-intl/routing";

export const withLocale: MiddlewareFactory = (next: NextMiddleware) => {
  return async (request: NextRequest, _next: NextFetchEvent) => {
    let nextResult = await next(request, _next);
    const hasOwnerUrl = request.url.includes("join");
    const cookieLocale = request.cookies.get("NEXT_LOCALE");
    const locale = cookieLocale?.value
      ? cookieLocale.value
      : hasOwnerUrl
        ? "id"
        : defaultLocale;
    request.cookies.set("NEXT_LOCALE", locale.toLowerCase());

    if (
      request.url.includes("api") ||
      request.url.includes("sound") ||
      request.url.includes("icon") ||
      request.url.includes("favicon.ico") ||
      request.url.includes("sitemap") ||
      request.url.includes("sitemap.xml") ||
      request.url.includes("robots.txt") ||
      request.url.includes("8574014f25c14ed691ad984a4f0d0ff6.txt")
    ) {
      return nextResult;
    }
    const handleI18Routing = createMiddleware(
      defineRouting({
        locales: locales,
        defaultLocale: locale,
        localePrefix: "always",
      })
    );

    return handleI18Routing(request);
  };
};
