{"component": {"dataTable": {"filterData": "<PERSON>lter gegevens", "viewTableOptions": "Bekijk", "toggleColumns": "<PERSON><PERSON><PERSON><PERSON> wisselen", "pagination": {"rowPerPage": "Rijen per pagina", "goToFIrstPage": "Ga naar eerste pagina", "goToPreviousPage": "Ga naar vorige pagina", "goToNextPage": "Ga naar volgende pagina", "goToLastPage": "Ga naar laatste pagina"}, "dateRangeFIlter": {"placeholder": "<PERSON>es een datum"}, "page": "<PERSON><PERSON><PERSON>"}, "cta": {"seeOriginal": "<PERSON><PERSON>", "seeTranslation": "<PERSON><PERSON> verta<PERSON>"}}, "conjuntion": {"or": "of", "and": "en", "of": "van", "for": "voor"}, "appName": "Property Plaza", "seeker": {"banner": {"seekers": {"discoverDreamHome": {"title": "<PERSON>d je lange<PERSON><PERSON>jn thui<PERSON>, bed<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> of land in Bali."}, "connectToPropertyOwner": {"title": "<PERSON><PERSON> in direct contact met geverifieerde eigenaren"}}}, "seekersLandingPage": {"seo": {"tabs": {"optionOne": {"title": "<PERSON><PERSON><PERSON><PERSON> goed te koop in Bali", "content": {"one": {"title": "Luxe Villa's", "description": "Premium wonen in Bali"}, "two": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Budgetvriendelijke huizen op Bali"}, "three": {"title": "Villa's met <PERSON><PERSON><PERSON><PERSON>", "description": "Prachtige villa's aan het strand"}, "four": {"title": "Bergvilla's", "description": "<PERSON>zen in rustige omgeving met bergzicht"}, "five": {"title": "<PERSON><PERSON> met Rijstveldenzicht", "description": "Schilderachtig plattelandsleven"}, "six": {"title": "Jungle Villa's", "description": "Villa's in een tropische omgeving"}, "seven": {"title": "Huisdier<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Huisdieren welkom hier"}, "eight": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> met <PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> met <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "nine": {"title": "<PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> je d<PERSON>"}, "ten": {"title": "Properties aan het Strand", "description": "Directe toegang tot het strand"}, "eleven": {"title": "Gemeubileer<PERSON>", "description": "<PERSON><PERSON><PERSON> om in te trekken"}, "twelve": {"title": "Villa's met <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON> met fijn<PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "thirteen": {"title": "<PERSON><PERSON> met <PERSON><PERSON><PERSON><PERSON>", "description": "Panoramisch uitzicht vanuit het dak"}, "fourteen": {"title": "Zakelijk Eigendom", "description": "Commerciële investeringsmogelijkheden"}, "fifteen": {"title": "Kleine Bedrijfsruimtes", "description": "Compacte commerciële units"}, "sixteen": {"title": "Middelgrote Kantoorruimte", "description": "Professionele bedrijfsomgeving"}, "seventeen": {"title": "Grote Commerciële Units", "description": "Uitgebreide bedrijfsruimtes"}, "eighteen": {"title": "Recent <PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON>euw gerenoveerde panden"}, "nineteen": {"title": "Onderhuur Toegestane Villa's", "description": "Flexibele verhuurmogelijkheden"}, "twenty": {"title": "<PERSON><PERSON> met Gemeentelijk Water", "description": "Betrouwbare watervoorziening"}, "twentyOne": {"title": "Nieuw Gebouwde Villa's", "description": "Frisse moderne constructie"}, "twentyTwo": {"title": "<PERSON><PERSON> met <PERSON><PERSON><PERSON>", "description": "Luxe badkamervoorzieningen"}, "twentyThree": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Veilige voertuigopslag"}, "twentyFour": {"title": "<PERSON><PERSON> met <PERSON><PERSON><PERSON><PERSON>", "description": "Privé zwembad inbegrepen"}}}, "optionTwo": {"title": "<PERSON><PERSON><PERSON><PERSON>", "content": {"one": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Budgetvriendelijke woonruimtes"}, "two": {"title": "Gemeubileer<PERSON>", "description": "<PERSON><PERSON><PERSON> om in te trekken"}, "three": {"title": "Huisdiervriendelijke Verhuur", "description": "Huisdieren welkom hier"}, "four": {"title": "Maandelijkse Huurhuizen", "description": "Flexibele maandelijkse verblijven"}, "five": {"title": "Eenjarig <PERSON>con<PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> w<PERSON>mogelijkheden"}, "six": {"title": "Lange<PERSON><PERSON>jn Villa", "description": "Villa voor langere tijd"}, "seven": {"title": "Jaarlijks huren van Appartementen", "description": "Jaarlijkse huurhuizen"}, "eight": {"title": "Betaalbare Studio", "description": "Compact budget wonen"}, "nine": {"title": "Appartementen met <PERSON><PERSON><PERSON><PERSON>", "description": "Groene rustige omgeving"}, "ten": {"title": "Dakterras Aanwezig", "description": "Hemelzicht wonen"}, "eleven": {"title": "Rijstveld Uitzicht", "description": "Schilderachtig landelijk uitzicht"}, "twelve": {"title": "Grote Familie Villa's", "description": "<PERSON><PERSON><PERSON>"}, "thirteen": {"title": "Budgetvriendelijke Verhuur", "description": "<PERSON>al<PERSON>e woonoploss<PERSON>"}, "fourteen": {"title": "<PERSON><PERSON><PERSON><PERSON> met <PERSON><PERSON><PERSON><PERSON>", "description": "Exclusieve zwembadtoegang"}, "fifteen": {"title": "Parkeren op prive terrein", "description": "Veilige parkeerplaats beschikbaar"}, "sixteen": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Flexibele huurvoorwaarden"}}}, "optionThree": {"title": "Digital Nomads", "content": {"one": {"title": "Ver<PERSON>ur wifi inbegrpen", "description": "Snel internet inbegrepen"}, "two": {"title": "Coworking Dichtbij", "description": "Werkruimtes beschikbaar"}, "three": {"title": "Thuiswerk Villa's", "description": "<PERSON><PERSON><PERSON> vanuit het paradijs"}, "four": {"title": "Digital Nomads Studio's", "description": "Verbonden woonruimtes"}, "five": {"title": "Maandelijkse Coworking Verhuur", "description": "Flexibele werkruimte oplossingen"}, "six": {"title": "Werkruimtes aan het strand", "description": "Oceaanzicht werkomgeving"}, "seven": {"title": "Ka<PERSON><PERSON><PERSON><PERSON><PERSON> omringd door groen", "description": "Door natuur geïnspireerde werkruimtes"}, "eight": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Persoonlijke werkruimtes"}, "nine": {"title": "Bali Dakterras Werkruimtes", "description": "Letterlijk Verhoogde werkervaring"}, "ten": {"title": "Nomade-Vriendelijke Appartementen", "description": "Digitale levensstijl"}, "eleven": {"title": "Co-<PERSON> <PERSON><PERSON><PERSON><PERSON>", "description": "Gemeenschapsgericht wonen"}, "twelve": {"title": "Minimalistische Werkruimtes", "description": "Minimalistich maakt <PERSON>ief"}, "thirteen": {"title": "Zakelijk Hub", "description": "Professionele werkomgeving"}, "fourteen": {"title": "Creatieve Studio Verhuur", "description": "Insp<PERSON><PERSON><PERSON> creatieve rui<PERSON>"}, "fifteen": {"title": "Nomade Verblijven voor Langetermijn", "description": "Uitgebreid nomade leven"}, "sixteen": {"title": "Rustige Werk Retraites", "description": "In stilte werken"}}}, "optionFour": {"title": "Off-plan ontwikkelingen", "content": {"one": {"title": "Toekomstige Aantwikkelingen", "description": "Aankomende eigendom projecten"}, "two": {"title": "Investerings Eigendommen", "description": "Strategische investeringsmogelijkheden"}, "three": {"title": "Pre-Constructie Villa's", "description": "Vroege investeringsopties"}, "four": {"title": "Off-Plan Luxe Villa's", "description": "Premium toekomstige woningen"}, "five": {"title": "Toekomstige Appartementen", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON> van morgen"}, "six": {"title": "Strand Uit-Plan", "description": "Toekomstige oceanfront huizen"}, "seven": {"title": "Bergzicht Aantwikkelingen", "description": "Aankomende berg retraites"}, "eight": {"title": "Rijstveld Bekijk Aantwikkelingen", "description": "Toekomstige plattelandshuizen"}, "nine": {"title": "Jungle Uit-Plan", "description": "Toekomstige tropische ontsnappingen"}, "ten": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> woon ontwikkelingen"}, "eleven": {"title": "<PERSON><PERSON>", "description": "Toekomstige tech huizen"}, "twelve": {"title": "Dakterras Aantwikkelingen", "description": "Hemelzicht eigendommen"}, "thirteen": {"title": "Uit-Plan Zakelijk", "description": "Toekomstige commerciële rui<PERSON>"}, "fourteen": {"title": "Duurzame Projecten", "description": "Milieuvriendelijke toekomstige huizen"}, "fifteen": {"title": "Hoogbouw Aantwikkelingen", "description": "Naarekomstig verticaal wonen"}, "sixteen": {"title": "Onderhuur Aantwikkelingen", "description": "Flexibele toekomstige eigendommen"}}}}, "title": "Ont<PERSON> alle soorten onroerend goed"}}, "subscription": {"upgradeSubscription": {"title": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>!", "description": "Ontgrendel extra foto's, zoom in op exacte locaties en chat direct met eigenaren. Vind je droomeigendom sneller en met gemak!"}, "benefit": {"weeklyEmailUpdate": "Wekelijkse Email Updates", "realtimeNotification": "Echttime Notificaties", "priceHistory": {"lastThreeMonth": "Laatste 3 Maanden", "fullPriceHistory": "Volledige Prijsgeschiedenis"}, "virtualTour": {"limitedAccess": "Beperkte Naaregang", "fullAccess": "Volledige Virtuele Naarur Naaregang"}, "neightborInsight": {"detail": "Gedetailleerde Buurt <PERSON>", "marketTrendAndPrediction": "Markttrends en Voorspellingen"}, "comparisonTool": {"compareUpToThreeProperty": "Vergelijk tot 3 Eigendommen", "compareUnlimitedProperty": "Vergelijk Onbeperkt Eigendommen"}, "expertConsultant": {"email": "Email Consultatie", "realtime": "Echttime Consultant"}, "fivePerWeeks": "5 nieuwe eigenaren/week", "fifteenPerWeeks": "15 Keer per Week"}, "downgrade": {"title": "Downgrade to {package} plan", "description": "<PERSON><PERSON><PERSON> review the changes that will occur when downgrading from {currentPakket} to {downgradePakketName}", "content": {"title": "Door canceling, you will lose access to these features:", "downgradeEffectiveDate": "Your downgrade will take effect on {effectedDatum}, one day before your next billing date of {nextFactureringDatum}", "optionOne": "Unlimited saved listings (will be limited to 20)", "optionTwo": "Echt - time notifications(will change to weekly email updates)", "optionThree": "Vol price history(will be limited to last 3 months)", "optionFour": "Vol virtual tour access (will be limited)", "optionFive": "Market trends and predictions (will be replaced with detailed neighborhood data)", "optionSix": "Unlimited property comparisons(will be limited to 3)", "optionSeven": "Vol access to off - market listings and transactions(will be limited)"}}, "cancel": {"content": {"optionOne": "Alle premium features of your current plan", "optionTwo": "Access to saved listings", "optionThree": "Notificaties and updates", "optionFour": "Prijs history and market data", "optionFive": "Virtueel tour access", "optionSix": "Eigendom comparison tools", "optionSeven": "Access to off-market listings and transactions"}, "title": " Annuleer Abonnement", "description": "<PERSON><PERSON><PERSON> review the consequences of cancelling your subscription"}, "signUp": {"title": "V<PERSON>r in Your Details", "description": "We’ll use this info to send you order updates."}}, "popup": {"followInstagram": {"title": "Let's connect", "description": "Volg us for Bali vibes, insider tips, and exclusive updates!"}}, "blog": {"sectionTitle": "Your ultimate guide to finding and investing in Bali properties", "content": {"optionOne": {"title": "The Future of Aanroerend Gaed Investment"}, "optionTwo": {"title": "5 Tips for Eerste-Tijd Eigendom Buyers"}, "optionThree": {"title": "Boven 10 Cities for Aanroerend Gaed Investment in 2024"}, "optionFour": {"title": "How to Prepare Your Thuis for Sale"}}, "moreArticles": {"title": "Meer articles"}, "author": {"defaultName": "Eigendom plaza"}, "badge": {"title": "Blog"}}, "transaction": {"dataTable": {"amount": "Bedrag", "transactionDate ": "Datum", "transactionId": "Transactie ID", "invoiceNumber ": "Factuur number", "nextBillingDate": "Volgde billing date", "plan": "Plan", "invoiceNumber": "Factuur number", "action": "Action", "noResult": "Nee transaction history"}, "seekers": {"dataTable": {"download": "Download"}}}, "misc": {"subscribeEmail": {"title": "Abonneer to our newsletter"}, "page": "Page", "faqNotFound": "Can't find a related question", "enableSoundNotification": {"title": "Aantvangen sound notifications.?", "description": "We suggest you to enable sound notification to get notified instantly"}, "today": "Vandaag", "messageTooShort": {"chatWithCs": {}}, "priceCantBeZero": "Pri<PERSON><PERSON> can't be 0", "successUpdateListingPrice": "Succesvol update listing price", "loading": "loading ...", "noResult": "no result", "noImageUploaded": "no image uploaded", "preparingBuyCredit": "preparing buy credit...", "foundError": "Oops we found an error", "failedUpdatingPrice": "Daar's an issue when updating price", "meter": "meter", "recentSearch": "Recent search", "hide": "Verbergen", "ascendingOrder": "A-Z", "descendingOrder": "Z-A", "verificationAlreadySent": "Verification code is sent to your whatsapp", "popularProperty": "Populair search", "popularSearch": "Populair search", "allType": "Alle type", "startFrom": "Begining from", "currency": "<PERSON><PERSON><PERSON><PERSON>", "language": "Language", "minimum": "Minimum", "home": "<PERSON><PERSON><PERSON>", "more": "more", "allProperty": "Alle property", "suggestion": "<PERSON><PERSON><PERSON> suggestion", "comparisonType": {"lessThan": "less than", "moreThan": "more than"}, "copy": {"successCopyContent": "Succesvol copy {content} to clipboard"}, "month": "{count, plural, one {month} other {months}}", "year": "{count, plural, one {year} other {years}}", "mapLocation": "Locatie", "userNotFound": "user not found", "ownerProperty": "Eigenaar property", "error": {"tooManyRequest": {"title": "Te many request", "description": "Could not request property, please try again later"}, "propertyNotFound": {"title": "Credit package not found", "description": "Could not find requested property"}, "packageNotFound": {"description": "Could not find requested credit package, please use another package"}, "seekers": {"propertyNotFound": {"title": "Oops, something is wrong"}}}, "subscibePropgram": {"searchPage": {"description": "Abonneer to do zoom more, see more photo from properties, and many more."}, "detailPage": {"description": "Unlock full access & start your journey to your dream property."}, "favorite": {"title": "Unlock Exclusive Access: Bewaar Your Favoriets!", "description": " Abonneer now to save and revisit your favorite properties anytime! "}}, "status": "Status:", "continueWith": "continue with", "primary": "Primary", "vat": "VAT", "expires": "Expires", "perMonth": "/month", "yourCurrentPlan": "Current plan", "upgradeTo": "Upgrade to {plan}", "downgradeTo": "Downgrade to {plan}", "free": "<PERSON><PERSON><PERSON>", "region": "Regio", "areas": "Oppervlaktes", "findYourPerfectProperty": "Vind your perfect property", "notAvailable": "<PERSON><PERSON>", "review": "Reviews", "priceIsNegotiable": "The price and/or duration is open to negotiation.", "yearWithCount": "{count, plural, one {year} other {years}}", "shareProperty": {"title": "Delen property"}, "chatCustomerSupport": "<PERSON><PERSON>", "readLess": "Lees less", "readMore": "Lees more", "any": "Alles", "unlimited": "Unlimited", "limitedAccess": "Limieted Access", "fullAccess": "Vol Access", "mostPopular": "Meeste popular", "step": "step {count}:", "representedBy": "Represented by", "view": "Bekijk", "importantNotice": "Importerenant notice", "middlemanProperty": "<PERSON><PERSON> property", "officiallyRepresenting": "<PERSON><PERSON><PERSON><PERSON> representing", "otherProperties": "Andere Eigendommen", "popularPropertyNearby": "Vergelijkbaar properties", "rentalTerms": "<PERSON><PERSON><PERSON><PERSON> terms", "propertyType": "Eigendom type:", "ofThreePicture": "of 3 Pictures", "notPossibleToFavorite": "Niet possible to Favoriet", "seeAllPhoto": "; see all photos", "seeExactLocation": "; see exact location", "saveProperty": "Bewaar up to {count} properties"}, "metadata": {"listingDetailPage": {"description": "This {propertyType} is for {listingType} is verified listing in Bali. Veilig and trusted real estate.", "title": "Geverifieerd Bali Eigendom", "keywords": "verified property listing in Bali, Bali real estate for sale, buy villa in Bali, trusted real estate agent Bali, Bali property investment, Bali property market"}, "searchPage": {"title": "Vind Your Eigendom in Bali | Eigendom Plaza", "description": "<PERSON><PERSON> the {category} in Bali, Blader listings, connect with owners, and find your perfect property today!", "multipleCategoryOrLocation": {"title": "Vinding {category} in {location}  | Eigendom Plaza"}}, "rootLayout": {"title": "Geverifieerd Bali Aanroerend Gaed & Verhuurs | Eigendom Plaza", "description": "Ontdekken verified Bali property listings for sale or long-term rent. Veilig, affordable, and scam-free options for families, expats, or honeymoons.", "keyword": "verified Bali property listings, Bali real estate, long-term rentals in Bali, affordable Bali villas, avoid Bali property scams, family vacation rentals Bali, honeymoon villas Bali"}, "subsriptionPlan": {"title": "Word lid Eigendom Plaza Plan | Krijg Exclusive Geverifieerd Eigendommen in Bali", "description": "Unlock exclusive benefits with Eigendom Plaza subscription. Krijg early access to verified properties, premium support, and scam-free listings in Bali."}, "favorite": {"title": "Opgeslagen Eigendommen | Your Favoriet Bali Villa's & Investments | Eigendom Plaza", "description": "Easily access your saved Bali properties, from luxury villas to investment opportunities. Revisit your favorites anytime and connect directly with property owners!"}, "messagesPage": {"title": "Chat with Eigendom Eigenaren & Bekijkkers | Eigendom Plaza Messenger", "description": "Connect instantly with property owners, buyers, and our support team. Negotiate deals, ask questions, and get real-time responses—no middleman involved!"}, "message": {"title": "Berichten | Chat with Eigendom Eigenaren & Buyers | Eigendom Plaza", "description": "Manage your conversations with property owners, buyers, and our support team. Ask questions, negotiate deals, and stay updated on your Bali real estate opportunities."}, "notificationSetting": {"title": "Manage Nietificatie Waarschuwingen | Eigendom Plaza", "description": "Customize your notification settings to stay updated on new messages, property updates, and exclusive offers."}, "profile": {"title": "Your Profiel | Manage Your Eigendom Plaza Account", "description": "Update your account details, track saved properties, and connect with property owners. Personalize your real estate experience with ease!"}, "security": {"title": "Account Security Instellingen | Protect Your Eigendom Plaza Account", "description": "Beveilig your account with password updates, two-factor authentication (2FA), and advanced login settings. Keep your Eigendom Plaza profile safe from unauthorized access."}, "termsOfUse": {"title": "Voorwaarden of Use | Eigendom Plaza", "description": "<PERSON>s the Voorwaarden of Use for Eigendom Plaza. Onderstand the guidelines, user responsibilities, and legal agreements when using our platform for property listings, real estate searches, and transactions."}, "privacyPolicy": {"title": "Privacy Beleid | Eigendom Plaza", "description": "Learn how Eigendom Plaza collects, uses, and protects your personal information. Lees our Privacy Beleid to understand your rights and how we ensure data security for property seekers and owners."}, "userDataDeletion": {"title": "Gebruiker Data Deletion | Eigendom Plaza", "description": "Eigendom Plaza respects your right to control your personal data. This document outlines the process for requesting the deletion of your user data from our systems."}, "aboutUs": {"hero": {"title": "Making Eigendom Zoek Eenvoudig and Transparent", "description": "Eigendom Plaza is dedicated to connecting property seekers with their ideal homes and spaces through an intuitive platform that prioritizes transparency, quality, and user experience.", "browseProperties": "<PERSON><PERSON>", "contactUs": "Neem contact op"}, "mission": {"title": "Our Mission & Waardes", "description": "We're on a mission to transform the property search experience by creating a platform that puts users first and makes finding your next property a delightful journey.", "values": {"global": {"title": "Global Perspective", "description": "We bring international standards to local markets, helping you find properties that meet global quality benchmarks."}, "trust": {"title": "Built on Trust", "description": "We verify listings and owners to ensure you can make decisions with confidence and peace of mind."}, "quality": {"title": "Kwaliteit Eerste", "description": "We curate properties that meet our high standards, saving you time and ensuring satisfaction."}, "community": {"title": "Community Focused", "description": "We build connections between property seekers and owners, creating a supportive ecosystem."}}}, "story": {"title": "Our Story", "paragraph1": "Eigendom Plaza began in 2018 when our founders experienced firsthand the challenges of finding quality properties in unfamiliar markets. They envisioned a platform that would make property search transparent, efficient, and enjoyable.", "paragraph2": "Begining with just a handful of listings in Amsterdam, we've grown to become a trusted platform across multiple countries, helping thousands of people find their perfect property match every month.", "paragraph3": "<PERSON><PERSON><PERSON>, we continue to innovate and expand, guided by our commitment to quality, transparency, and exceptional user experience."}, "team": {"title": "Meet Our Team", "description": "The passionate individuals behind Eigendom Plaza are dedicated to revolutionizing how people find and secure properties.", "roles": {"ceo": "Chief Executive <PERSON><PERSON><PERSON><PERSON>", "cto": "Chief <PERSON> Kanto<PERSON><PERSON>", "marketing": "Head of Marketing", "operations": "Operations Director", "customerSuccess": "<PERSON>lant Succesvol Manager", "propertySpecialist": "Senior Eigendom Specialist"}}, "cta": {"title": "<PERSON><PERSON> to Vind Your Perfect Eigendom?", "description": "Begin your property search journey with Eigendom Plaza today and discover why thousands of users trust us to find their ideal spaces.", "findProperty": "Vind a Eigendom", "getInTouch": "Krijg in Contact"}, "title": "Over Eigendom Plaza | Vertrouwd Bali Eigendom Experts & Geverifieerd Listings", "description": "Learn about Eigendom Plaza, the trusted Bali property platform with verified listings. Meet our experienced team committed to safe and secure property transactions in Bali.", "keyword": "Over Eigendom Plaza, Bali property agency, Bali real estate team, Vertrouwd property platform Bali, Geverifieerd property listings Bali, Buy property in Bali safely"}}, "listing": {"detail": {"images": {"showAllImages": "Toon all images", "title": "Images"}, "contactCount": "{count} mensen hebben al contact opgenomen met de eigenaar", "popularFacility": {"title": "Key Features"}, "mainFacilities": {"title": "Amenities and Specifications", "bathroom": {"content": "{count} <PERSON><PERSON><PERSON>(s)"}, "bedroom": "{count} <PERSON><PERSON><PERSON><PERSON><PERSON>", "shellAndCore": "Shell and core only", "gardenSize": "Tuin size", "landSize": "Grond size", "view": "Bekijk", "yearsOfBuild": "Build in", "furnishing": {"furnished": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "living": {"privateLiving": "Sluitend living", "openLiving": "Openen living", "sharedLiving": "Delend living"}, "parking": {"publicParking": "Openenbaar parking", "privateParking": "Privé parking"}, "pool": {"available": "Zwembad available"}}, "rentalPricingIncluding": {"title": "What's Inbegrepen", "villageFeeIncluded": "Dorpstakss", "wifi": "Wifi", "gardenFeeIncluded": "Garbage fees", "waterFeeIncluded": "Drinking Water"}}, "misc": {"minimumRent": "Minimum rent", "propertyNotFound": "We can't find property that you looking for.", "searchNoResultCount": "<PERSON><PERSON> exact match.", "searchResultCount": "{count, plural, one {# property} other {# properties}}", "favoritePropertyNotFound": "You haven't saved any properties yet! Bezoek the properties page and click the heart icon to add your favorites.", "availabelAt": "<PERSON><PERSON><PERSON><PERSON><PERSON> at", "maximumRent": "Maximum rent"}, "pricing": {"suffix": {"leasehold": "for {count} {durationType}"}}, "category": {"villa": "Villa's", "apartment": "Appartments", "guestHouse": "<PERSON><PERSON>", "commercial": "Commer<PERSON><PERSON>", "cafeAndRestaurent": "Cafe or Restaurant", "office": "<PERSON><PERSON><PERSON>", "shops": "Shops", "shellAndCore": "Shell & core", "land": "Grond", "title": "Categories", "homestay": "T<PERSON>ss<PERSON><PERSON>", "rooms": "<PERSON><PERSON>", "badge": {"title": "Eigendom category"}}, "feature": {"additionalFeature": {"buildingSize": "Gebouw size", "bedroom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bathroom": "Badkamer", "land": "Grond size", "plumbing": "Plumbing", "airCondition": "Air Conditioning (AC)", "balcony": "Balcony", "bathub": "<PERSON><PERSON>", "constructionNearby": "Construction nearby", "garden": "<PERSON><PERSON>", "gazebo": "Gazebo", "petAllowed": "Pet(s) allowed", "recentlyRenovated": "Recently renovated", "rooftopTerrace": "Rooftop terrace", "subleaseAllowed": "Sublease allowed", "terrace": "Terrace", "municipalWaterwork": "Municipal waterwork"}}, "homepage": {"howItWorks": "How it works", "howItWorksDescription": "Easily find your ideal property and connect directly with the owner—no middleman, no hassle."}, "filter": {"elictricity": {"optionOne": {"title": "<PERSON><PERSON>"}, "optionTwo": {"title": "< 5kW"}, "optionThree": {"title": "5 kW - 10 kW"}, "optionFour": {"title": "10 kW - 20 kW"}, "optionFive": {"title": "20 kW >"}}, "others": {"elictricity": {"title": "Elektriciteit (KW)"}, "minimumContract": {"title": "Minimum contract"}, "title": "Anderes", "typeContract": {"title": "Type contract"}, "yearsOfBuild": {"title": "Jaar Built"}, "parking": {"title": "Parkeren status"}, "pool": {"title": "Zwembad availability"}, "closeOrOpenLiving": {"title": "Type of living"}, "furnished": {"title": "Meubilering status"}}, "minimumContract": {"optionOne": {"title": "<PERSON><PERSON>"}, "optionTwo": {"title": "< 1 Jaar"}, "optionThree": {"title": "1 > 3 Jaars"}, "optionFour": {"title": "3 > 5 Jaars"}, "optionFive": {"title": "> 5 years"}}, "priceRange": {"title": "Prijs range", "description": "<PERSON><PERSON><PERSON><PERSON> of the Eigendom"}, "typeProperty": {"optionThree": {"subOption": {"optionOne": {"title": "<PERSON>"}, "description": "Commercieel space with building size {comparisonType} {count}", "optionTwo": {"title": "Middel"}, "optionThree": {"title": "Groot"}}, "title": "Zakelijk/Work", "description": "<PERSON><PERSON> and <PERSON><PERSON><PERSON>"}, "optionTwo": {"subOption": {"optionOne": {"title": "Villa's"}, "optionTwo": {"title": "Appartment"}, "optionThree": {"title": "<PERSON><PERSON>"}}, "title": "Place to live", "description": "Your Ideal Thuis Awaits"}, "optionOne": {"title": "Alles"}, "optionFour": {"title": "Grond"}, "title": "What are you looking for?", "description": "Vind What You Need"}, "view": {"optionOne": {"title": "Alles"}, "optionTwo": {"title": "Mountain"}, "optionThree": {"title": "Ocean"}, "optionFour": {"title": "Ricefield"}, "optionFive": {"title": "Jungle"}}, "typeView": {"title": "Bekijk"}, "sortBy": {"higherPrice": "<PERSON><PERSON><PERSON><PERSON> (Hoogest Eerste)", "lowerPrice": "Prijs (Laagest Eerste)", "newestFirst": "<PERSON><PERSON> (Nieuwste Eerste)", "oldest": "<PERSON><PERSON> (Oudste Eerste)", "smallest": "Eigendom Grootte (Kleinste Eerste)", "largest": "Eigendom Grootte (Grootste Eerste)", "mostViewed": "Populariteit (Meeste Bekeken)", "mostFavorited": "Populariteit (Meeste Favoriet)", "natureView": "Bekijk/Scenery (Nature Bekijks Eerste)"}, "category": {"all": {"title": "Alle properties"}}, "yearsOfBuild": {"optionAny": {"title": "Alles"}, "optionOne": {"title": "< 2015"}, "optionTwo": {"title": "2016 - 2019"}, "optionThree": {"title": "2020 - 2024"}, "optionFour": {"title": "Current year"}}, "othersFeature": {"title": "Andere features"}, "propertySize": {"title": "Eigendom size", "landSize": {"title": "Grond size"}, "buildingSize": {"title": "Gebouw size"}, "gardenSize": {"title": "Tuin size"}}}, "featureFilter": {"optionOne": {"title": "Bathtub"}, "optionTwo": {"title": "Air Conditioning (AC)"}, "optionThree": {"title": "Pet(s) allowed"}, "optionFour": {"title": "Tuin backyard"}, "optionFive": {"title": "Gazebo"}, "optionSix": {"title": "Rooftop terrace"}, "optionSeven": {"title": "Balcony"}, "optionEight": {"title": "Terrance"}, "title": "Features"}, "locationFilter": {"optionOne": {"title": "Main street"}, "optionTwo": {"title": "Sluiten to beach"}, "title": "Locatie"}, "propertyCondition": {"optionOne": {"title": "Sublease allowed"}, "optionTwo": {"title": "Construction nearby"}, "optionThree": {"title": "Municipal waterwork"}, "optionFour": {"title": "Plumbing"}, "optionFive": {"title": "Recently renovated"}, "title": "Eigendom condition"}, "rentalIncludeFilter": {"optionOne": {"title": "Wi-fi"}, "optionTwo": {"title": "Garbage <PERSON>"}, "optionThreetitle": "Water", "optionFour": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "title": "<PERSON><PERSON><PERSON><PERSON> including"}, "popularProperty": {"title": "Populair properties"}, "newestProperty": {"title": "Nieuwste properties"}, "newestCommercialProperty": {"title": "Nieuwste commercial properties"}, "featuredProperty": {"title": "Uitgelicht properties"}, "search": {"placeholder": "Vind specific location", "title": "Zoek property"}, "viewAllProperties": "Bekijk Alle Eigendommen"}, "cta": {"login": "Inloggen", "createAccount": "Sign Omhoog now", "continueWith": "Ga door with {field}", "edit": "Bewerk", "updatePassword": "Update", "verify": "Verify", "seeTranslation": "Bekijk translation", "seeOriginal": "Bekijk original", "filter": "Filter", "chatCustomerService": "Need assistance? Chat with our Klant Ondersteuning team now!", "requestCreateListing": "Maken listing request", "createListing": "Maken listing", "contactAccountmanager": "Contact the Account Manager", "disableListing": "Uitgeschakeld listing", "finishReview": "Voltooien review", "saveAsDraft": "<PERSON><PERSON><PERSON> as draft", "addReview": "Toevoegen review", "save": "<PERSON><PERSON><PERSON>", "back": "Terug", "joinWaitngList": "Word lid the waiting list", "logout": "Log uit", "cancel": "<PERSON><PERSON><PERSON>", "signUpNow": "Registreer now", "sendResetPassword": "Verzoek wachtwoord wijziging", "checkOut": "Checkout", "contactToWhatsapp": "Neem contact met ons op via WhatsApp", "createYourAccount": "Maken your account", "subscribe": "Abonneer now!", "signUp": "Registreer", "goBack": "Ga back", "search": "<PERSON><PERSON>", "removeSearch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disable": "Disable", "enable": "Enable", "sendRequest": "Verstuur request", "closeChat": "Sluiten chat", "sellPropertyFast": "Snel Sell or Rent Your Eigendom", "next": "Volgde", "activateListing": "Activeer listing", "activate": "Activate", "changeStatus": "Verandering status", "changePrice": "Verandering price", "pay": "Pay", "topUp": "bij<PERSON>n", "viewAllProperty": "Bekijk all property", "readMore": "Lees more ...", "changePassword": "Verandering password", "filters": "Filters", "clearAll": "Wis all", "previous": "Vorige", "maps": "Maps", "list": "List", "contactOwner": "Contact the Eigenaar", "share": "<PERSON><PERSON>", "readLess": "Lees less", "copyLink": "Kopiëren link", "close": "Sluiten", "findOtherProperty": "Bekijk other properties", "findOtherPackage": "Bekijk other packages", "followUsOnInstagram": "Volg us on Instagram", "changePhoto": "Verandering Photo", "saveChanges": "<PERSON><PERSON><PERSON>", "change": "<PERSON><PERSON><PERSON>", "confirm": "Bevestig", "viewAll": "Bekijk all", "signOutAll": "Log uit op alle apparaten", "signOutDevice": "Log uit apparaat", "update": "Update", "addPaymentMethod": "Toevoegen payment method", "requestChangePassword": "Request Verandering password", "showAllProperty": "Toon all properties", "saved": "Opgeslagen", "get2FACode": "Genereer 2FA code", "downgrade": "Downgrade", "cancelSubscription": "Annuleer subscription", "contactMiddleman": "Neem contact op met vertegenwoordiger", "saveUpTo": "<PERSON><PERSON><PERSON>", "remove": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "setPrimary": "Set as primary", "editBilling": "Bewerk", "continueCheckout": "Doorgaan to Betaling", "detail": "Detail"}, "conjuntion": {"or": "or", "and": "and", "of": "of", "for": "for"}, "propertyDetail": {"totalBathroom": {"title": "Totaal bathroom"}, "totalBedroom": {"title": "Totaal bedroom"}, "buildingSize": {"title": "Gebouw size"}, "cascoStatus": {"title": "Casco status"}, "gardenSize": {"title": "Tuin size"}, "landSize": {"title": "Grond size"}, "viewOfProperty": {"title": "Bekijk of property"}, "yearsOfBuild": {"title": "Jaars of build"}, "villageFee": {"title": "Dorpstaks"}, "wifi": {"title": "Wifi"}, "garbageFee": {"title": "Garbage fee"}, "waterFee": {"title": "Water fee"}, "electricity": {"title": "Elektriciteit"}, "furnishingStatus": {"title": "Meubilering status"}, "livingStatus": {"title": "Type wonen"}, "parking": {"title": "Parkeren status"}}, "detail": {"rentalPricingIncluding": {"electricity": "Kwh Elektriciteit"}, "mainFacilities": {"furnishing": {"unfurnished": "Ongemeubileerd"}}}, "form": {"login": {"title": "Inloggen", "description": "Ga door to your property"}, "signUp": {"description": "Fill in your details to begin your journey!", "title": "<PERSON>n Account"}, "utility": {"fieldRequired": "<PERSON><PERSON><PERSON> provide {field}", "invalidFormat": "Ongeldig {field} format", "eitherFieldRequired": "Either {fields} must be provided", "enterValidField": "<PERSON><PERSON><PERSON> enter a valid {field}", "forgotField": "Vergeten your {field}?", "resetField": "Resetten {field}", "minimumLength": "{field} minimum length is {length}", "fieldNotMatch": "{field} komen niet overeen", "optional": "(optional)", "coordinateRequired": "Voer in latitude and longitude, separated by a comma", "wrongFormatCoordinate": "Fout format of coordinates, please input latitude and longitude separated by a comma", "maximumLength": "{field} exceeded the maximum length of {length} characters ", "passwordWeak": "Wachtwoord is too weak", "password": {"minimumLength": "Minimaal 8 karakters", "numberRequired": "Tenminste one number", "notCommonWord": "Niet common word", "specialCharacter": "Tenminste one symbol", "uppercaseRequired": "Tenminste one uppercase", "lowercaseRequired": "Tenminste one lowercase"}}, "field": {"email": "Email", "phoneNumber": "Telefoon number", "password": "Wachtwoord", "confirmPassword": "Bevestig password", "username": "Gebruikername", "firstName": "Eerste name", "lastName": "Laatste name", "address": "<PERSON><PERSON>", "aboutYou": "Over you", "language": "Language", "message": "Bericht", "typeProperty": "Eigendom Type", "yearsOfBuild": "Jaar of onstruction", "propertyView": "Bekijk Type", "landSize": "Grond Oppervlakte", "buildingSize": "Gebouw Oppervlakte", "gardenSize": "Tuin/Outdoor Ruimte", "typeBedroom": "# of Slaapkamers", "totalBathroom": "# of Badkamer", "wifi": "WiFi (Mb/s)", "cleaning": "total Schoonmaak", "cleaningTime": "Sc<PERSON>onmaak time", "review": "Review", "postalCode": "Postcode", "coordinate": "Coördinaten", "propertyLocation": "Located", "roadSize": "Entrance road size", "addressLocation": "Eigendom address", "distric": "District", "city": "Stad/Regency", "province": "<PERSON><PERSON><PERSON>", "typeContract": "Contract type", "minimumDuration": "Minimale duur", "minimumPrice": "<PERSON><PERSON><PERSON><PERSON>", "maximumDuration": "Maximale duur", "elictricity": "Elektriciteit (KW/h)", "furnishingStatus": "Me<PERSON>lering", "parking": "<PERSON><PERSON>", "poolAvailability": "Zwembad", "livingStatus": "<PERSON><PERSON>", "role": "Role", "fullName": "Vol name", "name": "Name", "otp": "OTP", "statusListing": "Listing status", "excerpt": "Excerpt", "title": "Title", "banjar": "Banjar", "phoneOrWhatsappNumber": "Telefoon or Whatsapp number", "villaAddress": "Villa address", "preferredDate": "Preferred date", "tier": "Tier"}, "title": {"login": "Inloggen", "signUp": "Registreer", "staffLogin": "Inloggen Staff", "resetPassword": "Resetten password", "enterOtpCode": "Verify Je e-mail to Unlock Bali's <PERSON><PERSON>"}, "label": {"emailOrPhone": "Email or Telefoon number", "password": "Wachtwoord", "username": "Gebruikername", "confirmPassword": "Bevestig password", "firstName": "Eerste name", "lastName": "Laatste name", "aboutYou": "Over you", "email": "Email", "address": "Thuis address", "phoneNumber": "Telefoon number", "language": "<PERSON>", "PropertyType": "Eigendom type", "cascoProperty": "Casco eigendom", "yearsOfBuild": "Jaar Built", "propertyView": "Eigendom Bekijk", "landSize": "Grond Oppervlakte", "buildingSize": "Gebouw Oppervlakte", "gardenSize": "Tuin/Outdoor Ruimte", "totalBedroom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "totalBathroom": "<PERSON><PERSON><PERSON>(s)", "wifi": "WiFi", "cleaning": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "coordinate": "Coördinaten", "propertyLocation": "Eigendom Locatie", "roadSize": "Entrance <PERSON><PERSON>", "addressLocation": "<PERSON><PERSON>", "postalCode": "Postcode", "distric": "District", "city": "Stad/Regency", "province": "<PERSON><PERSON><PERSON>", "typeContract": "Contract type", "availableContract": "<PERSON><PERSON><PERSON><PERSON><PERSON> from", "minimumDuration": "Minimale duur", "minimumPrice": "Asking <PERSON><PERSON><PERSON>", "maximumDuration": "Maximale duur", "elictricity": "Elektriciteit", "furnishingStatus": "Me<PERSON>lering", "parkingStatus": "<PERSON><PERSON>", "poolAvailability": "Zwembad", "livingStatus": "<PERSON><PERSON>", "fullName": "Vol name", "name": "Name", "requestCreateListing": "Vertel ons iets over je eigendom", "propertyDescription": "Eigendom description", "pricing": "Prijzen", "type": "Type", "amount": "Bedrag", "price": "<PERSON><PERSON><PERSON><PERSON>", "date": "Aankoopda<PERSON> ", "requestUpdateListing": "Vertel ons wat je wilt wijzigen", "promoCode": "Promocode", "status": "Listing status", "excerpt": "Excerpt", "propertyTitle": "Title", "maximum": "Maximum", "userId": "Gebruiker ID", "banjar": "Banjar"}, "placeholder": {"basePlaceholder": "<PERSON><PERSON><PERSON> je in {field}", "aboutYou": "Tell us the things we should know about you", "pickLanguage": "Selecteer a language", "baseSelectPlaceholder": "Selecteer {field}", "baseNumberPlaceholder": "Stel je in {field}", "availableContract": "Vroegst beschikbare contractdatum", "searchQuestion": "Zoek FAQ", "example": {"requestHelpToCs": "Can I also contact you via Whatssapp?", "email": "<EMAIL>", "phoneNumber": "+91 123 456 789", "firtName": "<PERSON>", "lastName": "<PERSON><PERSON>", "requestCreateListing": "Villa with 4 bedrooms, 3 bathrooms, and a private pool located in Canggu, Slechtung", "requestUpdateListing": "Ik wil het aantal badkamers wijzigen"}, "enterPromoCode": "<PERSON><PERSON><PERSON> in promo code", "seekersFindPropertyLocation": "Zoek for destination"}, "description": {"resetPassword": "we sturen je wachtwoord verzoek naar je e-mail", "passwordRequirement": "Wachtwoord should have minimum 8 Characters, 1 Hoofdletter, 1 lowecase, 1 number and 1 Symbol", "userId": "This is your unique identifier in our system"}}, "auth": {"login": {"subtitle": "Ga door to find your property"}, "register": {"subtitle": "Fill in your details to begin your journey!"}, "subtitle": "Word lid our property seekers program", "resetPassword": {"subtitle": "Resetten your account password "}, "createAccount": "Mis niet have an account?", "alreadyHaveAccount": "Already have account?", "otp": {"content": {"title": "We've sent a magic link to your inbox. Bevestig your email to:", "cantFindEmail": "Can't find the email? Check your spam folder or resend the code below."}, "item": {"one": "Access exclusive Bali property listings", "two": "Bewaar your favorite villas & lands", "three": "Krijg personalized alerts for new opportunities"}}}, "error": {"failedLogin": {"title": "Oops we have issue when login account"}, "signUp": {"title": "Oops, failed to sign up"}, "messageTooShort": {"title": "Bericht is too short", "description": "<PERSON><PERSON><PERSON> give more context to the message"}, "failedSendMessage": {"title": "Oops, there's error when sending the message"}, "updateNotification": {"title": "Failed to update {field}"}, "requestForgetPassword": {"title": "Oops, failed request forget password"}, "Subscribing": "Oops, there's something error when subscribing to a plan", "failedEnablingTwoFA": "Oops, Failed implementing Two Factor Authentication", "failedUpdatePayment": "Unable to Update Betaling Method"}, "success": {"sendVerification": {"title": "Verification code successfully sent to "}, "copyUrl": {"title": "Succesvol copy url property", "description": "You can share this to anyone"}, "sendMessageToCs": {"title": "<PERSON><PERSON><PERSON> successfully sent to our team", "description": "We will reach out to you shortly."}, "sendMessageToOwner": {"title": "<PERSON><PERSON><PERSON> successfully sent to the property owner", "description": "<PERSON><PERSON><PERSON> wait for the owner's response."}, "updateNotification": {"title": "Succesvolfully updated {field}"}, "requestForgetPassword": {"title": "Wachtwoord reset request successful", "description": "<PERSON><PERSON><PERSON> check your email: {email}"}, "activateTotp": "Two-Factor Authentication successfully activated", "upgradeSubscription": "Abonnement successfully upgraded", "cancelSubscription": "Abonnement successfully cancelled", "downGrade": "Plan successfully downgraded", "updatePayment": "Succesvol update payment method"}, "message": {"otpRequest": {"failedToast": {"title": "Oops, Failed request OTP"}}, "utils": {"messageTooLongError": {}}, "waitingResponse": "Waiting for response", "notMessageList": "Begin chatting with property owners today! Bezoek property details and click 'Chat with <PERSON><PERSON><PERSON><PERSON>' to begin.", "searchChat": "Zoek messages", "chatCs": {"title": "<PERSON><PERSON><PERSON> you need help or a question?", "description": "Let us know and we will answer reply shortly."}, "chatOwner": {"title": "Begin a chat with the owner", "description": "Ask anything about the property."}, "category": {"customerSupport": "CS", "seekers": "Eigendom seekers", "accountManager": "AM", "owner": "<PERSON><PERSON><PERSON><PERSON>"}, "chatEnded": "Chat ended", "textChatEnded": "Dank je wel for reaching out to Eigendom-plaza Ondersteuning. We appreciate your time, and we wish you success in all your endeavors. 🙏 Eigendom-plaza Ondersteuning Team", "subscriptionSignUp": {"failedToast": {"title": "Oops, Sommigething Went Fout"}}, "newMessage": "new message from"}, "user": {"account": "Account"}, "otp": {"resendVerificationCode": "Resend Verification Link"}, "HowItWorks": {"optionOne": {"title": "Choose your membership", "description": "Selecteer a plan to access exclusive property listing details and connect with owners."}, "optionTwo": {"title": "Vind your ideal property", "description": "Blader detailed listings and discover the perfect home or investment."}, "optionThree": {"title": "Chat and negotiate", "description": "Directly connect with verified property owners to discuss terms and make a deal."}, "optionFour": {"title": "Move in and enjoy", "description": "Definitiefize your agreement, settle in, and start your new journey in Bali."}}, "footer": {"tabsOne": {"content": {"optionOne": {"title": "Villa's"}, "optionTwo": {"title": "Appartement"}, "optionThree": {"title": "Guesthouse"}, "optionFour": {"title": "T<PERSON>ss<PERSON><PERSON>"}, "optionFive": {"title": "Shops"}, "optionSix": {"title": "<PERSON><PERSON><PERSON>"}, "optionSeven": {"title": "Restaurant"}}}, "tabsTwo": {"content": {"optionOne": {"title": "Krijg your property listed"}, "optionTwo": {"title": "FAQ for Eigenaren"}}}, "tabsThree": {"content": {"optionOne": {"title": "How to get memberships"}, "optionTwo": {"title": "Commercieel properties"}, "optionThree": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> to live"}, "optionFour": {}}}, "tabsFour": {"content": {"optionOne": {"title": "Frequently Asked Questions (FAQ) "}, "optionTwo": {"title": "<PERSON> <PERSON><PERSON>"}, "optionThree": {"title": "Over us"}, "optionFour": {"title": "Voorwaarden of use"}, "optionFive": {"title": "Privacy Beleid"}}}, "slogan": "Connecting People to Eigendommen", "exploreProperties": {"title": "Verkennen properties"}, "properyOwner": {"title": "Eigenaar of the Eigendom"}, "FaQ": {"title": "FAQ"}, "help": {"title": "<PERSON><PERSON><PERSON>"}, "copyright": "2025 Eigendom Plaza. Alle Rechten Gereserveerd."}, "setting": {"subscriptionStatus": {"billing": {"billingHistory": {"title": "Facturering Hoistory", "description": "Bekijk and download your billing history"}, "title": "Facturering", "description": "Manage your payment methods and view your billing history", "paymentMethod": {"title": "Betaling Methods", "description": "Manage your payment methods and billing information"}, "billingInformation": {"title": "Facturering Information"}}, "subscription": {"title": "Manage Abonnement", "features": {"optionOne": "Contact The Eigenaar", "optionTwo": "Photos", "optionThree": "Map Locatie", "optionFour": "Advance Filtering", "optionFive": "Opgeslagen Listings", "optionSix": "Listing Updates", "optionSeven": "<PERSON><PERSON><PERSON><PERSON>", "optionEight": "<PERSON><PERSON><PERSON><PERSON> (Map and Video)", "optionNine": "Neighborhood Insights", "optionTen": "Comparison Tel", "optionEleven": "Expert Consultation", "optionTwelve": "Uit Market Listings", "optionThirdteen": "Transacties", "optionFourteen": "Favoriet Eigendommen", "optionFifteen": "Map location", "optionSixteen": "Chat with owner"}, "monthly": "Ma<PERSON><PERSON><PERSON><PERSON>", "quarterly": "Quarterly", "description": "Review and manage your subscription plan"}, "title": "Abonnement status"}, "favorites": {"savedItems": {"title": "Opgeslagen Items", "description": "Bekijk and manage your saved properties"}, "title": "Favoriets", "favoriteSellers": {"title": "<PERSON><PERSON><PERSON><PERSON>"}}, "notification": {"soundNotification": {"title": "Sound Notificaties", "description": "Play a sound when you receive a new notification."}, "emailForNewMessages": {"title": "Email Notificaties for <PERSON>euw Be<PERSON>ten", "description": "K<PERSON>jg email notifications when you receive new messages."}, "emailForNewProperties": {"title": "Email Notificaties for Nieuw Eigendommen", "description": "Aantvangen emails when new properties match your saved searches."}, "priceChangeAlert": {"title": "P<PERSON>j<PERSON> Waarschuwingen", "description": "<PERSON><PERSON><PERSON><PERSON> notified when the price changes for properties you saved."}, "newsletter": {"title": "Nieuwsletter", "description": "Aantvangen our monthly newsletter with market updates and real estate tips."}, "specialOffer": {"title": "Special Uiters", "description": "Krijg notified about exclusive deals and limited-time promotions."}, "surveys": {"title": "Surveys", "description": "Nemen part in surveys and help us improve our services."}}, "profile": {"notification": {"title": "Notificaties"}, "personalInfo": {"title": "Personal Info", "changeEmail": {"title": "Verandering email", "description": "<PERSON><PERSON><PERSON> in your new email address below. We'll send a verification email to confirm the change."}, "changePhone": {"title": "Verandering phone number", "description": "<PERSON><PERSON><PERSON> in your new phone number below. We'll send a verification SMS to confirm the change."}}, "security": {"loginHistory": {"title": "Inloggen Hoistory", "description": "Verandering your password to keep your account secure", "table": {"date": "Datum", "device": "<PERSON><PERSON>", "location": "Locatie", "status": "Status"}}, "connectedDevice": {"title": "Two-Factor Authentication", "description": "Toevoegen an extra layer of security to your account", "lastActive": "Laatste active", "currentDevice": "Current device"}, "twoFA": {"title": "Two-Factor Authentication", "description": "Toevoegen an extra layer of security to your account", "useAuthenticatorApp": "Use an Aunteticator App to enable 2FA", "scanQRCodeWithAuthenticatorApp": "Scan the QR code with your authenticator app.", "enterAuthenticatorCode": "Voer in the code below from your app."}, "title": "Inloggen and Security", "password": {"lastChanged": "Laatste changed"}}, "title": "<PERSON><PERSON>", "notifications": {"title": "Notificaties"}}, "accountAndProfile": {"security": {"title": "Inloggen & Security", "description": "Manage your account security settings and login preferences"}}, "messages": {"title": "Berichten", "messages": {"title": "Berichten"}}}, "accountAndProfile": {"favorite": "Favoriet", "message": "Bericht", "notification": "Notificaties", "profile": "<PERSON><PERSON>", "security": "Security", "logout": {"title": "Log uit account"}}, "info": {"": {"messageTooLongError": {}}, "messageTooLongError": {"description": "<PERSON><PERSON><PERSON> send messages with fewer than {count} characters."}, "messageTooLong": {"title": "Bericht Te Long", "description": "<PERSON><PERSON><PERSON> respond with a clear and concise message."}, "noPaymentMethodsAdded": "Nee payment methods added"}, "settings": {"profile": {"notification": {"description": "Manage your notification preferences"}, "personalInfo": {"description": "Manage your personal information and contact details"}, "security": {"password": {"title": "Wachtwoord", "description": "Manage your password account"}}}, "personalInfo": {"changeEmail": {"title": "<PERSON><PERSON><PERSON> Email", "description": "<PERSON><PERSON><PERSON> in your new email address below. We'll send a verification email to confirm the change."}, "changePhone": {"title": "Verandering Telefoon number", "description": "<PERSON><PERSON><PERSON> in your new phone number below. We'll send a verification SMS to confirm the change."}}}, "navbar": {"search": {"locationTitle": "Locatie", "category": "Category", "flexibleLocation": "I'm flexible", "propertyType": "Selecteer property category"}}, "owner": {"accountAndProfile": {"logout": {"description": "Are you sure you want to log out of your account?"}}}, "component": {"pagination": {"rowPerPage": "Row per page", "goToFirstPage": "Ga to first page", "goToPreviousPage": "Ga to previous page", "goToNextPage": "Ga to next page", "goToLastPage": "Ga to last page"}, "dataTable": {"pagination": {}}}, "faq": {"group": {"usingThePlatform": "Using the Platform", "contactingPropertyOwner": "Contacting Eigendom Eigenaren", "propertyVerificationAndSafety": "Eigendom Verification and Veiligty", "paymentAndFee": "<PERSON>lings and Tariefs", "propertyVisitAndContract": "Eigendom Bezoeks and Contracts"}, "searchPlaceholder": "<PERSON><PERSON> question", "searchProperty": {"title": "How do I search for properties on the platform?", "description": "You can search for properties by using filters such as location, property type, and price range. Voer in your preferences in the search bar to find listings that match your needs."}, "contactOwner": {"title": "How do I contact the owner of a property?", "description": "Each listing on the website includes a chat feature that allows you to contact the owner directly through your Eigendom Plaza account."}, "savingProperty": {"title": "Can I save properties I'm interested in?", "description": "J<PERSON>, you can add properties to your favorites list, which can be accessed in your profile."}, "receivingNotification": {"title": "Can I receive notifications for new listings?", "description": "Ja, you can set up notifications for specific property criteria in your profile settings."}, "technicalIssue": {"title": "What if I experience technical issues on the platform?", "description": "If you encounter any issues, you can contact <PERSON><PERSON>teuni<PERSON> through the chat feature after logging in."}, "negotiatingPrice": {"title": "How do I negotiate the price or terms with the owner?", "description": "Negotiations take place directly through the chat. If both parties agree, they may exchange contact details for further discussion."}, "contactOwnerDirectly": {"title": "Can I contact owners directly?", "description": "<PERSON><PERSON>, after creating an account and subscribing to one of our plans, you can chat directly with property owners. However, please only contact owners of properties you are genuinely interested in, as there are limits on the number of messages you can send per week."}, "limitContactingOwner": {"title": "What are the limits on contacting owners?", "description": "The number of initial messages you can send per week depends on your subscription plan. Currently, you can contact up to 15 owners per week, with this limit resetting every seven days."}, "reasonOfLimitContactingOwner": {"title": "Why is there a limit on contacting owners?", "description": "This restriction helps prevent owners from being overwhelmed with messages and ensures a better experience for both owners and potential buyers/renters."}, "ifOwnerDoesntResponse": {"title": "What if an owner does not respond to my message?", "description": "If an owner does not reply within three days, the contact attempt will not count toward your weekly message limit. Our system tracks inactive owners, and accounts with repeated inactivity may be removed from the platform."}, "checkOwnerisVerified": {"title": "How do I know if the owner is verified?", "description": "We verify the owner's identity and check ownership documents before allowing a listing on our platform. However, we strongly advise that you meet the owner and visit the property in person before making any payments."}, "trustListingPhoto": {"description": "Alle listings go through a verification process. However, we always recommend visiting the property in person before making a decision.", "title": "Can I trust the photos and information in a listing? "}, "safeToDeposit": {"title": "Is it safe to send a deposit?", "description": "Sinds we verify each owner's identity, it should be safe to send a deposit. However, we strongly recommend keeping deposit amounts as low as possible and ensuring that the name on the bank transfer matches the owner's verified details exactly."}, "issueWhenRenting": {"title": "What happens if I have issues with the property after renting or buying?", "description": "Eigendom Plaza is only a platform connecting buyers/renters with owners. Elke issues should be resolved directly with the property owner. It is essential to agree on all terms clearly in a contract before finalizing a deal."}, "handlingPayment": {"title": "How do I handle payments or deposits?", "description": "Betalings are arranged directly with the owner. We strongly advise against transferring any money before seeing the property and meeting the owner in person. If needed, escrow services are available from third-party providers."}, "additionalFee": {"title": "Are there any additional fees I should be aware of?", "description": "Eigendom Plaza does not charge additional fees beyond the subscription required to contact property owners. Elke other costs, such as service fees or taxes, should be discussed directly with the owner."}, "payForSubscription": {"title": "How can I pay for a subscription?", "description": "We accept several payment options, including credit cards, Klarna, Gaogle Pay, and Apple Pay."}, "subscriptionOffer": {"title": "What types of subscriptions do you offer?", "description": "We currently offer only monthly subscriptions, with two different paid plans featuring various benefits. You can view available plans here: ", "extraContent": "Eigendom Plaza Abonnements."}, "cancelSubscription": {"description": "<PERSON><PERSON>, you can cancel at any time, and your subscription will remain active until the end of the current billing cycle. Naar manage your subscription, visit: ", "extraContent": "Manage Abonnement.", "title": "Can I cancel my subscription during the month?"}, "schedulePropertyVisit": {"title": "Can I schedule a property visit through the platform?", "description": "<PERSON><PERSON>, you can arrange a visit by contacting the owner via the chat feature and setting up a suitable time."}, "requestContract": {"title": "Can I request a contract through the platform?", "description": "We do not provide contracts, but a downloadable contract template is available. However, no rights can be derived from this template. Eigenaren may have legal partners who can assist in drafting official contracts."}, "platformOfferLegalAdvice": {"title": "Doenenes the platform offer legal or financial advice?", "description": "<PERSON><PERSON>, but we collaborate with various legal and financial service providers. If you need recommendations, please contact <PERSON><PERSON> for more information."}, "disputeWithOwner": {"title": "What if I have a dispute with the owner?"}, "legalContracts": {"description": "Disputes must be resolved directly between you and the owner. Eigendom Plaza does not mediate in conflicts."}, "title": "FAQs for Vinding Your Perfect Bali Eigendom", "badge": "Frequent Ask and Question"}, "seeker": {"faq": {"cancelSubscription": {"title": "Can I cancel my subscription during the month?"}}}, "termsOfUse": {"title": "Voorwaarden of use"}, "privacyPolicy": {"title": "Privacy Beleid"}, "userDataDeletion": {"title": "Gebruiker data deletion"}, "aboutUs": {"hero": {"title": "Making Eigendom Zoek Eenvoudig and Transparent", "description": "Eigendom Plaza is dedicated to connecting property seekers with their ideal homes and spaces through an intuitive platform that prioritizes transparency, quality, and user experience.", "browseProperties": "<PERSON><PERSON>", "contactUs": "Neem contact op"}, "mission": {"title": "Our Mission & Waardes", "description": "We're on a mission to transform the property search experience by creating a platform that puts users first and makes finding your next property a delightful journey.", "values": {"global": {"title": "Global Perspective", "description": "We bring international standards to local markets, helping you find properties that meet global quality benchmarks."}, "trust": {"title": "Built on Trust", "description": "We verify listings and owners to ensure you can make decisions with confidence and peace of mind."}, "quality": {"title": "Kwaliteit Eerste", "description": "We curate properties that meet our high standards, saving you time and ensuring satisfaction."}, "community": {"title": "Community Focused", "description": "We build connections between property seekers and owners, creating a supportive ecosystem."}, "innovation": {"title": "Innovation", "description": "We continuously improve our platform and services to meet the evolving needs of our clients."}, "personalization": {"title": "Personalization", "description": "We recognize that each client's needs are unique and tailor our approach accordingly."}}, "ourMission": {"title": "Our Mission", "additionalText": "We are committed to showcasing the best properties while maintaining the highest standards of integrity and customer satisfaction."}, "ourVision": {"title": "Our Vision", "description": "Naar be the most trusted and innovative real estate platform, setting the standard for excellence in property services and customer experience.", "additionalText": "We envision a future where finding your dream property is accessible to all, supported by cutting-edge technology and a deep understanding of both local and international client needs."}, "ourCoreValues": {"title": "Our Core Waardes"}}, "story": {"title": "Our Story", "companyTitle": "Your Gateway to Eigendom Zoek", "paragraph1": "Eigendom Plaza began in 2024 when our founders experienced firsthand the challenges of finding quality properties in unfamiliar markets. They envisioned a platform that would make property search transparent, efficient, and enjoyable.", "paragraph2": "Begining with just a handful of listings in Canggu (Bali), we've grown to become a trusted platform, helping thousands of people find their perfect property match every month.", "paragraph3": "<PERSON><PERSON><PERSON>, we continue to innovate and expand, guided by our commitment to quality, transparency, and exceptional user experience."}, "team": {"title": "Meet Our Team", "description": "The passionate individuals behind Eigendom Plaza are dedicated to revolutionizing how people find and secure properties.", "roles": {"ceo": "Chief Executive <PERSON><PERSON><PERSON><PERSON>", "cto": "Chief <PERSON> Kanto<PERSON><PERSON>", "marketing": "Head of Marketing", "propertySpecialist": "Eigendom Specialist", "frontend": "Frontend Developer", "backend": "Backend Developer", "backend2": "Backend Developer", "tester": "QA Tester", "customerSuccess": "<PERSON>lant Succesvol Manager"}, "members": {"rt": {"name": "<PERSON>", "bio": "As an entrepreneur focused on innovation and process optimization, <PERSON> realized that Bali lacked a transparent real estate market."}, "thijs": {"name": "<PERSON><PERSON><PERSON><PERSON>", "bio": "Has experience setting up multiple SaaS projects. Leads the development team and ensures an optimal technical infrastructure."}, "joost": {"name": "<PERSON><PERSON>", "bio": "Marketing advisor responsible for the strategy to help seekers find their ideal property in Bali."}, "aditya": {"name": "<PERSON><PERSON><PERSON>", "bio": "Met his experience and drive as a Frontend Developer, <PERSON><PERSON><PERSON> ensures the platform was developed quickly with a focus on the customer journey."}, "anjas": {"name": "<PERSON><PERSON><PERSON>", "bio": "As a Backend Developer, <PERSON><PERSON><PERSON> ensures everything continues to run smoothly, no matter where in the world the platform is being used."}, "nuni": {"name": "<PERSON><PERSON>", "bio": "Our communicative Backend Developer works with <PERSON><PERSON><PERSON> to ensure all systems continue to function optimally."}, "dennis": {"name": "<PERSON>", "bio": "Specializes in identifying and verifying listings to maintain the high standards of our platform."}, "andrea": {"name": "<PERSON>", "bio": "Specializes in identifying and verifying listings to maintain the high standards of our platform."}, "natha": {"name": "<PERSON><PERSON>", "bio": "Specializes in identifying and verifying listings to maintain the high standards of our platform."}, "rizki": {"name": "<PERSON><PERSON><PERSON>", "bio": "As our dedicated QA Tester, <PERSON><PERSON><PERSON> meticulously tests every feature to ensure a seamless user experience across all devices and platforms."}}}, "cta": {"title": "<PERSON><PERSON> to Vind Your Perfect Eigendom?", "description": "Begin your property search journey with Eigendom Plaza today and discover why thousands of users trust us to find their ideal spaces.", "findProperty": "Vind a Eigendom", "getInTouch": "Krijg in Contact"}, "contact": {"title": "Krijg in Contact", "visitUs": {"title": "Bezoek Us (in Bali)", "address": "Jl. Sempol 17C\nPererenan - Canggu\nIndonesia 80351"}, "emailUs": {"title": "Email Us", "general": "Voor general inquiries:", "generalEmail": "info (at) property-plaza.com", "listings": "Voor property listings:", "listingsEmail": "listings (at) property-plaza.com"}, "callUs": {"title": "Chat with Us", "officeHours": "Kantoor <PERSON>: 9AM - 6PM (GMT+8)", "phone": "+62 ***********", "whatsapp": "WhatsApp:", "whatsappNumber": "+62 812 3456 7890"}}, "tabs": {"company": "Our Company", "team": "Our Team", "mission": "Mission & Vision"}}, "plan": {"title": "Exclusive Access to Bali’s Beste Eigendom Deals!", "description": "Krijg exclusive access to off-market villas, prime real estate, and investment opportunities before anyone else. <PERSON><PERSON><PERSON>’t miss out—subscribe now and secure your next property with confidence!"}, "srOnly": {"FindBestPropertyOnBali": "Vind the best properties on Bali"}}, "universal": {"otp": {"verifyHeader": "Verify your {field}", "changeRegisterData": "Verandering your register data", "resendVerificationCode": "Resend Verification code"}, "form": {"utility": {"fieldRequired": "<PERSON><PERSON><PERSON> provide {field}", "invalidFormat": "Ongeldig {field} format", "eitherFieldRequired": "Either {fields} must be provided", "enterValidField": "<PERSON><PERSON><PERSON> enter a valid {field}", "forgotField": "Vergeten your {field}?", "resetField": "Resetten {field}", "minimumLength": "{field} minimum length is {length}", "fieldNotMatch": "{field} komen niet overeen", "optional": "(optional)", "coordinateRequired": "Voer in latitude and longitude, separated by a comma", "wrongFormatCoordinate": "Fout format of coordinates, please input latitude and longitude separated by a comma", "maximumLength": "{field} exceeded the maximum length of {length} characters ", "passwordWeak": "Wachtwoord is too weak", "password": {"minimumLength": "Minimaal 8 karakters", "numberRequired": "Tenminste one number", "notCommonWord": "Niet common word", "specialCharacter": "Tenminste Een symbol ", "uppercaseRequired": "Tenminste one uppercase", "lowercaseRequired": "Tenminste one lowercase"}}, "field": {"email": "Email", "phoneNumber": "Telefoon number", "password": "Wachtwoord", "confirmPassword": "Bevestig password", "username": "Gebruikername", "firstName": "Eerste name", "lastName": "Laatste name", "address": "<PERSON><PERSON>", "aboutYou": "Over you", "language": "Language", "message": "Bericht", "typeProperty": "Eigendom Type", "yearsOfBuild": "Jaar of onstruction", "propertyView": "Bekijk Type", "landSize": "Grond Oppervlakte", "buildingSize": "Gebouw Oppervlakte", "gardenSize": "Tuin/Outdoor Ruimte", "typeBedroom": "# of Slaapkamers", "totalBathroom": "# of Badkamer", "wifi": "WiFi (Mb/s)", "cleaning": "total Schoonmaak", "cleaningTime": "Sc<PERSON>onmaak time", "review": "Review", "postalCode": "Postcode", "coordinate": "Coördinaten", "propertyLocation": "Located", "roadSize": "Entrance road size", "addressLocation": "Eigendom address", "distric": "District", "city": "Stad/Regency", "province": "<PERSON><PERSON><PERSON>", "typeContract": "Contract type", "minimumDuration": "Minimale duur", "minimumPrice": "<PERSON><PERSON><PERSON><PERSON>", "maximumDuration": "Maximale duur", "elictricity": "Elektriciteit (KW/h)", "furnishingStatus": "Me<PERSON>lering", "parking": "<PERSON><PERSON>", "poolAvailability": "Zwembad", "livingStatus": "<PERSON><PERSON>", "role": "Role", "fullName": "Vol name", "name": "Name", "otp": "OTP", "statusListing": "Listing status", "excerpt": "Excerpt", "title": "Title", "banjar": "Banjar"}, "title": {"login": "Inloggen", "signUp": "Registreer", "staffLogin": "Inloggen Staff", "resetPassword": "Resetten password", "enterOtpCode": "Voer in OTP code", "createPassword": "Maken password"}, "label": {"emailOrPhone": "Email or Telefoon number", "password": "Wachtwoord", "username": "Gebruikername", "confirmPassword": "Bevestig password", "firstName": "Eerste name", "lastName": "Laatste name", "aboutYou": "Over you", "email": "Email", "address": "Thuis address", "phoneNumber": "Telefoon number", "language": "<PERSON>", "PropertyType": "Eigendom type", "cascoProperty": "Casco eigendom", "yearsOfBuild": "Jaar Built", "propertyView": "Eigendom Bekijk", "landSize": "Grond Oppervlakte", "buildingSize": "Gebouw Oppervlakte", "gardenSize": "Tuin/Outdoor Ruimte", "totalBedroom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "totalBathroom": "<PERSON><PERSON><PERSON>(s)", "wifi": "WiFi", "cleaning": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "coordinate": "Coördinaten", "propertyLocation": "Eigendom Locatie", "roadSize": "Entrance <PERSON><PERSON>", "addressLocation": "<PERSON><PERSON>", "postalCode": "Postcode", "distric": "District", "city": "Stad/Regency", "province": "<PERSON><PERSON><PERSON>", "typeContract": "Contract type", "availableContract": "<PERSON><PERSON><PERSON><PERSON><PERSON> from", "minimumDuration": "Minimale duur", "minimumPrice": "Asking <PERSON><PERSON><PERSON>", "maximumDuration": "Maximale duur", "elictricity": "Elektriciteit", "furnishingStatus": "Me<PERSON>lering", "parkingStatus": "<PERSON><PERSON>", "poolAvailability": "Zwembad", "livingStatus": "<PERSON><PERSON>", "fullName": "Vol name", "name": "Name", "requestCreateListing": "Vertel ons iets over je eigendom", "propertyDescription": "Eigendom description", "pricing": "Prijzen", "type": "Type", "amount": "Bedrag", "price": "<PERSON><PERSON><PERSON><PERSON>", "date": "Aankoopda<PERSON> ", "requestUpdateListing": "Vertel ons wat je wilt wijzigen", "promoCode": "Promocode", "status": "Listing status", "excerpt": "Excerpt", "propertyTitle": "Title", "maximum": "Maximum", "userId": "Gebruiker ID", "banjar": "Banjar"}, "placeholder": {"basePlaceholder": "<PERSON><PERSON><PERSON> je in {field}", "aboutYou": "Tell us the things we should know about you", "pickLanguage": "Selecteer a language", "baseSelectPlaceholder": "Selecteer {field}", "baseNumberPlaceholder": "Stel je in {field}", "availableContract": "Vroegst beschikbare contractdatum", "searchQuestion": "Zoek FAQ", "example": {"requestHelpToCs": "Can I also contact you via Whatssapp?", "email": "<EMAIL>", "phoneNumber": "+91 123 456 789", "firtName": "<PERSON>", "lastName": "<PERSON><PERSON>", "requestCreateListing": "Villa with 4 bedrooms, 3 bathrooms, and a private pool located in Canggu, Slechtung", "requestUpdateListing": "Ik wil het aantal badkamers wijzigen"}, "enterPromoCode": "<PERSON><PERSON><PERSON> in promo code", "seekersFindPropertyLocation": "Zoek for destination"}, "description": {"resetPassword": "we sturen je wachtwoord verzoek naar je e-mail", "passwordRequirement": "Wachtwoord should have minimum 8 Characters, 1 Hoofdletter, 1 lowecase, 1 number and 1 Symbol", "userId": "This is your unique identifier in our system"}, "subtitle": {"resetPassword": "Resetten password for your account"}}, "conjuntion": {"or": "or", "and": "and", "of": "of", "for": "for"}, "cta": {"login": "Inloggen", "createAccount": "Sign Omhoog now", "continueWith": "Ga door with {field}", "edit": "Bewerk", "updatePassword": "Update", "verify": "Verify", "seeTranslation": "Bekijk translation", "seeOriginal": "Bekijk original", "filter": "Filter", "chatCustomerService": "Need help? Chat with our Klant Ondersteuning", "requestCreateListing": "Maken listing request", "createListing": "Maken listing", "contactAccountmanager": "N<PERSON>m contact op met Account Manager", "disableListing": "Uitgeschakeld listing", "finishReview": "Voltooien review", "saveAsDraft": "<PERSON><PERSON><PERSON> as draft", "addReview": "Toevoegen review", "save": "<PERSON><PERSON><PERSON>", "back": "Terug", "joinWaitngList": "Word lid the waiting list", "logout": "Log uit", "cancel": "<PERSON><PERSON><PERSON>", "signUpNow": "Registreer now", "sendResetPassword": "Verzoek wachtwoord wijziging", "checkOut": "Checkout", "contactToWhatsapp": "Neem contact met ons op via WhatsApp", "createYourAccount": "Maken your account", "subscribe": "Abonneer", "signUp": "Registreer", "goBack": "Ga back", "search": "<PERSON><PERSON>", "removeSearch": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disable": "Disable", "enable": "Enable", "sendRequest": "Verstuur request", "closeChat": "Sluiten chat", "sellPropertyFast": "Snel Sell or Rent Your Eigendom", "next": "Volgde", "activateListing": "Activeer listing", "activate": "Activate", "changeStatus": "Verandering status", "changePrice": "Verandering price", "pay": "Pay", "topUp": "bij<PERSON>n", "viewAllProperty": "Bekijk all property", "readMore": "Lees more ...", "changePassword": "Verandering password", "filters": "Filters", "clearAll": "Wis all", "previous": "Vorige", "maps": "Maps", "list": "List", "contactOwner": "Contact the Eigenaar", "share": "<PERSON><PERSON>", "readLess": "Lees less", "copyLink": "Kopiëren link", "close": "Sluiten", "findOtherProperty": "Bekijk other properties", "findOtherPackage": "Bekijk other packages", "followUsOnInstagram": "Volg us on Instagram", "changePhoto": "Verandering Photo", "saveChanges": "<PERSON><PERSON><PERSON>", "change": "<PERSON><PERSON><PERSON>", "confirm": "Bevestig", "viewAll": "Bekijk all", "signOutAll": "Log uit op alle apparaten", "signOutDevice": "Log uit apparaat", "update": "Update", "addPaymentMethod": "Toevoegen payment method", "requestChangePassword": "Request Verandering password"}, "error": {"resetPassword": {"title": "Oops, failed request reset password"}, "requestForgetPassword": {"title": "Oops, failed request forget password"}, "foundError": "Oops, there's an error when updating user detail"}, "success": {"requestForgotPassword": {"title": "Succesvol request Vergeet password", "description": "<PERSON><PERSON><PERSON> check your email to continue the process"}, "updateUser": "Succesvol update user detail", "createPassword": {"title": "Succesvol create new password", "description": "<PERSON><PERSON><PERSON> log in to continue"}}, "popup": {"followInstagram": {"title": "Let's connect", "description": "Volg us for Bali vibes, insider tips, and exclusive updates!"}}, "misc": {"enableSoundNotification": {"title": "Enable notification", "description": "<PERSON><PERSON>j<PERSON> latest update from our team and owner"}, "foundError": "<PERSON><PERSON><PERSON>, kan verificatie e-mail niet versturen", "ascendingOrder": "Oplopende volgorde", "descendingOrder": "Aflopende volgorde", "hide": "Verbergen", "userNotFound": "user not found", "profileImageAlt": "property-plaza gebruiker profiel afbeelding", "temporaryBanner": {"title": "Your Ideal Bali Eigendom Is on the Way", "description": "We know how important it is to find the right space, whether it’s a homestay, shop, or serene piece of land. That’s why we’re taking the time to source only listings that truly meet your needs."}}, "universal": {"success": {"createPassword": {"title": "Succesvol create new password"}}}}, "ContactUs": {"pageTitle": "Neem contact op | Eigendom Plaza", "pageDescription": "Contact Eigendom Plaza. Krijg in touch with our team for inquiries, support, or collaboration. We're ready to assist you with prompt, reliable responses.", "title": "Neem contact op", "subtitle": "Heb je vragen of hulp nodig? We zijn er om te helpen. Neem contact met ons op via het onderstaande formulier.", "sendMessage": "Verstuur Us a Bericht", "nameField": "Je naam", "emailField": "Je e-mail", "subjectField": "Onderwerp", "messageField": "Your Bericht", "submitButton": "Verstuur Bericht", "messageSent": "<PERSON> bericht is succesvol verzonden. We nemen binnenkort contact met je op!"}, "owner": {"misc": {"rentalTerms": "<PERSON><PERSON><PERSON><PERSON> terms", "ownerProperty": "Eigenaar property"}, "listing": {"detail": {"contactCount": "{count} mensen hebben al contact opgenomen met de eigenaar"}}}, "verify": {"seo": {"title": "Bali Villa Inspectie Service – Voorkom Oplichting Voor Je Huurt", "description": "Bezorgd over oplichting bij het huren van een villa in Bali? Wij inspecteren villa's, verifiëren juridische documenten en rapporteren verborgen risico's voordat je betaalt. Boek nu je onafhankelijke en professionele Bali villa inspectie.", "keywords": "Bali villa inspectie service, vermijd huur oplichting in Bali, villa controle voor verhuur, eigendom inspectie Bali, villa fraude preventie Bali, gecertificeerde villa inspectie"}, "hero": {"badge": "Geverifieerde Bali Villa Inspectie", "title": "Bali Villa Inspectie – Vermijd Oplichting Voor Je Huurt", "subtitle": "Op zoek naar een huurwoning in Bali? He<PERSON><PERSON> zijn villa verhuur oplichting en neplijsten veel voorkomend. Eigendom Plaza biedt een onafhankelijke en vertrouwde villa inspectie service om je huurinvestering te beschermen. Ons team bezoekt het eigendom, verifieert documenten en verhuurder identiteit, inspecteert de structuur en benadrukt potentiële risico's.", "benefits": ["Verhuurder Identiteit & Document Verificatie", "Professionele Eigendom Inspectie & Foto's", "Schriftelijke Rapporten & Video Rondleidingen", "<PERSON><PERSON> dan 20% van Bali villa verhuur verbergt ernstige risico's of oplichting"], "cta": "Boek nu een onafhankelijke en professionele villa inspectie in Bali - voordat je je aanbetaling doet", "warning": "<PERSON><PERSON><PERSON>w niet alleen op foto's of beloft<PERSON> van make<PERSON>."}, "howItWorks": {"title": "Hoe Onze Bali Villa Inspectie Werkt – 3 Eenvoudige Stappen", "subtitle": "<PERSON>ij inspecteren Bali villa's en leveren je gepersonaliseerde rapport binnen 24 uur – gebaseerd op je gekozen inspectie pakket.", "steps": {"book": {"title": "<PERSON><PERSON>", "description": "Selecteereer je inspectie pakket (Basis, Standaard, of Premium), kies je inspectie datum, en verstuur de eigendom locatie. \n \n Na het boeken sturen we je een bevestigingsmail en een korte checklist om de inspectie zo efficiënt mogelijk te maken.", "result": "Boekingsbevestiging + villa voorbereidingschecklist"}, "inspect": {"title": "Ter Plaatse Villa Bezoek", "description": "Our expert visits the property to carry out verifications, depending on the package you choose. This includes for example: inspecting the property's structure, identifying potential hidden costs, taking photos or a video walkthrough, verifying the landlord’s identity, and reviewing the rental or sales agreement.", "result": {"basic": "Basis – Villa bezoek + verhuurder verificatie + foto's + huurovereenkomst concept", "standard": "Standaard – Eigendom inspection + written report + video walkthrough + consultation call", "premium": "Premium – BPN eigendom verificatie + langetermijn huur sjabloon + prioriteit boeken"}}, "report": {"title": "Aantvangen Your Report in 24 Uren", "description": "Aance the inspection is complete, you'll receive a personalized and detailed report via email or WhatsApp. Met this comprehensive overview, you’ll be empowered to make a safe and well-informed rental or purchase decision.", "result": {"basic": "<PERSON><PERSON> – <PERSON>oto's + stem overzicht + huurovereenkomst concept", "standard": "Standaard – Schriftelijk inspectie rapport + video + consultatie gesprek", "premium": "Premium – Volledig package + BPN verification + long-term rental template"}}}, "whyChoose": {"title": "Why choose Eigendom Plaza's Bali villa inspection?", "description": "Elk pakket geeft je transparantie en gemoedsrust – voordat je je aanbetaling doet."}}, "pricing": {"title": "Prijspakketten", "subtitle": "Lokale Marktprijzen - Kies het inspectieniveau dat bij je behoeften past", "popular": "<PERSON><PERSON>ula<PERSON>", "tiers": {"basic": {"name": "Basis <PERSON>", "subtitle": "<PERSON><PERSON> kortetermi<PERSON> verhuur\n(1+ ma<PERSON><PERSON>)", "features": ["Afspraak & villa bezoek", "Huurovereenkomst concept", "Verhuurder identiteit verificatie", "Foto's van het eigendom", "Kort stem overzicht"]}, "standard": {"name": "Standaard Pakket", "subtitle": "Aanbevolen voor middellange verhuur\n(meerdere maanden of langer)", "popular": "Aanbevolen", "features": ["Inclusief Basis Pakket, plus:", "Algemene eigendom inspectie", "Schriftelijk inspectie rapport", "Video rondleiding van het eigendom", "Telefoongesprek om belangrijke bevindingen te bespreken"]}, "premium": {"name": "Premium Pakket", "subtitle": "Aanbevolen voor langetermijn verhuur\n(1+ jaar)", "features": ["Inclusief Standaard Pakket, plus:", "Eigendom verificatie via BPN (Indonesische Grondregistratie)*", "Langetermijn h<PERSON>urovereenkomst sjabloon", "Prioriteit boeken voor inspecties"], "footnote": "*BPN verificatie biedt eigendom bevestiging, geen juridische garantie"}}, "cta": "Selecteer {tierName}"}, "booking": {"title": "Boek Your Inspection", "subtitle": "<PERSON>ul het on<PERSON><PERSON><PERSON><PERSON> formulier in en we nemen binnen 24 uur contact met je op", "form": {"firstName": {"label": "Eerste Name", "placeholder": "<PERSON><PERSON><PERSON> in your first name", "required": "Eerste name is required", "minLength": "Eerste name must be at least 2 characters"}, "lastName": {"label": "Laatste Name", "placeholder": "<PERSON><PERSON><PERSON> in your last name", "required": "Laatste name is required", "minLength": "Laatste name must be at least 2 characters"}, "email": {"label": "<PERSON><PERSON>", "placeholder": "<EMAIL>", "required": "E-mailadres is verplicht", "invalid": "<PERSON><PERSON><PERSON> enter a valid email address"}, "whatsappNumber": {"label": "<PERSON>s<PERSON><PERSON>", "placeholder": "+62 812 3456 7890", "required": "WhatsApp nummer is verplicht", "invalid": "<PERSON><PERSON><PERSON> enter a valid WhatsApp number"}, "villaAddress": {"label": "Villa Adres", "placeholder": "Volledig villa address in Bali", "required": "Villa adres is verplicht", "minLength": "G<PERSON>eve provide a complete villa address"}, "preferredDate": {"label": "Gewenste Inspectie Datum", "required": "Preferred inspection date is required"}, "tier": {"label": "<PERSON><PERSON><PERSON>", "placeholder": "Selecteereer inspectie niveau", "required": "Gelieve select an inspection tier", "options": {"basic": "Basis - IDR 4,500,000", "smart": "Smart - IDR 6,000,000", "fullShield": "Vol Shield - IDR 8,500,000"}}, "cta": "Reserveer Inspection", "submitting": "Verstuur...", "disclaimer": "Door dit formulier in te dienen, ga je ermee akkoord dat we contact met je opnemen via WhatsApp voor inspectie coördinatie.", "success": {"title": "Boeking Verstuurted!", "message": "We nemen binnen 24 uur contact met je op om je inspectie te bevestigen."}, "error": {"title": "Fout", "message": "Boeking indienen mislukt. Probeer het opnieuw."}}}, "success": {"title": "Betaling Succesvolvol!", "subtitle": "Je villa inspectie is succesvol geboekt.", "details": {"title": "Wat gebeurt er nu?", "confirmation": "Bevestiging Email", "confirmationDesc": "<PERSON> ontvangt binnen 5 minuten een gedetailleerde bevestigingsmail.", "scheduling": "<PERSON><PERSON><PERSON>", "schedulingDesc": "Ons team ne<PERSON><PERSON> binnen 24 uur contact met je op om de exacte tijd te bevestigen.", "contact": "Directe Communicatie", "contactDesc": "We nemen contact op via WhatsApp voor updates of vragen."}, "nextSteps": {"title": "Volgde Steps", "step1": "Controleer je e-mail voor boekingsbevestiging en voorbereidingschecklist", "step2": "<PERSON><PERSON>g ervoor dat er iemand be<PERSON> is bij het eigendom tijdens de inspectie", "step3": "Bereid relevante documenten voor (indien beschikbaar)", "step4": "Onze inspecteur neemt 1 dag voor het bezoek contact met je op"}, "actions": {"backHome": "Terug to Thuis", "contactUs": "Contact Ondersteuning"}}, "socialProof": {"title": "Wat Onze Klanten Zeggen", "subtitle": "Echte ervaringen van eigendom zoekers die onze inspectie service hebben gebruikt", "reviews": {"review1": {"name": "<PERSON>", "location": "Nederland", "text": "De inspectie heeft me van een grote fout gered! Ze ontdekten schimmelplekken die niet zichtbaar waren op de eerdere foto's. Dit heb ik gebruikt om een betere prijs te onderhandelen. Professionele service en gedetailleerd rapport.", "propertyType": "Villa in Canggu"}, "review2": {"name": "<PERSON>", "location": "Australië", "text": "Uitstekende service! De inspecteur was grondig en de video rondleiding was ongelooflijk nuttig omdat ik niet persoonlijk kon bezoeken. Elke cent waard.", "propertyType": "Appartement in Seminyak"}, "review3": {"name": "Maria L.", "location": "Duitsland", "text": "<PERSON><PERSON> was huiverig om een groot bedrag over te maken, maar na hun check van property en persoon kon ik met een gerust hart betalen. Gaf me veel rust!", "propertyType": "Huis in Ubud"}}, "trustIndicators": {"verified": "Geverifieerde Reviews", "realClients": "Echte Klanten", "actualInspections": "Werkelijke Inspecties"}}, "disclaimer": {"title": "Disclaimer", "content": "Wij fungeren als een onafhankelijke inspectieservice. Onze rapporten zijn gebaseerd op ter plaatse observaties en beschikbare documenten. Aan onze bevindingen kunnen geen rechten worden ontleend. Wij bieden geen juridische garanties betreffende eigendom of grondtitel. Raadpleeg altijd een gecertificeerde notaris of juridisch adviseur voor juridische due diligence."}, "cta": {"bookInspection": "📋 Boek Professioneel Villa Inspection Nu"}, "footnote": "✅ Protect your investment • Avoid scams • Krijg peace of mind"}}