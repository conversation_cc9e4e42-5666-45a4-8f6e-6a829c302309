{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(\\/?index|\\/?index\\.json))?[\\/#\\?]?$", "originalSource": "/"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/(en|id))(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/(en|id)/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!hooks|_next\\/static|_next\\/image|favicon.ico|icon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp|mp3)$).*))(.json)?[\\/#\\?]?$", "originalSource": "/((?!hooks|_next/static|_next/image|favicon.ico|icon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp|mp3)$).*)"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(.json)?[\\/#\\?]?$", "originalSource": "/api/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "Uyivpvyq0RIhceAR_DZfO", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "F0nM9P/qFVVTs32QAV+uffKTshdSVbNU67Nm+0ap1Qg=", "__NEXT_PREVIEW_MODE_ID": "d8163bce261aa3437483218abc58a699", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "6e3764fd462d9083d057481eeae8a55e0fbfdfe98c8bc3070fa65af705130b41", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "87204420fd2944c534bef2feee937b0c4d1130683096f2ba6a57d9b034144ed6"}}}, "functions": {}, "sortedMiddleware": ["/"]}