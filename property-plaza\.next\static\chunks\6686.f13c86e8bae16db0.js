(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6686],{83238:function(e,t,n){"use strict";var r,i,o,a,s,l=n(2265),u=(r=Object.create(null),l&&Object.keys(l).forEach(function(e){if("default"!==e){var t=Object.getOwnPropertyDescriptor(l,e);Object.defineProperty(r,e,t.get?t:{enumerable:!0,get:function(){return l[e]}})}}),r.default=l,Object.freeze(r));let{useRef:c,useEffect:d,isValidElement:f}=u,p=(null!==(o=u.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE)&&void 0!==o||u.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Symbol.for("react.memo_cache_sentinel")),h="function"==typeof(null===(a=u.__COMPILER_RUNTIME)||void 0===a?void 0:a.c)?u.__COMPILER_RUNTIME.c:function(e){return u.useMemo(()=>{let t=Array(e);for(let n=0;n<e;n++)t[n]=p;return t[p]=!0,t},[])},y={};["readContext","useCallback","useContext","useEffect","useImperativeHandle","useInsertionEffect","useLayoutEffect","useMemo","useReducer","useRef","useState","useDebugValue","useDeferredValue","useTransition","useMutableSource","useSyncExternalStore","useId","unstable_isNewReconciler","getCacheSignal","getCacheForType","useCacheRefresh"].forEach(e=>{y[e]=()=>{throw Error(`[React] Unexpected React hook call (${e}) from a React compiled function. Check that all hooks are called directly and named according to convention ('use[A-Z]') `)}}),y.useMemoCache=e=>{throw Error("React Compiler internal invariant violation: unexpected null dispatcher")},(i=s||(s={}))[i.PushGuardContext=0]="PushGuardContext",i[i.PopGuardContext=1]="PopGuardContext",i[i.PushExpectHook=2]="PushExpectHook",i[i.PopExpectHook=3]="PopExpectHook",t.c=h},56686:function(e,t,n){"use strict";let r,i;n.r(t),n.d(t,{default:function(){return aQ}});var o,a,s,l,u,c,d,f,p,h,y,m,g,v,b=n(57437),w=n(83238),x=n(2265),E=n(54887),_=n(65069);let k={"handshake/syn":_.bK,"handshake/syn-ack":_.Ol,"handshake/ack":_.ei,"channel/response":_.Ag,"channel/heartbeat":_.UE,"channel/disconnect":_.Hq,"overlay/focus":"visual-editing/focus","overlay/navigate":"visual-editing/navigate","overlay/toggle":"visual-editing/toggle","presentation/toggleOverlay":"presentation/toggle-overlay"},j={[_.bK]:"handshake/syn",[_.Ol]:"handshake/syn-ack",[_.ei]:"handshake/ack",[_.Ag]:"channel/response",[_.UE]:"channel/heartbeat",[_.Hq]:"channel/disconnect","visual-editing/focus":"overlay/focus","visual-editing/navigate":"overlay/navigate","visual-editing/toggle":"overlay/toggle","presentation/toggle-overlay":"presentation/toggleOverlay"},S=e=>{let{data:t}=e;return t&&"object"==typeof t&&"domain"in t&&"type"in t&&"from"in t&&"to"in t&&("sanity/channels"===t.domain&&(t.domain=_.yK),"overlays"===t.to&&(t.to="visual-editing"),"overlays"===t.from&&(t.from="visual-editing"),t.channelId=t.connectionId,delete t.connectionId,t.type=k[t.type]??t.type),e},I=({context:e},t)=>{let{sources:n,targetOrigin:r}=e,i=(e=>{let{channelId:t,...n}=e,r={...n,connectionId:t};return r.domain===_.yK&&(r.domain="sanity/channels"),"visual-editing"===r.to&&(r.to="overlays"),"visual-editing"===r.from&&(r.from="overlays"),r.type=j[r.type]??r.type,"channel/response"===r.type&&r.responseTo&&!r.data&&(r.data={responseTo:r.responseTo}),("handshake/syn"===r.type||"handshake/syn-ack"===r.type||"handshake/ack"===r.type)&&(r.data={id:r.connectionId}),r})(t.message);n.forEach(e=>{e.postMessage(i,{targetOrigin:r})})},O=()=>({listen:(0,_.F8)(S),requestMachine:(0,_.lB)().provide({actions:{"send message":I}})}),A={alt:"altKey",ctrl:"ctrlKey",mod:"u">typeof window&&/Mac|iPod|iPhone|iPad/.test(window.navigator.platform)?"metaKey":"ctrlKey",shift:"shiftKey"};function P(e){return"Alt"===e.key}var M=n(10868);function $(e){return e.split(/[[.\]]/g).filter(Boolean).map(e=>e.includes("==")?function(e){let[t,n]=e.split("==");if("_key"!==t)throw Error(`Currently only "_key" is supported as path segment. Found ${t}`);if(typeof n>"u")throw Error('Invalid path segment, expected `key=="value"`');return{_key:n.replace(/^['"]/,"").replace(/['"]$/,"")}}(e):R.test(e)?Number(e):e)}let R=/^-?\d+$/,C=/^[a-z_$]+/;function T(e){return e.map((e,t)=>{var n;return n=0===t,Array.isArray(e)?`[${e[0]}:${e[1]||""}]`:"number"==typeof e?`[${e}]`:"object"==typeof e&&"_key"in e&&"string"==typeof e._key?`[_key==${JSON.stringify(e._key)}]`:"string"==typeof e&&C.test(e)?n?e:`.${e}`:`['${e}']`}).join("")}function L(e){if("createIfNotExists"in e)return{type:"createIfNotExists",document:e.createIfNotExists};if("createOrReplace"in e)return{type:"createOrReplace",document:e.createOrReplace};if("create"in e)return{type:"create",document:e.create};if("delete"in e)return{id:e.delete.id,type:"delete"};if("patch"in e){var t,n;return{type:"patch",id:e.patch.id,patches:[..."set"in(n=t=e.patch)?Object.keys(n.set).map(e=>({path:$(e),op:{type:"set",value:n.set[e]}})):[],..."setIfMissing"in t?Object.keys(t.setIfMissing).map(e=>({path:$(e),op:{type:"setIfMissing",value:t.setIfMissing[e]}})):[],..."unset"in t?t.unset.map(e=>({path:$(e),op:{type:"unset"}})):[],..."inc"in t?Object.keys(t.inc).map(e=>({path:$(e),op:{type:"inc",amount:t.inc[e]}})):[],..."inc"in t?Object.keys(t.dec).map(e=>({path:$(e),op:{type:"dec",amount:t.dec[e]}})):[],...function(e){if(!("insert"in e))return[];let t=function(e){let t=D.filter(t=>t in e);if(t.length>1)throw Error(`Insert patch is ambiguous. Should only contain one of: ${D.join(", ")}, instead found ${t.join(", ")}`);return t[0]}(e.insert);if(!t)throw Error("Insert patch missing position");let n=$(e.insert[t]),r=n.pop();return[{path:n,op:{type:"insert",position:t,referenceItem:r,items:e.insert.items}}]}(t)]}}throw Error(`Unknown mutation: ${JSON.stringify(e)}`)}let D=["before","replace","after"];function N(e){return z(e)}function F(e){return e.flatMap(N)}function z(e){if("create"===e.type||"createIfNotExists"===e.type||"createOrReplace"===e.type)return{[e.type]:e.document};if("delete"===e.type)return{delete:{id:e.id}};let t=e.options?.ifRevision;return e.patches.map(n=>({patch:{id:e.id,...t&&{ifRevisionID:t},...function(e){let{path:t,op:n}=e;if("unset"===n.type)return{unset:[T(t)]};if("insert"===n.type)return{insert:{[n.position]:T([...t,n.referenceItem]),items:n.items}};if("diffMatchPatch"===n.type)return{diffMatchPatch:{[T(t)]:n.value}};if("inc"===n.type)return{inc:{[T(t)]:n.amount}};if("dec"===n.type)return{dec:{[T(t)]:n.amount}};if("set"===n.type||"setIfMissing"===n.type)return{[n.type]:{[T(t)]:n.value}};if("truncate"===n.type){let e=[n.startIndex,"number"==typeof n.endIndex?n.endIndex:""].join(":");return{unset:[`${T(t)}[${e}]`]}}if("upsert"===n.type)return{unset:n.items.map(e=>T([...t,{_key:e._key}])),insert:{[n.position]:T([...t,n.referenceItem]),items:n.items}};if("assign"===n.type)return{set:Object.fromEntries(Object.keys(n.value).map(e=>[T(t.concat(e)),n.value[e]]))};if("unassign"===n.type)return{unset:n.keys.map(e=>T(t.concat(e)))};if("replace"===n.type)return{insert:{replace:T(t.concat(n.referenceItem)),items:n.items}};if("remove"===n.type)return{unset:[T(t.concat(n.referenceItem))]};throw Error(`Unknown operation type ${n.type}`)}(n)}}))}function U(e){return Array.isArray(e)?e:[e]}var W=Object.freeze({__proto__:null,decode:function(e){return L(e)},decodeAll:function(e){return e.map(L)},encode:N,encodeAll:F,encodeMutation:z,encodeTransaction:function(e){return{transactionId:e.id,mutations:F(e.mutations)}}});function V(e,t,n){return{type:"insert",referenceItem:n,position:t,items:U(e)}}function q(e,t){return{type:"truncate",startIndex:e,endIndex:t}}function B(e){return{type:"remove",referenceItem:e}}function G(e,t){return{path:"string"==typeof e?$(e):e,op:t}}var H=n(40211);let Z=(0,H.ah)(),K=Z,Y=new Set,X=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,J=/_key\s*==\s*['"](.*)['"]/,Q=/^\d*:\d*$/;function ee(e){return"number"==typeof e||"string"==typeof e&&/^\[\d+\]$/.test(e)}function et(e){return"string"==typeof e?J.test(e.trim()):"object"==typeof e&&"_key"in e}function en(e){if("string"==typeof e&&Q.test(e))return!0;if(!Array.isArray(e)||2!==e.length)return!1;let[t,n]=e;return("number"==typeof t||""===t)&&("number"==typeof n||""===n)}function er(e){if(!Array.isArray(e))throw Error("Path is not an array");return e.reduce((e,t,n)=>{let r=typeof t;if("number"===r)return`${e}[${t}]`;if("string"===r)return`${e}${0===n?"":"."}${t}`;if(et(t)&&t._key)return`${e}[_key=="${t._key}"]`;if(Array.isArray(t)){let[n,r]=t;return`${e}[${n}:${r}]`}throw Error(`Unsupported path segment \`${JSON.stringify(t)}\``)},"")}function ei(e){if("string"!=typeof e)throw Error("Path is not a string");let t=e.match(X);if(!t)throw Error("Invalid path string");return t.map(eo)}function eo(e){return ee(e)?Number(e.replace(/[^\d]/g,"")):et(e)?{_key:e.match(J)[1]}:en(e)?function(e){let[t,n]=e.split(":").map(e=>""===e?e:Number(e));return[t,n]}(e):e}var ea=Object.freeze({__proto__:null,fromString:ei,get:function(e,t,n){let r="string"==typeof t?ei(t):t;if(!Array.isArray(r))throw Error("Path must be an array or a string");let i=e;for(let e=0;e<r.length;e++){let t=r[e];if(ee(t)){if(!Array.isArray(i))return n;i=i[t]}if(et(t)){if(!Array.isArray(i))return n;i=i.find(e=>e._key===t._key)}if("string"==typeof t&&(i="object"==typeof i&&null!==i?i[t]:void 0),typeof i>"u")return n}return i},isIndexSegment:ee,isIndexTuple:en,isKeySegment:et,reKeySegment:J,toString:er});let es="drafts.";function el(e){return{lang:e?.lang??p?.lang,message:e?.message,abortEarly:e?.abortEarly??p?.abortEarly,abortPipeEarly:e?.abortPipeEarly??p?.abortPipeEarly,skipPipe:e?.skipPipe}}function eu(e,t,n,r,i){var o,a,s,l;let u;let c=i&&"input"in i?i.input:n.value,d=i?.expected??e.expects,f=i?.received??("object"==(u=typeof c)&&(u=(c&&Object.getPrototypeOf(c)?.constructor?.name)??"null"),"string"===u?`"${c}"`:"number"===u||"bigint"===u||"boolean"===u?`${c}`:u),p={kind:e.kind,type:e.type,input:c,expected:d,received:f,message:`Invalid ${t}: ${d?`Expected ${d} but r`:"R"}eceived ${f}`,requirement:e.requirement,path:i?.path,issues:i?.issues,lang:r.lang,abortEarly:r.abortEarly,abortPipeEarly:r.abortPipeEarly,skipPipe:r.skipPipe},g="schema"===e.kind,v=e.message??(o=e.reference,a=p.lang,m?.get(o)?.get(a))??(g?(s=p.lang,y?.get(s)):null)??r.message??(l=p.lang,h?.get(l));v&&(p.message="function"==typeof v?v(p):v),g&&(n.typed=!1),n.issues?n.issues.push(p):n.issues=[p]}var ec=class extends Error{issues;constructor(e){super(e[0].message),this.name="ValiError",this.issues=e}};function ed(e,t){return{kind:"schema",type:"object",reference:ed,expects:"Object",async:!1,entries:e,message:t,_run(e,t){let n=e.value;if(n&&"object"==typeof n)for(let r in e.typed=!0,e.value={},this.entries){let i=n[r],o=this.entries[r]._run({typed:!1,value:i},t);if(o.issues){let a={type:"object",origin:"value",input:n,key:r,value:i};for(let t of o.issues)t.path?t.path.unshift(a):t.path=[a],e.issues?.push(t);if(e.issues||(e.issues=o.issues),t.abortEarly){e.typed=!1;break}}o.typed||(e.typed=!1),(void 0!==o.value||r in n)&&(e.value[r]=o.value)}else eu(this,"type",e,t);return e}}}function ef(e,...t){let n={kind:"schema",type:"optional",reference:ef,expects:`${e.expects} | undefined`,async:!1,wrapped:e,_run(e,t){return void 0===e.value&&("default"in this&&(e.value="function"==typeof this.default?this.default(e,t):this.default),void 0===e.value)?(e.typed=!0,e):this.wrapped._run(e,t)}};return 0 in t&&(n.default=t[0]),n}function ep(e){return{kind:"schema",type:"string",reference:ep,expects:"string",async:!1,message:e,_run(e,t){return"string"==typeof e.value?e.typed=!0:eu(this,"type",e,t),e}}}function eh(e,t,n){let r=e._run({typed:!1,value:t},el(n));return{typed:r.typed,success:!r.issues,output:r.value,issues:r.issues}}let ey=/^([\w-]+):(0|[1-9][0-9]*)$/,em=/^([\w-]+):([0-9]+),([0-9]+)$/,eg=/^([\w-]+):([\w-]+)$/,ev="drafts.",eb=function(...e){return{...e[0],pipe:e,_run(t,n){for(let r=0;r<e.length;r++){t=e[r]._run(t,n);let i=e[r+1];if(n.skipPipe||t.issues&&(n.abortEarly||n.abortPipeEarly||i?.kind==="schema"||i?.kind==="transformation")){t.typed=!1;break}}return t}}}(ep(),function e(t,n){return{kind:"validation",type:"min_length",reference:e,async:!1,expects:`>=${t}`,requirement:t,message:n,_run(e,t){return e.typed&&e.value.length<this.requirement&&eu(this,"length",e,t,{received:`${e.value.length}`}),e}}}(1)),ew=ef(eb),ex=ed({baseUrl:eb,dataset:ew,id:eb,path:eb,projectId:ew,tool:ew,type:ew,workspace:ew,isDraft:ef(ep())}),eE=ed({origin:eb,href:eb,data:ef(function e(t,n,r){return{kind:"schema",type:"record",reference:e,expects:"Object",async:!1,key:t,value:n,message:r,_run(e,t){let n=e.value;if(n&&"object"==typeof n){for(let r in e.typed=!0,e.value={},n)if("__proto__"!==r&&"prototype"!==r&&"constructor"!==r){let i=n[r],o=this.key._run({typed:!1,value:r},t);if(o.issues){let a={type:"record",origin:"key",input:n,key:r,value:i};for(let t of o.issues)t.path=[a],e.issues?.push(t);if(e.issues||(e.issues=o.issues),t.abortEarly){e.typed=!1;break}}let a=this.value._run({typed:!1,value:i},t);if(a.issues){let o={type:"record",origin:"value",input:n,key:r,value:i};for(let t of a.issues)t.path?t.path.unshift(o):t.path=[o],e.issues?.push(t);if(e.issues||(e.issues=a.issues),t.abortEarly){e.typed=!1;break}}o.typed&&a.typed||(e.typed=!1),o.typed&&(e.value[o.value]=a.value)}}else eu(this,"type",e,t);return e}}}(ep(),function e(){return{kind:"schema",type:"unknown",reference:e,expects:"unknown",async:!1,_run:e=>(e.typed=!0,e)}}()))});function e_(e){let t=eh(ex,e);if(t.success)return t.output;let n=eh(eE,e);if(n.success)try{let e=new URL(n.output.href,typeof document>"u"?"https://example.com":location.origin);return e.searchParams.size>0?function(e,t,n){let r=e._run({typed:!1,value:t},el(void 0));if(r.issues)throw new ec(r.issues);return r.value}(ex,Object.fromEntries(e.searchParams.entries())):n.output}catch(e){return console.error("Failed to parse sanity node",e),n.output}}let ek=/_key\s*==\s*['"](.*)['"]/,ej=/^\d*:\d*$/;function eS(e){return"number"==typeof e||"string"==typeof e&&/^\[\d+\]$/.test(e)}function eI(e){return"string"==typeof e?ek.test(e.trim()):"object"==typeof e&&"_key"in e}let eO=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,eA=/_key\s*==\s*['"](.*)['"]/;function eP(e,t,n){let r="string"==typeof t?function(e){if("string"!=typeof e)throw Error("Path is not a string");let t=e.match(eO);if(!t)throw Error("Invalid path string");return t.map(eM)}(t):t;if(!Array.isArray(r))throw Error("Path must be an array or a string");let i=e;for(let e=0;e<r.length;e++){let t=r[e];if(eS(t)){if(!Array.isArray(i))return n;i=i[t]}if(eI(t)){if(!Array.isArray(i))return n;i=i.find(e=>e._key===t._key)}if("string"==typeof t&&(i="object"==typeof i&&null!==i?i[t]:void 0),typeof i>"u")return n}return i}function eM(e){return eS(e)?Number(e.replace(/[^\d]/g,"")):eI(e)?{_key:e.match(eA)[1]}:!function(e){if("string"==typeof e&&ej.test(e))return!0;if(!Array.isArray(e)||2!==e.length)return!1;let[t,n]=e;return!("number"!=typeof t&&""!==t||"number"!=typeof n&&""!==n)}(e)?e:function(e){let[t,n]=e.split(":").map(e=>""===e?e:Number(e));return[t,n]}(e)}function e$(e){return e.startsWith(ev)}function eR(e){return e$(e)?e:ev+e}function eC(e){return e$(e)?e.slice(ev.length):e}function eT(){return(0,x.useSyncExternalStore)(eN,eD,eL)}function eL(){return Z}function eD(){return K}function eN(e){return Y.add(e),()=>Y.delete(e)}function eF(e,t){let n;return(...r)=>{clearTimeout(n),n=setTimeout(()=>{e.apply(e,r)},t)}}function ez(e,t){let n=window.self!==window.top||window.opener;if(t===Z||!n)throw Error("The `useDocuments` hook cannot be used in this context");let r=eR(e),i=eC(e),o=t.getSnapshot().context?.documents,a=o?.[r],s=o?.[i],l=a||s;if(!l)throw Error(`Document "${e}" not found`);let u=a.getSnapshot().context?.local||s.getSnapshot().context?.local,c=new Promise(e=>{if(u)e(u);else{let t=l.on("ready",n=>{let{snapshot:r}=n;e(r||null),t.unsubscribe()})}});return{draftDoc:a,draftId:r,getSnapshot:()=>c,publishedDoc:s,publishedId:i,get snapshot(){if(!u)throw Error(`Snapshot for document "${e}" not found`);return u}}}function eU(){let e,t,n;let r=(0,w.c)(7),i=eT();r[0]!==i?(e=e=>({id:e,commit:()=>{let{draftDoc:t}=ez(e,i);t.send({type:"submit"})},get:t=>{let{snapshot:n}=ez(e,i);return t?eP(n,t):n},getSnapshot:function(e,t){let{getSnapshot:n}=ez(e,t);return n}(e,i),patch:async(t,n)=>{let r=ez(e,i),{draftDoc:o,draftId:a,getSnapshot:s,publishedId:l}=r,{commit:u=!0}=n||{},c=await ("function"==typeof t?t({draftId:a,publishedId:l,get snapshot(){return r.snapshot},getSnapshot:s}):t),d=await s();if(!d)throw Error(`Snapshot for document "${e}" not found`);o.send({type:"mutate",mutations:[{type:"createIfNotExists",document:{...d,_id:a}},{type:"patch",id:a,patches:U(c)}]}),u&&("object"==typeof u&&"debounce"in u?eF(()=>o.send({type:"submit"}),u.debounce)():o.send({type:"submit"}))}}),r[0]=i,r[1]=e):e=r[1];let o=e;r[2]!==i?(t=(e,t,n)=>{let{draftDoc:r}=ez(e,i),{commit:o}=n||{},a=void 0===o||o;r.send({type:"mutate",mutations:t}),a&&("object"==typeof a&&"debounce"in a?eF(()=>r.send({type:"submit"}),a.debounce)():r.send({type:"submit"}))},r[2]=i,r[3]=t):t=r[3];let a=t;return r[4]!==o||r[5]!==a?(n={getDocument:o,mutateDocument:a},r[4]=o,r[5]=a,r[6]=n):n=r[6],n}let eW=()=>{if(i)return i;i=[];for(let e=0;e<256;++e)i[e]=(e+256).toString(16).slice(1);return i};function eV(e){let t=eW();return(function(e=16){let t=new Uint8Array(e);return function(e){let t="undefined"!=typeof window&&"crypto"in window?window.crypto:globalThis.crypto;if(!t||!t.getRandomValues)throw Error("WebCrypto not available in this environment");t.getRandomValues(e)}(t),t})(e).reduce((e,n)=>e+t[n],"").slice(0,e)}function eq(e){let t,n;let r="string"==typeof e?e:e.path,i=r.lastIndexOf("."),o=r.substring(i+1,r.length);if(!o.indexOf("["))throw Error("Invalid path: not an array");let a=r.lastIndexOf("["),s=r.substring(0,a);if(o.includes("_key")){let e=o.indexOf('"')+1,r=o.indexOf('"',e);t=o.substring(e,r),n=!0}else{let e=o.indexOf("[")+1,r=o.indexOf("]",e);t=o.substring(e,r),n=!1}if(!s||!t)throw Error("Invalid path");return{path:s,key:t,hasExplicitKey:n}}async function eB(e,t,n){if(!e.type)throw Error("Node type is missing");let{path:r,key:i}=eq(e),o=await t.getSnapshot(),a=eP(o,r),s=eP(o,e.path),l=a.findIndex(e=>e._key===i),u=-1,c="before";if("first"===n){if(0===l)return[];u=0,c="before"}else if("last"===n){if(l===a.length-1)return[];u=-1,c="after"}else if("next"===n){if(l===a.length-1)return[];u=l,c="after"}else if("previous"===n){if(0===l)return[];u=l-1,c="before"}return[G(r,q(l,l+1)),G(r,V(s,c,u))]}var eG={0:8203,1:8204,2:8205,3:8290,4:8291,5:8288,6:65279,7:8289,8:119155,9:119156,a:119157,b:119158,c:119159,d:119160,e:119161,f:119162},eH={0:8203,1:8204,2:8205,3:65279},eZ=[,,,,].fill(String.fromCodePoint(eH[0])).join(""),eK=Object.fromEntries(Object.entries(eH).map(e=>e.reverse())),eY=Object.fromEntries(Object.entries(eG).map(e=>e.reverse())),eX=`${Object.values(eG).map(e=>`\\u{${e.toString(16)}}`).join("")}`,eJ=RegExp(`[${eX}]{4,}`,"gu");let eQ=[];for(let e=0;e<256;++e)eQ.push((e+256).toString(16).slice(1));let e0=new Uint8Array(16);var e1={randomUUID:"u">typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)};function e2(e){let t=e.getBoundingClientRect();return{x:t.x+scrollX,y:t.y+scrollY,w:t.width,h:t.height}}function e3(e,t,n){return"x"===n?{x:e.x+t,y:e.y,w:e.w-2*t,h:e.h}:{x:e.x,y:e.y+t,w:e.w,h:e.h-2*t}}function e5(e,t){return e.x===t.x&&e.y===t.y&&e.w===t.w&&e.h===t.h}function e4(e,t){let n=e.x-t.x,r=e.y-t.y;return Math.sqrt(n*n+r*r)}function e8(e,t,n){let r,i;let o={x:e.x1,y:e.y1};if(t.some(e=>(function(e,t){let n=e.x>=t.x&&e.x<=t.x+t.w,r=e.y>=t.y&&e.y<=t.y+t.h;return n&&r})(o,e3(e,Math.min(e.w,e.h)/10,"horizontal"===n?"x":"y"))))return null;for(let a of t){let t=function(e,t){let n=[{x1:t.x,y1:t.y,x2:t.x+t.w,y2:t.y},{x1:t.x+t.w,y1:t.y,x2:t.x+t.w,y2:t.y+t.h},{x1:t.x+t.w,y1:t.y+t.h,x2:t.x,y2:t.y+t.h},{x1:t.x,y1:t.y+t.h,x2:t.x,y2:t.y}],r=[];for(let t=0;t<n.length;t++){let i=function(e,t){let{x1:n,y1:r,x2:i,y2:o}=e,{x1:a,y1:s,x2:l,y2:u}=t;if(n===i&&r===o||a===l&&s===u)return!1;let c=(u-s)*(i-n)-(l-a)*(o-r);if(0===c)return!1;let d=((l-a)*(r-s)-(u-s)*(n-a))/c,f=((i-n)*(r-s)-(o-r)*(n-a))/c;return!(d<0)&&!(d>1)&&!(f<0)&&!(f>1)&&{x:n+d*(i-n),y:r+d*(o-r)}}(e,n[t]);if(i){let e=!1;for(let t=0;t<r.length;t++)r[t].x===i.x&&r[t].y===i.y&&(e=!0);e||r.push(i)}}return 0!==r.length&&r.sort((t,n)=>e4(t,{x:e.x1,y:e.y1})-e4(n,{x:e.x1,y:e.y1}))}(e,e3(a,Math.min(a.w,a.h)/10,"horizontal"===n?"x":"y"));if(t){let e=t[0];r?e4(o,e)<e4(o,r)&&(r=e,i=a):(r=e,i=a)}}return i||null}function e6(e,t,n){let{x:r,y:i,w:o,h:a}=e,{x:s,y:l}=n;return{x:s+(r-s)*t,y:l+(i-l)*t,w:o*t,h:a*t}}function e9(e){let t=Math.max(0,Math.min(...e.map(e=>e.y))),n=Math.min(document.body.scrollHeight,Math.max(...e.map(e=>e.y+e.h)));return{min:t,max:n,height:n-t}}function e7(e,t){return t.find(t=>e5(e2(t.elements.element),e))?.sanity}function te(e,t,n){return Object.values(t).every(e=>null===e)?null:"horizontal"===n?{left:t.left?{rect:t.left,sanity:e7(t.left,e)}:null,right:t.right?{rect:t.right,sanity:e7(t.right,e)}:null}:{top:t.top?{rect:t.top,sanity:e7(t.top,e)}:null,bottom:t.bottom?{rect:t.bottom,sanity:e7(t.bottom,e)}:null}}function tt(e){let t=document.body.getBoundingClientRect();return{x:Math.max(t.x,Math.min(e.clientX,t.x+t.width)),y:e.clientY+window.scrollY}}function tn(e,t,n){let r=e2(t),i=[...t.querySelectorAll(":where(h1, h2, h3, h4, p, a, img, span, button):not(:has(*))")];e.x<=r.x&&(e.x=r.x),e.x>=r.x+r.w&&(e.x=r.x+r.w),e.y>=r.y+r.h&&(e.y=r.y+r.h),e.y<=r.y&&(e.y=r.y);let o=i.map(e=>{let t=e6(e2(e),n,{x:r.x,y:r.y});return{x:t.x-r.x,y:t.y-r.y,w:t.w,h:t.h,tagName:e.tagName}});return{offsetX:(r.x-e.x)*n,offsetY:(r.y-e.y)*n,w:r.w*n,h:r.h*n,maxWidth:r.w*n*.75,childRects:o}}function tr(e){let t=function(e){let t=Math.max(0,Math.min(...e.map(e=>e.x))),n=Math.min(document.body.offsetWidth,Math.max(...e.map(e=>e.x+e.w)));return{min:t,max:n,width:n-t}}(e),n=e9(e),r=t.min>8&&t.min+t.width<=window.innerWidth-8,i=n.min>8&&n.min+n.height<=document.body.scrollHeight-8,o=r&&i;return{x:o?t.min-8:t.min,y:o?n.min-8:n.min,w:o?t.width+16:t.width,h:o?n.height+16:n.height}}async function ti(e,t,n,r,i,o){return new Promise(a=>{if(1===new DOMMatrix(window.getComputedStyle(t).transform).a)return;let s=n-window.innerHeight,l=scrollY;(e-=window.innerHeight/2)<0&&(e=0),t.addEventListener("transitionend",()=>{t.style.transition="none",t.style.transform="none",scrollTo({top:e,behavior:"instant"}),setTimeout(()=>{r({type:"overlay/dragEndMinimapTransition"}),r({type:"overlay/dragToggleMinimap",display:!1})},2*i),a()},{once:!0}),r({type:"overlay/dragStartMinimapTransition"}),t.style.transform=`translateY(${Math.max(l-e,-s+l)}px) scale(1)`,o&&(document.body.style.overflow=o.body.overflow,document.body.style.height=o.body.height,document.documentElement.style.overflow=o.documentElement.overflow,document.documentElement.style.height=o.documentElement.height)})}let to=!1,ta={x:0,y:0},ts={x:0,y:0},tl=typeof document>"u"?0:document.documentElement.scrollHeight,tu=null,tc=e=>e instanceof HTMLElement||e instanceof SVGElement,td=e=>e&&tc(e)?e.dataset?.sanityOverlayElement?e:td(e.parentElement):null;function tf(e,t=!1){return eJ.lastIndex=0,eJ.test(e)?function(e,t=!1){try{let n=function(e){let t=e.match(eJ);if(t)return function(e,t=!1){let n=Array.from(e);if(n.length%2==0){if(n.length%4||!e.startsWith(eZ))return function(e,t){var n;let r=[];for(let t=.5*e.length;t--;){let n=`${eY[e[2*t].codePointAt(0)]}${eY[e[2*t+1].codePointAt(0)]}`;r.unshift(String.fromCharCode(parseInt(n,16)))}let i=[],o=[r.join("")],a=10;for(;o.length;){let e=o.shift();try{if(i.push(JSON.parse(e)),t)break}catch(r){if(!a--)throw r;let t=+(null==(n=r.message.match(/\sposition\s(\d+)$/))?void 0:n[1]);if(!t)throw r;o.unshift(e.substring(0,t),e.substring(t))}}return i}(n,t)}else throw Error("Encoded data has invalid length");let r=[];for(let e=.25*n.length;e--;){let t=n.slice(4*e,4*e+4).map(e=>eK[e.codePointAt(0)]).join("");r.unshift(String.fromCharCode(parseInt(t,4)))}if(t){r.shift();let e=r.indexOf("\0");return -1===e&&(e=r.length),[JSON.parse(r.slice(0,e).join(""))]}return r.join("").split("\0").filter(Boolean).map(e=>JSON.parse(e))}(t[0],!0)[0]}(e);return n&&"sanity.io"===n.origin?(t&&(n.href=n.href?.replace(".alt","")),n):null}catch(t){return console.error("Failed to decode stega for string: ",e,"with the original error: ",t),null}}(e,t):null}let tp=e=>e.nodeType===Node.ELEMENT_NODE,th=e=>"IMG"===e.tagName,ty=e=>"TIME"===e.tagName,tm=e=>"SVG"===e.tagName.toUpperCase();function tg(e){return"path"in e}function tv(e){let t=e.lastIndexOf(".");return e.substring(t,e.length).includes("[")}function tb(e){if(!tv(e))return null;let t=e.split(".");return t[t.length-1]=t[t.length-1].replace(/\[.*?\]/g,"[]"),t.join(".")}function tw(e,t){return!(!tv(e.path)||!tv(t.path))&&tb(e.path)===tb(t.path)}function tx(e,t,n,r){if(!e.getAttribute("data-sanity")||e.getAttribute("data-sanity-drag-disable")||!t||!tg(t)||!tv(t.path))return null;let i=e.getAttribute("data-sanity-drag-group"),o=[...n].reduce((e,n)=>{let o=r.get(n),a=n.getAttribute("data-sanity-drag-disable"),s=n.getAttribute("data-sanity-drag-group"),l=null!==n.getAttribute("data-sanity");return o&&!a&&tg(o.sanity)&&tw(t,o.sanity)&&(null===i||i===s)&&l&&e.push(o),e},[]);return o.length<=1?null:o}let tE=(0,x.createContext)(null),t_=e=>"object"==typeof e&&null!=e&&1===e.nodeType,tk=(e,t)=>(!t||"hidden"!==e)&&"visible"!==e&&"clip"!==e,tj=(e,t)=>{if(e.clientHeight<e.scrollHeight||e.clientWidth<e.scrollWidth){let n=getComputedStyle(e,null);return tk(n.overflowY,t)||tk(n.overflowX,t)||(e=>{let t=(e=>{if(!e.ownerDocument||!e.ownerDocument.defaultView)return null;try{return e.ownerDocument.defaultView.frameElement}catch(e){return null}})(e);return!!t&&(t.clientHeight<e.scrollHeight||t.clientWidth<e.scrollWidth)})(e)}return!1},tS=(e,t,n,r,i,o,a,s)=>o<e&&a>t||o>e&&a<t?0:o<=e&&s<=n||a>=t&&s>=n?o-e-r:a>t&&s<n||o<e&&s>n?a-t+i:0,tI=e=>{let t=e.parentElement;return null==t?e.getRootNode().host||null:t},tO=(e,t)=>{var n,r,i,o;if("undefined"==typeof document)return[];let{scrollMode:a,block:s,inline:l,boundary:u,skipOverflowHiddenElements:c}=t,d="function"==typeof u?u:e=>e!==u;if(!t_(e))throw TypeError("Invalid target");let f=document.scrollingElement||document.documentElement,p=[],h=e;for(;t_(h)&&d(h);){if((h=tI(h))===f){p.push(h);break}null!=h&&h===document.body&&tj(h)&&!tj(document.documentElement)||null!=h&&tj(h,c)&&p.push(h)}let y=null!=(r=null==(n=window.visualViewport)?void 0:n.width)?r:innerWidth,m=null!=(o=null==(i=window.visualViewport)?void 0:i.height)?o:innerHeight,{scrollX:g,scrollY:v}=window,{height:b,width:w,top:x,right:E,bottom:_,left:k}=e.getBoundingClientRect(),{top:j,right:S,bottom:I,left:O}=(e=>{let t=window.getComputedStyle(e);return{top:parseFloat(t.scrollMarginTop)||0,right:parseFloat(t.scrollMarginRight)||0,bottom:parseFloat(t.scrollMarginBottom)||0,left:parseFloat(t.scrollMarginLeft)||0}})(e),A="start"===s||"nearest"===s?x-j:"end"===s?_+I:x+b/2-j+I,P="center"===l?k+w/2-O+S:"end"===l?E+S:k-O,M=[];for(let e=0;e<p.length;e++){let t=p[e],{height:n,width:r,top:i,right:o,bottom:u,left:c}=t.getBoundingClientRect();if("if-needed"===a&&x>=0&&k>=0&&_<=m&&E<=y&&x>=i&&_<=u&&k>=c&&E<=o)break;let d=getComputedStyle(t),h=parseInt(d.borderLeftWidth,10),j=parseInt(d.borderTopWidth,10),S=parseInt(d.borderRightWidth,10),I=parseInt(d.borderBottomWidth,10),O=0,$=0,R="offsetWidth"in t?t.offsetWidth-t.clientWidth-h-S:0,C="offsetHeight"in t?t.offsetHeight-t.clientHeight-j-I:0,T="offsetWidth"in t?0===t.offsetWidth?0:r/t.offsetWidth:0,L="offsetHeight"in t?0===t.offsetHeight?0:n/t.offsetHeight:0;if(f===t)O="start"===s?A:"end"===s?A-m:"nearest"===s?tS(v,v+m,m,j,I,v+A,v+A+b,b):A-m/2,$="start"===l?P:"center"===l?P-y/2:"end"===l?P-y:tS(g,g+y,y,h,S,g+P,g+P+w,w),O=Math.max(0,O+v),$=Math.max(0,$+g);else{O="start"===s?A-i-j:"end"===s?A-u+I+C:"nearest"===s?tS(i,u,n,j,I+C,A,A+b,b):A-(i+n/2)+C/2,$="start"===l?P-c-h:"center"===l?P-(c+r/2)+R/2:"end"===l?P-o+S+R:tS(c,o,r,h,S+R,P,P+w,w);let{scrollLeft:e,scrollTop:a}=t;O=0===L?0:Math.max(0,Math.min(a+O/L,t.scrollHeight-n/L+C)),$=0===T?0:Math.max(0,Math.min(e+$/T,t.scrollWidth-r/T+R)),A+=a-O,P+=e-$}M.push({el:t,top:O,left:$})}return M},tA=e=>!1===e?{block:"end",inline:"nearest"}:e===Object(e)&&0!==Object.keys(e).length?e:{block:"start",inline:"nearest"};var tP=n(48769),tM=n(5853),t$=n(78743),tR=n(37219),tC=(0,n(37169).d)(function(e){return function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"}}),tT=n(30325),tL=n(99287),tD=function(e){function t(){var t=e.call(this)||this;return t.closed=!1,t.currentObservers=null,t.observers=[],t.isStopped=!1,t.hasError=!1,t.thrownError=null,t}return(0,tM.ZT)(t,e),t.prototype.lift=function(e){var t=new tN(this,this);return t.operator=e,t},t.prototype._throwIfClosed=function(){if(this.closed)throw new tC},t.prototype.next=function(e){var t=this;(0,tL.x)(function(){var n,r;if(t._throwIfClosed(),!t.isStopped){t.currentObservers||(t.currentObservers=Array.from(t.observers));try{for(var i=(0,tM.XA)(t.currentObservers),o=i.next();!o.done;o=i.next())o.value.next(e)}catch(e){n={error:e}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(n)throw n.error}}}})},t.prototype.error=function(e){var t=this;(0,tL.x)(function(){if(t._throwIfClosed(),!t.isStopped){t.hasError=t.isStopped=!0,t.thrownError=e;for(var n=t.observers;n.length;)n.shift().error(e)}})},t.prototype.complete=function(){var e=this;(0,tL.x)(function(){if(e._throwIfClosed(),!e.isStopped){e.isStopped=!0;for(var t=e.observers;t.length;)t.shift().complete()}})},t.prototype.unsubscribe=function(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null},Object.defineProperty(t.prototype,"observed",{get:function(){var e;return(null===(e=this.observers)||void 0===e?void 0:e.length)>0},enumerable:!1,configurable:!0}),t.prototype._trySubscribe=function(t){return this._throwIfClosed(),e.prototype._trySubscribe.call(this,t)},t.prototype._subscribe=function(e){return this._throwIfClosed(),this._checkFinalizedStatuses(e),this._innerSubscribe(e)},t.prototype._innerSubscribe=function(e){var t=this,n=this.hasError,r=this.isStopped,i=this.observers;return n||r?tR.Lc:(this.currentObservers=null,i.push(e),new tR.w0(function(){t.currentObservers=null,(0,tT.P)(i,e)}))},t.prototype._checkFinalizedStatuses=function(e){var t=this.hasError,n=this.thrownError,r=this.isStopped;t?e.error(n):r&&e.complete()},t.prototype.asObservable=function(){var e=new t$.y;return e.source=this,e},t.create=function(e,t){return new tN(e,t)},t}(t$.y),tN=function(e){function t(t,n){var r=e.call(this)||this;return r.destination=t,r.source=n,r}return(0,tM.ZT)(t,e),t.prototype.next=function(e){var t,n;null===(n=null===(t=this.destination)||void 0===t?void 0:t.next)||void 0===n||n.call(t,e)},t.prototype.error=function(e){var t,n;null===(n=null===(t=this.destination)||void 0===t?void 0:t.error)||void 0===n||n.call(t,e)},t.prototype.complete=function(){var e,t;null===(t=null===(e=this.destination)||void 0===e?void 0:e.complete)||void 0===t||t.call(e)},t.prototype._subscribe=function(e){var t,n;return null!==(n=null===(t=this.source)||void 0===t?void 0:t.subscribe(e))&&void 0!==n?n:tR.Lc},t}(tD),tF={now:function(){return(tF.delegate||Date).now()},delegate:void 0},tz=function(e){function t(t,n,r){void 0===t&&(t=1/0),void 0===n&&(n=1/0),void 0===r&&(r=tF);var i=e.call(this)||this;return i._bufferSize=t,i._windowTime=n,i._timestampProvider=r,i._buffer=[],i._infiniteTimeWindow=!0,i._infiniteTimeWindow=n===1/0,i._bufferSize=Math.max(1,t),i._windowTime=Math.max(1,n),i}return(0,tM.ZT)(t,e),t.prototype.next=function(t){var n=this.isStopped,r=this._buffer,i=this._infiniteTimeWindow,o=this._timestampProvider,a=this._windowTime;!n&&(r.push(t),i||r.push(o.now()+a)),this._trimBuffer(),e.prototype.next.call(this,t)},t.prototype._subscribe=function(e){this._throwIfClosed(),this._trimBuffer();for(var t=this._innerSubscribe(e),n=this._infiniteTimeWindow,r=this._buffer.slice(),i=0;i<r.length&&!e.closed;i+=n?1:2)e.next(r[i]);return this._checkFinalizedStatuses(e),t},t.prototype._trimBuffer=function(){var e=this._bufferSize,t=this._timestampProvider,n=this._buffer,r=this._infiniteTimeWindow,i=(r?1:2)*e;if(e<1/0&&i<n.length&&n.splice(0,n.length-i),!r){for(var o=t.now(),a=0,s=1;s<n.length&&n[s]<=o;s+=2)a=s;a&&n.splice(0,a+1)}},t}(tD),tU=n(85315),tW=n(15531),tV=n(96750),tq=n(66692),tB=n(94146),tG=n(48522);function tH(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var r=(0,tB.yG)(t),i=(0,tB._6)(t,1/0);return t.length?1===t.length?(0,tV.Xf)(t[0]):(void 0===(e=i)&&(e=1/0),(0,tU.z)(tW.y,e))((0,tG.D)(t,r)):tq.E}function tZ(e){let[t,n]=e;return[t,n]}function tK(e,t){let n=e,r=t,i=n.length,o=r.length;if(0===i||0===o)return 0;i>o?n=n.substring(i-o):i<o&&(r=r.substring(0,i));let a=Math.min(i,o);if(n===r)return a;let s=0,l=1;for(let e=0;-1!==e;){let t=n.substring(a-l);if(-1===(e=r.indexOf(t)))break;l+=e,(0===e||n.substring(a-l)===r.substring(0,l))&&(s=l,l++)}return s}function tY(e,t){if(!e||!t||e[0]!==t[0])return 0;let n=0,r=Math.min(e.length,t.length),i=r,o=0;for(;n<i;)e.substring(o,i)===t.substring(o,i)?o=n=i:r=i,i=Math.floor((r-n)/2+n);return i}function tX(e,t){if(!e||!t||e[e.length-1]!==t[t.length-1])return 0;let n=0,r=Math.min(e.length,t.length),i=r,o=0;for(;n<i;)e.substring(e.length-i,e.length-o)===t.substring(t.length-i,t.length-o)?o=n=i:r=i,i=Math.floor((r-n)/2+n);return i}function tJ(e){let t=e.charCodeAt(0);return t>=56320&&t<=57343}function tQ(e,t,n,r,i){let o=e.substring(0,n),a=t.substring(0,r),s=e.substring(n),l=t.substring(r),u=t9(o,a,{checkLines:!1,deadline:i}),c=t9(s,l,{checkLines:!1,deadline:i});return u.concat(c)}function t0(e,t,n){let r=e.slice(n,n+Math.floor(e.length/4)),i=-1,o="",a,s,l,u;for(;-1!==(i=t.indexOf(r,i+1));){let r=tY(e.slice(n),t.slice(i)),c=tX(e.slice(0,n),t.slice(0,i));o.length<c+r&&(o=t.slice(i-c,i)+t.slice(i,i+r),a=e.slice(0,n-c),s=e.slice(n+r),l=t.slice(0,i-c),u=t.slice(i+r))}return 2*o.length>=e.length?[a||"",s||"",l||"",u||"",o||""]:null}var t1=Object.defineProperty,t2=Object.getOwnPropertySymbols,t3=Object.prototype.hasOwnProperty,t5=Object.prototype.propertyIsEnumerable,t4=(e,t,n)=>t in e?t1(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,t8=(e,t)=>{for(var n in t||(t={}))t3.call(t,n)&&t4(e,n,t[n]);if(t2)for(var n of t2(t))t5.call(t,n)&&t4(e,n,t[n]);return e};function t6(e,t,n){var r,i;let o;if(null===e||null===t)throw Error("Null input. (diff)");let a=t9(e,t,t8({checkLines:!0,deadline:(i=(r=n||{}).timeout||1,o=1,"u">typeof i&&(o=i<=0?Number.MAX_VALUE:i),Date.now()+1e3*o)},r));return function(e){for(let t=0;t<e.length;t++){let[n,r]=e[t];if(0===r.length)continue;let i=r[0];(function(e){let t=e.charCodeAt(0);return t>=55296&&t<=56319})(r[r.length-1])&&0===n&&nt(e,t,1),tJ(i)&&0===n&&nt(e,t,-1)}for(let t=0;t<e.length;t++)0===e[t][1].length&&e.splice(t,1)}(a),a}function t9(e,t,n){let r=e,i=t;if(r===i)return r?[[0,r]]:[];let o=tY(r,i),a=r.substring(0,o);o=tX(r=r.substring(o),i=i.substring(o));let s=r.substring(r.length-o),l=function(e,t,n){let r;if(!e)return[[1,t]];if(!t)return[[-1,e]];let i=e.length>t.length?e:t,o=e.length>t.length?t:e,a=i.indexOf(o);if(-1!==a)return r=[[1,i.substring(0,a)],[0,o],[1,i.substring(a+o.length)]],e.length>t.length&&(r[0][0]=-1,r[2][0]=-1),r;if(1===o.length)return[[-1,e],[1,t]];let s=function(e,t,n=1){let r,i,o,a,s;if(n<=0)return null;let l=e.length>t.length?e:t,u=e.length>t.length?t:e;if(l.length<4||2*u.length<l.length)return null;let c=t0(l,u,Math.ceil(l.length/4)),d=t0(l,u,Math.ceil(l.length/2));if(c&&d)r=c[4].length>d[4].length?c:d;else{if(!c&&!d)return null;d?c||(r=d):r=c}if(!r)throw Error("Unable to find a half match.");return e.length>t.length?(i=r[0],o=r[1],a=r[2],s=r[3]):(a=r[0],s=r[1],i=r[2],o=r[3]),[i,o,a,s,r[4]]}(e,t);if(s){let e=s[0],t=s[1],r=s[2],i=s[3],o=s[4],a=t9(e,r,n),l=t9(t,i,n);return a.concat([[0,o]],l)}return n.checkLines&&e.length>100&&t.length>100?function(e,t,n){let r=e,i=t,o=function(e,t){let n=[],r={};function i(e){let t="",i=0,a=-1,s=n.length;for(;a<e.length-1;){-1===(a=e.indexOf(`
`,i))&&(a=e.length-1);let l=e.slice(i,a+1);(r.hasOwnProperty?r.hasOwnProperty(l):void 0!==r[l])?t+=String.fromCharCode(r[l]):(s===o&&(l=e.slice(i),a=e.length),t+=String.fromCharCode(s),r[l]=s,n[s++]=l),i=a+1}return t}n[0]="";let o=4e4,a=i(e);return o=65535,{chars1:a,chars2:i(t),lineArray:n}}(r,i);r=o.chars1,i=o.chars2;let a=o.lineArray,s=t9(r,i,{checkLines:!1,deadline:n.deadline});(function(e,t){for(let n=0;n<e.length;n++){let r=e[n][1],i=[];for(let e=0;e<r.length;e++)i[e]=t[r.charCodeAt(e)];e[n][1]=i.join("")}})(s,a),(s=nn(s)).push([0,""]);let l=0,u=0,c=0,d="",f="";for(;l<s.length;){switch(s[l][0]){case 1:c++,f+=s[l][1];break;case -1:u++,d+=s[l][1];break;case 0:if(u>=1&&c>=1){s.splice(l-u-c,u+c),l=l-u-c;let e=t9(d,f,{checkLines:!1,deadline:n.deadline});for(let t=e.length-1;t>=0;t--)s.splice(l,0,e[t]);l+=e.length}c=0,u=0,d="",f="";break;default:throw Error("Unknown diff operation.")}l++}return s.pop(),s}(e,t,n):function(e,t,n){let r=e.length,i=t.length,o=Math.ceil((r+i)/2),a=2*o,s=Array(a),l=Array(a);for(let e=0;e<a;e++)s[e]=-1,l[e]=-1;s[o+1]=0,l[o+1]=0;let u=r-i,c=u%2!=0,d=0,f=0,p=0,h=0;for(let y=0;y<o&&!(Date.now()>n);y++){for(let p=-y+d;p<=y-f;p+=2){let h;let m=o+p,g=(h=p===-y||p!==y&&s[m-1]<s[m+1]?s[m+1]:s[m-1]+1)-p;for(;h<r&&g<i&&e.charAt(h)===t.charAt(g);)h++,g++;if(s[m]=h,h>r)f+=2;else if(g>i)d+=2;else if(c){let i=o+u-p;if(i>=0&&i<a&&-1!==l[i]&&h>=r-l[i])return tQ(e,t,h,g,n)}}for(let d=-y+p;d<=y-h;d+=2){let f;let m=o+d,g=(f=d===-y||d!==y&&l[m-1]<l[m+1]?l[m+1]:l[m-1]+1)-d;for(;f<r&&g<i&&e.charAt(r-f-1)===t.charAt(i-g-1);)f++,g++;if(l[m]=f,f>r)h+=2;else if(g>i)p+=2;else if(!c){let i=o+u-d;if(i>=0&&i<a&&-1!==s[i]){let a=s[i],l=o+a-i;if(a>=(f=r-f))return tQ(e,t,a,l,n)}}}}return[[-1,e],[1,t]]}(e,t,n.deadline)}(r=r.substring(0,r.length-o),i=i.substring(0,i.length-o),n);return a&&l.unshift([0,a]),s&&l.push([0,s]),l=nu(l)}function t7(e,t,n){return 1===n?e+t:t+e}function ne(e,t){return 1===t?[e.substring(0,e.length-1),e[e.length-1]]:[e.substring(1),e[0]]}function nt(e,t,n){var r,i;let o=1===n?-1:1,a=null,s=null,l=t+n;for(;l>=0&&l<e.length&&(null===a||null===s);l+=n){let[r,i]=e[l];if(0!==i.length){if(1===r){null===a&&(a=l);continue}if(-1===r){null===s&&(s=l);continue}if(0===r){if(null===a&&null===s){let[r,i]=ne(e[t][1],n);e[t][1]=r,e[l][1]=t7(e[l][1],i,o);return}break}}}if(null!==a&&null!==s&&(r=a,i=s,1===n?e[r][1][e[r][1].length-1]===e[i][1][e[i][1].length-1]:e[r][1][0]===e[i][1][0])){let[r,i]=ne(e[a][1],o),[l]=ne(e[s][1],o);e[a][1]=r,e[s][1]=l,e[t][1]=t7(e[t][1],i,n);return}let[u,c]=ne(e[t][1],n);e[t][1]=u,null===a?(e.splice(l,0,[1,c]),null!==s&&s>=l&&s++):e[a][1]=t7(e[a][1],c,o),null===s?e.splice(l,0,[-1,c]):e[s][1]=t7(e[s][1],c,o)}function nn(e){let t=e.map(e=>tZ(e)),n=!1,r=[],i=0,o=null,a=0,s=0,l=0,u=0,c=0;for(;a<t.length;)0===t[a][0]?(r[i++]=a,s=u,l=c,u=0,c=0,o=t[a][1]):(1===t[a][0]?u+=t[a][1].length:c+=t[a][1].length,o&&o.length<=Math.max(s,l)&&o.length<=Math.max(u,c)&&(t.splice(r[i-1],0,[-1,o]),t[r[i-1]+1][0]=1,i--,a=--i>0?r[i-1]:-1,s=0,l=0,u=0,c=0,o=null,n=!0)),a++;for(n&&(t=nu(t)),t=nl(t),a=1;a<t.length;){if(-1===t[a-1][0]&&1===t[a][0]){let e=t[a-1][1],n=t[a][1],r=tK(e,n),i=tK(n,e);r>=i?(r>=e.length/2||r>=n.length/2)&&(t.splice(a,0,[0,n.substring(0,r)]),t[a-1][1]=e.substring(0,e.length-r),t[a+1][1]=n.substring(r),a++):(i>=e.length/2||i>=n.length/2)&&(t.splice(a,0,[0,e.substring(0,i)]),t[a-1][0]=1,t[a-1][1]=n.substring(0,n.length-i),t[a+1][0]=-1,t[a+1][1]=e.substring(i),a++),a++}a++}return t}let nr=/[^a-zA-Z0-9]/,ni=/\s/,no=/[\r\n]/,na=/\n\r?\n$/,ns=/^\r?\n\r?\n/;function nl(e){let t=e.map(e=>tZ(e));function n(e,t){if(!e||!t)return 6;let n=e.charAt(e.length-1),r=t.charAt(0),i=n.match(nr),o=r.match(nr),a=i&&n.match(ni),s=o&&r.match(ni),l=a&&n.match(no),u=s&&r.match(no),c=l&&e.match(na),d=u&&t.match(ns);return c||d?5:l||u?4:i&&!a&&s?3:a||s?2:i||o?1:0}let r=1;for(;r<t.length-1;){if(0===t[r-1][0]&&0===t[r+1][0]){let e=t[r-1][1],i=t[r][1],o=t[r+1][1],a=tX(e,i);if(a){let t=i.substring(i.length-a);e=e.substring(0,e.length-a),i=t+i.substring(0,i.length-a),o=t+o}let s=e,l=i,u=o,c=n(e,i)+n(i,o);for(;i.charAt(0)===o.charAt(0);){e+=i.charAt(0),i=i.substring(1)+o.charAt(0),o=o.substring(1);let t=n(e,i)+n(i,o);t>=c&&(c=t,s=e,l=i,u=o)}t[r-1][1]!==s&&(s?t[r-1][1]=s:(t.splice(r-1,1),r--),t[r][1]=l,u?t[r+1][1]=u:(t.splice(r+1,1),r--))}r++}return t}function nu(e){let t=e.map(e=>tZ(e));t.push([0,""]);let n=0,r=0,i=0,o="",a="",s;for(;n<t.length;)switch(t[n][0]){case 1:i++,a+=t[n][1],n++;break;case -1:r++,o+=t[n][1],n++;break;case 0:r+i>1?(0!==r&&0!==i&&(0!==(s=tY(a,o))&&(n-r-i>0&&0===t[n-r-i-1][0]?t[n-r-i-1][1]+=a.substring(0,s):(t.splice(0,0,[0,a.substring(0,s)]),n++),a=a.substring(s),o=o.substring(s)),0!==(s=tX(a,o))&&(t[n][1]=a.substring(a.length-s)+t[n][1],a=a.substring(0,a.length-s),o=o.substring(0,o.length-s))),n-=r+i,t.splice(n,r+i),o.length&&(t.splice(n,0,[-1,o]),n++),a.length&&(t.splice(n,0,[1,a]),n++),n++):0!==n&&0===t[n-1][0]?(t[n-1][1]+=t[n][1],t.splice(n,1)):n++,i=0,r=0,o="",a="";break;default:throw Error("Unknown diff operation")}""===t[t.length-1][1]&&t.pop();let l=!1;for(n=1;n<t.length-1;)0===t[n-1][0]&&0===t[n+1][0]&&(t[n][1].substring(t[n][1].length-t[n-1][1].length)===t[n-1][1]?(t[n][1]=t[n-1][1]+t[n][1].substring(0,t[n][1].length-t[n-1][1].length),t[n+1][1]=t[n-1][1]+t[n+1][1],t.splice(n-1,1),l=!0):t[n][1].substring(0,t[n+1][1].length)===t[n+1][1]&&(t[n-1][1]+=t[n+1][1],t[n][1]=t[n][1].substring(t[n+1][1].length)+t[n+1][1],t.splice(n+1,1),l=!0)),n++;return l&&(t=nu(t)),t}var nc=Object.defineProperty,nd=Object.getOwnPropertySymbols,nf=Object.prototype.hasOwnProperty,np=Object.prototype.propertyIsEnumerable,nh=(e,t,n)=>t in e?nc(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,ny=(e,t)=>{for(var n in t||(t={}))nf.call(t,n)&&nh(e,n,t[n]);if(nd)for(var n of nd(t))np.call(t,n)&&nh(e,n,t[n]);return e};let nm={threshold:.5,distance:1e3};function ng(e,t,n){if(null===e||null===t||null===n)throw Error("Null input. (match())");let r=Math.max(0,Math.min(n,e.length));return e===t?0:e.length?e.substring(r,r+t.length)===t?r:function(e,t,n,r={}){if(t.length>32)throw Error("Pattern too long for this browser.");let i=ny(ny({},nm),r),o=function(e){let t={};for(let n=0;n<e.length;n++)t[e.charAt(n)]=0;for(let n=0;n<e.length;n++)t[e.charAt(n)]|=1<<e.length-n-1;return t}(t);function a(e,r){let o=e/t.length,a=Math.abs(n-r);return i.distance?o+a/i.distance:a?1:o}let s=i.threshold,l=e.indexOf(t,n);-1!==l&&(s=Math.min(a(0,l),s),-1!==(l=e.lastIndexOf(t,n+t.length))&&(s=Math.min(a(0,l),s)));let u=1<<t.length-1;l=-1;let c,d,f=t.length+e.length,p=[];for(let r=0;r<t.length;r++){for(c=0,d=f;c<d;)a(r,n+d)<=s?c=d:f=d,d=Math.floor((f-c)/2+c);f=d;let i=Math.max(1,n-d+1),h=Math.min(n+d,e.length)+t.length,y=Array(h+2);y[h+1]=(1<<r)-1;for(let t=h;t>=i;t--){let c=o[e.charAt(t-1)];if(0===r?y[t]=(y[t+1]<<1|1)&c:y[t]=(y[t+1]<<1|1)&c|((p[t+1]|p[t])<<1|1)|p[t+1],y[t]&u){let e=a(r,t-1);if(e<=s){if(s=e,(l=t-1)>n)i=Math.max(1,2*n-l);else break}}}if(a(r+1,n)>s)break;p=y}return l}(e,t,r):-1}function nv(e){let t=[];for(let n=0;n<e.length;n++)1!==e[n][0]&&(t[n]=e[n][1]);return t.join("")}function nb(e){let t=[];for(let n=0;n<e.length;n++)-1!==e[n][0]&&(t[n]=e[n][1]);return t.join("")}function nw(e,t){let n=0,r=0,i=0,o=0,a;for(a=0;a<e.length&&(1!==e[a][0]&&(n+=e[a][1].length),-1!==e[a][0]&&(r+=e[a][1].length),!(n>t));a++)i=n,o=r;return e.length!==a&&-1===e[a][0]?o:o+(t-i)}function nx(e){let t=0;for(let n=0;n<e.length;n++){let r=e.codePointAt(n);if(typeof r>"u")throw Error("Failed to get codepoint");t+=nE(r)}return t}function nE(e){return e<=127?1:e<=2047?2:e<=65535?3:4}function n_(e,t){return{diffs:[],start1:e,start2:t,utf8Start1:e,utf8Start2:t,length1:0,length2:0,utf8Length1:0,utf8Length2:0}}var nk=Object.defineProperty,nj=Object.getOwnPropertySymbols,nS=Object.prototype.hasOwnProperty,nI=Object.prototype.propertyIsEnumerable,nO=(e,t,n)=>t in e?nk(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,nA=(e,t)=>{for(var n in t||(t={}))nS.call(t,n)&&nO(e,n,t[n]);if(nj)for(var n of nj(t))nI.call(t,n)&&nO(e,n,t[n]);return e};let nP={margin:4};function nM(e={}){return nA(nA({},nP),e)}function n$(e,t,n){if(0===t.length)return[];let r=[],i=n_(0,0),o=0,a=0,s=0,l=0,u=0,c=e,d=e;for(let e=0;e<t.length;e++){let f=t[e],[p,h]=f,y=h.length,m=nx(h);switch(o||0===p||(i.start1=a,i.start2=s,i.utf8Start1=l,i.utf8Start2=u),p){case 1:i.diffs[o++]=f,i.length2+=y,i.utf8Length2+=m,d=d.substring(0,s)+h+d.substring(s);break;case -1:i.length1+=y,i.utf8Length1+=m,i.diffs[o++]=f,d=d.substring(0,s)+d.substring(s+y);break;case 0:y<=2*n.margin&&o&&t.length!==e+1?(i.diffs[o++]=f,i.length1+=y,i.length2+=y,i.utf8Length1+=m,i.utf8Length2+=m):y>=2*n.margin&&o&&(nR(i,c,n),r.push(i),i=n_(-1,-1),o=0,c=d,a=s,l=u);break;default:throw Error("Unknown diff type")}1!==p&&(a+=y,l+=m),-1!==p&&(s+=y,u+=m)}return o&&(nR(i,c,n),r.push(i)),r}function nR(e,t,n){if(0===t.length)return;let r=t.substring(e.start2,e.start2+e.length1),i=0;for(;t.indexOf(r)!==t.lastIndexOf(r)&&r.length<32-n.margin-n.margin;)i+=n.margin,r=t.substring(e.start2-i,e.start2+e.length1+i);i+=n.margin;let o=e.start2-i;o>=1&&tJ(t[o])&&o--;let a=t.substring(o,e.start2);a&&e.diffs.unshift([0,a]);let s=a.length,l=nx(a),u=e.start2+e.length1+i;u<t.length&&tJ(t[u])&&u++;let c=t.substring(e.start2+e.length1,u);c&&e.diffs.push([0,c]);let d=c.length,f=nx(c);e.start1-=s,e.start2-=s,e.utf8Start1-=l,e.utf8Start2-=l,e.length1+=s+d,e.length2+=s+d,e.utf8Length1+=l+f,e.utf8Length2+=l+f}let nC=/^@@ -(\d+),?(\d*) \+(\d+),?(\d*) @@$/;function nT(e){return parseInt(e,10)}function nL(e){let t,n,r;let{utf8Length1:i,utf8Length2:o,utf8Start1:a,utf8Start2:s,diffs:l}=e;t=0===i?`${a},0`:1===i?`${a+1}`:`${a+1},${i}`,n=0===o?`${s},0`:1===o?`${s+1}`:`${s+1},${o}`;let u=[`@@ -${t} +${n} @@
`];for(let e=0;e<l.length;e++){switch(l[e][0]){case 1:r="+";break;case -1:r="-";break;case 0:r=" ";break;default:throw Error("Unknown patch operation.")}u[e+1]=`${r+encodeURI(l[e][1])}
`}return u.join("").replace(/%20/g," ")}var nD="object"==typeof global&&global&&global.Object===Object&&global,nN="object"==typeof self&&self&&self.Object===Object&&self,nF=nD||nN||Function("return this")(),nz=nF.Symbol,nU=Object.prototype,nW=nU.hasOwnProperty,nV=nU.toString,nq=nz?nz.toStringTag:void 0,nB=function(e){var t=nW.call(e,nq),n=e[nq];try{e[nq]=void 0;var r=!0}catch(e){}var i=nV.call(e);return r&&(t?e[nq]=n:delete e[nq]),i},nG=Object.prototype.toString,nH=nz?nz.toStringTag:void 0,nZ=function(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":nH&&nH in Object(e)?nB(e):nG.call(e)},nK=function(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)},nY=function(e){if(!nK(e))return!1;var t=nZ(e);return"[object Function]"==t||"[object GeneratorFunction]"==t||"[object AsyncFunction]"==t||"[object Proxy]"==t},nX=nF["__core-js_shared__"],nJ=(o=/[^.]+$/.exec(nX&&nX.keys&&nX.keys.IE_PROTO||""))?"Symbol(src)_1."+o:"",nQ=Function.prototype.toString,n0=function(e){if(null!=e){try{return nQ.call(e)}catch(e){}try{return e+""}catch(e){}}return""},n1=/^\[object .+?Constructor\]$/,n2=Object.prototype,n3=Function.prototype.toString,n5=n2.hasOwnProperty,n4=RegExp("^"+n3.call(n5).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),n8=function(e,t){var n,r=null==e?void 0:e[t];return nK(n=r)&&(!nJ||!(nJ in n))&&(nY(n)?n4:n1).test(n0(n))?r:void 0},n6=function(){try{var e=n8(Object,"defineProperty");return e({},"",{}),e}catch(e){}}(),n9=function(e,t,n){"__proto__"==t&&n6?n6(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n},n7=function(e,t,n,r){for(var i=-1,o=null==e?0:e.length;++i<o;){var a=e[i];t(r,a,n(a),e)}return r},re=function(e,t,n){for(var r=-1,i=Object(e),o=n(e),a=o.length;a--;){var s=o[++r];if(!1===t(i[s],s,i))break}return e},rt=function(e,t){for(var n=-1,r=Array(e);++n<e;)r[n]=t(n);return r},rn=function(e){return null!=e&&"object"==typeof e},rr=function(e){return rn(e)&&"[object Arguments]"==nZ(e)},ri=Object.prototype,ro=ri.hasOwnProperty,ra=ri.propertyIsEnumerable,rs=rr(function(){return arguments}())?rr:function(e){return rn(e)&&ro.call(e,"callee")&&!ra.call(e,"callee")},rl=Array.isArray,ru="object"==typeof exports&&exports&&!exports.nodeType&&exports,rc=ru&&"object"==typeof module&&module&&!module.nodeType&&module,rd=rc&&rc.exports===ru?nF.Buffer:void 0,rf=(rd?rd.isBuffer:void 0)||function(){return!1},rp=/^(?:0|[1-9]\d*)$/,rh=function(e,t){var n=typeof e;return!!(t=null==t?9007199254740991:t)&&("number"==n||"symbol"!=n&&rp.test(e))&&e>-1&&e%1==0&&e<t},ry=function(e){return"number"==typeof e&&e>-1&&e%1==0&&e<=9007199254740991},rm={};rm["[object Float32Array]"]=rm["[object Float64Array]"]=rm["[object Int8Array]"]=rm["[object Int16Array]"]=rm["[object Int32Array]"]=rm["[object Uint8Array]"]=rm["[object Uint8ClampedArray]"]=rm["[object Uint16Array]"]=rm["[object Uint32Array]"]=!0,rm["[object Arguments]"]=rm["[object Array]"]=rm["[object ArrayBuffer]"]=rm["[object Boolean]"]=rm["[object DataView]"]=rm["[object Date]"]=rm["[object Error]"]=rm["[object Function]"]=rm["[object Map]"]=rm["[object Number]"]=rm["[object Object]"]=rm["[object RegExp]"]=rm["[object Set]"]=rm["[object String]"]=rm["[object WeakMap]"]=!1;var rg="object"==typeof exports&&exports&&!exports.nodeType&&exports,rv=rg&&"object"==typeof module&&module&&!module.nodeType&&module,rb=rv&&rv.exports===rg&&nD.process,rw=function(){try{var e=rv&&rv.require&&rv.require("util").types;if(e)return e;return rb&&rb.binding&&rb.binding("util")}catch(e){}}(),rx=rw&&rw.isTypedArray,rE=rx?function(e){return rx(e)}:function(e){return rn(e)&&ry(e.length)&&!!rm[nZ(e)]},r_=Object.prototype.hasOwnProperty,rk=function(e,t){var n=rl(e),r=!n&&rs(e),i=!n&&!r&&rf(e),o=!n&&!r&&!i&&rE(e),a=n||r||i||o,s=a?rt(e.length,String):[],l=s.length;for(var u in e)(t||r_.call(e,u))&&!(a&&("length"==u||i&&("offset"==u||"parent"==u)||o&&("buffer"==u||"byteLength"==u||"byteOffset"==u)||rh(u,l)))&&s.push(u);return s},rj=Object.prototype,rS=function(e){var t=e&&e.constructor;return e===("function"==typeof t&&t.prototype||rj)},rI=(a=Object.keys,s=Object,function(e){return a(s(e))}),rO=Object.prototype.hasOwnProperty,rA=function(e){if(!rS(e))return rI(e);var t=[];for(var n in Object(e))rO.call(e,n)&&"constructor"!=n&&t.push(n);return t},rP=function(e){return null!=e&&ry(e.length)&&!nY(e)},rM=function(e){return rP(e)?rk(e):rA(e)},r$=(l=function(e,t){return e&&re(e,t,rM)},function(e,t){if(null==e)return e;if(!rP(e))return l(e,t);for(var n=e.length,r=-1,i=Object(e);(u?r--:++r<n)&&!1!==t(i[r],r,i););return e}),rR=function(e,t){return e===t||e!=e&&t!=t},rC=function(e,t){for(var n=e.length;n--;)if(rR(e[n][0],t))return n;return -1},rT=Array.prototype.splice;function rL(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}rL.prototype.clear=function(){this.__data__=[],this.size=0},rL.prototype.delete=function(e){var t=this.__data__,n=rC(t,e);return!(n<0)&&(n==t.length-1?t.pop():rT.call(t,n,1),--this.size,!0)},rL.prototype.get=function(e){var t=this.__data__,n=rC(t,e);return n<0?void 0:t[n][1]},rL.prototype.has=function(e){return rC(this.__data__,e)>-1},rL.prototype.set=function(e,t){var n=this.__data__,r=rC(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this};var rD=n8(nF,"Map"),rN=n8(Object,"create"),rF=Object.prototype.hasOwnProperty,rz=Object.prototype.hasOwnProperty;function rU(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}rU.prototype.clear=function(){this.__data__=rN?rN(null):{},this.size=0},rU.prototype.delete=function(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t},rU.prototype.get=function(e){var t=this.__data__;if(rN){var n=t[e];return"__lodash_hash_undefined__"===n?void 0:n}return rF.call(t,e)?t[e]:void 0},rU.prototype.has=function(e){var t=this.__data__;return rN?void 0!==t[e]:rz.call(t,e)},rU.prototype.set=function(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=rN&&void 0===t?"__lodash_hash_undefined__":t,this};var rW=function(e){var t=typeof e;return"string"==t||"number"==t||"symbol"==t||"boolean"==t?"__proto__"!==e:null===e},rV=function(e,t){var n=e.__data__;return rW(t)?n["string"==typeof t?"string":"hash"]:n.map};function rq(e){var t=-1,n=null==e?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function rB(e){var t=this.__data__=new rL(e);this.size=t.size}function rG(e){var t=-1,n=null==e?0:e.length;for(this.__data__=new rq;++t<n;)this.add(e[t])}rq.prototype.clear=function(){this.size=0,this.__data__={hash:new rU,map:new(rD||rL),string:new rU}},rq.prototype.delete=function(e){var t=rV(this,e).delete(e);return this.size-=t?1:0,t},rq.prototype.get=function(e){return rV(this,e).get(e)},rq.prototype.has=function(e){return rV(this,e).has(e)},rq.prototype.set=function(e,t){var n=rV(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this},rB.prototype.clear=function(){this.__data__=new rL,this.size=0},rB.prototype.delete=function(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n},rB.prototype.get=function(e){return this.__data__.get(e)},rB.prototype.has=function(e){return this.__data__.has(e)},rB.prototype.set=function(e,t){var n=this.__data__;if(n instanceof rL){var r=n.__data__;if(!rD||r.length<199)return r.push([e,t]),this.size=++n.size,this;n=this.__data__=new rq(r)}return n.set(e,t),this.size=n.size,this},rG.prototype.add=rG.prototype.push=function(e){return this.__data__.set(e,"__lodash_hash_undefined__"),this},rG.prototype.has=function(e){return this.__data__.has(e)};var rH=function(e,t){for(var n=-1,r=null==e?0:e.length;++n<r;)if(t(e[n],n,e))return!0;return!1},rZ=function(e,t,n,r,i,o){var a=1&n,s=e.length,l=t.length;if(s!=l&&!(a&&l>s))return!1;var u=o.get(e),c=o.get(t);if(u&&c)return u==t&&c==e;var d=-1,f=!0,p=2&n?new rG:void 0;for(o.set(e,t),o.set(t,e);++d<s;){var h=e[d],y=t[d];if(r)var m=a?r(y,h,d,t,e,o):r(h,y,d,e,t,o);if(void 0!==m){if(m)continue;f=!1;break}if(p){if(!rH(t,function(e,t){if(!p.has(t)&&(h===e||i(h,e,n,r,o)))return p.push(t)})){f=!1;break}}else if(!(h===y||i(h,y,n,r,o))){f=!1;break}}return o.delete(e),o.delete(t),f},rK=nF.Uint8Array,rY=function(e){var t=-1,n=Array(e.size);return e.forEach(function(e,r){n[++t]=[r,e]}),n},rX=function(e){var t=-1,n=Array(e.size);return e.forEach(function(e){n[++t]=e}),n},rJ=nz?nz.prototype:void 0,rQ=rJ?rJ.valueOf:void 0,r0=function(e,t,n,r,i,o,a){switch(n){case"[object DataView]":if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)break;e=e.buffer,t=t.buffer;case"[object ArrayBuffer]":if(e.byteLength!=t.byteLength||!o(new rK(e),new rK(t)))break;return!0;case"[object Boolean]":case"[object Date]":case"[object Number]":return rR(+e,+t);case"[object Error]":return e.name==t.name&&e.message==t.message;case"[object RegExp]":case"[object String]":return e==t+"";case"[object Map]":var s=rY;case"[object Set]":var l=1&r;if(s||(s=rX),e.size!=t.size&&!l)break;var u=a.get(e);if(u)return u==t;r|=2,a.set(e,t);var c=rZ(s(e),s(t),r,i,o,a);return a.delete(e),c;case"[object Symbol]":if(rQ)return rQ.call(e)==rQ.call(t)}return!1},r1=function(e,t){for(var n=-1,r=t.length,i=e.length;++n<r;)e[i+n]=t[n];return e},r2=function(e,t,n){var r=t(e);return rl(e)?r:r1(r,n(e))},r3=function(e,t){for(var n=-1,r=null==e?0:e.length,i=0,o=[];++n<r;){var a=e[n];t(a,n,e)&&(o[i++]=a)}return o},r5=Object.prototype.propertyIsEnumerable,r4=Object.getOwnPropertySymbols,r8=r4?function(e){return null==e?[]:r3(r4(e=Object(e)),function(t){return r5.call(e,t)})}:function(){return[]},r6=function(e){return r2(e,rM,r8)},r9=Object.prototype.hasOwnProperty,r7=function(e,t,n,r,i,o){var a=1&n,s=r6(e),l=s.length;if(l!=r6(t).length&&!a)return!1;for(var u=l;u--;){var c=s[u];if(!(a?c in t:r9.call(t,c)))return!1}var d=o.get(e),f=o.get(t);if(d&&f)return d==t&&f==e;var p=!0;o.set(e,t),o.set(t,e);for(var h=a;++u<l;){var y=e[c=s[u]],m=t[c];if(r)var g=a?r(m,y,c,t,e,o):r(y,m,c,e,t,o);if(!(void 0===g?y===m||i(y,m,n,r,o):g)){p=!1;break}h||(h="constructor"==c)}if(p&&!h){var v=e.constructor,b=t.constructor;v!=b&&"constructor"in e&&"constructor"in t&&!("function"==typeof v&&v instanceof v&&"function"==typeof b&&b instanceof b)&&(p=!1)}return o.delete(e),o.delete(t),p},ie=n8(nF,"DataView"),it=n8(nF,"Promise"),ir=n8(nF,"Set"),ii=n8(nF,"WeakMap"),io="[object Map]",ia="[object Promise]",is="[object Set]",il="[object WeakMap]",iu="[object DataView]",ic=n0(ie),id=n0(rD),ip=n0(it),ih=n0(ir),iy=n0(ii),im=nZ;(ie&&im(new ie(new ArrayBuffer(1)))!=iu||rD&&im(new rD)!=io||it&&im(it.resolve())!=ia||ir&&im(new ir)!=is||ii&&im(new ii)!=il)&&(im=function(e){var t=nZ(e),n="[object Object]"==t?e.constructor:void 0,r=n?n0(n):"";if(r)switch(r){case ic:return iu;case id:return io;case ip:return ia;case ih:return is;case iy:return il}return t});var ig=im,iv="[object Arguments]",ib="[object Array]",iw="[object Object]",ix=Object.prototype.hasOwnProperty,iE=function(e,t,n,r,i,o){var a=rl(e),s=rl(t),l=a?ib:ig(e),u=s?ib:ig(t);l=l==iv?iw:l,u=u==iv?iw:u;var c=l==iw,d=u==iw,f=l==u;if(f&&rf(e)){if(!rf(t))return!1;a=!0,c=!1}if(f&&!c)return o||(o=new rB),a||rE(e)?rZ(e,t,n,r,i,o):r0(e,t,l,n,r,i,o);if(!(1&n)){var p=c&&ix.call(e,"__wrapped__"),h=d&&ix.call(t,"__wrapped__");if(p||h){var y=p?e.value():e,m=h?t.value():t;return o||(o=new rB),i(y,m,n,r,o)}}return!!f&&(o||(o=new rB),r7(e,t,n,r,i,o))},i_=function e(t,n,r,i,o){return t===n||(null!=t&&null!=n&&(rn(t)||rn(n))?iE(t,n,r,i,e,o):t!=t&&n!=n)},ik=function(e,t,n,r){var i=n.length,o=i,a=!r;if(null==e)return!o;for(e=Object(e);i--;){var s=n[i];if(a&&s[2]?s[1]!==e[s[0]]:!(s[0]in e))return!1}for(;++i<o;){var l=(s=n[i])[0],u=e[l],c=s[1];if(a&&s[2]){if(void 0===u&&!(l in e))return!1}else{var d=new rB;if(r)var f=r(u,c,l,e,t,d);if(!(void 0===f?i_(c,u,3,r,d):f))return!1}}return!0},ij=function(e){return e==e&&!nK(e)},iS=function(e){for(var t=rM(e),n=t.length;n--;){var r=t[n],i=e[r];t[n]=[r,i,ij(i)]}return t},iI=function(e,t){return function(n){return null!=n&&n[e]===t&&(void 0!==t||e in Object(n))}},iO=function(e){var t=iS(e);return 1==t.length&&t[0][2]?iI(t[0][0],t[0][1]):function(n){return n===e||ik(n,e,t)}},iA=function(e){return"symbol"==typeof e||rn(e)&&"[object Symbol]"==nZ(e)},iP=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,iM=/^\w*$/,i$=function(e,t){if(rl(e))return!1;var n=typeof e;return!!("number"==n||"symbol"==n||"boolean"==n||null==e||iA(e))||iM.test(e)||!iP.test(e)||null!=t&&e in Object(t)};function iR(e,t){if("function"!=typeof e||null!=t&&"function"!=typeof t)throw TypeError("Expected a function");var n=function(){var r=arguments,i=t?t.apply(this,r):r[0],o=n.cache;if(o.has(i))return o.get(i);var a=e.apply(this,r);return n.cache=o.set(i,a)||o,a};return n.cache=new(iR.Cache||rq),n}iR.Cache=rq;var iC=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,iT=/\\(\\)?/g,iL=(d=(c=iR(function(e){var t=[];return 46===e.charCodeAt(0)&&t.push(""),e.replace(iC,function(e,n,r,i){t.push(r?i.replace(iT,"$1"):n||e)}),t},function(e){return 500===d.size&&d.clear(),e})).cache,c),iD=function(e,t){for(var n=-1,r=null==e?0:e.length,i=Array(r);++n<r;)i[n]=t(e[n],n,e);return i},iN=1/0,iF=nz?nz.prototype:void 0,iz=iF?iF.toString:void 0,iU=function e(t){if("string"==typeof t)return t;if(rl(t))return iD(t,e)+"";if(iA(t))return iz?iz.call(t):"";var n=t+"";return"0"==n&&1/t==-iN?"-0":n},iW=function(e,t){return rl(e)?e:i$(e,t)?[e]:iL(null==e?"":iU(e))},iV=1/0,iq=function(e){if("string"==typeof e||iA(e))return e;var t=e+"";return"0"==t&&1/e==-iV?"-0":t},iB=function(e,t){t=iW(t,e);for(var n=0,r=t.length;null!=e&&n<r;)e=e[iq(t[n++])];return n&&n==r?e:void 0},iG=function(e,t,n){var r=null==e?void 0:iB(e,t);return void 0===r?n:r},iH=function(e,t){return null!=e&&t in Object(e)},iZ=function(e,t,n){t=iW(t,e);for(var r=-1,i=t.length,o=!1;++r<i;){var a=iq(t[r]);if(!(o=null!=e&&n(e,a)))break;e=e[a]}return o||++r!=i?o:!!(i=null==e?0:e.length)&&ry(i)&&rh(a,i)&&(rl(e)||rs(e))},iK=function(e){return e},iY=function(e){var t;return i$(e)?(t=iq(e),function(e){return null==e?void 0:e[t]}):function(t){return iB(t,e)}},iX=function(e){if("function"==typeof e)return e;if(null==e)return iK;if("object"==typeof e){var t,n;return rl(e)?(t=e[0],n=e[1],i$(t)&&ij(n)?iI(iq(t),n):function(e){var r=iG(e,t);return void 0===r&&r===n?null!=e&&iZ(e,t,iH):i_(n,r,3)}):iO(e)}return iY(e)},iJ=Object.prototype.hasOwnProperty,iQ=(f=function(e,t,n){iJ.call(e,n)?e[n].push(t):n9(e,n,[t])},function(e,t){return(rl(e)?n7:function(e,t,n,r){return r$(e,function(e,i,o){t(r,e,n(e),o)}),r})(e,f,iX(t,2),{})});let i0=["Value","Copy","Blank","ReturnIntoArray","ReturnIntoObject","ReturnIntoObjectSameKey","PushField","PushElement","PushParent","Pop","PushFieldCopy","PushFieldBlank","PushElementCopy","PushElementBlank","ReturnIntoObjectPop","ReturnIntoObjectSameKeyPop","ReturnIntoArrayPop","ObjectSetFieldValue","ObjectCopyField","ObjectDeleteField","ArrayAppendValue","ArrayAppendSlice","StringAppendString","StringAppendSlice"];class i1{model;root;patch;i;inputStack;outputStack;constructor(e,t,n){this.model=e,this.root=t,this.patch=n,this.i=0,this.inputStack=[],this.outputStack=[]}read(){return this.patch[this.i++]}process(){for(this.inputStack.push({value:this.root}),this.outputStack.push({value:this.root});this.i<this.patch.length;){let e=this.read(),t=i0[e];if(!t)throw Error(`Unknown opcode: ${e}`);this[`process${t}`].apply(this)}let e=this.outputStack.pop();return this.finalizeOutput(e)}inputEntry(){return this.inputStack[this.inputStack.length-1]}inputKey(e,t){return e.keys||(e.keys=this.model.objectGetKeys(e.value).sort()),e.keys[t]}outputEntry(){return this.outputStack[this.outputStack.length-1]}outputArray(){let e=this.outputEntry();return e.writeValue||(e.writeValue=this.model.copyArray(e.value)),e.writeValue}outputObject(){let e=this.outputEntry();return e.writeValue||(e.writeValue=this.model.copyObject(e.value)),e.writeValue}outputString(){let e=this.outputEntry();return e.writeValue||(e.writeValue=this.model.copyString(e.value)),e.writeValue}finalizeOutput(e){return e.writeValue?this.model.finalize(e.writeValue):e.value}processValue(){let e=this.model.wrap(this.read());this.outputStack.push({value:e})}processCopy(){let e=this.inputEntry();this.outputStack.push({value:e.value})}processBlank(){this.outputStack.push({value:null})}processReturnIntoArray(){let e=this.outputStack.pop(),t=this.finalizeOutput(e),n=this.outputArray();this.model.arrayAppendValue(n,t)}processReturnIntoObject(){let e=this.read(),t=this.outputStack.pop(),n=this.finalizeOutput(t);n=this.model.markChanged(n);let r=this.outputObject();this.model.objectSetField(r,e,n)}processReturnIntoObjectSameKey(){let e=this.inputEntry(),t=this.outputStack.pop(),n=this.finalizeOutput(t),r=this.outputObject();this.model.objectSetField(r,e.key,n)}processPushField(){let e=this.read(),t=this.inputEntry(),n=this.inputKey(t,e),r=this.model.objectGetField(t.value,n);this.inputStack.push({value:r,key:n})}processPushElement(){let e=this.read(),t=this.inputEntry(),n=this.model.arrayGetElement(t.value,e);this.inputStack.push({value:n})}processPop(){this.inputStack.pop()}processPushFieldCopy(){this.processPushField(),this.processCopy()}processPushFieldBlank(){this.processPushField(),this.processBlank()}processPushElementCopy(){this.processPushElement(),this.processCopy()}processPushElementBlank(){this.processPushElement(),this.processBlank()}processReturnIntoObjectPop(){this.processReturnIntoObject(),this.processPop()}processReturnIntoObjectSameKeyPop(){this.processReturnIntoObjectSameKey(),this.processPop()}processReturnIntoArrayPop(){this.processReturnIntoArray(),this.processPop()}processObjectSetFieldValue(){this.processValue(),this.processReturnIntoObject()}processObjectCopyField(){this.processPushField(),this.processCopy(),this.processReturnIntoObjectSameKey(),this.processPop()}processObjectDeleteField(){let e=this.read(),t=this.inputEntry(),n=this.inputKey(t,e),r=this.outputObject();this.model.objectDeleteField(r,n)}processArrayAppendValue(){let e=this.model.wrap(this.read()),t=this.outputArray();this.model.arrayAppendValue(t,e)}processArrayAppendSlice(){let e=this.read(),t=this.read(),n=this.outputArray(),r=this.inputEntry().value;this.model.arrayAppendSlice(n,r,e,t)}processStringAppendString(){let e=this.model.wrap(this.read()),t=this.outputString();this.model.stringAppendValue(t,e)}processStringAppendSlice(){let e=this.read(),t=this.read(),n=this.outputString(),r=this.inputEntry().value;this.model.stringAppendSlice(n,r,e,t)}}function i2(e,t,n=0){let r=n,i=0;for(i=n;r<t;i++){var o;let t=(o=e.codePointAt(i))>>16?4:o>>11?3:o>>7?2:1;4===t&&i++,r+=t}return i}class i3{wrap(e){return e}finalize(e){return Array.isArray(e)?e:e.data}markChanged(e){return e}objectGetKeys(e){return Object.keys(e)}objectGetField(e,t){return e[t]}arrayGetElement(e,t){return e[t]}copyObject(e){let t={type:"object",data:{}};if(null!==e)for(let[n,r]of Object.entries(e))t.data[n]=r;return t}copyArray(e){return null===e?[]:e.slice()}copyString(e){return{type:"string",data:null===e?"":e}}objectSetField(e,t,n){e.data[t]=n}objectDeleteField(e,t){delete e.data[t]}arrayAppendValue(e,t){e.push(t)}arrayAppendSlice(e,t,n,r){e.push(...t.slice(n,r))}stringAppendSlice(e,t,n,r){let i=i2(t,n),o=i2(t,r,i);e.data+=t.slice(i,o)}stringAppendValue(e,t){e.data+=t}}var i5=n(19142),i4=n(17753);function i8(e){void 0===e&&(e={});var t=e.connector,n=void 0===t?function(){return new tD}:t,r=e.resetOnError,i=void 0===r||r,o=e.resetOnComplete,a=void 0===o||o,s=e.resetOnRefCountZero,l=void 0===s||s;return function(e){var t,r,o,s=0,u=!1,c=!1,d=function(){null==r||r.unsubscribe(),r=void 0},f=function(){d(),t=o=void 0,u=c=!1},p=function(){var e=t;f(),null==e||e.unsubscribe()};return(0,i4.e)(function(e,h){s++,c||u||d();var y=o=null!=o?o:n();h.add(function(){0!=--s||c||u||(r=i6(p,l))}),y.subscribe(h),!t&&s>0&&(t=new i5.Hp({next:function(e){return y.next(e)},error:function(e){c=!0,d(),r=i6(f,i,e),y.error(e)},complete:function(){u=!0,d(),r=i6(f,a),y.complete()}}),(0,tV.Xf)(e).subscribe(t))})(e)}}function i6(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];if(!0===t){e();return}if(!1!==t){var i=new i5.Hp({next:function(){i.unsubscribe(),e()}});return(0,tV.Xf)(t.apply(void 0,(0,tM.ev)([],(0,tM.CR)(n)))).subscribe(i)}}var i9=n(44465),i7=n(68851),oe=n(96987),ot=function(e){function t(t,n){return e.call(this)||this}return(0,tM.ZT)(t,e),t.prototype.schedule=function(e,t){return void 0===t&&(t=0),this},t}(tR.w0),on={setInterval:function(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var i=on.delegate;return(null==i?void 0:i.setInterval)?i.setInterval.apply(i,(0,tM.ev)([e,t],(0,tM.CR)(n))):setInterval.apply(void 0,(0,tM.ev)([e,t],(0,tM.CR)(n)))},clearInterval:function(e){var t=on.delegate;return((null==t?void 0:t.clearInterval)||clearInterval)(e)},delegate:void 0},or=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.scheduler=t,r.work=n,r.pending=!1,r}return(0,tM.ZT)(t,e),t.prototype.schedule=function(e,t){if(void 0===t&&(t=0),this.closed)return this;this.state=e;var n,r=this.id,i=this.scheduler;return null!=r&&(this.id=this.recycleAsyncId(i,r,t)),this.pending=!0,this.delay=t,this.id=null!==(n=this.id)&&void 0!==n?n:this.requestAsyncId(i,this.id,t),this},t.prototype.requestAsyncId=function(e,t,n){return void 0===n&&(n=0),on.setInterval(e.flush.bind(e,this),n)},t.prototype.recycleAsyncId=function(e,t,n){if(void 0===n&&(n=0),null!=n&&this.delay===n&&!1===this.pending)return t;null!=t&&on.clearInterval(t)},t.prototype.execute=function(e,t){if(this.closed)return Error("executing a cancelled action");this.pending=!1;var n=this._execute(e,t);if(n)return n;!1===this.pending&&null!=this.id&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))},t.prototype._execute=function(e,t){var n,r=!1;try{this.work(e)}catch(e){r=!0,n=e||Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),n},t.prototype.unsubscribe=function(){if(!this.closed){var t=this.id,n=this.scheduler,r=n.actions;this.work=this.state=this.scheduler=null,this.pending=!1,(0,tT.P)(r,this),null!=t&&(this.id=this.recycleAsyncId(n,t,null)),this.delay=null,e.prototype.unsubscribe.call(this)}},t}(ot),oi=1,oo={};function oa(e){return e in oo&&(delete oo[e],!0)}var os=function(e){var t=oi++;return oo[t]=!0,g||(g=Promise.resolve()),g.then(function(){return oa(t)&&e()}),t},ol=function(e){oa(e)},ou={setImmediate:function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=ou.delegate;return((null==n?void 0:n.setImmediate)||os).apply(void 0,(0,tM.ev)([],(0,tM.CR)(e)))},clearImmediate:function(e){var t=ou.delegate;return((null==t?void 0:t.clearImmediate)||ol)(e)},delegate:void 0},oc=function(e){function t(t,n){var r=e.call(this,t,n)||this;return r.scheduler=t,r.work=n,r}return(0,tM.ZT)(t,e),t.prototype.requestAsyncId=function(t,n,r){return(void 0===r&&(r=0),null!==r&&r>0)?e.prototype.requestAsyncId.call(this,t,n,r):(t.actions.push(this),t._scheduled||(t._scheduled=ou.setImmediate(t.flush.bind(t,void 0))))},t.prototype.recycleAsyncId=function(t,n,r){if(void 0===r&&(r=0),null!=r?r>0:this.delay>0)return e.prototype.recycleAsyncId.call(this,t,n,r);var i,o=t.actions;null!=n&&(null===(i=o[o.length-1])||void 0===i?void 0:i.id)!==n&&(ou.clearImmediate(n),t._scheduled===n&&(t._scheduled=void 0))},t}(or),od=function(){function e(t,n){void 0===n&&(n=e.now),this.schedulerActionCtor=t,this.now=n}return e.prototype.schedule=function(e,t,n){return void 0===t&&(t=0),new this.schedulerActionCtor(this,e).schedule(n,t)},e.now=tF.now,e}(),of=new(function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return(0,tM.ZT)(t,e),t.prototype.flush=function(e){this._active=!0;var t,n=this._scheduled;this._scheduled=void 0;var r=this.actions;e=e||r.shift();do if(t=e.execute(e.state,e.delay))break;while((e=r[0])&&e.id===n&&r.shift());if(this._active=!1,t){for(;(e=r[0])&&e.id===n&&r.shift();)e.unsubscribe();throw t}},t}(function(e){function t(t,n){void 0===n&&(n=od.now);var r=e.call(this,t,n)||this;return r.actions=[],r._active=!1,r}return(0,tM.ZT)(t,e),t.prototype.flush=function(e){var t,n=this.actions;if(this._active){n.push(e);return}this._active=!0;do if(t=e.execute(e.state,e.delay))break;while(e=n.shift());if(this._active=!1,t){for(;e=n.shift();)e.unsubscribe();throw t}},t}(od)))(oc),op=n(25181),oh=n(87746);function oy(e){if("patch"===e.type)return e.id;if("create"===e.type)return e.document._id;if("delete"===e.type)return e.id;if("createIfNotExists"===e.type||"createOrReplace"===e.type)return e.document._id;throw Error("Invalid mutation type")}let om=(e=21)=>{let t="",n=crypto.getRandomValues(new Uint8Array(e));for(;e--;)t+="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict"[63&n[e]];return t};function og(e){return"string"==typeof e?._key}function ov(e){return"object"==typeof e&&"_key"in e&&"string"==typeof e._key}function ob(e){return"number"==typeof e||ov(e)}function ow(e,t){if(0===e.length)return t;let n=t;for(let t of e){if(ob(t)){if(!Array.isArray(n))return;if(ov(t)){n=n.find(e=>e._key===t._key);continue}n=n[t];continue}n=n[t]}return n}let ox=/^[a-z_$]+/;function oE(e){return e.map((e,t)=>{var n;return n=0===t,Array.isArray(e)?`[${e[0]}:${e[1]||""}]`:"number"==typeof e?`[${e}]`:ov(e)?`[_key==${JSON.stringify(e._key)}]`:"string"==typeof e&&ox.test(e)?n?e:`.${e}`:`['${e}']`}).join("")}function o_(e){return null!==e&&"object"==typeof e&&!Array.isArray(e)}function ok(e,t){if("number"==typeof t)return function(e,t){if(0===e&&(-1===t||0===t))return 0;let n=t<0?e+t:t;return n>=e||n<0?null:n}(e.length,t);if(ov(t)){let n=e.findIndex(e=>(null!==e&&"object"==typeof e&&"string"==typeof e._key&&e._key||null)===t._key);return -1===n?null:n}throw Error(`Expected path segment to be addressing a single array item either by numeric index or by '_key'. Instead saw ${JSON.stringify(t)}`)}function oj(e,t,n,r){let i=e.slice();return i.splice(t,n,...r||[]),i}function oS(e,t){if(!Array.isArray(t))throw TypeError('Cannot apply "insert()" on non-array value');let n=ok(t,e.referenceItem);if(null===n)throw Error(`Found no matching array element to insert ${e.position}`);return 0===t.length?e.items:oj(t,"before"===e.position?n:n+1,0,e.items)}let oI=Object.prototype.hasOwnProperty.call.bind(Object.prototype.hasOwnProperty);var oO=Object.freeze({__proto__:null,assign:function(e,t){if(!o_(t))throw TypeError('Cannot apply "assign()" on non-object value');return!function(e){for(let t in e)if(oI(e,t))return!1;return!0}(e.value)?{...t,...e.value}:t},dec:function(e,t){if("number"!=typeof t)throw TypeError('Cannot apply "dec()" on non-numeric value');return t-e.amount},diffMatchPatch:function(e,t){if("string"!=typeof t)throw TypeError('Cannot apply "diffMatchPatch()" on non-string value');return function(e,t,n={}){if("string"==typeof e)throw Error("Patches must be an array - pass the patch to `parsePatch()` first");let r=t;if(0===e.length)return[r,[]];let i=function(e,t,n={}){let r=0,i=0;function o(e){for(;r<e;){let e=t.codePointAt(i);if(typeof e>"u")return i;r+=nE(e),e>65535?i+=2:i+=1}if(!n.allowExceedingIndices&&r!==e)throw Error("Failed to determine byte offset");return i}let a=[];for(let t of e)a.push({diffs:t.diffs.map(e=>tZ(e)),start1:o(t.start1),start2:o(t.start2),utf8Start1:t.utf8Start1,utf8Start2:t.utf8Start2,length1:t.length1,length2:t.length2,utf8Length1:t.utf8Length1,utf8Length2:t.utf8Length2});return a}(e,r,{allowExceedingIndices:n.allowExceedingIndices}),o=n.margin||4,a=n.deleteThreshold||.4,s=function(e,t=4){let n="";for(let e=1;e<=t;e++)n+=String.fromCharCode(e);for(let n of e)n.start1+=t,n.start2+=t,n.utf8Start1+=t,n.utf8Start2+=t;let r=e[0],i=r.diffs;if(0===i.length||0!==i[0][0])i.unshift([0,n]),r.start1-=t,r.start2-=t,r.utf8Start1-=t,r.utf8Start2-=t,r.length1+=t,r.length2+=t,r.utf8Length1+=t,r.utf8Length2+=t;else if(t>i[0][1].length){let e=i[0][1].length,o=t-e;i[0][1]=n.substring(e)+i[0][1],r.start1-=o,r.start2-=o,r.utf8Start1-=o,r.utf8Start2-=o,r.length1+=o,r.length2+=o,r.utf8Length1+=o,r.utf8Length2+=o}if(0===(i=(r=e[e.length-1]).diffs).length||0!==i[i.length-1][0])i.push([0,n]),r.length1+=t,r.length2+=t,r.utf8Length1+=t,r.utf8Length2+=t;else if(t>i[i.length-1][1].length){let e=t-i[i.length-1][1].length;i[i.length-1][1]+=n.substring(0,e),r.length1+=e,r.length2+=e,r.utf8Length1+=e,r.utf8Length2+=e}return n}(i,o);r=s+r+s,function(e,t=4){for(let n=0;n<e.length;n++){if(e[n].length1<=32)continue;let r=e[n];e.splice(n--,1);let i=r.start1,o=r.start2,a="";for(;0!==r.diffs.length;){let s=n_(i-a.length,o-a.length),l=!0;if(""!==a){let e=nx(a);s.length1=a.length,s.utf8Length1=e,s.length2=a.length,s.utf8Length2=e,s.diffs.push([0,a])}for(;0!==r.diffs.length&&s.length1<32-t;){let e=r.diffs[0][0],n=r.diffs[0][1],a=nx(n);if(1===e){s.length2+=n.length,s.utf8Length2+=a,o+=n.length;let e=r.diffs.shift();e&&s.diffs.push(e),l=!1}else -1===e&&1===s.diffs.length&&0===s.diffs[0][0]&&n.length>64?(s.length1+=n.length,s.utf8Length1+=a,i+=n.length,l=!1,s.diffs.push([e,n]),r.diffs.shift()):(a=nx(n=n.substring(0,32-s.length1-t)),s.length1+=n.length,s.utf8Length1+=a,i+=n.length,0===e?(s.length2+=n.length,s.utf8Length2+=a,o+=n.length):l=!1,s.diffs.push([e,n]),n===r.diffs[0][1]?r.diffs.shift():r.diffs[0][1]=r.diffs[0][1].substring(n.length))}a=(a=nb(s.diffs)).substring(a.length-t);let u=nv(r.diffs).substring(0,t),c=nx(u);""!==u&&(s.length1+=u.length,s.length2+=u.length,s.utf8Length1+=c,s.utf8Length2+=c,0!==s.diffs.length&&0===s.diffs[s.diffs.length-1][0]?s.diffs[s.diffs.length-1][1]+=u:s.diffs.push([0,u])),l||e.splice(++n,0,s)}}}(i,o);let l=0,u=[];for(let e=0;e<i.length;e++){let t=i[e].start2+l,n=nv(i[e].diffs),o,s=-1;if(n.length>32?-1!==(o=ng(r,n.substring(0,32),t))&&(-1===(s=ng(r,n.substring(n.length-32),t+n.length-32))||o>=s)&&(o=-1):o=ng(r,n,t),-1===o)u[e]=!1,l-=i[e].length2-i[e].length1;else{let c;if(u[e]=!0,l=o-t,c=-1===s?r.substring(o,o+n.length):r.substring(o,s+32),n===c)r=r.substring(0,o)+nb(i[e].diffs)+r.substring(o+n.length);else{let t=t6(n,c,{checkLines:!1});if(n.length>32&&function(e){let t=0,n=0,r=0;for(let i=0;i<e.length;i++){let o=e[i][0],a=e[i][1];switch(o){case 1:n+=a.length;break;case -1:r+=a.length;break;case 0:t+=Math.max(n,r),n=0,r=0;break;default:throw Error("Unknown diff operation.")}}return t+Math.max(n,r)}(t)/n.length>a)u[e]=!1;else{t=nl(t);let n=0,a=0;for(let s=0;s<i[e].diffs.length;s++){let l=i[e].diffs[s];0!==l[0]&&(a=nw(t,n)),1===l[0]?r=r.substring(0,o+a)+l[1]+r.substring(o+a):-1===l[0]&&(r=r.substring(0,o+a)+r.substring(o+nw(t,n+l[1].length))),-1!==l[0]&&(n+=l[1].length)}}}}}return[r=r.substring(s.length,r.length-s.length),u]}(function(e){if(!e)return[];let t=[],n=e.split(`
`),r=0;for(;r<n.length;){let e=n[r].match(nC);if(!e)throw Error(`Invalid patch string: ${n[r]}`);let i=n_(nT(e[1]),nT(e[3]));for(t.push(i),""===e[2]?(i.start1--,i.utf8Start1--,i.length1=1,i.utf8Length1=1):"0"===e[2]?(i.length1=0,i.utf8Length1=0):(i.start1--,i.utf8Start1--,i.utf8Length1=nT(e[2]),i.length1=i.utf8Length1),""===e[4]?(i.start2--,i.utf8Start2--,i.length2=1,i.utf8Length2=1):"0"===e[4]?(i.length2=0,i.utf8Length2=0):(i.start2--,i.utf8Start2--,i.utf8Length2=nT(e[4]),i.length2=i.utf8Length2),r++;r<n.length;){let e;let t=n[r],o=t.charAt(0);if("@"===o)break;if(""===o){r++;continue}try{e=decodeURI(t.slice(1))}catch(e){throw Error(`Illegal escape in parse: ${t}`)}let a=nx(e)-e.length;if("-"===o)i.diffs.push([-1,e]),i.length1-=a;else if("+"===o)i.diffs.push([1,e]),i.length2-=a;else if(" "===o)i.diffs.push([0,e]),i.length1-=a,i.length2-=a;else throw Error(`Invalid patch mode "${o}" in: ${e}`);r++}}return t}(e.value),t)[0]},inc:function(e,t){if("number"!=typeof t)throw TypeError('Cannot apply "inc()" on non-numeric value');return t+e.amount},insert:oS,remove:function(e,t){if(!Array.isArray(t))throw TypeError('Cannot apply "remove()" on non-array value');let n=ok(t,e.referenceItem);if(null===n)throw Error("Found no matching array element to replace");return oj(t,n,1,[])},replace:function(e,t){if(!Array.isArray(t))throw TypeError('Cannot apply "replace()" on non-array value');let n=ok(t,e.referenceItem);if(null===n)throw Error("Found no matching array element to replace");return oj(t,n,e.items.length,e.items)},set:function(e,t){return e.value},setIfMissing:function(e,t){return t??e.value},truncate:function(e,t){if(!Array.isArray(t))throw TypeError('Cannot apply "truncate()" on non-array value');return"number"==typeof e.endIndex?t.slice(0,e.startIndex).concat(t.slice(e.endIndex)):t.slice(0,e.startIndex)},unassign:function(e,t){if(!o_(t))throw TypeError('Cannot apply "unassign()" on non-object value');return 0===e.keys.length?t:function(e,t){let n={...e};for(let e of t)delete n[e];return n}(t,e.keys)},unset:function(e){},upsert:function(e,t){if(!Array.isArray(t))throw TypeError('Cannot apply "upsert()" on non-array value');if(0===e.items.length)return t;let n=[],r=[];if(e.items.forEach((e,i)=>{let o=t.findIndex(t=>t?._key===e._key);o>=0?n[o]=i:r.push(e)}),0===n.length&&0==r.length)return t;let i=[...t];for(let t of n)i[t]=e.items[n[t]];return oS({type:"insert",items:r,referenceItem:e.referenceItem,position:e.position},i)}});function oA(e,t){return e.reduce((e,t)=>oP(t,e),t)}function oP(e,t){return function e(t,n,r){if(!(t.length>0))return function(e,t){if(!(e.type in oO))throw Error(`Invalid operation type: "${e.type}"`);return oO[e.type](e,t)}(n,r);let[i,...o]=t;if(ob(i)&&Array.isArray(r))return function(t,n,r,i){let o=ok(i,t);if(null===o||-1===o)return i;let a=i[o],s=e(n,r,a);return s===a?i:oj(i,o,1,[s])}(i,o,n,r);if("string"==typeof i&&o_(r))return function(t,n,r,i){let o=i[t];if(void 0===o&&n.length>0)return i;let a=e(n,r,o);return a===o?i:{...i,[t]:a}}(i,o,n,r);throw Error(`Cannot apply operation of type "${n.type}" to path ${oE(t)} on ${typeof r} value`)}(e.path,e.op,t)}function oM(e){return"_id"in e}function o$(e,t){return t.reduce((e,t)=>{let n=oR(e,t);if("error"===n.status)throw Error(n.message);return"noop"===n.status?e:n.after},e)}function oR(e,t){if("create"===t.type)return function(e,t){var n;if(e)return{status:"error",message:"Document already exist"};let r=oM(n=t.document)?n:{...n,_id:om()};return{status:"created",id:r._id,after:r}}(e,t);if("createIfNotExists"===t.type)return oM(t.document)?e?{status:"noop"}:{status:"created",id:t.document._id,after:t.document}:{status:"error",message:"Cannot createIfNotExists on document without _id"};if("delete"===t.type)return e?t.id!==e._id?{status:"error",message:"Delete mutation targeted wrong document"}:{status:"deleted",id:t.id,before:e,after:void 0}:{status:"noop"};if("createOrReplace"===t.type)return oM(t.document)?e?{status:"updated",id:t.document._id,before:e,after:t.document}:{status:"created",id:t.document._id,after:t.document}:{status:"error",message:"Cannot createIfNotExists on document without _id"};if("patch"===t.type)return function(e,t){if(!e)return{status:"error",message:"Cannot apply patch on nonexistent document"};let n=function(e,t){if(e.options?.ifRevision&&t._rev!==e.options.ifRevision)throw Error("Revision mismatch");if(e.id!==t._id)throw Error(`Document id mismatch. Refusing to apply mutation for document with id="${e.id}" on the given document with id="${t._id}"`);return oA(e.patches,t)}(t,e);return e===n?{status:"noop"}:{status:"updated",id:t.id,before:e,after:n}}(e,t);throw Error(`Invalid mutation type: ${t.type}`)}function oC(e,t,n){let r=[];for(let n of e.slice().reverse()){if(t(n))return r;r.push(n)}return r.reverse()}function oT(e,t){return oE(e)===oE(t)}function oL(e,t){let n=e;return t.reduce((e,t)=>{let r=n;if(n=oP(t,n),"set"===t.op.type&&"string"==typeof t.op.value){let n=ow(t.path,r);if("string"==typeof n){let r={...t,op:{type:"diffMatchPatch",value:(function(e,t,n){if("string"==typeof e&&"string"==typeof t){let n=t6(e,t,{checkLines:!0});return n.length>2&&(n=function(e,t=4){let n=e.map(e=>tZ(e)),r=!1,i=[],o=0,a=null,s=0,l=!1,u=!1,c=!1,d=!1;for(;s<n.length;)0===n[s][0]?(n[s][1].length<t&&(c||d)?(i[o++]=s,l=c,u=d,a=n[s][1]):(o=0,a=null),c=!1,d=!1):(-1===n[s][0]?d=!0:c=!0,a&&(l&&u&&c&&d||a.length<t/2&&3===function(...e){return e.reduce((e,t)=>e+(t?1:0),0)}(l,u,c,d))&&(n.splice(i[o-1],0,[-1,a]),n[i[o-1]+1][0]=1,o--,a=null,l&&u?(c=!0,d=!0,o=0):(s=--o>0?i[o-1]:-1,c=!1,d=!1),r=!0)),s++;return r&&(n=nu(n)),n}(n=nn(n))),n$(e,n,nM(void 0))}if(e&&Array.isArray(e)&&typeof t>"u")return n$(nv(e),e,nM(void 0));if("string"==typeof e&&t&&Array.isArray(t))return n$(e,t,nM(void 0));throw Error("Unknown call format to make()")})(n,t.op.value).map(nL).join("")}};return e.flatMap(e=>oT(e.path,t.path)&&"diffMatchPatch"===e.op.type?[]:e).concat(r)}}return e.push(t),e},[])}function oD(e,t,n,r){let i=t,o=r.map(t=>{let n=t.mutations.flatMap(t=>{if(oy(t)!==e)return[];let n=i;return i=o$(i,[t]),n&&"patch"===t.type?{type:"dmpified",mutation:{...t,dmpPatches:oL(n,t.patches),original:t.patches}}:t});return{...t,mutations:n}}),a=n;return o.map(t=>{let n=[];return t.mutations.forEach(t=>{if("dmpified"===t.type)try{a=oA(t.mutation.dmpPatches,a),n.push(t)}catch{console.warn("Failed to apply dmp patch, falling back to original");try{a=oA(t.mutation.original,a),n.push(t)}catch(t){throw Error(`Failed to apply patch for document "${e}": ${t.message}`)}}else a=o$(a,[t])})}),[r.map(t=>({...t,mutations:t.mutations.map(t=>"patch"!==t.type||oy(t)!==e?t:{...t,patches:t.patches.map(e=>"set"!==e.op.type?e:{...e,op:{...e.op,value:ow(e.path,a)}})})})),a]}function oN(e){return function(e){if("create"===e.type||"createIfNotExists"===e.type||"createOrReplace"===e.type)return{[e.type]:e.document};if("delete"===e.type)return{delete:{id:e.id}};let t=e.options?.ifRevision;return e.patches.map(n=>({patch:{id:e.id,...t&&{ifRevisionID:t},...function(e){let{path:t,op:n}=e;if("unset"===n.type)return{unset:[oE(t)]};if("insert"===n.type)return{insert:{[n.position]:oE([...t,n.referenceItem]),items:n.items}};if("diffMatchPatch"===n.type)return{diffMatchPatch:{[oE(t)]:n.value}};if("inc"===n.type)return{inc:{[oE(t)]:n.amount}};if("dec"===n.type)return{dec:{[oE(t)]:n.amount}};if("set"===n.type||"setIfMissing"===n.type)return{[n.type]:{[oE(t)]:n.value}};if("truncate"===n.type){let e=[n.startIndex,"number"==typeof n.endIndex?n.endIndex:""].join(":");return{unset:[`${oE(t)}[${e}]`]}}if("upsert"===n.type)return{unset:n.items.map(e=>oE([...t,{_key:e._key}])),insert:{[n.position]:oE([...t,n.referenceItem]),items:n.items}};if("assign"===n.type)return{set:Object.fromEntries(Object.keys(n.value).map(e=>[oE(t.concat(e)),n.value[e]]))};if("unassign"===n.type)return{unset:n.keys.map(e=>oE(t.concat(e)))};if("replace"===n.type)return{insert:{replace:oE(t.concat(n.referenceItem)),items:n.items}};if("remove"===n.type)return{unset:[oE(t.concat(n.referenceItem))]};throw Error(`Unknown operation type ${n.type}`)}(n)}}))}(e)}function oF(e){var t,n,r,i,o,a,s,l;let u=e.listen('*[!(_id in path("_.**"))]',{},{events:["welcome","mutation","reconnect"],includeResult:!1,includePreviousRevision:!1,visibility:"transaction",effectFormat:"mendoza",includeMutations:!1}).pipe(i8({resetOnRefCountZero:!0})),c=u.pipe((0,i9.h)(e=>"reconnect"===e.type)),d=u.pipe((0,i9.h)(e=>"welcome"===e.type)),f=u.pipe((0,i9.h)(e=>"mutation"===e.type)),p=tH(d,c).pipe((l=!1,s=void 0===(i=(t={bufferSize:1,refCount:!0}).bufferSize)?1/0:i,n=void 0===(o=t.windowTime)?1/0:o,l=void 0!==(a=t.refCount)&&a,r=t.scheduler,i8({connector:function(){return new tz(s,n,r)},resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:l}))).pipe((0,i9.h)(e=>"welcome"===e.type));return tH(p,f,c)}let oz=(0,op.cY)({types:{},actions:{"assign error to context":(0,oh.a)({error:({event:e})=>e}),"clear error from context":(0,oh.a)({error:void 0}),"connect to server-sent events":(0,tP.O)({type:"connect"}),"listen to server-sent events":(0,tP.P)("server-sent events",{id:"listener",input:({context:e})=>({listener:e.sharedListener||oF(e.client),id:e.id})}),"stop listening to server-sent events":(0,tP.R)("listener"),"buffer remote mutation events":(0,oh.a)({mutationEvents:({event:e,context:t})=>((0,op.ZD)(e,"mutation"),[...t.mutationEvents,e])}),"restore stashed changes":(0,oh.a)({stagedChanges:({event:e,context:t})=>((0,op.ZD)(e,"xstate.done.actor.submitTransactions"),t.stashedChanges),stashedChanges:[]}),"rebase fetched remote snapshot":(0,oh.b)(({enqueue:e})=>{e.assign(({event:e,context:t})=>{(0,op.ZD)(e,"xstate.done.actor.getDocument");let n=t.remote,r=e.output,i=!1;for(let e of t.mutationEvents)e.effects?.apply&&(e.previousRev||"appear"===e.transition)&&(i||e.previousRev!==r?._rev||(i=!0),i&&(r=oU(r,e.effects.apply,e.resultRev)));t.cache&&(!t.cache.has(t.id)||t.cache.get(t.id)._rev!==r?._rev)&&t.cache.set(t.id,r);let[o,a]=oD(t.id,null===n?void 0:n,null===r?void 0:r,t.stagedChanges);return{remote:r,local:a,stagedChanges:o,mutationEvents:[]}}),e.sendParent(({context:e})=>({type:"rebased.remote",id:e.id,document:e.remote}))}),"apply mendoza patch":(0,oh.a)(({event:e,context:t})=>{(0,op.ZD)(e,"mutation");let n=t.remote;if(e.transactionId===n?._rev)return{};let r=oU(n,e.effects.apply,e.resultRev);t.cache&&(!t.cache.has(t.id)||t.cache.get(t.id)._rev!==r?._rev)&&t.cache.set(t.id,r);let[i,o]=oD(t.id,null===n?void 0:n,null===r?void 0:r,t.stagedChanges);return{remote:r,local:o,stagedChanges:i}}),"increment fetch attempts":(0,oh.a)({fetchRemoteSnapshotAttempts:({context:e})=>e.fetchRemoteSnapshotAttempts+1}),"reset fetch attempts":(0,oh.a)({fetchRemoteSnapshotAttempts:0}),"increment submit attempts":(0,oh.a)({submitTransactionsAttempts:({context:e})=>e.submitTransactionsAttempts+1}),"reset submit attempts":(0,oh.a)({submitTransactionsAttempts:0}),"stage mutation":(0,oh.a)({stagedChanges:({event:e,context:t})=>((0,op.ZD)(e,"mutate"),[...t.stagedChanges,{transaction:!1,mutations:e.mutations}])}),"stash mutation":(0,oh.a)({stashedChanges:({event:e,context:t})=>((0,op.ZD)(e,"mutate"),[...t.stashedChanges,{transaction:!1,mutations:e.mutations}])}),"rebase local snapshot":(0,oh.b)(({enqueue:e})=>{e.assign({local:({event:e,context:t})=>{(0,op.ZD)(e,"mutate");let n=new Map;return t.local&&n.set(t.id,t.local),function(e,t){e.forEach(e=>{("created"===e.status||"updated"===e.status)&&t.set(e.id,e.after),"deleted"===e.status&&t.delete(e.id)})}(function(e,t){let n=Object.create(null);for(let r of e){let e=oy(r);if(!e)throw Error("Unable to get document id from mutation");let i=n[e]?.after||t.get(e),o=oR(i,r);if("error"===o.status)throw Error(o.message);"noop"!==o.status&&("updated"===o.status||"created"===o.status||"deleted"===o.status)&&(e in n||(n[e]={before:i,after:void 0,muts:[]}),n[e].after=o.after)}return Object.entries(n).map(([e,{before:t,after:n,muts:r}])=>({id:e,status:n?t?"updated":"created":"deleted",mutations:r,before:t,after:n}))}(e.mutations,n),n),n.get(t.id)}}),e.sendParent(({context:e})=>({type:"rebased.local",id:e.id,document:e.local}))}),"send pristine event to parent":(0,oh.s)(({context:e})=>({type:"pristine",id:e.id})),"send sync event to parent":(0,oh.s)(({context:e})=>({type:"sync",id:e.id,document:e.remote})),"send mutation event to parent":(0,oh.s)(({context:e,event:t})=>((0,op.ZD)(t,"mutation"),{type:"mutation",id:e.id,previousRev:t.previousRev,resultRev:t.resultRev,effects:t.effects}))},actors:{"server-sent events":(0,H.Y$)(({input:e})=>{let{listener:t,id:n}=e;return(0,i7.P)(()=>t).pipe((0,i9.h)(e=>"welcome"===e.type||"reconnect"===e.type||"mutation"===e.type&&e.documentId===n),(0,oe.Q)(of))}),"fetch remote snapshot":(0,H.p4)(async({input:e,signal:t})=>{let{client:n,id:r}=e;return await n.getDocument(r,{signal:t}).catch(e=>{if(!(e instanceof Error&&"AbortError"===e.name))throw e})}),"submit mutations as transactions":(0,H.p4)(async({input:e,signal:t})=>{let{client:n,transactions:r}=e;for(let e of r){if(t.aborted)return;await n.dataRequest("mutate",{transactionId:e.id,mutations:e.mutations.flatMap(oN)},{visibility:"async",returnDocuments:!1,signal:t}).catch(e=>{if(!(e instanceof Error&&"AbortError"===e.name))throw e})}})},delays:{fetchRemoteSnapshotTimeout:({context:e})=>1e3*Math.pow(2,e.fetchRemoteSnapshotAttempts),submitTransactionsTimeout:({context:e})=>1e3*Math.pow(2,e.submitTransactionsAttempts)}}).createMachine({id:"document-mutator",context:({input:e})=>({client:e.client.withConfig({allowReconfigure:!1}),sharedListener:e.sharedListener,id:e.id,remote:void 0,local:void 0,mutationEvents:[],stagedChanges:[],stashedChanges:[],error:void 0,fetchRemoteSnapshotAttempts:0,submitTransactionsAttempts:0,cache:e.cache}),entry:["connect to server-sent events"],on:{mutate:{actions:["rebase local snapshot","stage mutation"]}},initial:"disconnected",states:{disconnected:{on:{connect:{target:"connecting",actions:["listen to server-sent events"]}}},connecting:{on:{welcome:"connected",reconnect:"reconnecting",error:"connectFailure"},tags:["busy"]},connectFailure:{on:{connect:{target:"connecting",actions:["listen to server-sent events"]}},entry:["stop listening to server-sent events","assign error to context"],exit:["clear error from context"],tags:["error"]},reconnecting:{on:{welcome:{target:"connected"},error:{target:"connectFailure"}},tags:["busy","error"]},connected:{on:{mutation:{actions:["buffer remote mutation events"]},reconnect:"reconnecting"},entry:["clear error from context"],initial:"loading",states:{loading:{invoke:{src:"fetch remote snapshot",id:"getDocument",input:({context:e})=>({client:e.client,id:e.id}),onError:{target:"loadFailure"},onDone:{target:"loaded",actions:["rebase fetched remote snapshot","reset fetch attempts"]}},tags:["busy"]},loaded:{entry:["send sync event to parent"],on:{mutation:{actions:["apply mendoza patch","send mutation event to parent"]}},initial:"pristine",states:{pristine:{on:{mutate:{actions:["rebase local snapshot","stage mutation"],target:"dirty"}},tags:["ready"]},dirty:{on:{submit:"submitting"},tags:["ready"]},submitting:{on:{mutate:{actions:["rebase local snapshot","stash mutation"]}},invoke:{src:"submit mutations as transactions",id:"submitTransactions",input:({context:e})=>{let t=new Map;return t.set(e.id,e.remote),{client:e.client,transactions:(function(e,t){let n=[],r=[];return e.forEach(e=>{t(e)?r.push(e):(r.length>0&&n.push(r),r=[],n.push([e]))}),r.length>0&&n.push(r),n})(e.stagedChanges,e=>!e.transaction).flatMap(e=>({...e[0],mutations:e.flatMap(e=>e.mutations)})).map(e=>({...e,mutations:Object.values(iQ(e.mutations,oy)).flatMap(e=>{var t;return(0===(t=0===e.length?e:e.reduce((e,t)=>"delete"===t.type?[t]:(e.push(t),e),[])).length?t:t.reduce((e,t)=>("createIfNotExists"!==t.type?e.push(t):oC(e,e=>"delete"===e.type).find(e=>"createIfNotExists"===e.type)||e.push(t),e),[])).flat().reduce((e,t)=>{let n=e[e.length-1];return n&&"patch"!==n.type||"patch"!==t.type?e.concat(t):e.slice(0,-1).concat({...t,patches:(n?.patches||[]).concat(t.patches)})},[])})})).map(e=>({...e,mutations:e.mutations.map(e=>"patch"!==e.type?e:{...e,patches:e.patches.reduce((e,t)=>{if("unset"!==t.op.type)return e.push(t),e;let n=e.filter(e=>{var n,r;return n=t.path,r=e.path,!(n.length<=r.length&&n.every((e,t)=>{var n;return n=function(e,t){if(t<0||t>=e.length)throw Error("Index out of bounds");return e[t]}(r,t),og(e)&&og(n)?e._key===n._key:"number"==typeof e?Number(e)===Number(n):e===n}))});return n.push(t),n},[]).reduceRight((e,t)=>(e.find(e=>{var n,r;return n=e.op,("set"===(r=t.op).type||"unset"===r.type)&&("set"===n.type||"unset"===n.type)&&oT(e.path,t.path)})||e.unshift(t),e),[]).reduce((e,t)=>("setIfMissing"!==t.op.type?e.push(t):oC(e,e=>"unset"===e.op.type).find(e=>"setIfMissing"===e.op.type&&oT(e.path,t.path))||e.push(t),e),[])})})).map(e=>({...e,mutations:e.mutations.map((e,n)=>{var r;return"patch"===e.type&&(r=t.get(e.id))?{...e,patches:oL(r,e.patches)}:e})})).map(e=>e.transaction&&void 0!==e.id?{id:e.id,mutations:e.mutations}:{mutations:e.mutations})}},onError:{target:"submitFailure"},onDone:{target:"pristine",actions:["restore stashed changes","reset submit attempts","send pristine event to parent"]}},tags:["busy","ready"]},submitFailure:{exit:["clear error from context"],after:{submitTransactionsTimeout:{actions:["increment submit attempts"],target:"submitting"}},on:{retry:"submitting"},tags:["error","ready"]}}},loadFailure:{exit:["clear error from context"],after:{fetchRemoteSnapshotTimeout:{actions:["increment fetch attempts"],target:"loading"}},on:{retry:"loading"},tags:["error"]}}}}});function oU(e,t,n){var r;let i=(r=function(e){if(!e)return null;let{_rev:t,...n}=e;return n}(e),new i1(new i3,r,t).process());return i?Object.assign(i,{_rev:n}):null}let oW=e=>{let t=(0,H.p4)(async({input:t,signal:n})=>{let{id:r}=t,{snapshot:i}=await e.fetch("visual-editing/fetch-snapshot",{documentId:r},{signal:n});return i}),n=(0,H.p4)(async({input:t})=>{let{transactions:n}=t;for(let t of n){let n=W.encodeTransaction(t);return e.post("visual-editing/mutate",n)}});return oz.provide({actions:{"send sync event to parent":(0,oh.b)(({enqueue:e})=>{e.sendParent(({context:e})=>({type:"sync",id:e.id,document:e.remote})),e.emit(({context:e})=>({type:"ready",snapshot:e.local}))})},actors:{"fetch remote snapshot":t,"submit mutations as transactions":n}})},oV=(0,op.cY)({types:{},actions:{"emit sync event":(0,oh.e)(({event:e})=>((0,op.ZD)(e,"sync"),e)),"emit mutation event":(0,oh.e)(({event:e})=>((0,op.ZD)(e,"mutation"),e)),"emit rebased event":(0,oh.e)(({event:e})=>((0,op.ZD)(e,["rebased.local","rebased.remote"]),e)),"emit pristine event":(0,oh.e)(({event:e})=>((0,op.ZD)(e,["pristine"]),e)),"add document actor":(0,oh.a)({documents:({context:e,event:t,spawn:n})=>{(0,op.ZD)(t,"observe");let r=t.documentId;return e.documents[r]?e.documents:{...e.documents,[r]:n("documentMutatorMachine",{input:{id:r,client:e.client,sharedListener:e.sharedListener||oF(e.client)},id:r})}}}),"stop remote snapshot":(0,tP.R)(({context:e,event:t})=>((0,op.ZD)(t,"unobserve"),e.documents[t.documentId])),"remove remote snapshot from context":(0,oh.a)({documents:({context:e,event:t})=>{if((0,op.ZD)(t,"unobserve"),!e.documents[t.documentId])return e.documents;let{[t.documentId]:n,...r}=e.documents;return r}})},actors:{documentMutatorMachine:oz}}).createMachine({id:"dataset-mutator",context:({input:e})=>({documents:{},client:e.client,sharedListener:e.sharedListener}),on:{sync:{actions:["emit sync event"]},mutation:{actions:["emit mutation event"]},"rebased.*":{actions:["emit rebased event"]},pristine:{actions:["emit pristine event"]},observe:{actions:["add document actor"]},unobserve:{actions:["stop remote snapshot","remove remote snapshot from context"]}},initial:"pristine",states:{pristine:{}}}),oq=e=>oV.provide({actors:{documentMutatorMachine:oW(e)}}),oB=e=>{let t,n,r,i;let o=(0,w.c)(8),{comlink:a,history:s}=e;return o[0]!==a||o[1]!==s?(t=()=>a?.on("presentation/navigate",e=>{s?.update(e)}),n=[a,s],o[0]=a,o[1]=s,o[2]=t,o[3]=n):(t=o[2],n=o[3]),(0,x.useEffect)(t,n),o[4]!==a||o[5]!==s?(r=()=>{if(s)return s.subscribe(e=>{e.title=e.title||document.title,a?.post("visual-editing/navigate",e)})},i=[a,s],o[4]=a,o[5]=s,o[6]=r,o[7]=i):(r=o[6],i=o[7]),(0,x.useEffect)(r,i),null},oG=e=>{let t,n;let r=(0,w.c)(3),{comlink:i}=e;return r[0]!==i?(t=()=>{let e=()=>{i.post("visual-editing/meta",{title:document.title})},t=new MutationObserver(t=>{let[n]=t;"TITLE"===n.target.nodeName&&e()});return t.observe(document.head,{subtree:!0,characterData:!0,childList:!0}),e(),()=>t.disconnect()},n=[i],r[0]=i,r[1]=t,r[2]=n):(t=r[1],n=r[2]),(0,x.useEffect)(t,n),null};function oH(e){let t=new CustomEvent("sanity/dragEnd",{detail:e,cancelable:!0});window.dispatchEvent(t)}let oZ=(0,x.createContext)(null);function oK(){let e=(0,x.useContext)(oZ);if(!e)throw Error("Schema context is missing");return e}function oY(e,t,n,r){if(!e.type)throw Error("Node type is missing");return()=>t.patch(()=>(function(e,t,n){let{path:r,key:i}=eq(e);return[G(r,V([{_type:t,_key:eV()}],n,{_key:i}))]})(e,n,r))}function oX(e){let{node:t,doc:n}=e;return n?[{type:"action",label:"Duplicate",icon:M.C,action:function(e,t){if(!e.type)throw Error("Node type is missing");return()=>t.patch(async({getSnapshot:t})=>(function(e,t,n="after"){let{path:r,key:i}=eq(e);return[G(r,V({...eP(t,e.path),_key:eV()},n,{_key:i}))]})(e,await t()))}(t,n)}]:[]}function oJ(e){let{node:t,doc:n}=e;return n?[{type:"action",label:"Remove",icon:M.R,action:function(e,t){if(!e.type)throw Error("Node type is missing");return()=>t.patch(async({getSnapshot:t})=>(function(e,t){let{path:n,key:r}=eq(e),i=eP(t,n).findIndex(e=>e._key===r);return[G(n,q(i,i+1))]})(e,await t()))}(t,n)}]:[]}async function oQ(e,t=!0){let{node:n,doc:r}=e;if(!r)return[];let i=[],o=[],[a,s,l,u]=await Promise.all([eB(n,r,"previous"),eB(n,r,"next"),eB(n,r,"first"),eB(n,r,"last")]);return l.length&&o.push({type:"action",label:"To top",icon:M.c,action:()=>r.patch(l)}),a.length&&o.push({type:"action",label:"Up",icon:M.e,action:()=>r.patch(a)}),s.length&&o.push({type:"action",label:"Down",icon:M.f,action:()=>r.patch(s)}),u.length&&o.push({type:"action",label:"To bottom",icon:M.U,action:()=>r.patch(u)}),o.length&&(i.push({type:"group",label:"Move",icon:M.S,items:o}),t&&i.push({type:"divider"})),i}let o0=e=>{let t,n,r,i,o;let a=(0,w.c)(12),{label:s,parent:l,width:u,onSelect:c,boundaryElement:d}=e;return a[0]===Symbol.for("react.memo_cache_sentinel")?(t=["left-start","right","left","right-end","left-end","bottom","top"],a[0]=t):t=a[0],a[1]===Symbol.for("react.memo_cache_sentinel")?(n=[4,4,4,4],a[1]=n):n=a[1],a[2]!==d||a[3]!==u?(r={arrow:!1,constrainSize:!0,floatingBoundary:d,padding:0,placement:"right-start",fallbackPlacements:t,preventOverflow:!0,width:u,__unstable_margins:n},a[2]=d,a[3]=u,a[4]=r):r=a[4],a[5]!==c||a[6]!==l?(i=(0,b.jsx)(M.h,{node:l,onSelect:c}),a[5]=c,a[6]=l,a[7]=i):i=a[7],a[8]!==s||a[9]!==r||a[10]!==i?(o=(0,b.jsx)(M.M,{fontSize:1,icon:M.b,padding:2,popover:r,space:2,text:s,children:i}),a[8]=s,a[9]=r,a[10]=i,a[11]=o):o=a[11],o},o1=[-4,4,-4,4];function o2(e){let t;let n=(0,w.c)(25),{node:r,onDismiss:i,boundaryElement:o}=e;n[0]!==r||n[1]!==i?(t=()=>{"action"===r.type&&(r.action?.(),i?.())},n[0]=r,n[1]=i,n[2]=t):t=n[2];let a=t;if("divider"===r.type){let e;return n[3]===Symbol.for("react.memo_cache_sentinel")?(e=(0,b.jsx)(M.i,{}),n[3]=e):e=n[3],e}if("action"===r.type){let e;let t=!r.action;return n[4]!==r.hotkeys||n[5]!==r.icon||n[6]!==r.label||n[7]!==a||n[8]!==t?(e=(0,b.jsx)(M.p,{fontSize:1,hotkeys:r.hotkeys,icon:r.icon,padding:2,space:2,text:r.label,disabled:t,onClick:a}),n[4]=r.hotkeys,n[5]=r.icon,n[6]=r.label,n[7]=a,n[8]=t,n[9]=e):e=n[9],e}if("group"===r.type){let e,t,a;let s=r.icon;n[10]===Symbol.for("react.memo_cache_sentinel")?(e={arrow:!1,constrainSize:!0,placement:"right-start",fallbackPlacements:["left-start","right","left","right-end","left-end","bottom","top"],preventOverflow:!0,__unstable_margins:o1},n[10]=e):e=n[10];let l=r.label;if(n[11]!==o||n[12]!==r.items||n[13]!==i){let e;n[15]!==o||n[16]!==i?(e=(e,t)=>(0,b.jsx)(o2,{node:e,onDismiss:i,boundaryElement:o},t),n[15]=o,n[16]=i,n[17]=e):e=n[17],t=r.items.map(e),n[11]=o,n[12]=r.items,n[13]=i,n[14]=t}else t=n[14];return n[18]!==r.icon||n[19]!==r.label||n[20]!==t?(a=(0,b.jsx)(M.M,{fontSize:1,icon:s,padding:2,popover:e,space:2,text:l,children:t}),n[18]=r.icon,n[19]=r.label,n[20]=t,n[21]=a):a=n[21],a}if("custom"===r.type){let e;let{component:t}=r;return n[22]!==t||n[23]!==o?(e=(0,b.jsx)(t,{boundaryElement:o}),n[22]=t,n[23]=o,n[24]=e):e=n[24],e}return null}let o3=e=>{let t,n,r,i,o,a,s,l,u,c,d,f,p,h,y,m,g,v;let E=(0,w.c)(43),{node:_,onDismiss:k,position:j}=e,{x:S,y:I}=j,[O,A]=(0,x.useState)(null),{getField:P}=oK(),{getDocument:$}=eU();E[0]!==P||E[1]!==_?(t=P(_),E[0]=P,E[1]=_,E[2]=t):t=E[2];let{field:R,parent:C}=t;n=R?.title||R?.name||"Unknown type";let[T,L]=(0,x.useState)(void 0);E[3]!==R||E[4]!==$||E[5]!==_||E[6]!==C?(r=()=>{(async()=>{let e=$(_.id);e&&L(await function(e){let{node:t,field:n,parent:r,doc:i}=e;return"arrayItem"===n?.type?async function(e){let{node:t,field:n,doc:r}=e,i=[];return i.push(...oX(e)),i.push(...oJ(e)),i.push(...await oQ(e)),i.push({type:"action",label:"Insert before",icon:M.a,action:oY(t,r,n.name,"before")}),i.push({type:"action",label:"Insert after",icon:M.b,action:oY(t,r,n.name,"after")}),i}({node:t,field:n,doc:i}):"union"===r?.type?async function(e){let{doc:t,node:n,parent:r}=e,i=[];if(i.push(...oX(e)),i.push(...oJ(e)),i.push(...await oQ(e)),r.options?.insertMenu){let e=(r.options.insertMenu||{}).views?.some(e=>"grid"===e.name)?0:void 0;i.push({type:"custom",component:({boundaryElement:i})=>(0,b.jsx)(o0,{label:"Insert before",onSelect:e=>{oY(n,t,e.name,"before")()},parent:r,width:e,boundaryElement:i})}),i.push({type:"custom",component:({boundaryElement:i})=>(0,b.jsx)(o0,{label:"Insert after",onSelect:e=>{oY(n,t,e.name,"after")()},parent:r,width:e,boundaryElement:i})})}else i.push({type:"group",label:"Insert before",icon:M.a,items:r.of.filter(e=>"unionOption"===e.type).map(e=>({type:"action",icon:(0,M.g)(e),label:"block"===e.name?"Paragraph":e.title||e.name,action:oY(n,t,e.name,"before")}))}),i.push({type:"group",label:"Insert after",icon:M.b,items:r.of.filter(e=>"unionOption"===e.type).map(e=>({type:"action",label:"block"===e.name?"Paragraph":e.title||e.name,icon:(0,M.g)(e),action:oY(n,t,e.name,"after")}))});return i}({node:t,parent:r,doc:i}):Promise.resolve([])}({node:_,field:R,parent:C,doc:e}))})()},i=[R,_,C,$],E[3]=R,E[4]=$,E[5]=_,E[6]=C,E[7]=r,E[8]=i):(r=E[7],i=E[8]),(0,x.useEffect)(r,i),E[9]!==S||E[10]!==I?(a={getBoundingClientRect:()=>({bottom:I,left:S,right:S,top:I,width:0,height:0})},E[9]=S,E[10]=I,E[11]=a):a=E[11],o=a,E[12]!==R?(l=(0,M.g)(R),E[12]=R,E[13]=l):l=E[13],s=l,E[14]===Symbol.for("react.memo_cache_sentinel")?(u={minWidth:120,maxWidth:160},E[14]=u):u=E[14],E[15]!==s||E[16]!==T?(c=(0,b.jsx)(M.j,{flex:"none",children:T?(0,b.jsx)(M.T,{size:1,children:s}):(0,b.jsx)(M.k,{size:1})}),E[15]=s,E[16]=T,E[17]=c):c=E[17];let D=T?n:"Loading...";E[18]!==D?(d=(0,b.jsx)(M.l,{flex:1,space:2,children:(0,b.jsx)(M.T,{size:1,weight:"semibold",children:D})}),E[18]=D,E[19]=d):d=E[19],E[20]!==c||E[21]!==d?(f=(0,b.jsxs)(M.F,{gap:2,padding:2,children:[c,d]}),E[20]=c,E[21]=d,E[22]=f):f=E[22],E[23]!==O||E[24]!==T||E[25]!==k?(p=T&&(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(M.i,{}),T.map((e,t)=>(0,b.jsx)(o2,{node:e,onDismiss:k,boundaryElement:O},t))]}),E[23]=O,E[24]=T,E[25]=k,E[26]=p):p=E[26],E[27]!==f||E[28]!==p?(h=(0,b.jsxs)(M.m,{style:u,children:[f,p]}),E[27]=f,E[28]=p,E[29]=h):h=E[29];let N=`${S}-${I}`;return E[30]!==S||E[31]!==I?(y={position:"absolute",left:S,top:I},E[30]=S,E[31]=I,E[32]=y):y=E[32],E[33]!==N||E[34]!==y?(m=(0,b.jsx)("div",{style:y},N),E[33]=N,E[34]=y,E[35]=m):m=E[35],E[36]!==o||E[37]!==h||E[38]!==m?(g=(0,b.jsx)(M.n,{__unstable_margins:o1,arrow:!1,open:!0,placement:"right-start",referenceElement:o,content:h,children:m}),E[36]=o,E[37]=h,E[38]=m,E[39]=g):g=E[39],E[40]!==k||E[41]!==g?(v=(0,b.jsx)(M.o,{setBoundaryElement:A,onDismiss:k,children:g}),E[40]=k,E[41]=g,E[42]=v):v=E[42],v},o5=(0,x.createContext)(null),o4=(0,M.d)(M.q)`
  background-color: var(--overlay-bg);
  border-radius: 3px;
  pointer-events: none;
  position: absolute;
  will-change: transform;
  box-shadow: var(--overlay-box-shadow);
  transition: none;

  --overlay-bg: transparent;
  --overlay-box-shadow: inset 0 0 0 1px transparent;

  [data-overlays] & {
    --overlay-bg: color-mix(in srgb, transparent 95%, var(--card-focus-ring-color));
    --overlay-box-shadow: inset 0 0 0 2px
      color-mix(in srgb, transparent 50%, var(--card-focus-ring-color));
  }

  [data-fading-out] & {
    transition:
      box-shadow 1550ms,
      background-color 1550ms;

    --overlay-bg: rgba(0, 0, 255, 0);
    --overlay-box-shadow: inset 0 0 0 1px transparent;
  }

  &[data-focused] {
    --overlay-box-shadow: inset 0 0 0 1px var(--card-focus-ring-color);
  }

  &[data-hovered]:not([data-focused]) {
    transition: none;
    --overlay-box-shadow: inset 0 0 0 2px var(--card-focus-ring-color);
  }

  /* [data-unmounted] & {
    --overlay-box-shadow: inset 0 0 0 1px var(--card-focus-ring-color);
  } */

  :link {
    text-decoration: none;
  }
`,o8=(0,M.d)(M.F)`
  bottom: 100%;
  cursor: pointer;
  pointer-events: none;
  position: absolute;
  right: 0;

  [data-hovered] & {
    pointer-events: all;
  }
`,o6=(0,M.d)(M.q)`
  background-color: var(--card-focus-ring-color);
  right: 0;
  border-radius: 3px;

  & [data-ui='Text'] {
    color: #fff;
    white-space: nowrap;
  }
`,o9=(0,M.d)(M.F)`
  bottom: 100%;
  cursor: pointer;
  pointer-events: none;
  position: absolute;
  left: 0;
`,o7=(0,M.d)(M.F)`
  display: flex;
  align-items: center;
  background-color: var(--card-focus-ring-color);
  right: 0;
  border-radius: 3px;
  & [data-ui='Text'],
  & [data-sanity-icon] {
    color: #fff;
    white-space: nowrap;
  }
`,ae=e=>{let t,n,r,i,o,a,s,l,u,c;let d=(0,w.c)(35),{element:f,focused:p,componentResolver:h,node:y,showActions:m,draggable:g}=e,{getField:v,getType:E}=oK();d[0]!==E||d[1]!==y?(t=E(y),d[0]=E,d[1]=y,d[2]=t):t=d[2];let _=t;d[3]!==v||d[4]!==y?(n=v(y),d[3]=v,d[4]=y,d[5]=n):n=d[5];let{field:k,parent:j}=n;d[6]!==y?(r="path"in y?function(e){let{id:t,type:n,path:r,baseUrl:i,tool:o,workspace:a}=e;return function(e){let{baseUrl:t,workspace:n="default",tool:r="default",id:i,type:o,path:a,projectId:s,dataset:l}=e;if(!t)throw Error("baseUrl is required");if(!a)throw Error("path is required");if(!i)throw Error("id is required");if("/"!==t&&t.endsWith("/"))throw Error("baseUrl must not end with a slash");let u="default"===n?void 0:n,c="default"===r?void 0:r,d=i.startsWith(es)?i.slice(es.length):i,f=Array.isArray(a)?er(a.map(e=>{if("string"==typeof e||"number"==typeof e)return e;if(""!==e._key)return{_key:e._key};if(-1!==e._index)return e._index;throw Error(`invalid segment:${JSON.stringify(e)}`)})):a,p=new URLSearchParams({baseUrl:t,id:d,type:o,path:f});u&&p.set("workspace",u),c&&p.set("tool",c),s&&p.set("projectId",s),l&&p.set("dataset",l),i.startsWith(es)&&p.set("isDraft","");let h=["/"===t?"":t];u&&h.push(u);let y=["mode=presentation",`id=${d}`,`type=${o}`,`path=${encodeURIComponent(f)}`];return c&&y.push(`tool=${c}`),h.push("intent","edit",`${y.join(";")}?${p}`),h.join("/")}({baseUrl:i,workspace:a,tool:o,type:n,id:t,path:function(e){let t="";for(let n of e)"string"!=typeof n?"number"!=typeof n?null!==n&&Array.isArray(n)?(t&&(t+=":"),t+=`${n.join(",")}}`):n._key&&(t&&(t+=":"),t+=`${n._key}`):(t&&(t+=":"),t+=`${n}`):(t&&(t+="."),t+=n);return t}(ea.fromString(r))})}(y):y.href,d[6]=y,d[7]=r):r=d[7];let S=r,I=function(){let e=(0,x.useContext)(o5);if(!e)throw Error("Preview Snapshots context is missing");return e}();e:{let e;if(!("path"in y)){i=void 0;break e}d[8]!==y.id||d[9]!==I?(e=I.find(e=>e._id===y.id)?.title,d[8]=y.id,d[9]=I,d[10]=e):e=d[10],i=e}let O=i;e:{let e;if(!("path"in y)||!k||!_){o=void 0;break e}let t=k.value.type,n=!!p;d[11]!==f||d[12]!==k||d[13]!==y||d[14]!==j||d[15]!==_||d[16]!==n||d[17]!==t?(e={document:_,element:f,field:k,focused:n,node:y,parent:j,type:t},d[11]=f,d[12]=k,d[13]=y,d[14]=j,d[15]=_,d[16]=n,d[17]=t,d[18]=e):e=d[18],o=e}let A=o,P=function(e,t){let n;let r=(0,w.c)(4);e:{let i;if(!e){n=void 0;break e}if(r[0]!==e||r[1]!==t){let o=t?.(e);if(!o){n=void 0;break e}if((0,x.isValidElement)(o)){n=o;break e}i=(Array.isArray(o)?o:[o]).map(an),r[0]=e,r[1]=t,r[2]=i,r[3]=n}else i=r[2],n=r[3];n=i}return n}(A,h);d[19]!==_?(a=_?.icon?(0,b.jsx)("div",{dangerouslySetInnerHTML:{__html:_.icon}}):(0,b.jsx)(M.D,{}),d[19]=_,d[20]=a):a=d[20];let $=a;return d[21]!==S||d[22]!==m?(s=m?(0,b.jsx)(o8,{gap:1,paddingBottom:1,"data-sanity-overlay-element":!0,children:(0,b.jsx)(ar,{href:S})}):null,d[21]=S,d[22]=m,d[23]=s):s=d[23],d[24]!==g||d[25]!==$||d[26]!==O?(l=O&&(0,b.jsx)(o9,{gap:1,paddingBottom:1,children:(0,b.jsxs)(o7,{gap:2,padding:2,children:[g&&(0,b.jsx)(M.j,{marginRight:1,children:(0,b.jsx)(M.T,{className:"drag-handle",size:0,children:(0,b.jsx)(M.r,{})})}),(0,b.jsx)(M.T,{size:0,children:$}),(0,b.jsx)(M.T,{size:1,weight:"medium",children:O})]})}),d[24]=g,d[25]=$,d[26]=O,d[27]=l):l=d[27],d[28]!==A||d[29]!==P?(u=Array.isArray(P)?P.map((e,t)=>{let{component:n,props:r}=e;return(0,b.jsx)(n,{PointerEvents:M.P,...A,...r},t)}):P,d[28]=A,d[29]=P,d[30]=u):u=d[30],d[31]!==s||d[32]!==l||d[33]!==u?(c=(0,b.jsxs)(b.Fragment,{children:[s,l,u]}),d[31]=s,d[32]=l,d[33]=u,d[34]=c):c=d[34],c},at=(0,x.memo)(function(e){let t,n,r,i,o,a;let s=(0,w.c)(17),{focused:l,hovered:u,rect:c,wasMaybeCollapsed:d,enableScrollIntoView:f}=e,p=(0,x.useRef)(null),h=(0,x.useRef)(!1),y=`${c.w}px`,m=`${c.h}px`,g=`translate(${c.x}px, ${c.y}px)`;s[0]!==y||s[1]!==m||s[2]!==g?(n={width:y,height:m,transform:g},s[0]=y,s[1]=m,s[2]=g,s[3]=n):n=s[3],t=n,s[4]!==f||s[5]!==l||s[6]!==d?(r=()=>{if(!h.current&&!d&&!0===l&&p.current&&f){let e=p.current;(function(e,t){if(!e.isConnected||!(e=>{let t=e;for(;t&&t.parentNode;){if(t.parentNode===document)return!0;t=t.parentNode instanceof ShadowRoot?t.parentNode.host:t.parentNode}return!1})(e))return;let n=(e=>{let t=window.getComputedStyle(e);return{top:parseFloat(t.scrollMarginTop)||0,right:parseFloat(t.scrollMarginRight)||0,bottom:parseFloat(t.scrollMarginBottom)||0,left:parseFloat(t.scrollMarginLeft)||0}})(e);if("object"==typeof t&&"function"==typeof t.behavior)return t.behavior(tO(e,t));let r="boolean"==typeof t||null==t?void 0:t.behavior;for(let{el:i,top:o,left:a}of tO(e,tA(t))){let e=o-n.top+n.bottom,t=a-n.left+n.right;i.scroll({top:e,left:t,behavior:r})}})(p.current,{behavior:t=>{0!==t.length&&e.scrollIntoView({behavior:"smooth",block:"center",inline:"nearest"})},scrollMode:"if-needed",block:"center",inline:"nearest"})}h.current=!0===l},i=[l,d,f],s[4]=f,s[5]=l,s[6]=d,s[7]=r,s[8]=i):(r=s[7],i=s[8]),(0,x.useEffect)(r,i);let v=l?"":void 0,E=u?"":void 0;return s[9]!==u||s[10]!==e?(o=u&&(0,b.jsx)(ae,{...e}),s[9]=u,s[10]=e,s[11]=o):o=s[11],s[12]!==t||s[13]!==v||s[14]!==E||s[15]!==o?(a=(0,b.jsx)(o4,{"data-focused":v,"data-hovered":E,ref:p,style:t,children:o}),s[12]=t,s[13]=v,s[14]=E,s[15]=o,s[16]=a):a=s[16],a});function an(e){return"object"==typeof e&&"component"in e?e:{component:e,props:{}}}let ar=(0,x.memo)(function(e){let t,n,r,i;let o=(0,w.c)(6),a=(0,x.useSyncExternalStore)(ai,ao);return o[0]!==e.href||o[1]!==a?(n=function e(t,n){try{let r=new URL(t,typeof location>"u"?void 0:location.origin);if(r.hash){let t=new URL(e(r.hash.slice(1),n));return`${r.origin}${r.pathname}${r.search}#${t.pathname}${t.search}`}return r.searchParams.set("preview",n),r.toString()}catch{return t}}(e.href,a),o[0]=e.href,o[1]=a,o[2]=n):n=o[2],t=n,o[3]===Symbol.for("react.memo_cache_sentinel")?(r=(0,b.jsx)(o6,{padding:2,children:(0,b.jsx)(M.T,{size:1,weight:"medium",children:"Open in Studio"})}),o[3]=r):r=o[3],o[4]!==t?(i=(0,b.jsx)(M.j,{as:"a",href:t,target:"_blank",rel:"noopener noreferrer",children:r}),o[4]=t,o[5]=i):i=o[5],i});function ai(e){let t=()=>e();return window.addEventListener("popstate",t),()=>window.removeEventListener("popstate",t)}function ao(){return window.location.href}let aa=e=>{let t;let n=(0,w.c)(5),{dragGroupRect:r}=e,i=`${r.y}px`,o=`${r.x}px`,a=r.w-1+"px",s=r.h-1+"px";return n[0]!==i||n[1]!==o||n[2]!==a||n[3]!==s?(t=(0,b.jsx)("div",{style:{position:"absolute",top:i,left:o,width:a,height:s,border:"1px dashed #f0709b",pointerEvents:"none"}}),n[0]=i,n[1]=o,n[2]=a,n[3]=s,n[4]=t):t=n[4],t},as=({dragInsertPosition:e})=>{if(null===e)return;let t=0,n=0,r=0,i=0;if("horizontal"==(e?.left||e?.right?"horizontal":"vertical")){let{left:o,right:a}=e;if(r=6,a&&o){let e=o.rect.x+o.rect.w,r=a.rect.x,s=.0125*Math.min(a.rect.h,o.rect.h);t=.5*e+.5*r-3,n=o.rect.y+s,i=Math.min(a.rect.h,o.rect.h)-2*s}else if(a&&!o){let e=.0125*a.rect.h;t=a.rect.x-3,n=a.rect.y+e,i=a.rect.h-2*e}else if(o&&!a){let e=.0125*o.rect.h;t=o.rect.x+o.rect.w-3,n=o.rect.y+e,i=o.rect.h-2*e}}else{let{bottom:o,top:a}=e;if(o&&a){let e=Math.min(a.rect.x,o.rect.x),s=a.rect.y+a.rect.h,l=o.rect.y,u=.0125*Math.min(o.rect.w,a.rect.w);i=6,t=e+u,n=.5*s+.5*l-3,r=Math.max(o.rect.w,a.rect.w)-2*u}else if(o&&!a){let e=.0125*o.rect.w;t=o.rect.x+e,n=o.rect.y-3,r=o.rect.w-2*e,i=6}else if(a&&!o){let e=.0125*a.rect.w;t=a.rect.x+e,n=a.rect.y+a.rect.h-3,r=a.rect.w-2*e,i=6}}return(0,b.jsx)("div",{style:{position:"absolute",width:`${r}px`,height:`${i}px`,transform:`translate(${t}px, ${n}px)`,background:"#556bfc",border:"2px solid white",borderRadius:"999px",zIndex:"999999"}})},al=M.d.div`
  --drag-preview-opacity: 0.98;
  --drag-preview-skeleton-stroke: #ffffff;

  @media (prefers-color-scheme: dark) {
    --drag-preview-skeleton-stroke: #383d51;
  }

  position: fixed;
  display: grid;
  transform: ${({$scaleFactor:e,$width:t,$height:n})=>`translate(calc(var(--drag-preview-x) - ${t/2}px), calc(var(--drag-preview-y) - ${n/2}px)) scale(${e})`};
  width: ${({$width:e})=>`${e}px`};
  height: ${({$height:e})=>`${e}px`};
  z-index: 9999999;
  opacity: var(--drag-preview-opacity);
  cursor: move;

  .drag-preview-content-wrapper {
    position: relative;
    width: 100%;
    height: 100%;
    container-type: inline-size;
  }

  [data-ui='card'] {
    position: relative;
    width: 100%;
    height: 100%;
  }

  .drag-preview-skeleton {
    position: absolute;
    inset: 0;

    rect {
      stroke: var(--drag-preview-skeleton-stroke);
    }
  }

  .drag-preview-handle {
    position: absolute;
    top: 4cqmin;
    left: 4cqmin;
    width: 6cqmin;
    fill: var(--drag-preview-handle-fill);
  }
`,au=e=>{var t,n;let r,i,o,a,s,l,u;let c=(0,w.c)(24),{skeleton:d}=e,f=Math.min(d.maxWidth,window.innerWidth/2),p=d.w>f?f/d.w:1,h=d.offsetX*p,y=d.offsetY*p,m=(0,M.u)(),g=(0,M.s)(),v=g.radius[~~((n=(d.w-0)*((t=g.radius.length-2)-1)/1920+1)<1?1:n>t?t:n)];c[0]!==d.childRects?(r=d.childRects.filter(ac),c[0]=d.childRects,c[1]=r):r=c[1];let x=r;c[2]!==d.childRects?(i=d.childRects.filter(ad),c[2]=d.childRects,c[3]=i):i=c[3];let E=i,_=d.w,k=d.h,j=m?"dark":"light",S=`0 0 ${d.w} ${d.h}`;return c[4]!==x||c[5]!==E?(o=[...x,...E],c[4]=x,c[5]=E,c[6]=o):o=c[6],c[7]!==o||c[8]!==g.color.skeleton.from?(a=o.map((e,t)=>(0,b.jsx)("rect",{x:e.x,y:e.y,width:e.w,height:e.h,fill:g.color.skeleton.from},t)),c[7]=o,c[8]=g.color.skeleton.from,c[9]=a):a=c[9],c[10]!==S||c[11]!==a?(s=(0,b.jsx)("div",{className:"drag-preview-content-wrapper",children:(0,b.jsx)("svg",{className:"drag-preview-skeleton",viewBox:S,children:a})}),c[10]=S,c[11]=a,c[12]=s):s=c[12],c[13]!==v||c[14]!==j||c[15]!==s?(l=(0,b.jsx)(M.q,{radius:v,shadow:4,overflow:"hidden",tone:"transparent",scheme:j,children:s}),c[13]=v,c[14]=j,c[15]=s,c[16]=l):l=c[16],c[17]!==h||c[18]!==y||c[19]!==p||c[20]!==d.h||c[21]!==d.w||c[22]!==l?(u=(0,b.jsx)(al,{$width:_,$height:k,$offsetX:h,$offsetY:y,$scaleFactor:p,children:l}),c[17]=h,c[18]=y,c[19]=p,c[20]=d.h,c[21]=d.w,c[22]=l,c[23]=u):u=c[23],u};function ac(e){return"IMG"===e.tagName}function ad(e){return"IMG"!==e.tagName}let af=(0,M.d)(M.q)`
  position: fixed;
  bottom: 2rem;
  left: 2rem;
`,ap=()=>{let e,t;let n=(0,w.c)(2);return n[0]===Symbol.for("react.memo_cache_sentinel")?(e={zIndex:"999999"},n[0]=e):e=n[0],n[1]===Symbol.for("react.memo_cache_sentinel")?(t=(0,b.jsx)(af,{padding:2,shadow:2,radius:2,style:e,children:(0,b.jsxs)(M.F,{align:"center",gap:2,children:[(0,b.jsx)(M.H,{keys:["Shift"]}),(0,b.jsx)(M.T,{size:1,children:"Zoom Out"}),(0,b.jsx)(M.E,{})]})}),n[1]=t):t=n[1],t},ah=(e,t)=>{let{type:n}=t;switch(n){case"element/register":return e.find(e=>e.id===t.id)?e:[...e,{id:t.id,activated:!1,element:t.element,focused:!1,hovered:!1,rect:t.rect,sanity:t.sanity,dragDisabled:t.dragDisabled}];case"element/activate":return e.map(e=>e.id===t.id?{...e,activated:!0}:e);case"element/update":return e.map(e=>e.id===t.id?{...e,sanity:t.sanity,rect:t.rect}:e);case"element/unregister":return e.filter(e=>e.id!==t.id);case"element/deactivate":return e.map(e=>e.id===t.id?{...e,activated:!1,hovered:!1}:e);case"element/mouseenter":return e.map(e=>e.id===t.id?{...e,rect:t.rect,hovered:!0}:{...e,hovered:!1});case"element/mouseleave":return e.map(e=>e.id===t.id?{...e,hovered:!1}:e);case"element/updateRect":return e.map(e=>e.id===t.id?{...e,rect:t.rect}:e);case"element/click":return e.map(e=>({...e,focused:e.id===t.id&&"clicked"}));case"overlay/blur":case"presentation/blur":return e.map(e=>({...e,focused:!1}));case"presentation/focus":{let n=e.find(e=>"clicked"===e.focused);return e.map(e=>{let r="path"in e.sanity&&e.sanity.id===t.data.id&&e.sanity.path===t.data.path;return n&&e===n&&r?e:{...e,focused:r&&n?"duplicate":r}})}default:return e}};function ay(e,t){let{type:n}=t,{contextMenu:r,focusPath:i,perspective:o,isDragging:a,dragInsertPosition:s,dragShowMinimap:l,dragShowMinimapPrompt:u,dragSkeleton:c,dragMinimapTransition:d,dragGroupRect:f}=e,p=!1;if("presentation/focus"===n){let n=e.focusPath;n!==(i=t.data.path)&&(p=n.slice(i.length).startsWith("["))}return"presentation/perspective"===n&&(o=t.data.perspective),"element/contextmenu"===n&&(r="sanity"in t?{node:t.sanity,position:t.position}:null),("element/click"===n||"element/mouseleave"===n||"overlay/blur"===n||"presentation/blur"===n||"presentation/focus"===n)&&(r=null),"overlay/dragUpdateInsertPosition"===n&&(s=t.insertPosition),"overlay/dragStart"===n&&(a=!0),"overlay/dragUpdateSkeleton"===t.type&&(c=t.skeleton),"overlay/dragEnd"===n&&(a=!1),"overlay/dragToggleMinimapPrompt"===t.type&&(u=t.display),"overlay/dragStartMinimapTransition"===n&&(d=!0),"overlay/dragEndMinimapTransition"===n&&(d=!1),"overlay/dragUpdateGroupRect"===n&&(f=t.groupRect),"overlay/dragToggleMinimap"===n&&(l=t.display),{...e,contextMenu:r,elements:ah(e.elements,t),dragInsertPosition:s,dragSkeleton:c,dragGroupRect:f,isDragging:a,focusPath:i,perspective:o,wasMaybeCollapsed:p,dragShowMinimap:l,dragShowMinimapPrompt:u,dragMinimapTransition:d}}let am=function(e){let t,n,r,i;let o=(0,w.c)(7),{comlink:a,children:s}=e;o[0]===Symbol.for("react.memo_cache_sentinel")?(t=[],o[0]=t):t=o[0];let[l,u]=(0,x.useState)(t);return o[1]!==a?(n=()=>a?.on("presentation/preview-snapshots",e=>{u(e.snapshots)}),r=[a],o[1]=a,o[2]=n,o[3]=r):(n=o[2],r=o[3]),(0,x.useEffect)(n,r),o[4]!==s||o[5]!==l?(i=(0,b.jsx)(o5.Provider,{value:l,children:s}),o[4]=s,o[5]=l,o[6]=i):i=o[6],i};function ag(e){return"document"===e.type}function av(e){return"type"===e.type}let ab=function(e){let t,n,r,i,o,a,s,l,u,c,d,f,p;let h=(0,w.c)(27),{comlink:y,children:m,elements:g}=e;h[0]===Symbol.for("react.memo_cache_sentinel")?(t=new Map,h[0]=t):t=h[0];let[v,E]=(0,x.useState)(t),[_,k]=(0,x.useState)(null);h[1]!==y?(n=async e=>{if(y)try{let t=await y.fetch("visual-editing/schema",void 0,{signal:e,suppressWarnings:!0});k(t.schema)}catch{}},h[1]=y,h[2]=n):n=h[2];let j=n;h[3]!==y||h[4]!==j?(r=()=>{if(!y)return;let e=new AbortController,t=y.onStatus(()=>{j(e.signal)},"connected");return()=>{e.abort(),t()}},i=[y,j],h[3]=y,h[4]=j,h[5]=r,h[6]=i):(r=h[5],i=h[6]),(0,x.useEffect)(r,i),h[7]===Symbol.for("react.memo_cache_sentinel")?(o=[],h[7]=o):o=h[7];let S=(0,x.useRef)(o);h[8]!==y||h[9]!==g?(a=()=>{let e=new AbortController,t=g.reduce((e,t)=>{let{sanity:n}=t;if(!("id"in n)||!n.path.includes("[_key=="))return e;let r=n.path.split(".").toReversed().reduce((e,t)=>e.length?[t,...e]:t.includes("[_key==")?[t]:[],[]).join(".");return e.find(e=>e.id===n.id&&e.path===r)||e.push({id:n.id,path:r}),e},[]);return t.some(e=>!S.current.find(t=>{let{id:n,path:r}=t;return n===e.id&&r===e.path}))&&(async(e,t)=>{if(e.length&&y)try{let n=await y.fetch("visual-editing/schema-union-types",{paths:e},{signal:t,suppressWarnings:!0});E(n.types),S.current=e}catch{}})(t,e.signal),()=>e.abort()},s=[y,g],h[8]=y,h[9]=g,h[10]=a,h[11]=s):(a=h[10],s=h[11]),(0,x.useEffect)(a,s),h[12]!==_?(l=(e,t)=>{if(!_||"string"!=typeof e&&(!("path"in e)||!Array.isArray(_)))return;let n="string"==typeof e?e:e.type;return _.filter("document"===(t||"document")?ag:av).find(e=>e.name===n)},h[12]=_,h[13]=l):l=h[13];let I=l;h[14]!==I||h[15]!==v?(u=e=>{if(!("path"in e))return{field:void 0,parent:void 0};let t=I(e);if(!t)return{field:void 0,parent:void 0};let n=e.path.split(".").flatMap(aw);try{return function t(n,r,i,o){let a=void 0===o?[]:o;if(!n)return{field:void 0,parent:void 0};let[s,...l]=r;if("fields"in n){let e=n.fields[s];if(!e&&"rest"in n)return t(n.rest,r,n,a);if(!l.length)return{field:e,parent:i};if(!e)throw Error(`[@sanity/visual-editing] No field could be resolved at path: "${[...a,...r].join(".")}"`);return t(e.value,l,n,[...a,s])}if("array"===n.type)return t(n.of,r,n,a);if("arrayItem"===n.type)return l.length?t(n.value,l,n,[...a,s]):{field:n,parent:i};if("union"===n.type){let r=s.startsWith("[_key==")?v?.get(e.id)?.get([a.join("."),s].filter(Boolean).join("")):s;return t(n.of.find(e=>"unionOption"===e.type?e.name===r:e),l,n,[...a,s])}if("unionOption"===n.type)return s?t(n.value,r,n,a):{field:n,parent:i};if("inline"===n.type)return t(I(n.name,"type").value,r,n,a);throw Error(`[@sanity/visual-editing] No field could be resolved at path: "${[...a,...r].join(".")}"`)}(t,n,void 0)}catch(e){return e instanceof Error&&console.warn(e.message),{field:void 0,parent:void 0}}},h[14]=I,h[15]=v,h[16]=u):u=h[16];let O=u;return h[17]!==_?(d=_||[],h[17]=_,h[18]=d):d=h[18],h[19]!==O||h[20]!==I||h[21]!==v||h[22]!==d?(f={getField:O,getType:I,resolvedTypes:v,schema:d},h[19]=O,h[20]=I,h[21]=v,h[22]=d,h[23]=f):f=h[23],c=f,h[24]!==m||h[25]!==c?(p=(0,b.jsx)(oZ.Provider,{value:c,children:m}),h[24]=m,h[25]=c,h[26]=p):p=h[26],p};function aw(e){return e.includes("[")?e.split(/(\[.+\])/,2):[e]}let ax=(e=>{let t=e,n=new Set;return{getState:()=>t,setState:e=>{t=e(t),n.forEach(e=>e())},subscribe:e=>(n.add(e),()=>n.delete(e))}})({}),aE=e=>{let t,n,r,i,o,a,s;let l=(0,w.c)(11),{comlink:u,children:c}=e;return l[0]!==u?(t=()=>u?.on("presentation/shared-state",a_),n=[u],l[0]=u,l[1]=t,l[2]=n):(t=l[1],n=l[2]),(0,x.useEffect)(t,n),l[3]!==u?(r=()=>{(async function(){let e=await u?.fetch("visual-editing/shared-state",void 0,{suppressWarnings:!0});e&&ax.setState(()=>e.state)})().catch(ak)},i=[u],l[3]=u,l[4]=r,l[5]=i):(r=l[4],i=l[5]),(0,x.useEffect)(r,i),l[6]!==u?(a={comlink:u,store:ax},l[6]=u,l[7]=a):a=l[7],o=a,l[8]!==c||l[9]!==o?(s=(0,b.jsx)(tE.Provider,{value:o,children:c}),l[8]=c,l[9]=o,l[10]=s):s=l[10],s};function a_(e){"value"in e?ax.setState(t=>({...t,[e.key]:e.value})):ax.setState(t=>Object.fromEntries(Object.entries(t).filter(t=>{let[n]=t;return n!==e.key})))}function ak(e){console.debug(e),console.warn("[@sanity/visual-editing]: Failed to fetch shared state. Check your version of `@sanity/presentation` is up-to-date")}let aj={"Visual Editing Drag Sequence Completed":{type:"log",name:(v={name:"Visual Editing Drag Sequence Completed",description:"An array is successfully reordered using drag and drop.",version:1}).name,version:v.version,description:v.description,schema:void 0}};function aS(){}function aI(e){return e.id}function aO(e){return!!e}function aA(e){let{sanity:t}=e;return"id"in t?{...t,id:"isDraft"in t?`${ev}${t.id}`:t.id}:null}let aP=M.d.div`
  background-color: transparent;
  direction: ltr;
  inset: 0;
  pointer-events: none;
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: ${({$zIndex:e})=>e??"9999999"};
`;function aM(e){let t,n;return t=requestAnimationFrame(()=>{n=requestAnimationFrame(e)}),()=>{void 0!==t&&cancelAnimationFrame(t),void 0!==n&&cancelAnimationFrame(n)}}let a$=e=>{let t,n,r,i,o;let a=(0,w.c)(8),{documentIds:s}=e;a[0]===Symbol.for("react.memo_cache_sentinel")?(t=[],a[0]=t):t=a[0];let[l,u]=(0,x.useState)(t);a[1]!==s?(n=()=>{u(e=>{let t=Array.from(new Set(s));return e.length===t.length&&0===e.reduce(aT,t)?.length?e:t})},r=[s],a[1]=s,a[2]=n,a[3]=r):(n=a[2],r=a[3]),(0,x.useEffect)(n,r);let c=eT();return a[4]!==c||a[5]!==l?(i=()=>{for(let e of l)c.send({type:"observe",documentId:eR(e)}),c.send({type:"observe",documentId:eC(e)});return()=>{for(let e of l)c.send({type:"unobserve",documentId:eR(e)}),c.send({type:"unobserve",documentId:eC(e)})}},o=[c,l],a[4]=c,a[5]=l,a[6]=i,a[7]=o):(i=a[6],o=a[7]),(0,x.useEffect)(i,o),null},aR=e=>{let t,n,i;let o=(0,w.c)(11),{comlink:a,dispatch:s,inFrame:l,onDrag:u,overlayEnabled:c,rootElement:d}=e,{dispatchDragEndEvent:f}=function(){let e,t,n;let r=(0,w.c)(4),{getDocument:i}=eU();return r[0]!==i?(e=()=>{let e=e=>{let{insertPosition:t,target:n,preventInsertDefault:r}=e.detail;if(r)return;let o=function(e){if(e){let{top:t,right:n,bottom:r,left:i}=e;if(i||t)return{node:(i??t).sanity,position:"after"};if(n||r)return{node:(n??r).sanity,position:"before"}}}(t);if(o){let e=i(n.id),{node:t,position:r}=o,{key:a,hasExplicitKey:s}=eq(n),{path:l,key:u}=eq(t);l&&u&&u!==a&&e.patch(async e=>{let{getSnapshot:t}=e,i=eP(await t(),n.path);return s?[G(l,B({_key:a})),G(l,V(i,r,{_key:u}))]:[G(l,B(~~a)),G(l,V(i,r,u>a?~~u-1:~~u))]})}};return window.addEventListener("sanity/dragEnd",e),()=>{window.removeEventListener("sanity/dragEnd",e)}},t=[i],r[0]=i,r[1]=e,r[2]=t):(e=r[1],t=r[2]),(0,x.useEffect)(e,t),r[3]===Symbol.for("react.memo_cache_sentinel")?(n={dispatchDragEndEvent:oH},r[3]=n):n=r[3],n}();o[0]!==a||o[1]!==s||o[2]!==f||o[3]!==u?(t=e=>{if("element/click"===e.type){let{sanity:t}=e;a?.post("visual-editing/focus",t)}else if("overlay/activate"===e.type)a?.post("visual-editing/toggle",{enabled:!0});else if("overlay/deactivate"===e.type)a?.post("visual-editing/toggle",{enabled:!1});else if("overlay/dragEnd"===e.type){let{insertPosition:t,target:n,dragGroup:r,flow:i,preventInsertDefault:o}=e;f({insertPosition:t,target:n,dragGroup:r,flow:i,preventInsertDefault:o}),t&&function(e,t,n){if(!n)return;let r=aj[e];if(!r)throw Error(`Telemetry event: ${e} does not exist`);n.post("visual-editing/telemetry-log",{event:r,data:null})}("Visual Editing Drag Sequence Completed",0,a)}else{if("overlay/dragUpdateCursorPosition"===e.type)return void u(e.x,e.y);if("overlay/setCursor"===e.type){let{element:t,cursor:n}=e;n?t.style.cursor=n:t.style.removeProperty("cursor")}}s(e)},o[0]=a,o[1]=s,o[2]=f,o[3]=u,o[4]=t):t=o[4];let p=function(e,t,n){let i,o;let a=(0,w.c)(6),s=(0,x.useRef)(),l=eT()!==Z;return a[0]!==e||a[1]!==t||a[2]!==n||a[3]!==l?(i=()=>{if(e)return s.current=function({handler:e,overlayElement:t,inFrame:n,optimisticActorReady:i}){let o=!1,a=new Map,s=new WeakMap,l=new Set,u=new WeakMap,c=new WeakMap,d,f,p,h=!1,y=[],m=()=>y[y.length-1];function g(e,t){e.removeEventListener("click",t.click,{capture:!0}),e.removeEventListener("contextmenu",t.contextmenu,{capture:!0}),e.removeEventListener("mousemove",t.mousemove,{capture:!0}),e.removeEventListener("mousedown",t.mousedown,{capture:!0}),e.removeEventListener("mouseenter",t.mouseenter),e.removeEventListener("mouseleave",t.mouseleave)}function v({id:t,elements:n,handlers:r}){let{element:i,measureElement:o}=n;i.addEventListener("click",r.click,{capture:!0}),i.addEventListener("contextmenu",r.contextmenu,{capture:!0}),i.addEventListener("mousemove",r.mousemove,{once:!0,capture:!0}),i.addEventListener("mousedown",r.mousedown,{capture:!0}),d.observe(o),e({type:"element/activate",id:t})}function b({id:t,elements:n,handlers:r}){let{element:i,measureElement:o}=n;g(i,r),d.unobserve(o),y=y.filter(e=>e!==i),e({type:"element/deactivate",id:t})}function w(t){if(n&&i)for(let n of y){if(t===n){let n=s.get(t)?.sanity;if(!n||!tg(n))return;if(tx(t,n,l,s)){let n=t.style.cursor;n&&c.set(t,n),e({type:"overlay/setCursor",element:t,cursor:"move"});continue}}x(n)}}function x(t){let n=c.get(t);e({type:"overlay/setCursor",element:t,cursor:n})}function E(c){for(let d of function e(t){let n=[];function r(e,t){let r=function(e){if("object"==typeof e&&null!==e)return e_(e);try{return e_(JSON.parse(e))}catch{return function(e){let t=e.split(";").reduce((e,t)=>{let[n,r]=t.split("=");if(!n||t.includes("=")&&!r)return e;switch(n){case"id":e.id=r;break;case"type":e.type=r;break;case"path":e.path=ea.toString(function(e){let t=[];for(let n of e.split(".")){let e=ey.exec(n);if(e){t.push(e[1],Number(e[2]));continue}let r=em.exec(n);if(r){t.push(r[1],[Number(r[2]),Number(r[3])]);continue}let i=eg.exec(n);i?t.push(i[1],{_key:i[2]}):t.push(n)}return t}(r));break;case"base":e.baseUrl=decodeURIComponent(r);break;case"tool":e.tool=r;break;case"workspace":e.workspace=r;break;case"projectId":e.projectId=r;break;case"dataset":e.dataset=r;break;case"isDraft":e.isDraft=""}return e},{});if(!ex._run({typed:!1,value:t},{abortEarly:!0}).issues)return t}(e)}}(t);if(!r)return;let i=function e(t){let{display:n}=window.getComputedStyle(t);if("inline"!==n)return t;let r=t.parentElement;return r?e(r):null}(e);i&&n.push({elements:{element:e,measureElement:i},sanity:r})}if(t)for(let i of t.childNodes){let{nodeType:t,parentElement:o,textContent:a}=i;if(tp(i)&&void 0!==i.dataset?.sanityEditTarget){let t=e(i).map(({sanity:e})=>e);if(!t.map(e=>tg(e)).every((e,t,n)=>e===n[0]))continue;let r=function(e){if(!e.length||!e.map(e=>tg(e)).every((e,t,n)=>e===n[0]))return;if(!tg(e[0]))return e[0];let t=e.filter(tg),n=e[0],r=["projectId","dataset","id","baseUrl","workspace","tool"];for(let e=1;e<t.length;e++){let i=t[e];if(r.some(e=>i[e]!==n?.[e])){n=void 0;break}n.path=function(e,t){let n=e.split("."),r=t.split("."),i=Math.min(n.length,r.length);return n=n.slice(0,i).reverse(),r=r.slice(0,i).reverse(),n.reduce((e,t,n)=>t===r[n]?[...e,t]:[],[]).reverse().join(".")}(n.path,i.path)}return n}(t);r&&n.push({elements:{element:i,measureElement:i},sanity:r})}else if(t===Node.TEXT_NODE&&o&&a){let e=tf(a);if(!e)continue;r(o,e)}else if(tp(i)){if("SCRIPT"===i.tagName||"SANITY-VISUAL-EDITING"===i.tagName)continue;if(i.dataset?.sanity)r(i,i.dataset.sanity);else if(i.dataset?.sanityEditInfo)r(i,i.dataset.sanityEditInfo);else{if(th(i)){let e=tf(i.alt,!0);if(!e)continue;r(i,e);continue}if(ty(i)){let e=tf(i.dateTime,!0);if(!e)continue;r(i,e)}else if(tm(i)){if(!i.ariaLabel)continue;let e=tf(i.ariaLabel,!0);if(!e)continue;r(i,e)}}n.push(...e(i))}}return n}(c)){let{element:c}=d.elements;s.has(c)?function({elements:t,sanity:n}){let{element:r}=t,i=s.get(r);i&&(s.set(r,{...i,sanity:n}),e({type:"element/update",id:i.id,rect:e2(r),sanity:n}))}(d):function({elements:c,sanity:d}){let{element:p,measureElement:g}=c,b={click(t){let r=t.target;if(p===m()&&p.contains(r)){n&&(t.preventDefault(),t.stopPropagation());let r=s.get(p)?.sanity;r&&!h&&e({type:"element/click",id:E,sanity:r})}},contextmenu(t){if(!("path"in d&&n&&i&&d.path.split(".").pop()?.includes("[_key==")))return;let r=t.target;p===m()&&p.contains(r)&&(n&&(t.preventDefault(),t.stopPropagation()),e({type:"element/contextmenu",id:E,position:{x:t.clientX,y:t.clientY},sanity:d}))},mousedown(t){if(t.preventDefault(),t.currentTarget!==y.at(-1)||p.getAttribute("data-sanity-drag-disable")||!n||!i)return;let r=s.get(p)?.sanity;if(!r||!tg(r)||!tv(r.path))return;let o=tx(p,d,l,s);o&&function(e){var t;let{mouseEvent:n,element:r,overlayGroup:i,handler:o,target:a,onSequenceStart:s,onSequenceEnd:l}=e;if(0!==n.button)return;window.focus();let u=i.map(e=>e2(e.elements.element)),c=r.getAttribute("data-sanity-drag-flow")||((t=u).some(e=>t.filter(t=>!e5(e,t)).some(t=>e.y===t.y))?"horizontal":"vertical"),d=r.getAttribute("data-sanity-drag-group"),f=!!r.getAttribute("data-sanity-drag-minimap-disable"),p=!!r.getAttribute("data-sanity-drag-prevent-default"),h=r.getAttribute("data-unstable_sanity-drag-document-height"),y=r.getAttribute("data-unstable_sanity-drag-group-height"),m=null,g=tt(n),v=document.body,{minYScaled:b,scaleFactor:w}=function(e,t){let n=t||e9(e).height,r=(n+=200)>window.innerHeight?window.innerHeight/n:1,{min:i}=e9(e.map(e=>e6(e,r,{x:window.innerWidth/2,y:0})));return{scaleFactor:r,minYScaled:i-100*r}}(u,y?~~y:null),x=!1,E=!1,_=!0;to||(tu={body:{overflow:window.getComputedStyle(document.body).overflow,height:window.getComputedStyle(document.body).height},documentElement:{overflow:window.getComputedStyle(document.documentElement).overflow,height:window.getComputedStyle(document.documentElement).height}},tl=h?~~h:document.documentElement.scrollHeight);let k=setInterval(()=>{u=i.map(e=>e2(e.elements.element))},150),j=()=>{w>=1||(o({type:"overlay/dragUpdateSkeleton",skeleton:tn(ts,r,w)}),o({type:"overlay/dragToggleMinimapPrompt",display:!1}),o({type:"overlay/dragToggleMinimap",display:!0}),to=!0,(async function(e,t,n,r,i){return new Promise(i=>{e.addEventListener("transitionend",()=>{setTimeout(()=>{r({type:"overlay/dragEndMinimapTransition"})},300),i()},{once:!0}),r({type:"overlay/dragStartMinimapTransition"}),r({type:"overlay/dragToggleMinimap",display:!0}),document.body.style.overflow="hidden",document.body.style.height="100%",document.documentElement.style.overflow="initial",document.documentElement.style.height="100%",setTimeout(()=>{e.style.transformOrigin="50% 0px",e.style.transition="transform 150ms ease",e.style.transform=`translate3d(0px, ${-n+scrollY}px, 0px) scale(${t})`},25)})})(v,w,b,o,0).then(()=>{setTimeout(()=>{o({type:"overlay/dragUpdateGroupRect",groupRect:tr(u)})},300)}))},S=e=>{Math.abs(e.deltaY)>=10&&w<1&&!to&&!E&&!f&&_&&(o({type:"overlay/dragToggleMinimapPrompt",display:!0}),E=!0),!e.shiftKey||to||f||(window.dispatchEvent(new CustomEvent("unstable_sanity/dragApplyMinimap")),setTimeout(()=>{j()},50))},I=e=>{if(e.preventDefault(),ta=function(e){let t=document.body,n=window.getComputedStyle(t).transform;if("none"===n)return{x:e.x,y:e.y};let r=new DOMMatrix(n).inverse(),i=new DOMPoint(e.x,e.y).matrixTransform(r);return{x:i.x,y:i.y}}(ts=tt(e)),4>Math.abs(e4(ts,g)))return;if(!x){let e=tr(u),t=tn(ts,r,1);o({type:"overlay/dragStart",flow:c}),o({type:"overlay/dragUpdateSkeleton",skeleton:t}),o({type:"overlay/dragUpdateGroupRect",groupRect:e}),x=!0,s()}o({type:"overlay/dragUpdateCursorPosition",x:ts.x,y:ts.y}),!e.shiftKey||to||f||(window.dispatchEvent(new CustomEvent("unstable_sanity/dragApplyMinimap")),setTimeout(()=>{j()},50));let t=function(e,t,n){if("horizontal"===n){let r={x1:e.x,y1:e.y,x2:e.x-1e8,y2:e.y},i={x1:e.x,y1:e.y,x2:e.x+1e8,y2:e.y};return{left:e8(r,t,n),right:e8(i,t,n)}}{let r={x1:e.x,y1:e.y,x2:e.x,y2:e.y-1e8},i={x1:e.x,y1:e.y,x2:e.x,y2:e.y+1e8};return{top:e8(r,t,n),bottom:e8(i,t,n)}}}(ts,u,c);JSON.stringify(m)!==JSON.stringify(t)&&o({type:"overlay/dragUpdateInsertPosition",insertPosition:te(i,m=t,c)})},O=()=>{_=!1,o({type:"overlay/dragEnd",target:a,insertPosition:m?te(i,m,c):null,dragGroup:d,flow:c,preventInsertDefault:p}),E&&o({type:"overlay/dragToggleMinimapPrompt",display:!1}),to||(clearInterval(k),l(),R(),$()),M()},A=e=>{"Shift"===e.key&&to&&(to=!1,o({type:"overlay/dragUpdateSkeleton",skeleton:tn(ts,r,1/w)}),window.dispatchEvent(new CustomEvent("unstable_sanity/dragResetMinimap")),setTimeout(()=>{ti(ta.y,v,tl,o,150,tu)},50),o({type:"overlay/dragUpdateGroupRect",groupRect:null}),_||(clearInterval(k),M(),R(),$(),l()))},P=()=>{o({type:"overlay/dragUpdateGroupRect",groupRect:null}),window.dispatchEvent(new CustomEvent("unstable_sanity/dragResetMinimap")),setTimeout(()=>{ti(ta.y,v,tl,o,150,tu).then(()=>{to=!1})},50),clearInterval(k),M(),R(),$(),l()},M=()=>{window.removeEventListener("mousemove",I),window.removeEventListener("wheel",S),window.removeEventListener("mouseup",O)},$=()=>{window.removeEventListener("keyup",A)},R=()=>{window.removeEventListener("blur",P)};window.addEventListener("blur",P),window.addEventListener("keyup",A),window.addEventListener("wheel",S),window.addEventListener("mousemove",I),window.addEventListener("mouseup",O)}({element:p,handler:e,mouseEvent:t,overlayGroup:o,target:r,onSequenceStart:()=>{h=!0},onSequenceEnd:()=>{setTimeout(()=>{h=!1},250)}})},mousemove(e){b.mouseenter(e);let t=e.currentTarget;t&&(t.addEventListener("mouseenter",b.mouseenter),t.addEventListener("mouseleave",b.mouseleave))},mouseenter(){document.querySelector("vercel-live-feedback")&&p.closest("[data-vercel-edit-info]")||p.closest("[data-vercel-edit-target]")||(y.push(p),e({type:"element/mouseenter",id:E,rect:e2(p)}),w(p))},mouseleave(n){function r(){y.pop();let t=m();if(e({type:"element/mouseleave",id:E}),t){w(t);let n=s.get(t);n&&e({type:"element/mouseenter",id:n.id,rect:e2(t)})}x(p)}let{relatedTarget:i}=n,o=td(i),a=t.contains(o);if(tc(o)&&a)return function e(t){let n=i=>{let{relatedTarget:o}=i;td(o)?o&&tc(o)&&(t.removeEventListener("mouseleave",n),e(o)):(t.removeEventListener("mouseleave",n),r())};t.addEventListener("mouseleave",n)}(o);r()}},E=function(e,t,n){if(e1.randomUUID&&!e)return e1.randomUUID();let i=(e=e||{}).random||(e.rng||function(){if(!r){if(typeof crypto>"u"||!crypto.getRandomValues)throw Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");r=crypto.getRandomValues.bind(crypto)}return r(e0)})();return i[6]=15&i[6]|64,i[8]=63&i[8]|128,function(e,t=0){return(eQ[e[t+0]]+eQ[e[t+1]]+eQ[e[t+2]]+eQ[e[t+3]]+"-"+eQ[e[t+4]]+eQ[e[t+5]]+"-"+eQ[e[t+6]]+eQ[e[t+7]]+"-"+eQ[e[t+8]]+eQ[e[t+9]]+"-"+eQ[e[t+10]]+eQ[e[t+11]]+eQ[e[t+12]]+eQ[e[t+13]]+eQ[e[t+14]]+eQ[e[t+15]]).toLowerCase()}(i)}(),_={id:E,elements:c,sanity:d,handlers:b};l.add(p),u.set(g,p),a.set(E,p),s.set(p,_),f?.observe(p),e({type:"element/register",id:E,element:p,rect:e2(p),sanity:d,dragDisabled:!!p.getAttribute("data-sanity-drag-disable")}),o&&v(_)}(d)}}function _(t){let n=s.get(t);if(n){let{id:r,handlers:i}=n;g(t,i),d.unobserve(t),s.delete(t),l.delete(t),a.delete(r),e({type:"element/unregister",id:r})}}function k(t){let n=s.get(t);n&&e({type:"element/updateRect",id:n.id,rect:e2(t)})}function j(e){if(o)for(let t of e){let{target:e}=t,n=tc(e)&&s.get(e);n&&(t.isIntersecting?v(n):b(n))}}function S(t){let n=td(t.target);n?"capture"===n.dataset.sanityOverlayElement&&(t.preventDefault(),t.stopPropagation()):(y=[],e({type:"overlay/blur"}))}function I(){for(let e of l)k(e)}function O(t){"Escape"===t.key&&(y=[],e({type:"overlay/blur"}))}function A(e){let{target:t}=e;if(t!==window.document&&tc(t))for(let e of l)t.contains(e)&&k(e)}function P(){o||(f=new IntersectionObserver(j,{threshold:.3}),l.forEach(e=>f.observe(e)),e({type:"overlay/activate"}),o=!0)}function M(){o&&(f?.disconnect(),l.forEach(e=>{let t=s.get(e);t&&b(t)}),e({type:"overlay/deactivate"}),o=!1)}return window.document.fonts.ready.then(()=>{for(let e of l)k(e)}),window.addEventListener("click",S),window.addEventListener("contextmenu",S),window.addEventListener("keydown",O),window.addEventListener("resize",I),window.addEventListener("scroll",A,{capture:!0,passive:!0}),d=new ResizeObserver(function(e){for(let t of e){let e=t.target;if(tc(e)){let t=u.get(e);if(!t)return;k(t)}}}),(p=new MutationObserver(function(e){let n=!1;for(let r of e){let{target:e,type:i}=r,o="characterData"===i?e.parentElement:e;o===t||t.contains(o)||(n=!0,tc(o)&&E({childNodes:[o]}))}if(n)for(let e of l)e.isConnected||_(e)})).observe(document.body,{attributes:!0,characterData:!0,childList:!0,subtree:!0}),E(document.body),P(),{activate:P,deactivate:M,destroy:function(){window.removeEventListener("click",S),window.removeEventListener("contextmenu",S),window.removeEventListener("keydown",O),window.removeEventListener("resize",I),window.removeEventListener("scroll",A),p.disconnect(),d.disconnect(),l.forEach(e=>{_(e)}),a.clear(),l.clear(),y=[],M()}}}({handler:t,overlayElement:e,inFrame:n,optimisticActorReady:l}),()=>{s.current?.destroy(),s.current=void 0}},o=[e,t,n,l],a[0]=e,a[1]=t,a[2]=n,a[3]=l,a[4]=i,a[5]=o):(i=a[4],o=a[5]),(0,x.useEffect)(i,o),s}(d,t,l);return o[5]!==p.current||o[6]!==c?(n=()=>{c?p.current?.activate():p.current?.deactivate()},o[5]=p.current,o[6]=c,o[7]=n):n=o[7],o[8]!==p||o[9]!==c?(i=[p,c],o[8]=p,o[9]=c,o[10]=i):i=o[10],(0,x.useEffect)(n,i),null},aC=e=>{let t,n,r,i,o,a,s,l,u,c,d,f,p,h,y,m,g,v,E,_,k,j,S,I;let O=(0,w.c)(70),{comlink:$,componentResolver:R,inFrame:C,zIndex:T}=e,[L,D]=(0,x.useState)(),N=(0,M.u)();O[0]===Symbol.for("react.memo_cache_sentinel")?(t={contextMenu:null,dragInsertPosition:null,dragShowMinimap:!1,dragShowMinimapPrompt:!1,dragSkeleton:null,elements:[],focusPath:"",isDragging:!1,perspective:"published",wasMaybeCollapsed:!1,dragMinimapTransition:!1,dragGroupRect:null},O[0]=t):t=O[0];let[F,z]=(0,x.useReducer)(ay,t),{contextMenu:U,dragInsertPosition:W,dragShowMinimap:V,dragShowMinimapPrompt:q,dragSkeleton:B,elements:G,isDragging:H,perspective:K,wasMaybeCollapsed:Y,dragMinimapTransition:X,dragGroupRect:J}=F,[Q,ee]=(0,x.useState)(null),[et,en]=(0,x.useState)(!0);O[1]!==$?(n=()=>{let e=[$?.on("presentation/focus",e=>{z({type:"presentation/focus",data:e})}),$?.on("presentation/blur",e=>{z({type:"presentation/blur",data:e})}),$?.on("presentation/toggle-overlay",()=>{en(aL)}),$?.onStatus(e=>{D(e)})].filter(Boolean);return()=>e.forEach(aD)},r=[$],O[1]=$,O[2]=n,O[3]=r):(n=O[2],r=O[3]),(0,x.useEffect)(n,r),function(e,t){let n,r;let i=(0,w.c)(4);i[0]!==e||i[1]!==t?(n=()=>{let n=new AbortController;e?.fetch("visual-editing/fetch-perspective",void 0,{signal:n.signal,suppressWarnings:!0}).then(e=>{t({type:"presentation/perspective",data:e})}).catch(aS);let r=e?.on("presentation/perspective",e=>{t({type:"presentation/perspective",data:e})});return()=>{r?.(),n.abort()}},r=[e,t],i[0]=e,i[1]=t,i[2]=n,i[3]=r):(n=i[2],r=i[3]),(0,x.useEffect)(n,r)}($,z),function(e,t,n){let r,i,o;let a=(0,w.c)(7),s=(0,x.useRef)(void 0);a[0]!==e?(r=(t,n)=>{e?.post("visual-editing/documents",{documents:t,perspective:n})},a[0]=e,a[1]=r):r=a[1];let l=r;a[2]!==t||a[3]!==n||a[4]!==l?(i=()=>{let e=t.map(aA).filter(aO),r=new Set(e.map(aI));if(!s.current||!function(e,t){if(e===t)return!0;if(e.size!==t.size)return!1;for(let n of e)if(!t.has(n))return!1;return!0}(r,s.current.nodeIds)||n!==s.current.perspective){let t=Array.from(r).map(t=>{let{type:n,projectId:r,dataset:i}=e.find(e=>e.id===t);return r&&i?{_id:t,_type:n,_projectId:r,_dataset:i}:{_id:t,_type:n}});s.current={nodeIds:r,perspective:n},l(t,n)}},o=[t,n,l],a[2]=t,a[3]=n,a[4]=l,a[5]=i,a[6]=o):(i=a[5],o=a[6]),(0,x.useEffect)(i,o)}($,G,K),O[4]!==Q?(i=(e,t)=>{Q&&(Q.style.setProperty("--drag-preview-x",`${e}px`),Q.style.setProperty("--drag-preview-y",t-window.scrollY+"px"))},O[4]=Q,O[5]=i):i=O[5];let er=i;O[6]===Symbol.for("react.memo_cache_sentinel")?(o=()=>{let e=e=>{P(e)&&en(aF)},t=e=>{P(e)&&en(az),["mod","\\"].every(t=>A[t]?e[A[t]]:e.key===t.toUpperCase())&&en(aU)};return window.addEventListener("click",aN),window.addEventListener("keydown",t),window.addEventListener("keyup",e),()=>{window.removeEventListener("click",aN),window.removeEventListener("keydown",t),window.removeEventListener("keyup",e)}},a=[en],O[6]=o,O[7]=a):(o=O[6],a=O[7]),(0,x.useEffect)(o,a);let[ei,eo]=(0,x.useState)(!1),[ea,es]=(0,x.useState)(!1),el=(0,x.useRef)(void 0);O[8]!==et?(s=()=>{if(et)return aM(()=>{eo(!0),aM(()=>{es(!0),el.current=setTimeout(()=>{es(!1),eo(!1)},1500)})});el.current&&(clearTimeout(el.current),eo(!1),es(!1))},l=[et],O[8]=et,O[9]=s,O[10]=l):(s=O[9],l=O[10]),(0,x.useEffect)(s,l),O[11]!==G?(c=G.flatMap(aW),O[11]=G,O[12]=c):c=O[12],u=c,O[13]===Symbol.for("react.memo_cache_sentinel")?(d=()=>{z({type:"overlay/blur"})},O[13]=d):d=O[13];let eu=d,ec=eT()!==Z;f=ec?R:void 0;e:{let e;if(C&&"connected"!==L||H){let e;O[14]===Symbol.for("react.memo_cache_sentinel")?(e=[],O[14]=e):e=O[14],p=e;break e}O[15]!==f||O[16]!==X||O[17]!==V||O[18]!==G||O[19]!==C||O[20]!==H||O[21]!==ec||O[22]!==Y?(e=G.filter(aV).map(e=>{let{id:t,element:n,focused:r,hovered:i,rect:o,sanity:a,dragDisabled:s}=e,l=!s&&!!n.getAttribute("data-sanity")&&ec&&G.some(e=>"id"in e.sanity&&"id"in a&&tw(e.sanity,a)&&e.sanity.path!==a.path);return(0,b.jsx)(at,{componentResolver:f,element:n,enableScrollIntoView:!H&&!X&&!V,focused:r,hovered:i,node:a,rect:o,showActions:!C,draggable:l,isDragging:H||X,wasMaybeCollapsed:r&&Y},t)}),O[15]=f,O[16]=X,O[17]=V,O[18]=G,O[19]=C,O[20]=H,O[21]=ec,O[22]=Y,O[23]=e):e=O[23],p=e}let ed=p,ef=N?"dark":"light",ep=ea?"":void 0,eh=ei?"":void 0;return O[24]!==u||O[25]!==K?(h=(0,b.jsx)(a$,{documentIds:u,perspective:K}),O[24]=u,O[25]=K,O[26]=h):h=O[26],O[27]!==$||O[28]!==C||O[29]!==et||O[30]!==Q||O[31]!==er?(y=(0,b.jsx)(aR,{comlink:$,dispatch:z,inFrame:C,onDrag:er,overlayEnabled:et,rootElement:Q}),O[27]=$,O[28]=C,O[29]=et,O[30]=Q,O[31]=er,O[32]=y):y=O[32],O[33]!==U?(m=U&&(0,b.jsx)(o3,{...U,onDismiss:eu}),O[33]=U,O[34]=m):m=O[34],O[35]!==J||O[36]!==W||O[37]!==X||O[38]!==q||O[39]!==H?(g=H&&!X&&(0,b.jsxs)(b.Fragment,{children:[W&&(0,b.jsx)(as,{dragInsertPosition:W}),q&&(0,b.jsx)(ap,{}),J&&(0,b.jsx)(aa,{dragGroupRect:J})]}),O[35]=J,O[36]=W,O[37]=X,O[38]=q,O[39]=H,O[40]=g):g=O[40],O[41]!==B||O[42]!==H?(v=H&&B&&(0,b.jsx)(au,{skeleton:B}),O[41]=B,O[42]=H,O[43]=v):v=O[43],O[44]!==ed||O[45]!==ep||O[46]!==eh||O[47]!==h||O[48]!==y||O[49]!==m||O[50]!==g||O[51]!==v||O[52]!==T?(E=(0,b.jsxs)(aP,{"data-fading-out":ep,"data-overlays":eh,ref:ee,$zIndex:T,children:[h,y,m,ed,g,v]}),O[44]=ed,O[45]=ep,O[46]=eh,O[47]=h,O[48]=y,O[49]=m,O[50]=g,O[51]=v,O[52]=T,O[53]=E):E=O[53],O[54]!==$||O[55]!==E?(_=(0,b.jsx)(aE,{comlink:$,children:E}),O[54]=$,O[55]=E,O[56]=_):_=O[56],O[57]!==$||O[58]!==_?(k=(0,b.jsx)(am,{comlink:$,children:_}),O[57]=$,O[58]=_,O[59]=k):k=O[59],O[60]!==$||O[61]!==G||O[62]!==k?(j=(0,b.jsx)(ab,{comlink:$,elements:G,children:k}),O[60]=$,O[61]=G,O[62]=k,O[63]=j):j=O[63],O[64]!==Q||O[65]!==j?(S=(0,b.jsx)(M.L,{children:(0,b.jsx)(M.t,{element:Q,children:j})}),O[64]=Q,O[65]=j,O[66]=S):S=O[66],O[67]!==ef||O[68]!==S?(I=(0,b.jsx)(M.v,{scheme:ef,theme:M.w,tone:"transparent",children:S}),O[67]=ef,O[68]=S,O[69]=I):I=O[69],I};function aT(e,t){return e.filter(e=>e!==t)}function aL(e){return!e}function aD(e){return e()}function aN(e){let t=e.target;if(((0,M.x)(t)||(0,M.y)(t)&&t.closest("a"))&&e.altKey){e.preventDefault(),e.stopPropagation();let t=new MouseEvent(e.type,{...e,altKey:!1,bubbles:!0,cancelable:!0});e.target?.dispatchEvent(t)}}function aF(e){return!e}function az(e){return!e}function aU(e){return!e}function aW(e){return"id"in e.sanity?[e.sanity.id]:[]}function aV(e){return e.activated||e.focused}let aq=e=>{let t,n;let r=(0,w.c)(4),{comlink:i,refresh:o}=e,a=(0,x.useRef)(0),s=(0,x.useRef)(0);return r[0]!==i||r[1]!==o?(t=()=>i.on("presentation/refresh",e=>{if("manual"===e.source){let t;clearTimeout(a.current);let n=o(e);!1!==n&&(i.post("visual-editing/refreshing",e),t=!1,a.current=window.setTimeout(()=>{i.post("visual-editing/refreshed",e),t=!0},3e3),n?.finally?.(()=>{t||(clearTimeout(a.current),i.post("visual-editing/refreshed",e))}))}else if("mutation"===e.source){clearTimeout(s.current);let t=o(e);if(!1===t)return;i.post("visual-editing/refreshing",e),s.current=window.setTimeout(()=>{let t=o(e);!1!==t&&(i.post("visual-editing/refreshing",e),t?.finally?.(()=>{i.post("visual-editing/refreshed",e)})||i.post("visual-editing/refreshed",e))},1e3),t?.finally?.(()=>{i.post("visual-editing/refreshed",e)})||i.post("visual-editing/refreshed",e)}}),n=[i,o],r[0]=i,r[1]=o,r[2]=t,r[3]=n):(t=r[2],n=r[3]),(0,x.useEffect)(t,n),null};function aB(){console.warn("[@sanity/visual-editing] Package version mismatch detected: Please update your Sanity studio to prevent potential compatibility issues.")}function aG(){}let aH=e=>{let t,n,r,i,o,a,s,l;let u=(0,w.c)(20),{components:c,history:d,portal:f,refresh:p,zIndex:h}=e,y=void 0===f||f,[m,g]=(0,x.useState)(null);u[0]===Symbol.for("react.memo_cache_sentinel")?(t=()=>g(window.self!==window.top||!!window.opener),n=[],u[0]=t,u[1]=n):(t=u[0],n=u[1]),(0,x.useEffect)(t,n);let[v,k]=(0,x.useState)(null);u[2]!==y?(r=()=>{if(!1===y)return;let e=document.createElement("sanity-visual-editing");return document.documentElement.appendChild(e),k(e),()=>{k(null),document.documentElement.contains(e)&&document.documentElement.removeChild(e)}},i=[y],u[2]=y,u[3]=r,u[4]=i):(r=u[3],i=u[4]),(0,x.useEffect)(r,i);let j=function(e){let t,n;let r=(0,w.c)(3),i=void 0===e||e,[o,a]=(0,x.useState)();return r[0]!==i?(t=()=>{let e;if(!i)return;let t=(0,_.dS)({name:"visual-editing",connectTo:"presentation"},(0,_.zB)().provide({actors:O()}));e=0;let n=t.start(),r=t.onStatus(()=>{e=window.setTimeout(()=>{a(t)},3e3)},"connected");return()=>{clearTimeout(e),r(),n(),a(void 0)}},n=[i],r[0]=i,r[1]=t,r[2]=n):(t=r[1],n=r[2]),(0,x.useEffect)(t,n),o}(!0===m);(function(e){let t,n;let r=(0,w.c)(3);r[0]!==e?(t=()=>{if(!e)return;let t=function(e){let t=new tz(1),n=new tD;return e.fetch("visual-editing/snapshot-welcome",void 0,{suppressWarnings:!0}).then(e=>{t.next(e.event)}).catch(()=>{}),e.on("presentation/snapshot-event",e=>{"reconnect"===e.event.type&&t.next(e.event),"mutation"===e.event.type&&n.next(e.event)}),tH(t,n)}(e),n=oq(e),r=(0,tP.A)(n,{input:{client:{withConfig:aG},sharedListener:t}});r.start();let i=new AbortController,o=e.onStatus(()=>{e.fetch("visual-editing/features",void 0,{signal:i.signal,suppressWarnings:!0}).then(e=>{e.features.optimistic&&function(e){for(let t of(K=e,Y))t()}(r)}).catch(aB)},"connected");return()=>{r.stop(),i.abort(),o()}},n=[e],r[0]=e,r[1]=t,r[2]=n):(t=r[1],n=r[2]),(0,x.useEffect)(t,n)})(j),u[5]!==j||u[6]!==c||u[7]!==m||u[8]!==h?(o=null!==m&&(0,b.jsx)(aC,{comlink:j,componentResolver:c,inFrame:m,zIndex:h}),u[5]=j,u[6]=c,u[7]=m,u[8]=h,u[9]=o):o=u[9],u[10]!==j||u[11]!==d||u[12]!==p?(a=j&&(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(oB,{comlink:j,history:d}),(0,b.jsx)(oG,{comlink:j}),p&&(0,b.jsx)(aq,{comlink:j,refresh:p})]}),u[10]=j,u[11]=d,u[12]=p,u[13]=a):a=u[13],u[14]!==o||u[15]!==a?(s=(0,b.jsxs)(b.Fragment,{children:[o,a]}),u[14]=o,u[15]=a,u[16]=s):s=u[16];let S=s;return!1!==y&&v?(u[17]!==S||u[18]!==v?(l=(0,E.createPortal)(S,v),u[17]=S,u[18]=v,u[19]=l):l=u[19],l):S};aH.displayName="VisualEditing";var aZ=n(99376);n(83079);var aK=(0,n(12119).$)("23977280e679cbd5490718534d869d8b006b3dfa");function aY(e){let t=e.indexOf("#"),n=e.indexOf("?"),r=n>-1&&(t<0||n<t);return r||t>-1?{pathname:e.substring(0,r?n:t),query:r?e.substring(n,t>-1?t:void 0):"",hash:t>-1?e.slice(t):""}:{pathname:e,query:"",hash:""}}function aX(e,t){if(!function(e,t){if("string"!=typeof e)return!1;let{pathname:n}=aY(e);return n===t||n.startsWith(`${t}/`)}(e,t))return e;let n=e.slice(t.length);return n.startsWith("/")?n:`/${n}`}let aJ=(e,t)=>{let{pathname:n,query:r,hash:i}=aY(e);return t?n.endsWith("/")?`${n}${r}${i}`:`${n}/${r}${i}`:`${n.replace(/\/$/,"")||"/"}${r}${i}`};function aQ(e){let{basePath:t="",components:n,refresh:r,trailingSlash:i=!1,zIndex:o}=e,a=(0,aZ.useRouter)(),s=(0,x.useRef)(a),[l,u]=(0,x.useState)();(0,x.useEffect)(()=>{s.current=a},[a]);let c=(0,x.useMemo)(()=>({subscribe:e=>(u(()=>e),()=>u(void 0)),update:e=>{switch(e.type){case"push":return s.current.push(aX(e.url,t));case"pop":return s.current.back();case"replace":return s.current.replace(aX(e.url,t));default:throw Error(`Unknown update type: ${e.type}`)}}}),[t]),d=(0,aZ.usePathname)(),f=(0,aZ.useSearchParams)();(0,x.useEffect)(()=>{l&&l({type:"push",url:aJ(function(e,t){if(!e.startsWith("/")||!t)return e;if("/"===e&&t)return t;let{pathname:n,query:r,hash:i}=aY(e);return`${t}${n}${r}${i}`}(`${d}${f?.size?`?${f}`:""}`,t),i)})},[t,l,d,f,i]);let p=(0,x.useCallback)(e=>{if(r)return r(e);switch(e.source){case"manual":return e.livePreviewEnabled?(console.debug("Live preview is setup, calling router.refresh() to refresh the server components without refetching cached data"),s.current.refresh(),Promise.resolve()):(console.debug("No loaders in live mode detected, or preview kit setup, revalidating root layout"),aK());case"mutation":return e.livePreviewEnabled?(console.debug("Live preview is setup, mutation is skipped assuming its handled by the live preview"),!1):(console.debug("No loaders in live mode detected, or preview kit setup, revalidating root layout"),aK());default:throw Error("Unknown refresh source",{cause:e})}},[r]);return(0,b.jsx)(aH,{components:n,history:c,portal:!0,refresh:p,zIndex:o})}}}]);