exports.id=9467,exports.ids=[9467],exports.modules={9416:(e,a,i)=>{e.exports={parallel:i(45381),serial:i(28962),serialOrdered:i(45646)}},79767:e=>{e.exports=function(e){Object.keys(e.jobs).forEach(a.bind(e)),e.jobs={}};function a(e){"function"==typeof this.jobs[e]&&this.jobs[e]()}},57679:(e,a,i)=>{var t=i(29449);e.exports=function(e){var a=!1;return t(function(){a=!0}),function(i,n){a?e(i,n):t(function(){e(i,n)})}}},29449:e=>{e.exports=function(e){var a="function"==typeof setImmediate?setImmediate:"object"==typeof process&&"function"==typeof process.nextTick?process.nextTick:null;a?a(e):setTimeout(e,0)}},47033:(e,a,i)=>{var t=i(57679),n=i(79767);e.exports=function(e,a,i,s){var o,r,c=i.keyedList?i.keyedList[i.index]:i.index;i.jobs[c]=(o=e[c],r=function(e,a){c in i.jobs&&(delete i.jobs[c],e?n(i):i.results[c]=a,s(e,i.results))},2==a.length?a(o,t(r)):a(o,c,t(r)))}},86507:e=>{e.exports=function(e,a){var i=!Array.isArray(e),t={index:0,keyedList:i||a?Object.keys(e):null,jobs:{},results:i?{}:[],size:i?Object.keys(e).length:e.length};return a&&t.keyedList.sort(i?a:function(i,t){return a(e[i],e[t])}),t}},3907:(e,a,i)=>{var t=i(79767),n=i(57679);e.exports=function(e){Object.keys(this.jobs).length&&(this.index=this.size,t(this),n(e)(null,this.results))}},45381:(e,a,i)=>{var t=i(47033),n=i(86507),s=i(3907);e.exports=function(e,a,i){for(var o=n(e);o.index<(o.keyedList||e).length;)t(e,a,o,function(e,a){if(e){i(e,a);return}if(0===Object.keys(o.jobs).length){i(null,o.results);return}}),o.index++;return s.bind(o,i)}},28962:(e,a,i)=>{var t=i(45646);e.exports=function(e,a,i){return t(e,a,null,i)}},45646:(e,a,i)=>{var t=i(47033),n=i(86507),s=i(3907);function o(e,a){return e<a?-1:e>a?1:0}e.exports=function(e,a,i,o){var r=n(e,i);return t(e,a,r,function i(n,s){if(n){o(n,s);return}if(r.index++,r.index<(r.keyedList||e).length){t(e,a,r,i);return}o(null,r.results)}),s.bind(r,o)},e.exports.ascending=o,e.exports.descending=function(e,a){return -1*o(e,a)}},49567:(e,a,i)=>{var t=i(21764),n=i(76162).Stream,s=i(49334);function o(){this.writable=!1,this.readable=!0,this.dataSize=0,this.maxDataSize=2097152,this.pauseStreams=!0,this._released=!1,this._streams=[],this._currentStream=null,this._insideLoop=!1,this._pendingNext=!1}e.exports=o,t.inherits(o,n),o.create=function(e){var a=new this;for(var i in e=e||{})a[i]=e[i];return a},o.isStreamLike=function(e){return"function"!=typeof e&&"string"!=typeof e&&"boolean"!=typeof e&&"number"!=typeof e&&!Buffer.isBuffer(e)},o.prototype.append=function(e){if(o.isStreamLike(e)){if(!(e instanceof s)){var a=s.create(e,{maxDataSize:1/0,pauseStream:this.pauseStreams});e.on("data",this._checkDataSize.bind(this)),e=a}this._handleErrors(e),this.pauseStreams&&e.pause()}return this._streams.push(e),this},o.prototype.pipe=function(e,a){return n.prototype.pipe.call(this,e,a),this.resume(),e},o.prototype._getNext=function(){if(this._currentStream=null,this._insideLoop){this._pendingNext=!0;return}this._insideLoop=!0;try{do this._pendingNext=!1,this._realGetNext();while(this._pendingNext)}finally{this._insideLoop=!1}},o.prototype._realGetNext=function(){var e=this._streams.shift();if(void 0===e){this.end();return}if("function"!=typeof e){this._pipeNext(e);return}e((function(e){o.isStreamLike(e)&&(e.on("data",this._checkDataSize.bind(this)),this._handleErrors(e)),this._pipeNext(e)}).bind(this))},o.prototype._pipeNext=function(e){if(this._currentStream=e,o.isStreamLike(e)){e.on("end",this._getNext.bind(this)),e.pipe(this,{end:!1});return}this.write(e),this._getNext()},o.prototype._handleErrors=function(e){var a=this;e.on("error",function(e){a._emitError(e)})},o.prototype.write=function(e){this.emit("data",e)},o.prototype.pause=function(){this.pauseStreams&&(this.pauseStreams&&this._currentStream&&"function"==typeof this._currentStream.pause&&this._currentStream.pause(),this.emit("pause"))},o.prototype.resume=function(){this._released||(this._released=!0,this.writable=!0,this._getNext()),this.pauseStreams&&this._currentStream&&"function"==typeof this._currentStream.resume&&this._currentStream.resume(),this.emit("resume")},o.prototype.end=function(){this._reset(),this.emit("end")},o.prototype.destroy=function(){this._reset(),this.emit("close")},o.prototype._reset=function(){this.writable=!1,this._streams=[],this._currentStream=null},o.prototype._checkDataSize=function(){if(this._updateDataSize(),!(this.dataSize<=this.maxDataSize)){var e="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this._emitError(Error(e))}},o.prototype._updateDataSize=function(){this.dataSize=0;var e=this;this._streams.forEach(function(a){a.dataSize&&(e.dataSize+=a.dataSize)}),this._currentStream&&this._currentStream.dataSize&&(this.dataSize+=this._currentStream.dataSize)},o.prototype._emitError=function(e){this._reset(),this.emit("error",e)}},49334:(e,a,i)=>{var t=i(76162).Stream,n=i(21764);function s(){this.source=null,this.dataSize=0,this.maxDataSize=1048576,this.pauseStream=!0,this._maxDataSizeExceeded=!1,this._released=!1,this._bufferedEvents=[]}e.exports=s,n.inherits(s,t),s.create=function(e,a){var i=new this;for(var t in a=a||{})i[t]=a[t];i.source=e;var n=e.emit;return e.emit=function(){return i._handleEmit(arguments),n.apply(e,arguments)},e.on("error",function(){}),i.pauseStream&&e.pause(),i},Object.defineProperty(s.prototype,"readable",{configurable:!0,enumerable:!0,get:function(){return this.source.readable}}),s.prototype.setEncoding=function(){return this.source.setEncoding.apply(this.source,arguments)},s.prototype.resume=function(){this._released||this.release(),this.source.resume()},s.prototype.pause=function(){this.source.pause()},s.prototype.release=function(){this._released=!0,this._bufferedEvents.forEach((function(e){this.emit.apply(this,e)}).bind(this)),this._bufferedEvents=[]},s.prototype.pipe=function(){var e=t.prototype.pipe.apply(this,arguments);return this.resume(),e},s.prototype._handleEmit=function(e){if(this._released){this.emit.apply(this,e);return}"data"===e[0]&&(this.dataSize+=e[1].length,this._checkIfMaxDataSizeExceeded()),this._bufferedEvents.push(e)},s.prototype._checkIfMaxDataSizeExceeded=function(){if(!this._maxDataSizeExceeded&&!(this.dataSize<=this.maxDataSize)){this._maxDataSizeExceeded=!0;var e="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this.emit("error",Error(e))}}},31719:(e,a,i)=>{var t;e.exports=function(){if(!t){try{t=i(28781)("follow-redirects")}catch(e){}"function"!=typeof t&&(t=function(){})}t.apply(null,arguments)}},10133:(e,a,i)=>{var t=i(17360),n=t.URL,s=i(32615),o=i(35240),r=i(76162).Writable,c=i(27790),p=i(31719);!function(){var e="undefined"!=typeof process,a="undefined"!=typeof window&&"undefined"!=typeof document,i=A(Error.captureStackTrace);e||!a&&i||console.warn("The follow-redirects package should be excluded from browser builds.")}();var l=!1;try{c(new n(""))}catch(e){l="ERR_INVALID_URL"===e.code}var u=["auth","host","hostname","href","path","pathname","port","protocol","query","search","hash"],d=["abort","aborted","connect","error","socket","timeout"],m=Object.create(null);d.forEach(function(e){m[e]=function(a,i,t){this._redirectable.emit(e,a,i,t)}});var f=T("ERR_INVALID_URL","Invalid URL",TypeError),h=T("ERR_FR_REDIRECTION_FAILURE","Redirected request failed"),x=T("ERR_FR_TOO_MANY_REDIRECTS","Maximum number of redirects exceeded",h),v=T("ERR_FR_MAX_BODY_LENGTH_EXCEEDED","Request body larger than maxBodyLength limit"),b=T("ERR_STREAM_WRITE_AFTER_END","write after end"),g=r.prototype.destroy||w;function y(e,a){r.call(this),this._sanitizeOptions(e),this._options=e,this._ended=!1,this._ending=!1,this._redirectCount=0,this._redirects=[],this._requestBodyLength=0,this._requestBodyBuffers=[],a&&this.on("response",a);var i=this;this._onNativeResponse=function(e){try{i._processResponse(e)}catch(e){i.emit("error",e instanceof h?e:new h({cause:e}))}},this._performRequest()}function _(e){var a={maxRedirects:21,maxBodyLength:10485760},i={};return Object.keys(e).forEach(function(t){var s=t+":",o=i[s]=e[t],r=a[t]=Object.create(o);Object.defineProperties(r,{request:{value:function(e,t,o){var r;return(r=e,n&&r instanceof n)?e=E(e):R(e)?e=E(k(e)):(o=t,t=j(e),e={protocol:s}),A(t)&&(o=t,t=null),(t=Object.assign({maxRedirects:a.maxRedirects,maxBodyLength:a.maxBodyLength},e,t)).nativeProtocols=i,R(t.host)||R(t.hostname)||(t.hostname="::1"),c.equal(t.protocol,s,"protocol mismatch"),p("options",t),new y(t,o)},configurable:!0,enumerable:!0,writable:!0},get:{value:function(e,a,i){var t=r.request(e,a,i);return t.end(),t},configurable:!0,enumerable:!0,writable:!0}})}),a}function w(){}function k(e){var a;if(l)a=new n(e);else if(!R((a=j(t.parse(e))).protocol))throw new f({input:e});return a}function j(e){if(/^\[/.test(e.hostname)&&!/^\[[:0-9a-f]+\]$/i.test(e.hostname)||/^\[/.test(e.host)&&!/^\[[:0-9a-f]+\](:\d+)?$/i.test(e.host))throw new f({input:e.href||e});return e}function E(e,a){var i=a||{};for(var t of u)i[t]=e[t];return i.hostname.startsWith("[")&&(i.hostname=i.hostname.slice(1,-1)),""!==i.port&&(i.port=Number(i.port)),i.path=i.search?i.pathname+i.search:i.pathname,i}function S(e,a){var i;for(var t in a)e.test(t)&&(i=a[t],delete a[t]);return null==i?void 0:String(i).trim()}function T(e,a,i){function t(i){A(Error.captureStackTrace)&&Error.captureStackTrace(this,this.constructor),Object.assign(this,i||{}),this.code=e,this.message=this.cause?a+": "+this.cause.message:a}return t.prototype=new(i||Error),Object.defineProperties(t.prototype,{constructor:{value:t,enumerable:!1},name:{value:"Error ["+e+"]",enumerable:!1}}),t}function O(e,a){for(var i of d)e.removeListener(i,m[i]);e.on("error",w),e.destroy(a)}function R(e){return"string"==typeof e||e instanceof String}function A(e){return"function"==typeof e}y.prototype=Object.create(r.prototype),y.prototype.abort=function(){O(this._currentRequest),this._currentRequest.abort(),this.emit("abort")},y.prototype.destroy=function(e){return O(this._currentRequest,e),g.call(this,e),this},y.prototype.write=function(e,a,i){if(this._ending)throw new b;if(!R(e)&&!("object"==typeof e&&"length"in e))throw TypeError("data should be a string, Buffer or Uint8Array");if(A(a)&&(i=a,a=null),0===e.length){i&&i();return}this._requestBodyLength+e.length<=this._options.maxBodyLength?(this._requestBodyLength+=e.length,this._requestBodyBuffers.push({data:e,encoding:a}),this._currentRequest.write(e,a,i)):(this.emit("error",new v),this.abort())},y.prototype.end=function(e,a,i){if(A(e)?(i=e,e=a=null):A(a)&&(i=a,a=null),e){var t=this,n=this._currentRequest;this.write(e,a,function(){t._ended=!0,n.end(null,null,i)}),this._ending=!0}else this._ended=this._ending=!0,this._currentRequest.end(null,null,i)},y.prototype.setHeader=function(e,a){this._options.headers[e]=a,this._currentRequest.setHeader(e,a)},y.prototype.removeHeader=function(e){delete this._options.headers[e],this._currentRequest.removeHeader(e)},y.prototype.setTimeout=function(e,a){var i=this;function t(a){a.setTimeout(e),a.removeListener("timeout",a.destroy),a.addListener("timeout",a.destroy)}function n(a){i._timeout&&clearTimeout(i._timeout),i._timeout=setTimeout(function(){i.emit("timeout"),s()},e),t(a)}function s(){i._timeout&&(clearTimeout(i._timeout),i._timeout=null),i.removeListener("abort",s),i.removeListener("error",s),i.removeListener("response",s),i.removeListener("close",s),a&&i.removeListener("timeout",a),i.socket||i._currentRequest.removeListener("socket",n)}return a&&this.on("timeout",a),this.socket?n(this.socket):this._currentRequest.once("socket",n),this.on("socket",t),this.on("abort",s),this.on("error",s),this.on("response",s),this.on("close",s),this},["flushHeaders","getHeader","setNoDelay","setSocketKeepAlive"].forEach(function(e){y.prototype[e]=function(a,i){return this._currentRequest[e](a,i)}}),["aborted","connection","socket"].forEach(function(e){Object.defineProperty(y.prototype,e,{get:function(){return this._currentRequest[e]}})}),y.prototype._sanitizeOptions=function(e){if(e.headers||(e.headers={}),e.host&&(e.hostname||(e.hostname=e.host),delete e.host),!e.pathname&&e.path){var a=e.path.indexOf("?");a<0?e.pathname=e.path:(e.pathname=e.path.substring(0,a),e.search=e.path.substring(a))}},y.prototype._performRequest=function(){var e=this._options.protocol,a=this._options.nativeProtocols[e];if(!a)throw TypeError("Unsupported protocol "+e);if(this._options.agents){var i=e.slice(0,-1);this._options.agent=this._options.agents[i]}var n=this._currentRequest=a.request(this._options,this._onNativeResponse);for(var s of(n._redirectable=this,d))n.on(s,m[s]);if(this._currentUrl=/^\//.test(this._options.path)?t.format(this._options):this._options.path,this._isRedirect){var o=0,r=this,c=this._requestBodyBuffers;!function e(a){if(n===r._currentRequest){if(a)r.emit("error",a);else if(o<c.length){var i=c[o++];n.finished||n.write(i.data,i.encoding,e)}else r._ended&&n.end()}}()}},y.prototype._processResponse=function(e){var a,i,s,o=e.statusCode;this._options.trackRedirects&&this._redirects.push({url:this._currentUrl,headers:e.headers,statusCode:o});var r=e.headers.location;if(!r||!1===this._options.followRedirects||o<300||o>=400){e.responseUrl=this._currentUrl,e.redirects=this._redirects,this.emit("response",e),this._requestBodyBuffers=[];return}if(O(this._currentRequest),e.destroy(),++this._redirectCount>this._options.maxRedirects)throw new x;var u=this._options.beforeRedirect;u&&(s=Object.assign({Host:e.req.getHeader("host")},this._options.headers));var d=this._options.method;(301!==o&&302!==o||"POST"!==this._options.method)&&(303!==o||/^(?:GET|HEAD)$/.test(this._options.method))||(this._options.method="GET",this._requestBodyBuffers=[],S(/^content-/i,this._options.headers));var m=S(/^host$/i,this._options.headers),f=k(this._currentUrl),h=m||f.host,v=/^\w+:/.test(r)?this._currentUrl:t.format(Object.assign(f,{host:h})),b=l?new n(r,v):k(t.resolve(v,r));if(p("redirecting to",b.href),this._isRedirect=!0,E(b,this._options),(b.protocol===f.protocol||"https:"===b.protocol)&&(b.host===h||(c(R(a=b.host)&&R(h)),(i=a.length-h.length-1)>0&&"."===a[i]&&a.endsWith(h)))||S(/^(?:(?:proxy-)?authorization|cookie)$/i,this._options.headers),A(u)){var g={headers:e.headers,statusCode:o},y={url:v,method:d,headers:s};u(this._options,g,y),this._sanitizeOptions(this._options)}this._performRequest()},e.exports=_({http:s,https:o}),e.exports.wrap=_},7118:(e,a,i)=>{var t=i(49567),n=i(21764),s=i(55315),o=i(32615),r=i(35240),c=i(17360).parse,p=i(92048),l=i(76162).Stream,u=i(71362),d=i(9416),m=i(57747);function f(e){if(!(this instanceof f))return new f(e);for(var a in this._overheadLength=0,this._valueLength=0,this._valuesToMeasure=[],t.call(this),e=e||{})this[a]=e[a]}e.exports=f,n.inherits(f,t),f.LINE_BREAK="\r\n",f.DEFAULT_CONTENT_TYPE="application/octet-stream",f.prototype.append=function(e,a,i){"string"==typeof(i=i||{})&&(i={filename:i});var s=t.prototype.append.bind(this);if("number"==typeof a&&(a=""+a),n.isArray(a)){this._error(Error("Arrays are not supported."));return}var o=this._multiPartHeader(e,a,i),r=this._multiPartFooter();s(o),s(a),s(r),this._trackLength(o,a,i)},f.prototype._trackLength=function(e,a,i){var t=0;null!=i.knownLength?t+=+i.knownLength:Buffer.isBuffer(a)?t=a.length:"string"==typeof a&&(t=Buffer.byteLength(a)),this._valueLength+=t,this._overheadLength+=Buffer.byteLength(e)+f.LINE_BREAK.length,a&&(a.path||a.readable&&a.hasOwnProperty("httpVersion")||a instanceof l)&&(i.knownLength||this._valuesToMeasure.push(a))},f.prototype._lengthRetriever=function(e,a){e.hasOwnProperty("fd")?void 0!=e.end&&e.end!=1/0&&void 0!=e.start?a(null,e.end+1-(e.start?e.start:0)):p.stat(e.path,function(i,t){if(i){a(i);return}a(null,t.size-(e.start?e.start:0))}):e.hasOwnProperty("httpVersion")?a(null,+e.headers["content-length"]):e.hasOwnProperty("httpModule")?(e.on("response",function(i){e.pause(),a(null,+i.headers["content-length"])}),e.resume()):a("Unknown stream")},f.prototype._multiPartHeader=function(e,a,i){if("string"==typeof i.header)return i.header;var t,n=this._getContentDisposition(a,i),s=this._getContentType(a,i),o="",r={"Content-Disposition":["form-data",'name="'+e+'"'].concat(n||[]),"Content-Type":[].concat(s||[])};for(var c in"object"==typeof i.header&&m(r,i.header),r)if(r.hasOwnProperty(c)){if(null==(t=r[c]))continue;Array.isArray(t)||(t=[t]),t.length&&(o+=c+": "+t.join("; ")+f.LINE_BREAK)}return"--"+this.getBoundary()+f.LINE_BREAK+o+f.LINE_BREAK},f.prototype._getContentDisposition=function(e,a){var i,t;return"string"==typeof a.filepath?i=s.normalize(a.filepath).replace(/\\/g,"/"):a.filename||e.name||e.path?i=s.basename(a.filename||e.name||e.path):e.readable&&e.hasOwnProperty("httpVersion")&&(i=s.basename(e.client._httpMessage.path||"")),i&&(t='filename="'+i+'"'),t},f.prototype._getContentType=function(e,a){var i=a.contentType;return!i&&e.name&&(i=u.lookup(e.name)),!i&&e.path&&(i=u.lookup(e.path)),!i&&e.readable&&e.hasOwnProperty("httpVersion")&&(i=e.headers["content-type"]),!i&&(a.filepath||a.filename)&&(i=u.lookup(a.filepath||a.filename)),i||"object"!=typeof e||(i=f.DEFAULT_CONTENT_TYPE),i},f.prototype._multiPartFooter=function(){return(function(e){var a=f.LINE_BREAK;0===this._streams.length&&(a+=this._lastBoundary()),e(a)}).bind(this)},f.prototype._lastBoundary=function(){return"--"+this.getBoundary()+"--"+f.LINE_BREAK},f.prototype.getHeaders=function(e){var a,i={"content-type":"multipart/form-data; boundary="+this.getBoundary()};for(a in e)e.hasOwnProperty(a)&&(i[a.toLowerCase()]=e[a]);return i},f.prototype.setBoundary=function(e){this._boundary=e},f.prototype.getBoundary=function(){return this._boundary||this._generateBoundary(),this._boundary},f.prototype.getBuffer=function(){for(var e=new Buffer.alloc(0),a=this.getBoundary(),i=0,t=this._streams.length;i<t;i++)"function"!=typeof this._streams[i]&&(Buffer.isBuffer(this._streams[i])?e=Buffer.concat([e,this._streams[i]]):e=Buffer.concat([e,Buffer.from(this._streams[i])]),("string"!=typeof this._streams[i]||this._streams[i].substring(2,a.length+2)!==a)&&(e=Buffer.concat([e,Buffer.from(f.LINE_BREAK)])));return Buffer.concat([e,Buffer.from(this._lastBoundary())])},f.prototype._generateBoundary=function(){for(var e="--------------------------",a=0;a<24;a++)e+=Math.floor(10*Math.random()).toString(16);this._boundary=e},f.prototype.getLengthSync=function(){var e=this._overheadLength+this._valueLength;return this._streams.length&&(e+=this._lastBoundary().length),this.hasKnownLength()||this._error(Error("Cannot calculate proper length in synchronous way.")),e},f.prototype.hasKnownLength=function(){var e=!0;return this._valuesToMeasure.length&&(e=!1),e},f.prototype.getLength=function(e){var a=this._overheadLength+this._valueLength;if(this._streams.length&&(a+=this._lastBoundary().length),!this._valuesToMeasure.length){process.nextTick(e.bind(this,null,a));return}d.parallel(this._valuesToMeasure,this._lengthRetriever,function(i,t){if(i){e(i);return}t.forEach(function(e){a+=e}),e(null,a)})},f.prototype.submit=function(e,a){var i,t,n={method:"post"};return"string"==typeof e?t=m({port:(e=c(e)).port,path:e.pathname,host:e.hostname,protocol:e.protocol},n):(t=m(e,n)).port||(t.port="https:"==t.protocol?443:80),t.headers=this.getHeaders(e.headers),i="https:"==t.protocol?r.request(t):o.request(t),this.getLength((function(e,t){if(e&&"Unknown stream"!==e){this._error(e);return}if(t&&i.setHeader("Content-Length",t),this.pipe(i),a){var n,s=function(e,t){return i.removeListener("error",s),i.removeListener("response",n),a.call(this,e,t)};n=s.bind(this,null),i.on("error",s),i.on("response",n)}}).bind(this)),i},f.prototype._error=function(e){this.error||(this.error=e,this.pause(),this.emit("error",e))},f.prototype.toString=function(){return"[object FormData]"}},57747:e=>{e.exports=function(e,a){return Object.keys(a).forEach(function(i){e[i]=e[i]||a[i]}),e}},58406:(e,a,i)=>{"use strict";i.d(a,{Z:()=>t});let t=(0,i(26323).Z)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},70457:(e,a,i)=>{"use strict";i.d(a,{Z:()=>t});let t=(0,i(26323).Z)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},80412:(e,a,i)=>{e.exports=i(40572)},71362:(e,a,i)=>{"use strict";var t=i(80412),n=i(55315).extname,s=/^\s*([^;\s]*)(?:;|\s|$)/,o=/^text\//i;function r(e){if(!e||"string"!=typeof e)return!1;var a=s.exec(e),i=a&&t[a[1].toLowerCase()];return i&&i.charset?i.charset:!!(a&&o.test(a[1]))&&"UTF-8"}a.charset=r,a.charsets={lookup:r},a.contentType=function(e){if(!e||"string"!=typeof e)return!1;var i=-1===e.indexOf("/")?a.lookup(e):e;if(!i)return!1;if(-1===i.indexOf("charset")){var t=a.charset(i);t&&(i+="; charset="+t.toLowerCase())}return i},a.extension=function(e){if(!e||"string"!=typeof e)return!1;var i=s.exec(e),t=i&&a.extensions[i[1].toLowerCase()];return!!t&&!!t.length&&t[0]},a.extensions=Object.create(null),a.lookup=function(e){if(!e||"string"!=typeof e)return!1;var i=n("x."+e).toLowerCase().substr(1);return!!i&&(a.types[i]||!1)},a.types=Object.create(null),function(e,a){var i=["nginx","apache",void 0,"iana"];Object.keys(t).forEach(function(n){var s=t[n],o=s.extensions;if(o&&o.length){e[n]=o;for(var r=0;r<o.length;r++){var c=o[r];if(a[c]){var p=i.indexOf(t[a[c]].source),l=i.indexOf(s.source);if("application/octet-stream"!==a[c]&&(p>l||p===l&&"application/"===a[c].substr(0,12)))continue}a[c]=n}}})}(a.extensions,a.types)},80818:(e,a,i)=>{"use strict";let t,n,s;var o=Object.create,r=Object.defineProperty,c=Object.defineProperties,p=Object.getOwnPropertyDescriptor,l=Object.getOwnPropertyDescriptors,u=Object.getOwnPropertyNames,d=Object.getOwnPropertySymbols,m=Object.getPrototypeOf,f=Object.prototype.hasOwnProperty,h=Object.prototype.propertyIsEnumerable,x=(e,a,i)=>a in e?r(e,a,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[a]=i,v=(e,a)=>{for(var i in a||(a={}))f.call(a,i)&&x(e,i,a[i]);if(d)for(var i of d(a))h.call(a,i)&&x(e,i,a[i]);return e},b=(e,a)=>c(e,l(a)),g=(e,a,i,t)=>{if(a&&"object"==typeof a||"function"==typeof a)for(let n of u(a))f.call(e,n)||n===i||r(e,n,{get:()=>a[n],enumerable:!(t=p(a,n))||t.enumerable});return e},y={};((e,a)=>{for(var i in a)r(e,i,{get:a[i],enumerable:!0})})(y,{useRouter:()=>j}),e.exports=g(r({},"__esModule",{value:!0}),y);var _=i(34178),w=i(28964),k=(s=null!=(t=i(80361))?o(m(t)):{},g(!n&&t&&t.__esModule?s:r(s,"default",{value:t,enumerable:!0}),t)),j=r(()=>{let e=(0,_.useRouter)(),a=(0,_.usePathname)();(0,w.useEffect)(()=>{k.done()},[a]);let i=(0,w.useCallback)((i,t)=>{i!==a&&k.start(),e.replace(i,t)},[e,a]),t=(0,w.useCallback)((i,t)=>{i!==a&&k.start(),e.push(i,t)},[e,a]);return b(v({},e),{replace:i,push:t})},"name",{value:"useRouter",configurable:!0})},81786:(e,a,i)=>{"use strict";var t=i(17360).parse,n={ftp:21,gopher:70,http:80,https:443,ws:80,wss:443},s=String.prototype.endsWith||function(e){return e.length<=this.length&&-1!==this.indexOf(e,this.length-e.length)};function o(e){return process.env[e.toLowerCase()]||process.env[e.toUpperCase()]||""}a.j=function(e){var a,i,r,c="string"==typeof e?t(e):e||{},p=c.protocol,l=c.host,u=c.port;if("string"!=typeof l||!l||"string"!=typeof p||(p=p.split(":",1)[0],a=l=l.replace(/:\d*$/,""),i=u=parseInt(u)||n[p]||0,!(!(r=(o("npm_config_no_proxy")||o("no_proxy")).toLowerCase())||"*"!==r&&r.split(/[,\s]/).every(function(e){if(!e)return!0;var t=e.match(/^(.+):(\d+)$/),n=t?t[1]:e,o=t?parseInt(t[2]):0;return!!o&&o!==i||(/^[.*]/.test(n)?("*"===n.charAt(0)&&(n=n.slice(1)),!s.call(a,n)):a!==n)}))))return"";var d=o("npm_config_"+p+"_proxy")||o(p+"_proxy")||o("npm_config_proxy")||o("all_proxy");return d&&-1===d.indexOf("://")&&(d=p+"://"+d),d}},34631:(e,a,i)=>{"use strict";i.d(a,{F:()=>p});var t=i(2704);let n=(e,a,i)=>{if(e&&"reportValidity"in e){let n=(0,t.U2)(i,a);e.setCustomValidity(n&&n.message||""),e.reportValidity()}},s=(e,a)=>{for(let i in a.fields){let t=a.fields[i];t&&t.ref&&"reportValidity"in t.ref?n(t.ref,i,e):t.refs&&t.refs.forEach(a=>n(a,i,e))}},o=(e,a)=>{a.shouldUseNativeValidation&&s(e,a);let i={};for(let n in e){let s=(0,t.U2)(a.fields,n),o=Object.assign(e[n]||{},{ref:s&&s.ref});if(r(a.names||Object.keys(e),n)){let e=Object.assign({},(0,t.U2)(i,n));(0,t.t8)(e,"root",o),(0,t.t8)(i,n,e)}else(0,t.t8)(i,n,o)}return i},r=(e,a)=>e.some(e=>e.startsWith(a+"."));var c=function(e,a){for(var i={};e.length;){var n=e[0],s=n.code,o=n.message,r=n.path.join(".");if(!i[r]){if("unionErrors"in n){var c=n.unionErrors[0].errors[0];i[r]={message:c.message,type:c.code}}else i[r]={message:o,type:s}}if("unionErrors"in n&&n.unionErrors.forEach(function(a){return a.errors.forEach(function(a){return e.push(a)})}),a){var p=i[r].types,l=p&&p[n.code];i[r]=(0,t.KN)(r,a,i,s,l?[].concat(l,n.message):n.message)}e.shift()}return i},p=function(e,a,i){return void 0===i&&(i={}),function(t,n,r){try{return Promise.resolve(function(n,o){try{var c=Promise.resolve(e["sync"===i.mode?"parse":"parseAsync"](t,a)).then(function(e){return r.shouldUseNativeValidation&&s({},r),{errors:{},values:i.raw?t:e}})}catch(e){return o(e)}return c&&c.then?c.then(void 0,o):c}(0,function(e){if(Array.isArray(null==e?void 0:e.errors))return{values:{},errors:o(c(e.errors,!r.shouldUseNativeValidation&&"all"===r.criteriaMode),r)};throw e}))}catch(e){return Promise.reject(e)}}}},40768:(e,a,i)=>{"use strict";i.d(a,{f:()=>r});var t=i(28964),n=i(20002),s=i(97247),o=t.forwardRef((e,a)=>(0,s.jsx)(n.WV.label,{...e,ref:a,onMouseDown:a=>{a.target.closest("button, input, select, textarea")||(e.onMouseDown?.(a),!a.defaultPrevented&&a.detail>1&&a.preventDefault())}}));o.displayName="Label";var r=o},88111:(e,a,i)=>{"use strict";i.d(a,{D:()=>u});var t=i(28964),n=i(48079),s=i(59489),o=i(62945),r=i(51370),c=class extends o.l{#e;#a=void 0;#i;#t;constructor(e,a){super(),this.#e=e,this.setOptions(a),this.bindMethods(),this.#n()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let a=this.options;this.options=this.#e.defaultMutationOptions(e),(0,r.VS)(this.options,a)||this.#e.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#i,observer:this}),a?.mutationKey&&this.options.mutationKey&&(0,r.Ym)(a.mutationKey)!==(0,r.Ym)(this.options.mutationKey)?this.reset():this.#i?.state.status==="pending"&&this.#i.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#i?.removeObserver(this)}onMutationUpdate(e){this.#n(),this.#s(e)}getCurrentResult(){return this.#a}reset(){this.#i?.removeObserver(this),this.#i=void 0,this.#n(),this.#s()}mutate(e,a){return this.#t=a,this.#i?.removeObserver(this),this.#i=this.#e.getMutationCache().build(this.#e,this.options),this.#i.addObserver(this),this.#i.execute(e)}#n(){let e=this.#i?.state??(0,n.R)();this.#a={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#s(e){s.V.batch(()=>{if(this.#t&&this.hasListeners()){let a=this.#a.variables,i=this.#a.context;e?.type==="success"?(this.#t.onSuccess?.(e.data,a,i),this.#t.onSettled?.(e.data,null,a,i)):e?.type==="error"&&(this.#t.onError?.(e.error,a,i),this.#t.onSettled?.(void 0,e.error,a,i))}this.listeners.forEach(e=>{e(this.#a)})})}},p=i(41755),l=i(75191);function u(e,a){let i=(0,p.NL)(a),[n]=t.useState(()=>new c(i,e));t.useEffect(()=>{n.setOptions(e)},[n,e]);let o=t.useSyncExternalStore(t.useCallback(e=>n.subscribe(s.V.batchCalls(e)),[n]),()=>n.getCurrentResult(),()=>n.getCurrentResult()),r=t.useCallback((e,a)=>{n.mutate(e,a).catch(l.Z)},[n]);if(o.error&&(0,l.L)(n.options.throwOnError,[o.error]))throw o.error;return{...o,mutate:r,mutateAsync:o.mutate}}},75191:(e,a,i)=>{"use strict";function t(e,a){return"function"==typeof e?e(...a):!!e}function n(){}i.d(a,{L:()=>t,Z:()=>n})},10863:(e,a,i)=>{"use strict";let t,n,s,o,r;i.d(a,{Z:()=>aH});var c,p,l,u={};function d(e,a){return function(){return e.apply(a,arguments)}}i.r(u),i.d(u,{hasBrowserEnv:()=>ef,hasStandardBrowserEnv:()=>ex,hasStandardBrowserWebWorkerEnv:()=>ev,navigator:()=>eh,origin:()=>eb});let{toString:m}=Object.prototype,{getPrototypeOf:f}=Object,h=(t=Object.create(null),e=>{let a=m.call(e);return t[a]||(t[a]=a.slice(8,-1).toLowerCase())}),x=e=>(e=e.toLowerCase(),a=>h(a)===e),v=e=>a=>typeof a===e,{isArray:b}=Array,g=v("undefined"),y=x("ArrayBuffer"),_=v("string"),w=v("function"),k=v("number"),j=e=>null!==e&&"object"==typeof e,E=e=>{if("object"!==h(e))return!1;let a=f(e);return(null===a||a===Object.prototype||null===Object.getPrototypeOf(a))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)},S=x("Date"),T=x("File"),O=x("Blob"),R=x("FileList"),A=x("URLSearchParams"),[C,N,z,L]=["ReadableStream","Request","Response","Headers"].map(x);function F(e,a,{allOwnKeys:i=!1}={}){let t,n;if(null!=e){if("object"!=typeof e&&(e=[e]),b(e))for(t=0,n=e.length;t<n;t++)a.call(null,e[t],t,e);else{let n;let s=i?Object.getOwnPropertyNames(e):Object.keys(e),o=s.length;for(t=0;t<o;t++)n=s[t],a.call(null,e[n],n,e)}}}function D(e,a){let i;a=a.toLowerCase();let t=Object.keys(e),n=t.length;for(;n-- >0;)if(a===(i=t[n]).toLowerCase())return i;return null}let P="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,U=e=>!g(e)&&e!==P,B=(n="undefined"!=typeof Uint8Array&&f(Uint8Array),e=>n&&e instanceof n),q=x("HTMLFormElement"),Z=(({hasOwnProperty:e})=>(a,i)=>e.call(a,i))(Object.prototype),I=x("RegExp"),V=(e,a)=>{let i=Object.getOwnPropertyDescriptors(e),t={};F(i,(i,n)=>{let s;!1!==(s=a(i,n,e))&&(t[n]=s||i)}),Object.defineProperties(e,t)},M="abcdefghijklmnopqrstuvwxyz",$="0123456789",H={DIGIT:$,ALPHA:M,ALPHA_DIGIT:M+M.toUpperCase()+$},K=x("AsyncFunction"),W=(c="function"==typeof setImmediate,p=w(P.postMessage),c?setImmediate:p?(o=`axios@${Math.random()}`,r=[],P.addEventListener("message",({source:e,data:a})=>{e===P&&a===o&&r.length&&r.shift()()},!1),e=>{r.push(e),P.postMessage(o,"*")}):e=>setTimeout(e)),J="undefined"!=typeof queueMicrotask?queueMicrotask.bind(P):"undefined"!=typeof process&&process.nextTick||W,G={isArray:b,isArrayBuffer:y,isBuffer:function(e){return null!==e&&!g(e)&&null!==e.constructor&&!g(e.constructor)&&w(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let a;return e&&("function"==typeof FormData&&e instanceof FormData||w(e.append)&&("formdata"===(a=h(e))||"object"===a&&w(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){return"undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&y(e.buffer)},isString:_,isNumber:k,isBoolean:e=>!0===e||!1===e,isObject:j,isPlainObject:E,isReadableStream:C,isRequest:N,isResponse:z,isHeaders:L,isUndefined:g,isDate:S,isFile:T,isBlob:O,isRegExp:I,isFunction:w,isStream:e=>j(e)&&w(e.pipe),isURLSearchParams:A,isTypedArray:B,isFileList:R,forEach:F,merge:function e(){let{caseless:a}=U(this)&&this||{},i={},t=(t,n)=>{let s=a&&D(i,n)||n;E(i[s])&&E(t)?i[s]=e(i[s],t):E(t)?i[s]=e({},t):b(t)?i[s]=t.slice():i[s]=t};for(let e=0,a=arguments.length;e<a;e++)arguments[e]&&F(arguments[e],t);return i},extend:(e,a,i,{allOwnKeys:t}={})=>(F(a,(a,t)=>{i&&w(a)?e[t]=d(a,i):e[t]=a},{allOwnKeys:t}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,a,i,t)=>{e.prototype=Object.create(a.prototype,t),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:a.prototype}),i&&Object.assign(e.prototype,i)},toFlatObject:(e,a,i,t)=>{let n,s,o;let r={};if(a=a||{},null==e)return a;do{for(s=(n=Object.getOwnPropertyNames(e)).length;s-- >0;)o=n[s],(!t||t(o,e,a))&&!r[o]&&(a[o]=e[o],r[o]=!0);e=!1!==i&&f(e)}while(e&&(!i||i(e,a))&&e!==Object.prototype);return a},kindOf:h,kindOfTest:x,endsWith:(e,a,i)=>{e=String(e),(void 0===i||i>e.length)&&(i=e.length),i-=a.length;let t=e.indexOf(a,i);return -1!==t&&t===i},toArray:e=>{if(!e)return null;if(b(e))return e;let a=e.length;if(!k(a))return null;let i=Array(a);for(;a-- >0;)i[a]=e[a];return i},forEachEntry:(e,a)=>{let i;let t=(e&&e[Symbol.iterator]).call(e);for(;(i=t.next())&&!i.done;){let t=i.value;a.call(e,t[0],t[1])}},matchAll:(e,a)=>{let i;let t=[];for(;null!==(i=e.exec(a));)t.push(i);return t},isHTMLForm:q,hasOwnProperty:Z,hasOwnProp:Z,reduceDescriptors:V,freezeMethods:e=>{V(e,(a,i)=>{if(w(e)&&-1!==["arguments","caller","callee"].indexOf(i))return!1;if(w(e[i])){if(a.enumerable=!1,"writable"in a){a.writable=!1;return}a.set||(a.set=()=>{throw Error("Can not rewrite read-only method '"+i+"'")})}})},toObjectSet:(e,a)=>{let i={};return(e=>{e.forEach(e=>{i[e]=!0})})(b(e)?e:String(e).split(a)),i},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(e,a,i){return a.toUpperCase()+i}),noop:()=>{},toFiniteNumber:(e,a)=>null!=e&&Number.isFinite(e=+e)?e:a,findKey:D,global:P,isContextDefined:U,ALPHABET:H,generateString:(e=16,a=H.ALPHA_DIGIT)=>{let i="",{length:t}=a;for(;e--;)i+=a[Math.random()*t|0];return i},isSpecCompliantForm:function(e){return!!(e&&w(e.append)&&"FormData"===e[Symbol.toStringTag]&&e[Symbol.iterator])},toJSONObject:e=>{let a=Array(10),i=(e,t)=>{if(j(e)){if(a.indexOf(e)>=0)return;if(!("toJSON"in e)){a[t]=e;let n=b(e)?[]:{};return F(e,(e,a)=>{let s=i(e,t+1);g(s)||(n[a]=s)}),a[t]=void 0,n}}return e};return i(e,0)},isAsyncFn:K,isThenable:e=>e&&(j(e)||w(e))&&w(e.then)&&w(e.catch),setImmediate:W,asap:J};function Y(e,a,i,t,n){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=Error().stack,this.message=e,this.name="AxiosError",a&&(this.code=a),i&&(this.config=i),t&&(this.request=t),n&&(this.response=n,this.status=n.status?n.status:null)}G.inherits(Y,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:G.toJSONObject(this.config),code:this.code,status:this.status}}});let X=Y.prototype,Q={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Q[e]={value:e}}),Object.defineProperties(Y,Q),Object.defineProperty(X,"isAxiosError",{value:!0}),Y.from=(e,a,i,t,n,s)=>{let o=Object.create(X);return G.toFlatObject(e,o,function(e){return e!==Error.prototype},e=>"isAxiosError"!==e),Y.call(o,e.message,a,i,t,n),o.cause=e,o.name=e.name,s&&Object.assign(o,s),o};var ee=i(7118);function ea(e){return G.isPlainObject(e)||G.isArray(e)}function ei(e){return G.endsWith(e,"[]")?e.slice(0,-2):e}function et(e,a,i){return e?e.concat(a).map(function(e,a){return e=ei(e),!i&&a?"["+e+"]":e}).join(i?".":""):a}let en=G.toFlatObject(G,{},null,function(e){return/^is[A-Z]/.test(e)}),es=function(e,a,i){if(!G.isObject(e))throw TypeError("target must be an object");a=a||new(ee||FormData);let t=(i=G.toFlatObject(i,{metaTokens:!0,dots:!1,indexes:!1},!1,function(e,a){return!G.isUndefined(a[e])})).metaTokens,n=i.visitor||p,s=i.dots,o=i.indexes,r=(i.Blob||"undefined"!=typeof Blob&&Blob)&&G.isSpecCompliantForm(a);if(!G.isFunction(n))throw TypeError("visitor must be a function");function c(e){if(null===e)return"";if(G.isDate(e))return e.toISOString();if(!r&&G.isBlob(e))throw new Y("Blob is not supported. Use a Buffer instead.");return G.isArrayBuffer(e)||G.isTypedArray(e)?r&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function p(e,i,n){let r=e;if(e&&!n&&"object"==typeof e){if(G.endsWith(i,"{}"))i=t?i:i.slice(0,-2),e=JSON.stringify(e);else{var p;if(G.isArray(e)&&(p=e,G.isArray(p)&&!p.some(ea))||(G.isFileList(e)||G.endsWith(i,"[]"))&&(r=G.toArray(e)))return i=ei(i),r.forEach(function(e,t){G.isUndefined(e)||null===e||a.append(!0===o?et([i],t,s):null===o?i:i+"[]",c(e))}),!1}}return!!ea(e)||(a.append(et(n,i,s),c(e)),!1)}let l=[],u=Object.assign(en,{defaultVisitor:p,convertValue:c,isVisitable:ea});if(!G.isObject(e))throw TypeError("data must be an object");return function e(i,t){if(!G.isUndefined(i)){if(-1!==l.indexOf(i))throw Error("Circular reference detected in "+t.join("."));l.push(i),G.forEach(i,function(i,s){!0===(!(G.isUndefined(i)||null===i)&&n.call(a,i,G.isString(s)?s.trim():s,t,u))&&e(i,t?t.concat(s):[s])}),l.pop()}}(e),a};function eo(e){let a={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(e){return a[e]})}function er(e,a){this._pairs=[],e&&es(e,this,a)}let ec=er.prototype;function ep(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function el(e,a,i){let t;if(!a)return e;let n=i&&i.encode||ep,s=i&&i.serialize;if(t=s?s(a,i):G.isURLSearchParams(a)?a.toString():new er(a,i).toString(n)){let a=e.indexOf("#");-1!==a&&(e=e.slice(0,a)),e+=(-1===e.indexOf("?")?"?":"&")+t}return e}ec.append=function(e,a){this._pairs.push([e,a])},ec.toString=function(e){let a=e?function(a){return e.call(this,a,eo)}:eo;return this._pairs.map(function(e){return a(e[0])+"="+a(e[1])},"").join("&")};class eu{constructor(){this.handlers=[]}use(e,a,i){return this.handlers.push({fulfilled:e,rejected:a,synchronous:!!i&&i.synchronous,runWhen:i?i.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){G.forEach(this.handlers,function(a){null!==a&&e(a)})}}let ed={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},em={isNode:!0,classes:{URLSearchParams:i(17360).URLSearchParams,FormData:ee,Blob:"undefined"!=typeof Blob&&Blob||null},protocols:["http","https","file","data"]},ef="undefined"!=typeof window&&"undefined"!=typeof document,eh="object"==typeof navigator&&navigator||void 0,ex=ef&&(!eh||0>["ReactNative","NativeScript","NS"].indexOf(eh.product)),ev="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,eb=ef&&window.location.href||"http://localhost",eg={...u,...em},ey=function(e){if(G.isFormData(e)&&G.isFunction(e.entries)){let a={};return G.forEachEntry(e,(e,i)=>{!function e(a,i,t,n){let s=a[n++];if("__proto__"===s)return!0;let o=Number.isFinite(+s),r=n>=a.length;return(s=!s&&G.isArray(t)?t.length:s,r)?G.hasOwnProp(t,s)?t[s]=[t[s],i]:t[s]=i:(t[s]&&G.isObject(t[s])||(t[s]=[]),e(a,i,t[s],n)&&G.isArray(t[s])&&(t[s]=function(e){let a,i;let t={},n=Object.keys(e),s=n.length;for(a=0;a<s;a++)t[i=n[a]]=e[i];return t}(t[s]))),!o}(G.matchAll(/\w+|\[(\w*)]/g,e).map(e=>"[]"===e[0]?"":e[1]||e[0]),i,a,0)}),a}return null},e_={transitional:ed,adapter:["xhr","http","fetch"],transformRequest:[function(e,a){let i;let t=a.getContentType()||"",n=t.indexOf("application/json")>-1,s=G.isObject(e);if(s&&G.isHTMLForm(e)&&(e=new FormData(e)),G.isFormData(e))return n?JSON.stringify(ey(e)):e;if(G.isArrayBuffer(e)||G.isBuffer(e)||G.isStream(e)||G.isFile(e)||G.isBlob(e)||G.isReadableStream(e))return e;if(G.isArrayBufferView(e))return e.buffer;if(G.isURLSearchParams(e))return a.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();if(s){if(t.indexOf("application/x-www-form-urlencoded")>-1){var o,r;return(o=e,r=this.formSerializer,es(o,new eg.classes.URLSearchParams,Object.assign({visitor:function(e,a,i,t){return eg.isNode&&G.isBuffer(e)?(this.append(a,e.toString("base64")),!1):t.defaultVisitor.apply(this,arguments)}},r))).toString()}if((i=G.isFileList(e))||t.indexOf("multipart/form-data")>-1){let a=this.env&&this.env.FormData;return es(i?{"files[]":e}:e,a&&new a,this.formSerializer)}}return s||n?(a.setContentType("application/json",!1),function(e,a,i){if(G.isString(e))try{return(0,JSON.parse)(e),G.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(0,JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){let a=this.transitional||e_.transitional,i=a&&a.forcedJSONParsing,t="json"===this.responseType;if(G.isResponse(e)||G.isReadableStream(e))return e;if(e&&G.isString(e)&&(i&&!this.responseType||t)){let i=a&&a.silentJSONParsing;try{return JSON.parse(e)}catch(e){if(!i&&t){if("SyntaxError"===e.name)throw Y.from(e,Y.ERR_BAD_RESPONSE,this,null,this.response);throw e}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:eg.classes.FormData,Blob:eg.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};G.forEach(["delete","get","head","post","put","patch"],e=>{e_.headers[e]={}});let ew=G.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),ek=e=>{let a,i,t;let n={};return e&&e.split("\n").forEach(function(e){t=e.indexOf(":"),a=e.substring(0,t).trim().toLowerCase(),i=e.substring(t+1).trim(),!a||n[a]&&ew[a]||("set-cookie"===a?n[a]?n[a].push(i):n[a]=[i]:n[a]=n[a]?n[a]+", "+i:i)}),n},ej=Symbol("internals");function eE(e){return e&&String(e).trim().toLowerCase()}function eS(e){return!1===e||null==e?e:G.isArray(e)?e.map(eS):String(e)}let eT=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function eO(e,a,i,t,n){if(G.isFunction(t))return t.call(this,a,i);if(n&&(a=i),G.isString(a)){if(G.isString(t))return -1!==a.indexOf(t);if(G.isRegExp(t))return t.test(a)}}class eR{constructor(e){e&&this.set(e)}set(e,a,i){let t=this;function n(e,a,i){let n=eE(a);if(!n)throw Error("header name must be a non-empty string");let s=G.findKey(t,n);s&&void 0!==t[s]&&!0!==i&&(void 0!==i||!1===t[s])||(t[s||a]=eS(e))}let s=(e,a)=>G.forEach(e,(e,i)=>n(e,i,a));if(G.isPlainObject(e)||e instanceof this.constructor)s(e,a);else if(G.isString(e)&&(e=e.trim())&&!eT(e))s(ek(e),a);else if(G.isHeaders(e))for(let[a,t]of e.entries())n(t,a,i);else null!=e&&n(a,e,i);return this}get(e,a){if(e=eE(e)){let i=G.findKey(this,e);if(i){let e=this[i];if(!a)return e;if(!0===a)return function(e){let a;let i=Object.create(null),t=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;for(;a=t.exec(e);)i[a[1]]=a[2];return i}(e);if(G.isFunction(a))return a.call(this,e,i);if(G.isRegExp(a))return a.exec(e);throw TypeError("parser must be boolean|regexp|function")}}}has(e,a){if(e=eE(e)){let i=G.findKey(this,e);return!!(i&&void 0!==this[i]&&(!a||eO(this,this[i],i,a)))}return!1}delete(e,a){let i=this,t=!1;function n(e){if(e=eE(e)){let n=G.findKey(i,e);n&&(!a||eO(i,i[n],n,a))&&(delete i[n],t=!0)}}return G.isArray(e)?e.forEach(n):n(e),t}clear(e){let a=Object.keys(this),i=a.length,t=!1;for(;i--;){let n=a[i];(!e||eO(this,this[n],n,e,!0))&&(delete this[n],t=!0)}return t}normalize(e){let a=this,i={};return G.forEach(this,(t,n)=>{let s=G.findKey(i,n);if(s){a[s]=eS(t),delete a[n];return}let o=e?n.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,a,i)=>a.toUpperCase()+i):String(n).trim();o!==n&&delete a[n],a[o]=eS(t),i[o]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){let a=Object.create(null);return G.forEach(this,(i,t)=>{null!=i&&!1!==i&&(a[t]=e&&G.isArray(i)?i.join(", "):i)}),a}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,a])=>e+": "+a).join("\n")}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...a){let i=new this(e);return a.forEach(e=>i.set(e)),i}static accessor(e){let a=(this[ej]=this[ej]={accessors:{}}).accessors,i=this.prototype;function t(e){let t=eE(e);a[t]||(function(e,a){let i=G.toCamelCase(" "+a);["get","set","has"].forEach(t=>{Object.defineProperty(e,t+i,{value:function(e,i,n){return this[t].call(this,a,e,i,n)},configurable:!0})})}(i,e),a[t]=!0)}return G.isArray(e)?e.forEach(t):t(e),this}}function eA(e,a){let i=this||e_,t=a||i,n=eR.from(t.headers),s=t.data;return G.forEach(e,function(e){s=e.call(i,s,n.normalize(),a?a.status:void 0)}),n.normalize(),s}function eC(e){return!!(e&&e.__CANCEL__)}function eN(e,a,i){Y.call(this,null==e?"canceled":e,Y.ERR_CANCELED,a,i),this.name="CanceledError"}function ez(e,a,i){let t=i.config.validateStatus;!i.status||!t||t(i.status)?e(i):a(new Y("Request failed with status code "+i.status,[Y.ERR_BAD_REQUEST,Y.ERR_BAD_RESPONSE][Math.floor(i.status/100)-4],i.config,i.request,i))}function eL(e,a){return e&&!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(a)?a?e.replace(/\/?\/$/,"")+"/"+a.replace(/^\/+/,""):e:a}eR.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),G.reduceDescriptors(eR.prototype,({value:e},a)=>{let i=a[0].toUpperCase()+a.slice(1);return{get:()=>e,set(e){this[i]=e}}}),G.freezeMethods(eR),G.inherits(eN,Y,{__CANCEL__:!0});var eF=i(81786),eD=i(32615),eP=i(35240),eU=i(21764),eB=i(10133),eq=i(71568);let eZ="1.7.7";function eI(e){let a=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return a&&a[1]||""}let eV=/^(?:([^;]+);)?(?:[^;]+;)?(base64|),([\s\S]*)$/;var eM=i(76162);let e$=Symbol("internals");class eH extends eM.Transform{constructor(e){super({readableHighWaterMark:(e=G.toFlatObject(e,{maxRate:0,chunkSize:65536,minChunkSize:100,timeWindow:500,ticksRate:2,samplesCount:15},null,(e,a)=>!G.isUndefined(a[e]))).chunkSize});let a=this[e$]={timeWindow:e.timeWindow,chunkSize:e.chunkSize,maxRate:e.maxRate,minChunkSize:e.minChunkSize,bytesSeen:0,isCaptured:!1,notifiedBytesLoaded:0,ts:Date.now(),bytes:0,onReadCallback:null};this.on("newListener",e=>{"progress"!==e||a.isCaptured||(a.isCaptured=!0)})}_read(e){let a=this[e$];return a.onReadCallback&&a.onReadCallback(),super._read(e)}_transform(e,a,i){let t=this[e$],n=t.maxRate,s=this.readableHighWaterMark,o=t.timeWindow,r=n/(1e3/o),c=!1!==t.minChunkSize?Math.max(t.minChunkSize,.01*r):0,p=(e,a)=>{let i=Buffer.byteLength(e);t.bytesSeen+=i,t.bytes+=i,t.isCaptured&&this.emit("progress",t.bytesSeen),this.push(e)?process.nextTick(a):t.onReadCallback=()=>{t.onReadCallback=null,process.nextTick(a)}},l=(e,a)=>{let i;let l=Buffer.byteLength(e),u=null,d=s,m=0;if(n){let e=Date.now();(!t.ts||(m=e-t.ts)>=o)&&(t.ts=e,i=r-t.bytes,t.bytes=i<0?-i:0,m=0),i=r-t.bytes}if(n){if(i<=0)return setTimeout(()=>{a(null,e)},o-m);i<d&&(d=i)}d&&l>d&&l-d>c&&(u=e.subarray(d),e=e.subarray(0,d)),p(e,u?()=>{process.nextTick(a,null,u)}:a)};l(e,function e(a,t){if(a)return i(a);t?l(t,e):i(null)})}}var eK=i(17702);let{asyncIterator:eW}=Symbol,eJ=async function*(e){e.stream?yield*e.stream():e.arrayBuffer?yield await e.arrayBuffer():e[eW]?yield*e[eW]():yield e},eG=G.ALPHABET.ALPHA_DIGIT+"-_",eY=new eU.TextEncoder,eX=eY.encode("\r\n");class eQ{constructor(e,a){let{escapeName:i}=this.constructor,t=G.isString(a),n=`Content-Disposition: form-data; name="${i(e)}"${!t&&a.name?`; filename="${i(a.name)}"`:""}\r
`;t?a=eY.encode(String(a).replace(/\r?\n|\r\n?/g,"\r\n")):n+=`Content-Type: ${a.type||"application/octet-stream"}\r
`,this.headers=eY.encode(n+"\r\n"),this.contentLength=t?a.byteLength:a.size,this.size=this.headers.byteLength+this.contentLength+2,this.name=e,this.value=a}async *encode(){yield this.headers;let{value:e}=this;G.isTypedArray(e)?yield e:yield*eJ(e),yield eX}static escapeName(e){return String(e).replace(/[\r\n"]/g,e=>({"\r":"%0D","\n":"%0A",'"':"%22"})[e])}}let e0=(e,a,i)=>{let{tag:t="form-data-boundary",size:n=25,boundary:s=t+"-"+G.generateString(n,eG)}=i||{};if(!G.isFormData(e))throw TypeError("FormData instance required");if(s.length<1||s.length>70)throw Error("boundary must be 10-70 characters long");let o=eY.encode("--"+s+"\r\n"),r=eY.encode("--"+s+"--\r\n\r\n"),c=r.byteLength,p=Array.from(e.entries()).map(([e,a])=>{let i=new eQ(e,a);return c+=i.size,i});c+=o.byteLength*p.length;let l={"Content-Type":`multipart/form-data; boundary=${s}`};return Number.isFinite(c=G.toFiniteNumber(c))&&(l["Content-Length"]=c),a&&a(l),eM.Readable.from(async function*(){for(let e of p)yield o,yield*e.encode();yield r}())};class e1 extends eM.Transform{__transform(e,a,i){this.push(e),i()}_transform(e,a,i){if(0!==e.length&&(this._transform=this.__transform,120!==e[0])){let e=Buffer.alloc(2);e[0]=120,e[1]=156,this.push(e,a)}this.__transform(e,a,i)}}let e2=(e,a)=>G.isAsyncFn(e)?function(...i){let t=i.pop();e.apply(this,i).then(e=>{try{a?t(null,...a(e)):t(null,e)}catch(e){t(e)}},t)}:e,e4=function(e,a){let i;let t=Array(e=e||10),n=Array(e),s=0,o=0;return a=void 0!==a?a:1e3,function(r){let c=Date.now(),p=n[o];i||(i=c),t[s]=r,n[s]=c;let l=o,u=0;for(;l!==s;)u+=t[l++],l%=e;if((s=(s+1)%e)===o&&(o=(o+1)%e),c-i<a)return;let d=p&&c-p;return d?Math.round(1e3*u/d):void 0}},e3=function(e,a){let i,t,n=0,s=1e3/a,o=(a,s=Date.now())=>{n=s,i=null,t&&(clearTimeout(t),t=null),e.apply(null,a)};return[(...e)=>{let a=Date.now(),r=a-n;r>=s?o(e,a):(i=e,t||(t=setTimeout(()=>{t=null,o(i)},s-r)))},()=>i&&o(i)]},e9=(e,a,i=3)=>{let t=0,n=e4(50,250);return e3(i=>{let s=i.loaded,o=i.lengthComputable?i.total:void 0,r=s-t,c=n(r);t=s,e({loaded:s,total:o,progress:o?s/o:void 0,bytes:r,rate:c||void 0,estimated:c&&o&&s<=o?(o-s)/c:void 0,event:i,lengthComputable:null!=o,[a?"download":"upload"]:!0})},i)},e7=(e,a)=>{let i=null!=e;return[t=>a[0]({lengthComputable:i,total:e,loaded:t}),a[1]]},e6=e=>(...a)=>G.asap(()=>e(...a)),e5={flush:eq.constants.Z_SYNC_FLUSH,finishFlush:eq.constants.Z_SYNC_FLUSH},e8={flush:eq.constants.BROTLI_OPERATION_FLUSH,finishFlush:eq.constants.BROTLI_OPERATION_FLUSH},ae=G.isFunction(eq.createBrotliDecompress),{http:aa,https:ai}=eB,at=/https:?/,an=eg.protocols.map(e=>e+":"),as=(e,[a,i])=>(e.on("end",i).on("error",i),a);function ao(e,a){e.beforeRedirects.proxy&&e.beforeRedirects.proxy(e),e.beforeRedirects.config&&e.beforeRedirects.config(e,a)}let ar="undefined"!=typeof process&&"process"===G.kindOf(process),ac=e=>new Promise((a,i)=>{let t,n;let s=(e,a)=>{!n&&(n=!0,t&&t(e,a))},o=e=>{s(e,!0),i(e)};e(e=>{s(e),a(e)},o,e=>t=e).catch(o)}),ap=({address:e,family:a})=>{if(!G.isString(e))throw TypeError("address must be a string");return{address:e,family:a||(0>e.indexOf(".")?6:4)}},al=(e,a)=>ap(G.isObject(e)?e:{address:e,family:a}),au=ar&&function(e){return ac(async function(a,i,t){let n,s,o,r,c,p,l,{data:u,lookup:d,family:m}=e,{responseType:f,responseEncoding:h}=e,x=e.method.toUpperCase(),v=!1;if(d){let e=e2(d,e=>G.isArray(e)?e:[e]);d=(a,i,t)=>{e(a,i,(e,a,n)=>{if(e)return t(e);let s=G.isArray(a)?a.map(e=>al(e)):[al(a,n)];i.all?t(e,s):t(e,s[0].address,s[0].family)})}}let b=new eK.EventEmitter,g=()=>{e.cancelToken&&e.cancelToken.unsubscribe(y),e.signal&&e.signal.removeEventListener("abort",y),b.removeAllListeners()};function y(a){b.emit("abort",!a||a.type?new eN(null,e,c):a)}t((e,a)=>{r=!0,a&&(v=!0,g())}),b.once("abort",i),(e.cancelToken||e.signal)&&(e.cancelToken&&e.cancelToken.subscribe(y),e.signal&&(e.signal.aborted?y():e.signal.addEventListener("abort",y)));let _=new URL(eL(e.baseURL,e.url),eg.hasBrowserEnv?eg.origin:void 0),w=_.protocol||an[0];if("data:"===w){let t;if("GET"!==x)return ez(a,i,{status:405,statusText:"method not allowed",headers:{},config:e});try{t=function(e,a,i){let t=i&&i.Blob||eg.classes.Blob,n=eI(e);if(void 0===a&&t&&(a=!0),"data"===n){e=n.length?e.slice(n.length+1):e;let i=eV.exec(e);if(!i)throw new Y("Invalid URL",Y.ERR_INVALID_URL);let s=i[1],o=i[2],r=i[3],c=Buffer.from(decodeURIComponent(r),o?"base64":"utf8");if(a){if(!t)throw new Y("Blob is not supported",Y.ERR_NOT_SUPPORT);return new t([c],{type:s})}return c}throw new Y("Unsupported protocol "+n,Y.ERR_NOT_SUPPORT)}(e.url,"blob"===f,{Blob:e.env&&e.env.Blob})}catch(a){throw Y.from(a,Y.ERR_BAD_REQUEST,e)}return"text"===f?(t=t.toString(h),h&&"utf8"!==h||(t=G.stripBOM(t))):"stream"===f&&(t=eM.Readable.from(t)),ez(a,i,{data:t,status:200,statusText:"OK",headers:new eR,config:e})}if(-1===an.indexOf(w))return i(new Y("Unsupported protocol "+w,Y.ERR_BAD_REQUEST,e));let k=eR.from(e.headers).normalize();k.set("User-Agent","axios/"+eZ,!1);let{onUploadProgress:j,onDownloadProgress:E}=e,S=e.maxRate;if(G.isSpecCompliantForm(u)){let e=k.getContentType(/boundary=([-_\w\d]{10,70})/i);u=e0(u,e=>{k.set(e)},{tag:`axios-${eZ}-boundary`,boundary:e&&e[1]||void 0})}else if(G.isFormData(u)&&G.isFunction(u.getHeaders)){if(k.set(u.getHeaders()),!k.hasContentLength())try{let e=await eU.promisify(u.getLength).call(u);Number.isFinite(e)&&e>=0&&k.setContentLength(e)}catch(e){}}else if(G.isBlob(u))u.size&&k.setContentType(u.type||"application/octet-stream"),k.setContentLength(u.size||0),u=eM.Readable.from(eJ(u));else if(u&&!G.isStream(u)){if(Buffer.isBuffer(u));else if(G.isArrayBuffer(u))u=Buffer.from(new Uint8Array(u));else{if(!G.isString(u))return i(new Y("Data after transformation must be a string, an ArrayBuffer, a Buffer, or a Stream",Y.ERR_BAD_REQUEST,e));u=Buffer.from(u,"utf-8")}if(k.setContentLength(u.length,!1),e.maxBodyLength>-1&&u.length>e.maxBodyLength)return i(new Y("Request body larger than maxBodyLength limit",Y.ERR_BAD_REQUEST,e))}let T=G.toFiniteNumber(k.getContentLength());G.isArray(S)?(n=S[0],s=S[1]):n=s=S,u&&(j||n)&&(G.isStream(u)||(u=eM.Readable.from(u,{objectMode:!1})),u=eM.pipeline([u,new eH({maxRate:G.toFiniteNumber(n)})],G.noop),j&&u.on("progress",as(u,e7(T,e9(e6(j),!1,3))))),e.auth&&(o=(e.auth.username||"")+":"+(e.auth.password||"")),!o&&_.username&&(o=_.username+":"+_.password),o&&k.delete("authorization");try{p=el(_.pathname+_.search,e.params,e.paramsSerializer).replace(/^\?/,"")}catch(t){let a=Error(t.message);return a.config=e,a.url=e.url,a.exists=!0,i(a)}k.set("Accept-Encoding","gzip, compress, deflate"+(ae?", br":""),!1);let O={path:p,method:x,headers:k.toJSON(),agents:{http:e.httpAgent,https:e.httpsAgent},auth:o,protocol:w,family:m,beforeRedirect:ao,beforeRedirects:{}};G.isUndefined(d)||(O.lookup=d),e.socketPath?O.socketPath=e.socketPath:(O.hostname=_.hostname.startsWith("[")?_.hostname.slice(1,-1):_.hostname,O.port=_.port,function e(a,i,t){let n=i;if(!n&&!1!==n){let e=(0,eF.j)(t);e&&(n=new URL(e))}if(n){if(n.username&&(n.auth=(n.username||"")+":"+(n.password||"")),n.auth){(n.auth.username||n.auth.password)&&(n.auth=(n.auth.username||"")+":"+(n.auth.password||""));let e=Buffer.from(n.auth,"utf8").toString("base64");a.headers["Proxy-Authorization"]="Basic "+e}a.headers.host=a.hostname+(a.port?":"+a.port:"");let e=n.hostname||n.host;a.hostname=e,a.host=e,a.port=n.port,a.path=t,n.protocol&&(a.protocol=n.protocol.includes(":")?n.protocol:`${n.protocol}:`)}a.beforeRedirects.proxy=function(a){e(a,i,a.href)}}(O,e.proxy,w+"//"+_.hostname+(_.port?":"+_.port:"")+O.path));let R=at.test(O.protocol);if(O.agent=R?e.httpsAgent:e.httpAgent,e.transport?l=e.transport:0===e.maxRedirects?l=R?eP:eD:(e.maxRedirects&&(O.maxRedirects=e.maxRedirects),e.beforeRedirect&&(O.beforeRedirects.config=e.beforeRedirect),l=R?ai:aa),e.maxBodyLength>-1?O.maxBodyLength=e.maxBodyLength:O.maxBodyLength=1/0,e.insecureHTTPParser&&(O.insecureHTTPParser=e.insecureHTTPParser),c=l.request(O,function(t){if(c.destroyed)return;let n=[t],o=+t.headers["content-length"];if(E||s){let e=new eH({maxRate:G.toFiniteNumber(s)});E&&e.on("progress",as(e,e7(o,e9(e6(E),!0,3)))),n.push(e)}let r=t,p=t.req||c;if(!1!==e.decompress&&t.headers["content-encoding"])switch(("HEAD"===x||204===t.statusCode)&&delete t.headers["content-encoding"],(t.headers["content-encoding"]||"").toLowerCase()){case"gzip":case"x-gzip":case"compress":case"x-compress":n.push(eq.createUnzip(e5)),delete t.headers["content-encoding"];break;case"deflate":n.push(new e1),n.push(eq.createUnzip(e5)),delete t.headers["content-encoding"];break;case"br":ae&&(n.push(eq.createBrotliDecompress(e8)),delete t.headers["content-encoding"])}r=n.length>1?eM.pipeline(n,G.noop):n[0];let l=eM.finished(r,()=>{l(),g()}),u={status:t.statusCode,statusText:t.statusMessage,headers:new eR(t.headers),config:e,request:p};if("stream"===f)u.data=r,ez(a,i,u);else{let t=[],n=0;r.on("data",function(a){t.push(a),n+=a.length,e.maxContentLength>-1&&n>e.maxContentLength&&(v=!0,r.destroy(),i(new Y("maxContentLength size of "+e.maxContentLength+" exceeded",Y.ERR_BAD_RESPONSE,e,p)))}),r.on("aborted",function(){if(v)return;let a=new Y("maxContentLength size of "+e.maxContentLength+" exceeded",Y.ERR_BAD_RESPONSE,e,p);r.destroy(a),i(a)}),r.on("error",function(a){c.destroyed||i(Y.from(a,null,e,p))}),r.on("end",function(){try{let e=1===t.length?t[0]:Buffer.concat(t);"arraybuffer"===f||(e=e.toString(h),h&&"utf8"!==h||(e=G.stripBOM(e))),u.data=e}catch(a){return i(Y.from(a,null,e,u.request,u))}ez(a,i,u)})}b.once("abort",e=>{r.destroyed||(r.emit("error",e),r.destroy())})}),b.once("abort",e=>{i(e),c.destroy(e)}),c.on("error",function(a){i(Y.from(a,null,e,c))}),c.on("socket",function(e){e.setKeepAlive(!0,6e4)}),e.timeout){let a=parseInt(e.timeout,10);if(Number.isNaN(a)){i(new Y("error trying to parse `config.timeout` to int",Y.ERR_BAD_OPTION_VALUE,e,c));return}c.setTimeout(a,function(){if(r)return;let a=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded",t=e.transitional||ed;e.timeoutErrorMessage&&(a=e.timeoutErrorMessage),i(new Y(a,t.clarifyTimeoutError?Y.ETIMEDOUT:Y.ECONNABORTED,e,c)),y()})}if(G.isStream(u)){let a=!1,i=!1;u.on("end",()=>{a=!0}),u.once("error",e=>{i=!0,c.destroy(e)}),u.on("close",()=>{a||i||y(new eN("Request stream has been aborted",e,c))}),u.pipe(c)}else c.end(u)})},ad=eg.hasStandardBrowserEnv?function(){let e;let a=eg.navigator&&/(msie|trident)/i.test(eg.navigator.userAgent),i=document.createElement("a");function t(e){let t=e;return a&&(i.setAttribute("href",t),t=i.href),i.setAttribute("href",t),{href:i.href,protocol:i.protocol?i.protocol.replace(/:$/,""):"",host:i.host,search:i.search?i.search.replace(/^\?/,""):"",hash:i.hash?i.hash.replace(/^#/,""):"",hostname:i.hostname,port:i.port,pathname:"/"===i.pathname.charAt(0)?i.pathname:"/"+i.pathname}}return e=t(window.location.href),function(a){let i=G.isString(a)?t(a):a;return i.protocol===e.protocol&&i.host===e.host}}():function(){return!0},am=eg.hasStandardBrowserEnv?{write(e,a,i,t,n,s){let o=[e+"="+encodeURIComponent(a)];G.isNumber(i)&&o.push("expires="+new Date(i).toGMTString()),G.isString(t)&&o.push("path="+t),G.isString(n)&&o.push("domain="+n),!0===s&&o.push("secure"),document.cookie=o.join("; ")},read(e){let a=document.cookie.match(RegExp("(^|;\\s*)("+e+")=([^;]*)"));return a?decodeURIComponent(a[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}},af=e=>e instanceof eR?{...e}:e;function ah(e,a){a=a||{};let i={};function t(e,a,i){return G.isPlainObject(e)&&G.isPlainObject(a)?G.merge.call({caseless:i},e,a):G.isPlainObject(a)?G.merge({},a):G.isArray(a)?a.slice():a}function n(e,a,i){return G.isUndefined(a)?G.isUndefined(e)?void 0:t(void 0,e,i):t(e,a,i)}function s(e,a){if(!G.isUndefined(a))return t(void 0,a)}function o(e,a){return G.isUndefined(a)?G.isUndefined(e)?void 0:t(void 0,e):t(void 0,a)}function r(i,n,s){return s in a?t(i,n):s in e?t(void 0,i):void 0}let c={url:s,method:s,data:s,baseURL:o,transformRequest:o,transformResponse:o,paramsSerializer:o,timeout:o,timeoutMessage:o,withCredentials:o,withXSRFToken:o,adapter:o,responseType:o,xsrfCookieName:o,xsrfHeaderName:o,onUploadProgress:o,onDownloadProgress:o,decompress:o,maxContentLength:o,maxBodyLength:o,beforeRedirect:o,transport:o,httpAgent:o,httpsAgent:o,cancelToken:o,socketPath:o,responseEncoding:o,validateStatus:r,headers:(e,a)=>n(af(e),af(a),!0)};return G.forEach(Object.keys(Object.assign({},e,a)),function(t){let s=c[t]||n,o=s(e[t],a[t],t);G.isUndefined(o)&&s!==r||(i[t]=o)}),i}let ax=e=>{let a;let i=ah({},e),{data:t,withXSRFToken:n,xsrfHeaderName:s,xsrfCookieName:o,headers:r,auth:c}=i;if(i.headers=r=eR.from(r),i.url=el(eL(i.baseURL,i.url),e.params,e.paramsSerializer),c&&r.set("Authorization","Basic "+btoa((c.username||"")+":"+(c.password?unescape(encodeURIComponent(c.password)):""))),G.isFormData(t)){if(eg.hasStandardBrowserEnv||eg.hasStandardBrowserWebWorkerEnv)r.setContentType(void 0);else if(!1!==(a=r.getContentType())){let[e,...i]=a?a.split(";").map(e=>e.trim()).filter(Boolean):[];r.setContentType([e||"multipart/form-data",...i].join("; "))}}if(eg.hasStandardBrowserEnv&&(n&&G.isFunction(n)&&(n=n(i)),n||!1!==n&&ad(i.url))){let e=s&&o&&am.read(o);e&&r.set(s,e)}return i},av="undefined"!=typeof XMLHttpRequest&&function(e){return new Promise(function(a,i){let t,n,s,o,r;let c=ax(e),p=c.data,l=eR.from(c.headers).normalize(),{responseType:u,onUploadProgress:d,onDownloadProgress:m}=c;function f(){o&&o(),r&&r(),c.cancelToken&&c.cancelToken.unsubscribe(t),c.signal&&c.signal.removeEventListener("abort",t)}let h=new XMLHttpRequest;function x(){if(!h)return;let t=eR.from("getAllResponseHeaders"in h&&h.getAllResponseHeaders());ez(function(e){a(e),f()},function(e){i(e),f()},{data:u&&"text"!==u&&"json"!==u?h.response:h.responseText,status:h.status,statusText:h.statusText,headers:t,config:e,request:h}),h=null}h.open(c.method.toUpperCase(),c.url,!0),h.timeout=c.timeout,"onloadend"in h?h.onloadend=x:h.onreadystatechange=function(){h&&4===h.readyState&&(0!==h.status||h.responseURL&&0===h.responseURL.indexOf("file:"))&&setTimeout(x)},h.onabort=function(){h&&(i(new Y("Request aborted",Y.ECONNABORTED,e,h)),h=null)},h.onerror=function(){i(new Y("Network Error",Y.ERR_NETWORK,e,h)),h=null},h.ontimeout=function(){let a=c.timeout?"timeout of "+c.timeout+"ms exceeded":"timeout exceeded",t=c.transitional||ed;c.timeoutErrorMessage&&(a=c.timeoutErrorMessage),i(new Y(a,t.clarifyTimeoutError?Y.ETIMEDOUT:Y.ECONNABORTED,e,h)),h=null},void 0===p&&l.setContentType(null),"setRequestHeader"in h&&G.forEach(l.toJSON(),function(e,a){h.setRequestHeader(a,e)}),G.isUndefined(c.withCredentials)||(h.withCredentials=!!c.withCredentials),u&&"json"!==u&&(h.responseType=c.responseType),m&&([s,r]=e9(m,!0),h.addEventListener("progress",s)),d&&h.upload&&([n,o]=e9(d),h.upload.addEventListener("progress",n),h.upload.addEventListener("loadend",o)),(c.cancelToken||c.signal)&&(t=a=>{h&&(i(!a||a.type?new eN(null,e,h):a),h.abort(),h=null)},c.cancelToken&&c.cancelToken.subscribe(t),c.signal&&(c.signal.aborted?t():c.signal.addEventListener("abort",t)));let v=eI(c.url);if(v&&-1===eg.protocols.indexOf(v)){i(new Y("Unsupported protocol "+v+":",Y.ERR_BAD_REQUEST,e));return}h.send(p||null)})},ab=(e,a)=>{let{length:i}=e=e?e.filter(Boolean):[];if(a||i){let i,t=new AbortController,n=function(e){if(!i){i=!0,o();let a=e instanceof Error?e:this.reason;t.abort(a instanceof Y?a:new eN(a instanceof Error?a.message:a))}},s=a&&setTimeout(()=>{s=null,n(new Y(`timeout ${a} of ms exceeded`,Y.ETIMEDOUT))},a),o=()=>{e&&(s&&clearTimeout(s),s=null,e.forEach(e=>{e.unsubscribe?e.unsubscribe(n):e.removeEventListener("abort",n)}),e=null)};e.forEach(e=>e.addEventListener("abort",n));let{signal:r}=t;return r.unsubscribe=()=>G.asap(o),r}},ag=function*(e,a){let i,t=e.byteLength;if(!a||t<a){yield e;return}let n=0;for(;n<t;)i=n+a,yield e.slice(n,i),n=i},ay=async function*(e,a){for await(let i of a_(e))yield*ag(i,a)},a_=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}let a=e.getReader();try{for(;;){let{done:e,value:i}=await a.read();if(e)break;yield i}}finally{await a.cancel()}},aw=(e,a,i,t)=>{let n;let s=ay(e,a),o=0,r=e=>{!n&&(n=!0,t&&t(e))};return new ReadableStream({async pull(e){try{let{done:a,value:t}=await s.next();if(a){r(),e.close();return}let n=t.byteLength;if(i){let e=o+=n;i(e)}e.enqueue(new Uint8Array(t))}catch(e){throw r(e),e}},cancel:e=>(r(e),s.return())},{highWaterMark:2})},ak="function"==typeof fetch&&"function"==typeof Request&&"function"==typeof Response,aj=ak&&"function"==typeof ReadableStream,aE=ak&&("function"==typeof TextEncoder?(s=new TextEncoder,e=>s.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer())),aS=(e,...a)=>{try{return!!e(...a)}catch(e){return!1}},aT=aj&&aS(()=>{let e=!1,a=new Request(eg.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!a}),aO=aj&&aS(()=>G.isReadableStream(new Response("").body)),aR={stream:aO&&(e=>e.body)};ak&&(l=new Response,["text","arrayBuffer","blob","formData","stream"].forEach(e=>{aR[e]||(aR[e]=G.isFunction(l[e])?a=>a[e]():(a,i)=>{throw new Y(`Response type '${e}' is not supported`,Y.ERR_NOT_SUPPORT,i)})}));let aA=async e=>{if(null==e)return 0;if(G.isBlob(e))return e.size;if(G.isSpecCompliantForm(e)){let a=new Request(eg.origin,{method:"POST",body:e});return(await a.arrayBuffer()).byteLength}return G.isArrayBufferView(e)||G.isArrayBuffer(e)?e.byteLength:(G.isURLSearchParams(e)&&(e+=""),G.isString(e))?(await aE(e)).byteLength:void 0},aC=async(e,a)=>{let i=G.toFiniteNumber(e.getContentLength());return null==i?aA(a):i},aN={http:au,xhr:av,fetch:ak&&(async e=>{let a,i,{url:t,method:n,data:s,signal:o,cancelToken:r,timeout:c,onDownloadProgress:p,onUploadProgress:l,responseType:u,headers:d,withCredentials:m="same-origin",fetchOptions:f}=ax(e);u=u?(u+"").toLowerCase():"text";let h=ab([o,r&&r.toAbortSignal()],c),x=h&&h.unsubscribe&&(()=>{h.unsubscribe()});try{if(l&&aT&&"get"!==n&&"head"!==n&&0!==(i=await aC(d,s))){let e,a=new Request(t,{method:"POST",body:s,duplex:"half"});if(G.isFormData(s)&&(e=a.headers.get("content-type"))&&d.setContentType(e),a.body){let[e,t]=e7(i,e9(e6(l)));s=aw(a.body,65536,e,t)}}G.isString(m)||(m=m?"include":"omit");let o="credentials"in Request.prototype;a=new Request(t,{...f,signal:h,method:n.toUpperCase(),headers:d.normalize().toJSON(),body:s,duplex:"half",credentials:o?m:void 0});let r=await fetch(a),c=aO&&("stream"===u||"response"===u);if(aO&&(p||c&&x)){let e={};["status","statusText","headers"].forEach(a=>{e[a]=r[a]});let a=G.toFiniteNumber(r.headers.get("content-length")),[i,t]=p&&e7(a,e9(e6(p),!0))||[];r=new Response(aw(r.body,65536,i,()=>{t&&t(),x&&x()}),e)}u=u||"text";let v=await aR[G.findKey(aR,u)||"text"](r,e);return!c&&x&&x(),await new Promise((i,t)=>{ez(i,t,{data:v,headers:eR.from(r.headers),status:r.status,statusText:r.statusText,config:e,request:a})})}catch(i){if(x&&x(),i&&"TypeError"===i.name&&/fetch/i.test(i.message))throw Object.assign(new Y("Network Error",Y.ERR_NETWORK,e,a),{cause:i.cause||i});throw Y.from(i,i&&i.code,e,a)}})};G.forEach(aN,(e,a)=>{if(e){try{Object.defineProperty(e,"name",{value:a})}catch(e){}Object.defineProperty(e,"adapterName",{value:a})}});let az=e=>`- ${e}`,aL=e=>G.isFunction(e)||null===e||!1===e,aF={getAdapter:e=>{let a,i;let{length:t}=e=G.isArray(e)?e:[e],n={};for(let s=0;s<t;s++){let t;if(i=a=e[s],!aL(a)&&void 0===(i=aN[(t=String(a)).toLowerCase()]))throw new Y(`Unknown adapter '${t}'`);if(i)break;n[t||"#"+s]=i}if(!i){let e=Object.entries(n).map(([e,a])=>`adapter ${e} `+(!1===a?"is not supported by the environment":"is not available in the build"));throw new Y("There is no suitable adapter to dispatch the request "+(t?e.length>1?"since :\n"+e.map(az).join("\n"):" "+az(e[0]):"as no adapter specified"),"ERR_NOT_SUPPORT")}return i}};function aD(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new eN(null,e)}function aP(e){return aD(e),e.headers=eR.from(e.headers),e.data=eA.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1),aF.getAdapter(e.adapter||e_.adapter)(e).then(function(a){return aD(e),a.data=eA.call(e,e.transformResponse,a),a.headers=eR.from(a.headers),a},function(a){return!eC(a)&&(aD(e),a&&a.response&&(a.response.data=eA.call(e,e.transformResponse,a.response),a.response.headers=eR.from(a.response.headers))),Promise.reject(a)})}let aU={};["object","boolean","number","function","string","symbol"].forEach((e,a)=>{aU[e]=function(i){return typeof i===e||"a"+(a<1?"n ":" ")+e}});let aB={};aU.transitional=function(e,a,i){function t(e,a){return"[Axios v"+eZ+"] Transitional option '"+e+"'"+a+(i?". "+i:"")}return(i,n,s)=>{if(!1===e)throw new Y(t(n," has been removed"+(a?" in "+a:"")),Y.ERR_DEPRECATED);return a&&!aB[n]&&(aB[n]=!0,console.warn(t(n," has been deprecated since v"+a+" and will be removed in the near future"))),!e||e(i,n,s)}};let aq={assertOptions:function(e,a,i){if("object"!=typeof e)throw new Y("options must be an object",Y.ERR_BAD_OPTION_VALUE);let t=Object.keys(e),n=t.length;for(;n-- >0;){let s=t[n],o=a[s];if(o){let a=e[s],i=void 0===a||o(a,s,e);if(!0!==i)throw new Y("option "+s+" must be "+i,Y.ERR_BAD_OPTION_VALUE);continue}if(!0!==i)throw new Y("Unknown option "+s,Y.ERR_BAD_OPTION)}},validators:aU},aZ=aq.validators;class aI{constructor(e){this.defaults=e,this.interceptors={request:new eu,response:new eu}}async request(e,a){try{return await this._request(e,a)}catch(e){if(e instanceof Error){let a;Error.captureStackTrace?Error.captureStackTrace(a={}):a=Error();let i=a.stack?a.stack.replace(/^.+\n/,""):"";try{e.stack?i&&!String(e.stack).endsWith(i.replace(/^.+\n.+\n/,""))&&(e.stack+="\n"+i):e.stack=i}catch(e){}}throw e}}_request(e,a){let i,t;"string"==typeof e?(a=a||{}).url=e:a=e||{};let{transitional:n,paramsSerializer:s,headers:o}=a=ah(this.defaults,a);void 0!==n&&aq.assertOptions(n,{silentJSONParsing:aZ.transitional(aZ.boolean),forcedJSONParsing:aZ.transitional(aZ.boolean),clarifyTimeoutError:aZ.transitional(aZ.boolean)},!1),null!=s&&(G.isFunction(s)?a.paramsSerializer={serialize:s}:aq.assertOptions(s,{encode:aZ.function,serialize:aZ.function},!0)),a.method=(a.method||this.defaults.method||"get").toLowerCase();let r=o&&G.merge(o.common,o[a.method]);o&&G.forEach(["delete","get","head","post","put","patch","common"],e=>{delete o[e]}),a.headers=eR.concat(r,o);let c=[],p=!0;this.interceptors.request.forEach(function(e){("function"!=typeof e.runWhen||!1!==e.runWhen(a))&&(p=p&&e.synchronous,c.unshift(e.fulfilled,e.rejected))});let l=[];this.interceptors.response.forEach(function(e){l.push(e.fulfilled,e.rejected)});let u=0;if(!p){let e=[aP.bind(this),void 0];for(e.unshift.apply(e,c),e.push.apply(e,l),t=e.length,i=Promise.resolve(a);u<t;)i=i.then(e[u++],e[u++]);return i}t=c.length;let d=a;for(u=0;u<t;){let e=c[u++],a=c[u++];try{d=e(d)}catch(e){a.call(this,e);break}}try{i=aP.call(this,d)}catch(e){return Promise.reject(e)}for(u=0,t=l.length;u<t;)i=i.then(l[u++],l[u++]);return i}getUri(e){return el(eL((e=ah(this.defaults,e)).baseURL,e.url),e.params,e.paramsSerializer)}}G.forEach(["delete","get","head","options"],function(e){aI.prototype[e]=function(a,i){return this.request(ah(i||{},{method:e,url:a,data:(i||{}).data}))}}),G.forEach(["post","put","patch"],function(e){function a(a){return function(i,t,n){return this.request(ah(n||{},{method:e,headers:a?{"Content-Type":"multipart/form-data"}:{},url:i,data:t}))}}aI.prototype[e]=a(),aI.prototype[e+"Form"]=a(!0)});class aV{constructor(e){let a;if("function"!=typeof e)throw TypeError("executor must be a function.");this.promise=new Promise(function(e){a=e});let i=this;this.promise.then(e=>{if(!i._listeners)return;let a=i._listeners.length;for(;a-- >0;)i._listeners[a](e);i._listeners=null}),this.promise.then=e=>{let a;let t=new Promise(e=>{i.subscribe(e),a=e}).then(e);return t.cancel=function(){i.unsubscribe(a)},t},e(function(e,t,n){i.reason||(i.reason=new eN(e,t,n),a(i.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason){e(this.reason);return}this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;let a=this._listeners.indexOf(e);-1!==a&&this._listeners.splice(a,1)}toAbortSignal(){let e=new AbortController,a=a=>{e.abort(a)};return this.subscribe(a),e.signal.unsubscribe=()=>this.unsubscribe(a),e.signal}static source(){let e;return{token:new aV(function(a){e=a}),cancel:e}}}let aM={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(aM).forEach(([e,a])=>{aM[a]=e});let a$=function e(a){let i=new aI(a),t=d(aI.prototype.request,i);return G.extend(t,aI.prototype,i,{allOwnKeys:!0}),G.extend(t,i,null,{allOwnKeys:!0}),t.create=function(i){return e(ah(a,i))},t}(e_);a$.Axios=aI,a$.CanceledError=eN,a$.CancelToken=aV,a$.isCancel=eC,a$.VERSION=eZ,a$.toFormData=es,a$.AxiosError=Y,a$.Cancel=a$.CanceledError,a$.all=function(e){return Promise.all(e)},a$.spread=function(e){return function(a){return e.apply(null,a)}},a$.isAxiosError=function(e){return G.isObject(e)&&!0===e.isAxiosError},a$.mergeConfig=ah,a$.AxiosHeaders=eR,a$.formToJSON=e=>ey(G.isHTMLForm(e)?new FormData(e):e),a$.getAdapter=aF.getAdapter,a$.HttpStatusCode=aM,a$.default=a$;let aH=a$},2704:(e,a,i)=>{"use strict";i.d(a,{Gc:()=>S,KN:()=>P,Qr:()=>D,RV:()=>T,U2:()=>v,cI:()=>e_,t8:()=>_});var t=i(28964),n=e=>"checkbox"===e.type,s=e=>e instanceof Date,o=e=>null==e;let r=e=>"object"==typeof e;var c=e=>!o(e)&&!Array.isArray(e)&&r(e)&&!s(e),p=e=>c(e)&&e.target?n(e.target)?e.target.checked:e.target.value:e,l=e=>e.substring(0,e.search(/\.\d+(\.|$)/))||e,u=(e,a)=>e.has(l(a)),d=e=>{let a=e.constructor&&e.constructor.prototype;return c(a)&&a.hasOwnProperty("isPrototypeOf")},m="undefined"!=typeof window&&void 0!==window.HTMLElement&&"undefined"!=typeof document;function f(e){let a;let i=Array.isArray(e);if(e instanceof Date)a=new Date(e);else if(e instanceof Set)a=new Set(e);else if(!(!(m&&(e instanceof Blob||e instanceof FileList))&&(i||c(e))))return e;else if(a=i?[]:{},i||d(e))for(let i in e)e.hasOwnProperty(i)&&(a[i]=f(e[i]));else a=e;return a}var h=e=>Array.isArray(e)?e.filter(Boolean):[],x=e=>void 0===e,v=(e,a,i)=>{if(!a||!c(e))return i;let t=h(a.split(/[,[\].]+?/)).reduce((e,a)=>o(e)?e:e[a],e);return x(t)||t===e?x(e[a])?i:e[a]:t},b=e=>"boolean"==typeof e,g=e=>/^\w*$/.test(e),y=e=>h(e.replace(/["|']|\]/g,"").split(/\.|\[/)),_=(e,a,i)=>{let t=-1,n=g(a)?[a]:y(a),s=n.length,o=s-1;for(;++t<s;){let a=n[t],s=i;if(t!==o){let i=e[a];s=c(i)||Array.isArray(i)?i:isNaN(+n[t+1])?{}:[]}if("__proto__"===a)return;e[a]=s,e=e[a]}return e};let w={BLUR:"blur",FOCUS_OUT:"focusout",CHANGE:"change"},k={onBlur:"onBlur",onChange:"onChange",onSubmit:"onSubmit",onTouched:"onTouched",all:"all"},j={max:"max",min:"min",maxLength:"maxLength",minLength:"minLength",pattern:"pattern",required:"required",validate:"validate"},E=t.createContext(null),S=()=>t.useContext(E),T=e=>{let{children:a,...i}=e;return t.createElement(E.Provider,{value:i},a)};var O=(e,a,i,t=!0)=>{let n={defaultValues:a._defaultValues};for(let s in e)Object.defineProperty(n,s,{get:()=>(a._proxyFormState[s]!==k.all&&(a._proxyFormState[s]=!t||k.all),i&&(i[s]=!0),e[s])});return n},R=e=>c(e)&&!Object.keys(e).length,A=(e,a,i,t)=>{i(e);let{name:n,...s}=e;return R(s)||Object.keys(s).length>=Object.keys(a).length||Object.keys(s).find(e=>a[e]===(!t||k.all))},C=e=>Array.isArray(e)?e:[e],N=(e,a,i)=>!e||!a||e===a||C(e).some(e=>e&&(i?e===a:e.startsWith(a)||a.startsWith(e)));function z(e){let a=t.useRef(e);a.current=e,t.useEffect(()=>{let i=!e.disabled&&a.current.subject&&a.current.subject.subscribe({next:a.current.next});return()=>{i&&i.unsubscribe()}},[e.disabled])}var L=e=>"string"==typeof e,F=(e,a,i,t,n)=>L(e)?(t&&a.watch.add(e),v(i,e,n)):Array.isArray(e)?e.map(e=>(t&&a.watch.add(e),v(i,e))):(t&&(a.watchAll=!0),i);let D=e=>e.render(function(e){let a=S(),{name:i,disabled:n,control:s=a.control,shouldUnregister:o}=e,r=u(s._names.array,i),c=function(e){let a=S(),{control:i=a.control,name:n,defaultValue:s,disabled:o,exact:r}=e||{},c=t.useRef(n);c.current=n,z({disabled:o,subject:i._subjects.values,next:e=>{N(c.current,e.name,r)&&l(f(F(c.current,i._names,e.values||i._formValues,!1,s)))}});let[p,l]=t.useState(i._getWatch(n,s));return t.useEffect(()=>i._removeUnmounted()),p}({control:s,name:i,defaultValue:v(s._formValues,i,v(s._defaultValues,i,e.defaultValue)),exact:!0}),l=function(e){let a=S(),{control:i=a.control,disabled:n,name:s,exact:o}=e||{},[r,c]=t.useState(i._formState),p=t.useRef(!0),l=t.useRef({isDirty:!1,isLoading:!1,dirtyFields:!1,touchedFields:!1,validatingFields:!1,isValidating:!1,isValid:!1,errors:!1}),u=t.useRef(s);return u.current=s,z({disabled:n,next:e=>p.current&&N(u.current,e.name,o)&&A(e,l.current,i._updateFormState)&&c({...i._formState,...e}),subject:i._subjects.state}),t.useEffect(()=>(p.current=!0,l.current.isValid&&i._updateValid(!0),()=>{p.current=!1}),[i]),O(r,i,l.current,!1)}({control:s,name:i,exact:!0}),d=t.useRef(s.register(i,{...e.rules,value:c,...b(e.disabled)?{disabled:e.disabled}:{}}));return t.useEffect(()=>{let e=s._options.shouldUnregister||o,a=(e,a)=>{let i=v(s._fields,e);i&&i._f&&(i._f.mount=a)};if(a(i,!0),e){let e=f(v(s._options.defaultValues,i));_(s._defaultValues,i,e),x(v(s._formValues,i))&&_(s._formValues,i,e)}return()=>{(r?e&&!s._state.action:e)?s.unregister(i):a(i,!1)}},[i,s,r,o]),t.useEffect(()=>{v(s._fields,i)&&s._updateDisabledField({disabled:n,fields:s._fields,name:i,value:v(s._fields,i)._f.value})},[n,i,s]),{field:{name:i,value:c,...b(n)||l.disabled?{disabled:l.disabled||n}:{},onChange:t.useCallback(e=>d.current.onChange({target:{value:p(e),name:i},type:w.CHANGE}),[i]),onBlur:t.useCallback(()=>d.current.onBlur({target:{value:v(s._formValues,i),name:i},type:w.BLUR}),[i,s]),ref:t.useCallback(e=>{let a=v(s._fields,i);a&&e&&(a._f.ref={focus:()=>e.focus(),select:()=>e.select(),setCustomValidity:a=>e.setCustomValidity(a),reportValidity:()=>e.reportValidity()})},[s._fields,i])},formState:l,fieldState:Object.defineProperties({},{invalid:{enumerable:!0,get:()=>!!v(l.errors,i)},isDirty:{enumerable:!0,get:()=>!!v(l.dirtyFields,i)},isTouched:{enumerable:!0,get:()=>!!v(l.touchedFields,i)},isValidating:{enumerable:!0,get:()=>!!v(l.validatingFields,i)},error:{enumerable:!0,get:()=>v(l.errors,i)}})}}(e));var P=(e,a,i,t,n)=>a?{...i[e],types:{...i[e]&&i[e].types?i[e].types:{},[t]:n||!0}}:{},U=e=>({isOnSubmit:!e||e===k.onSubmit,isOnBlur:e===k.onBlur,isOnChange:e===k.onChange,isOnAll:e===k.all,isOnTouch:e===k.onTouched}),B=(e,a,i)=>!i&&(a.watchAll||a.watch.has(e)||[...a.watch].some(a=>e.startsWith(a)&&/^\.\w+/.test(e.slice(a.length))));let q=(e,a,i,t)=>{for(let n of i||Object.keys(e)){let i=v(e,n);if(i){let{_f:e,...s}=i;if(e){if(e.refs&&e.refs[0]&&a(e.refs[0],n)&&!t||e.ref&&a(e.ref,e.name)&&!t)break;q(s,a)}else c(s)&&q(s,a)}}};var Z=(e,a,i)=>{let t=C(v(e,i));return _(t,"root",a[i]),_(e,i,t),e},I=e=>"file"===e.type,V=e=>"function"==typeof e,M=e=>{if(!m)return!1;let a=e?e.ownerDocument:0;return e instanceof(a&&a.defaultView?a.defaultView.HTMLElement:HTMLElement)},$=e=>L(e),H=e=>"radio"===e.type,K=e=>e instanceof RegExp;let W={value:!1,isValid:!1},J={value:!0,isValid:!0};var G=e=>{if(Array.isArray(e)){if(e.length>1){let a=e.filter(e=>e&&e.checked&&!e.disabled).map(e=>e.value);return{value:a,isValid:!!a.length}}return e[0].checked&&!e[0].disabled?e[0].attributes&&!x(e[0].attributes.value)?x(e[0].value)||""===e[0].value?J:{value:e[0].value,isValid:!0}:J:W}return W};let Y={isValid:!1,value:null};var X=e=>Array.isArray(e)?e.reduce((e,a)=>a&&a.checked&&!a.disabled?{isValid:!0,value:a.value}:e,Y):Y;function Q(e,a,i="validate"){if($(e)||Array.isArray(e)&&e.every($)||b(e)&&!e)return{type:i,message:$(e)?e:"",ref:a}}var ee=e=>c(e)&&!K(e)?e:{value:e,message:""},ea=async(e,a,i,t,s)=>{let{ref:r,refs:p,required:l,maxLength:u,minLength:d,min:m,max:f,pattern:h,validate:g,name:y,valueAsNumber:_,mount:w,disabled:k}=e._f,E=v(a,y);if(!w||k)return{};let S=p?p[0]:r,T=e=>{t&&S.reportValidity&&(S.setCustomValidity(b(e)?"":e||""),S.reportValidity())},O={},A=H(r),C=n(r),N=(_||I(r))&&x(r.value)&&x(E)||M(r)&&""===r.value||""===E||Array.isArray(E)&&!E.length,z=P.bind(null,y,i,O),F=(e,a,i,t=j.maxLength,n=j.minLength)=>{let s=e?a:i;O[y]={type:e?t:n,message:s,ref:r,...z(e?t:n,s)}};if(s?!Array.isArray(E)||!E.length:l&&(!(A||C)&&(N||o(E))||b(E)&&!E||C&&!G(p).isValid||A&&!X(p).isValid)){let{value:e,message:a}=$(l)?{value:!!l,message:l}:ee(l);if(e&&(O[y]={type:j.required,message:a,ref:S,...z(j.required,a)},!i))return T(a),O}if(!N&&(!o(m)||!o(f))){let e,a;let t=ee(f),n=ee(m);if(o(E)||isNaN(E)){let i=r.valueAsDate||new Date(E),s=e=>new Date(new Date().toDateString()+" "+e),o="time"==r.type,c="week"==r.type;L(t.value)&&E&&(e=o?s(E)>s(t.value):c?E>t.value:i>new Date(t.value)),L(n.value)&&E&&(a=o?s(E)<s(n.value):c?E<n.value:i<new Date(n.value))}else{let i=r.valueAsNumber||(E?+E:E);o(t.value)||(e=i>t.value),o(n.value)||(a=i<n.value)}if((e||a)&&(F(!!e,t.message,n.message,j.max,j.min),!i))return T(O[y].message),O}if((u||d)&&!N&&(L(E)||s&&Array.isArray(E))){let e=ee(u),a=ee(d),t=!o(e.value)&&E.length>+e.value,n=!o(a.value)&&E.length<+a.value;if((t||n)&&(F(t,e.message,a.message),!i))return T(O[y].message),O}if(h&&!N&&L(E)){let{value:e,message:a}=ee(h);if(K(e)&&!E.match(e)&&(O[y]={type:j.pattern,message:a,ref:r,...z(j.pattern,a)},!i))return T(a),O}if(g){if(V(g)){let e=Q(await g(E,a),S);if(e&&(O[y]={...e,...z(j.validate,e.message)},!i))return T(e.message),O}else if(c(g)){let e={};for(let t in g){if(!R(e)&&!i)break;let n=Q(await g[t](E,a),S,t);n&&(e={...n,...z(t,n.message)},T(n.message),i&&(O[y]=e))}if(!R(e)&&(O[y]={ref:S,...e},!i))return O}}return T(!0),O};function ei(e,a){let i=Array.isArray(a)?a:g(a)?[a]:y(a),t=1===i.length?e:function(e,a){let i=a.slice(0,-1).length,t=0;for(;t<i;)e=x(e)?t++:e[a[t++]];return e}(e,i),n=i.length-1,s=i[n];return t&&delete t[s],0!==n&&(c(t)&&R(t)||Array.isArray(t)&&function(e){for(let a in e)if(e.hasOwnProperty(a)&&!x(e[a]))return!1;return!0}(t))&&ei(e,i.slice(0,-1)),e}var et=()=>{let e=[];return{get observers(){return e},next:a=>{for(let i of e)i.next&&i.next(a)},subscribe:a=>(e.push(a),{unsubscribe:()=>{e=e.filter(e=>e!==a)}}),unsubscribe:()=>{e=[]}}},en=e=>o(e)||!r(e);function es(e,a){if(en(e)||en(a))return e===a;if(s(e)&&s(a))return e.getTime()===a.getTime();let i=Object.keys(e),t=Object.keys(a);if(i.length!==t.length)return!1;for(let n of i){let i=e[n];if(!t.includes(n))return!1;if("ref"!==n){let e=a[n];if(s(i)&&s(e)||c(i)&&c(e)||Array.isArray(i)&&Array.isArray(e)?!es(i,e):i!==e)return!1}}return!0}var eo=e=>"select-multiple"===e.type,er=e=>H(e)||n(e),ec=e=>M(e)&&e.isConnected,ep=e=>{for(let a in e)if(V(e[a]))return!0;return!1};function el(e,a={}){let i=Array.isArray(e);if(c(e)||i)for(let i in e)Array.isArray(e[i])||c(e[i])&&!ep(e[i])?(a[i]=Array.isArray(e[i])?[]:{},el(e[i],a[i])):o(e[i])||(a[i]=!0);return a}var eu=(e,a)=>(function e(a,i,t){let n=Array.isArray(a);if(c(a)||n)for(let n in a)Array.isArray(a[n])||c(a[n])&&!ep(a[n])?x(i)||en(t[n])?t[n]=Array.isArray(a[n])?el(a[n],[]):{...el(a[n])}:e(a[n],o(i)?{}:i[n],t[n]):t[n]=!es(a[n],i[n]);return t})(e,a,el(a)),ed=(e,{valueAsNumber:a,valueAsDate:i,setValueAs:t})=>x(e)?e:a?""===e?NaN:e?+e:e:i&&L(e)?new Date(e):t?t(e):e;function em(e){let a=e.ref;return(e.refs?e.refs.every(e=>e.disabled):a.disabled)?void 0:I(a)?a.files:H(a)?X(e.refs).value:eo(a)?[...a.selectedOptions].map(({value:e})=>e):n(a)?G(e.refs).value:ed(x(a.value)?e.ref.value:a.value,e)}var ef=(e,a,i,t)=>{let n={};for(let i of e){let e=v(a,i);e&&_(n,i,e._f)}return{criteriaMode:i,names:[...e],fields:n,shouldUseNativeValidation:t}},eh=e=>x(e)?e:K(e)?e.source:c(e)?K(e.value)?e.value.source:e.value:e,ex=e=>e.mount&&(e.required||e.min||e.max||e.maxLength||e.minLength||e.pattern||e.validate);function ev(e,a,i){let t=v(e,i);if(t||g(i))return{error:t,name:i};let n=i.split(".");for(;n.length;){let t=n.join("."),s=v(a,t),o=v(e,t);if(s&&!Array.isArray(s)&&i!==t)break;if(o&&o.type)return{name:t,error:o};n.pop()}return{name:i}}var eb=(e,a,i,t,n)=>!n.isOnAll&&(!i&&n.isOnTouch?!(a||e):(i?t.isOnBlur:n.isOnBlur)?!e:(i?!t.isOnChange:!n.isOnChange)||e),eg=(e,a)=>!h(v(e,a)).length&&ei(e,a);let ey={mode:k.onSubmit,reValidateMode:k.onChange,shouldFocusError:!0};function e_(e={}){let a=t.useRef(),i=t.useRef(),[r,l]=t.useState({isDirty:!1,isValidating:!1,isLoading:V(e.defaultValues),isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,submitCount:0,dirtyFields:{},touchedFields:{},validatingFields:{},errors:e.errors||{},disabled:e.disabled||!1,defaultValues:V(e.defaultValues)?void 0:e.defaultValues});a.current||(a.current={...function(e={}){let a,i={...ey,...e},t={submitCount:0,isDirty:!1,isLoading:V(i.defaultValues),isValidating:!1,isSubmitted:!1,isSubmitting:!1,isSubmitSuccessful:!1,isValid:!1,touchedFields:{},dirtyFields:{},validatingFields:{},errors:i.errors||{},disabled:i.disabled||!1},r={},l=(c(i.defaultValues)||c(i.values))&&f(i.defaultValues||i.values)||{},d=i.shouldUnregister?{}:f(l),g={action:!1,mount:!1,watch:!1},y={mount:new Set,unMount:new Set,array:new Set,watch:new Set},j=0,E={isDirty:!1,dirtyFields:!1,validatingFields:!1,touchedFields:!1,isValidating:!1,isValid:!1,errors:!1},S={values:et(),array:et(),state:et()},T=U(i.mode),O=U(i.reValidateMode),A=i.criteriaMode===k.all,N=e=>a=>{clearTimeout(j),j=setTimeout(e,a)},z=async e=>{if(E.isValid||e){let e=i.resolver?R((await W()).errors):await G(r,!0);e!==t.isValid&&S.state.next({isValid:e})}},D=(e,a)=>{(E.isValidating||E.validatingFields)&&((e||Array.from(y.mount)).forEach(e=>{e&&(a?_(t.validatingFields,e,a):ei(t.validatingFields,e))}),S.state.next({validatingFields:t.validatingFields,isValidating:!R(t.validatingFields)}))},P=(e,a)=>{_(t.errors,e,a),S.state.next({errors:t.errors})},$=(e,a,i,t)=>{let n=v(r,e);if(n){let s=v(d,e,x(i)?v(l,e):i);x(s)||t&&t.defaultChecked||a?_(d,e,a?s:em(n._f)):Q(e,s),g.mount&&z()}},H=(e,a,i,n,s)=>{let o=!1,c=!1,p={name:e},u=!!(v(r,e)&&v(r,e)._f&&v(r,e)._f.disabled);if(!i||n){E.isDirty&&(c=t.isDirty,t.isDirty=p.isDirty=Y(),o=c!==p.isDirty);let i=u||es(v(l,e),a);c=!!(!u&&v(t.dirtyFields,e)),i||u?ei(t.dirtyFields,e):_(t.dirtyFields,e,!0),p.dirtyFields=t.dirtyFields,o=o||E.dirtyFields&&!i!==c}if(i){let a=v(t.touchedFields,e);a||(_(t.touchedFields,e,i),p.touchedFields=t.touchedFields,o=o||E.touchedFields&&a!==i)}return o&&s&&S.state.next(p),o?p:{}},K=(i,n,s,o)=>{let r=v(t.errors,i),c=E.isValid&&b(n)&&t.isValid!==n;if(e.delayError&&s?(a=N(()=>P(i,s)))(e.delayError):(clearTimeout(j),a=null,s?_(t.errors,i,s):ei(t.errors,i)),(s?!es(r,s):r)||!R(o)||c){let e={...o,...c&&b(n)?{isValid:n}:{},errors:t.errors,name:i};t={...t,...e},S.state.next(e)}},W=async e=>{D(e,!0);let a=await i.resolver(d,i.context,ef(e||y.mount,r,i.criteriaMode,i.shouldUseNativeValidation));return D(e),a},J=async e=>{let{errors:a}=await W(e);if(e)for(let i of e){let e=v(a,i);e?_(t.errors,i,e):ei(t.errors,i)}else t.errors=a;return a},G=async(e,a,n={valid:!0})=>{for(let s in e){let o=e[s];if(o){let{_f:e,...r}=o;if(e){let r=y.array.has(e.name);D([s],!0);let c=await ea(o,d,A,i.shouldUseNativeValidation&&!a,r);if(D([s]),c[e.name]&&(n.valid=!1,a))break;a||(v(c,e.name)?r?Z(t.errors,c,e.name):_(t.errors,e.name,c[e.name]):ei(t.errors,e.name))}R(r)||await G(r,a,n)}}return n.valid},Y=(e,a)=>(e&&a&&_(d,e,a),!es(ek(),l)),X=(e,a,i)=>F(e,y,{...g.mount?d:x(a)?l:L(e)?{[e]:a}:a},i,a),Q=(e,a,i={})=>{let t=v(r,e),s=a;if(t){let i=t._f;i&&(i.disabled||_(d,e,ed(a,i)),s=M(i.ref)&&o(a)?"":a,eo(i.ref)?[...i.ref.options].forEach(e=>e.selected=s.includes(e.value)):i.refs?n(i.ref)?i.refs.length>1?i.refs.forEach(e=>(!e.defaultChecked||!e.disabled)&&(e.checked=Array.isArray(s)?!!s.find(a=>a===e.value):s===e.value)):i.refs[0]&&(i.refs[0].checked=!!s):i.refs.forEach(e=>e.checked=e.value===s):I(i.ref)?i.ref.value="":(i.ref.value=s,i.ref.type||S.values.next({name:e,values:{...d}})))}(i.shouldDirty||i.shouldTouch)&&H(e,s,i.shouldTouch,i.shouldDirty,!0),i.shouldValidate&&ew(e)},ee=(e,a,i)=>{for(let t in a){let n=a[t],o=`${e}.${t}`,c=v(r,o);!y.array.has(e)&&en(n)&&(!c||c._f)||s(n)?Q(o,n,i):ee(o,n,i)}},ep=(e,a,i={})=>{let n=v(r,e),s=y.array.has(e),c=f(a);_(d,e,c),s?(S.array.next({name:e,values:{...d}}),(E.isDirty||E.dirtyFields)&&i.shouldDirty&&S.state.next({name:e,dirtyFields:eu(l,d),isDirty:Y(e,c)})):!n||n._f||o(c)?Q(e,c,i):ee(e,c,i),B(e,y)&&S.state.next({...t}),S.values.next({name:g.mount?e:void 0,values:{...d}})},el=async e=>{g.mount=!0;let n=e.target,s=n.name,o=!0,c=v(r,s),l=e=>{o=Number.isNaN(e)||e===v(d,s,e)};if(c){let u,m;let f=n.type?em(c._f):p(e),h=e.type===w.BLUR||e.type===w.FOCUS_OUT,x=!ex(c._f)&&!i.resolver&&!v(t.errors,s)&&!c._f.deps||eb(h,v(t.touchedFields,s),t.isSubmitted,O,T),b=B(s,y,h);_(d,s,f),h?(c._f.onBlur&&c._f.onBlur(e),a&&a(0)):c._f.onChange&&c._f.onChange(e);let g=H(s,f,h,!1),k=!R(g)||b;if(h||S.values.next({name:s,type:e.type,values:{...d}}),x)return E.isValid&&z(),k&&S.state.next({name:s,...b?{}:g});if(!h&&b&&S.state.next({...t}),i.resolver){let{errors:e}=await W([s]);if(l(f),o){let a=ev(t.errors,r,s),i=ev(e,r,a.name||s);u=i.error,s=i.name,m=R(e)}}else D([s],!0),u=(await ea(c,d,A,i.shouldUseNativeValidation))[s],D([s]),l(f),o&&(u?m=!1:E.isValid&&(m=await G(r,!0)));o&&(c._f.deps&&ew(c._f.deps),K(s,m,u,g))}},e_=(e,a)=>{if(v(t.errors,a)&&e.focus)return e.focus(),1},ew=async(e,a={})=>{let n,s;let o=C(e);if(i.resolver){let a=await J(x(e)?e:o);n=R(a),s=e?!o.some(e=>v(a,e)):n}else e?((s=(await Promise.all(o.map(async e=>{let a=v(r,e);return await G(a&&a._f?{[e]:a}:a)}))).every(Boolean))||t.isValid)&&z():s=n=await G(r);return S.state.next({...!L(e)||E.isValid&&n!==t.isValid?{}:{name:e},...i.resolver||!e?{isValid:n}:{},errors:t.errors}),a.shouldFocus&&!s&&q(r,e_,e?o:y.mount),s},ek=e=>{let a={...g.mount?d:l};return x(e)?a:L(e)?v(a,e):e.map(e=>v(a,e))},ej=(e,a)=>({invalid:!!v((a||t).errors,e),isDirty:!!v((a||t).dirtyFields,e),error:v((a||t).errors,e),isValidating:!!v(t.validatingFields,e),isTouched:!!v((a||t).touchedFields,e)}),eE=(e,a,i)=>{let n=(v(r,e,{_f:{}})._f||{}).ref,{ref:s,message:o,type:c,...p}=v(t.errors,e)||{};_(t.errors,e,{...p,...a,ref:n}),S.state.next({name:e,errors:t.errors,isValid:!1}),i&&i.shouldFocus&&n&&n.focus&&n.focus()},eS=(e,a={})=>{for(let n of e?C(e):y.mount)y.mount.delete(n),y.array.delete(n),a.keepValue||(ei(r,n),ei(d,n)),a.keepError||ei(t.errors,n),a.keepDirty||ei(t.dirtyFields,n),a.keepTouched||ei(t.touchedFields,n),a.keepIsValidating||ei(t.validatingFields,n),i.shouldUnregister||a.keepDefaultValue||ei(l,n);S.values.next({values:{...d}}),S.state.next({...t,...a.keepDirty?{isDirty:Y()}:{}}),a.keepIsValid||z()},eT=({disabled:e,name:a,field:i,fields:t,value:n})=>{if(b(e)&&g.mount||e){let s=e?void 0:x(n)?em(i?i._f:v(t,a)._f):n;_(d,a,s),H(a,s,!1,!1,!0)}},eO=(e,a={})=>{let t=v(r,e),n=b(a.disabled);return _(r,e,{...t||{},_f:{...t&&t._f?t._f:{ref:{name:e}},name:e,mount:!0,...a}}),y.mount.add(e),t?eT({field:t,disabled:a.disabled,name:e,value:a.value}):$(e,!0,a.value),{...n?{disabled:a.disabled}:{},...i.progressive?{required:!!a.required,min:eh(a.min),max:eh(a.max),minLength:eh(a.minLength),maxLength:eh(a.maxLength),pattern:eh(a.pattern)}:{},name:e,onChange:el,onBlur:el,ref:n=>{if(n){eO(e,a),t=v(r,e);let i=x(n.value)&&n.querySelectorAll&&n.querySelectorAll("input,select,textarea")[0]||n,s=er(i),o=t._f.refs||[];(s?o.find(e=>e===i):i===t._f.ref)||(_(r,e,{_f:{...t._f,...s?{refs:[...o.filter(ec),i,...Array.isArray(v(l,e))?[{}]:[]],ref:{type:i.type,name:e}}:{ref:i}}}),$(e,!1,void 0,i))}else(t=v(r,e,{}))._f&&(t._f.mount=!1),(i.shouldUnregister||a.shouldUnregister)&&!(u(y.array,e)&&g.action)&&y.unMount.add(e)}}},eR=()=>i.shouldFocusError&&q(r,e_,y.mount),eA=(e,a)=>async n=>{let s;n&&(n.preventDefault&&n.preventDefault(),n.persist&&n.persist());let o=f(d);if(S.state.next({isSubmitting:!0}),i.resolver){let{errors:e,values:a}=await W();t.errors=e,o=a}else await G(r);if(ei(t.errors,"root"),R(t.errors)){S.state.next({errors:{}});try{await e(o,n)}catch(e){s=e}}else a&&await a({...t.errors},n),eR(),setTimeout(eR);if(S.state.next({isSubmitted:!0,isSubmitting:!1,isSubmitSuccessful:R(t.errors)&&!s,submitCount:t.submitCount+1,errors:t.errors}),s)throw s},eC=(a,i={})=>{let n=a?f(a):l,s=f(n),o=R(a),c=o?l:s;if(i.keepDefaultValues||(l=n),!i.keepValues){if(i.keepDirtyValues)for(let e of y.mount)v(t.dirtyFields,e)?_(c,e,v(d,e)):ep(e,v(c,e));else{if(m&&x(a))for(let e of y.mount){let a=v(r,e);if(a&&a._f){let e=Array.isArray(a._f.refs)?a._f.refs[0]:a._f.ref;if(M(e)){let a=e.closest("form");if(a){a.reset();break}}}}r={}}d=e.shouldUnregister?i.keepDefaultValues?f(l):{}:f(c),S.array.next({values:{...c}}),S.values.next({values:{...c}})}y={mount:i.keepDirtyValues?y.mount:new Set,unMount:new Set,array:new Set,watch:new Set,watchAll:!1,focus:""},g.mount=!E.isValid||!!i.keepIsValid||!!i.keepDirtyValues,g.watch=!!e.shouldUnregister,S.state.next({submitCount:i.keepSubmitCount?t.submitCount:0,isDirty:!o&&(i.keepDirty?t.isDirty:!!(i.keepDefaultValues&&!es(a,l))),isSubmitted:!!i.keepIsSubmitted&&t.isSubmitted,dirtyFields:o?{}:i.keepDirtyValues?i.keepDefaultValues&&d?eu(l,d):t.dirtyFields:i.keepDefaultValues&&a?eu(l,a):i.keepDirty?t.dirtyFields:{},touchedFields:i.keepTouched?t.touchedFields:{},errors:i.keepErrors?t.errors:{},isSubmitSuccessful:!!i.keepIsSubmitSuccessful&&t.isSubmitSuccessful,isSubmitting:!1})},eN=(e,a)=>eC(V(e)?e(d):e,a);return{control:{register:eO,unregister:eS,getFieldState:ej,handleSubmit:eA,setError:eE,_executeSchema:W,_getWatch:X,_getDirty:Y,_updateValid:z,_removeUnmounted:()=>{for(let e of y.unMount){let a=v(r,e);a&&(a._f.refs?a._f.refs.every(e=>!ec(e)):!ec(a._f.ref))&&eS(e)}y.unMount=new Set},_updateFieldArray:(e,a=[],i,n,s=!0,o=!0)=>{if(n&&i){if(g.action=!0,o&&Array.isArray(v(r,e))){let a=i(v(r,e),n.argA,n.argB);s&&_(r,e,a)}if(o&&Array.isArray(v(t.errors,e))){let a=i(v(t.errors,e),n.argA,n.argB);s&&_(t.errors,e,a),eg(t.errors,e)}if(E.touchedFields&&o&&Array.isArray(v(t.touchedFields,e))){let a=i(v(t.touchedFields,e),n.argA,n.argB);s&&_(t.touchedFields,e,a)}E.dirtyFields&&(t.dirtyFields=eu(l,d)),S.state.next({name:e,isDirty:Y(e,a),dirtyFields:t.dirtyFields,errors:t.errors,isValid:t.isValid})}else _(d,e,a)},_updateDisabledField:eT,_getFieldArray:a=>h(v(g.mount?d:l,a,e.shouldUnregister?v(l,a,[]):[])),_reset:eC,_resetDefaultValues:()=>V(i.defaultValues)&&i.defaultValues().then(e=>{eN(e,i.resetOptions),S.state.next({isLoading:!1})}),_updateFormState:e=>{t={...t,...e}},_disableForm:e=>{b(e)&&(S.state.next({disabled:e}),q(r,(a,i)=>{let t=v(r,i);t&&(a.disabled=t._f.disabled||e,Array.isArray(t._f.refs)&&t._f.refs.forEach(a=>{a.disabled=t._f.disabled||e}))},0,!1))},_subjects:S,_proxyFormState:E,_setErrors:e=>{t.errors=e,S.state.next({errors:t.errors,isValid:!1})},get _fields(){return r},get _formValues(){return d},get _state(){return g},set _state(value){g=value},get _defaultValues(){return l},get _names(){return y},set _names(value){y=value},get _formState(){return t},set _formState(value){t=value},get _options(){return i},set _options(value){i={...i,...value}}},trigger:ew,register:eO,handleSubmit:eA,watch:(e,a)=>V(e)?S.values.subscribe({next:i=>e(X(void 0,a),i)}):X(e,a,!0),setValue:ep,getValues:ek,reset:eN,resetField:(e,a={})=>{v(r,e)&&(x(a.defaultValue)?ep(e,f(v(l,e))):(ep(e,a.defaultValue),_(l,e,f(a.defaultValue))),a.keepTouched||ei(t.touchedFields,e),a.keepDirty||(ei(t.dirtyFields,e),t.isDirty=a.defaultValue?Y(e,f(v(l,e))):Y()),!a.keepError&&(ei(t.errors,e),E.isValid&&z()),S.state.next({...t}))},clearErrors:e=>{e&&C(e).forEach(e=>ei(t.errors,e)),S.state.next({errors:e?t.errors:{}})},unregister:eS,setError:eE,setFocus:(e,a={})=>{let i=v(r,e),t=i&&i._f;if(t){let e=t.refs?t.refs[0]:t.ref;e.focus&&(e.focus(),a.shouldSelect&&e.select())}},getFieldState:ej}}(e),formState:r});let d=a.current.control;return d._options=e,z({subject:d._subjects.state,next:e=>{A(e,d._proxyFormState,d._updateFormState,!0)&&l({...d._formState})}}),t.useEffect(()=>d._disableForm(e.disabled),[d,e.disabled]),t.useEffect(()=>{if(d._proxyFormState.isDirty){let e=d._getDirty();e!==r.isDirty&&d._subjects.state.next({isDirty:e})}},[d,r.isDirty]),t.useEffect(()=>{e.values&&!es(e.values,i.current)?(d._reset(e.values,d._options.resetOptions),i.current=e.values,l(e=>({...e}))):d._resetDefaultValues()},[e.values,d]),t.useEffect(()=>{e.errors&&d._setErrors(e.errors)},[e.errors,d]),t.useEffect(()=>{d._state.mount||(d._updateValid(),d._state.mount=!0),d._state.watch&&(d._state.watch=!1,d._subjects.state.next({...d._formState})),d._removeUnmounted()}),t.useEffect(()=>{e.shouldUnregister&&d._subjects.values.next({values:d._getWatch()})},[e.shouldUnregister,d]),a.current.formState=O(r,d),a.current}},47751:(e,a,i)=>{"use strict";let t;i.d(a,{z:()=>ac}),function(e){e.assertEqual=e=>e,e.assertIs=function(e){},e.assertNever=function(e){throw Error()},e.arrayToEnum=e=>{let a={};for(let i of e)a[i]=i;return a},e.getValidEnumValues=a=>{let i=e.objectKeys(a).filter(e=>"number"!=typeof a[a[e]]),t={};for(let e of i)t[e]=a[e];return e.objectValues(t)},e.objectValues=a=>e.objectKeys(a).map(function(e){return a[e]}),e.objectKeys="function"==typeof Object.keys?e=>Object.keys(e):e=>{let a=[];for(let i in e)Object.prototype.hasOwnProperty.call(e,i)&&a.push(i);return a},e.find=(e,a)=>{for(let i of e)if(a(i))return i},e.isInteger="function"==typeof Number.isInteger?e=>Number.isInteger(e):e=>"number"==typeof e&&isFinite(e)&&Math.floor(e)===e,e.joinValues=function(e,a=" | "){return e.map(e=>"string"==typeof e?`'${e}'`:e).join(a)},e.jsonStringifyReplacer=(e,a)=>"bigint"==typeof a?a.toString():a}(ai||(ai={})),(at||(at={})).mergeShapes=(e,a)=>({...e,...a});let n=ai.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),s=e=>{switch(typeof e){case"undefined":return n.undefined;case"string":return n.string;case"number":return isNaN(e)?n.nan:n.number;case"boolean":return n.boolean;case"function":return n.function;case"bigint":return n.bigint;case"symbol":return n.symbol;case"object":if(Array.isArray(e))return n.array;if(null===e)return n.null;if(e.then&&"function"==typeof e.then&&e.catch&&"function"==typeof e.catch)return n.promise;if("undefined"!=typeof Map&&e instanceof Map)return n.map;if("undefined"!=typeof Set&&e instanceof Set)return n.set;if("undefined"!=typeof Date&&e instanceof Date)return n.date;return n.object;default:return n.unknown}},o=ai.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]);class r extends Error{get errors(){return this.issues}constructor(e){super(),this.issues=[],this.addIssue=e=>{this.issues=[...this.issues,e]},this.addIssues=(e=[])=>{this.issues=[...this.issues,...e]};let a=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,a):this.__proto__=a,this.name="ZodError",this.issues=e}format(e){let a=e||function(e){return e.message},i={_errors:[]},t=e=>{for(let n of e.issues)if("invalid_union"===n.code)n.unionErrors.map(t);else if("invalid_return_type"===n.code)t(n.returnTypeError);else if("invalid_arguments"===n.code)t(n.argumentsError);else if(0===n.path.length)i._errors.push(a(n));else{let e=i,t=0;for(;t<n.path.length;){let i=n.path[t];t===n.path.length-1?(e[i]=e[i]||{_errors:[]},e[i]._errors.push(a(n))):e[i]=e[i]||{_errors:[]},e=e[i],t++}}};return t(this),i}static assert(e){if(!(e instanceof r))throw Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,ai.jsonStringifyReplacer,2)}get isEmpty(){return 0===this.issues.length}flatten(e=e=>e.message){let a={},i=[];for(let t of this.issues)t.path.length>0?(a[t.path[0]]=a[t.path[0]]||[],a[t.path[0]].push(e(t))):i.push(e(t));return{formErrors:i,fieldErrors:a}}get formErrors(){return this.flatten()}}r.create=e=>new r(e);let c=(e,a)=>{let i;switch(e.code){case o.invalid_type:i=e.received===n.undefined?"Required":`Expected ${e.expected}, received ${e.received}`;break;case o.invalid_literal:i=`Invalid literal value, expected ${JSON.stringify(e.expected,ai.jsonStringifyReplacer)}`;break;case o.unrecognized_keys:i=`Unrecognized key(s) in object: ${ai.joinValues(e.keys,", ")}`;break;case o.invalid_union:i="Invalid input";break;case o.invalid_union_discriminator:i=`Invalid discriminator value. Expected ${ai.joinValues(e.options)}`;break;case o.invalid_enum_value:i=`Invalid enum value. Expected ${ai.joinValues(e.options)}, received '${e.received}'`;break;case o.invalid_arguments:i="Invalid function arguments";break;case o.invalid_return_type:i="Invalid function return type";break;case o.invalid_date:i="Invalid date";break;case o.invalid_string:"object"==typeof e.validation?"includes"in e.validation?(i=`Invalid input: must include "${e.validation.includes}"`,"number"==typeof e.validation.position&&(i=`${i} at one or more positions greater than or equal to ${e.validation.position}`)):"startsWith"in e.validation?i=`Invalid input: must start with "${e.validation.startsWith}"`:"endsWith"in e.validation?i=`Invalid input: must end with "${e.validation.endsWith}"`:ai.assertNever(e.validation):i="regex"!==e.validation?`Invalid ${e.validation}`:"Invalid";break;case o.too_small:i="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at least":"more than"} ${e.minimum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at least":"over"} ${e.minimum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${e.minimum}`:"date"===e.type?`Date must be ${e.exact?"exactly equal to ":e.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(e.minimum))}`:"Invalid input";break;case o.too_big:i="array"===e.type?`Array must contain ${e.exact?"exactly":e.inclusive?"at most":"less than"} ${e.maximum} element(s)`:"string"===e.type?`String must contain ${e.exact?"exactly":e.inclusive?"at most":"under"} ${e.maximum} character(s)`:"number"===e.type?`Number must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"bigint"===e.type?`BigInt must be ${e.exact?"exactly":e.inclusive?"less than or equal to":"less than"} ${e.maximum}`:"date"===e.type?`Date must be ${e.exact?"exactly":e.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(e.maximum))}`:"Invalid input";break;case o.custom:i="Invalid input";break;case o.invalid_intersection_types:i="Intersection results could not be merged";break;case o.not_multiple_of:i=`Number must be a multiple of ${e.multipleOf}`;break;case o.not_finite:i="Number must be finite";break;default:i=a.defaultError,ai.assertNever(e)}return{message:i}},p=c;function l(){return p}let u=e=>{let{data:a,path:i,errorMaps:t,issueData:n}=e,s=[...i,...n.path||[]],o={...n,path:s};if(void 0!==n.message)return{...n,path:s,message:n.message};let r="";for(let e of t.filter(e=>!!e).slice().reverse())r=e(o,{data:a,defaultError:r}).message;return{...n,path:s,message:r}};function d(e,a){let i=l(),t=u({issueData:a,data:e.data,path:e.path,errorMaps:[e.common.contextualErrorMap,e.schemaErrorMap,i,i===c?void 0:c].filter(e=>!!e)});e.common.issues.push(t)}class m{constructor(){this.value="valid"}dirty(){"valid"===this.value&&(this.value="dirty")}abort(){"aborted"!==this.value&&(this.value="aborted")}static mergeArray(e,a){let i=[];for(let t of a){if("aborted"===t.status)return f;"dirty"===t.status&&e.dirty(),i.push(t.value)}return{status:e.value,value:i}}static async mergeObjectAsync(e,a){let i=[];for(let e of a){let a=await e.key,t=await e.value;i.push({key:a,value:t})}return m.mergeObjectSync(e,i)}static mergeObjectSync(e,a){let i={};for(let t of a){let{key:a,value:n}=t;if("aborted"===a.status||"aborted"===n.status)return f;"dirty"===a.status&&e.dirty(),"dirty"===n.status&&e.dirty(),"__proto__"!==a.value&&(void 0!==n.value||t.alwaysSet)&&(i[a.value]=n.value)}return{status:e.value,value:i}}}let f=Object.freeze({status:"aborted"}),h=e=>({status:"dirty",value:e}),x=e=>({status:"valid",value:e}),v=e=>"aborted"===e.status,b=e=>"dirty"===e.status,g=e=>"valid"===e.status,y=e=>"undefined"!=typeof Promise&&e instanceof Promise;function _(e,a,i,t){if("a"===i&&!t)throw TypeError("Private accessor was defined without a getter");if("function"==typeof a?e!==a||!t:!a.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?t:"a"===i?t.call(e):t?t.value:a.get(e)}function w(e,a,i,t,n){if("m"===t)throw TypeError("Private method is not writable");if("a"===t&&!n)throw TypeError("Private accessor was defined without a setter");if("function"==typeof a?e!==a||!n:!a.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===t?n.call(e,i):n?n.value=i:a.set(e,i),i}"function"==typeof SuppressedError&&SuppressedError,function(e){e.errToObj=e=>"string"==typeof e?{message:e}:e||{},e.toString=e=>"string"==typeof e?e:null==e?void 0:e.message}(an||(an={}));class k{constructor(e,a,i,t){this._cachedPath=[],this.parent=e,this.data=a,this._path=i,this._key=t}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}}let j=(e,a)=>{if(g(a))return{success:!0,data:a.value};if(!e.common.issues.length)throw Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;let a=new r(e.common.issues);return this._error=a,this._error}}};function E(e){if(!e)return{};let{errorMap:a,invalid_type_error:i,required_error:t,description:n}=e;if(a&&(i||t))throw Error('Can\'t use "invalid_type_error" or "required_error" in conjunction with custom error map.');return a?{errorMap:a,description:n}:{errorMap:(a,n)=>{var s,o;let{message:r}=e;return"invalid_enum_value"===a.code?{message:null!=r?r:n.defaultError}:void 0===n.data?{message:null!==(s=null!=r?r:t)&&void 0!==s?s:n.defaultError}:"invalid_type"!==a.code?{message:n.defaultError}:{message:null!==(o=null!=r?r:i)&&void 0!==o?o:n.defaultError}},description:n}}class S{get description(){return this._def.description}_getType(e){return s(e.data)}_getOrReturnCtx(e,a){return a||{common:e.parent.common,data:e.data,parsedType:s(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new m,ctx:{common:e.parent.common,data:e.data,parsedType:s(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){let a=this._parse(e);if(y(a))throw Error("Synchronous parse encountered promise.");return a}_parseAsync(e){return Promise.resolve(this._parse(e))}parse(e,a){let i=this.safeParse(e,a);if(i.success)return i.data;throw i.error}safeParse(e,a){var i;let t={common:{issues:[],async:null!==(i=null==a?void 0:a.async)&&void 0!==i&&i,contextualErrorMap:null==a?void 0:a.errorMap},path:(null==a?void 0:a.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:s(e)},n=this._parseSync({data:e,path:t.path,parent:t});return j(t,n)}"~validate"(e){var a,i;let t={common:{issues:[],async:!!this["~standard"].async},path:[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:s(e)};if(!this["~standard"].async)try{let a=this._parseSync({data:e,path:[],parent:t});return g(a)?{value:a.value}:{issues:t.common.issues}}catch(e){(null===(i=null===(a=null==e?void 0:e.message)||void 0===a?void 0:a.toLowerCase())||void 0===i?void 0:i.includes("encountered"))&&(this["~standard"].async=!0),t.common={issues:[],async:!0}}return this._parseAsync({data:e,path:[],parent:t}).then(e=>g(e)?{value:e.value}:{issues:t.common.issues})}async parseAsync(e,a){let i=await this.safeParseAsync(e,a);if(i.success)return i.data;throw i.error}async safeParseAsync(e,a){let i={common:{issues:[],contextualErrorMap:null==a?void 0:a.errorMap,async:!0},path:(null==a?void 0:a.path)||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:s(e)},t=this._parse({data:e,path:i.path,parent:i});return j(i,await (y(t)?t:Promise.resolve(t)))}refine(e,a){let i=e=>"string"==typeof a||void 0===a?{message:a}:"function"==typeof a?a(e):a;return this._refinement((a,t)=>{let n=e(a),s=()=>t.addIssue({code:o.custom,...i(a)});return"undefined"!=typeof Promise&&n instanceof Promise?n.then(e=>!!e||(s(),!1)):!!n||(s(),!1)})}refinement(e,a){return this._refinement((i,t)=>!!e(i)||(t.addIssue("function"==typeof a?a(i,t):a),!1))}_refinement(e){return new ey({schema:this,typeName:ar.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this),this["~standard"]={version:1,vendor:"zod",validate:e=>this["~validate"](e)}}optional(){return e_.create(this,this._def)}nullable(){return ew.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return et.create(this)}promise(){return eg.create(this,this._def)}or(e){return es.create([this,e],this._def)}and(e){return ec.create(this,e,this._def)}transform(e){return new ey({...E(this._def),schema:this,typeName:ar.ZodEffects,effect:{type:"transform",transform:e}})}default(e){return new ek({...E(this._def),innerType:this,defaultValue:"function"==typeof e?e:()=>e,typeName:ar.ZodDefault})}brand(){return new eT({typeName:ar.ZodBranded,type:this,...E(this._def)})}catch(e){return new ej({...E(this._def),innerType:this,catchValue:"function"==typeof e?e:()=>e,typeName:ar.ZodCatch})}describe(e){return new this.constructor({...this._def,description:e})}pipe(e){return eO.create(this,e)}readonly(){return eR.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}}let T=/^c[^\s-]{8,}$/i,O=/^[0-9a-z]+$/,R=/^[0-9A-HJKMNP-TV-Z]{26}$/i,A=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,C=/^[a-z0-9_-]{21}$/i,N=/^[A-Za-z0-9-_]+\.[A-Za-z0-9-_]+\.[A-Za-z0-9-_]*$/,z=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,L=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,F=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,D=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\/(3[0-2]|[12]?[0-9])$/,P=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))$/,U=/^(([0-9a-fA-F]{1,4}:){7,7}[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,7}:|([0-9a-fA-F]{1,4}:){1,6}:[0-9a-fA-F]{1,4}|([0-9a-fA-F]{1,4}:){1,5}(:[0-9a-fA-F]{1,4}){1,2}|([0-9a-fA-F]{1,4}:){1,4}(:[0-9a-fA-F]{1,4}){1,3}|([0-9a-fA-F]{1,4}:){1,3}(:[0-9a-fA-F]{1,4}){1,4}|([0-9a-fA-F]{1,4}:){1,2}(:[0-9a-fA-F]{1,4}){1,5}|[0-9a-fA-F]{1,4}:((:[0-9a-fA-F]{1,4}){1,6})|:((:[0-9a-fA-F]{1,4}){1,7}|:)|fe80:(:[0-9a-fA-F]{0,4}){0,4}%[0-9a-zA-Z]{1,}|::(ffff(:0{1,4}){0,1}:){0,1}((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])|([0-9a-fA-F]{1,4}:){1,4}:((25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9])\.){3,3}(25[0-5]|(2[0-4]|1{0,1}[0-9]){0,1}[0-9]))\/(12[0-8]|1[01][0-9]|[1-9]?[0-9])$/,B=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,q=/^([0-9a-zA-Z-_]{4})*(([0-9a-zA-Z-_]{2}(==)?)|([0-9a-zA-Z-_]{3}(=)?))?$/,Z="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",I=RegExp(`^${Z}$`);function V(e){let a="([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";return e.precision?a=`${a}\\.\\d{${e.precision}}`:null==e.precision&&(a=`${a}(\\.\\d+)?`),a}function M(e){let a=`${Z}T${V(e)}`,i=[];return i.push(e.local?"Z?":"Z"),e.offset&&i.push("([+-]\\d{2}:?\\d{2})"),a=`${a}(${i.join("|")})`,RegExp(`^${a}$`)}class $ extends S{_parse(e){var a,i,s,r;let c;if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==n.string){let a=this._getOrReturnCtx(e);return d(a,{code:o.invalid_type,expected:n.string,received:a.parsedType}),f}let p=new m;for(let n of this._def.checks)if("min"===n.kind)e.data.length<n.value&&(d(c=this._getOrReturnCtx(e,c),{code:o.too_small,minimum:n.value,type:"string",inclusive:!0,exact:!1,message:n.message}),p.dirty());else if("max"===n.kind)e.data.length>n.value&&(d(c=this._getOrReturnCtx(e,c),{code:o.too_big,maximum:n.value,type:"string",inclusive:!0,exact:!1,message:n.message}),p.dirty());else if("length"===n.kind){let a=e.data.length>n.value,i=e.data.length<n.value;(a||i)&&(c=this._getOrReturnCtx(e,c),a?d(c,{code:o.too_big,maximum:n.value,type:"string",inclusive:!0,exact:!0,message:n.message}):i&&d(c,{code:o.too_small,minimum:n.value,type:"string",inclusive:!0,exact:!0,message:n.message}),p.dirty())}else if("email"===n.kind)L.test(e.data)||(d(c=this._getOrReturnCtx(e,c),{validation:"email",code:o.invalid_string,message:n.message}),p.dirty());else if("emoji"===n.kind)t||(t=RegExp("^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$","u")),t.test(e.data)||(d(c=this._getOrReturnCtx(e,c),{validation:"emoji",code:o.invalid_string,message:n.message}),p.dirty());else if("uuid"===n.kind)A.test(e.data)||(d(c=this._getOrReturnCtx(e,c),{validation:"uuid",code:o.invalid_string,message:n.message}),p.dirty());else if("nanoid"===n.kind)C.test(e.data)||(d(c=this._getOrReturnCtx(e,c),{validation:"nanoid",code:o.invalid_string,message:n.message}),p.dirty());else if("cuid"===n.kind)T.test(e.data)||(d(c=this._getOrReturnCtx(e,c),{validation:"cuid",code:o.invalid_string,message:n.message}),p.dirty());else if("cuid2"===n.kind)O.test(e.data)||(d(c=this._getOrReturnCtx(e,c),{validation:"cuid2",code:o.invalid_string,message:n.message}),p.dirty());else if("ulid"===n.kind)R.test(e.data)||(d(c=this._getOrReturnCtx(e,c),{validation:"ulid",code:o.invalid_string,message:n.message}),p.dirty());else if("url"===n.kind)try{new URL(e.data)}catch(a){d(c=this._getOrReturnCtx(e,c),{validation:"url",code:o.invalid_string,message:n.message}),p.dirty()}else"regex"===n.kind?(n.regex.lastIndex=0,n.regex.test(e.data)||(d(c=this._getOrReturnCtx(e,c),{validation:"regex",code:o.invalid_string,message:n.message}),p.dirty())):"trim"===n.kind?e.data=e.data.trim():"includes"===n.kind?e.data.includes(n.value,n.position)||(d(c=this._getOrReturnCtx(e,c),{code:o.invalid_string,validation:{includes:n.value,position:n.position},message:n.message}),p.dirty()):"toLowerCase"===n.kind?e.data=e.data.toLowerCase():"toUpperCase"===n.kind?e.data=e.data.toUpperCase():"startsWith"===n.kind?e.data.startsWith(n.value)||(d(c=this._getOrReturnCtx(e,c),{code:o.invalid_string,validation:{startsWith:n.value},message:n.message}),p.dirty()):"endsWith"===n.kind?e.data.endsWith(n.value)||(d(c=this._getOrReturnCtx(e,c),{code:o.invalid_string,validation:{endsWith:n.value},message:n.message}),p.dirty()):"datetime"===n.kind?M(n).test(e.data)||(d(c=this._getOrReturnCtx(e,c),{code:o.invalid_string,validation:"datetime",message:n.message}),p.dirty()):"date"===n.kind?I.test(e.data)||(d(c=this._getOrReturnCtx(e,c),{code:o.invalid_string,validation:"date",message:n.message}),p.dirty()):"time"===n.kind?RegExp(`^${V(n)}$`).test(e.data)||(d(c=this._getOrReturnCtx(e,c),{code:o.invalid_string,validation:"time",message:n.message}),p.dirty()):"duration"===n.kind?z.test(e.data)||(d(c=this._getOrReturnCtx(e,c),{validation:"duration",code:o.invalid_string,message:n.message}),p.dirty()):"ip"===n.kind?(a=e.data,("v4"===(i=n.version)||!i)&&F.test(a)||("v6"===i||!i)&&P.test(a)||(d(c=this._getOrReturnCtx(e,c),{validation:"ip",code:o.invalid_string,message:n.message}),p.dirty())):"jwt"===n.kind?!function(e,a){if(!N.test(e))return!1;try{let[i]=e.split("."),t=i.replace(/-/g,"+").replace(/_/g,"/").padEnd(i.length+(4-i.length%4)%4,"="),n=JSON.parse(atob(t));if("object"!=typeof n||null===n||!n.typ||!n.alg||a&&n.alg!==a)return!1;return!0}catch(e){return!1}}(e.data,n.alg)&&(d(c=this._getOrReturnCtx(e,c),{validation:"jwt",code:o.invalid_string,message:n.message}),p.dirty()):"cidr"===n.kind?(s=e.data,("v4"===(r=n.version)||!r)&&D.test(s)||("v6"===r||!r)&&U.test(s)||(d(c=this._getOrReturnCtx(e,c),{validation:"cidr",code:o.invalid_string,message:n.message}),p.dirty())):"base64"===n.kind?B.test(e.data)||(d(c=this._getOrReturnCtx(e,c),{validation:"base64",code:o.invalid_string,message:n.message}),p.dirty()):"base64url"===n.kind?q.test(e.data)||(d(c=this._getOrReturnCtx(e,c),{validation:"base64url",code:o.invalid_string,message:n.message}),p.dirty()):ai.assertNever(n);return{status:p.value,value:e.data}}_regex(e,a,i){return this.refinement(a=>e.test(a),{validation:a,code:o.invalid_string,...an.errToObj(i)})}_addCheck(e){return new $({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...an.errToObj(e)})}url(e){return this._addCheck({kind:"url",...an.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...an.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...an.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...an.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...an.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...an.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...an.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...an.errToObj(e)})}base64url(e){return this._addCheck({kind:"base64url",...an.errToObj(e)})}jwt(e){return this._addCheck({kind:"jwt",...an.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...an.errToObj(e)})}cidr(e){return this._addCheck({kind:"cidr",...an.errToObj(e)})}datetime(e){var a,i;return"string"==typeof e?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:void 0===(null==e?void 0:e.precision)?null:null==e?void 0:e.precision,offset:null!==(a=null==e?void 0:e.offset)&&void 0!==a&&a,local:null!==(i=null==e?void 0:e.local)&&void 0!==i&&i,...an.errToObj(null==e?void 0:e.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return"string"==typeof e?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:void 0===(null==e?void 0:e.precision)?null:null==e?void 0:e.precision,...an.errToObj(null==e?void 0:e.message)})}duration(e){return this._addCheck({kind:"duration",...an.errToObj(e)})}regex(e,a){return this._addCheck({kind:"regex",regex:e,...an.errToObj(a)})}includes(e,a){return this._addCheck({kind:"includes",value:e,position:null==a?void 0:a.position,...an.errToObj(null==a?void 0:a.message)})}startsWith(e,a){return this._addCheck({kind:"startsWith",value:e,...an.errToObj(a)})}endsWith(e,a){return this._addCheck({kind:"endsWith",value:e,...an.errToObj(a)})}min(e,a){return this._addCheck({kind:"min",value:e,...an.errToObj(a)})}max(e,a){return this._addCheck({kind:"max",value:e,...an.errToObj(a)})}length(e,a){return this._addCheck({kind:"length",value:e,...an.errToObj(a)})}nonempty(e){return this.min(1,an.errToObj(e))}trim(){return new $({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new $({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new $({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>"datetime"===e.kind)}get isDate(){return!!this._def.checks.find(e=>"date"===e.kind)}get isTime(){return!!this._def.checks.find(e=>"time"===e.kind)}get isDuration(){return!!this._def.checks.find(e=>"duration"===e.kind)}get isEmail(){return!!this._def.checks.find(e=>"email"===e.kind)}get isURL(){return!!this._def.checks.find(e=>"url"===e.kind)}get isEmoji(){return!!this._def.checks.find(e=>"emoji"===e.kind)}get isUUID(){return!!this._def.checks.find(e=>"uuid"===e.kind)}get isNANOID(){return!!this._def.checks.find(e=>"nanoid"===e.kind)}get isCUID(){return!!this._def.checks.find(e=>"cuid"===e.kind)}get isCUID2(){return!!this._def.checks.find(e=>"cuid2"===e.kind)}get isULID(){return!!this._def.checks.find(e=>"ulid"===e.kind)}get isIP(){return!!this._def.checks.find(e=>"ip"===e.kind)}get isCIDR(){return!!this._def.checks.find(e=>"cidr"===e.kind)}get isBase64(){return!!this._def.checks.find(e=>"base64"===e.kind)}get isBase64url(){return!!this._def.checks.find(e=>"base64url"===e.kind)}get minLength(){let e=null;for(let a of this._def.checks)"min"===a.kind&&(null===e||a.value>e)&&(e=a.value);return e}get maxLength(){let e=null;for(let a of this._def.checks)"max"===a.kind&&(null===e||a.value<e)&&(e=a.value);return e}}$.create=e=>{var a;return new $({checks:[],typeName:ar.ZodString,coerce:null!==(a=null==e?void 0:e.coerce)&&void 0!==a&&a,...E(e)})};class H extends S{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){let a;if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==n.number){let a=this._getOrReturnCtx(e);return d(a,{code:o.invalid_type,expected:n.number,received:a.parsedType}),f}let i=new m;for(let t of this._def.checks)"int"===t.kind?ai.isInteger(e.data)||(d(a=this._getOrReturnCtx(e,a),{code:o.invalid_type,expected:"integer",received:"float",message:t.message}),i.dirty()):"min"===t.kind?(t.inclusive?e.data<t.value:e.data<=t.value)&&(d(a=this._getOrReturnCtx(e,a),{code:o.too_small,minimum:t.value,type:"number",inclusive:t.inclusive,exact:!1,message:t.message}),i.dirty()):"max"===t.kind?(t.inclusive?e.data>t.value:e.data>=t.value)&&(d(a=this._getOrReturnCtx(e,a),{code:o.too_big,maximum:t.value,type:"number",inclusive:t.inclusive,exact:!1,message:t.message}),i.dirty()):"multipleOf"===t.kind?0!==function(e,a){let i=(e.toString().split(".")[1]||"").length,t=(a.toString().split(".")[1]||"").length,n=i>t?i:t;return parseInt(e.toFixed(n).replace(".",""))%parseInt(a.toFixed(n).replace(".",""))/Math.pow(10,n)}(e.data,t.value)&&(d(a=this._getOrReturnCtx(e,a),{code:o.not_multiple_of,multipleOf:t.value,message:t.message}),i.dirty()):"finite"===t.kind?Number.isFinite(e.data)||(d(a=this._getOrReturnCtx(e,a),{code:o.not_finite,message:t.message}),i.dirty()):ai.assertNever(t);return{status:i.value,value:e.data}}gte(e,a){return this.setLimit("min",e,!0,an.toString(a))}gt(e,a){return this.setLimit("min",e,!1,an.toString(a))}lte(e,a){return this.setLimit("max",e,!0,an.toString(a))}lt(e,a){return this.setLimit("max",e,!1,an.toString(a))}setLimit(e,a,i,t){return new H({...this._def,checks:[...this._def.checks,{kind:e,value:a,inclusive:i,message:an.toString(t)}]})}_addCheck(e){return new H({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:an.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:an.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:an.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:an.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:an.toString(e)})}multipleOf(e,a){return this._addCheck({kind:"multipleOf",value:e,message:an.toString(a)})}finite(e){return this._addCheck({kind:"finite",message:an.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:an.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:an.toString(e)})}get minValue(){let e=null;for(let a of this._def.checks)"min"===a.kind&&(null===e||a.value>e)&&(e=a.value);return e}get maxValue(){let e=null;for(let a of this._def.checks)"max"===a.kind&&(null===e||a.value<e)&&(e=a.value);return e}get isInt(){return!!this._def.checks.find(e=>"int"===e.kind||"multipleOf"===e.kind&&ai.isInteger(e.value))}get isFinite(){let e=null,a=null;for(let i of this._def.checks){if("finite"===i.kind||"int"===i.kind||"multipleOf"===i.kind)return!0;"min"===i.kind?(null===a||i.value>a)&&(a=i.value):"max"===i.kind&&(null===e||i.value<e)&&(e=i.value)}return Number.isFinite(a)&&Number.isFinite(e)}}H.create=e=>new H({checks:[],typeName:ar.ZodNumber,coerce:(null==e?void 0:e.coerce)||!1,...E(e)});class K extends S{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){let a;if(this._def.coerce)try{e.data=BigInt(e.data)}catch(a){return this._getInvalidInput(e)}if(this._getType(e)!==n.bigint)return this._getInvalidInput(e);let i=new m;for(let t of this._def.checks)"min"===t.kind?(t.inclusive?e.data<t.value:e.data<=t.value)&&(d(a=this._getOrReturnCtx(e,a),{code:o.too_small,type:"bigint",minimum:t.value,inclusive:t.inclusive,message:t.message}),i.dirty()):"max"===t.kind?(t.inclusive?e.data>t.value:e.data>=t.value)&&(d(a=this._getOrReturnCtx(e,a),{code:o.too_big,type:"bigint",maximum:t.value,inclusive:t.inclusive,message:t.message}),i.dirty()):"multipleOf"===t.kind?e.data%t.value!==BigInt(0)&&(d(a=this._getOrReturnCtx(e,a),{code:o.not_multiple_of,multipleOf:t.value,message:t.message}),i.dirty()):ai.assertNever(t);return{status:i.value,value:e.data}}_getInvalidInput(e){let a=this._getOrReturnCtx(e);return d(a,{code:o.invalid_type,expected:n.bigint,received:a.parsedType}),f}gte(e,a){return this.setLimit("min",e,!0,an.toString(a))}gt(e,a){return this.setLimit("min",e,!1,an.toString(a))}lte(e,a){return this.setLimit("max",e,!0,an.toString(a))}lt(e,a){return this.setLimit("max",e,!1,an.toString(a))}setLimit(e,a,i,t){return new K({...this._def,checks:[...this._def.checks,{kind:e,value:a,inclusive:i,message:an.toString(t)}]})}_addCheck(e){return new K({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:an.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:an.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:an.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:an.toString(e)})}multipleOf(e,a){return this._addCheck({kind:"multipleOf",value:e,message:an.toString(a)})}get minValue(){let e=null;for(let a of this._def.checks)"min"===a.kind&&(null===e||a.value>e)&&(e=a.value);return e}get maxValue(){let e=null;for(let a of this._def.checks)"max"===a.kind&&(null===e||a.value<e)&&(e=a.value);return e}}K.create=e=>{var a;return new K({checks:[],typeName:ar.ZodBigInt,coerce:null!==(a=null==e?void 0:e.coerce)&&void 0!==a&&a,...E(e)})};class W extends S{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==n.boolean){let a=this._getOrReturnCtx(e);return d(a,{code:o.invalid_type,expected:n.boolean,received:a.parsedType}),f}return x(e.data)}}W.create=e=>new W({typeName:ar.ZodBoolean,coerce:(null==e?void 0:e.coerce)||!1,...E(e)});class J extends S{_parse(e){let a;if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==n.date){let a=this._getOrReturnCtx(e);return d(a,{code:o.invalid_type,expected:n.date,received:a.parsedType}),f}if(isNaN(e.data.getTime()))return d(this._getOrReturnCtx(e),{code:o.invalid_date}),f;let i=new m;for(let t of this._def.checks)"min"===t.kind?e.data.getTime()<t.value&&(d(a=this._getOrReturnCtx(e,a),{code:o.too_small,message:t.message,inclusive:!0,exact:!1,minimum:t.value,type:"date"}),i.dirty()):"max"===t.kind?e.data.getTime()>t.value&&(d(a=this._getOrReturnCtx(e,a),{code:o.too_big,message:t.message,inclusive:!0,exact:!1,maximum:t.value,type:"date"}),i.dirty()):ai.assertNever(t);return{status:i.value,value:new Date(e.data.getTime())}}_addCheck(e){return new J({...this._def,checks:[...this._def.checks,e]})}min(e,a){return this._addCheck({kind:"min",value:e.getTime(),message:an.toString(a)})}max(e,a){return this._addCheck({kind:"max",value:e.getTime(),message:an.toString(a)})}get minDate(){let e=null;for(let a of this._def.checks)"min"===a.kind&&(null===e||a.value>e)&&(e=a.value);return null!=e?new Date(e):null}get maxDate(){let e=null;for(let a of this._def.checks)"max"===a.kind&&(null===e||a.value<e)&&(e=a.value);return null!=e?new Date(e):null}}J.create=e=>new J({checks:[],coerce:(null==e?void 0:e.coerce)||!1,typeName:ar.ZodDate,...E(e)});class G extends S{_parse(e){if(this._getType(e)!==n.symbol){let a=this._getOrReturnCtx(e);return d(a,{code:o.invalid_type,expected:n.symbol,received:a.parsedType}),f}return x(e.data)}}G.create=e=>new G({typeName:ar.ZodSymbol,...E(e)});class Y extends S{_parse(e){if(this._getType(e)!==n.undefined){let a=this._getOrReturnCtx(e);return d(a,{code:o.invalid_type,expected:n.undefined,received:a.parsedType}),f}return x(e.data)}}Y.create=e=>new Y({typeName:ar.ZodUndefined,...E(e)});class X extends S{_parse(e){if(this._getType(e)!==n.null){let a=this._getOrReturnCtx(e);return d(a,{code:o.invalid_type,expected:n.null,received:a.parsedType}),f}return x(e.data)}}X.create=e=>new X({typeName:ar.ZodNull,...E(e)});class Q extends S{constructor(){super(...arguments),this._any=!0}_parse(e){return x(e.data)}}Q.create=e=>new Q({typeName:ar.ZodAny,...E(e)});class ee extends S{constructor(){super(...arguments),this._unknown=!0}_parse(e){return x(e.data)}}ee.create=e=>new ee({typeName:ar.ZodUnknown,...E(e)});class ea extends S{_parse(e){let a=this._getOrReturnCtx(e);return d(a,{code:o.invalid_type,expected:n.never,received:a.parsedType}),f}}ea.create=e=>new ea({typeName:ar.ZodNever,...E(e)});class ei extends S{_parse(e){if(this._getType(e)!==n.undefined){let a=this._getOrReturnCtx(e);return d(a,{code:o.invalid_type,expected:n.void,received:a.parsedType}),f}return x(e.data)}}ei.create=e=>new ei({typeName:ar.ZodVoid,...E(e)});class et extends S{_parse(e){let{ctx:a,status:i}=this._processInputParams(e),t=this._def;if(a.parsedType!==n.array)return d(a,{code:o.invalid_type,expected:n.array,received:a.parsedType}),f;if(null!==t.exactLength){let e=a.data.length>t.exactLength.value,n=a.data.length<t.exactLength.value;(e||n)&&(d(a,{code:e?o.too_big:o.too_small,minimum:n?t.exactLength.value:void 0,maximum:e?t.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:t.exactLength.message}),i.dirty())}if(null!==t.minLength&&a.data.length<t.minLength.value&&(d(a,{code:o.too_small,minimum:t.minLength.value,type:"array",inclusive:!0,exact:!1,message:t.minLength.message}),i.dirty()),null!==t.maxLength&&a.data.length>t.maxLength.value&&(d(a,{code:o.too_big,maximum:t.maxLength.value,type:"array",inclusive:!0,exact:!1,message:t.maxLength.message}),i.dirty()),a.common.async)return Promise.all([...a.data].map((e,i)=>t.type._parseAsync(new k(a,e,a.path,i)))).then(e=>m.mergeArray(i,e));let s=[...a.data].map((e,i)=>t.type._parseSync(new k(a,e,a.path,i)));return m.mergeArray(i,s)}get element(){return this._def.type}min(e,a){return new et({...this._def,minLength:{value:e,message:an.toString(a)}})}max(e,a){return new et({...this._def,maxLength:{value:e,message:an.toString(a)}})}length(e,a){return new et({...this._def,exactLength:{value:e,message:an.toString(a)}})}nonempty(e){return this.min(1,e)}}et.create=(e,a)=>new et({type:e,minLength:null,maxLength:null,exactLength:null,typeName:ar.ZodArray,...E(a)});class en extends S{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(null!==this._cached)return this._cached;let e=this._def.shape(),a=ai.objectKeys(e);return this._cached={shape:e,keys:a}}_parse(e){if(this._getType(e)!==n.object){let a=this._getOrReturnCtx(e);return d(a,{code:o.invalid_type,expected:n.object,received:a.parsedType}),f}let{status:a,ctx:i}=this._processInputParams(e),{shape:t,keys:s}=this._getCached(),r=[];if(!(this._def.catchall instanceof ea&&"strip"===this._def.unknownKeys))for(let e in i.data)s.includes(e)||r.push(e);let c=[];for(let e of s){let a=t[e],n=i.data[e];c.push({key:{status:"valid",value:e},value:a._parse(new k(i,n,i.path,e)),alwaysSet:e in i.data})}if(this._def.catchall instanceof ea){let e=this._def.unknownKeys;if("passthrough"===e)for(let e of r)c.push({key:{status:"valid",value:e},value:{status:"valid",value:i.data[e]}});else if("strict"===e)r.length>0&&(d(i,{code:o.unrecognized_keys,keys:r}),a.dirty());else if("strip"===e);else throw Error("Internal ZodObject error: invalid unknownKeys value.")}else{let e=this._def.catchall;for(let a of r){let t=i.data[a];c.push({key:{status:"valid",value:a},value:e._parse(new k(i,t,i.path,a)),alwaysSet:a in i.data})}}return i.common.async?Promise.resolve().then(async()=>{let e=[];for(let a of c){let i=await a.key,t=await a.value;e.push({key:i,value:t,alwaysSet:a.alwaysSet})}return e}).then(e=>m.mergeObjectSync(a,e)):m.mergeObjectSync(a,c)}get shape(){return this._def.shape()}strict(e){return an.errToObj,new en({...this._def,unknownKeys:"strict",...void 0!==e?{errorMap:(a,i)=>{var t,n,s,o;let r=null!==(s=null===(n=(t=this._def).errorMap)||void 0===n?void 0:n.call(t,a,i).message)&&void 0!==s?s:i.defaultError;return"unrecognized_keys"===a.code?{message:null!==(o=an.errToObj(e).message)&&void 0!==o?o:r}:{message:r}}}:{}})}strip(){return new en({...this._def,unknownKeys:"strip"})}passthrough(){return new en({...this._def,unknownKeys:"passthrough"})}extend(e){return new en({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new en({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:ar.ZodObject})}setKey(e,a){return this.augment({[e]:a})}catchall(e){return new en({...this._def,catchall:e})}pick(e){let a={};return ai.objectKeys(e).forEach(i=>{e[i]&&this.shape[i]&&(a[i]=this.shape[i])}),new en({...this._def,shape:()=>a})}omit(e){let a={};return ai.objectKeys(this.shape).forEach(i=>{e[i]||(a[i]=this.shape[i])}),new en({...this._def,shape:()=>a})}deepPartial(){return function e(a){if(a instanceof en){let i={};for(let t in a.shape){let n=a.shape[t];i[t]=e_.create(e(n))}return new en({...a._def,shape:()=>i})}return a instanceof et?new et({...a._def,type:e(a.element)}):a instanceof e_?e_.create(e(a.unwrap())):a instanceof ew?ew.create(e(a.unwrap())):a instanceof ep?ep.create(a.items.map(a=>e(a))):a}(this)}partial(e){let a={};return ai.objectKeys(this.shape).forEach(i=>{let t=this.shape[i];e&&!e[i]?a[i]=t:a[i]=t.optional()}),new en({...this._def,shape:()=>a})}required(e){let a={};return ai.objectKeys(this.shape).forEach(i=>{if(e&&!e[i])a[i]=this.shape[i];else{let e=this.shape[i];for(;e instanceof e_;)e=e._def.innerType;a[i]=e}}),new en({...this._def,shape:()=>a})}keyof(){return ex(ai.objectKeys(this.shape))}}en.create=(e,a)=>new en({shape:()=>e,unknownKeys:"strip",catchall:ea.create(),typeName:ar.ZodObject,...E(a)}),en.strictCreate=(e,a)=>new en({shape:()=>e,unknownKeys:"strict",catchall:ea.create(),typeName:ar.ZodObject,...E(a)}),en.lazycreate=(e,a)=>new en({shape:e,unknownKeys:"strip",catchall:ea.create(),typeName:ar.ZodObject,...E(a)});class es extends S{_parse(e){let{ctx:a}=this._processInputParams(e),i=this._def.options;if(a.common.async)return Promise.all(i.map(async e=>{let i={...a,common:{...a.common,issues:[]},parent:null};return{result:await e._parseAsync({data:a.data,path:a.path,parent:i}),ctx:i}})).then(function(e){for(let a of e)if("valid"===a.result.status)return a.result;for(let i of e)if("dirty"===i.result.status)return a.common.issues.push(...i.ctx.common.issues),i.result;let i=e.map(e=>new r(e.ctx.common.issues));return d(a,{code:o.invalid_union,unionErrors:i}),f});{let e;let t=[];for(let n of i){let i={...a,common:{...a.common,issues:[]},parent:null},s=n._parseSync({data:a.data,path:a.path,parent:i});if("valid"===s.status)return s;"dirty"!==s.status||e||(e={result:s,ctx:i}),i.common.issues.length&&t.push(i.common.issues)}if(e)return a.common.issues.push(...e.ctx.common.issues),e.result;let n=t.map(e=>new r(e));return d(a,{code:o.invalid_union,unionErrors:n}),f}}get options(){return this._def.options}}es.create=(e,a)=>new es({options:e,typeName:ar.ZodUnion,...E(a)});let eo=e=>{if(e instanceof ef)return eo(e.schema);if(e instanceof ey)return eo(e.innerType());if(e instanceof eh)return[e.value];if(e instanceof ev)return e.options;if(e instanceof eb)return ai.objectValues(e.enum);if(e instanceof ek)return eo(e._def.innerType);if(e instanceof Y)return[void 0];else if(e instanceof X)return[null];else if(e instanceof e_)return[void 0,...eo(e.unwrap())];else if(e instanceof ew)return[null,...eo(e.unwrap())];else if(e instanceof eT)return eo(e.unwrap());else if(e instanceof eR)return eo(e.unwrap());else if(e instanceof ej)return eo(e._def.innerType);else return[]};class er extends S{_parse(e){let{ctx:a}=this._processInputParams(e);if(a.parsedType!==n.object)return d(a,{code:o.invalid_type,expected:n.object,received:a.parsedType}),f;let i=this.discriminator,t=a.data[i],s=this.optionsMap.get(t);return s?a.common.async?s._parseAsync({data:a.data,path:a.path,parent:a}):s._parseSync({data:a.data,path:a.path,parent:a}):(d(a,{code:o.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[i]}),f)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,a,i){let t=new Map;for(let i of a){let a=eo(i.shape[e]);if(!a.length)throw Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(let n of a){if(t.has(n))throw Error(`Discriminator property ${String(e)} has duplicate value ${String(n)}`);t.set(n,i)}}return new er({typeName:ar.ZodDiscriminatedUnion,discriminator:e,options:a,optionsMap:t,...E(i)})}}class ec extends S{_parse(e){let{status:a,ctx:i}=this._processInputParams(e),t=(e,t)=>{if(v(e)||v(t))return f;let r=function e(a,i){let t=s(a),o=s(i);if(a===i)return{valid:!0,data:a};if(t===n.object&&o===n.object){let t=ai.objectKeys(i),n=ai.objectKeys(a).filter(e=>-1!==t.indexOf(e)),s={...a,...i};for(let t of n){let n=e(a[t],i[t]);if(!n.valid)return{valid:!1};s[t]=n.data}return{valid:!0,data:s}}if(t===n.array&&o===n.array){if(a.length!==i.length)return{valid:!1};let t=[];for(let n=0;n<a.length;n++){let s=e(a[n],i[n]);if(!s.valid)return{valid:!1};t.push(s.data)}return{valid:!0,data:t}}return t===n.date&&o===n.date&&+a==+i?{valid:!0,data:a}:{valid:!1}}(e.value,t.value);return r.valid?((b(e)||b(t))&&a.dirty(),{status:a.value,value:r.data}):(d(i,{code:o.invalid_intersection_types}),f)};return i.common.async?Promise.all([this._def.left._parseAsync({data:i.data,path:i.path,parent:i}),this._def.right._parseAsync({data:i.data,path:i.path,parent:i})]).then(([e,a])=>t(e,a)):t(this._def.left._parseSync({data:i.data,path:i.path,parent:i}),this._def.right._parseSync({data:i.data,path:i.path,parent:i}))}}ec.create=(e,a,i)=>new ec({left:e,right:a,typeName:ar.ZodIntersection,...E(i)});class ep extends S{_parse(e){let{status:a,ctx:i}=this._processInputParams(e);if(i.parsedType!==n.array)return d(i,{code:o.invalid_type,expected:n.array,received:i.parsedType}),f;if(i.data.length<this._def.items.length)return d(i,{code:o.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),f;!this._def.rest&&i.data.length>this._def.items.length&&(d(i,{code:o.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),a.dirty());let t=[...i.data].map((e,a)=>{let t=this._def.items[a]||this._def.rest;return t?t._parse(new k(i,e,i.path,a)):null}).filter(e=>!!e);return i.common.async?Promise.all(t).then(e=>m.mergeArray(a,e)):m.mergeArray(a,t)}get items(){return this._def.items}rest(e){return new ep({...this._def,rest:e})}}ep.create=(e,a)=>{if(!Array.isArray(e))throw Error("You must pass an array of schemas to z.tuple([ ... ])");return new ep({items:e,typeName:ar.ZodTuple,rest:null,...E(a)})};class el extends S{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:a,ctx:i}=this._processInputParams(e);if(i.parsedType!==n.object)return d(i,{code:o.invalid_type,expected:n.object,received:i.parsedType}),f;let t=[],s=this._def.keyType,r=this._def.valueType;for(let e in i.data)t.push({key:s._parse(new k(i,e,i.path,e)),value:r._parse(new k(i,i.data[e],i.path,e)),alwaysSet:e in i.data});return i.common.async?m.mergeObjectAsync(a,t):m.mergeObjectSync(a,t)}get element(){return this._def.valueType}static create(e,a,i){return new el(a instanceof S?{keyType:e,valueType:a,typeName:ar.ZodRecord,...E(i)}:{keyType:$.create(),valueType:e,typeName:ar.ZodRecord,...E(a)})}}class eu extends S{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:a,ctx:i}=this._processInputParams(e);if(i.parsedType!==n.map)return d(i,{code:o.invalid_type,expected:n.map,received:i.parsedType}),f;let t=this._def.keyType,s=this._def.valueType,r=[...i.data.entries()].map(([e,a],n)=>({key:t._parse(new k(i,e,i.path,[n,"key"])),value:s._parse(new k(i,a,i.path,[n,"value"]))}));if(i.common.async){let e=new Map;return Promise.resolve().then(async()=>{for(let i of r){let t=await i.key,n=await i.value;if("aborted"===t.status||"aborted"===n.status)return f;("dirty"===t.status||"dirty"===n.status)&&a.dirty(),e.set(t.value,n.value)}return{status:a.value,value:e}})}{let e=new Map;for(let i of r){let t=i.key,n=i.value;if("aborted"===t.status||"aborted"===n.status)return f;("dirty"===t.status||"dirty"===n.status)&&a.dirty(),e.set(t.value,n.value)}return{status:a.value,value:e}}}}eu.create=(e,a,i)=>new eu({valueType:a,keyType:e,typeName:ar.ZodMap,...E(i)});class ed extends S{_parse(e){let{status:a,ctx:i}=this._processInputParams(e);if(i.parsedType!==n.set)return d(i,{code:o.invalid_type,expected:n.set,received:i.parsedType}),f;let t=this._def;null!==t.minSize&&i.data.size<t.minSize.value&&(d(i,{code:o.too_small,minimum:t.minSize.value,type:"set",inclusive:!0,exact:!1,message:t.minSize.message}),a.dirty()),null!==t.maxSize&&i.data.size>t.maxSize.value&&(d(i,{code:o.too_big,maximum:t.maxSize.value,type:"set",inclusive:!0,exact:!1,message:t.maxSize.message}),a.dirty());let s=this._def.valueType;function r(e){let i=new Set;for(let t of e){if("aborted"===t.status)return f;"dirty"===t.status&&a.dirty(),i.add(t.value)}return{status:a.value,value:i}}let c=[...i.data.values()].map((e,a)=>s._parse(new k(i,e,i.path,a)));return i.common.async?Promise.all(c).then(e=>r(e)):r(c)}min(e,a){return new ed({...this._def,minSize:{value:e,message:an.toString(a)}})}max(e,a){return new ed({...this._def,maxSize:{value:e,message:an.toString(a)}})}size(e,a){return this.min(e,a).max(e,a)}nonempty(e){return this.min(1,e)}}ed.create=(e,a)=>new ed({valueType:e,minSize:null,maxSize:null,typeName:ar.ZodSet,...E(a)});class em extends S{constructor(){super(...arguments),this.validate=this.implement}_parse(e){let{ctx:a}=this._processInputParams(e);if(a.parsedType!==n.function)return d(a,{code:o.invalid_type,expected:n.function,received:a.parsedType}),f;function i(e,i){return u({data:e,path:a.path,errorMaps:[a.common.contextualErrorMap,a.schemaErrorMap,l(),c].filter(e=>!!e),issueData:{code:o.invalid_arguments,argumentsError:i}})}function t(e,i){return u({data:e,path:a.path,errorMaps:[a.common.contextualErrorMap,a.schemaErrorMap,l(),c].filter(e=>!!e),issueData:{code:o.invalid_return_type,returnTypeError:i}})}let s={errorMap:a.common.contextualErrorMap},p=a.data;if(this._def.returns instanceof eg){let e=this;return x(async function(...a){let n=new r([]),o=await e._def.args.parseAsync(a,s).catch(e=>{throw n.addIssue(i(a,e)),n}),c=await Reflect.apply(p,this,o);return await e._def.returns._def.type.parseAsync(c,s).catch(e=>{throw n.addIssue(t(c,e)),n})})}{let e=this;return x(function(...a){let n=e._def.args.safeParse(a,s);if(!n.success)throw new r([i(a,n.error)]);let o=Reflect.apply(p,this,n.data),c=e._def.returns.safeParse(o,s);if(!c.success)throw new r([t(o,c.error)]);return c.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new em({...this._def,args:ep.create(e).rest(ee.create())})}returns(e){return new em({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,a,i){return new em({args:e||ep.create([]).rest(ee.create()),returns:a||ee.create(),typeName:ar.ZodFunction,...E(i)})}}class ef extends S{get schema(){return this._def.getter()}_parse(e){let{ctx:a}=this._processInputParams(e);return this._def.getter()._parse({data:a.data,path:a.path,parent:a})}}ef.create=(e,a)=>new ef({getter:e,typeName:ar.ZodLazy,...E(a)});class eh extends S{_parse(e){if(e.data!==this._def.value){let a=this._getOrReturnCtx(e);return d(a,{received:a.data,code:o.invalid_literal,expected:this._def.value}),f}return{status:"valid",value:e.data}}get value(){return this._def.value}}function ex(e,a){return new ev({values:e,typeName:ar.ZodEnum,...E(a)})}eh.create=(e,a)=>new eh({value:e,typeName:ar.ZodLiteral,...E(a)});class ev extends S{constructor(){super(...arguments),as.set(this,void 0)}_parse(e){if("string"!=typeof e.data){let a=this._getOrReturnCtx(e),i=this._def.values;return d(a,{expected:ai.joinValues(i),received:a.parsedType,code:o.invalid_type}),f}if(_(this,as,"f")||w(this,as,new Set(this._def.values),"f"),!_(this,as,"f").has(e.data)){let a=this._getOrReturnCtx(e),i=this._def.values;return d(a,{received:a.data,code:o.invalid_enum_value,options:i}),f}return x(e.data)}get options(){return this._def.values}get enum(){let e={};for(let a of this._def.values)e[a]=a;return e}get Values(){let e={};for(let a of this._def.values)e[a]=a;return e}get Enum(){let e={};for(let a of this._def.values)e[a]=a;return e}extract(e,a=this._def){return ev.create(e,{...this._def,...a})}exclude(e,a=this._def){return ev.create(this.options.filter(a=>!e.includes(a)),{...this._def,...a})}}as=new WeakMap,ev.create=ex;class eb extends S{constructor(){super(...arguments),ao.set(this,void 0)}_parse(e){let a=ai.getValidEnumValues(this._def.values),i=this._getOrReturnCtx(e);if(i.parsedType!==n.string&&i.parsedType!==n.number){let e=ai.objectValues(a);return d(i,{expected:ai.joinValues(e),received:i.parsedType,code:o.invalid_type}),f}if(_(this,ao,"f")||w(this,ao,new Set(ai.getValidEnumValues(this._def.values)),"f"),!_(this,ao,"f").has(e.data)){let e=ai.objectValues(a);return d(i,{received:i.data,code:o.invalid_enum_value,options:e}),f}return x(e.data)}get enum(){return this._def.values}}ao=new WeakMap,eb.create=(e,a)=>new eb({values:e,typeName:ar.ZodNativeEnum,...E(a)});class eg extends S{unwrap(){return this._def.type}_parse(e){let{ctx:a}=this._processInputParams(e);return a.parsedType!==n.promise&&!1===a.common.async?(d(a,{code:o.invalid_type,expected:n.promise,received:a.parsedType}),f):x((a.parsedType===n.promise?a.data:Promise.resolve(a.data)).then(e=>this._def.type.parseAsync(e,{path:a.path,errorMap:a.common.contextualErrorMap})))}}eg.create=(e,a)=>new eg({type:e,typeName:ar.ZodPromise,...E(a)});class ey extends S{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===ar.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){let{status:a,ctx:i}=this._processInputParams(e),t=this._def.effect||null,n={addIssue:e=>{d(i,e),e.fatal?a.abort():a.dirty()},get path(){return i.path}};if(n.addIssue=n.addIssue.bind(n),"preprocess"===t.type){let e=t.transform(i.data,n);if(i.common.async)return Promise.resolve(e).then(async e=>{if("aborted"===a.value)return f;let t=await this._def.schema._parseAsync({data:e,path:i.path,parent:i});return"aborted"===t.status?f:"dirty"===t.status||"dirty"===a.value?h(t.value):t});{if("aborted"===a.value)return f;let t=this._def.schema._parseSync({data:e,path:i.path,parent:i});return"aborted"===t.status?f:"dirty"===t.status||"dirty"===a.value?h(t.value):t}}if("refinement"===t.type){let e=e=>{let a=t.refinement(e,n);if(i.common.async)return Promise.resolve(a);if(a instanceof Promise)throw Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return e};if(!1!==i.common.async)return this._def.schema._parseAsync({data:i.data,path:i.path,parent:i}).then(i=>"aborted"===i.status?f:("dirty"===i.status&&a.dirty(),e(i.value).then(()=>({status:a.value,value:i.value}))));{let t=this._def.schema._parseSync({data:i.data,path:i.path,parent:i});return"aborted"===t.status?f:("dirty"===t.status&&a.dirty(),e(t.value),{status:a.value,value:t.value})}}if("transform"===t.type){if(!1!==i.common.async)return this._def.schema._parseAsync({data:i.data,path:i.path,parent:i}).then(e=>g(e)?Promise.resolve(t.transform(e.value,n)).then(e=>({status:a.value,value:e})):e);{let e=this._def.schema._parseSync({data:i.data,path:i.path,parent:i});if(!g(e))return e;let s=t.transform(e.value,n);if(s instanceof Promise)throw Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:a.value,value:s}}}ai.assertNever(t)}}ey.create=(e,a,i)=>new ey({schema:e,typeName:ar.ZodEffects,effect:a,...E(i)}),ey.createWithPreprocess=(e,a,i)=>new ey({schema:a,effect:{type:"preprocess",transform:e},typeName:ar.ZodEffects,...E(i)});class e_ extends S{_parse(e){return this._getType(e)===n.undefined?x(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}e_.create=(e,a)=>new e_({innerType:e,typeName:ar.ZodOptional,...E(a)});class ew extends S{_parse(e){return this._getType(e)===n.null?x(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}}ew.create=(e,a)=>new ew({innerType:e,typeName:ar.ZodNullable,...E(a)});class ek extends S{_parse(e){let{ctx:a}=this._processInputParams(e),i=a.data;return a.parsedType===n.undefined&&(i=this._def.defaultValue()),this._def.innerType._parse({data:i,path:a.path,parent:a})}removeDefault(){return this._def.innerType}}ek.create=(e,a)=>new ek({innerType:e,typeName:ar.ZodDefault,defaultValue:"function"==typeof a.default?a.default:()=>a.default,...E(a)});class ej extends S{_parse(e){let{ctx:a}=this._processInputParams(e),i={...a,common:{...a.common,issues:[]}},t=this._def.innerType._parse({data:i.data,path:i.path,parent:{...i}});return y(t)?t.then(e=>({status:"valid",value:"valid"===e.status?e.value:this._def.catchValue({get error(){return new r(i.common.issues)},input:i.data})})):{status:"valid",value:"valid"===t.status?t.value:this._def.catchValue({get error(){return new r(i.common.issues)},input:i.data})}}removeCatch(){return this._def.innerType}}ej.create=(e,a)=>new ej({innerType:e,typeName:ar.ZodCatch,catchValue:"function"==typeof a.catch?a.catch:()=>a.catch,...E(a)});class eE extends S{_parse(e){if(this._getType(e)!==n.nan){let a=this._getOrReturnCtx(e);return d(a,{code:o.invalid_type,expected:n.nan,received:a.parsedType}),f}return{status:"valid",value:e.data}}}eE.create=e=>new eE({typeName:ar.ZodNaN,...E(e)});let eS=Symbol("zod_brand");class eT extends S{_parse(e){let{ctx:a}=this._processInputParams(e),i=a.data;return this._def.type._parse({data:i,path:a.path,parent:a})}unwrap(){return this._def.type}}class eO extends S{_parse(e){let{status:a,ctx:i}=this._processInputParams(e);if(i.common.async)return(async()=>{let e=await this._def.in._parseAsync({data:i.data,path:i.path,parent:i});return"aborted"===e.status?f:"dirty"===e.status?(a.dirty(),h(e.value)):this._def.out._parseAsync({data:e.value,path:i.path,parent:i})})();{let e=this._def.in._parseSync({data:i.data,path:i.path,parent:i});return"aborted"===e.status?f:"dirty"===e.status?(a.dirty(),{status:"dirty",value:e.value}):this._def.out._parseSync({data:e.value,path:i.path,parent:i})}}static create(e,a){return new eO({in:e,out:a,typeName:ar.ZodPipeline})}}class eR extends S{_parse(e){let a=this._def.innerType._parse(e),i=e=>(g(e)&&(e.value=Object.freeze(e.value)),e);return y(a)?a.then(e=>i(e)):i(a)}unwrap(){return this._def.innerType}}function eA(e,a={},i){return e?Q.create().superRefine((t,n)=>{var s,o;if(!e(t)){let e="function"==typeof a?a(t):"string"==typeof a?{message:a}:a,r=null===(o=null!==(s=e.fatal)&&void 0!==s?s:i)||void 0===o||o;n.addIssue({code:"custom",..."string"==typeof e?{message:e}:e,fatal:r})}}):Q.create()}eR.create=(e,a)=>new eR({innerType:e,typeName:ar.ZodReadonly,...E(a)});let eC={object:en.lazycreate};!function(e){e.ZodString="ZodString",e.ZodNumber="ZodNumber",e.ZodNaN="ZodNaN",e.ZodBigInt="ZodBigInt",e.ZodBoolean="ZodBoolean",e.ZodDate="ZodDate",e.ZodSymbol="ZodSymbol",e.ZodUndefined="ZodUndefined",e.ZodNull="ZodNull",e.ZodAny="ZodAny",e.ZodUnknown="ZodUnknown",e.ZodNever="ZodNever",e.ZodVoid="ZodVoid",e.ZodArray="ZodArray",e.ZodObject="ZodObject",e.ZodUnion="ZodUnion",e.ZodDiscriminatedUnion="ZodDiscriminatedUnion",e.ZodIntersection="ZodIntersection",e.ZodTuple="ZodTuple",e.ZodRecord="ZodRecord",e.ZodMap="ZodMap",e.ZodSet="ZodSet",e.ZodFunction="ZodFunction",e.ZodLazy="ZodLazy",e.ZodLiteral="ZodLiteral",e.ZodEnum="ZodEnum",e.ZodEffects="ZodEffects",e.ZodNativeEnum="ZodNativeEnum",e.ZodOptional="ZodOptional",e.ZodNullable="ZodNullable",e.ZodDefault="ZodDefault",e.ZodCatch="ZodCatch",e.ZodPromise="ZodPromise",e.ZodBranded="ZodBranded",e.ZodPipeline="ZodPipeline",e.ZodReadonly="ZodReadonly"}(ar||(ar={}));let eN=$.create,ez=H.create,eL=eE.create,eF=K.create,eD=W.create,eP=J.create,eU=G.create,eB=Y.create,eq=X.create,eZ=Q.create,eI=ee.create,eV=ea.create,eM=ei.create,e$=et.create,eH=en.create,eK=en.strictCreate,eW=es.create,eJ=er.create,eG=ec.create,eY=ep.create,eX=el.create,eQ=eu.create,e0=ed.create,e1=em.create,e2=ef.create,e4=eh.create,e3=ev.create,e9=eb.create,e7=eg.create,e6=ey.create,e5=e_.create,e8=ew.create,ae=ey.createWithPreprocess,aa=eO.create;var ai,at,an,as,ao,ar,ac=Object.freeze({__proto__:null,defaultErrorMap:c,setErrorMap:function(e){p=e},getErrorMap:l,makeIssue:u,EMPTY_PATH:[],addIssueToContext:d,ParseStatus:m,INVALID:f,DIRTY:h,OK:x,isAborted:v,isDirty:b,isValid:g,isAsync:y,get util(){return ai},get objectUtil(){return at},ZodParsedType:n,getParsedType:s,ZodType:S,datetimeRegex:M,ZodString:$,ZodNumber:H,ZodBigInt:K,ZodBoolean:W,ZodDate:J,ZodSymbol:G,ZodUndefined:Y,ZodNull:X,ZodAny:Q,ZodUnknown:ee,ZodNever:ea,ZodVoid:ei,ZodArray:et,ZodObject:en,ZodUnion:es,ZodDiscriminatedUnion:er,ZodIntersection:ec,ZodTuple:ep,ZodRecord:el,ZodMap:eu,ZodSet:ed,ZodFunction:em,ZodLazy:ef,ZodLiteral:eh,ZodEnum:ev,ZodNativeEnum:eb,ZodPromise:eg,ZodEffects:ey,ZodTransformer:ey,ZodOptional:e_,ZodNullable:ew,ZodDefault:ek,ZodCatch:ej,ZodNaN:eE,BRAND:eS,ZodBranded:eT,ZodPipeline:eO,ZodReadonly:eR,custom:eA,Schema:S,ZodSchema:S,late:eC,get ZodFirstPartyTypeKind(){return ar},coerce:{string:e=>$.create({...e,coerce:!0}),number:e=>H.create({...e,coerce:!0}),boolean:e=>W.create({...e,coerce:!0}),bigint:e=>K.create({...e,coerce:!0}),date:e=>J.create({...e,coerce:!0})},any:eZ,array:e$,bigint:eF,boolean:eD,date:eP,discriminatedUnion:eJ,effect:e6,enum:e3,function:e1,instanceof:(e,a={message:`Input not instance of ${e.name}`})=>eA(a=>a instanceof e,a),intersection:eG,lazy:e2,literal:e4,map:eQ,nan:eL,nativeEnum:e9,never:eV,null:eq,nullable:e8,number:ez,object:eH,oboolean:()=>eD().optional(),onumber:()=>ez().optional(),optional:e5,ostring:()=>eN().optional(),pipeline:aa,preprocess:ae,promise:e7,record:eX,set:e0,strictObject:eK,string:eN,symbol:eU,transformer:e6,tuple:eY,undefined:eB,union:eW,unknown:eI,void:eM,NEVER:f,ZodIssueCode:o,quotelessJson:e=>JSON.stringify(e,null,2).replace(/"([^"]+)":/g,"$1:"),ZodError:r})},40572:e=>{"use strict";e.exports=JSON.parse('{"application/1d-interleaved-parityfec":{"source":"iana"},"application/3gpdash-qoe-report+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/3gpp-ims+xml":{"source":"iana","compressible":true},"application/3gpphal+json":{"source":"iana","compressible":true},"application/3gpphalforms+json":{"source":"iana","compressible":true},"application/a2l":{"source":"iana"},"application/ace+cbor":{"source":"iana"},"application/activemessage":{"source":"iana"},"application/activity+json":{"source":"iana","compressible":true},"application/alto-costmap+json":{"source":"iana","compressible":true},"application/alto-costmapfilter+json":{"source":"iana","compressible":true},"application/alto-directory+json":{"source":"iana","compressible":true},"application/alto-endpointcost+json":{"source":"iana","compressible":true},"application/alto-endpointcostparams+json":{"source":"iana","compressible":true},"application/alto-endpointprop+json":{"source":"iana","compressible":true},"application/alto-endpointpropparams+json":{"source":"iana","compressible":true},"application/alto-error+json":{"source":"iana","compressible":true},"application/alto-networkmap+json":{"source":"iana","compressible":true},"application/alto-networkmapfilter+json":{"source":"iana","compressible":true},"application/alto-updatestreamcontrol+json":{"source":"iana","compressible":true},"application/alto-updatestreamparams+json":{"source":"iana","compressible":true},"application/aml":{"source":"iana"},"application/andrew-inset":{"source":"iana","extensions":["ez"]},"application/applefile":{"source":"iana"},"application/applixware":{"source":"apache","extensions":["aw"]},"application/at+jwt":{"source":"iana"},"application/atf":{"source":"iana"},"application/atfx":{"source":"iana"},"application/atom+xml":{"source":"iana","compressible":true,"extensions":["atom"]},"application/atomcat+xml":{"source":"iana","compressible":true,"extensions":["atomcat"]},"application/atomdeleted+xml":{"source":"iana","compressible":true,"extensions":["atomdeleted"]},"application/atomicmail":{"source":"iana"},"application/atomsvc+xml":{"source":"iana","compressible":true,"extensions":["atomsvc"]},"application/atsc-dwd+xml":{"source":"iana","compressible":true,"extensions":["dwd"]},"application/atsc-dynamic-event-message":{"source":"iana"},"application/atsc-held+xml":{"source":"iana","compressible":true,"extensions":["held"]},"application/atsc-rdt+json":{"source":"iana","compressible":true},"application/atsc-rsat+xml":{"source":"iana","compressible":true,"extensions":["rsat"]},"application/atxml":{"source":"iana"},"application/auth-policy+xml":{"source":"iana","compressible":true},"application/bacnet-xdd+zip":{"source":"iana","compressible":false},"application/batch-smtp":{"source":"iana"},"application/bdoc":{"compressible":false,"extensions":["bdoc"]},"application/beep+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/calendar+json":{"source":"iana","compressible":true},"application/calendar+xml":{"source":"iana","compressible":true,"extensions":["xcs"]},"application/call-completion":{"source":"iana"},"application/cals-1840":{"source":"iana"},"application/captive+json":{"source":"iana","compressible":true},"application/cbor":{"source":"iana"},"application/cbor-seq":{"source":"iana"},"application/cccex":{"source":"iana"},"application/ccmp+xml":{"source":"iana","compressible":true},"application/ccxml+xml":{"source":"iana","compressible":true,"extensions":["ccxml"]},"application/cdfx+xml":{"source":"iana","compressible":true,"extensions":["cdfx"]},"application/cdmi-capability":{"source":"iana","extensions":["cdmia"]},"application/cdmi-container":{"source":"iana","extensions":["cdmic"]},"application/cdmi-domain":{"source":"iana","extensions":["cdmid"]},"application/cdmi-object":{"source":"iana","extensions":["cdmio"]},"application/cdmi-queue":{"source":"iana","extensions":["cdmiq"]},"application/cdni":{"source":"iana"},"application/cea":{"source":"iana"},"application/cea-2018+xml":{"source":"iana","compressible":true},"application/cellml+xml":{"source":"iana","compressible":true},"application/cfw":{"source":"iana"},"application/city+json":{"source":"iana","compressible":true},"application/clr":{"source":"iana"},"application/clue+xml":{"source":"iana","compressible":true},"application/clue_info+xml":{"source":"iana","compressible":true},"application/cms":{"source":"iana"},"application/cnrp+xml":{"source":"iana","compressible":true},"application/coap-group+json":{"source":"iana","compressible":true},"application/coap-payload":{"source":"iana"},"application/commonground":{"source":"iana"},"application/conference-info+xml":{"source":"iana","compressible":true},"application/cose":{"source":"iana"},"application/cose-key":{"source":"iana"},"application/cose-key-set":{"source":"iana"},"application/cpl+xml":{"source":"iana","compressible":true,"extensions":["cpl"]},"application/csrattrs":{"source":"iana"},"application/csta+xml":{"source":"iana","compressible":true},"application/cstadata+xml":{"source":"iana","compressible":true},"application/csvm+json":{"source":"iana","compressible":true},"application/cu-seeme":{"source":"apache","extensions":["cu"]},"application/cwt":{"source":"iana"},"application/cybercash":{"source":"iana"},"application/dart":{"compressible":true},"application/dash+xml":{"source":"iana","compressible":true,"extensions":["mpd"]},"application/dash-patch+xml":{"source":"iana","compressible":true,"extensions":["mpp"]},"application/dashdelta":{"source":"iana"},"application/davmount+xml":{"source":"iana","compressible":true,"extensions":["davmount"]},"application/dca-rft":{"source":"iana"},"application/dcd":{"source":"iana"},"application/dec-dx":{"source":"iana"},"application/dialog-info+xml":{"source":"iana","compressible":true},"application/dicom":{"source":"iana"},"application/dicom+json":{"source":"iana","compressible":true},"application/dicom+xml":{"source":"iana","compressible":true},"application/dii":{"source":"iana"},"application/dit":{"source":"iana"},"application/dns":{"source":"iana"},"application/dns+json":{"source":"iana","compressible":true},"application/dns-message":{"source":"iana"},"application/docbook+xml":{"source":"apache","compressible":true,"extensions":["dbk"]},"application/dots+cbor":{"source":"iana"},"application/dskpp+xml":{"source":"iana","compressible":true},"application/dssc+der":{"source":"iana","extensions":["dssc"]},"application/dssc+xml":{"source":"iana","compressible":true,"extensions":["xdssc"]},"application/dvcs":{"source":"iana"},"application/ecmascript":{"source":"iana","compressible":true,"extensions":["es","ecma"]},"application/edi-consent":{"source":"iana"},"application/edi-x12":{"source":"iana","compressible":false},"application/edifact":{"source":"iana","compressible":false},"application/efi":{"source":"iana"},"application/elm+json":{"source":"iana","charset":"UTF-8","compressible":true},"application/elm+xml":{"source":"iana","compressible":true},"application/emergencycalldata.cap+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/emergencycalldata.comment+xml":{"source":"iana","compressible":true},"application/emergencycalldata.control+xml":{"source":"iana","compressible":true},"application/emergencycalldata.deviceinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.ecall.msd":{"source":"iana"},"application/emergencycalldata.providerinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.serviceinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.subscriberinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.veds+xml":{"source":"iana","compressible":true},"application/emma+xml":{"source":"iana","compressible":true,"extensions":["emma"]},"application/emotionml+xml":{"source":"iana","compressible":true,"extensions":["emotionml"]},"application/encaprtp":{"source":"iana"},"application/epp+xml":{"source":"iana","compressible":true},"application/epub+zip":{"source":"iana","compressible":false,"extensions":["epub"]},"application/eshop":{"source":"iana"},"application/exi":{"source":"iana","extensions":["exi"]},"application/expect-ct-report+json":{"source":"iana","compressible":true},"application/express":{"source":"iana","extensions":["exp"]},"application/fastinfoset":{"source":"iana"},"application/fastsoap":{"source":"iana"},"application/fdt+xml":{"source":"iana","compressible":true,"extensions":["fdt"]},"application/fhir+json":{"source":"iana","charset":"UTF-8","compressible":true},"application/fhir+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/fido.trusted-apps+json":{"compressible":true},"application/fits":{"source":"iana"},"application/flexfec":{"source":"iana"},"application/font-sfnt":{"source":"iana"},"application/font-tdpfr":{"source":"iana","extensions":["pfr"]},"application/font-woff":{"source":"iana","compressible":false},"application/framework-attributes+xml":{"source":"iana","compressible":true},"application/geo+json":{"source":"iana","compressible":true,"extensions":["geojson"]},"application/geo+json-seq":{"source":"iana"},"application/geopackage+sqlite3":{"source":"iana"},"application/geoxacml+xml":{"source":"iana","compressible":true},"application/gltf-buffer":{"source":"iana"},"application/gml+xml":{"source":"iana","compressible":true,"extensions":["gml"]},"application/gpx+xml":{"source":"apache","compressible":true,"extensions":["gpx"]},"application/gxf":{"source":"apache","extensions":["gxf"]},"application/gzip":{"source":"iana","compressible":false,"extensions":["gz"]},"application/h224":{"source":"iana"},"application/held+xml":{"source":"iana","compressible":true},"application/hjson":{"extensions":["hjson"]},"application/http":{"source":"iana"},"application/hyperstudio":{"source":"iana","extensions":["stk"]},"application/ibe-key-request+xml":{"source":"iana","compressible":true},"application/ibe-pkg-reply+xml":{"source":"iana","compressible":true},"application/ibe-pp-data":{"source":"iana"},"application/iges":{"source":"iana"},"application/im-iscomposing+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/index":{"source":"iana"},"application/index.cmd":{"source":"iana"},"application/index.obj":{"source":"iana"},"application/index.response":{"source":"iana"},"application/index.vnd":{"source":"iana"},"application/inkml+xml":{"source":"iana","compressible":true,"extensions":["ink","inkml"]},"application/iotp":{"source":"iana"},"application/ipfix":{"source":"iana","extensions":["ipfix"]},"application/ipp":{"source":"iana"},"application/isup":{"source":"iana"},"application/its+xml":{"source":"iana","compressible":true,"extensions":["its"]},"application/java-archive":{"source":"apache","compressible":false,"extensions":["jar","war","ear"]},"application/java-serialized-object":{"source":"apache","compressible":false,"extensions":["ser"]},"application/java-vm":{"source":"apache","compressible":false,"extensions":["class"]},"application/javascript":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["js","mjs"]},"application/jf2feed+json":{"source":"iana","compressible":true},"application/jose":{"source":"iana"},"application/jose+json":{"source":"iana","compressible":true},"application/jrd+json":{"source":"iana","compressible":true},"application/jscalendar+json":{"source":"iana","compressible":true},"application/json":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["json","map"]},"application/json-patch+json":{"source":"iana","compressible":true},"application/json-seq":{"source":"iana"},"application/json5":{"extensions":["json5"]},"application/jsonml+json":{"source":"apache","compressible":true,"extensions":["jsonml"]},"application/jwk+json":{"source":"iana","compressible":true},"application/jwk-set+json":{"source":"iana","compressible":true},"application/jwt":{"source":"iana"},"application/kpml-request+xml":{"source":"iana","compressible":true},"application/kpml-response+xml":{"source":"iana","compressible":true},"application/ld+json":{"source":"iana","compressible":true,"extensions":["jsonld"]},"application/lgr+xml":{"source":"iana","compressible":true,"extensions":["lgr"]},"application/link-format":{"source":"iana"},"application/load-control+xml":{"source":"iana","compressible":true},"application/lost+xml":{"source":"iana","compressible":true,"extensions":["lostxml"]},"application/lostsync+xml":{"source":"iana","compressible":true},"application/lpf+zip":{"source":"iana","compressible":false},"application/lxf":{"source":"iana"},"application/mac-binhex40":{"source":"iana","extensions":["hqx"]},"application/mac-compactpro":{"source":"apache","extensions":["cpt"]},"application/macwriteii":{"source":"iana"},"application/mads+xml":{"source":"iana","compressible":true,"extensions":["mads"]},"application/manifest+json":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["webmanifest"]},"application/marc":{"source":"iana","extensions":["mrc"]},"application/marcxml+xml":{"source":"iana","compressible":true,"extensions":["mrcx"]},"application/mathematica":{"source":"iana","extensions":["ma","nb","mb"]},"application/mathml+xml":{"source":"iana","compressible":true,"extensions":["mathml"]},"application/mathml-content+xml":{"source":"iana","compressible":true},"application/mathml-presentation+xml":{"source":"iana","compressible":true},"application/mbms-associated-procedure-description+xml":{"source":"iana","compressible":true},"application/mbms-deregister+xml":{"source":"iana","compressible":true},"application/mbms-envelope+xml":{"source":"iana","compressible":true},"application/mbms-msk+xml":{"source":"iana","compressible":true},"application/mbms-msk-response+xml":{"source":"iana","compressible":true},"application/mbms-protection-description+xml":{"source":"iana","compressible":true},"application/mbms-reception-report+xml":{"source":"iana","compressible":true},"application/mbms-register+xml":{"source":"iana","compressible":true},"application/mbms-register-response+xml":{"source":"iana","compressible":true},"application/mbms-schedule+xml":{"source":"iana","compressible":true},"application/mbms-user-service-description+xml":{"source":"iana","compressible":true},"application/mbox":{"source":"iana","extensions":["mbox"]},"application/media-policy-dataset+xml":{"source":"iana","compressible":true,"extensions":["mpf"]},"application/media_control+xml":{"source":"iana","compressible":true},"application/mediaservercontrol+xml":{"source":"iana","compressible":true,"extensions":["mscml"]},"application/merge-patch+json":{"source":"iana","compressible":true},"application/metalink+xml":{"source":"apache","compressible":true,"extensions":["metalink"]},"application/metalink4+xml":{"source":"iana","compressible":true,"extensions":["meta4"]},"application/mets+xml":{"source":"iana","compressible":true,"extensions":["mets"]},"application/mf4":{"source":"iana"},"application/mikey":{"source":"iana"},"application/mipc":{"source":"iana"},"application/missing-blocks+cbor-seq":{"source":"iana"},"application/mmt-aei+xml":{"source":"iana","compressible":true,"extensions":["maei"]},"application/mmt-usd+xml":{"source":"iana","compressible":true,"extensions":["musd"]},"application/mods+xml":{"source":"iana","compressible":true,"extensions":["mods"]},"application/moss-keys":{"source":"iana"},"application/moss-signature":{"source":"iana"},"application/mosskey-data":{"source":"iana"},"application/mosskey-request":{"source":"iana"},"application/mp21":{"source":"iana","extensions":["m21","mp21"]},"application/mp4":{"source":"iana","extensions":["mp4s","m4p"]},"application/mpeg4-generic":{"source":"iana"},"application/mpeg4-iod":{"source":"iana"},"application/mpeg4-iod-xmt":{"source":"iana"},"application/mrb-consumer+xml":{"source":"iana","compressible":true},"application/mrb-publish+xml":{"source":"iana","compressible":true},"application/msc-ivr+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/msc-mixer+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/msword":{"source":"iana","compressible":false,"extensions":["doc","dot"]},"application/mud+json":{"source":"iana","compressible":true},"application/multipart-core":{"source":"iana"},"application/mxf":{"source":"iana","extensions":["mxf"]},"application/n-quads":{"source":"iana","extensions":["nq"]},"application/n-triples":{"source":"iana","extensions":["nt"]},"application/nasdata":{"source":"iana"},"application/news-checkgroups":{"source":"iana","charset":"US-ASCII"},"application/news-groupinfo":{"source":"iana","charset":"US-ASCII"},"application/news-transmission":{"source":"iana"},"application/nlsml+xml":{"source":"iana","compressible":true},"application/node":{"source":"iana","extensions":["cjs"]},"application/nss":{"source":"iana"},"application/oauth-authz-req+jwt":{"source":"iana"},"application/oblivious-dns-message":{"source":"iana"},"application/ocsp-request":{"source":"iana"},"application/ocsp-response":{"source":"iana"},"application/octet-stream":{"source":"iana","compressible":false,"extensions":["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"]},"application/oda":{"source":"iana","extensions":["oda"]},"application/odm+xml":{"source":"iana","compressible":true},"application/odx":{"source":"iana"},"application/oebps-package+xml":{"source":"iana","compressible":true,"extensions":["opf"]},"application/ogg":{"source":"iana","compressible":false,"extensions":["ogx"]},"application/omdoc+xml":{"source":"apache","compressible":true,"extensions":["omdoc"]},"application/onenote":{"source":"apache","extensions":["onetoc","onetoc2","onetmp","onepkg"]},"application/opc-nodeset+xml":{"source":"iana","compressible":true},"application/oscore":{"source":"iana"},"application/oxps":{"source":"iana","extensions":["oxps"]},"application/p21":{"source":"iana"},"application/p21+zip":{"source":"iana","compressible":false},"application/p2p-overlay+xml":{"source":"iana","compressible":true,"extensions":["relo"]},"application/parityfec":{"source":"iana"},"application/passport":{"source":"iana"},"application/patch-ops-error+xml":{"source":"iana","compressible":true,"extensions":["xer"]},"application/pdf":{"source":"iana","compressible":false,"extensions":["pdf"]},"application/pdx":{"source":"iana"},"application/pem-certificate-chain":{"source":"iana"},"application/pgp-encrypted":{"source":"iana","compressible":false,"extensions":["pgp"]},"application/pgp-keys":{"source":"iana","extensions":["asc"]},"application/pgp-signature":{"source":"iana","extensions":["asc","sig"]},"application/pics-rules":{"source":"apache","extensions":["prf"]},"application/pidf+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/pidf-diff+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/pkcs10":{"source":"iana","extensions":["p10"]},"application/pkcs12":{"source":"iana"},"application/pkcs7-mime":{"source":"iana","extensions":["p7m","p7c"]},"application/pkcs7-signature":{"source":"iana","extensions":["p7s"]},"application/pkcs8":{"source":"iana","extensions":["p8"]},"application/pkcs8-encrypted":{"source":"iana"},"application/pkix-attr-cert":{"source":"iana","extensions":["ac"]},"application/pkix-cert":{"source":"iana","extensions":["cer"]},"application/pkix-crl":{"source":"iana","extensions":["crl"]},"application/pkix-pkipath":{"source":"iana","extensions":["pkipath"]},"application/pkixcmp":{"source":"iana","extensions":["pki"]},"application/pls+xml":{"source":"iana","compressible":true,"extensions":["pls"]},"application/poc-settings+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/postscript":{"source":"iana","compressible":true,"extensions":["ai","eps","ps"]},"application/ppsp-tracker+json":{"source":"iana","compressible":true},"application/problem+json":{"source":"iana","compressible":true},"application/problem+xml":{"source":"iana","compressible":true},"application/provenance+xml":{"source":"iana","compressible":true,"extensions":["provx"]},"application/prs.alvestrand.titrax-sheet":{"source":"iana"},"application/prs.cww":{"source":"iana","extensions":["cww"]},"application/prs.cyn":{"source":"iana","charset":"7-BIT"},"application/prs.hpub+zip":{"source":"iana","compressible":false},"application/prs.nprend":{"source":"iana"},"application/prs.plucker":{"source":"iana"},"application/prs.rdf-xml-crypt":{"source":"iana"},"application/prs.xsf+xml":{"source":"iana","compressible":true},"application/pskc+xml":{"source":"iana","compressible":true,"extensions":["pskcxml"]},"application/pvd+json":{"source":"iana","compressible":true},"application/qsig":{"source":"iana"},"application/raml+yaml":{"compressible":true,"extensions":["raml"]},"application/raptorfec":{"source":"iana"},"application/rdap+json":{"source":"iana","compressible":true},"application/rdf+xml":{"source":"iana","compressible":true,"extensions":["rdf","owl"]},"application/reginfo+xml":{"source":"iana","compressible":true,"extensions":["rif"]},"application/relax-ng-compact-syntax":{"source":"iana","extensions":["rnc"]},"application/remote-printing":{"source":"iana"},"application/reputon+json":{"source":"iana","compressible":true},"application/resource-lists+xml":{"source":"iana","compressible":true,"extensions":["rl"]},"application/resource-lists-diff+xml":{"source":"iana","compressible":true,"extensions":["rld"]},"application/rfc+xml":{"source":"iana","compressible":true},"application/riscos":{"source":"iana"},"application/rlmi+xml":{"source":"iana","compressible":true},"application/rls-services+xml":{"source":"iana","compressible":true,"extensions":["rs"]},"application/route-apd+xml":{"source":"iana","compressible":true,"extensions":["rapd"]},"application/route-s-tsid+xml":{"source":"iana","compressible":true,"extensions":["sls"]},"application/route-usd+xml":{"source":"iana","compressible":true,"extensions":["rusd"]},"application/rpki-ghostbusters":{"source":"iana","extensions":["gbr"]},"application/rpki-manifest":{"source":"iana","extensions":["mft"]},"application/rpki-publication":{"source":"iana"},"application/rpki-roa":{"source":"iana","extensions":["roa"]},"application/rpki-updown":{"source":"iana"},"application/rsd+xml":{"source":"apache","compressible":true,"extensions":["rsd"]},"application/rss+xml":{"source":"apache","compressible":true,"extensions":["rss"]},"application/rtf":{"source":"iana","compressible":true,"extensions":["rtf"]},"application/rtploopback":{"source":"iana"},"application/rtx":{"source":"iana"},"application/samlassertion+xml":{"source":"iana","compressible":true},"application/samlmetadata+xml":{"source":"iana","compressible":true},"application/sarif+json":{"source":"iana","compressible":true},"application/sarif-external-properties+json":{"source":"iana","compressible":true},"application/sbe":{"source":"iana"},"application/sbml+xml":{"source":"iana","compressible":true,"extensions":["sbml"]},"application/scaip+xml":{"source":"iana","compressible":true},"application/scim+json":{"source":"iana","compressible":true},"application/scvp-cv-request":{"source":"iana","extensions":["scq"]},"application/scvp-cv-response":{"source":"iana","extensions":["scs"]},"application/scvp-vp-request":{"source":"iana","extensions":["spq"]},"application/scvp-vp-response":{"source":"iana","extensions":["spp"]},"application/sdp":{"source":"iana","extensions":["sdp"]},"application/secevent+jwt":{"source":"iana"},"application/senml+cbor":{"source":"iana"},"application/senml+json":{"source":"iana","compressible":true},"application/senml+xml":{"source":"iana","compressible":true,"extensions":["senmlx"]},"application/senml-etch+cbor":{"source":"iana"},"application/senml-etch+json":{"source":"iana","compressible":true},"application/senml-exi":{"source":"iana"},"application/sensml+cbor":{"source":"iana"},"application/sensml+json":{"source":"iana","compressible":true},"application/sensml+xml":{"source":"iana","compressible":true,"extensions":["sensmlx"]},"application/sensml-exi":{"source":"iana"},"application/sep+xml":{"source":"iana","compressible":true},"application/sep-exi":{"source":"iana"},"application/session-info":{"source":"iana"},"application/set-payment":{"source":"iana"},"application/set-payment-initiation":{"source":"iana","extensions":["setpay"]},"application/set-registration":{"source":"iana"},"application/set-registration-initiation":{"source":"iana","extensions":["setreg"]},"application/sgml":{"source":"iana"},"application/sgml-open-catalog":{"source":"iana"},"application/shf+xml":{"source":"iana","compressible":true,"extensions":["shf"]},"application/sieve":{"source":"iana","extensions":["siv","sieve"]},"application/simple-filter+xml":{"source":"iana","compressible":true},"application/simple-message-summary":{"source":"iana"},"application/simplesymbolcontainer":{"source":"iana"},"application/sipc":{"source":"iana"},"application/slate":{"source":"iana"},"application/smil":{"source":"iana"},"application/smil+xml":{"source":"iana","compressible":true,"extensions":["smi","smil"]},"application/smpte336m":{"source":"iana"},"application/soap+fastinfoset":{"source":"iana"},"application/soap+xml":{"source":"iana","compressible":true},"application/sparql-query":{"source":"iana","extensions":["rq"]},"application/sparql-results+xml":{"source":"iana","compressible":true,"extensions":["srx"]},"application/spdx+json":{"source":"iana","compressible":true},"application/spirits-event+xml":{"source":"iana","compressible":true},"application/sql":{"source":"iana"},"application/srgs":{"source":"iana","extensions":["gram"]},"application/srgs+xml":{"source":"iana","compressible":true,"extensions":["grxml"]},"application/sru+xml":{"source":"iana","compressible":true,"extensions":["sru"]},"application/ssdl+xml":{"source":"apache","compressible":true,"extensions":["ssdl"]},"application/ssml+xml":{"source":"iana","compressible":true,"extensions":["ssml"]},"application/stix+json":{"source":"iana","compressible":true},"application/swid+xml":{"source":"iana","compressible":true,"extensions":["swidtag"]},"application/tamp-apex-update":{"source":"iana"},"application/tamp-apex-update-confirm":{"source":"iana"},"application/tamp-community-update":{"source":"iana"},"application/tamp-community-update-confirm":{"source":"iana"},"application/tamp-error":{"source":"iana"},"application/tamp-sequence-adjust":{"source":"iana"},"application/tamp-sequence-adjust-confirm":{"source":"iana"},"application/tamp-status-query":{"source":"iana"},"application/tamp-status-response":{"source":"iana"},"application/tamp-update":{"source":"iana"},"application/tamp-update-confirm":{"source":"iana"},"application/tar":{"compressible":true},"application/taxii+json":{"source":"iana","compressible":true},"application/td+json":{"source":"iana","compressible":true},"application/tei+xml":{"source":"iana","compressible":true,"extensions":["tei","teicorpus"]},"application/tetra_isi":{"source":"iana"},"application/thraud+xml":{"source":"iana","compressible":true,"extensions":["tfi"]},"application/timestamp-query":{"source":"iana"},"application/timestamp-reply":{"source":"iana"},"application/timestamped-data":{"source":"iana","extensions":["tsd"]},"application/tlsrpt+gzip":{"source":"iana"},"application/tlsrpt+json":{"source":"iana","compressible":true},"application/tnauthlist":{"source":"iana"},"application/token-introspection+jwt":{"source":"iana"},"application/toml":{"compressible":true,"extensions":["toml"]},"application/trickle-ice-sdpfrag":{"source":"iana"},"application/trig":{"source":"iana","extensions":["trig"]},"application/ttml+xml":{"source":"iana","compressible":true,"extensions":["ttml"]},"application/tve-trigger":{"source":"iana"},"application/tzif":{"source":"iana"},"application/tzif-leap":{"source":"iana"},"application/ubjson":{"compressible":false,"extensions":["ubj"]},"application/ulpfec":{"source":"iana"},"application/urc-grpsheet+xml":{"source":"iana","compressible":true},"application/urc-ressheet+xml":{"source":"iana","compressible":true,"extensions":["rsheet"]},"application/urc-targetdesc+xml":{"source":"iana","compressible":true,"extensions":["td"]},"application/urc-uisocketdesc+xml":{"source":"iana","compressible":true},"application/vcard+json":{"source":"iana","compressible":true},"application/vcard+xml":{"source":"iana","compressible":true},"application/vemmi":{"source":"iana"},"application/vividence.scriptfile":{"source":"apache"},"application/vnd.1000minds.decision-model+xml":{"source":"iana","compressible":true,"extensions":["1km"]},"application/vnd.3gpp-prose+xml":{"source":"iana","compressible":true},"application/vnd.3gpp-prose-pc3ch+xml":{"source":"iana","compressible":true},"application/vnd.3gpp-v2x-local-service-information":{"source":"iana"},"application/vnd.3gpp.5gnas":{"source":"iana"},"application/vnd.3gpp.access-transfer-events+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.bsf+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.gmop+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.gtpc":{"source":"iana"},"application/vnd.3gpp.interworking-data":{"source":"iana"},"application/vnd.3gpp.lpp":{"source":"iana"},"application/vnd.3gpp.mc-signalling-ear":{"source":"iana"},"application/vnd.3gpp.mcdata-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-payload":{"source":"iana"},"application/vnd.3gpp.mcdata-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-signalling":{"source":"iana"},"application/vnd.3gpp.mcdata-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-floor-request+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-location-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-mbms-usage-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-signed+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-ue-init-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-affiliation-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-location-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-mbms-usage-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-transmission-request+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mid-call+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.ngap":{"source":"iana"},"application/vnd.3gpp.pfcp":{"source":"iana"},"application/vnd.3gpp.pic-bw-large":{"source":"iana","extensions":["plb"]},"application/vnd.3gpp.pic-bw-small":{"source":"iana","extensions":["psb"]},"application/vnd.3gpp.pic-bw-var":{"source":"iana","extensions":["pvb"]},"application/vnd.3gpp.s1ap":{"source":"iana"},"application/vnd.3gpp.sms":{"source":"iana"},"application/vnd.3gpp.sms+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.srvcc-ext+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.srvcc-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.state-and-event-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.ussd+xml":{"source":"iana","compressible":true},"application/vnd.3gpp2.bcmcsinfo+xml":{"source":"iana","compressible":true},"application/vnd.3gpp2.sms":{"source":"iana"},"application/vnd.3gpp2.tcap":{"source":"iana","extensions":["tcap"]},"application/vnd.3lightssoftware.imagescal":{"source":"iana"},"application/vnd.3m.post-it-notes":{"source":"iana","extensions":["pwn"]},"application/vnd.accpac.simply.aso":{"source":"iana","extensions":["aso"]},"application/vnd.accpac.simply.imp":{"source":"iana","extensions":["imp"]},"application/vnd.acucobol":{"source":"iana","extensions":["acu"]},"application/vnd.acucorp":{"source":"iana","extensions":["atc","acutc"]},"application/vnd.adobe.air-application-installer-package+zip":{"source":"apache","compressible":false,"extensions":["air"]},"application/vnd.adobe.flash.movie":{"source":"iana"},"application/vnd.adobe.formscentral.fcdt":{"source":"iana","extensions":["fcdt"]},"application/vnd.adobe.fxp":{"source":"iana","extensions":["fxp","fxpl"]},"application/vnd.adobe.partial-upload":{"source":"iana"},"application/vnd.adobe.xdp+xml":{"source":"iana","compressible":true,"extensions":["xdp"]},"application/vnd.adobe.xfdf":{"source":"iana","extensions":["xfdf"]},"application/vnd.aether.imp":{"source":"iana"},"application/vnd.afpc.afplinedata":{"source":"iana"},"application/vnd.afpc.afplinedata-pagedef":{"source":"iana"},"application/vnd.afpc.cmoca-cmresource":{"source":"iana"},"application/vnd.afpc.foca-charset":{"source":"iana"},"application/vnd.afpc.foca-codedfont":{"source":"iana"},"application/vnd.afpc.foca-codepage":{"source":"iana"},"application/vnd.afpc.modca":{"source":"iana"},"application/vnd.afpc.modca-cmtable":{"source":"iana"},"application/vnd.afpc.modca-formdef":{"source":"iana"},"application/vnd.afpc.modca-mediummap":{"source":"iana"},"application/vnd.afpc.modca-objectcontainer":{"source":"iana"},"application/vnd.afpc.modca-overlay":{"source":"iana"},"application/vnd.afpc.modca-pagesegment":{"source":"iana"},"application/vnd.age":{"source":"iana","extensions":["age"]},"application/vnd.ah-barcode":{"source":"iana"},"application/vnd.ahead.space":{"source":"iana","extensions":["ahead"]},"application/vnd.airzip.filesecure.azf":{"source":"iana","extensions":["azf"]},"application/vnd.airzip.filesecure.azs":{"source":"iana","extensions":["azs"]},"application/vnd.amadeus+json":{"source":"iana","compressible":true},"application/vnd.amazon.ebook":{"source":"apache","extensions":["azw"]},"application/vnd.amazon.mobi8-ebook":{"source":"iana"},"application/vnd.americandynamics.acc":{"source":"iana","extensions":["acc"]},"application/vnd.amiga.ami":{"source":"iana","extensions":["ami"]},"application/vnd.amundsen.maze+xml":{"source":"iana","compressible":true},"application/vnd.android.ota":{"source":"iana"},"application/vnd.android.package-archive":{"source":"apache","compressible":false,"extensions":["apk"]},"application/vnd.anki":{"source":"iana"},"application/vnd.anser-web-certificate-issue-initiation":{"source":"iana","extensions":["cii"]},"application/vnd.anser-web-funds-transfer-initiation":{"source":"apache","extensions":["fti"]},"application/vnd.antix.game-component":{"source":"iana","extensions":["atx"]},"application/vnd.apache.arrow.file":{"source":"iana"},"application/vnd.apache.arrow.stream":{"source":"iana"},"application/vnd.apache.thrift.binary":{"source":"iana"},"application/vnd.apache.thrift.compact":{"source":"iana"},"application/vnd.apache.thrift.json":{"source":"iana"},"application/vnd.api+json":{"source":"iana","compressible":true},"application/vnd.aplextor.warrp+json":{"source":"iana","compressible":true},"application/vnd.apothekende.reservation+json":{"source":"iana","compressible":true},"application/vnd.apple.installer+xml":{"source":"iana","compressible":true,"extensions":["mpkg"]},"application/vnd.apple.keynote":{"source":"iana","extensions":["key"]},"application/vnd.apple.mpegurl":{"source":"iana","extensions":["m3u8"]},"application/vnd.apple.numbers":{"source":"iana","extensions":["numbers"]},"application/vnd.apple.pages":{"source":"iana","extensions":["pages"]},"application/vnd.apple.pkpass":{"compressible":false,"extensions":["pkpass"]},"application/vnd.arastra.swi":{"source":"iana"},"application/vnd.aristanetworks.swi":{"source":"iana","extensions":["swi"]},"application/vnd.artisan+json":{"source":"iana","compressible":true},"application/vnd.artsquare":{"source":"iana"},"application/vnd.astraea-software.iota":{"source":"iana","extensions":["iota"]},"application/vnd.audiograph":{"source":"iana","extensions":["aep"]},"application/vnd.autopackage":{"source":"iana"},"application/vnd.avalon+json":{"source":"iana","compressible":true},"application/vnd.avistar+xml":{"source":"iana","compressible":true},"application/vnd.balsamiq.bmml+xml":{"source":"iana","compressible":true,"extensions":["bmml"]},"application/vnd.balsamiq.bmpr":{"source":"iana"},"application/vnd.banana-accounting":{"source":"iana"},"application/vnd.bbf.usp.error":{"source":"iana"},"application/vnd.bbf.usp.msg":{"source":"iana"},"application/vnd.bbf.usp.msg+json":{"source":"iana","compressible":true},"application/vnd.bekitzur-stech+json":{"source":"iana","compressible":true},"application/vnd.bint.med-content":{"source":"iana"},"application/vnd.biopax.rdf+xml":{"source":"iana","compressible":true},"application/vnd.blink-idb-value-wrapper":{"source":"iana"},"application/vnd.blueice.multipass":{"source":"iana","extensions":["mpm"]},"application/vnd.bluetooth.ep.oob":{"source":"iana"},"application/vnd.bluetooth.le.oob":{"source":"iana"},"application/vnd.bmi":{"source":"iana","extensions":["bmi"]},"application/vnd.bpf":{"source":"iana"},"application/vnd.bpf3":{"source":"iana"},"application/vnd.businessobjects":{"source":"iana","extensions":["rep"]},"application/vnd.byu.uapi+json":{"source":"iana","compressible":true},"application/vnd.cab-jscript":{"source":"iana"},"application/vnd.canon-cpdl":{"source":"iana"},"application/vnd.canon-lips":{"source":"iana"},"application/vnd.capasystems-pg+json":{"source":"iana","compressible":true},"application/vnd.cendio.thinlinc.clientconf":{"source":"iana"},"application/vnd.century-systems.tcp_stream":{"source":"iana"},"application/vnd.chemdraw+xml":{"source":"iana","compressible":true,"extensions":["cdxml"]},"application/vnd.chess-pgn":{"source":"iana"},"application/vnd.chipnuts.karaoke-mmd":{"source":"iana","extensions":["mmd"]},"application/vnd.ciedi":{"source":"iana"},"application/vnd.cinderella":{"source":"iana","extensions":["cdy"]},"application/vnd.cirpack.isdn-ext":{"source":"iana"},"application/vnd.citationstyles.style+xml":{"source":"iana","compressible":true,"extensions":["csl"]},"application/vnd.claymore":{"source":"iana","extensions":["cla"]},"application/vnd.cloanto.rp9":{"source":"iana","extensions":["rp9"]},"application/vnd.clonk.c4group":{"source":"iana","extensions":["c4g","c4d","c4f","c4p","c4u"]},"application/vnd.cluetrust.cartomobile-config":{"source":"iana","extensions":["c11amc"]},"application/vnd.cluetrust.cartomobile-config-pkg":{"source":"iana","extensions":["c11amz"]},"application/vnd.coffeescript":{"source":"iana"},"application/vnd.collabio.xodocuments.document":{"source":"iana"},"application/vnd.collabio.xodocuments.document-template":{"source":"iana"},"application/vnd.collabio.xodocuments.presentation":{"source":"iana"},"application/vnd.collabio.xodocuments.presentation-template":{"source":"iana"},"application/vnd.collabio.xodocuments.spreadsheet":{"source":"iana"},"application/vnd.collabio.xodocuments.spreadsheet-template":{"source":"iana"},"application/vnd.collection+json":{"source":"iana","compressible":true},"application/vnd.collection.doc+json":{"source":"iana","compressible":true},"application/vnd.collection.next+json":{"source":"iana","compressible":true},"application/vnd.comicbook+zip":{"source":"iana","compressible":false},"application/vnd.comicbook-rar":{"source":"iana"},"application/vnd.commerce-battelle":{"source":"iana"},"application/vnd.commonspace":{"source":"iana","extensions":["csp"]},"application/vnd.contact.cmsg":{"source":"iana","extensions":["cdbcmsg"]},"application/vnd.coreos.ignition+json":{"source":"iana","compressible":true},"application/vnd.cosmocaller":{"source":"iana","extensions":["cmc"]},"application/vnd.crick.clicker":{"source":"iana","extensions":["clkx"]},"application/vnd.crick.clicker.keyboard":{"source":"iana","extensions":["clkk"]},"application/vnd.crick.clicker.palette":{"source":"iana","extensions":["clkp"]},"application/vnd.crick.clicker.template":{"source":"iana","extensions":["clkt"]},"application/vnd.crick.clicker.wordbank":{"source":"iana","extensions":["clkw"]},"application/vnd.criticaltools.wbs+xml":{"source":"iana","compressible":true,"extensions":["wbs"]},"application/vnd.cryptii.pipe+json":{"source":"iana","compressible":true},"application/vnd.crypto-shade-file":{"source":"iana"},"application/vnd.cryptomator.encrypted":{"source":"iana"},"application/vnd.cryptomator.vault":{"source":"iana"},"application/vnd.ctc-posml":{"source":"iana","extensions":["pml"]},"application/vnd.ctct.ws+xml":{"source":"iana","compressible":true},"application/vnd.cups-pdf":{"source":"iana"},"application/vnd.cups-postscript":{"source":"iana"},"application/vnd.cups-ppd":{"source":"iana","extensions":["ppd"]},"application/vnd.cups-raster":{"source":"iana"},"application/vnd.cups-raw":{"source":"iana"},"application/vnd.curl":{"source":"iana"},"application/vnd.curl.car":{"source":"apache","extensions":["car"]},"application/vnd.curl.pcurl":{"source":"apache","extensions":["pcurl"]},"application/vnd.cyan.dean.root+xml":{"source":"iana","compressible":true},"application/vnd.cybank":{"source":"iana"},"application/vnd.cyclonedx+json":{"source":"iana","compressible":true},"application/vnd.cyclonedx+xml":{"source":"iana","compressible":true},"application/vnd.d2l.coursepackage1p0+zip":{"source":"iana","compressible":false},"application/vnd.d3m-dataset":{"source":"iana"},"application/vnd.d3m-problem":{"source":"iana"},"application/vnd.dart":{"source":"iana","compressible":true,"extensions":["dart"]},"application/vnd.data-vision.rdz":{"source":"iana","extensions":["rdz"]},"application/vnd.datapackage+json":{"source":"iana","compressible":true},"application/vnd.dataresource+json":{"source":"iana","compressible":true},"application/vnd.dbf":{"source":"iana","extensions":["dbf"]},"application/vnd.debian.binary-package":{"source":"iana"},"application/vnd.dece.data":{"source":"iana","extensions":["uvf","uvvf","uvd","uvvd"]},"application/vnd.dece.ttml+xml":{"source":"iana","compressible":true,"extensions":["uvt","uvvt"]},"application/vnd.dece.unspecified":{"source":"iana","extensions":["uvx","uvvx"]},"application/vnd.dece.zip":{"source":"iana","extensions":["uvz","uvvz"]},"application/vnd.denovo.fcselayout-link":{"source":"iana","extensions":["fe_launch"]},"application/vnd.desmume.movie":{"source":"iana"},"application/vnd.dir-bi.plate-dl-nosuffix":{"source":"iana"},"application/vnd.dm.delegation+xml":{"source":"iana","compressible":true},"application/vnd.dna":{"source":"iana","extensions":["dna"]},"application/vnd.document+json":{"source":"iana","compressible":true},"application/vnd.dolby.mlp":{"source":"apache","extensions":["mlp"]},"application/vnd.dolby.mobile.1":{"source":"iana"},"application/vnd.dolby.mobile.2":{"source":"iana"},"application/vnd.doremir.scorecloud-binary-document":{"source":"iana"},"application/vnd.dpgraph":{"source":"iana","extensions":["dpg"]},"application/vnd.dreamfactory":{"source":"iana","extensions":["dfac"]},"application/vnd.drive+json":{"source":"iana","compressible":true},"application/vnd.ds-keypoint":{"source":"apache","extensions":["kpxx"]},"application/vnd.dtg.local":{"source":"iana"},"application/vnd.dtg.local.flash":{"source":"iana"},"application/vnd.dtg.local.html":{"source":"iana"},"application/vnd.dvb.ait":{"source":"iana","extensions":["ait"]},"application/vnd.dvb.dvbisl+xml":{"source":"iana","compressible":true},"application/vnd.dvb.dvbj":{"source":"iana"},"application/vnd.dvb.esgcontainer":{"source":"iana"},"application/vnd.dvb.ipdcdftnotifaccess":{"source":"iana"},"application/vnd.dvb.ipdcesgaccess":{"source":"iana"},"application/vnd.dvb.ipdcesgaccess2":{"source":"iana"},"application/vnd.dvb.ipdcesgpdd":{"source":"iana"},"application/vnd.dvb.ipdcroaming":{"source":"iana"},"application/vnd.dvb.iptv.alfec-base":{"source":"iana"},"application/vnd.dvb.iptv.alfec-enhancement":{"source":"iana"},"application/vnd.dvb.notif-aggregate-root+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-container+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-generic+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-msglist+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-registration-request+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-registration-response+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-init+xml":{"source":"iana","compressible":true},"application/vnd.dvb.pfr":{"source":"iana"},"application/vnd.dvb.service":{"source":"iana","extensions":["svc"]},"application/vnd.dxr":{"source":"iana"},"application/vnd.dynageo":{"source":"iana","extensions":["geo"]},"application/vnd.dzr":{"source":"iana"},"application/vnd.easykaraoke.cdgdownload":{"source":"iana"},"application/vnd.ecdis-update":{"source":"iana"},"application/vnd.ecip.rlp":{"source":"iana"},"application/vnd.eclipse.ditto+json":{"source":"iana","compressible":true},"application/vnd.ecowin.chart":{"source":"iana","extensions":["mag"]},"application/vnd.ecowin.filerequest":{"source":"iana"},"application/vnd.ecowin.fileupdate":{"source":"iana"},"application/vnd.ecowin.series":{"source":"iana"},"application/vnd.ecowin.seriesrequest":{"source":"iana"},"application/vnd.ecowin.seriesupdate":{"source":"iana"},"application/vnd.efi.img":{"source":"iana"},"application/vnd.efi.iso":{"source":"iana"},"application/vnd.emclient.accessrequest+xml":{"source":"iana","compressible":true},"application/vnd.enliven":{"source":"iana","extensions":["nml"]},"application/vnd.enphase.envoy":{"source":"iana"},"application/vnd.eprints.data+xml":{"source":"iana","compressible":true},"application/vnd.epson.esf":{"source":"iana","extensions":["esf"]},"application/vnd.epson.msf":{"source":"iana","extensions":["msf"]},"application/vnd.epson.quickanime":{"source":"iana","extensions":["qam"]},"application/vnd.epson.salt":{"source":"iana","extensions":["slt"]},"application/vnd.epson.ssf":{"source":"iana","extensions":["ssf"]},"application/vnd.ericsson.quickcall":{"source":"iana"},"application/vnd.espass-espass+zip":{"source":"iana","compressible":false},"application/vnd.eszigno3+xml":{"source":"iana","compressible":true,"extensions":["es3","et3"]},"application/vnd.etsi.aoc+xml":{"source":"iana","compressible":true},"application/vnd.etsi.asic-e+zip":{"source":"iana","compressible":false},"application/vnd.etsi.asic-s+zip":{"source":"iana","compressible":false},"application/vnd.etsi.cug+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvcommand+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvdiscovery+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvprofile+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-bc+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-cod+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-npvr+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvservice+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsync+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvueprofile+xml":{"source":"iana","compressible":true},"application/vnd.etsi.mcid+xml":{"source":"iana","compressible":true},"application/vnd.etsi.mheg5":{"source":"iana"},"application/vnd.etsi.overload-control-policy-dataset+xml":{"source":"iana","compressible":true},"application/vnd.etsi.pstn+xml":{"source":"iana","compressible":true},"application/vnd.etsi.sci+xml":{"source":"iana","compressible":true},"application/vnd.etsi.simservs+xml":{"source":"iana","compressible":true},"application/vnd.etsi.timestamp-token":{"source":"iana"},"application/vnd.etsi.tsl+xml":{"source":"iana","compressible":true},"application/vnd.etsi.tsl.der":{"source":"iana"},"application/vnd.eu.kasparian.car+json":{"source":"iana","compressible":true},"application/vnd.eudora.data":{"source":"iana"},"application/vnd.evolv.ecig.profile":{"source":"iana"},"application/vnd.evolv.ecig.settings":{"source":"iana"},"application/vnd.evolv.ecig.theme":{"source":"iana"},"application/vnd.exstream-empower+zip":{"source":"iana","compressible":false},"application/vnd.exstream-package":{"source":"iana"},"application/vnd.ezpix-album":{"source":"iana","extensions":["ez2"]},"application/vnd.ezpix-package":{"source":"iana","extensions":["ez3"]},"application/vnd.f-secure.mobile":{"source":"iana"},"application/vnd.familysearch.gedcom+zip":{"source":"iana","compressible":false},"application/vnd.fastcopy-disk-image":{"source":"iana"},"application/vnd.fdf":{"source":"iana","extensions":["fdf"]},"application/vnd.fdsn.mseed":{"source":"iana","extensions":["mseed"]},"application/vnd.fdsn.seed":{"source":"iana","extensions":["seed","dataless"]},"application/vnd.ffsns":{"source":"iana"},"application/vnd.ficlab.flb+zip":{"source":"iana","compressible":false},"application/vnd.filmit.zfc":{"source":"iana"},"application/vnd.fints":{"source":"iana"},"application/vnd.firemonkeys.cloudcell":{"source":"iana"},"application/vnd.flographit":{"source":"iana","extensions":["gph"]},"application/vnd.fluxtime.clip":{"source":"iana","extensions":["ftc"]},"application/vnd.font-fontforge-sfd":{"source":"iana"},"application/vnd.framemaker":{"source":"iana","extensions":["fm","frame","maker","book"]},"application/vnd.frogans.fnc":{"source":"iana","extensions":["fnc"]},"application/vnd.frogans.ltf":{"source":"iana","extensions":["ltf"]},"application/vnd.fsc.weblaunch":{"source":"iana","extensions":["fsc"]},"application/vnd.fujifilm.fb.docuworks":{"source":"iana"},"application/vnd.fujifilm.fb.docuworks.binder":{"source":"iana"},"application/vnd.fujifilm.fb.docuworks.container":{"source":"iana"},"application/vnd.fujifilm.fb.jfi+xml":{"source":"iana","compressible":true},"application/vnd.fujitsu.oasys":{"source":"iana","extensions":["oas"]},"application/vnd.fujitsu.oasys2":{"source":"iana","extensions":["oa2"]},"application/vnd.fujitsu.oasys3":{"source":"iana","extensions":["oa3"]},"application/vnd.fujitsu.oasysgp":{"source":"iana","extensions":["fg5"]},"application/vnd.fujitsu.oasysprs":{"source":"iana","extensions":["bh2"]},"application/vnd.fujixerox.art-ex":{"source":"iana"},"application/vnd.fujixerox.art4":{"source":"iana"},"application/vnd.fujixerox.ddd":{"source":"iana","extensions":["ddd"]},"application/vnd.fujixerox.docuworks":{"source":"iana","extensions":["xdw"]},"application/vnd.fujixerox.docuworks.binder":{"source":"iana","extensions":["xbd"]},"application/vnd.fujixerox.docuworks.container":{"source":"iana"},"application/vnd.fujixerox.hbpl":{"source":"iana"},"application/vnd.fut-misnet":{"source":"iana"},"application/vnd.futoin+cbor":{"source":"iana"},"application/vnd.futoin+json":{"source":"iana","compressible":true},"application/vnd.fuzzysheet":{"source":"iana","extensions":["fzs"]},"application/vnd.genomatix.tuxedo":{"source":"iana","extensions":["txd"]},"application/vnd.gentics.grd+json":{"source":"iana","compressible":true},"application/vnd.geo+json":{"source":"iana","compressible":true},"application/vnd.geocube+xml":{"source":"iana","compressible":true},"application/vnd.geogebra.file":{"source":"iana","extensions":["ggb"]},"application/vnd.geogebra.slides":{"source":"iana"},"application/vnd.geogebra.tool":{"source":"iana","extensions":["ggt"]},"application/vnd.geometry-explorer":{"source":"iana","extensions":["gex","gre"]},"application/vnd.geonext":{"source":"iana","extensions":["gxt"]},"application/vnd.geoplan":{"source":"iana","extensions":["g2w"]},"application/vnd.geospace":{"source":"iana","extensions":["g3w"]},"application/vnd.gerber":{"source":"iana"},"application/vnd.globalplatform.card-content-mgt":{"source":"iana"},"application/vnd.globalplatform.card-content-mgt-response":{"source":"iana"},"application/vnd.gmx":{"source":"iana","extensions":["gmx"]},"application/vnd.google-apps.document":{"compressible":false,"extensions":["gdoc"]},"application/vnd.google-apps.presentation":{"compressible":false,"extensions":["gslides"]},"application/vnd.google-apps.spreadsheet":{"compressible":false,"extensions":["gsheet"]},"application/vnd.google-earth.kml+xml":{"source":"iana","compressible":true,"extensions":["kml"]},"application/vnd.google-earth.kmz":{"source":"iana","compressible":false,"extensions":["kmz"]},"application/vnd.gov.sk.e-form+xml":{"source":"iana","compressible":true},"application/vnd.gov.sk.e-form+zip":{"source":"iana","compressible":false},"application/vnd.gov.sk.xmldatacontainer+xml":{"source":"iana","compressible":true},"application/vnd.grafeq":{"source":"iana","extensions":["gqf","gqs"]},"application/vnd.gridmp":{"source":"iana"},"application/vnd.groove-account":{"source":"iana","extensions":["gac"]},"application/vnd.groove-help":{"source":"iana","extensions":["ghf"]},"application/vnd.groove-identity-message":{"source":"iana","extensions":["gim"]},"application/vnd.groove-injector":{"source":"iana","extensions":["grv"]},"application/vnd.groove-tool-message":{"source":"iana","extensions":["gtm"]},"application/vnd.groove-tool-template":{"source":"iana","extensions":["tpl"]},"application/vnd.groove-vcard":{"source":"iana","extensions":["vcg"]},"application/vnd.hal+json":{"source":"iana","compressible":true},"application/vnd.hal+xml":{"source":"iana","compressible":true,"extensions":["hal"]},"application/vnd.handheld-entertainment+xml":{"source":"iana","compressible":true,"extensions":["zmm"]},"application/vnd.hbci":{"source":"iana","extensions":["hbci"]},"application/vnd.hc+json":{"source":"iana","compressible":true},"application/vnd.hcl-bireports":{"source":"iana"},"application/vnd.hdt":{"source":"iana"},"application/vnd.heroku+json":{"source":"iana","compressible":true},"application/vnd.hhe.lesson-player":{"source":"iana","extensions":["les"]},"application/vnd.hl7cda+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.hl7v2+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.hp-hpgl":{"source":"iana","extensions":["hpgl"]},"application/vnd.hp-hpid":{"source":"iana","extensions":["hpid"]},"application/vnd.hp-hps":{"source":"iana","extensions":["hps"]},"application/vnd.hp-jlyt":{"source":"iana","extensions":["jlt"]},"application/vnd.hp-pcl":{"source":"iana","extensions":["pcl"]},"application/vnd.hp-pclxl":{"source":"iana","extensions":["pclxl"]},"application/vnd.httphone":{"source":"iana"},"application/vnd.hydrostatix.sof-data":{"source":"iana","extensions":["sfd-hdstx"]},"application/vnd.hyper+json":{"source":"iana","compressible":true},"application/vnd.hyper-item+json":{"source":"iana","compressible":true},"application/vnd.hyperdrive+json":{"source":"iana","compressible":true},"application/vnd.hzn-3d-crossword":{"source":"iana"},"application/vnd.ibm.afplinedata":{"source":"iana"},"application/vnd.ibm.electronic-media":{"source":"iana"},"application/vnd.ibm.minipay":{"source":"iana","extensions":["mpy"]},"application/vnd.ibm.modcap":{"source":"iana","extensions":["afp","listafp","list3820"]},"application/vnd.ibm.rights-management":{"source":"iana","extensions":["irm"]},"application/vnd.ibm.secure-container":{"source":"iana","extensions":["sc"]},"application/vnd.iccprofile":{"source":"iana","extensions":["icc","icm"]},"application/vnd.ieee.1905":{"source":"iana"},"application/vnd.igloader":{"source":"iana","extensions":["igl"]},"application/vnd.imagemeter.folder+zip":{"source":"iana","compressible":false},"application/vnd.imagemeter.image+zip":{"source":"iana","compressible":false},"application/vnd.immervision-ivp":{"source":"iana","extensions":["ivp"]},"application/vnd.immervision-ivu":{"source":"iana","extensions":["ivu"]},"application/vnd.ims.imsccv1p1":{"source":"iana"},"application/vnd.ims.imsccv1p2":{"source":"iana"},"application/vnd.ims.imsccv1p3":{"source":"iana"},"application/vnd.ims.lis.v2.result+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolconsumerprofile+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolproxy+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolproxy.id+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolsettings+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolsettings.simple+json":{"source":"iana","compressible":true},"application/vnd.informedcontrol.rms+xml":{"source":"iana","compressible":true},"application/vnd.informix-visionary":{"source":"iana"},"application/vnd.infotech.project":{"source":"iana"},"application/vnd.infotech.project+xml":{"source":"iana","compressible":true},"application/vnd.innopath.wamp.notification":{"source":"iana"},"application/vnd.insors.igm":{"source":"iana","extensions":["igm"]},"application/vnd.intercon.formnet":{"source":"iana","extensions":["xpw","xpx"]},"application/vnd.intergeo":{"source":"iana","extensions":["i2g"]},"application/vnd.intertrust.digibox":{"source":"iana"},"application/vnd.intertrust.nncp":{"source":"iana"},"application/vnd.intu.qbo":{"source":"iana","extensions":["qbo"]},"application/vnd.intu.qfx":{"source":"iana","extensions":["qfx"]},"application/vnd.iptc.g2.catalogitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.conceptitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.knowledgeitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.newsitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.newsmessage+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.packageitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.planningitem+xml":{"source":"iana","compressible":true},"application/vnd.ipunplugged.rcprofile":{"source":"iana","extensions":["rcprofile"]},"application/vnd.irepository.package+xml":{"source":"iana","compressible":true,"extensions":["irp"]},"application/vnd.is-xpr":{"source":"iana","extensions":["xpr"]},"application/vnd.isac.fcs":{"source":"iana","extensions":["fcs"]},"application/vnd.iso11783-10+zip":{"source":"iana","compressible":false},"application/vnd.jam":{"source":"iana","extensions":["jam"]},"application/vnd.japannet-directory-service":{"source":"iana"},"application/vnd.japannet-jpnstore-wakeup":{"source":"iana"},"application/vnd.japannet-payment-wakeup":{"source":"iana"},"application/vnd.japannet-registration":{"source":"iana"},"application/vnd.japannet-registration-wakeup":{"source":"iana"},"application/vnd.japannet-setstore-wakeup":{"source":"iana"},"application/vnd.japannet-verification":{"source":"iana"},"application/vnd.japannet-verification-wakeup":{"source":"iana"},"application/vnd.jcp.javame.midlet-rms":{"source":"iana","extensions":["rms"]},"application/vnd.jisp":{"source":"iana","extensions":["jisp"]},"application/vnd.joost.joda-archive":{"source":"iana","extensions":["joda"]},"application/vnd.jsk.isdn-ngn":{"source":"iana"},"application/vnd.kahootz":{"source":"iana","extensions":["ktz","ktr"]},"application/vnd.kde.karbon":{"source":"iana","extensions":["karbon"]},"application/vnd.kde.kchart":{"source":"iana","extensions":["chrt"]},"application/vnd.kde.kformula":{"source":"iana","extensions":["kfo"]},"application/vnd.kde.kivio":{"source":"iana","extensions":["flw"]},"application/vnd.kde.kontour":{"source":"iana","extensions":["kon"]},"application/vnd.kde.kpresenter":{"source":"iana","extensions":["kpr","kpt"]},"application/vnd.kde.kspread":{"source":"iana","extensions":["ksp"]},"application/vnd.kde.kword":{"source":"iana","extensions":["kwd","kwt"]},"application/vnd.kenameaapp":{"source":"iana","extensions":["htke"]},"application/vnd.kidspiration":{"source":"iana","extensions":["kia"]},"application/vnd.kinar":{"source":"iana","extensions":["kne","knp"]},"application/vnd.koan":{"source":"iana","extensions":["skp","skd","skt","skm"]},"application/vnd.kodak-descriptor":{"source":"iana","extensions":["sse"]},"application/vnd.las":{"source":"iana"},"application/vnd.las.las+json":{"source":"iana","compressible":true},"application/vnd.las.las+xml":{"source":"iana","compressible":true,"extensions":["lasxml"]},"application/vnd.laszip":{"source":"iana"},"application/vnd.leap+json":{"source":"iana","compressible":true},"application/vnd.liberty-request+xml":{"source":"iana","compressible":true},"application/vnd.llamagraphics.life-balance.desktop":{"source":"iana","extensions":["lbd"]},"application/vnd.llamagraphics.life-balance.exchange+xml":{"source":"iana","compressible":true,"extensions":["lbe"]},"application/vnd.logipipe.circuit+zip":{"source":"iana","compressible":false},"application/vnd.loom":{"source":"iana"},"application/vnd.lotus-1-2-3":{"source":"iana","extensions":["123"]},"application/vnd.lotus-approach":{"source":"iana","extensions":["apr"]},"application/vnd.lotus-freelance":{"source":"iana","extensions":["pre"]},"application/vnd.lotus-notes":{"source":"iana","extensions":["nsf"]},"application/vnd.lotus-organizer":{"source":"iana","extensions":["org"]},"application/vnd.lotus-screencam":{"source":"iana","extensions":["scm"]},"application/vnd.lotus-wordpro":{"source":"iana","extensions":["lwp"]},"application/vnd.macports.portpkg":{"source":"iana","extensions":["portpkg"]},"application/vnd.mapbox-vector-tile":{"source":"iana","extensions":["mvt"]},"application/vnd.marlin.drm.actiontoken+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.conftoken+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.license+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.mdcf":{"source":"iana"},"application/vnd.mason+json":{"source":"iana","compressible":true},"application/vnd.maxar.archive.3tz+zip":{"source":"iana","compressible":false},"application/vnd.maxmind.maxmind-db":{"source":"iana"},"application/vnd.mcd":{"source":"iana","extensions":["mcd"]},"application/vnd.medcalcdata":{"source":"iana","extensions":["mc1"]},"application/vnd.mediastation.cdkey":{"source":"iana","extensions":["cdkey"]},"application/vnd.meridian-slingshot":{"source":"iana"},"application/vnd.mfer":{"source":"iana","extensions":["mwf"]},"application/vnd.mfmp":{"source":"iana","extensions":["mfm"]},"application/vnd.micro+json":{"source":"iana","compressible":true},"application/vnd.micrografx.flo":{"source":"iana","extensions":["flo"]},"application/vnd.micrografx.igx":{"source":"iana","extensions":["igx"]},"application/vnd.microsoft.portable-executable":{"source":"iana"},"application/vnd.microsoft.windows.thumbnail-cache":{"source":"iana"},"application/vnd.miele+json":{"source":"iana","compressible":true},"application/vnd.mif":{"source":"iana","extensions":["mif"]},"application/vnd.minisoft-hp3000-save":{"source":"iana"},"application/vnd.mitsubishi.misty-guard.trustweb":{"source":"iana"},"application/vnd.mobius.daf":{"source":"iana","extensions":["daf"]},"application/vnd.mobius.dis":{"source":"iana","extensions":["dis"]},"application/vnd.mobius.mbk":{"source":"iana","extensions":["mbk"]},"application/vnd.mobius.mqy":{"source":"iana","extensions":["mqy"]},"application/vnd.mobius.msl":{"source":"iana","extensions":["msl"]},"application/vnd.mobius.plc":{"source":"iana","extensions":["plc"]},"application/vnd.mobius.txf":{"source":"iana","extensions":["txf"]},"application/vnd.mophun.application":{"source":"iana","extensions":["mpn"]},"application/vnd.mophun.certificate":{"source":"iana","extensions":["mpc"]},"application/vnd.motorola.flexsuite":{"source":"iana"},"application/vnd.motorola.flexsuite.adsi":{"source":"iana"},"application/vnd.motorola.flexsuite.fis":{"source":"iana"},"application/vnd.motorola.flexsuite.gotap":{"source":"iana"},"application/vnd.motorola.flexsuite.kmr":{"source":"iana"},"application/vnd.motorola.flexsuite.ttc":{"source":"iana"},"application/vnd.motorola.flexsuite.wem":{"source":"iana"},"application/vnd.motorola.iprm":{"source":"iana"},"application/vnd.mozilla.xul+xml":{"source":"iana","compressible":true,"extensions":["xul"]},"application/vnd.ms-3mfdocument":{"source":"iana"},"application/vnd.ms-artgalry":{"source":"iana","extensions":["cil"]},"application/vnd.ms-asf":{"source":"iana"},"application/vnd.ms-cab-compressed":{"source":"iana","extensions":["cab"]},"application/vnd.ms-color.iccprofile":{"source":"apache"},"application/vnd.ms-excel":{"source":"iana","compressible":false,"extensions":["xls","xlm","xla","xlc","xlt","xlw"]},"application/vnd.ms-excel.addin.macroenabled.12":{"source":"iana","extensions":["xlam"]},"application/vnd.ms-excel.sheet.binary.macroenabled.12":{"source":"iana","extensions":["xlsb"]},"application/vnd.ms-excel.sheet.macroenabled.12":{"source":"iana","extensions":["xlsm"]},"application/vnd.ms-excel.template.macroenabled.12":{"source":"iana","extensions":["xltm"]},"application/vnd.ms-fontobject":{"source":"iana","compressible":true,"extensions":["eot"]},"application/vnd.ms-htmlhelp":{"source":"iana","extensions":["chm"]},"application/vnd.ms-ims":{"source":"iana","extensions":["ims"]},"application/vnd.ms-lrm":{"source":"iana","extensions":["lrm"]},"application/vnd.ms-office.activex+xml":{"source":"iana","compressible":true},"application/vnd.ms-officetheme":{"source":"iana","extensions":["thmx"]},"application/vnd.ms-opentype":{"source":"apache","compressible":true},"application/vnd.ms-outlook":{"compressible":false,"extensions":["msg"]},"application/vnd.ms-package.obfuscated-opentype":{"source":"apache"},"application/vnd.ms-pki.seccat":{"source":"apache","extensions":["cat"]},"application/vnd.ms-pki.stl":{"source":"apache","extensions":["stl"]},"application/vnd.ms-playready.initiator+xml":{"source":"iana","compressible":true},"application/vnd.ms-powerpoint":{"source":"iana","compressible":false,"extensions":["ppt","pps","pot"]},"application/vnd.ms-powerpoint.addin.macroenabled.12":{"source":"iana","extensions":["ppam"]},"application/vnd.ms-powerpoint.presentation.macroenabled.12":{"source":"iana","extensions":["pptm"]},"application/vnd.ms-powerpoint.slide.macroenabled.12":{"source":"iana","extensions":["sldm"]},"application/vnd.ms-powerpoint.slideshow.macroenabled.12":{"source":"iana","extensions":["ppsm"]},"application/vnd.ms-powerpoint.template.macroenabled.12":{"source":"iana","extensions":["potm"]},"application/vnd.ms-printdevicecapabilities+xml":{"source":"iana","compressible":true},"application/vnd.ms-printing.printticket+xml":{"source":"apache","compressible":true},"application/vnd.ms-printschematicket+xml":{"source":"iana","compressible":true},"application/vnd.ms-project":{"source":"iana","extensions":["mpp","mpt"]},"application/vnd.ms-tnef":{"source":"iana"},"application/vnd.ms-windows.devicepairing":{"source":"iana"},"application/vnd.ms-windows.nwprinting.oob":{"source":"iana"},"application/vnd.ms-windows.printerpairing":{"source":"iana"},"application/vnd.ms-windows.wsd.oob":{"source":"iana"},"application/vnd.ms-wmdrm.lic-chlg-req":{"source":"iana"},"application/vnd.ms-wmdrm.lic-resp":{"source":"iana"},"application/vnd.ms-wmdrm.meter-chlg-req":{"source":"iana"},"application/vnd.ms-wmdrm.meter-resp":{"source":"iana"},"application/vnd.ms-word.document.macroenabled.12":{"source":"iana","extensions":["docm"]},"application/vnd.ms-word.template.macroenabled.12":{"source":"iana","extensions":["dotm"]},"application/vnd.ms-works":{"source":"iana","extensions":["wps","wks","wcm","wdb"]},"application/vnd.ms-wpl":{"source":"iana","extensions":["wpl"]},"application/vnd.ms-xpsdocument":{"source":"iana","compressible":false,"extensions":["xps"]},"application/vnd.msa-disk-image":{"source":"iana"},"application/vnd.mseq":{"source":"iana","extensions":["mseq"]},"application/vnd.msign":{"source":"iana"},"application/vnd.multiad.creator":{"source":"iana"},"application/vnd.multiad.creator.cif":{"source":"iana"},"application/vnd.music-niff":{"source":"iana"},"application/vnd.musician":{"source":"iana","extensions":["mus"]},"application/vnd.muvee.style":{"source":"iana","extensions":["msty"]},"application/vnd.mynfc":{"source":"iana","extensions":["taglet"]},"application/vnd.nacamar.ybrid+json":{"source":"iana","compressible":true},"application/vnd.ncd.control":{"source":"iana"},"application/vnd.ncd.reference":{"source":"iana"},"application/vnd.nearst.inv+json":{"source":"iana","compressible":true},"application/vnd.nebumind.line":{"source":"iana"},"application/vnd.nervana":{"source":"iana"},"application/vnd.netfpx":{"source":"iana"},"application/vnd.neurolanguage.nlu":{"source":"iana","extensions":["nlu"]},"application/vnd.nimn":{"source":"iana"},"application/vnd.nintendo.nitro.rom":{"source":"iana"},"application/vnd.nintendo.snes.rom":{"source":"iana"},"application/vnd.nitf":{"source":"iana","extensions":["ntf","nitf"]},"application/vnd.noblenet-directory":{"source":"iana","extensions":["nnd"]},"application/vnd.noblenet-sealer":{"source":"iana","extensions":["nns"]},"application/vnd.noblenet-web":{"source":"iana","extensions":["nnw"]},"application/vnd.nokia.catalogs":{"source":"iana"},"application/vnd.nokia.conml+wbxml":{"source":"iana"},"application/vnd.nokia.conml+xml":{"source":"iana","compressible":true},"application/vnd.nokia.iptv.config+xml":{"source":"iana","compressible":true},"application/vnd.nokia.isds-radio-presets":{"source":"iana"},"application/vnd.nokia.landmark+wbxml":{"source":"iana"},"application/vnd.nokia.landmark+xml":{"source":"iana","compressible":true},"application/vnd.nokia.landmarkcollection+xml":{"source":"iana","compressible":true},"application/vnd.nokia.n-gage.ac+xml":{"source":"iana","compressible":true,"extensions":["ac"]},"application/vnd.nokia.n-gage.data":{"source":"iana","extensions":["ngdat"]},"application/vnd.nokia.n-gage.symbian.install":{"source":"iana","extensions":["n-gage"]},"application/vnd.nokia.ncd":{"source":"iana"},"application/vnd.nokia.pcd+wbxml":{"source":"iana"},"application/vnd.nokia.pcd+xml":{"source":"iana","compressible":true},"application/vnd.nokia.radio-preset":{"source":"iana","extensions":["rpst"]},"application/vnd.nokia.radio-presets":{"source":"iana","extensions":["rpss"]},"application/vnd.novadigm.edm":{"source":"iana","extensions":["edm"]},"application/vnd.novadigm.edx":{"source":"iana","extensions":["edx"]},"application/vnd.novadigm.ext":{"source":"iana","extensions":["ext"]},"application/vnd.ntt-local.content-share":{"source":"iana"},"application/vnd.ntt-local.file-transfer":{"source":"iana"},"application/vnd.ntt-local.ogw_remote-access":{"source":"iana"},"application/vnd.ntt-local.sip-ta_remote":{"source":"iana"},"application/vnd.ntt-local.sip-ta_tcp_stream":{"source":"iana"},"application/vnd.oasis.opendocument.chart":{"source":"iana","extensions":["odc"]},"application/vnd.oasis.opendocument.chart-template":{"source":"iana","extensions":["otc"]},"application/vnd.oasis.opendocument.database":{"source":"iana","extensions":["odb"]},"application/vnd.oasis.opendocument.formula":{"source":"iana","extensions":["odf"]},"application/vnd.oasis.opendocument.formula-template":{"source":"iana","extensions":["odft"]},"application/vnd.oasis.opendocument.graphics":{"source":"iana","compressible":false,"extensions":["odg"]},"application/vnd.oasis.opendocument.graphics-template":{"source":"iana","extensions":["otg"]},"application/vnd.oasis.opendocument.image":{"source":"iana","extensions":["odi"]},"application/vnd.oasis.opendocument.image-template":{"source":"iana","extensions":["oti"]},"application/vnd.oasis.opendocument.presentation":{"source":"iana","compressible":false,"extensions":["odp"]},"application/vnd.oasis.opendocument.presentation-template":{"source":"iana","extensions":["otp"]},"application/vnd.oasis.opendocument.spreadsheet":{"source":"iana","compressible":false,"extensions":["ods"]},"application/vnd.oasis.opendocument.spreadsheet-template":{"source":"iana","extensions":["ots"]},"application/vnd.oasis.opendocument.text":{"source":"iana","compressible":false,"extensions":["odt"]},"application/vnd.oasis.opendocument.text-master":{"source":"iana","extensions":["odm"]},"application/vnd.oasis.opendocument.text-template":{"source":"iana","extensions":["ott"]},"application/vnd.oasis.opendocument.text-web":{"source":"iana","extensions":["oth"]},"application/vnd.obn":{"source":"iana"},"application/vnd.ocf+cbor":{"source":"iana"},"application/vnd.oci.image.manifest.v1+json":{"source":"iana","compressible":true},"application/vnd.oftn.l10n+json":{"source":"iana","compressible":true},"application/vnd.oipf.contentaccessdownload+xml":{"source":"iana","compressible":true},"application/vnd.oipf.contentaccessstreaming+xml":{"source":"iana","compressible":true},"application/vnd.oipf.cspg-hexbinary":{"source":"iana"},"application/vnd.oipf.dae.svg+xml":{"source":"iana","compressible":true},"application/vnd.oipf.dae.xhtml+xml":{"source":"iana","compressible":true},"application/vnd.oipf.mippvcontrolmessage+xml":{"source":"iana","compressible":true},"application/vnd.oipf.pae.gem":{"source":"iana"},"application/vnd.oipf.spdiscovery+xml":{"source":"iana","compressible":true},"application/vnd.oipf.spdlist+xml":{"source":"iana","compressible":true},"application/vnd.oipf.ueprofile+xml":{"source":"iana","compressible":true},"application/vnd.oipf.userprofile+xml":{"source":"iana","compressible":true},"application/vnd.olpc-sugar":{"source":"iana","extensions":["xo"]},"application/vnd.oma-scws-config":{"source":"iana"},"application/vnd.oma-scws-http-request":{"source":"iana"},"application/vnd.oma-scws-http-response":{"source":"iana"},"application/vnd.oma.bcast.associated-procedure-parameter+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.drm-trigger+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.imd+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.ltkm":{"source":"iana"},"application/vnd.oma.bcast.notification+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.provisioningtrigger":{"source":"iana"},"application/vnd.oma.bcast.sgboot":{"source":"iana"},"application/vnd.oma.bcast.sgdd+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.sgdu":{"source":"iana"},"application/vnd.oma.bcast.simple-symbol-container":{"source":"iana"},"application/vnd.oma.bcast.smartcard-trigger+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.sprov+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.stkm":{"source":"iana"},"application/vnd.oma.cab-address-book+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-feature-handler+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-pcc+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-subs-invite+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-user-prefs+xml":{"source":"iana","compressible":true},"application/vnd.oma.dcd":{"source":"iana"},"application/vnd.oma.dcdc":{"source":"iana"},"application/vnd.oma.dd2+xml":{"source":"iana","compressible":true,"extensions":["dd2"]},"application/vnd.oma.drm.risd+xml":{"source":"iana","compressible":true},"application/vnd.oma.group-usage-list+xml":{"source":"iana","compressible":true},"application/vnd.oma.lwm2m+cbor":{"source":"iana"},"application/vnd.oma.lwm2m+json":{"source":"iana","compressible":true},"application/vnd.oma.lwm2m+tlv":{"source":"iana"},"application/vnd.oma.pal+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.detailed-progress-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.final-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.groups+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.invocation-descriptor+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.optimized-progress-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.push":{"source":"iana"},"application/vnd.oma.scidm.messages+xml":{"source":"iana","compressible":true},"application/vnd.oma.xcap-directory+xml":{"source":"iana","compressible":true},"application/vnd.omads-email+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omads-file+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omads-folder+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omaloc-supl-init":{"source":"iana"},"application/vnd.onepager":{"source":"iana"},"application/vnd.onepagertamp":{"source":"iana"},"application/vnd.onepagertamx":{"source":"iana"},"application/vnd.onepagertat":{"source":"iana"},"application/vnd.onepagertatp":{"source":"iana"},"application/vnd.onepagertatx":{"source":"iana"},"application/vnd.openblox.game+xml":{"source":"iana","compressible":true,"extensions":["obgx"]},"application/vnd.openblox.game-binary":{"source":"iana"},"application/vnd.openeye.oeb":{"source":"iana"},"application/vnd.openofficeorg.extension":{"source":"apache","extensions":["oxt"]},"application/vnd.openstreetmap.data+xml":{"source":"iana","compressible":true,"extensions":["osm"]},"application/vnd.opentimestamps.ots":{"source":"iana"},"application/vnd.openxmlformats-officedocument.custom-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.customxmlproperties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawing+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.chart+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramcolors+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramdata+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramlayout+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramstyle+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.extended-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.commentauthors+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.handoutmaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.notesmaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.notesslide+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.presentation":{"source":"iana","compressible":false,"extensions":["pptx"]},"application/vnd.openxmlformats-officedocument.presentationml.presentation.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.presprops+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slide":{"source":"iana","extensions":["sldx"]},"application/vnd.openxmlformats-officedocument.presentationml.slide+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slidelayout+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slidemaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slideshow":{"source":"iana","extensions":["ppsx"]},"application/vnd.openxmlformats-officedocument.presentationml.slideshow.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slideupdateinfo+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.tablestyles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.tags+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.template":{"source":"iana","extensions":["potx"]},"application/vnd.openxmlformats-officedocument.presentationml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.viewprops+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.calcchain+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.externallink+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcachedefinition+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcacherecords+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivottable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.querytable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionheaders+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionlog+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedstrings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":{"source":"iana","compressible":false,"extensions":["xlsx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetmetadata+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.tablesinglecells+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.template":{"source":"iana","extensions":["xltx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.usernames+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.volatiledependencies+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.theme+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.themeoverride+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.vmldrawing":{"source":"iana"},"application/vnd.openxmlformats-officedocument.wordprocessingml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.document":{"source":"iana","compressible":false,"extensions":["docx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.glossary+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.endnotes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.fonttable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.footer+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.footnotes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.numbering+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.settings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.styles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.template":{"source":"iana","extensions":["dotx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.websettings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.core-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.digital-signature-xmlsignature+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.relationships+xml":{"source":"iana","compressible":true},"application/vnd.oracle.resource+json":{"source":"iana","compressible":true},"application/vnd.orange.indata":{"source":"iana"},"application/vnd.osa.netdeploy":{"source":"iana"},"application/vnd.osgeo.mapguide.package":{"source":"iana","extensions":["mgp"]},"application/vnd.osgi.bundle":{"source":"iana"},"application/vnd.osgi.dp":{"source":"iana","extensions":["dp"]},"application/vnd.osgi.subsystem":{"source":"iana","extensions":["esa"]},"application/vnd.otps.ct-kip+xml":{"source":"iana","compressible":true},"application/vnd.oxli.countgraph":{"source":"iana"},"application/vnd.pagerduty+json":{"source":"iana","compressible":true},"application/vnd.palm":{"source":"iana","extensions":["pdb","pqa","oprc"]},"application/vnd.panoply":{"source":"iana"},"application/vnd.paos.xml":{"source":"iana"},"application/vnd.patentdive":{"source":"iana"},"application/vnd.patientecommsdoc":{"source":"iana"},"application/vnd.pawaafile":{"source":"iana","extensions":["paw"]},"application/vnd.pcos":{"source":"iana"},"application/vnd.pg.format":{"source":"iana","extensions":["str"]},"application/vnd.pg.osasli":{"source":"iana","extensions":["ei6"]},"application/vnd.piaccess.application-licence":{"source":"iana"},"application/vnd.picsel":{"source":"iana","extensions":["efif"]},"application/vnd.pmi.widget":{"source":"iana","extensions":["wg"]},"application/vnd.poc.group-advertisement+xml":{"source":"iana","compressible":true},"application/vnd.pocketlearn":{"source":"iana","extensions":["plf"]},"application/vnd.powerbuilder6":{"source":"iana","extensions":["pbd"]},"application/vnd.powerbuilder6-s":{"source":"iana"},"application/vnd.powerbuilder7":{"source":"iana"},"application/vnd.powerbuilder7-s":{"source":"iana"},"application/vnd.powerbuilder75":{"source":"iana"},"application/vnd.powerbuilder75-s":{"source":"iana"},"application/vnd.preminet":{"source":"iana"},"application/vnd.previewsystems.box":{"source":"iana","extensions":["box"]},"application/vnd.proteus.magazine":{"source":"iana","extensions":["mgz"]},"application/vnd.psfs":{"source":"iana"},"application/vnd.publishare-delta-tree":{"source":"iana","extensions":["qps"]},"application/vnd.pvi.ptid1":{"source":"iana","extensions":["ptid"]},"application/vnd.pwg-multiplexed":{"source":"iana"},"application/vnd.pwg-xhtml-print+xml":{"source":"iana","compressible":true},"application/vnd.qualcomm.brew-app-res":{"source":"iana"},"application/vnd.quarantainenet":{"source":"iana"},"application/vnd.quark.quarkxpress":{"source":"iana","extensions":["qxd","qxt","qwd","qwt","qxl","qxb"]},"application/vnd.quobject-quoxdocument":{"source":"iana"},"application/vnd.radisys.moml+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-conf+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-conn+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-dialog+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-stream+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-conf+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-base+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-fax-detect+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-fax-sendrecv+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-group+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-speech+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-transform+xml":{"source":"iana","compressible":true},"application/vnd.rainstor.data":{"source":"iana"},"application/vnd.rapid":{"source":"iana"},"application/vnd.rar":{"source":"iana","extensions":["rar"]},"application/vnd.realvnc.bed":{"source":"iana","extensions":["bed"]},"application/vnd.recordare.musicxml":{"source":"iana","extensions":["mxl"]},"application/vnd.recordare.musicxml+xml":{"source":"iana","compressible":true,"extensions":["musicxml"]},"application/vnd.renlearn.rlprint":{"source":"iana"},"application/vnd.resilient.logic":{"source":"iana"},"application/vnd.restful+json":{"source":"iana","compressible":true},"application/vnd.rig.cryptonote":{"source":"iana","extensions":["cryptonote"]},"application/vnd.rim.cod":{"source":"apache","extensions":["cod"]},"application/vnd.rn-realmedia":{"source":"apache","extensions":["rm"]},"application/vnd.rn-realmedia-vbr":{"source":"apache","extensions":["rmvb"]},"application/vnd.route66.link66+xml":{"source":"iana","compressible":true,"extensions":["link66"]},"application/vnd.rs-274x":{"source":"iana"},"application/vnd.ruckus.download":{"source":"iana"},"application/vnd.s3sms":{"source":"iana"},"application/vnd.sailingtracker.track":{"source":"iana","extensions":["st"]},"application/vnd.sar":{"source":"iana"},"application/vnd.sbm.cid":{"source":"iana"},"application/vnd.sbm.mid2":{"source":"iana"},"application/vnd.scribus":{"source":"iana"},"application/vnd.sealed.3df":{"source":"iana"},"application/vnd.sealed.csf":{"source":"iana"},"application/vnd.sealed.doc":{"source":"iana"},"application/vnd.sealed.eml":{"source":"iana"},"application/vnd.sealed.mht":{"source":"iana"},"application/vnd.sealed.net":{"source":"iana"},"application/vnd.sealed.ppt":{"source":"iana"},"application/vnd.sealed.tiff":{"source":"iana"},"application/vnd.sealed.xls":{"source":"iana"},"application/vnd.sealedmedia.softseal.html":{"source":"iana"},"application/vnd.sealedmedia.softseal.pdf":{"source":"iana"},"application/vnd.seemail":{"source":"iana","extensions":["see"]},"application/vnd.seis+json":{"source":"iana","compressible":true},"application/vnd.sema":{"source":"iana","extensions":["sema"]},"application/vnd.semd":{"source":"iana","extensions":["semd"]},"application/vnd.semf":{"source":"iana","extensions":["semf"]},"application/vnd.shade-save-file":{"source":"iana"},"application/vnd.shana.informed.formdata":{"source":"iana","extensions":["ifm"]},"application/vnd.shana.informed.formtemplate":{"source":"iana","extensions":["itp"]},"application/vnd.shana.informed.interchange":{"source":"iana","extensions":["iif"]},"application/vnd.shana.informed.package":{"source":"iana","extensions":["ipk"]},"application/vnd.shootproof+json":{"source":"iana","compressible":true},"application/vnd.shopkick+json":{"source":"iana","compressible":true},"application/vnd.shp":{"source":"iana"},"application/vnd.shx":{"source":"iana"},"application/vnd.sigrok.session":{"source":"iana"},"application/vnd.simtech-mindmapper":{"source":"iana","extensions":["twd","twds"]},"application/vnd.siren+json":{"source":"iana","compressible":true},"application/vnd.smaf":{"source":"iana","extensions":["mmf"]},"application/vnd.smart.notebook":{"source":"iana"},"application/vnd.smart.teacher":{"source":"iana","extensions":["teacher"]},"application/vnd.snesdev-page-table":{"source":"iana"},"application/vnd.software602.filler.form+xml":{"source":"iana","compressible":true,"extensions":["fo"]},"application/vnd.software602.filler.form-xml-zip":{"source":"iana"},"application/vnd.solent.sdkm+xml":{"source":"iana","compressible":true,"extensions":["sdkm","sdkd"]},"application/vnd.spotfire.dxp":{"source":"iana","extensions":["dxp"]},"application/vnd.spotfire.sfs":{"source":"iana","extensions":["sfs"]},"application/vnd.sqlite3":{"source":"iana"},"application/vnd.sss-cod":{"source":"iana"},"application/vnd.sss-dtf":{"source":"iana"},"application/vnd.sss-ntf":{"source":"iana"},"application/vnd.stardivision.calc":{"source":"apache","extensions":["sdc"]},"application/vnd.stardivision.draw":{"source":"apache","extensions":["sda"]},"application/vnd.stardivision.impress":{"source":"apache","extensions":["sdd"]},"application/vnd.stardivision.math":{"source":"apache","extensions":["smf"]},"application/vnd.stardivision.writer":{"source":"apache","extensions":["sdw","vor"]},"application/vnd.stardivision.writer-global":{"source":"apache","extensions":["sgl"]},"application/vnd.stepmania.package":{"source":"iana","extensions":["smzip"]},"application/vnd.stepmania.stepchart":{"source":"iana","extensions":["sm"]},"application/vnd.street-stream":{"source":"iana"},"application/vnd.sun.wadl+xml":{"source":"iana","compressible":true,"extensions":["wadl"]},"application/vnd.sun.xml.calc":{"source":"apache","extensions":["sxc"]},"application/vnd.sun.xml.calc.template":{"source":"apache","extensions":["stc"]},"application/vnd.sun.xml.draw":{"source":"apache","extensions":["sxd"]},"application/vnd.sun.xml.draw.template":{"source":"apache","extensions":["std"]},"application/vnd.sun.xml.impress":{"source":"apache","extensions":["sxi"]},"application/vnd.sun.xml.impress.template":{"source":"apache","extensions":["sti"]},"application/vnd.sun.xml.math":{"source":"apache","extensions":["sxm"]},"application/vnd.sun.xml.writer":{"source":"apache","extensions":["sxw"]},"application/vnd.sun.xml.writer.global":{"source":"apache","extensions":["sxg"]},"application/vnd.sun.xml.writer.template":{"source":"apache","extensions":["stw"]},"application/vnd.sus-calendar":{"source":"iana","extensions":["sus","susp"]},"application/vnd.svd":{"source":"iana","extensions":["svd"]},"application/vnd.swiftview-ics":{"source":"iana"},"application/vnd.sycle+xml":{"source":"iana","compressible":true},"application/vnd.syft+json":{"source":"iana","compressible":true},"application/vnd.symbian.install":{"source":"apache","extensions":["sis","sisx"]},"application/vnd.syncml+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["xsm"]},"application/vnd.syncml.dm+wbxml":{"source":"iana","charset":"UTF-8","extensions":["bdm"]},"application/vnd.syncml.dm+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["xdm"]},"application/vnd.syncml.dm.notification":{"source":"iana"},"application/vnd.syncml.dmddf+wbxml":{"source":"iana"},"application/vnd.syncml.dmddf+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["ddf"]},"application/vnd.syncml.dmtnds+wbxml":{"source":"iana"},"application/vnd.syncml.dmtnds+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.syncml.ds.notification":{"source":"iana"},"application/vnd.tableschema+json":{"source":"iana","compressible":true},"application/vnd.tao.intent-module-archive":{"source":"iana","extensions":["tao"]},"application/vnd.tcpdump.pcap":{"source":"iana","extensions":["pcap","cap","dmp"]},"application/vnd.think-cell.ppttc+json":{"source":"iana","compressible":true},"application/vnd.tmd.mediaflex.api+xml":{"source":"iana","compressible":true},"application/vnd.tml":{"source":"iana"},"application/vnd.tmobile-livetv":{"source":"iana","extensions":["tmo"]},"application/vnd.tri.onesource":{"source":"iana"},"application/vnd.trid.tpt":{"source":"iana","extensions":["tpt"]},"application/vnd.triscape.mxs":{"source":"iana","extensions":["mxs"]},"application/vnd.trueapp":{"source":"iana","extensions":["tra"]},"application/vnd.truedoc":{"source":"iana"},"application/vnd.ubisoft.webplayer":{"source":"iana"},"application/vnd.ufdl":{"source":"iana","extensions":["ufd","ufdl"]},"application/vnd.uiq.theme":{"source":"iana","extensions":["utz"]},"application/vnd.umajin":{"source":"iana","extensions":["umj"]},"application/vnd.unity":{"source":"iana","extensions":["unityweb"]},"application/vnd.uoml+xml":{"source":"iana","compressible":true,"extensions":["uoml"]},"application/vnd.uplanet.alert":{"source":"iana"},"application/vnd.uplanet.alert-wbxml":{"source":"iana"},"application/vnd.uplanet.bearer-choice":{"source":"iana"},"application/vnd.uplanet.bearer-choice-wbxml":{"source":"iana"},"application/vnd.uplanet.cacheop":{"source":"iana"},"application/vnd.uplanet.cacheop-wbxml":{"source":"iana"},"application/vnd.uplanet.channel":{"source":"iana"},"application/vnd.uplanet.channel-wbxml":{"source":"iana"},"application/vnd.uplanet.list":{"source":"iana"},"application/vnd.uplanet.list-wbxml":{"source":"iana"},"application/vnd.uplanet.listcmd":{"source":"iana"},"application/vnd.uplanet.listcmd-wbxml":{"source":"iana"},"application/vnd.uplanet.signal":{"source":"iana"},"application/vnd.uri-map":{"source":"iana"},"application/vnd.valve.source.material":{"source":"iana"},"application/vnd.vcx":{"source":"iana","extensions":["vcx"]},"application/vnd.vd-study":{"source":"iana"},"application/vnd.vectorworks":{"source":"iana"},"application/vnd.vel+json":{"source":"iana","compressible":true},"application/vnd.verimatrix.vcas":{"source":"iana"},"application/vnd.veritone.aion+json":{"source":"iana","compressible":true},"application/vnd.veryant.thin":{"source":"iana"},"application/vnd.ves.encrypted":{"source":"iana"},"application/vnd.vidsoft.vidconference":{"source":"iana"},"application/vnd.visio":{"source":"iana","extensions":["vsd","vst","vss","vsw"]},"application/vnd.visionary":{"source":"iana","extensions":["vis"]},"application/vnd.vividence.scriptfile":{"source":"iana"},"application/vnd.vsf":{"source":"iana","extensions":["vsf"]},"application/vnd.wap.sic":{"source":"iana"},"application/vnd.wap.slc":{"source":"iana"},"application/vnd.wap.wbxml":{"source":"iana","charset":"UTF-8","extensions":["wbxml"]},"application/vnd.wap.wmlc":{"source":"iana","extensions":["wmlc"]},"application/vnd.wap.wmlscriptc":{"source":"iana","extensions":["wmlsc"]},"application/vnd.webturbo":{"source":"iana","extensions":["wtb"]},"application/vnd.wfa.dpp":{"source":"iana"},"application/vnd.wfa.p2p":{"source":"iana"},"application/vnd.wfa.wsc":{"source":"iana"},"application/vnd.windows.devicepairing":{"source":"iana"},"application/vnd.wmc":{"source":"iana"},"application/vnd.wmf.bootstrap":{"source":"iana"},"application/vnd.wolfram.mathematica":{"source":"iana"},"application/vnd.wolfram.mathematica.package":{"source":"iana"},"application/vnd.wolfram.player":{"source":"iana","extensions":["nbp"]},"application/vnd.wordperfect":{"source":"iana","extensions":["wpd"]},"application/vnd.wqd":{"source":"iana","extensions":["wqd"]},"application/vnd.wrq-hp3000-labelled":{"source":"iana"},"application/vnd.wt.stf":{"source":"iana","extensions":["stf"]},"application/vnd.wv.csp+wbxml":{"source":"iana"},"application/vnd.wv.csp+xml":{"source":"iana","compressible":true},"application/vnd.wv.ssp+xml":{"source":"iana","compressible":true},"application/vnd.xacml+json":{"source":"iana","compressible":true},"application/vnd.xara":{"source":"iana","extensions":["xar"]},"application/vnd.xfdl":{"source":"iana","extensions":["xfdl"]},"application/vnd.xfdl.webform":{"source":"iana"},"application/vnd.xmi+xml":{"source":"iana","compressible":true},"application/vnd.xmpie.cpkg":{"source":"iana"},"application/vnd.xmpie.dpkg":{"source":"iana"},"application/vnd.xmpie.plan":{"source":"iana"},"application/vnd.xmpie.ppkg":{"source":"iana"},"application/vnd.xmpie.xlim":{"source":"iana"},"application/vnd.yamaha.hv-dic":{"source":"iana","extensions":["hvd"]},"application/vnd.yamaha.hv-script":{"source":"iana","extensions":["hvs"]},"application/vnd.yamaha.hv-voice":{"source":"iana","extensions":["hvp"]},"application/vnd.yamaha.openscoreformat":{"source":"iana","extensions":["osf"]},"application/vnd.yamaha.openscoreformat.osfpvg+xml":{"source":"iana","compressible":true,"extensions":["osfpvg"]},"application/vnd.yamaha.remote-setup":{"source":"iana"},"application/vnd.yamaha.smaf-audio":{"source":"iana","extensions":["saf"]},"application/vnd.yamaha.smaf-phrase":{"source":"iana","extensions":["spf"]},"application/vnd.yamaha.through-ngn":{"source":"iana"},"application/vnd.yamaha.tunnel-udpencap":{"source":"iana"},"application/vnd.yaoweme":{"source":"iana"},"application/vnd.yellowriver-custom-menu":{"source":"iana","extensions":["cmp"]},"application/vnd.youtube.yt":{"source":"iana"},"application/vnd.zul":{"source":"iana","extensions":["zir","zirz"]},"application/vnd.zzazz.deck+xml":{"source":"iana","compressible":true,"extensions":["zaz"]},"application/voicexml+xml":{"source":"iana","compressible":true,"extensions":["vxml"]},"application/voucher-cms+json":{"source":"iana","compressible":true},"application/vq-rtcpxr":{"source":"iana"},"application/wasm":{"source":"iana","compressible":true,"extensions":["wasm"]},"application/watcherinfo+xml":{"source":"iana","compressible":true,"extensions":["wif"]},"application/webpush-options+json":{"source":"iana","compressible":true},"application/whoispp-query":{"source":"iana"},"application/whoispp-response":{"source":"iana"},"application/widget":{"source":"iana","extensions":["wgt"]},"application/winhlp":{"source":"apache","extensions":["hlp"]},"application/wita":{"source":"iana"},"application/wordperfect5.1":{"source":"iana"},"application/wsdl+xml":{"source":"iana","compressible":true,"extensions":["wsdl"]},"application/wspolicy+xml":{"source":"iana","compressible":true,"extensions":["wspolicy"]},"application/x-7z-compressed":{"source":"apache","compressible":false,"extensions":["7z"]},"application/x-abiword":{"source":"apache","extensions":["abw"]},"application/x-ace-compressed":{"source":"apache","extensions":["ace"]},"application/x-amf":{"source":"apache"},"application/x-apple-diskimage":{"source":"apache","extensions":["dmg"]},"application/x-arj":{"compressible":false,"extensions":["arj"]},"application/x-authorware-bin":{"source":"apache","extensions":["aab","x32","u32","vox"]},"application/x-authorware-map":{"source":"apache","extensions":["aam"]},"application/x-authorware-seg":{"source":"apache","extensions":["aas"]},"application/x-bcpio":{"source":"apache","extensions":["bcpio"]},"application/x-bdoc":{"compressible":false,"extensions":["bdoc"]},"application/x-bittorrent":{"source":"apache","extensions":["torrent"]},"application/x-blorb":{"source":"apache","extensions":["blb","blorb"]},"application/x-bzip":{"source":"apache","compressible":false,"extensions":["bz"]},"application/x-bzip2":{"source":"apache","compressible":false,"extensions":["bz2","boz"]},"application/x-cbr":{"source":"apache","extensions":["cbr","cba","cbt","cbz","cb7"]},"application/x-cdlink":{"source":"apache","extensions":["vcd"]},"application/x-cfs-compressed":{"source":"apache","extensions":["cfs"]},"application/x-chat":{"source":"apache","extensions":["chat"]},"application/x-chess-pgn":{"source":"apache","extensions":["pgn"]},"application/x-chrome-extension":{"extensions":["crx"]},"application/x-cocoa":{"source":"nginx","extensions":["cco"]},"application/x-compress":{"source":"apache"},"application/x-conference":{"source":"apache","extensions":["nsc"]},"application/x-cpio":{"source":"apache","extensions":["cpio"]},"application/x-csh":{"source":"apache","extensions":["csh"]},"application/x-deb":{"compressible":false},"application/x-debian-package":{"source":"apache","extensions":["deb","udeb"]},"application/x-dgc-compressed":{"source":"apache","extensions":["dgc"]},"application/x-director":{"source":"apache","extensions":["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"]},"application/x-doom":{"source":"apache","extensions":["wad"]},"application/x-dtbncx+xml":{"source":"apache","compressible":true,"extensions":["ncx"]},"application/x-dtbook+xml":{"source":"apache","compressible":true,"extensions":["dtb"]},"application/x-dtbresource+xml":{"source":"apache","compressible":true,"extensions":["res"]},"application/x-dvi":{"source":"apache","compressible":false,"extensions":["dvi"]},"application/x-envoy":{"source":"apache","extensions":["evy"]},"application/x-eva":{"source":"apache","extensions":["eva"]},"application/x-font-bdf":{"source":"apache","extensions":["bdf"]},"application/x-font-dos":{"source":"apache"},"application/x-font-framemaker":{"source":"apache"},"application/x-font-ghostscript":{"source":"apache","extensions":["gsf"]},"application/x-font-libgrx":{"source":"apache"},"application/x-font-linux-psf":{"source":"apache","extensions":["psf"]},"application/x-font-pcf":{"source":"apache","extensions":["pcf"]},"application/x-font-snf":{"source":"apache","extensions":["snf"]},"application/x-font-speedo":{"source":"apache"},"application/x-font-sunos-news":{"source":"apache"},"application/x-font-type1":{"source":"apache","extensions":["pfa","pfb","pfm","afm"]},"application/x-font-vfont":{"source":"apache"},"application/x-freearc":{"source":"apache","extensions":["arc"]},"application/x-futuresplash":{"source":"apache","extensions":["spl"]},"application/x-gca-compressed":{"source":"apache","extensions":["gca"]},"application/x-glulx":{"source":"apache","extensions":["ulx"]},"application/x-gnumeric":{"source":"apache","extensions":["gnumeric"]},"application/x-gramps-xml":{"source":"apache","extensions":["gramps"]},"application/x-gtar":{"source":"apache","extensions":["gtar"]},"application/x-gzip":{"source":"apache"},"application/x-hdf":{"source":"apache","extensions":["hdf"]},"application/x-httpd-php":{"compressible":true,"extensions":["php"]},"application/x-install-instructions":{"source":"apache","extensions":["install"]},"application/x-iso9660-image":{"source":"apache","extensions":["iso"]},"application/x-iwork-keynote-sffkey":{"extensions":["key"]},"application/x-iwork-numbers-sffnumbers":{"extensions":["numbers"]},"application/x-iwork-pages-sffpages":{"extensions":["pages"]},"application/x-java-archive-diff":{"source":"nginx","extensions":["jardiff"]},"application/x-java-jnlp-file":{"source":"apache","compressible":false,"extensions":["jnlp"]},"application/x-javascript":{"compressible":true},"application/x-keepass2":{"extensions":["kdbx"]},"application/x-latex":{"source":"apache","compressible":false,"extensions":["latex"]},"application/x-lua-bytecode":{"extensions":["luac"]},"application/x-lzh-compressed":{"source":"apache","extensions":["lzh","lha"]},"application/x-makeself":{"source":"nginx","extensions":["run"]},"application/x-mie":{"source":"apache","extensions":["mie"]},"application/x-mobipocket-ebook":{"source":"apache","extensions":["prc","mobi"]},"application/x-mpegurl":{"compressible":false},"application/x-ms-application":{"source":"apache","extensions":["application"]},"application/x-ms-shortcut":{"source":"apache","extensions":["lnk"]},"application/x-ms-wmd":{"source":"apache","extensions":["wmd"]},"application/x-ms-wmz":{"source":"apache","extensions":["wmz"]},"application/x-ms-xbap":{"source":"apache","extensions":["xbap"]},"application/x-msaccess":{"source":"apache","extensions":["mdb"]},"application/x-msbinder":{"source":"apache","extensions":["obd"]},"application/x-mscardfile":{"source":"apache","extensions":["crd"]},"application/x-msclip":{"source":"apache","extensions":["clp"]},"application/x-msdos-program":{"extensions":["exe"]},"application/x-msdownload":{"source":"apache","extensions":["exe","dll","com","bat","msi"]},"application/x-msmediaview":{"source":"apache","extensions":["mvb","m13","m14"]},"application/x-msmetafile":{"source":"apache","extensions":["wmf","wmz","emf","emz"]},"application/x-msmoney":{"source":"apache","extensions":["mny"]},"application/x-mspublisher":{"source":"apache","extensions":["pub"]},"application/x-msschedule":{"source":"apache","extensions":["scd"]},"application/x-msterminal":{"source":"apache","extensions":["trm"]},"application/x-mswrite":{"source":"apache","extensions":["wri"]},"application/x-netcdf":{"source":"apache","extensions":["nc","cdf"]},"application/x-ns-proxy-autoconfig":{"compressible":true,"extensions":["pac"]},"application/x-nzb":{"source":"apache","extensions":["nzb"]},"application/x-perl":{"source":"nginx","extensions":["pl","pm"]},"application/x-pilot":{"source":"nginx","extensions":["prc","pdb"]},"application/x-pkcs12":{"source":"apache","compressible":false,"extensions":["p12","pfx"]},"application/x-pkcs7-certificates":{"source":"apache","extensions":["p7b","spc"]},"application/x-pkcs7-certreqresp":{"source":"apache","extensions":["p7r"]},"application/x-pki-message":{"source":"iana"},"application/x-rar-compressed":{"source":"apache","compressible":false,"extensions":["rar"]},"application/x-redhat-package-manager":{"source":"nginx","extensions":["rpm"]},"application/x-research-info-systems":{"source":"apache","extensions":["ris"]},"application/x-sea":{"source":"nginx","extensions":["sea"]},"application/x-sh":{"source":"apache","compressible":true,"extensions":["sh"]},"application/x-shar":{"source":"apache","extensions":["shar"]},"application/x-shockwave-flash":{"source":"apache","compressible":false,"extensions":["swf"]},"application/x-silverlight-app":{"source":"apache","extensions":["xap"]},"application/x-sql":{"source":"apache","extensions":["sql"]},"application/x-stuffit":{"source":"apache","compressible":false,"extensions":["sit"]},"application/x-stuffitx":{"source":"apache","extensions":["sitx"]},"application/x-subrip":{"source":"apache","extensions":["srt"]},"application/x-sv4cpio":{"source":"apache","extensions":["sv4cpio"]},"application/x-sv4crc":{"source":"apache","extensions":["sv4crc"]},"application/x-t3vm-image":{"source":"apache","extensions":["t3"]},"application/x-tads":{"source":"apache","extensions":["gam"]},"application/x-tar":{"source":"apache","compressible":true,"extensions":["tar"]},"application/x-tcl":{"source":"apache","extensions":["tcl","tk"]},"application/x-tex":{"source":"apache","extensions":["tex"]},"application/x-tex-tfm":{"source":"apache","extensions":["tfm"]},"application/x-texinfo":{"source":"apache","extensions":["texinfo","texi"]},"application/x-tgif":{"source":"apache","extensions":["obj"]},"application/x-ustar":{"source":"apache","extensions":["ustar"]},"application/x-virtualbox-hdd":{"compressible":true,"extensions":["hdd"]},"application/x-virtualbox-ova":{"compressible":true,"extensions":["ova"]},"application/x-virtualbox-ovf":{"compressible":true,"extensions":["ovf"]},"application/x-virtualbox-vbox":{"compressible":true,"extensions":["vbox"]},"application/x-virtualbox-vbox-extpack":{"compressible":false,"extensions":["vbox-extpack"]},"application/x-virtualbox-vdi":{"compressible":true,"extensions":["vdi"]},"application/x-virtualbox-vhd":{"compressible":true,"extensions":["vhd"]},"application/x-virtualbox-vmdk":{"compressible":true,"extensions":["vmdk"]},"application/x-wais-source":{"source":"apache","extensions":["src"]},"application/x-web-app-manifest+json":{"compressible":true,"extensions":["webapp"]},"application/x-www-form-urlencoded":{"source":"iana","compressible":true},"application/x-x509-ca-cert":{"source":"iana","extensions":["der","crt","pem"]},"application/x-x509-ca-ra-cert":{"source":"iana"},"application/x-x509-next-ca-cert":{"source":"iana"},"application/x-xfig":{"source":"apache","extensions":["fig"]},"application/x-xliff+xml":{"source":"apache","compressible":true,"extensions":["xlf"]},"application/x-xpinstall":{"source":"apache","compressible":false,"extensions":["xpi"]},"application/x-xz":{"source":"apache","extensions":["xz"]},"application/x-zmachine":{"source":"apache","extensions":["z1","z2","z3","z4","z5","z6","z7","z8"]},"application/x400-bp":{"source":"iana"},"application/xacml+xml":{"source":"iana","compressible":true},"application/xaml+xml":{"source":"apache","compressible":true,"extensions":["xaml"]},"application/xcap-att+xml":{"source":"iana","compressible":true,"extensions":["xav"]},"application/xcap-caps+xml":{"source":"iana","compressible":true,"extensions":["xca"]},"application/xcap-diff+xml":{"source":"iana","compressible":true,"extensions":["xdf"]},"application/xcap-el+xml":{"source":"iana","compressible":true,"extensions":["xel"]},"application/xcap-error+xml":{"source":"iana","compressible":true},"application/xcap-ns+xml":{"source":"iana","compressible":true,"extensions":["xns"]},"application/xcon-conference-info+xml":{"source":"iana","compressible":true},"application/xcon-conference-info-diff+xml":{"source":"iana","compressible":true},"application/xenc+xml":{"source":"iana","compressible":true,"extensions":["xenc"]},"application/xhtml+xml":{"source":"iana","compressible":true,"extensions":["xhtml","xht"]},"application/xhtml-voice+xml":{"source":"apache","compressible":true},"application/xliff+xml":{"source":"iana","compressible":true,"extensions":["xlf"]},"application/xml":{"source":"iana","compressible":true,"extensions":["xml","xsl","xsd","rng"]},"application/xml-dtd":{"source":"iana","compressible":true,"extensions":["dtd"]},"application/xml-external-parsed-entity":{"source":"iana"},"application/xml-patch+xml":{"source":"iana","compressible":true},"application/xmpp+xml":{"source":"iana","compressible":true},"application/xop+xml":{"source":"iana","compressible":true,"extensions":["xop"]},"application/xproc+xml":{"source":"apache","compressible":true,"extensions":["xpl"]},"application/xslt+xml":{"source":"iana","compressible":true,"extensions":["xsl","xslt"]},"application/xspf+xml":{"source":"apache","compressible":true,"extensions":["xspf"]},"application/xv+xml":{"source":"iana","compressible":true,"extensions":["mxml","xhvml","xvml","xvm"]},"application/yang":{"source":"iana","extensions":["yang"]},"application/yang-data+json":{"source":"iana","compressible":true},"application/yang-data+xml":{"source":"iana","compressible":true},"application/yang-patch+json":{"source":"iana","compressible":true},"application/yang-patch+xml":{"source":"iana","compressible":true},"application/yin+xml":{"source":"iana","compressible":true,"extensions":["yin"]},"application/zip":{"source":"iana","compressible":false,"extensions":["zip"]},"application/zlib":{"source":"iana"},"application/zstd":{"source":"iana"},"audio/1d-interleaved-parityfec":{"source":"iana"},"audio/32kadpcm":{"source":"iana"},"audio/3gpp":{"source":"iana","compressible":false,"extensions":["3gpp"]},"audio/3gpp2":{"source":"iana"},"audio/aac":{"source":"iana"},"audio/ac3":{"source":"iana"},"audio/adpcm":{"source":"apache","extensions":["adp"]},"audio/amr":{"source":"iana","extensions":["amr"]},"audio/amr-wb":{"source":"iana"},"audio/amr-wb+":{"source":"iana"},"audio/aptx":{"source":"iana"},"audio/asc":{"source":"iana"},"audio/atrac-advanced-lossless":{"source":"iana"},"audio/atrac-x":{"source":"iana"},"audio/atrac3":{"source":"iana"},"audio/basic":{"source":"iana","compressible":false,"extensions":["au","snd"]},"audio/bv16":{"source":"iana"},"audio/bv32":{"source":"iana"},"audio/clearmode":{"source":"iana"},"audio/cn":{"source":"iana"},"audio/dat12":{"source":"iana"},"audio/dls":{"source":"iana"},"audio/dsr-es201108":{"source":"iana"},"audio/dsr-es202050":{"source":"iana"},"audio/dsr-es202211":{"source":"iana"},"audio/dsr-es202212":{"source":"iana"},"audio/dv":{"source":"iana"},"audio/dvi4":{"source":"iana"},"audio/eac3":{"source":"iana"},"audio/encaprtp":{"source":"iana"},"audio/evrc":{"source":"iana"},"audio/evrc-qcp":{"source":"iana"},"audio/evrc0":{"source":"iana"},"audio/evrc1":{"source":"iana"},"audio/evrcb":{"source":"iana"},"audio/evrcb0":{"source":"iana"},"audio/evrcb1":{"source":"iana"},"audio/evrcnw":{"source":"iana"},"audio/evrcnw0":{"source":"iana"},"audio/evrcnw1":{"source":"iana"},"audio/evrcwb":{"source":"iana"},"audio/evrcwb0":{"source":"iana"},"audio/evrcwb1":{"source":"iana"},"audio/evs":{"source":"iana"},"audio/flexfec":{"source":"iana"},"audio/fwdred":{"source":"iana"},"audio/g711-0":{"source":"iana"},"audio/g719":{"source":"iana"},"audio/g722":{"source":"iana"},"audio/g7221":{"source":"iana"},"audio/g723":{"source":"iana"},"audio/g726-16":{"source":"iana"},"audio/g726-24":{"source":"iana"},"audio/g726-32":{"source":"iana"},"audio/g726-40":{"source":"iana"},"audio/g728":{"source":"iana"},"audio/g729":{"source":"iana"},"audio/g7291":{"source":"iana"},"audio/g729d":{"source":"iana"},"audio/g729e":{"source":"iana"},"audio/gsm":{"source":"iana"},"audio/gsm-efr":{"source":"iana"},"audio/gsm-hr-08":{"source":"iana"},"audio/ilbc":{"source":"iana"},"audio/ip-mr_v2.5":{"source":"iana"},"audio/isac":{"source":"apache"},"audio/l16":{"source":"iana"},"audio/l20":{"source":"iana"},"audio/l24":{"source":"iana","compressible":false},"audio/l8":{"source":"iana"},"audio/lpc":{"source":"iana"},"audio/melp":{"source":"iana"},"audio/melp1200":{"source":"iana"},"audio/melp2400":{"source":"iana"},"audio/melp600":{"source":"iana"},"audio/mhas":{"source":"iana"},"audio/midi":{"source":"apache","extensions":["mid","midi","kar","rmi"]},"audio/mobile-xmf":{"source":"iana","extensions":["mxmf"]},"audio/mp3":{"compressible":false,"extensions":["mp3"]},"audio/mp4":{"source":"iana","compressible":false,"extensions":["m4a","mp4a"]},"audio/mp4a-latm":{"source":"iana"},"audio/mpa":{"source":"iana"},"audio/mpa-robust":{"source":"iana"},"audio/mpeg":{"source":"iana","compressible":false,"extensions":["mpga","mp2","mp2a","mp3","m2a","m3a"]},"audio/mpeg4-generic":{"source":"iana"},"audio/musepack":{"source":"apache"},"audio/ogg":{"source":"iana","compressible":false,"extensions":["oga","ogg","spx","opus"]},"audio/opus":{"source":"iana"},"audio/parityfec":{"source":"iana"},"audio/pcma":{"source":"iana"},"audio/pcma-wb":{"source":"iana"},"audio/pcmu":{"source":"iana"},"audio/pcmu-wb":{"source":"iana"},"audio/prs.sid":{"source":"iana"},"audio/qcelp":{"source":"iana"},"audio/raptorfec":{"source":"iana"},"audio/red":{"source":"iana"},"audio/rtp-enc-aescm128":{"source":"iana"},"audio/rtp-midi":{"source":"iana"},"audio/rtploopback":{"source":"iana"},"audio/rtx":{"source":"iana"},"audio/s3m":{"source":"apache","extensions":["s3m"]},"audio/scip":{"source":"iana"},"audio/silk":{"source":"apache","extensions":["sil"]},"audio/smv":{"source":"iana"},"audio/smv-qcp":{"source":"iana"},"audio/smv0":{"source":"iana"},"audio/sofa":{"source":"iana"},"audio/sp-midi":{"source":"iana"},"audio/speex":{"source":"iana"},"audio/t140c":{"source":"iana"},"audio/t38":{"source":"iana"},"audio/telephone-event":{"source":"iana"},"audio/tetra_acelp":{"source":"iana"},"audio/tetra_acelp_bb":{"source":"iana"},"audio/tone":{"source":"iana"},"audio/tsvcis":{"source":"iana"},"audio/uemclip":{"source":"iana"},"audio/ulpfec":{"source":"iana"},"audio/usac":{"source":"iana"},"audio/vdvi":{"source":"iana"},"audio/vmr-wb":{"source":"iana"},"audio/vnd.3gpp.iufp":{"source":"iana"},"audio/vnd.4sb":{"source":"iana"},"audio/vnd.audiokoz":{"source":"iana"},"audio/vnd.celp":{"source":"iana"},"audio/vnd.cisco.nse":{"source":"iana"},"audio/vnd.cmles.radio-events":{"source":"iana"},"audio/vnd.cns.anp1":{"source":"iana"},"audio/vnd.cns.inf1":{"source":"iana"},"audio/vnd.dece.audio":{"source":"iana","extensions":["uva","uvva"]},"audio/vnd.digital-winds":{"source":"iana","extensions":["eol"]},"audio/vnd.dlna.adts":{"source":"iana"},"audio/vnd.dolby.heaac.1":{"source":"iana"},"audio/vnd.dolby.heaac.2":{"source":"iana"},"audio/vnd.dolby.mlp":{"source":"iana"},"audio/vnd.dolby.mps":{"source":"iana"},"audio/vnd.dolby.pl2":{"source":"iana"},"audio/vnd.dolby.pl2x":{"source":"iana"},"audio/vnd.dolby.pl2z":{"source":"iana"},"audio/vnd.dolby.pulse.1":{"source":"iana"},"audio/vnd.dra":{"source":"iana","extensions":["dra"]},"audio/vnd.dts":{"source":"iana","extensions":["dts"]},"audio/vnd.dts.hd":{"source":"iana","extensions":["dtshd"]},"audio/vnd.dts.uhd":{"source":"iana"},"audio/vnd.dvb.file":{"source":"iana"},"audio/vnd.everad.plj":{"source":"iana"},"audio/vnd.hns.audio":{"source":"iana"},"audio/vnd.lucent.voice":{"source":"iana","extensions":["lvp"]},"audio/vnd.ms-playready.media.pya":{"source":"iana","extensions":["pya"]},"audio/vnd.nokia.mobile-xmf":{"source":"iana"},"audio/vnd.nortel.vbk":{"source":"iana"},"audio/vnd.nuera.ecelp4800":{"source":"iana","extensions":["ecelp4800"]},"audio/vnd.nuera.ecelp7470":{"source":"iana","extensions":["ecelp7470"]},"audio/vnd.nuera.ecelp9600":{"source":"iana","extensions":["ecelp9600"]},"audio/vnd.octel.sbc":{"source":"iana"},"audio/vnd.presonus.multitrack":{"source":"iana"},"audio/vnd.qcelp":{"source":"iana"},"audio/vnd.rhetorex.32kadpcm":{"source":"iana"},"audio/vnd.rip":{"source":"iana","extensions":["rip"]},"audio/vnd.rn-realaudio":{"compressible":false},"audio/vnd.sealedmedia.softseal.mpeg":{"source":"iana"},"audio/vnd.vmx.cvsd":{"source":"iana"},"audio/vnd.wave":{"compressible":false},"audio/vorbis":{"source":"iana","compressible":false},"audio/vorbis-config":{"source":"iana"},"audio/wav":{"compressible":false,"extensions":["wav"]},"audio/wave":{"compressible":false,"extensions":["wav"]},"audio/webm":{"source":"apache","compressible":false,"extensions":["weba"]},"audio/x-aac":{"source":"apache","compressible":false,"extensions":["aac"]},"audio/x-aiff":{"source":"apache","extensions":["aif","aiff","aifc"]},"audio/x-caf":{"source":"apache","compressible":false,"extensions":["caf"]},"audio/x-flac":{"source":"apache","extensions":["flac"]},"audio/x-m4a":{"source":"nginx","extensions":["m4a"]},"audio/x-matroska":{"source":"apache","extensions":["mka"]},"audio/x-mpegurl":{"source":"apache","extensions":["m3u"]},"audio/x-ms-wax":{"source":"apache","extensions":["wax"]},"audio/x-ms-wma":{"source":"apache","extensions":["wma"]},"audio/x-pn-realaudio":{"source":"apache","extensions":["ram","ra"]},"audio/x-pn-realaudio-plugin":{"source":"apache","extensions":["rmp"]},"audio/x-realaudio":{"source":"nginx","extensions":["ra"]},"audio/x-tta":{"source":"apache"},"audio/x-wav":{"source":"apache","extensions":["wav"]},"audio/xm":{"source":"apache","extensions":["xm"]},"chemical/x-cdx":{"source":"apache","extensions":["cdx"]},"chemical/x-cif":{"source":"apache","extensions":["cif"]},"chemical/x-cmdf":{"source":"apache","extensions":["cmdf"]},"chemical/x-cml":{"source":"apache","extensions":["cml"]},"chemical/x-csml":{"source":"apache","extensions":["csml"]},"chemical/x-pdb":{"source":"apache"},"chemical/x-xyz":{"source":"apache","extensions":["xyz"]},"font/collection":{"source":"iana","extensions":["ttc"]},"font/otf":{"source":"iana","compressible":true,"extensions":["otf"]},"font/sfnt":{"source":"iana"},"font/ttf":{"source":"iana","compressible":true,"extensions":["ttf"]},"font/woff":{"source":"iana","extensions":["woff"]},"font/woff2":{"source":"iana","extensions":["woff2"]},"image/aces":{"source":"iana","extensions":["exr"]},"image/apng":{"compressible":false,"extensions":["apng"]},"image/avci":{"source":"iana","extensions":["avci"]},"image/avcs":{"source":"iana","extensions":["avcs"]},"image/avif":{"source":"iana","compressible":false,"extensions":["avif"]},"image/bmp":{"source":"iana","compressible":true,"extensions":["bmp"]},"image/cgm":{"source":"iana","extensions":["cgm"]},"image/dicom-rle":{"source":"iana","extensions":["drle"]},"image/emf":{"source":"iana","extensions":["emf"]},"image/fits":{"source":"iana","extensions":["fits"]},"image/g3fax":{"source":"iana","extensions":["g3"]},"image/gif":{"source":"iana","compressible":false,"extensions":["gif"]},"image/heic":{"source":"iana","extensions":["heic"]},"image/heic-sequence":{"source":"iana","extensions":["heics"]},"image/heif":{"source":"iana","extensions":["heif"]},"image/heif-sequence":{"source":"iana","extensions":["heifs"]},"image/hej2k":{"source":"iana","extensions":["hej2"]},"image/hsj2":{"source":"iana","extensions":["hsj2"]},"image/ief":{"source":"iana","extensions":["ief"]},"image/jls":{"source":"iana","extensions":["jls"]},"image/jp2":{"source":"iana","compressible":false,"extensions":["jp2","jpg2"]},"image/jpeg":{"source":"iana","compressible":false,"extensions":["jpeg","jpg","jpe"]},"image/jph":{"source":"iana","extensions":["jph"]},"image/jphc":{"source":"iana","extensions":["jhc"]},"image/jpm":{"source":"iana","compressible":false,"extensions":["jpm"]},"image/jpx":{"source":"iana","compressible":false,"extensions":["jpx","jpf"]},"image/jxr":{"source":"iana","extensions":["jxr"]},"image/jxra":{"source":"iana","extensions":["jxra"]},"image/jxrs":{"source":"iana","extensions":["jxrs"]},"image/jxs":{"source":"iana","extensions":["jxs"]},"image/jxsc":{"source":"iana","extensions":["jxsc"]},"image/jxsi":{"source":"iana","extensions":["jxsi"]},"image/jxss":{"source":"iana","extensions":["jxss"]},"image/ktx":{"source":"iana","extensions":["ktx"]},"image/ktx2":{"source":"iana","extensions":["ktx2"]},"image/naplps":{"source":"iana"},"image/pjpeg":{"compressible":false},"image/png":{"source":"iana","compressible":false,"extensions":["png"]},"image/prs.btif":{"source":"iana","extensions":["btif"]},"image/prs.pti":{"source":"iana","extensions":["pti"]},"image/pwg-raster":{"source":"iana"},"image/sgi":{"source":"apache","extensions":["sgi"]},"image/svg+xml":{"source":"iana","compressible":true,"extensions":["svg","svgz"]},"image/t38":{"source":"iana","extensions":["t38"]},"image/tiff":{"source":"iana","compressible":false,"extensions":["tif","tiff"]},"image/tiff-fx":{"source":"iana","extensions":["tfx"]},"image/vnd.adobe.photoshop":{"source":"iana","compressible":true,"extensions":["psd"]},"image/vnd.airzip.accelerator.azv":{"source":"iana","extensions":["azv"]},"image/vnd.cns.inf2":{"source":"iana"},"image/vnd.dece.graphic":{"source":"iana","extensions":["uvi","uvvi","uvg","uvvg"]},"image/vnd.djvu":{"source":"iana","extensions":["djvu","djv"]},"image/vnd.dvb.subtitle":{"source":"iana","extensions":["sub"]},"image/vnd.dwg":{"source":"iana","extensions":["dwg"]},"image/vnd.dxf":{"source":"iana","extensions":["dxf"]},"image/vnd.fastbidsheet":{"source":"iana","extensions":["fbs"]},"image/vnd.fpx":{"source":"iana","extensions":["fpx"]},"image/vnd.fst":{"source":"iana","extensions":["fst"]},"image/vnd.fujixerox.edmics-mmr":{"source":"iana","extensions":["mmr"]},"image/vnd.fujixerox.edmics-rlc":{"source":"iana","extensions":["rlc"]},"image/vnd.globalgraphics.pgb":{"source":"iana"},"image/vnd.microsoft.icon":{"source":"iana","compressible":true,"extensions":["ico"]},"image/vnd.mix":{"source":"iana"},"image/vnd.mozilla.apng":{"source":"iana"},"image/vnd.ms-dds":{"compressible":true,"extensions":["dds"]},"image/vnd.ms-modi":{"source":"iana","extensions":["mdi"]},"image/vnd.ms-photo":{"source":"apache","extensions":["wdp"]},"image/vnd.net-fpx":{"source":"iana","extensions":["npx"]},"image/vnd.pco.b16":{"source":"iana","extensions":["b16"]},"image/vnd.radiance":{"source":"iana"},"image/vnd.sealed.png":{"source":"iana"},"image/vnd.sealedmedia.softseal.gif":{"source":"iana"},"image/vnd.sealedmedia.softseal.jpg":{"source":"iana"},"image/vnd.svf":{"source":"iana"},"image/vnd.tencent.tap":{"source":"iana","extensions":["tap"]},"image/vnd.valve.source.texture":{"source":"iana","extensions":["vtf"]},"image/vnd.wap.wbmp":{"source":"iana","extensions":["wbmp"]},"image/vnd.xiff":{"source":"iana","extensions":["xif"]},"image/vnd.zbrush.pcx":{"source":"iana","extensions":["pcx"]},"image/webp":{"source":"apache","extensions":["webp"]},"image/wmf":{"source":"iana","extensions":["wmf"]},"image/x-3ds":{"source":"apache","extensions":["3ds"]},"image/x-cmu-raster":{"source":"apache","extensions":["ras"]},"image/x-cmx":{"source":"apache","extensions":["cmx"]},"image/x-freehand":{"source":"apache","extensions":["fh","fhc","fh4","fh5","fh7"]},"image/x-icon":{"source":"apache","compressible":true,"extensions":["ico"]},"image/x-jng":{"source":"nginx","extensions":["jng"]},"image/x-mrsid-image":{"source":"apache","extensions":["sid"]},"image/x-ms-bmp":{"source":"nginx","compressible":true,"extensions":["bmp"]},"image/x-pcx":{"source":"apache","extensions":["pcx"]},"image/x-pict":{"source":"apache","extensions":["pic","pct"]},"image/x-portable-anymap":{"source":"apache","extensions":["pnm"]},"image/x-portable-bitmap":{"source":"apache","extensions":["pbm"]},"image/x-portable-graymap":{"source":"apache","extensions":["pgm"]},"image/x-portable-pixmap":{"source":"apache","extensions":["ppm"]},"image/x-rgb":{"source":"apache","extensions":["rgb"]},"image/x-tga":{"source":"apache","extensions":["tga"]},"image/x-xbitmap":{"source":"apache","extensions":["xbm"]},"image/x-xcf":{"compressible":false},"image/x-xpixmap":{"source":"apache","extensions":["xpm"]},"image/x-xwindowdump":{"source":"apache","extensions":["xwd"]},"message/cpim":{"source":"iana"},"message/delivery-status":{"source":"iana"},"message/disposition-notification":{"source":"iana","extensions":["disposition-notification"]},"message/external-body":{"source":"iana"},"message/feedback-report":{"source":"iana"},"message/global":{"source":"iana","extensions":["u8msg"]},"message/global-delivery-status":{"source":"iana","extensions":["u8dsn"]},"message/global-disposition-notification":{"source":"iana","extensions":["u8mdn"]},"message/global-headers":{"source":"iana","extensions":["u8hdr"]},"message/http":{"source":"iana","compressible":false},"message/imdn+xml":{"source":"iana","compressible":true},"message/news":{"source":"iana"},"message/partial":{"source":"iana","compressible":false},"message/rfc822":{"source":"iana","compressible":true,"extensions":["eml","mime"]},"message/s-http":{"source":"iana"},"message/sip":{"source":"iana"},"message/sipfrag":{"source":"iana"},"message/tracking-status":{"source":"iana"},"message/vnd.si.simp":{"source":"iana"},"message/vnd.wfa.wsc":{"source":"iana","extensions":["wsc"]},"model/3mf":{"source":"iana","extensions":["3mf"]},"model/e57":{"source":"iana"},"model/gltf+json":{"source":"iana","compressible":true,"extensions":["gltf"]},"model/gltf-binary":{"source":"iana","compressible":true,"extensions":["glb"]},"model/iges":{"source":"iana","compressible":false,"extensions":["igs","iges"]},"model/mesh":{"source":"iana","compressible":false,"extensions":["msh","mesh","silo"]},"model/mtl":{"source":"iana","extensions":["mtl"]},"model/obj":{"source":"iana","extensions":["obj"]},"model/step":{"source":"iana"},"model/step+xml":{"source":"iana","compressible":true,"extensions":["stpx"]},"model/step+zip":{"source":"iana","compressible":false,"extensions":["stpz"]},"model/step-xml+zip":{"source":"iana","compressible":false,"extensions":["stpxz"]},"model/stl":{"source":"iana","extensions":["stl"]},"model/vnd.collada+xml":{"source":"iana","compressible":true,"extensions":["dae"]},"model/vnd.dwf":{"source":"iana","extensions":["dwf"]},"model/vnd.flatland.3dml":{"source":"iana"},"model/vnd.gdl":{"source":"iana","extensions":["gdl"]},"model/vnd.gs-gdl":{"source":"apache"},"model/vnd.gs.gdl":{"source":"iana"},"model/vnd.gtw":{"source":"iana","extensions":["gtw"]},"model/vnd.moml+xml":{"source":"iana","compressible":true},"model/vnd.mts":{"source":"iana","extensions":["mts"]},"model/vnd.opengex":{"source":"iana","extensions":["ogex"]},"model/vnd.parasolid.transmit.binary":{"source":"iana","extensions":["x_b"]},"model/vnd.parasolid.transmit.text":{"source":"iana","extensions":["x_t"]},"model/vnd.pytha.pyox":{"source":"iana"},"model/vnd.rosette.annotated-data-model":{"source":"iana"},"model/vnd.sap.vds":{"source":"iana","extensions":["vds"]},"model/vnd.usdz+zip":{"source":"iana","compressible":false,"extensions":["usdz"]},"model/vnd.valve.source.compiled-map":{"source":"iana","extensions":["bsp"]},"model/vnd.vtu":{"source":"iana","extensions":["vtu"]},"model/vrml":{"source":"iana","compressible":false,"extensions":["wrl","vrml"]},"model/x3d+binary":{"source":"apache","compressible":false,"extensions":["x3db","x3dbz"]},"model/x3d+fastinfoset":{"source":"iana","extensions":["x3db"]},"model/x3d+vrml":{"source":"apache","compressible":false,"extensions":["x3dv","x3dvz"]},"model/x3d+xml":{"source":"iana","compressible":true,"extensions":["x3d","x3dz"]},"model/x3d-vrml":{"source":"iana","extensions":["x3dv"]},"multipart/alternative":{"source":"iana","compressible":false},"multipart/appledouble":{"source":"iana"},"multipart/byteranges":{"source":"iana"},"multipart/digest":{"source":"iana"},"multipart/encrypted":{"source":"iana","compressible":false},"multipart/form-data":{"source":"iana","compressible":false},"multipart/header-set":{"source":"iana"},"multipart/mixed":{"source":"iana"},"multipart/multilingual":{"source":"iana"},"multipart/parallel":{"source":"iana"},"multipart/related":{"source":"iana","compressible":false},"multipart/report":{"source":"iana"},"multipart/signed":{"source":"iana","compressible":false},"multipart/vnd.bint.med-plus":{"source":"iana"},"multipart/voice-message":{"source":"iana"},"multipart/x-mixed-replace":{"source":"iana"},"text/1d-interleaved-parityfec":{"source":"iana"},"text/cache-manifest":{"source":"iana","compressible":true,"extensions":["appcache","manifest"]},"text/calendar":{"source":"iana","extensions":["ics","ifb"]},"text/calender":{"compressible":true},"text/cmd":{"compressible":true},"text/coffeescript":{"extensions":["coffee","litcoffee"]},"text/cql":{"source":"iana"},"text/cql-expression":{"source":"iana"},"text/cql-identifier":{"source":"iana"},"text/css":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["css"]},"text/csv":{"source":"iana","compressible":true,"extensions":["csv"]},"text/csv-schema":{"source":"iana"},"text/directory":{"source":"iana"},"text/dns":{"source":"iana"},"text/ecmascript":{"source":"iana"},"text/encaprtp":{"source":"iana"},"text/enriched":{"source":"iana"},"text/fhirpath":{"source":"iana"},"text/flexfec":{"source":"iana"},"text/fwdred":{"source":"iana"},"text/gff3":{"source":"iana"},"text/grammar-ref-list":{"source":"iana"},"text/html":{"source":"iana","compressible":true,"extensions":["html","htm","shtml"]},"text/jade":{"extensions":["jade"]},"text/javascript":{"source":"iana","compressible":true},"text/jcr-cnd":{"source":"iana"},"text/jsx":{"compressible":true,"extensions":["jsx"]},"text/less":{"compressible":true,"extensions":["less"]},"text/markdown":{"source":"iana","compressible":true,"extensions":["markdown","md"]},"text/mathml":{"source":"nginx","extensions":["mml"]},"text/mdx":{"compressible":true,"extensions":["mdx"]},"text/mizar":{"source":"iana"},"text/n3":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["n3"]},"text/parameters":{"source":"iana","charset":"UTF-8"},"text/parityfec":{"source":"iana"},"text/plain":{"source":"iana","compressible":true,"extensions":["txt","text","conf","def","list","log","in","ini"]},"text/provenance-notation":{"source":"iana","charset":"UTF-8"},"text/prs.fallenstein.rst":{"source":"iana"},"text/prs.lines.tag":{"source":"iana","extensions":["dsc"]},"text/prs.prop.logic":{"source":"iana"},"text/raptorfec":{"source":"iana"},"text/red":{"source":"iana"},"text/rfc822-headers":{"source":"iana"},"text/richtext":{"source":"iana","compressible":true,"extensions":["rtx"]},"text/rtf":{"source":"iana","compressible":true,"extensions":["rtf"]},"text/rtp-enc-aescm128":{"source":"iana"},"text/rtploopback":{"source":"iana"},"text/rtx":{"source":"iana"},"text/sgml":{"source":"iana","extensions":["sgml","sgm"]},"text/shaclc":{"source":"iana"},"text/shex":{"source":"iana","extensions":["shex"]},"text/slim":{"extensions":["slim","slm"]},"text/spdx":{"source":"iana","extensions":["spdx"]},"text/strings":{"source":"iana"},"text/stylus":{"extensions":["stylus","styl"]},"text/t140":{"source":"iana"},"text/tab-separated-values":{"source":"iana","compressible":true,"extensions":["tsv"]},"text/troff":{"source":"iana","extensions":["t","tr","roff","man","me","ms"]},"text/turtle":{"source":"iana","charset":"UTF-8","extensions":["ttl"]},"text/ulpfec":{"source":"iana"},"text/uri-list":{"source":"iana","compressible":true,"extensions":["uri","uris","urls"]},"text/vcard":{"source":"iana","compressible":true,"extensions":["vcard"]},"text/vnd.a":{"source":"iana"},"text/vnd.abc":{"source":"iana"},"text/vnd.ascii-art":{"source":"iana"},"text/vnd.curl":{"source":"iana","extensions":["curl"]},"text/vnd.curl.dcurl":{"source":"apache","extensions":["dcurl"]},"text/vnd.curl.mcurl":{"source":"apache","extensions":["mcurl"]},"text/vnd.curl.scurl":{"source":"apache","extensions":["scurl"]},"text/vnd.debian.copyright":{"source":"iana","charset":"UTF-8"},"text/vnd.dmclientscript":{"source":"iana"},"text/vnd.dvb.subtitle":{"source":"iana","extensions":["sub"]},"text/vnd.esmertec.theme-descriptor":{"source":"iana","charset":"UTF-8"},"text/vnd.familysearch.gedcom":{"source":"iana","extensions":["ged"]},"text/vnd.ficlab.flt":{"source":"iana"},"text/vnd.fly":{"source":"iana","extensions":["fly"]},"text/vnd.fmi.flexstor":{"source":"iana","extensions":["flx"]},"text/vnd.gml":{"source":"iana"},"text/vnd.graphviz":{"source":"iana","extensions":["gv"]},"text/vnd.hans":{"source":"iana"},"text/vnd.hgl":{"source":"iana"},"text/vnd.in3d.3dml":{"source":"iana","extensions":["3dml"]},"text/vnd.in3d.spot":{"source":"iana","extensions":["spot"]},"text/vnd.iptc.newsml":{"source":"iana"},"text/vnd.iptc.nitf":{"source":"iana"},"text/vnd.latex-z":{"source":"iana"},"text/vnd.motorola.reflex":{"source":"iana"},"text/vnd.ms-mediapackage":{"source":"iana"},"text/vnd.net2phone.commcenter.command":{"source":"iana"},"text/vnd.radisys.msml-basic-layout":{"source":"iana"},"text/vnd.senx.warpscript":{"source":"iana"},"text/vnd.si.uricatalogue":{"source":"iana"},"text/vnd.sosi":{"source":"iana"},"text/vnd.sun.j2me.app-descriptor":{"source":"iana","charset":"UTF-8","extensions":["jad"]},"text/vnd.trolltech.linguist":{"source":"iana","charset":"UTF-8"},"text/vnd.wap.si":{"source":"iana"},"text/vnd.wap.sl":{"source":"iana"},"text/vnd.wap.wml":{"source":"iana","extensions":["wml"]},"text/vnd.wap.wmlscript":{"source":"iana","extensions":["wmls"]},"text/vtt":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["vtt"]},"text/x-asm":{"source":"apache","extensions":["s","asm"]},"text/x-c":{"source":"apache","extensions":["c","cc","cxx","cpp","h","hh","dic"]},"text/x-component":{"source":"nginx","extensions":["htc"]},"text/x-fortran":{"source":"apache","extensions":["f","for","f77","f90"]},"text/x-gwt-rpc":{"compressible":true},"text/x-handlebars-template":{"extensions":["hbs"]},"text/x-java-source":{"source":"apache","extensions":["java"]},"text/x-jquery-tmpl":{"compressible":true},"text/x-lua":{"extensions":["lua"]},"text/x-markdown":{"compressible":true,"extensions":["mkd"]},"text/x-nfo":{"source":"apache","extensions":["nfo"]},"text/x-opml":{"source":"apache","extensions":["opml"]},"text/x-org":{"compressible":true,"extensions":["org"]},"text/x-pascal":{"source":"apache","extensions":["p","pas"]},"text/x-processing":{"compressible":true,"extensions":["pde"]},"text/x-sass":{"extensions":["sass"]},"text/x-scss":{"extensions":["scss"]},"text/x-setext":{"source":"apache","extensions":["etx"]},"text/x-sfv":{"source":"apache","extensions":["sfv"]},"text/x-suse-ymp":{"compressible":true,"extensions":["ymp"]},"text/x-uuencode":{"source":"apache","extensions":["uu"]},"text/x-vcalendar":{"source":"apache","extensions":["vcs"]},"text/x-vcard":{"source":"apache","extensions":["vcf"]},"text/xml":{"source":"iana","compressible":true,"extensions":["xml"]},"text/xml-external-parsed-entity":{"source":"iana"},"text/yaml":{"compressible":true,"extensions":["yaml","yml"]},"video/1d-interleaved-parityfec":{"source":"iana"},"video/3gpp":{"source":"iana","extensions":["3gp","3gpp"]},"video/3gpp-tt":{"source":"iana"},"video/3gpp2":{"source":"iana","extensions":["3g2"]},"video/av1":{"source":"iana"},"video/bmpeg":{"source":"iana"},"video/bt656":{"source":"iana"},"video/celb":{"source":"iana"},"video/dv":{"source":"iana"},"video/encaprtp":{"source":"iana"},"video/ffv1":{"source":"iana"},"video/flexfec":{"source":"iana"},"video/h261":{"source":"iana","extensions":["h261"]},"video/h263":{"source":"iana","extensions":["h263"]},"video/h263-1998":{"source":"iana"},"video/h263-2000":{"source":"iana"},"video/h264":{"source":"iana","extensions":["h264"]},"video/h264-rcdo":{"source":"iana"},"video/h264-svc":{"source":"iana"},"video/h265":{"source":"iana"},"video/iso.segment":{"source":"iana","extensions":["m4s"]},"video/jpeg":{"source":"iana","extensions":["jpgv"]},"video/jpeg2000":{"source":"iana"},"video/jpm":{"source":"apache","extensions":["jpm","jpgm"]},"video/jxsv":{"source":"iana"},"video/mj2":{"source":"iana","extensions":["mj2","mjp2"]},"video/mp1s":{"source":"iana"},"video/mp2p":{"source":"iana"},"video/mp2t":{"source":"iana","extensions":["ts"]},"video/mp4":{"source":"iana","compressible":false,"extensions":["mp4","mp4v","mpg4"]},"video/mp4v-es":{"source":"iana"},"video/mpeg":{"source":"iana","compressible":false,"extensions":["mpeg","mpg","mpe","m1v","m2v"]},"video/mpeg4-generic":{"source":"iana"},"video/mpv":{"source":"iana"},"video/nv":{"source":"iana"},"video/ogg":{"source":"iana","compressible":false,"extensions":["ogv"]},"video/parityfec":{"source":"iana"},"video/pointer":{"source":"iana"},"video/quicktime":{"source":"iana","compressible":false,"extensions":["qt","mov"]},"video/raptorfec":{"source":"iana"},"video/raw":{"source":"iana"},"video/rtp-enc-aescm128":{"source":"iana"},"video/rtploopback":{"source":"iana"},"video/rtx":{"source":"iana"},"video/scip":{"source":"iana"},"video/smpte291":{"source":"iana"},"video/smpte292m":{"source":"iana"},"video/ulpfec":{"source":"iana"},"video/vc1":{"source":"iana"},"video/vc2":{"source":"iana"},"video/vnd.cctv":{"source":"iana"},"video/vnd.dece.hd":{"source":"iana","extensions":["uvh","uvvh"]},"video/vnd.dece.mobile":{"source":"iana","extensions":["uvm","uvvm"]},"video/vnd.dece.mp4":{"source":"iana"},"video/vnd.dece.pd":{"source":"iana","extensions":["uvp","uvvp"]},"video/vnd.dece.sd":{"source":"iana","extensions":["uvs","uvvs"]},"video/vnd.dece.video":{"source":"iana","extensions":["uvv","uvvv"]},"video/vnd.directv.mpeg":{"source":"iana"},"video/vnd.directv.mpeg-tts":{"source":"iana"},"video/vnd.dlna.mpeg-tts":{"source":"iana"},"video/vnd.dvb.file":{"source":"iana","extensions":["dvb"]},"video/vnd.fvt":{"source":"iana","extensions":["fvt"]},"video/vnd.hns.video":{"source":"iana"},"video/vnd.iptvforum.1dparityfec-1010":{"source":"iana"},"video/vnd.iptvforum.1dparityfec-2005":{"source":"iana"},"video/vnd.iptvforum.2dparityfec-1010":{"source":"iana"},"video/vnd.iptvforum.2dparityfec-2005":{"source":"iana"},"video/vnd.iptvforum.ttsavc":{"source":"iana"},"video/vnd.iptvforum.ttsmpeg2":{"source":"iana"},"video/vnd.motorola.video":{"source":"iana"},"video/vnd.motorola.videop":{"source":"iana"},"video/vnd.mpegurl":{"source":"iana","extensions":["mxu","m4u"]},"video/vnd.ms-playready.media.pyv":{"source":"iana","extensions":["pyv"]},"video/vnd.nokia.interleaved-multimedia":{"source":"iana"},"video/vnd.nokia.mp4vr":{"source":"iana"},"video/vnd.nokia.videovoip":{"source":"iana"},"video/vnd.objectvideo":{"source":"iana"},"video/vnd.radgamettools.bink":{"source":"iana"},"video/vnd.radgamettools.smacker":{"source":"iana"},"video/vnd.sealed.mpeg1":{"source":"iana"},"video/vnd.sealed.mpeg4":{"source":"iana"},"video/vnd.sealed.swf":{"source":"iana"},"video/vnd.sealedmedia.softseal.mov":{"source":"iana"},"video/vnd.uvvu.mp4":{"source":"iana","extensions":["uvu","uvvu"]},"video/vnd.vivo":{"source":"iana","extensions":["viv"]},"video/vnd.youtube.yt":{"source":"iana"},"video/vp8":{"source":"iana"},"video/vp9":{"source":"iana"},"video/webm":{"source":"apache","compressible":false,"extensions":["webm"]},"video/x-f4v":{"source":"apache","extensions":["f4v"]},"video/x-fli":{"source":"apache","extensions":["fli"]},"video/x-flv":{"source":"apache","compressible":false,"extensions":["flv"]},"video/x-m4v":{"source":"apache","extensions":["m4v"]},"video/x-matroska":{"source":"apache","compressible":false,"extensions":["mkv","mk3d","mks"]},"video/x-mng":{"source":"apache","extensions":["mng"]},"video/x-ms-asf":{"source":"apache","extensions":["asf","asx"]},"video/x-ms-vob":{"source":"apache","extensions":["vob"]},"video/x-ms-wm":{"source":"apache","extensions":["wm"]},"video/x-ms-wmv":{"source":"apache","compressible":false,"extensions":["wmv"]},"video/x-ms-wmx":{"source":"apache","extensions":["wmx"]},"video/x-ms-wvx":{"source":"apache","extensions":["wvx"]},"video/x-msvideo":{"source":"apache","extensions":["avi"]},"video/x-sgi-movie":{"source":"apache","extensions":["movie"]},"video/x-smv":{"source":"apache","extensions":["smv"]},"x-conference/x-cooltalk":{"source":"apache","extensions":["ice"]},"x-shader/x-fragment":{"compressible":true},"x-shader/x-vertex":{"compressible":true}}')}};