import { cookies } from "next/headers";
import HeroTermsOfUseSection from "./hero";
import { getLocale, getTranslations } from "next-intl/server";
import { getPrivacyPolictContent } from "@/core/services/sanity/services";
import Content from "./content";
import { Metadata } from "next";
import { privacySeekerUrl } from "@/lib/constanta/route";
import { routing } from "@/lib/locale/routing";

export async function generateMetadata(): Promise<Metadata> {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const t = await getTranslations("seeker")
  const baseUrl = process.env.USER_DOMAIN || "https://www.property-plaza.com/"
  const locale = await getLocale() || routing.defaultLocale


  return {
    title: t('metadata.privacyPolicy.title'),
    description: t('metadata.privacyPolicy.description'),
    alternates: {
      canonical: baseUrl + locale + privacySeekerUrl,
      languages: {
        "id": baseUrl + `id${privacySeekerUrl}`,
        "en": baseUrl + `en${privacySeekerUrl}`,
        "x-default": baseUrl + privacySeekerUrl.replace("/", ""),
      },
    },
    openGraph: {
      title: t('metadata.privacyPolicy.title'),
      description: t('metadata.privacyPolicy.description'),
      images: [
        {
          url: baseUrl + "og.png",
          width: 1200,
          height: 630,
          alt: "Property Plaza",
        }
      ],
      type: "website",
      url: baseUrl + privacySeekerUrl.replace("/", ""),
      countryName: "Indonesia",
      emails: "<EMAIL>",
      locale: locale,
      alternateLocale: routing.locales,
      siteName: "Property plaza",
    },
    applicationName: "Property plaza",
    icons: ["favicon.ico"],

    twitter: {
      card: "summary_large_image",
      title: t('metadata.privacyPolicy.description'),
      description: t('metadata.privacyPolicy.description'),
      images: [baseUrl + "og.jpg"],
    },
    robots: {
      index: true,
      follow: true
    },
  }
}

export default async function PrivacyPololicyPage() {
  const cookiesStore = cookies()
  const locale = cookiesStore.get('NEXT_LOCALE')?.value
  const t = await getTranslations("seeker")
  // const termOfUseContent = await getTermsOfUseContent(locale?.toLocaleLowerCase() || "en") // use this when locale for content are implemented
  const termOfUseContent = await getPrivacyPolictContent("en")
  return <>
    <HeroTermsOfUseSection />
    <Content content={termOfUseContent[0]} />
  </>
}