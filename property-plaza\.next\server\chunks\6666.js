exports.id=6666,exports.ids=[6666],exports.modules={56997:(e,t,a)=>{var s={"./en.json":[41806,1806],"./id.json":[27001,7001],"./nl.json":[27172,7172]};function i(e){if(!a.o(s,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=s[e],i=t[0];return a.e(t[1]).then(()=>a.t(i,19))}i.keys=()=>Object.keys(s),i.id=56997,e.exports=i},56804:()=>{},68645:()=>{},45033:(e,t,a)=>{Promise.resolve().then(a.bind(a,65218)),Promise.resolve().then(a.bind(a,72265)),Promise.resolve().then(a.bind(a,55169)),Promise.resolve().then(a.bind(a,19609)),Promise.resolve().then(a.bind(a,91860)),Promise.resolve().then(a.bind(a,75354)),Promise.resolve().then(a.bind(a,85805)),Promise.resolve().then(a.bind(a,26793)),Promise.resolve().then(a.bind(a,70697)),Promise.resolve().then(a.bind(a,92941)),Promise.resolve().then(a.bind(a,46702)),Promise.resolve().then(a.bind(a,95630)),Promise.resolve().then(a.bind(a,77649)),Promise.resolve().then(a.bind(a,44955)),Promise.resolve().then(a.t.bind(a,59838,23))},3192:(e,t,a)=>{Promise.resolve().then(a.t.bind(a,63642,23)),Promise.resolve().then(a.t.bind(a,87586,23)),Promise.resolve().then(a.t.bind(a,47838,23)),Promise.resolve().then(a.t.bind(a,58057,23)),Promise.resolve().then(a.t.bind(a,77741,23)),Promise.resolve().then(a.t.bind(a,13118,23))},35303:()=>{},65218:(e,t,a)=>{"use strict";a.d(t,{default:()=>r});var s=a(97247),i=a(34178);function r(){return(0,i.usePathname)(),(0,i.useSearchParams)(),s.jsx(s.Fragment,{})}a(28964)},72265:(e,t,a)=>{"use strict";a.d(t,{default:()=>m});var s=a(97247),i=a(16718),r=a(97078),n=a(165),o=a(72266),l=a(46689),d=a(58053),c=a(34178),u=a(28964),p=a(80526);function m(){let e=(0,c.usePathname)();o.Z.get(i.t8);let[t,a]=(0,u.useState)(!1),[p,m]=(0,u.useState)(!1),[g,h]=(0,u.useState)({necessary:!0,functional:!1,analytic:!1,marketing:!1}),v=e=>{["all","necessary","custom"].includes(e)&&(o.Z.set(i.t8,"true"),"necessary"==e?h(e=>({...e,necessary:!0})):"all"==e&&h(e=>({analytic:!0,functional:!0,marketing:!0,necessary:!0})),o.Z.set(i.K6,g.necessary.toString()),o.Z.set(i.Ge,g.functional.toString()),o.Z.set(i.QY,g.analytic.toString()),o.Z.set(i.$_,g.marketing.toString()),a(!1))};return s.jsx(r.M,{children:!t||e.includes("create-password")&&e.includes("reset-password")?null:(0,s.jsxs)(n.E.div,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},exit:{y:20,opacity:0},transition:{duration:.7,ease:"easeInOut"},className:"fixed max-w-sm w-full bottom-4 left-4 bg-background p-8 z-20 shadow-md rounded-2xl space-y-4",children:[(0,s.jsxs)("p",{className:"inline-flex items-center gap-2 font-semibold",children:[s.jsx("span",{children:s.jsx(l.Z,{})}),"Manage Cookie Preferences"]}),p?(0,s.jsxs)("section",{className:"space-y-4",children:[s.jsx(f,{title:"Necessary",onValueChange:e=>h(t=>({...t,necessary:e})),description:"These cookies are essential for the website to function properly. They enable core functionalities such as security, network management, and accessibility. You cannot disable these cookies.",value:g.necessary,disabled:!0}),s.jsx(f,{title:"Analytics",onValueChange:e=>h(t=>({...t,analytic:e})),description:"These cookies allow the website to remember your preferences and provide enhanced features like saved language settings or embedded videos.",value:g.analytic}),s.jsx(f,{title:"Functional",onValueChange:e=>h(t=>({...t,functional:e})),description:"These cookies are essential for the website to function properly. They enable core functionalities such as security, network management, and accessibility. You cannot disable these cookies.",value:g.functional}),s.jsx(f,{title:"Marketing",onValueChange:e=>h(t=>({...t,marketing:e})),description:"These cookies are used to deliver relevant ads and track your activity across websites to personalize your advertising experience.",value:g.marketing})]}):s.jsx("section",{children:s.jsx("p",{className:"text-seekers-text-light",children:"We use cookies to optimize your experience on our website. You can choose which categories of cookies to allow below."})}),(0,s.jsxs)("div",{className:"inline-flex gap-2",children:[s.jsx(d.z,{onClick:()=>v("all"),children:"Accept all"}),p?s.jsx(d.z,{variant:"ghost",onClick:()=>v("custom"),children:"Save preferences"}):s.jsx(d.z,{variant:"ghost",onClick:()=>m(!0),children:"Manage preferences"})]})]})})}function f({title:e,value:t,disabled:a,description:i,onValueChange:r}){return(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[s.jsx("p",{className:"font-semibold",children:e}),s.jsx(p.r,{checked:t,disabled:a,onCheckedChange:e=>r(e)})]}),s.jsx("p",{className:"text-xs",children:i})]})}},55169:(e,t,a)=>{"use strict";a.d(t,{default:()=>r});var s=a(97247);a(34523),a(97246);var i=a(84879);function r(){return(0,i.useLocale)(),s.jsx(s.Fragment,{})}a(28964)},19609:(e,t,a)=>{"use strict";a.d(t,{default:()=>r});var s=a(97247),i=a(80197);function r({children:e}){return s.jsx(i.un,{apiKey:"AIzaSyCOm6xsEL7MViTvzxhjmP6BRWPpCdCOtgM",children:e})}},91860:(e,t,a)=>{"use strict";a.d(t,{default:()=>p});var s=a(97247);a(23734),a(30922);var i=a(10906),r=a(30250),n=a(69133),o=a(92377);let l=(0,n.Ue)()((0,o.tJ)(e=>({hasNotificationSound:void 0,setNotificationSound:t=>e({hasNotificationSound:t}),isLoading:!0,setIsLoading:t=>e({isLoading:t}),editingStatus:[],setEditingStatus:(t,a)=>e(e=>{let s=e.editingStatus;if("add"==a)return s.includes(t)?{...e}:(s.push(t),{...e,editingStatus:s});if("remove"==a){let a=s.filter(e=>e!==t);return{...e,editingStatus:a}}return{...e}}),removeEditingStatus:()=>e({editingStatus:[]})}),{name:"settings",storage:(0,o.FL)(()=>localStorage),onRehydrateStorage:()=>e=>{e&&e.setIsLoading(!1)}}));var d=a(28964);let c=()=>{let{setNotificationSound:e}=l(e=>e),[t,a]=(0,d.useState)(null);return(0,d.useEffect)(()=>{let e=new Audio;e.src="/sounds/notification.mp3",a(e)},[]),{enableSoundNotification:t=>{e(t)},playSound:()=>{let e=new Audio;e.src="/sounds/notification.mp3",e.volume=1,e.play().then(()=>{}).catch(e=>{console.error("sound error",e)})},popUpNotification:(e,t)=>{if(!("Notification"in window)){console.warn("This browser does not support desktop notifications.");return}"granted"===Notification.permission?new Notification(e,{body:t||""}):"default"===Notification.permission?Notification.requestPermission().then(a=>{"granted"===a?new Notification(e,{body:t||""}):console.warn("Notification permission denied.")}):"denied"===Notification.permission&&console.warn("Notifications are denied by the user.")}}};var u=a(84879);function p({isSeeker:e=!1}){(0,u.useTranslations)("seeker");let{toast:t}=(0,i.pm)(),[a,n]=(0,d.useState)(!1),{updatechatDetail:o,updateSpecificAllChat:p}=(0,r.R)(e=>e),{hasNotificationSound:m,isLoading:f}=l(e=>e),{enableSoundNotification:g,playSound:h,popUpNotification:v}=c();return s.jsx(s.Fragment,{})}},75354:(e,t,a)=>{"use strict";a.d(t,{default:()=>o});var s=a(97247),i=a(58797),r=a(41755);let n=new i.S;function o({children:e}){return s.jsx(r.aH,{client:n,children:e})}},58053:(e,t,a)=>{"use strict";a.d(t,{z:()=>c});var s=a(97247),i=a(28964),r=a(12341),n=a(87972),o=a(25008),l=a(8749);let d=(0,n.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-seekers-primary text-white shadow hover:bg-seekers-primary/90","default-seekers":"bg-seekers-primary text-white shadow hover:bg-seekers-primary-light/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),c=i.forwardRef(({className:e,variant:t,size:a,asChild:i=!1,loading:n=!1,...c},u)=>{let p=i?r.g7:"button";return s.jsx(p,{className:(0,o.cn)(d({variant:t,size:a,className:e})),ref:u,disabled:n||c.disabled,...c,children:n?s.jsx(l.Z,{className:(0,o.cn)("h-4 w-4 animate-spin")}):c.children})});c.displayName="Button"},80526:(e,t,a)=>{"use strict";a.d(t,{r:()=>o});var s=a(97247),i=a(28964),r=a(39609),n=a(25008);let o=i.forwardRef(({className:e,...t},a)=>s.jsx(r.fC,{className:(0,n.cn)("peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-seekers-primary data-[state=unchecked]:bg-input",e),...t,ref:a,children:s.jsx(r.bU,{className:(0,n.cn)("pointer-events-none block h-4 w-4 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0")})}));o.displayName=r.fC.displayName},85805:(e,t,a)=>{"use strict";a.d(t,{Toaster:()=>v});var s=a(97247),i=a(28964),r=a(2095),n=a(84710),o=a(87972),l=a(25008);let d=n.zt,c=i.forwardRef(({className:e,...t},a)=>s.jsx(n.l_,{ref:a,className:(0,l.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4   sm:right-0  sm:flex-col md:max-w-[420px]",e),...t}));c.displayName=n.l_.displayName;let u=(0,o.j)("group pointer-events-auto relative flex w-full items-center justify-between space-x-2 overflow-hidden rounded-md border p-4 pr-6 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background",destructive:"destructive group border-destructive bg-destructive/20 backdrop-blur-sm text-destructive"}},defaultVariants:{variant:"default"}}),p=i.forwardRef(({className:e,variant:t,...a},i)=>s.jsx(n.fC,{ref:i,className:(0,l.cn)(u({variant:t}),e),...a}));p.displayName=n.fC.displayName,i.forwardRef(({className:e,...t},a)=>s.jsx(n.aU,{ref:a,className:(0,l.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium transition-colors hover:bg-secondary focus:outline-none focus:ring-1 focus:ring-ring disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t})).displayName=n.aU.displayName;let m=i.forwardRef(({className:e,...t},a)=>s.jsx(n.x8,{ref:a,className:(0,l.cn)("absolute right-1 top-1 rounded-md p-1 text-primary-lighter opacity-0 transition-opacity hover:text-primary focus:opacity-100 focus:outline-none focus:ring-1 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:s.jsx(r.Pxu,{className:"h-4 w-4"})}));m.displayName=n.x8.displayName;let f=i.forwardRef(({className:e,...t},a)=>s.jsx(n.Dx,{ref:a,className:(0,l.cn)("text-sm font-semibold [&+div]:text-xs",e),...t}));f.displayName=n.Dx.displayName;let g=i.forwardRef(({className:e,...t},a)=>s.jsx(n.dk,{ref:a,className:(0,l.cn)("text-sm opacity-90",e),...t}));g.displayName=n.dk.displayName;var h=a(10906);function v(){let{toasts:e}=(0,h.pm)();return(0,s.jsxs)(d,{children:[e.map(function({id:e,title:t,description:a,action:i,...r}){return(0,s.jsxs)(p,{...r,children:[(0,s.jsxs)("div",{className:"grid gap-1",children:[t&&s.jsx(f,{children:t}),a&&s.jsx(g,{children:a})]}),i,s.jsx(m,{})]},e)}),s.jsx(c,{})]})}},23734:(e,t,a)=>{"use strict";a.d(t,{Z5:()=>o,eN:()=>l,ug:()=>n});var s=a(34523),i=a.n(s),r=a(96643);function n(e,t="en"){return e.map(e=>{let a=e.messages[0];if(a)return console.log(t,(0,r.P)(e.ref_data?.title,t)),{code:e.code,category:e.category,roomId:e.participants.room_id,lastMessages:{createdAt:a?.created_at||e.created_at,displayAs:a?.display_as||"",displayName:a?.display_name||"",text:a?.text||"",isRead:a?.is_read||!1,isSent:a?.is_send||!1,id:a?.id||"",code:a?.code||""},participant:{email:e.participants.info?.email||"",fullName:e.participants.info?.display_name||"",phoneNumber:e.participants.info?.phone_number||"",image:e.participants.info.image||"",id:e.participants.info?.id||"",category:e.category,status:e.status,property:{title:(0,r.P)(e.ref_data?.title,t)||void 0,image:e.ref_data?.images[0].image||void 0},otherProperty:[]},status:e.status,updatedAt:e.updated_at}}).filter(e=>void 0!==e).sort((e,t)=>i()(t.lastMessages.createdAt).unix()-i()(e.lastMessages.createdAt).unix())}function o(e){return{createdAt:e.created_at,displayAs:e.display_as,displayName:e.display_name,isRead:e.is_read,isSent:e.is_send,text:e.text,id:e.id,code:e.code}}function l(e,t="en"){console.log(e.messages);let a=e.messages[e.messages?.length-1]||void 0,s=e.messages.map(e=>({createdAt:e.created_at,displayAs:e.display_as,displayName:e.display_name,isRead:e.is_read,isSent:e.is_send,text:e.text,id:e.id,code:e.code})),i=e.ref_data?.extended_list?.map(e=>({id:e.code,image:e.images[0]?.image||"",title:r.P(e.title,t)}));return{code:e.code,category:e.category,roomId:e.participants.room_id,lastMessages:{createdAt:a.created_at,displayAs:a.display_as,displayName:a.display_name,text:a.text,isRead:a.is_read,isSent:a.is_send,id:a.id,code:a.code||""},participant:{email:e.participants.info?.email||"",fullName:e.participants.info?.display_name||"",phoneNumber:e.participants.info?.phone_number||"",image:e.participants.info?.image||"",id:e.participants.info?.id||"",category:e.category,status:e.status,property:{id:e.ref_data?.code||"",image:e.ref_data?.images[0]?.image||"",title:(0,r.P)(e.ref_data?.title,t)||""},moreProperty:i||[]},allMessages:s,updatedAt:e.updated_at}}},96643:(e,t,a)=>{"use strict";function s(e){return{nextPage:e.next_page,page:e.page,pageCount:e.page_count,perPage:e.per_page,prevPage:e.prev_page,total:e.total}}function i(e,t="en"){if(!e)return"";if("string"==typeof e)return e;let a=e.find(e=>e.lang===t);return a?.value||e[0].value}a.d(t,{N:()=>s,P:()=>i})},30922:(e,t,a)=>{"use strict";a.d(t,{W:()=>n});var s=a(46629),i=a(72266),r=a(16718);let n=(0,s.ZP)("https://dev.property-plaza.id/",{extraHeaders:{"auth-token":i.Z.get(r.LA)||""},autoConnect:!1})},10906:(e,t,a)=>{"use strict";a.d(t,{pm:()=>p});var s=a(28964);let i=0,r=new Map,n=e=>{if(r.has(e))return;let t=setTimeout(()=>{r.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);r.set(e,t)},o=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:a}=t;return a?n(a):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],d={toasts:[]};function c(e){d=o(d,e),l.forEach(e=>{e(d)})}function u({...e}){let t=(i=(i+1)%Number.MAX_SAFE_INTEGER).toString(),a=()=>c({type:"DISMISS_TOAST",toastId:t});return c({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||a()}}}),{id:t,dismiss:a,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function p(){let[e,t]=s.useState(d);return s.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},16718:(e,t,a)=>{"use strict";a.d(t,{$_:()=>f,Ge:()=>p,K6:()=>u,LA:()=>s,QY:()=>m,Y:()=>g,Z9:()=>r,ac:()=>o,gr:()=>i,nM:()=>n,t8:()=>c,vQ:()=>d,xm:()=>l});let s="tkn",i="SEEKER",r=8,n=1,o=30,l=300,d=10,c="cookies-collection-status",u="necessary-cookies-collection-status",p="functional-cookies-collection-status",m="analytic-cookies-collection-status",f="marketing-cookies-collection-status",g={type:"t",minPrice:"minp",maxPrice:"maxp",landLargest:"landl",landSmallest:"lands",buildingLargest:"buildl",buildingSmallest:"builds",gardenLargest:"gardenl",gardenSmallest:"gardens",yearsOfBuild:"yob",bedroomTotal:"bedt",bathroomTotal:"batht",rentalOffer:"ro",propertyCondition:"pc",electircity:"el",parking:"pk",swimmingPool:"sp",typeLiving:"tl",furnished:"fs",view:"v",minimumContract:"minc",category:"c",subCategory:"sc",feature:"feat",propertyLocation:"pl",zoom:"z",viewMode:"viewMode"}},25008:(e,t,a)=>{"use strict";a.d(t,{E6:()=>u,ET:()=>f,Fg:()=>m,cn:()=>o,g6:()=>p,uf:()=>c,xG:()=>d,yT:()=>g});var s=a(61929),i=a(34523),r=a.n(i),n=a(35770);function o(...e){return(0,n.m6)((0,s.W)(e))}let l=e=>{switch(e){case"USD":default:return"en-us";case"IDR":return"id-ID";case"GBP":return"en-GB";case"AUD":return"en-AU";case"EUR":return"nl-NL"}},d=(e,t="USD",a="en-US")=>new Intl.NumberFormat(l(t),{style:"currency",currency:t,minimumFractionDigits:0,maximumFractionDigits:"IDR"==t?0:2}).format(e),c=(e,t="en-US")=>new Intl.NumberFormat(t,{style:"decimal",minimumFractionDigits:0,maximumFractionDigits:0}).format(e);function u(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}function p(e){let t=r()(e),a=r()();return t.isSame(a,"day")?t.format("HH:mm"):t.format("DD/MM/YY")}function m(e){return e.replaceAll(/[^a-zA-Z0-9\s]/g,"-").replaceAll(/\s/g,"--")}let f=(e,t)=>e.includes(t)?e.filter(e=>e!==t):[...e,t],g=e=>e.charAt(0).toUpperCase()+e.slice(1)},30250:(e,t,a)=>{"use strict";a.d(t,{R:()=>o});var s=a(34523),i=a.n(s),r=a(69133),n=a(92377);let o=(0,r.Ue)()((0,n.tJ)(e=>({currentLayout:"list",setlayout:t=>e({currentLayout:t}),roomId:void 0,setRoomId:t=>e({roomId:t}),chatDetail:[],setchatDetail:t=>e({chatDetail:t}),updatechatDetail:t=>e(e=>{let a=e.chatDetail.length;return e.chatDetail[a-1].id==t.id?{}:{chatDetail:[...e.chatDetail,t]}}),participant:void 0,setParticipant:t=>e({participant:t}),allChat:[],setAllChat:t=>e({allChat:t}),updateSpecificAllChat:(t,a,s)=>e(({allChat:e})=>{let r=e=>e.sort((e,t)=>i()(t.lastMessages.createdAt).unix()-i()(e.lastMessages.createdAt).unix());if(a)return{allChat:r([...e,t])};if(s){let a=e.findIndex(e=>e.code===s);if("roomId"in t){if(a<0)return{allChat:r([...e,t])};{let s=[...e];return s[a]=t,{allChat:r(s)}}}if("id"in t)return a>=0?{allChat:r(e.map((e,s)=>s===a?{...e,lastMessages:t}:e))}:{allChat:e}}if("roomId"in t){let a=e.findIndex(e=>e.code===t.code);if(a<0)return{allChat:r([...e,t].sort((e,t)=>i()(t.lastMessages.createdAt).unix()-i()(e.lastMessages.createdAt).unix()))};{let s=[...e];return s[a]=t,{allChat:r(s)}}}if("id"in t){let a=e.findIndex(e=>e.code===t.code);if(a>=0)return{allChat:r(e.map((e,s)=>s===a?{...e,lastMessages:t}:e))}}return{allChat:e}}),updateParticipantStatus:t=>e(e=>e.participant?{participant:{...e.participant,status:t}}:{})}),{name:"messagingLayout",storage:(0,n.FL)(()=>sessionStorage)}))},84448:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>S,viewport:()=>w});var s=a(72051),i=a(77595),r=a.n(i),n=a(60257);a(14397);var o=a(5e3),l=a(45347);let d=(0,l.createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\components\providers\google-maps-provider.tsx#default`),c=(0,l.createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\components\providers\tanstack-query-provider.tsx#default`),u=(0,l.createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\components\ui\toaster.tsx#Toaster`);var p=a(33835),m=a.n(p);let f=(0,l.createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\components\locale\moment-locale.tsx#default`),g=(0,l.createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\app\[locale]\facebook-pixel.tsx#default`);var h=a(26269),v=a(86677);let x=(0,l.createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\components\cookie-consent\cookie-consent.tsx#default`);var y=a(688);function b({children:e}){return s.jsx(y.dR,{useEnterprise:!0,reCaptchaKey:"6LfrGFUrAAAAAGJqQdr4HwH8y7lens_OJRrP9KJo",children:e})}let w={width:"device-width",initialScale:1,maximumScale:1,userScalable:!1,viewportFit:"cover"};async function S({children:e,params:t}){let a=await (0,o.Z)(),{locale:i}=t;return(0,s.jsxs)("html",{lang:i,children:[s.jsx("meta",{name:"google-site-verification",content:"4eaK7qBBsKK5tqiXQyuYzG6xiv0N80JPVDp4H61aIyw"}),s.jsx(n.Z,{messages:a,children:s.jsx(b,{children:(0,s.jsxs)("body",{className:`${r().className} relative`,children:[s.jsx(v.Z,{}),s.jsx(h.Suspense,{fallback:"null",children:s.jsx(g,{})}),s.jsx(f,{}),s.jsx(m(),{showSpinner:!1}),s.jsx(c,{children:(0,s.jsxs)(d,{children:[e,s.jsx(u,{})]})}),s.jsx(x,{})]})})})]})}},81729:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i});var s=a(41288);function i(){(0,s.redirect)("/")}},86677:(e,t,a)=>{"use strict";a.d(t,{Z:()=>s});let s=(0,a(45347).createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\components\providers\notification-provider.tsx#default`)},46969:(e,t,a)=>{"use strict";a.d(t,{ZP:()=>n,nD:()=>r});var s=a(81155),i=a(93844);let r=["en","id"],n=(0,s.Z)(async({requestLocale:e})=>{let t=await e;return{locale:await e,messages:(await a(56997)(`./${t}.json`)).default,defaultLocale:i.DI.defaultLocale,locales:i.DI.locales}})},93844:(e,t,a)=>{"use strict";a.d(t,{DI:()=>i,rU:()=>r});var s=a(28462);let i={locales:["en","id"],defaultLocale:"en"},{Link:r,redirect:n,usePathname:o,useRouter:l}=(0,s.Z)(i)},57481:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i});var s=a(54564);let i=e=>[{type:"image/x-icon",sizes:"64x64",url:(0,s.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},80603:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>i});var s=a(54564);let i=e=>[{type:"image/x-icon",sizes:"64x64",url:(0,s.fillMetadataSegment)("/[locale]",e.params,"icon.ico")+"?8465a906383c07d0"}]},14397:()=>{}};