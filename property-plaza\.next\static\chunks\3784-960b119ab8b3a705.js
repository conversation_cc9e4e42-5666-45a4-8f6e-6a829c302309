"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3784],{32660:function(e,n,t){t.d(n,{Z:function(){return r}});let r=(0,t(79205).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},40875:function(e,n,t){t.d(n,{Z:function(){return r}});let r=(0,t(79205).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},81197:function(e,n,t){t.d(n,{Z:function(){return r}});let r=(0,t(79205).Z)("Coffee",[["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M14 2v2",key:"6buw04"}],["path",{d:"M16 8a1 1 0 0 1 1 1v8a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4V9a1 1 0 0 1 1-1h14a4 4 0 1 1 0 8h-1",key:"pwadti"}],["path",{d:"M6 2v2",key:"colzsn"}]])},95252:function(e,n,t){t.d(n,{Z:function(){return r}});let r=(0,t(79205).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},18133:function(e,n,t){t.d(n,{Z:function(){return r}});let r=(0,t(79205).Z)("Hotel",[["path",{d:"M10 22v-6.57",key:"1wmca3"}],["path",{d:"M12 11h.01",key:"z322tv"}],["path",{d:"M12 7h.01",key:"1ivr5q"}],["path",{d:"M14 15.43V22",key:"1q2vjd"}],["path",{d:"M15 16a5 5 0 0 0-6 0",key:"o9wqvi"}],["path",{d:"M16 11h.01",key:"xkw8gn"}],["path",{d:"M16 7h.01",key:"1kdx03"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 7h.01",key:"1vti4s"}],["rect",{x:"4",y:"2",width:"16",height:"20",rx:"2",key:"1uxh74"}]])},14938:function(e,n,t){t.d(n,{Z:function(){return r}});let r=(0,t(79205).Z)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},75745:function(e,n,t){t.d(n,{Z:function(){return r}});let r=(0,t(79205).Z)("LandPlot",[["path",{d:"m12 8 6-3-6-3v10",key:"mvpnpy"}],["path",{d:"m8 11.99-5.5 3.14a1 1 0 0 0 0 1.74l8.5 4.86a2 2 0 0 0 2 0l8.5-4.86a1 1 0 0 0 0-1.74L16 12",key:"ek95tt"}],["path",{d:"m6.49 12.85 11.02 6.3",key:"1kt42w"}],["path",{d:"M17.51 12.85 6.5 19.15",key:"v55bdg"}]])},83774:function(e,n,t){t.d(n,{Z:function(){return r}});let r=(0,t(79205).Z)("MapPin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},58293:function(e,n,t){t.d(n,{Z:function(){return r}});let r=(0,t(79205).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},67410:function(e,n,t){t.d(n,{Z:function(){return r}});let r=(0,t(79205).Z)("Presentation",[["path",{d:"M2 3h20",key:"91anmk"}],["path",{d:"M21 3v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V3",key:"2k9sn8"}],["path",{d:"m7 21 5-5 5 5",key:"bip4we"}]])},56096:function(e,n,t){t.d(n,{Z:function(){return r}});let r=(0,t(79205).Z)("School",[["path",{d:"M14 22v-4a2 2 0 1 0-4 0v4",key:"hhkicm"}],["path",{d:"m18 10 4 2v8a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-8l4-2",key:"1vwozw"}],["path",{d:"M18 5v17",key:"1sw6gf"}],["path",{d:"m4 6 8-4 8 4",key:"1q0ilc"}],["path",{d:"M6 5v17",key:"1xfsm0"}],["circle",{cx:"12",cy:"9",r:"2",key:"1092wv"}]])},73247:function(e,n,t){t.d(n,{Z:function(){return r}});let r=(0,t(79205).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},16275:function(e,n,t){t.d(n,{Z:function(){return r}});let r=(0,t(79205).Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},72227:function(e,n,t){t.d(n,{Z:function(){return r}});let r=(0,t(79205).Z)("TreePalm",[["path",{d:"M13 8c0-2.76-2.46-5-5.5-5S2 5.24 2 8h2l1-1 1 1h4",key:"foxbe7"}],["path",{d:"M13 7.14A5.82 5.82 0 0 1 16.5 6c3.04 0 5.5 2.24 5.5 5h-3l-1-1-1 1h-3",key:"18arnh"}],["path",{d:"M5.89 9.71c-2.15 2.15-2.3 5.47-.35 7.43l4.24-4.25.7-.7.71-.71 2.12-2.12c-1.95-1.96-5.27-1.8-7.42.35",key:"ywahnh"}],["path",{d:"M11 15.5c.5 2.5-.17 4.5-1 6.5h4c2-5.5-.5-12-1-14",key:"ft0feo"}]])},87301:function(e,n,t){var r=t(46147);r.getFormatter,r.getLocale,r.getMessages,r.getNow,n.cF=r.getRequestConfig,r.getTimeZone,r.getTranslations,r.setRequestLocale,r.unstable_setRequestLocale},46147:function(e,n){function t(e){return()=>{throw Error("`".concat(e,"` is not supported in Client Components."))}}Object.defineProperty(n,"__esModule",{value:!0});let r=t("getFormatter"),u=t("getNow"),o=t("getTimeZone"),i=t("getMessages"),c=t("getLocale"),a=t("getTranslations"),l=t("unstable_setRequestLocale"),f=t("setRequestLocale");n.getFormatter=r,n.getLocale=c,n.getMessages=i,n.getNow=u,n.getRequestConfig=function(){return t("getRequestConfig")},n.getTimeZone=o,n.getTranslations=a,n.setRequestLocale=f,n.unstable_setRequestLocale=l},7729:function(e,n,t){t.d(n,{ee:function(){return eS},VY:function(){return eA},h_:function(){return eT},fC:function(){return eP},xz:function(){return eN}});var r,u=t(2265),o=t(6741),i=t(98575),c=t(57437),a=t(82912),l=t(26606),f=t(91096),s="dismissableLayer.update",d=u.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),p=u.forwardRef((e,n)=>{var t,p;let{disableOutsidePointerEvents:m=!1,onEscapeKeyDown:y,onPointerDownOutside:M,onFocusOutside:C,onInteractOutside:g,onDismiss:E,...w}=e,R=u.useContext(d),[P,S]=u.useState(null),N=null!==(p=null==P?void 0:P.ownerDocument)&&void 0!==p?p:null===(t=globalThis)||void 0===t?void 0:t.document,[,T]=u.useState({}),A=(0,i.e)(n,e=>S(e)),b=Array.from(R.layers),[L]=[...R.layersWithOutsidePointerEventsDisabled].slice(-1),k=b.indexOf(L),x=P?b.indexOf(P):-1,O=R.layersWithOutsidePointerEventsDisabled.size>0,D=x>=k,I=function(e){var n;let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(n=globalThis)||void 0===n?void 0:n.document,r=(0,l.W)(e),o=u.useRef(!1),i=u.useRef(()=>{});return u.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let n=function(){v("dismissableLayer.pointerDownOutside",r,u,{discrete:!0})},u={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",i.current),i.current=n,t.addEventListener("click",i.current,{once:!0})):n()}else t.removeEventListener("click",i.current);o.current=!1},n=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(n),t.removeEventListener("pointerdown",e),t.removeEventListener("click",i.current)}},[t,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let n=e.target,t=[...R.branches].some(e=>e.contains(n));!D||t||(null==M||M(e),null==g||g(e),e.defaultPrevented||null==E||E())},N),Z=function(e){var n;let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(n=globalThis)||void 0===n?void 0:n.document,r=(0,l.W)(e),o=u.useRef(!1);return u.useEffect(()=>{let e=e=>{e.target&&!o.current&&v("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let n=e.target;[...R.branches].some(e=>e.contains(n))||(null==C||C(e),null==g||g(e),e.defaultPrevented||null==E||E())},N);return(0,f.e)(e=>{x!==R.layers.size-1||(null==y||y(e),!e.defaultPrevented&&E&&(e.preventDefault(),E()))},N),u.useEffect(()=>{if(P)return m&&(0===R.layersWithOutsidePointerEventsDisabled.size&&(r=N.body.style.pointerEvents,N.body.style.pointerEvents="none"),R.layersWithOutsidePointerEventsDisabled.add(P)),R.layers.add(P),h(),()=>{m&&1===R.layersWithOutsidePointerEventsDisabled.size&&(N.body.style.pointerEvents=r)}},[P,N,m,R]),u.useEffect(()=>()=>{P&&(R.layers.delete(P),R.layersWithOutsidePointerEventsDisabled.delete(P),h())},[P,R]),u.useEffect(()=>{let e=()=>T({});return document.addEventListener(s,e),()=>document.removeEventListener(s,e)},[]),(0,c.jsx)(a.WV.div,{...w,ref:A,style:{pointerEvents:O?D?"auto":"none":void 0,...e.style},onFocusCapture:(0,o.M)(e.onFocusCapture,Z.onFocusCapture),onBlurCapture:(0,o.M)(e.onBlurCapture,Z.onBlurCapture),onPointerDownCapture:(0,o.M)(e.onPointerDownCapture,I.onPointerDownCapture)})});function h(){let e=new CustomEvent(s);document.dispatchEvent(e)}function v(e,n,t,r){let{discrete:u}=r,o=t.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:t});n&&o.addEventListener(e,n,{once:!0}),u?(0,a.jH)(o,i):o.dispatchEvent(i)}p.displayName="DismissableLayer",u.forwardRef((e,n)=>{let t=u.useContext(d),r=u.useRef(null),o=(0,i.e)(n,r);return u.useEffect(()=>{let e=r.current;if(e)return t.branches.add(e),()=>{t.branches.delete(e)}},[t.branches]),(0,c.jsx)(a.WV.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var m=0;function y(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var M=t(99103),C=t(99255),g=t(21107),E=t(54887),w=t(61188),R=u.forwardRef((e,n)=>{var t,r;let{container:o,...i}=e,[l,f]=u.useState(!1);(0,w.b)(()=>f(!0),[]);let s=o||l&&(null===(r=globalThis)||void 0===r?void 0:null===(t=r.document)||void 0===t?void 0:t.body);return s?E.createPortal((0,c.jsx)(a.WV.div,{...i,ref:n}),s):null});R.displayName="Portal";var P=e=>{var n,t;let r,o;let{present:c,children:a}=e,l=function(e){var n,t;let[r,o]=u.useState(),i=u.useRef({}),c=u.useRef(e),a=u.useRef("none"),[l,f]=(n=e?"mounted":"unmounted",t={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},u.useReducer((e,n)=>{let r=t[e][n];return null!=r?r:e},n));return u.useEffect(()=>{let e=S(i.current);a.current="mounted"===l?e:"none"},[l]),(0,w.b)(()=>{let n=i.current,t=c.current;if(t!==e){let r=a.current,u=S(n);e?f("MOUNT"):"none"===u||(null==n?void 0:n.display)==="none"?f("UNMOUNT"):t&&r!==u?f("ANIMATION_OUT"):f("UNMOUNT"),c.current=e}},[e,f]),(0,w.b)(()=>{if(r){var e;let n;let t=null!==(e=r.ownerDocument.defaultView)&&void 0!==e?e:window,u=e=>{let u=S(i.current).includes(e.animationName);if(e.target===r&&u&&(f("ANIMATION_END"),!c.current)){let e=r.style.animationFillMode;r.style.animationFillMode="forwards",n=t.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=e)})}},o=e=>{e.target===r&&(a.current=S(i.current))};return r.addEventListener("animationstart",o),r.addEventListener("animationcancel",u),r.addEventListener("animationend",u),()=>{t.clearTimeout(n),r.removeEventListener("animationstart",o),r.removeEventListener("animationcancel",u),r.removeEventListener("animationend",u)}}f("ANIMATION_END")},[r,f]),{isPresent:["mounted","unmountSuspended"].includes(l),ref:u.useCallback(e=>{e&&(i.current=getComputedStyle(e)),o(e)},[])}}(c),f="function"==typeof a?a({present:l.isPresent}):u.Children.only(a),s=(0,i.e)(l.ref,(r=null===(n=Object.getOwnPropertyDescriptor(f.props,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning?f.ref:(r=null===(t=Object.getOwnPropertyDescriptor(f,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in r&&r.isReactWarning?f.props.ref:f.props.ref||f.ref);return"function"==typeof a||l.isPresent?u.cloneElement(f,{ref:s}):null};function S(e){return(null==e?void 0:e.animationName)||"none"}P.displayName="Presence";var N=u.forwardRef((e,n)=>{let{children:t,...r}=e,o=u.Children.toArray(t),i=o.find(b);if(i){let e=i.props.children,t=o.map(n=>n!==i?n:u.Children.count(e)>1?u.Children.only(null):u.isValidElement(e)?e.props.children:null);return(0,c.jsx)(T,{...r,ref:n,children:u.isValidElement(e)?u.cloneElement(e,void 0,t):null})}return(0,c.jsx)(T,{...r,ref:n,children:t})});N.displayName="Slot";var T=u.forwardRef((e,n)=>{let{children:t,...r}=e;if(u.isValidElement(t)){let e,o;let c=(e=Object.getOwnPropertyDescriptor(t.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?t.ref:(e=Object.getOwnPropertyDescriptor(t,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?t.props.ref:t.props.ref||t.ref;return u.cloneElement(t,{...function(e,n){let t={...n};for(let r in n){let u=e[r],o=n[r];/^on[A-Z]/.test(r)?u&&o?t[r]=(...e)=>{o(...e),u(...e)}:u&&(t[r]=u):"style"===r?t[r]={...u,...o}:"className"===r&&(t[r]=[u,o].filter(Boolean).join(" "))}return{...e,...t}}(r,t.props),ref:n?(0,i.F)(n,c):c})}return u.Children.count(t)>1?u.Children.only(null):null});T.displayName="SlotClone";var A=({children:e})=>(0,c.jsx)(c.Fragment,{children:e});function b(e){return u.isValidElement(e)&&e.type===A}var L=t(80886),k=t(5478),x=t(5853),O=t(85770),D=t(17325),I=(0,t(31412)._)(),Z=function(){},B=u.forwardRef(function(e,n){var t=u.useRef(null),r=u.useState({onScrollCapture:Z,onWheelCapture:Z,onTouchMoveCapture:Z}),o=r[0],i=r[1],c=e.forwardProps,a=e.children,l=e.className,f=e.removeScrollBar,s=e.enabled,d=e.shards,p=e.sideCar,h=e.noIsolation,v=e.inert,m=e.allowPinchZoom,y=e.as,M=e.gapMode,C=(0,x._T)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),g=(0,D.q)([t,n]),E=(0,x.pi)((0,x.pi)({},C),o);return u.createElement(u.Fragment,null,s&&u.createElement(p,{sideCar:I,removeScrollBar:f,shards:d,noIsolation:h,inert:v,setCallbacks:i,allowPinchZoom:!!m,lockRef:t,gapMode:M}),c?u.cloneElement(u.Children.only(a),(0,x.pi)((0,x.pi)({},E),{ref:g})):u.createElement(void 0===y?"div":y,(0,x.pi)({},E,{className:l,ref:g}),a))});B.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},B.classNames={fullWidth:O.zi,zeroRight:O.pF};var G=t(49085),F=t(5517),W=t(18704),j=!1;if("undefined"!=typeof window)try{var U=Object.defineProperty({},"passive",{get:function(){return j=!0,!0}});window.addEventListener("test",U,U),window.removeEventListener("test",U,U)}catch(e){j=!1}var H=!!j&&{passive:!1},K=function(e,n){if(!(e instanceof Element))return!1;var t=window.getComputedStyle(e);return"hidden"!==t[n]&&!(t.overflowY===t.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===t[n])},V=function(e,n){var t=n.ownerDocument,r=n;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),_(e,r)){var u=Y(e,r);if(u[1]>u[2])return!0}r=r.parentNode}while(r&&r!==t.body);return!1},_=function(e,n){return"v"===e?K(n,"overflowY"):K(n,"overflowX")},Y=function(e,n){return"v"===e?[n.scrollTop,n.scrollHeight,n.clientHeight]:[n.scrollLeft,n.scrollWidth,n.clientWidth]},q=function(e,n,t,r,u){var o,i=(o=window.getComputedStyle(n).direction,"h"===e&&"rtl"===o?-1:1),c=i*r,a=t.target,l=n.contains(a),f=!1,s=c>0,d=0,p=0;do{var h=Y(e,a),v=h[0],m=h[1]-h[2]-i*v;(v||m)&&_(e,a)&&(d+=m,p+=v),a instanceof ShadowRoot?a=a.host:a=a.parentNode}while(!l&&a!==document.body||l&&(n.contains(a)||n===a));return s&&(u&&1>Math.abs(d)||!u&&c>d)?f=!0:!s&&(u&&1>Math.abs(p)||!u&&-c>p)&&(f=!0),f},z=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},J=function(e){return[e.deltaX,e.deltaY]},X=function(e){return e&&"current"in e?e.current:e},Q=0,$=[],ee=(0,G.L)(I,function(e){var n=u.useRef([]),t=u.useRef([0,0]),r=u.useRef(),o=u.useState(Q++)[0],i=u.useState(W.Ws)[0],c=u.useRef(e);u.useEffect(function(){c.current=e},[e]),u.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var n=(0,x.ev)([e.lockRef.current],(e.shards||[]).map(X),!0).filter(Boolean);return n.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),n.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var a=u.useCallback(function(e,n){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!c.current.allowPinchZoom;var u,o=z(e),i=t.current,a="deltaX"in e?e.deltaX:i[0]-o[0],l="deltaY"in e?e.deltaY:i[1]-o[1],f=e.target,s=Math.abs(a)>Math.abs(l)?"h":"v";if("touches"in e&&"h"===s&&"range"===f.type)return!1;var d=V(s,f);if(!d)return!0;if(d?u=s:(u="v"===s?"h":"v",d=V(s,f)),!d)return!1;if(!r.current&&"changedTouches"in e&&(a||l)&&(r.current=u),!u)return!0;var p=r.current||u;return q(p,n,e,"h"===p?a:l,!0)},[]),l=u.useCallback(function(e){if($.length&&$[$.length-1]===i){var t="deltaY"in e?J(e):z(e),r=n.current.filter(function(n){var r;return n.name===e.type&&(n.target===e.target||e.target===n.shadowParent)&&(r=n.delta)[0]===t[0]&&r[1]===t[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var u=(c.current.shards||[]).map(X).filter(Boolean).filter(function(n){return n.contains(e.target)});(u.length>0?a(e,u[0]):!c.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),f=u.useCallback(function(e,t,r,u){var o={name:e,delta:t,target:r,should:u,shadowParent:function(e){for(var n=null;null!==e;)e instanceof ShadowRoot&&(n=e.host,e=e.host),e=e.parentNode;return n}(r)};n.current.push(o),setTimeout(function(){n.current=n.current.filter(function(e){return e!==o})},1)},[]),s=u.useCallback(function(e){t.current=z(e),r.current=void 0},[]),d=u.useCallback(function(n){f(n.type,J(n),n.target,a(n,e.lockRef.current))},[]),p=u.useCallback(function(n){f(n.type,z(n),n.target,a(n,e.lockRef.current))},[]);u.useEffect(function(){return $.push(i),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:p}),document.addEventListener("wheel",l,H),document.addEventListener("touchmove",l,H),document.addEventListener("touchstart",s,H),function(){$=$.filter(function(e){return e!==i}),document.removeEventListener("wheel",l,H),document.removeEventListener("touchmove",l,H),document.removeEventListener("touchstart",s,H)}},[]);var h=e.removeScrollBar,v=e.inert;return u.createElement(u.Fragment,null,v?u.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?u.createElement(F.jp,{gapMode:e.gapMode}):null)}),en=u.forwardRef(function(e,n){return u.createElement(B,(0,x.pi)({},e,{ref:n,sideCar:ee}))});en.classNames=B.classNames;var et="Popover",[er,eu]=function(e,n=[]){let t=[],r=()=>{let n=t.map(e=>u.createContext(e));return function(t){let r=t?.[e]||n;return u.useMemo(()=>({[`__scope${e}`]:{...t,[e]:r}}),[t,r])}};return r.scopeName=e,[function(n,r){let o=u.createContext(r),i=t.length;t=[...t,r];let a=n=>{let{scope:t,children:r,...a}=n,l=t?.[e]?.[i]||o,f=u.useMemo(()=>a,Object.values(a));return(0,c.jsx)(l.Provider,{value:f,children:r})};return a.displayName=n+"Provider",[a,function(t,c){let a=c?.[e]?.[i]||o,l=u.useContext(a);if(l)return l;if(void 0!==r)return r;throw Error(`\`${t}\` must be used within \`${n}\``)}]},function(...e){let n=e[0];if(1===e.length)return n;let t=()=>{let t=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=t.reduce((n,{useScope:t,scopeName:r})=>{let u=t(e)[`__scope${r}`];return{...n,...u}},{});return u.useMemo(()=>({[`__scope${n.scopeName}`]:r}),[r])}};return t.scopeName=n.scopeName,t}(r,...n)]}(et,[g.D7]),eo=(0,g.D7)(),[ei,ec]=er(et),ea=e=>{let{__scopePopover:n,children:t,open:r,defaultOpen:o,onOpenChange:i,modal:a=!1}=e,l=eo(n),f=u.useRef(null),[s,d]=u.useState(!1),[p=!1,h]=(0,L.T)({prop:r,defaultProp:o,onChange:i});return(0,c.jsx)(g.fC,{...l,children:(0,c.jsx)(ei,{scope:n,contentId:(0,C.M)(),triggerRef:f,open:p,onOpenChange:h,onOpenToggle:u.useCallback(()=>h(e=>!e),[h]),hasCustomAnchor:s,onCustomAnchorAdd:u.useCallback(()=>d(!0),[]),onCustomAnchorRemove:u.useCallback(()=>d(!1),[]),modal:a,children:t})})};ea.displayName=et;var el="PopoverAnchor",ef=u.forwardRef((e,n)=>{let{__scopePopover:t,...r}=e,o=ec(el,t),i=eo(t),{onCustomAnchorAdd:a,onCustomAnchorRemove:l}=o;return u.useEffect(()=>(a(),()=>l()),[a,l]),(0,c.jsx)(g.ee,{...i,...r,ref:n})});ef.displayName=el;var es="PopoverTrigger",ed=u.forwardRef((e,n)=>{let{__scopePopover:t,...r}=e,u=ec(es,t),l=eo(t),f=(0,i.e)(n,u.triggerRef),s=(0,c.jsx)(a.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":u.open,"aria-controls":u.contentId,"data-state":eR(u.open),...r,ref:f,onClick:(0,o.M)(e.onClick,u.onOpenToggle)});return u.hasCustomAnchor?s:(0,c.jsx)(g.ee,{asChild:!0,...l,children:s})});ed.displayName=es;var ep="PopoverPortal",[eh,ev]=er(ep,{forceMount:void 0}),em=e=>{let{__scopePopover:n,forceMount:t,children:r,container:u}=e,o=ec(ep,n);return(0,c.jsx)(eh,{scope:n,forceMount:t,children:(0,c.jsx)(P,{present:t||o.open,children:(0,c.jsx)(R,{asChild:!0,container:u,children:r})})})};em.displayName=ep;var ey="PopoverContent",eM=u.forwardRef((e,n)=>{let t=ev(ey,e.__scopePopover),{forceMount:r=t.forceMount,...u}=e,o=ec(ey,e.__scopePopover);return(0,c.jsx)(P,{present:r||o.open,children:o.modal?(0,c.jsx)(eC,{...u,ref:n}):(0,c.jsx)(eg,{...u,ref:n})})});eM.displayName=ey;var eC=u.forwardRef((e,n)=>{let t=ec(ey,e.__scopePopover),r=u.useRef(null),a=(0,i.e)(n,r),l=u.useRef(!1);return u.useEffect(()=>{let e=r.current;if(e)return(0,k.Ry)(e)},[]),(0,c.jsx)(en,{as:N,allowPinchZoom:!0,children:(0,c.jsx)(eE,{...e,ref:a,trapFocus:t.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{var n;e.preventDefault(),l.current||null===(n=t.triggerRef.current)||void 0===n||n.focus()}),onPointerDownOutside:(0,o.M)(e.onPointerDownOutside,e=>{let n=e.detail.originalEvent,t=0===n.button&&!0===n.ctrlKey,r=2===n.button||t;l.current=r},{checkForDefaultPrevented:!1}),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),eg=u.forwardRef((e,n)=>{let t=ec(ey,e.__scopePopover),r=u.useRef(!1),o=u.useRef(!1);return(0,c.jsx)(eE,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:n=>{var u,i;null===(u=e.onCloseAutoFocus)||void 0===u||u.call(e,n),n.defaultPrevented||(r.current||null===(i=t.triggerRef.current)||void 0===i||i.focus(),n.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:n=>{var u,i;null===(u=e.onInteractOutside)||void 0===u||u.call(e,n),n.defaultPrevented||(r.current=!0,"pointerdown"!==n.detail.originalEvent.type||(o.current=!0));let c=n.target;(null===(i=t.triggerRef.current)||void 0===i?void 0:i.contains(c))&&n.preventDefault(),"focusin"===n.detail.originalEvent.type&&o.current&&n.preventDefault()}})}),eE=u.forwardRef((e,n)=>{let{__scopePopover:t,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:i,disableOutsidePointerEvents:a,onEscapeKeyDown:l,onPointerDownOutside:f,onFocusOutside:s,onInteractOutside:d,...h}=e,v=ec(ey,t),C=eo(t);return u.useEffect(()=>{var e,n;let t=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=t[0])&&void 0!==e?e:y()),document.body.insertAdjacentElement("beforeend",null!==(n=t[1])&&void 0!==n?n:y()),m++,()=>{1===m&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),m--}},[]),(0,c.jsx)(M.M,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:i,children:(0,c.jsx)(p,{asChild:!0,disableOutsidePointerEvents:a,onInteractOutside:d,onEscapeKeyDown:l,onPointerDownOutside:f,onFocusOutside:s,onDismiss:()=>v.onOpenChange(!1),children:(0,c.jsx)(g.VY,{"data-state":eR(v.open),role:"dialog",id:v.contentId,...C,...h,ref:n,style:{...h.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),ew="PopoverClose";function eR(e){return e?"open":"closed"}u.forwardRef((e,n)=>{let{__scopePopover:t,...r}=e,u=ec(ew,t);return(0,c.jsx)(a.WV.button,{type:"button",...r,ref:n,onClick:(0,o.M)(e.onClick,()=>u.onOpenChange(!1))})}).displayName=ew,u.forwardRef((e,n)=>{let{__scopePopover:t,...r}=e,u=eo(t);return(0,c.jsx)(g.Eh,{...u,...r,ref:n})}).displayName="PopoverArrow";var eP=ea,eS=ef,eN=ed,eT=em,eA=eM},31056:function(e,n,t){t.r(n),t.d(n,{AC:function(){return r.AC},AD:function(){return r.AD},AE:function(){return r.AE},AF:function(){return r.AF},AG:function(){return r.AG},AI:function(){return r.AI},AL:function(){return r.AL},AM:function(){return r.AM},AO:function(){return r.AO},AQ:function(){return r.AQ},AR:function(){return r.AR},AS:function(){return r.AS},AT:function(){return r.AT},AU:function(){return r.AU},AW:function(){return r.AW},AX:function(){return r.AX},AZ:function(){return r.AZ},BA:function(){return r.BA},BB:function(){return r.BB},BD:function(){return r.BD},BE:function(){return r.BE},BF:function(){return r.BF},BG:function(){return r.BG},BH:function(){return r.BH},BI:function(){return r.BI},BJ:function(){return r.BJ},BL:function(){return r.BL},BM:function(){return r.BM},BN:function(){return r.BN},BO:function(){return r.BO},BQ:function(){return r.BQ},BR:function(){return r.BR},BS:function(){return r.BS},BT:function(){return r.BT},BV:function(){return r.BV},BW:function(){return r.BW},BY:function(){return r.BY},BZ:function(){return r.BZ},CA:function(){return r.CA},CC:function(){return r.CC},CD:function(){return r.CD},CF:function(){return r.CF},CG:function(){return r.CG},CH:function(){return r.CH},CI:function(){return r.CI},CK:function(){return r.CK},CL:function(){return r.CL},CM:function(){return r.CM},CN:function(){return r.CN},CO:function(){return r.CO},CR:function(){return r.CR},CU:function(){return r.CU},CV:function(){return r.CV},CW:function(){return r.CW},CX:function(){return r.CX},CY:function(){return r.CY},CZ:function(){return r.CZ},DE:function(){return r.DE},DJ:function(){return r.DJ},DK:function(){return r.DK},DM:function(){return r.DM},DO:function(){return r.DO},DZ:function(){return r.DZ},EC:function(){return r.EC},EE:function(){return r.EE},EG:function(){return r.EG},EH:function(){return r.EH},ER:function(){return r.ER},ES:function(){return r.ES},ET:function(){return r.ET},EU:function(){return r.EU},FI:function(){return r.FI},FJ:function(){return r.FJ},FK:function(){return r.FK},FM:function(){return r.FM},FO:function(){return r.FO},FR:function(){return r.FR},GA:function(){return r.GA},GB:function(){return r.GB},GD:function(){return r.GD},GE:function(){return r.GE},GF:function(){return r.GF},GG:function(){return r.GG},GH:function(){return r.GH},GI:function(){return r.GI},GL:function(){return r.GL},GM:function(){return r.GM},GN:function(){return r.GN},GP:function(){return r.GP},GQ:function(){return r.GQ},GR:function(){return r.GR},GS:function(){return r.GS},GT:function(){return r.GT},GU:function(){return r.GU},GW:function(){return r.GW},GY:function(){return r.GY},HK:function(){return r.HK},HM:function(){return r.HM},HN:function(){return r.HN},HR:function(){return r.HR},HT:function(){return r.HT},HU:function(){return r.HU},IC:function(){return r.IC},ID:function(){return r.ID},IE:function(){return r.IE},IL:function(){return r.IL},IM:function(){return r.IM},IN:function(){return r.IN},IO:function(){return r.IO},IQ:function(){return r.IQ},IR:function(){return r.IR},IS:function(){return r.IS},IT:function(){return r.IT},JE:function(){return r.JE},JM:function(){return r.JM},JO:function(){return r.JO},JP:function(){return r.JP},KE:function(){return r.KE},KG:function(){return r.KG},KH:function(){return r.KH},KI:function(){return r.KI},KM:function(){return r.KM},KN:function(){return r.KN},KP:function(){return r.KP},KR:function(){return r.KR},KW:function(){return r.KW},KY:function(){return r.KY},KZ:function(){return r.KZ},LA:function(){return r.LA},LB:function(){return r.LB},LC:function(){return r.LC},LI:function(){return r.LI},LK:function(){return r.LK},LR:function(){return r.LR},LS:function(){return r.LS},LT:function(){return r.LT},LU:function(){return r.LU},LV:function(){return r.LV},LY:function(){return r.LY},MA:function(){return r.MA},MC:function(){return r.MC},MD:function(){return r.MD},ME:function(){return r.ME},MF:function(){return r.MF},MG:function(){return r.MG},MH:function(){return r.MH},MK:function(){return r.MK},ML:function(){return r.ML},MM:function(){return r.MM},MN:function(){return r.MN},MO:function(){return r.MO},MP:function(){return r.MP},MQ:function(){return r.MQ},MR:function(){return r.MR},MS:function(){return r.MS},MT:function(){return r.MT},MU:function(){return r.MU},MV:function(){return r.MV},MW:function(){return r.MW},MX:function(){return r.MX},MY:function(){return r.MY},MZ:function(){return r.MZ},NA:function(){return r.NA},NC:function(){return r.NC},NE:function(){return r.NE},NF:function(){return r.NF},NG:function(){return r.NG},NI:function(){return r.NI},NL:function(){return r.NL},NO:function(){return r.NO},NP:function(){return r.NP},NR:function(){return r.NR},NU:function(){return r.NU},NZ:function(){return r.NZ},OM:function(){return r.OM},PA:function(){return r.PA},PE:function(){return r.PE},PF:function(){return r.PF},PG:function(){return r.PG},PH:function(){return r.PH},PK:function(){return r.PK},PL:function(){return r.PL},PM:function(){return r.PM},PN:function(){return r.PN},PR:function(){return r.PR},PS:function(){return r.PS},PT:function(){return r.PT},PW:function(){return r.PW},PY:function(){return r.PY},QA:function(){return r.QA},RE:function(){return r.RE},RO:function(){return r.RO},RS:function(){return r.RS},RU:function(){return r.RU},RW:function(){return r.RW},SA:function(){return r.SA},SB:function(){return r.SB},SC:function(){return r.SC},SD:function(){return r.SD},SE:function(){return r.SE},SG:function(){return r.SG},SH:function(){return r.SH},SI:function(){return r.SI},SJ:function(){return r.SJ},SK:function(){return r.SK},SL:function(){return r.SL},SM:function(){return r.SM},SN:function(){return r.SN},SO:function(){return r.SO},SR:function(){return r.SR},SS:function(){return r.SS},ST:function(){return r.ST},SV:function(){return r.SV},SX:function(){return r.SX},SY:function(){return r.SY},SZ:function(){return r.SZ},TA:function(){return r.TA},TC:function(){return r.TC},TD:function(){return r.TD},TF:function(){return r.TF},TG:function(){return r.TG},TH:function(){return r.TH},TJ:function(){return r.TJ},TK:function(){return r.TK},TL:function(){return r.TL},TM:function(){return r.TM},TN:function(){return r.TN},TO:function(){return r.TO},TR:function(){return r.TR},TT:function(){return r.TT},TV:function(){return r.TV},TW:function(){return r.TW},TZ:function(){return r.TZ},UA:function(){return r.UA},UG:function(){return r.UG},UM:function(){return r.UM},US:function(){return r.US},UY:function(){return r.UY},UZ:function(){return r.UZ},VA:function(){return r.VA},VC:function(){return r.VC},VE:function(){return r.VE},VG:function(){return r.VG},VI:function(){return r.VI},VN:function(){return r.VN},VU:function(){return r.VU},WF:function(){return r.WF},WS:function(){return r.WS},XK:function(){return r.XK},YE:function(){return r.YE},YT:function(){return r.YT},ZA:function(){return r.ZA},ZM:function(){return r.ZM},ZW:function(){return r.ZW},default:function(){return r.ZP}});var r=t(45296)},48614:function(e,n,t){t.d(n,{M:function(){return y}});var r=t(57437),u=t(2265),o=t(58881),i=t(53576),c=t(64252),a=t(45750);class l extends u.Component{getSnapshotBeforeUpdate(e){let n=this.props.childRef.current;if(n&&e.isPresent&&!this.props.isPresent){let e=this.props.sizeRef.current;e.height=n.offsetHeight||0,e.width=n.offsetWidth||0,e.top=n.offsetTop,e.left=n.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function f(e){let{children:n,isPresent:t}=e,o=(0,u.useId)(),i=(0,u.useRef)(null),c=(0,u.useRef)({width:0,height:0,top:0,left:0}),{nonce:f}=(0,u.useContext)(a._);return(0,u.useInsertionEffect)(()=>{let{width:e,height:n,top:r,left:u}=c.current;if(t||!i.current||!e||!n)return;i.current.dataset.motionPopId=o;let a=document.createElement("style");return f&&(a.nonce=f),document.head.appendChild(a),a.sheet&&a.sheet.insertRule('\n          [data-motion-pop-id="'.concat(o,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(n,"px !important;\n            top: ").concat(r,"px !important;\n            left: ").concat(u,"px !important;\n          }\n        ")),()=>{document.head.removeChild(a)}},[t]),(0,r.jsx)(l,{isPresent:t,childRef:i,sizeRef:c,children:u.cloneElement(n,{ref:i})})}let s=e=>{let{children:n,initial:t,isPresent:o,onExitComplete:a,custom:l,presenceAffectsLayout:s,mode:p}=e,h=(0,i.h)(d),v=(0,u.useId)(),m=(0,u.useCallback)(e=>{for(let n of(h.set(e,!0),h.values()))if(!n)return;a&&a()},[h,a]),y=(0,u.useMemo)(()=>({id:v,initial:t,isPresent:o,custom:l,onExitComplete:m,register:e=>(h.set(e,!1),()=>h.delete(e))}),s?[Math.random(),m]:[o,m]);return(0,u.useMemo)(()=>{h.forEach((e,n)=>h.set(n,!1))},[o]),u.useEffect(()=>{o||h.size||!a||a()},[o]),"popLayout"===p&&(n=(0,r.jsx)(f,{isPresent:o,children:n})),(0,r.jsx)(c.O.Provider,{value:y,children:n})};function d(){return new Map}var p=t(49637);let h=e=>e.key||"";function v(e){let n=[];return u.Children.forEach(e,e=>{(0,u.isValidElement)(e)&&n.push(e)}),n}var m=t(11534);let y=e=>{let{children:n,custom:t,initial:c=!0,onExitComplete:a,presenceAffectsLayout:l=!0,mode:f="sync",propagate:d=!1}=e,[y,M]=(0,p.oO)(d),C=(0,u.useMemo)(()=>v(n),[n]),g=d&&!y?[]:C.map(h),E=(0,u.useRef)(!0),w=(0,u.useRef)(C),R=(0,i.h)(()=>new Map),[P,S]=(0,u.useState)(C),[N,T]=(0,u.useState)(C);(0,m.L)(()=>{E.current=!1,w.current=C;for(let e=0;e<N.length;e++){let n=h(N[e]);g.includes(n)?R.delete(n):!0!==R.get(n)&&R.set(n,!1)}},[N,g.length,g.join("-")]);let A=[];if(C!==P){let e=[...C];for(let n=0;n<N.length;n++){let t=N[n],r=h(t);g.includes(r)||(e.splice(n,0,t),A.push(t))}"wait"===f&&A.length&&(e=A),T(v(e)),S(C);return}let{forceRender:b}=(0,u.useContext)(o.p);return(0,r.jsx)(r.Fragment,{children:N.map(e=>{let n=h(e),u=(!d||!!y)&&(C===N||g.includes(n));return(0,r.jsx)(s,{isPresent:u,initial:(!E.current||!!c)&&void 0,custom:u?void 0:t,presenceAffectsLayout:l,mode:f,onExitComplete:u?void 0:()=>{if(!R.has(n))return;R.set(n,!0);let e=!0;R.forEach(n=>{n||(e=!1)}),e&&(null==b||b(),T(w.current),d&&(null==M||M()),a&&a())},children:e},n)})})}},67481:function(e,n,t){t.d(n,{Z:function(){return o}});let r=[],u=[];function o(e,n){let t,o,i,c;if(e===n)return 0;let a=e;e.length>n.length&&(e=n,n=a);let l=e.length,f=n.length;for(;l>0&&e.charCodeAt(~-l)===n.charCodeAt(~-f);)l--,f--;let s=0;for(;s<l&&e.charCodeAt(s)===n.charCodeAt(s);)s++;if(l-=s,f-=s,0===l)return f;let d=0,p=0;for(;d<l;)u[d]=e.charCodeAt(s+d),r[d]=++d;for(;p<f;)for(d=0,t=n.charCodeAt(s+p),i=p++,o=p;d<l;d++)c=t===u[d]?i:i+1,i=r[d],o=r[d]=i>o?c>o?o+1:c:c>i?i+1:c;return o}}}]);