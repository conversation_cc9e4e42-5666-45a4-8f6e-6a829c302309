import { cookies } from "next/headers";
import HeroTermsOfUseSection from "./hero";
import { getLocale, getTranslations } from "next-intl/server";
import { getTermsOfUseContent } from "@/core/services/sanity/services";
import Content from "./content";
import { Metadata } from "next";
import { termSeekerUrl } from "@/lib/constanta/route";

export async function generateMetadata(): Promise<Metadata> {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const baseUrl = process.env.USER_DOMAIN || "https://www.property-plaza.com/"
  const locale = await getLocale()

  const t = await getTranslations("seeker")
  return {
    title: t('metadata.termsOfUse.title'),
    description: t('metadata.termsOfUse.description'),
    openGraph: {
      title: t('metadata.termsOfUse.title'),
      description: t('metadata.termsOfUse.description'),
      images: [
        {
          url: baseUrl + "og.png",
          width: 1200,
          height: 630,
          alt: "Property Plaza",
        }
      ],
      type: "website",
      url: baseUrl + locale + termSeekerUrl,
    },
    twitter: {
      card: "summary_large_image",
      title: t('metadata.termsOfUse.title'),
      description: t('metadata.termsOfUse.description'),
      images: [baseUrl + "og.jpg"],
    },
    alternates: {
      canonical: baseUrl + locale + termSeekerUrl,
      languages: {
        "id": baseUrl + `id${termSeekerUrl}`,
        "en": baseUrl + `en${termSeekerUrl}`,
        "x-default": baseUrl + termSeekerUrl.replace("/", "")
      },

    }
  }
}

export default async function TermsOfUsePage() {
  const cookiesStore = cookies()
  const locale = cookiesStore.get('NEXT_LOCALE')?.value
  const t = await getTranslations("seeker")
  // const termOfUseContent = await getTermsOfUseContent(locale?.toLocaleLowerCase() || "en") // use this when locale for content are implemented
  const termOfUseContent = await getTermsOfUseContent("en")
  return <>
    <HeroTermsOfUseSection />
    <Content content={termOfUseContent[0]} />
  </>
}