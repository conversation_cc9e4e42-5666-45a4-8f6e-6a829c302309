"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3398],{92451:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(79205).Z)("ChevronLeft",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]])},92360:function(e,t,r){let n;r.d(t,{x8:function(){return eH},VY:function(){return eB},dk:function(){return eV},aV:function(){return eK},h_:function(){return eq},fC:function(){return e_},Dx:function(){return ez},xz:function(){return eW}});var i,s=r(2265);function o(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function u(...e){return s.useCallback(function(...e){return t=>{let r=!1,n=e.map(e=>{let n=a(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():a(e[t],null)}}}}(...e),e)}var l=r(57437),c=r(99255),d=r(80886),h=r(54887),f=r(98482),p=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=s.forwardRef((e,r)=>{let{asChild:n,...i}=e,s=n?f.g7:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(s,{...i,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{}),v=r(26606),m=r(91096),y="dismissableLayer.update",b=s.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),g=s.forwardRef((e,t)=>{var r,n;let{disableOutsidePointerEvents:a=!1,onEscapeKeyDown:c,onPointerDownOutside:d,onFocusOutside:h,onInteractOutside:f,onDismiss:g,...C}=e,w=s.useContext(b),[O,S]=s.useState(null),N=null!==(n=null==O?void 0:O.ownerDocument)&&void 0!==n?n:null===(r=globalThis)||void 0===r?void 0:r.document,[,D]=s.useState({}),T=u(t,e=>S(e)),I=Array.from(w.layers),[F]=[...w.layersWithOutsidePointerEventsDisabled].slice(-1),x=I.indexOf(F),P=O?I.indexOf(O):-1,L=w.layersWithOutsidePointerEventsDisabled.size>0,M=P>=x,k=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,n=(0,v.W)(e),i=s.useRef(!1),o=s.useRef(()=>{});return s.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){E("dismissableLayer.pointerDownOutside",n,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",o.current),o.current=t,r.addEventListener("click",o.current,{once:!0})):t()}else r.removeEventListener("click",o.current);i.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",o.current)}},[r,n]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,r=[...w.branches].some(e=>e.contains(t));!M||r||(null==d||d(e),null==f||f(e),e.defaultPrevented||null==g||g())},N),Q=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,n=(0,v.W)(e),i=s.useRef(!1);return s.useEffect(()=>{let e=e=>{e.target&&!i.current&&E("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,n]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;[...w.branches].some(e=>e.contains(t))||(null==h||h(e),null==f||f(e),e.defaultPrevented||null==g||g())},N);return(0,m.e)(e=>{P!==w.layers.size-1||(null==c||c(e),!e.defaultPrevented&&g&&(e.preventDefault(),g()))},N),s.useEffect(()=>{if(O)return a&&(0===w.layersWithOutsidePointerEventsDisabled.size&&(i=N.body.style.pointerEvents,N.body.style.pointerEvents="none"),w.layersWithOutsidePointerEventsDisabled.add(O)),w.layers.add(O),R(),()=>{a&&1===w.layersWithOutsidePointerEventsDisabled.size&&(N.body.style.pointerEvents=i)}},[O,N,a,w]),s.useEffect(()=>()=>{O&&(w.layers.delete(O),w.layersWithOutsidePointerEventsDisabled.delete(O),R())},[O,w]),s.useEffect(()=>{let e=()=>D({});return document.addEventListener(y,e),()=>document.removeEventListener(y,e)},[]),(0,l.jsx)(p.div,{...C,ref:T,style:{pointerEvents:L?M?"auto":"none":void 0,...e.style},onFocusCapture:o(e.onFocusCapture,Q.onFocusCapture),onBlurCapture:o(e.onBlurCapture,Q.onBlurCapture),onPointerDownCapture:o(e.onPointerDownCapture,k.onPointerDownCapture)})});function R(){let e=new CustomEvent(y);document.dispatchEvent(e)}function E(e,t,r,n){let{discrete:i}=n,s=r.originalEvent.target,o=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});(t&&s.addEventListener(e,t,{once:!0}),i)?s&&h.flushSync(()=>s.dispatchEvent(o)):s.dispatchEvent(o)}g.displayName="DismissableLayer",s.forwardRef((e,t)=>{let r=s.useContext(b),n=s.useRef(null),i=u(t,n);return s.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,l.jsx)(p.div,{...e,ref:i})}).displayName="DismissableLayerBranch";var C="focusScope.autoFocusOnMount",w="focusScope.autoFocusOnUnmount",O={bubbles:!1,cancelable:!0},S=s.forwardRef((e,t)=>{let{loop:r=!1,trapped:n=!1,onMountAutoFocus:i,onUnmountAutoFocus:o,...a}=e,[c,d]=s.useState(null),h=(0,v.W)(i),f=(0,v.W)(o),m=s.useRef(null),y=u(t,e=>d(e)),b=s.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;s.useEffect(()=>{if(n){let e=function(e){if(b.paused||!c)return;let t=e.target;c.contains(t)?m.current=t:T(m.current,{select:!0})},t=function(e){if(b.paused||!c)return;let t=e.relatedTarget;null===t||c.contains(t)||T(m.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&T(c)});return c&&r.observe(c,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[n,c,b.paused]),s.useEffect(()=>{if(c){I.add(b);let e=document.activeElement;if(!c.contains(e)){let t=new CustomEvent(C,O);c.addEventListener(C,h),c.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=document.activeElement;for(let n of e)if(T(n,{select:t}),document.activeElement!==r)return}(N(c).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&T(c))}return()=>{c.removeEventListener(C,h),setTimeout(()=>{let t=new CustomEvent(w,O);c.addEventListener(w,f),c.dispatchEvent(t),t.defaultPrevented||T(null!=e?e:document.body,{select:!0}),c.removeEventListener(w,f),I.remove(b)},0)}}},[c,h,f,b]);let g=s.useCallback(e=>{if(!r&&!n||b.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,i=document.activeElement;if(t&&i){let t=e.currentTarget,[n,s]=function(e){let t=N(e);return[D(t,e),D(t.reverse(),e)]}(t);n&&s?e.shiftKey||i!==s?e.shiftKey&&i===n&&(e.preventDefault(),r&&T(s,{select:!0})):(e.preventDefault(),r&&T(n,{select:!0})):i===t&&e.preventDefault()}},[r,n,b.paused]);return(0,l.jsx)(p.div,{tabIndex:-1,...a,ref:y,onKeyDown:g})});function N(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function D(e,t){for(let r of e)if(!function(e,t){let{upTo:r}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===r||e!==r);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function T(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}S.displayName="FocusScope";var I=(n=[],{add(e){let t=n[0];e!==t&&(null==t||t.pause()),(n=F(n,e)).unshift(e)},remove(e){var t;null===(t=(n=F(n,e))[0])||void 0===t||t.resume()}});function F(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}var x=r(61188),P=s.forwardRef((e,t)=>{var r,n;let{container:i,...o}=e,[a,u]=s.useState(!1);(0,x.b)(()=>u(!0),[]);let c=i||a&&(null===(n=globalThis)||void 0===n?void 0:null===(r=n.document)||void 0===r?void 0:r.body);return c?h.createPortal((0,l.jsx)(p.div,{...o,ref:t}),c):null});P.displayName="Portal";var L=e=>{var t,r;let n,i;let{present:o,children:a}=e,l=function(e){var t,r;let[n,i]=s.useState(),o=s.useRef({}),a=s.useRef(e),u=s.useRef("none"),[l,c]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},s.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return s.useEffect(()=>{let e=M(o.current);u.current="mounted"===l?e:"none"},[l]),(0,x.b)(()=>{let t=o.current,r=a.current;if(r!==e){let n=u.current,i=M(t);e?c("MOUNT"):"none"===i||(null==t?void 0:t.display)==="none"?c("UNMOUNT"):r&&n!==i?c("ANIMATION_OUT"):c("UNMOUNT"),a.current=e}},[e,c]),(0,x.b)(()=>{if(n){var e;let t;let r=null!==(e=n.ownerDocument.defaultView)&&void 0!==e?e:window,i=e=>{let i=M(o.current).includes(e.animationName);if(e.target===n&&i&&(c("ANIMATION_END"),!a.current)){let e=n.style.animationFillMode;n.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===n.style.animationFillMode&&(n.style.animationFillMode=e)})}},s=e=>{e.target===n&&(u.current=M(o.current))};return n.addEventListener("animationstart",s),n.addEventListener("animationcancel",i),n.addEventListener("animationend",i),()=>{r.clearTimeout(t),n.removeEventListener("animationstart",s),n.removeEventListener("animationcancel",i),n.removeEventListener("animationend",i)}}c("ANIMATION_END")},[n,c]),{isPresent:["mounted","unmountSuspended"].includes(l),ref:s.useCallback(e=>{e&&(o.current=getComputedStyle(e)),i(e)},[])}}(o),c="function"==typeof a?a({present:l.isPresent}):s.Children.only(a),d=u(l.ref,(n=null===(t=Object.getOwnPropertyDescriptor(c.props,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in n&&n.isReactWarning?c.ref:(n=null===(r=Object.getOwnPropertyDescriptor(c,"ref"))||void 0===r?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning?c.props.ref:c.props.ref||c.ref);return"function"==typeof a||l.isPresent?s.cloneElement(c,{ref:d}):null};function M(e){return(null==e?void 0:e.animationName)||"none"}L.displayName="Presence";var k=0;function Q(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var A=r(5853),U=r(85770),j=r(17325),_=(0,r(31412)._)(),W=function(){},q=s.forwardRef(function(e,t){var r=s.useRef(null),n=s.useState({onScrollCapture:W,onWheelCapture:W,onTouchMoveCapture:W}),i=n[0],o=n[1],a=e.forwardProps,u=e.children,l=e.className,c=e.removeScrollBar,d=e.enabled,h=e.shards,f=e.sideCar,p=e.noIsolation,v=e.inert,m=e.allowPinchZoom,y=e.as,b=e.gapMode,g=(0,A._T)(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),R=(0,j.q)([r,t]),E=(0,A.pi)((0,A.pi)({},g),i);return s.createElement(s.Fragment,null,d&&s.createElement(f,{sideCar:_,removeScrollBar:c,shards:h,noIsolation:p,inert:v,setCallbacks:o,allowPinchZoom:!!m,lockRef:r,gapMode:b}),a?s.cloneElement(s.Children.only(u),(0,A.pi)((0,A.pi)({},E),{ref:R})):s.createElement(void 0===y?"div":y,(0,A.pi)({},E,{className:l,ref:R}),u))});q.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},q.classNames={fullWidth:U.zi,zeroRight:U.pF};var K=r(49085),B=r(5517),z=r(18704),V=!1;if("undefined"!=typeof window)try{var H=Object.defineProperty({},"passive",{get:function(){return V=!0,!0}});window.addEventListener("test",H,H),window.removeEventListener("test",H,H)}catch(e){V=!1}var Z=!!V&&{passive:!1},$=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&!(r.overflowY===r.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===r[t])},Y=function(e,t){var r=t.ownerDocument,n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),G(e,n)){var i=X(e,n);if(i[1]>i[2])return!0}n=n.parentNode}while(n&&n!==r.body);return!1},G=function(e,t){return"v"===e?$(t,"overflowY"):$(t,"overflowX")},X=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},J=function(e,t,r,n,i){var s,o=(s=window.getComputedStyle(t).direction,"h"===e&&"rtl"===s?-1:1),a=o*n,u=r.target,l=t.contains(u),c=!1,d=a>0,h=0,f=0;do{var p=X(e,u),v=p[0],m=p[1]-p[2]-o*v;(v||m)&&G(e,u)&&(h+=m,f+=v),u instanceof ShadowRoot?u=u.host:u=u.parentNode}while(!l&&u!==document.body||l&&(t.contains(u)||t===u));return d&&(i&&1>Math.abs(h)||!i&&a>h)?c=!0:!d&&(i&&1>Math.abs(f)||!i&&-a>f)&&(c=!0),c},ee=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},et=function(e){return[e.deltaX,e.deltaY]},er=function(e){return e&&"current"in e?e.current:e},en=0,ei=[],es=(0,K.L)(_,function(e){var t=s.useRef([]),r=s.useRef([0,0]),n=s.useRef(),i=s.useState(en++)[0],o=s.useState(z.Ws)[0],a=s.useRef(e);s.useEffect(function(){a.current=e},[e]),s.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(i));var t=(0,A.ev)([e.lockRef.current],(e.shards||[]).map(er),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(i))}),function(){document.body.classList.remove("block-interactivity-".concat(i)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(i))})}}},[e.inert,e.lockRef.current,e.shards]);var u=s.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var i,s=ee(e),o=r.current,u="deltaX"in e?e.deltaX:o[0]-s[0],l="deltaY"in e?e.deltaY:o[1]-s[1],c=e.target,d=Math.abs(u)>Math.abs(l)?"h":"v";if("touches"in e&&"h"===d&&"range"===c.type)return!1;var h=Y(d,c);if(!h)return!0;if(h?i=d:(i="v"===d?"h":"v",h=Y(d,c)),!h)return!1;if(!n.current&&"changedTouches"in e&&(u||l)&&(n.current=i),!i)return!0;var f=n.current||i;return J(f,t,e,"h"===f?u:l,!0)},[]),l=s.useCallback(function(e){if(ei.length&&ei[ei.length-1]===o){var r="deltaY"in e?et(e):ee(e),n=t.current.filter(function(t){var n;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(n=t.delta)[0]===r[0]&&n[1]===r[1]})[0];if(n&&n.should){e.cancelable&&e.preventDefault();return}if(!n){var i=(a.current.shards||[]).map(er).filter(Boolean).filter(function(t){return t.contains(e.target)});(i.length>0?u(e,i[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=s.useCallback(function(e,r,n,i){var s={name:e,delta:r,target:n,should:i,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(n)};t.current.push(s),setTimeout(function(){t.current=t.current.filter(function(e){return e!==s})},1)},[]),d=s.useCallback(function(e){r.current=ee(e),n.current=void 0},[]),h=s.useCallback(function(t){c(t.type,et(t),t.target,u(t,e.lockRef.current))},[]),f=s.useCallback(function(t){c(t.type,ee(t),t.target,u(t,e.lockRef.current))},[]);s.useEffect(function(){return ei.push(o),e.setCallbacks({onScrollCapture:h,onWheelCapture:h,onTouchMoveCapture:f}),document.addEventListener("wheel",l,Z),document.addEventListener("touchmove",l,Z),document.addEventListener("touchstart",d,Z),function(){ei=ei.filter(function(e){return e!==o}),document.removeEventListener("wheel",l,Z),document.removeEventListener("touchmove",l,Z),document.removeEventListener("touchstart",d,Z)}},[]);var p=e.removeScrollBar,v=e.inert;return s.createElement(s.Fragment,null,v?s.createElement(o,{styles:"\n  .block-interactivity-".concat(i," {pointer-events: none;}\n  .allow-interactivity-").concat(i," {pointer-events: all;}\n")}):null,p?s.createElement(B.jp,{gapMode:e.gapMode}):null)}),eo=s.forwardRef(function(e,t){return s.createElement(q,(0,A.pi)({},e,{ref:t,sideCar:es}))});eo.classNames=q.classNames;var ea=r(5478),eu="Dialog",[el,ec]=function(e,t=[]){let r=[],n=()=>{let t=r.map(e=>s.createContext(e));return function(r){let n=r?.[e]||t;return s.useMemo(()=>({[`__scope${e}`]:{...r,[e]:n}}),[r,n])}};return n.scopeName=e,[function(t,n){let i=s.createContext(n),o=r.length;r=[...r,n];let a=t=>{let{scope:r,children:n,...a}=t,u=r?.[e]?.[o]||i,c=s.useMemo(()=>a,Object.values(a));return(0,l.jsx)(u.Provider,{value:c,children:n})};return a.displayName=t+"Provider",[a,function(r,a){let u=a?.[e]?.[o]||i,l=s.useContext(u);if(l)return l;if(void 0!==n)return n;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=r.reduce((t,{useScope:r,scopeName:n})=>{let i=r(e)[`__scope${n}`];return{...t,...i}},{});return s.useMemo(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return r.scopeName=t.scopeName,r}(n,...t)]}(eu),[ed,eh]=el(eu),ef=e=>{let{__scopeDialog:t,children:r,open:n,defaultOpen:i,onOpenChange:o,modal:a=!0}=e,u=s.useRef(null),h=s.useRef(null),[f=!1,p]=(0,d.T)({prop:n,defaultProp:i,onChange:o});return(0,l.jsx)(ed,{scope:t,triggerRef:u,contentRef:h,contentId:(0,c.M)(),titleId:(0,c.M)(),descriptionId:(0,c.M)(),open:f,onOpenChange:p,onOpenToggle:s.useCallback(()=>p(e=>!e),[p]),modal:a,children:r})};ef.displayName=eu;var ep="DialogTrigger",ev=s.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=eh(ep,r),s=u(t,i.triggerRef);return(0,l.jsx)(p.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":eM(i.open),...n,ref:s,onClick:o(e.onClick,i.onOpenToggle)})});ev.displayName=ep;var em="DialogPortal",[ey,eb]=el(em,{forceMount:void 0}),eg=e=>{let{__scopeDialog:t,forceMount:r,children:n,container:i}=e,o=eh(em,t);return(0,l.jsx)(ey,{scope:t,forceMount:r,children:s.Children.map(n,e=>(0,l.jsx)(L,{present:r||o.open,children:(0,l.jsx)(P,{asChild:!0,container:i,children:e})}))})};eg.displayName=em;var eR="DialogOverlay",eE=s.forwardRef((e,t)=>{let r=eb(eR,e.__scopeDialog),{forceMount:n=r.forceMount,...i}=e,s=eh(eR,e.__scopeDialog);return s.modal?(0,l.jsx)(L,{present:n||s.open,children:(0,l.jsx)(eC,{...i,ref:t})}):null});eE.displayName=eR;var eC=s.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=eh(eR,r);return(0,l.jsx)(eo,{as:f.g7,allowPinchZoom:!0,shards:[i.contentRef],children:(0,l.jsx)(p.div,{"data-state":eM(i.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),ew="DialogContent",eO=s.forwardRef((e,t)=>{let r=eb(ew,e.__scopeDialog),{forceMount:n=r.forceMount,...i}=e,s=eh(ew,e.__scopeDialog);return(0,l.jsx)(L,{present:n||s.open,children:s.modal?(0,l.jsx)(eS,{...i,ref:t}):(0,l.jsx)(eN,{...i,ref:t})})});eO.displayName=ew;var eS=s.forwardRef((e,t)=>{let r=eh(ew,e.__scopeDialog),n=s.useRef(null),i=u(t,r.contentRef,n);return s.useEffect(()=>{let e=n.current;if(e)return(0,ea.Ry)(e)},[]),(0,l.jsx)(eD,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:o(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=r.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:o(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:o(e.onFocusOutside,e=>e.preventDefault())})}),eN=s.forwardRef((e,t)=>{let r=eh(ew,e.__scopeDialog),n=s.useRef(!1),i=s.useRef(!1);return(0,l.jsx)(eD,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var s,o;null===(s=e.onCloseAutoFocus)||void 0===s||s.call(e,t),t.defaultPrevented||(n.current||null===(o=r.triggerRef.current)||void 0===o||o.focus(),t.preventDefault()),n.current=!1,i.current=!1},onInteractOutside:t=>{var s,o;null===(s=e.onInteractOutside)||void 0===s||s.call(e,t),t.defaultPrevented||(n.current=!0,"pointerdown"!==t.detail.originalEvent.type||(i.current=!0));let a=t.target;(null===(o=r.triggerRef.current)||void 0===o?void 0:o.contains(a))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),eD=s.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:n,onOpenAutoFocus:i,onCloseAutoFocus:o,...a}=e,c=eh(ew,r),d=s.useRef(null),h=u(t,d);return s.useEffect(()=>{var e,t;let r=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=r[0])&&void 0!==e?e:Q()),document.body.insertAdjacentElement("beforeend",null!==(t=r[1])&&void 0!==t?t:Q()),k++,()=>{1===k&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),k--}},[]),(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(S,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:i,onUnmountAutoFocus:o,children:(0,l.jsx)(g,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":eM(c.open),...a,ref:h,onDismiss:()=>c.onOpenChange(!1)})}),(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(eU,{titleId:c.titleId}),(0,l.jsx)(ej,{contentRef:d,descriptionId:c.descriptionId})]})]})}),eT="DialogTitle",eI=s.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=eh(eT,r);return(0,l.jsx)(p.h2,{id:i.titleId,...n,ref:t})});eI.displayName=eT;var eF="DialogDescription",ex=s.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=eh(eF,r);return(0,l.jsx)(p.p,{id:i.descriptionId,...n,ref:t})});ex.displayName=eF;var eP="DialogClose",eL=s.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,i=eh(eP,r);return(0,l.jsx)(p.button,{type:"button",...n,ref:t,onClick:o(e.onClick,()=>i.onOpenChange(!1))})});function eM(e){return e?"open":"closed"}eL.displayName=eP;var ek="DialogTitleWarning",[eQ,eA]=function(e,t){let r=s.createContext(t),n=e=>{let{children:t,...n}=e,i=s.useMemo(()=>n,Object.values(n));return(0,l.jsx)(r.Provider,{value:i,children:t})};return n.displayName=e+"Provider",[n,function(n){let i=s.useContext(r);if(i)return i;if(void 0!==t)return t;throw Error(`\`${n}\` must be used within \`${e}\``)}]}(ek,{contentName:ew,titleName:eT,docsSlug:"dialog"}),eU=e=>{let{titleId:t}=e,r=eA(ek),n="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return s.useEffect(()=>{t&&!document.getElementById(t)&&console.error(n)},[n,t]),null},ej=e=>{let{contentRef:t,descriptionId:r}=e,n=eA("DialogDescriptionWarning"),i="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(n.contentName,"}.");return s.useEffect(()=>{var e;let n=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");r&&n&&!document.getElementById(r)&&console.warn(i)},[i,t,r]),null},e_=ef,eW=ev,eq=eg,eK=eE,eB=eO,ez=eI,eV=ex,eH=eL},90759:function(e,t,r){r.d(t,{f:function(){return c}});var n=r(2265);r(54887);var i=r(98482),s=r(57437),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=n.forwardRef((e,r)=>{let{asChild:n,...o}=e,a=n?i.g7:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(a,{...o,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{}),a="horizontal",u=["horizontal","vertical"],l=n.forwardRef((e,t)=>{let{decorative:r,orientation:n=a,...i}=e,l=u.includes(n)?n:a;return(0,s.jsx)(o.div,{"data-orientation":l,...r?{role:"none"}:{"aria-orientation":"vertical"===l?l:void 0,role:"separator"},...i,ref:t})});l.displayName="Separator";var c=l},21733:function(e,t,r){r.d(t,{A:function(){return a},z:function(){return u}});var n=r(45345),i=r(18238),s=r(11255),o=r(7989),a=class extends o.F{#e;#t;#r;#n;#i;#s;constructor(e){super(),this.#s=!1,this.#i=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#r=e.cache,this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#e=function(e){let t="function"==typeof e.initialData?e.initialData():e.initialData,r=void 0!==t,n=r?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:r?n??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#e,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#n?.promise}setOptions(e){this.options={...this.#i,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#r.remove(this)}setData(e,t){let r=(0,n.oE)(this.state.data,e,this.options);return this.#o({data:r,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),r}setState(e,t){this.#o({type:"setState",state:e,setStateOptions:t})}cancel(e){let t=this.#n?.promise;return this.#n?.cancel(e),t?t.then(n.ZT).catch(n.ZT):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#e)}isActive(){return this.observers.some(e=>!1!==(0,n.Nc)(e.options.enabled,this))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===n.CN||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some(e=>e.getCurrentResult().isStale):void 0===this.state.data)}isStaleByTime(e=0){return this.state.isInvalidated||void 0===this.state.data||!(0,n.Kp)(this.state.dataUpdatedAt,e)}onFocus(){let e=this.observers.find(e=>e.shouldFetchOnWindowFocus());e?.refetch({cancelRefetch:!1}),this.#n?.continue()}onOnline(){let e=this.observers.find(e=>e.shouldFetchOnReconnect());e?.refetch({cancelRefetch:!1}),this.#n?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#r.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(t=>t!==e),this.observers.length||(this.#n&&(this.#s?this.#n.cancel({revert:!0}):this.#n.cancelRetry()),this.scheduleGc()),this.#r.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#o({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus){if(void 0!==this.state.data&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#n)return this.#n.continueRetry(),this.#n.promise}if(e&&this.setOptions(e),!this.options.queryFn){let e=this.observers.find(e=>e.options.queryFn);e&&this.setOptions(e.options)}let r=new AbortController,i=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#s=!0,r.signal)})},o={fetchOptions:t,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:()=>{let e=(0,n.cG)(this.options,t),r={queryKey:this.queryKey,meta:this.meta};return(i(r),this.#s=!1,this.options.persister)?this.options.persister(e,r,this):e(r)}};i(o),this.options.behavior?.onFetch(o,this),this.#t=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==o.fetchOptions?.meta)&&this.#o({type:"fetch",meta:o.fetchOptions?.meta});let a=e=>{(0,s.DV)(e)&&e.silent||this.#o({type:"error",error:e}),(0,s.DV)(e)||(this.#r.config.onError?.(e,this),this.#r.config.onSettled?.(this.state.data,e,this)),this.isFetchingOptimistic||this.scheduleGc(),this.isFetchingOptimistic=!1};return this.#n=(0,s.Mz)({initialPromise:t?.initialPromise,fn:o.fetchFn,abort:r.abort.bind(r),onSuccess:e=>{if(void 0===e){a(Error(`${this.queryHash} data is undefined`));return}try{this.setData(e)}catch(e){a(e);return}this.#r.config.onSuccess?.(e,this),this.#r.config.onSettled?.(e,this.state.error,this),this.isFetchingOptimistic||this.scheduleGc(),this.isFetchingOptimistic=!1},onError:a,onFail:(e,t)=>{this.#o({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#o({type:"pause"})},onContinue:()=>{this.#o({type:"continue"})},retry:o.options.retry,retryDelay:o.options.retryDelay,networkMode:o.options.networkMode,canRun:()=>!0}),this.#n.start()}#o(e){this.state=(t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":return{...t,...u(t.data,this.options),fetchMeta:e.meta??null};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let r=e.error;if((0,s.DV)(r)&&r.revert&&this.#t)return{...this.#t,fetchStatus:"idle"};return{...t,error:r,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:r,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),i.V.batch(()=>{this.observers.forEach(e=>{e.onQueryUpdate()}),this.#r.notify({query:this,type:"updated",action:e})})}};function u(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:(0,s.Kw)(t.networkMode)?"fetching":"paused",...void 0===e&&{error:null,status:"pending"}}}},16593:function(e,t,r){let n;r.d(t,{a:function(){return I}});var i=r(87045),s=r(18238),o=r(21733),a=r(24112),u=r(16803),l=r(45345),c=class extends a.l{constructor(e,t){super(),this.options=t,this.#a=e,this.#u=null,this.#l=(0,u.O)(),this.options.experimental_prefetchInRender||this.#l.reject(Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(t)}#a;#c=void 0;#d=void 0;#h=void 0;#f;#p;#l;#u;#v;#m;#y;#b;#g;#R;#E=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#c.addObserver(this),d(this.#c,this.options)?this.#C():this.updateResult(),this.#w())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return h(this.#c,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return h(this.#c,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#O(),this.#S(),this.#c.removeObserver(this)}setOptions(e,t){let r=this.options,n=this.#c;if(this.options=this.#a.defaultQueryOptions(e),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,l.Nc)(this.options.enabled,this.#c))throw Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#N(),this.#c.setOptions(this.options),r._defaulted&&!(0,l.VS)(this.options,r)&&this.#a.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#c,observer:this});let i=this.hasListeners();i&&f(this.#c,n,this.options,r)&&this.#C(),this.updateResult(t),i&&(this.#c!==n||(0,l.Nc)(this.options.enabled,this.#c)!==(0,l.Nc)(r.enabled,this.#c)||(0,l.KC)(this.options.staleTime,this.#c)!==(0,l.KC)(r.staleTime,this.#c))&&this.#D();let s=this.#T();i&&(this.#c!==n||(0,l.Nc)(this.options.enabled,this.#c)!==(0,l.Nc)(r.enabled,this.#c)||s!==this.#R)&&this.#I(s)}getOptimisticResult(e){let t=this.#a.getQueryCache().build(this.#a,e),r=this.createResult(t,e);return(0,l.VS)(this.getCurrentResult(),r)||(this.#h=r,this.#p=this.options,this.#f=this.#c.state),r}getCurrentResult(){return this.#h}trackResult(e,t){let r={};return Object.keys(e).forEach(n=>{Object.defineProperty(r,n,{configurable:!1,enumerable:!0,get:()=>(this.trackProp(n),t?.(n),e[n])})}),r}trackProp(e){this.#E.add(e)}getCurrentQuery(){return this.#c}refetch({...e}={}){return this.fetch({...e})}fetchOptimistic(e){let t=this.#a.defaultQueryOptions(e),r=this.#a.getQueryCache().build(this.#a,t);return r.isFetchingOptimistic=!0,r.fetch().then(()=>this.createResult(r,t))}fetch(e){return this.#C({...e,cancelRefetch:e.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#h))}#C(e){this.#N();let t=this.#c.fetch(this.options,e);return e?.throwOnError||(t=t.catch(l.ZT)),t}#D(){this.#O();let e=(0,l.KC)(this.options.staleTime,this.#c);if(l.sk||this.#h.isStale||!(0,l.PN)(e))return;let t=(0,l.Kp)(this.#h.dataUpdatedAt,e);this.#b=setTimeout(()=>{this.#h.isStale||this.updateResult()},t+1)}#T(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#c):this.options.refetchInterval)??!1}#I(e){this.#S(),this.#R=e,!l.sk&&!1!==(0,l.Nc)(this.options.enabled,this.#c)&&(0,l.PN)(this.#R)&&0!==this.#R&&(this.#g=setInterval(()=>{(this.options.refetchIntervalInBackground||i.j.isFocused())&&this.#C()},this.#R))}#w(){this.#D(),this.#I(this.#T())}#O(){this.#b&&(clearTimeout(this.#b),this.#b=void 0)}#S(){this.#g&&(clearInterval(this.#g),this.#g=void 0)}createResult(e,t){let r;let n=this.#c,i=this.options,s=this.#h,a=this.#f,u=this.#p,c=e!==n?e.state:this.#d,{state:h}=e,v={...h},m=!1;if(t._optimisticResults){let r=this.hasListeners(),s=!r&&d(e,t),a=r&&f(e,n,t,i);(s||a)&&(v={...v,...(0,o.z)(h.data,e.options)}),"isRestoring"===t._optimisticResults&&(v.fetchStatus="idle")}let{error:y,errorUpdatedAt:b,status:g}=v;if(t.select&&void 0!==v.data){if(s&&v.data===a?.data&&t.select===this.#v)r=this.#m;else try{this.#v=t.select,r=t.select(v.data),r=(0,l.oE)(s?.data,r,t),this.#m=r,this.#u=null}catch(e){this.#u=e}}else r=v.data;if(void 0!==t.placeholderData&&void 0===r&&"pending"===g){let e;if(s?.isPlaceholderData&&t.placeholderData===u?.placeholderData)e=s.data;else if(e="function"==typeof t.placeholderData?t.placeholderData(this.#y?.state.data,this.#y):t.placeholderData,t.select&&void 0!==e)try{e=t.select(e),this.#u=null}catch(e){this.#u=e}void 0!==e&&(g="success",r=(0,l.oE)(s?.data,e,t),m=!0)}this.#u&&(y=this.#u,r=this.#m,b=Date.now(),g="error");let R="fetching"===v.fetchStatus,E="pending"===g,C="error"===g,w=E&&R,O=void 0!==r;return{status:g,fetchStatus:v.fetchStatus,isPending:E,isSuccess:"success"===g,isError:C,isInitialLoading:w,isLoading:w,data:r,dataUpdatedAt:v.dataUpdatedAt,error:y,errorUpdatedAt:b,failureCount:v.fetchFailureCount,failureReason:v.fetchFailureReason,errorUpdateCount:v.errorUpdateCount,isFetched:v.dataUpdateCount>0||v.errorUpdateCount>0,isFetchedAfterMount:v.dataUpdateCount>c.dataUpdateCount||v.errorUpdateCount>c.errorUpdateCount,isFetching:R,isRefetching:R&&!E,isLoadingError:C&&!O,isPaused:"paused"===v.fetchStatus,isPlaceholderData:m,isRefetchError:C&&O,isStale:p(e,t),refetch:this.refetch,promise:this.#l}}updateResult(e){let t=this.#h,r=this.createResult(this.#c,this.options);if(this.#f=this.#c.state,this.#p=this.options,void 0!==this.#f.data&&(this.#y=this.#c),(0,l.VS)(r,t))return;if(this.options.experimental_prefetchInRender){let e=e=>{"error"===r.status?e.reject(r.error):void 0!==r.data&&e.resolve(r.data)},t=()=>{e(this.#l=r.promise=(0,u.O)())},n=this.#l;switch(n.status){case"pending":e(n);break;case"fulfilled":("error"===r.status||r.data!==n.value)&&t();break;case"rejected":("error"!==r.status||r.error!==n.reason)&&t()}}this.#h=r;let n={};e?.listeners!==!1&&(()=>{if(!t)return!0;let{notifyOnChangeProps:e}=this.options,r="function"==typeof e?e():e;if("all"===r||!r&&!this.#E.size)return!0;let n=new Set(r??this.#E);return this.options.throwOnError&&n.add("error"),Object.keys(this.#h).some(e=>this.#h[e]!==t[e]&&n.has(e))})()&&(n.listeners=!0),this.#F({...n,...e})}#N(){let e=this.#a.getQueryCache().build(this.#a,this.options);if(e===this.#c)return;let t=this.#c;this.#c=e,this.#d=e.state,this.hasListeners()&&(t?.removeObserver(this),e.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#w()}#F(e){s.V.batch(()=>{e.listeners&&this.listeners.forEach(e=>{e(this.#h)}),this.#a.getQueryCache().notify({query:this.#c,type:"observerResultsUpdated"})})}};function d(e,t){return!1!==(0,l.Nc)(t.enabled,e)&&void 0===e.state.data&&!("error"===e.state.status&&!1===t.retryOnMount)||void 0!==e.state.data&&h(e,t,t.refetchOnMount)}function h(e,t,r){if(!1!==(0,l.Nc)(t.enabled,e)){let n="function"==typeof r?r(e):r;return"always"===n||!1!==n&&p(e,t)}return!1}function f(e,t,r,n){return(e!==t||!1===(0,l.Nc)(n.enabled,e))&&(!r.suspense||"error"!==e.state.status)&&p(e,r)}function p(e,t){return!1!==(0,l.Nc)(t.enabled,e)&&e.isStaleByTime((0,l.KC)(t.staleTime,e))}var v=r(2265),m=r(29827);r(57437);var y=v.createContext((n=!1,{clearReset:()=>{n=!1},reset:()=>{n=!0},isReset:()=>n})),b=()=>v.useContext(y),g=r(51172),R=(e,t)=>{(e.suspense||e.throwOnError)&&!t.isReset()&&(e.retryOnMount=!1)},E=e=>{v.useEffect(()=>{e.clearReset()},[e])},C=e=>{let{result:t,errorResetBoundary:r,throwOnError:n,query:i}=e;return t.isError&&!r.isReset()&&!t.isFetching&&i&&(0,g.L)(n,[t.error,i])},w=v.createContext(!1),O=()=>v.useContext(w);w.Provider;var S=e=>{e.suspense&&("number"!=typeof e.staleTime&&(e.staleTime=1e3),"number"==typeof e.gcTime&&(e.gcTime=Math.max(e.gcTime,1e3)))},N=(e,t)=>e.isLoading&&e.isFetching&&!t,D=(e,t)=>e?.suspense&&t.isPending,T=(e,t,r)=>t.fetchOptimistic(e).catch(()=>{r.clearReset()});function I(e,t){return function(e,t,r){var n,i,o,a,u;let c=(0,m.NL)(r),d=O(),h=b(),f=c.defaultQueryOptions(e);null===(i=c.getDefaultOptions().queries)||void 0===i||null===(n=i._experimental_beforeQuery)||void 0===n||n.call(i,f),f._optimisticResults=d?"isRestoring":"optimistic",S(f),R(f,h),E(h);let p=!c.getQueryCache().get(f.queryHash),[y]=v.useState(()=>new t(c,f)),w=y.getOptimisticResult(f);if(v.useSyncExternalStore(v.useCallback(e=>{let t=d?()=>void 0:y.subscribe(s.V.batchCalls(e));return y.updateResult(),t},[y,d]),()=>y.getCurrentResult(),()=>y.getCurrentResult()),v.useEffect(()=>{y.setOptions(f,{listeners:!1})},[f,y]),D(f,w))throw T(f,y,h);if(C({result:w,errorResetBoundary:h,throwOnError:f.throwOnError,query:c.getQueryCache().get(f.queryHash)}))throw w.error;if(null===(a=c.getDefaultOptions().queries)||void 0===a||null===(o=a._experimental_afterQuery)||void 0===o||o.call(a,f,w),f.experimental_prefetchInRender&&!l.sk&&N(w,d)){let e=p?T(f,y,h):null===(u=c.getQueryCache().get(f.queryHash))||void 0===u?void 0:u.promise;null==e||e.catch(g.Z).finally(()=>{y.hasListeners()||y.updateResult()})}return f.notifyOnChangeProps?w:y.trackResult(w)}(e,c,t)}}}]);