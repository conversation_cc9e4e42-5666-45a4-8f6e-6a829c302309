(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3675],{61134:function(t,e,r){var n;!function(o){"use strict";var i,a={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},u=!0,c="[DecimalError] ",s=c+"Invalid argument: ",l=c+"Exponent out of range: ",f=Math.floor,p=Math.pow,h=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,d=f(1286742750677284.5),y={};function v(t,e){var r,n,o,i,a,c,s,l,f=t.constructor,p=f.precision;if(!t.s||!e.s)return e.s||(e=new f(t)),u?A(e,p):e;if(s=t.d,l=e.d,a=t.e,o=e.e,s=s.slice(),i=a-o){for(i<0?(n=s,i=-i,c=l.length):(n=l,o=a,c=s.length),i>(c=(a=Math.ceil(p/7))>c?a+1:c+1)&&(i=c,n.length=1),n.reverse();i--;)n.push(0);n.reverse()}for((c=s.length)-(i=l.length)<0&&(i=c,n=l,l=s,s=n),r=0;i;)r=(s[--i]=s[i]+l[i]+r)/1e7|0,s[i]%=1e7;for(r&&(s.unshift(r),++o),c=s.length;0==s[--c];)s.pop();return e.d=s,e.e=o,u?A(e,p):e}function m(t,e,r){if(t!==~~t||t<e||t>r)throw Error(s+t)}function b(t){var e,r,n,o=t.length-1,i="",a=t[0];if(o>0){for(i+=a,e=1;e<o;e++)(r=7-(n=t[e]+"").length)&&(i+=j(r)),i+=n;(r=7-(n=(a=t[e])+"").length)&&(i+=j(r))}else if(0===a)return"0";for(;a%10==0;)a/=10;return i+a}y.absoluteValue=y.abs=function(){var t=new this.constructor(this);return t.s&&(t.s=1),t},y.comparedTo=y.cmp=function(t){var e,r,n,o;if(t=new this.constructor(t),this.s!==t.s)return this.s||-t.s;if(this.e!==t.e)return this.e>t.e^this.s<0?1:-1;for(e=0,r=(n=this.d.length)<(o=t.d.length)?n:o;e<r;++e)if(this.d[e]!==t.d[e])return this.d[e]>t.d[e]^this.s<0?1:-1;return n===o?0:n>o^this.s<0?1:-1},y.decimalPlaces=y.dp=function(){var t=this.d.length-1,e=(t-this.e)*7;if(t=this.d[t])for(;t%10==0;t/=10)e--;return e<0?0:e},y.dividedBy=y.div=function(t){return g(this,new this.constructor(t))},y.dividedToIntegerBy=y.idiv=function(t){var e=this.constructor;return A(g(this,new e(t),0,1),e.precision)},y.equals=y.eq=function(t){return!this.cmp(t)},y.exponent=function(){return O(this)},y.greaterThan=y.gt=function(t){return this.cmp(t)>0},y.greaterThanOrEqualTo=y.gte=function(t){return this.cmp(t)>=0},y.isInteger=y.isint=function(){return this.e>this.d.length-2},y.isNegative=y.isneg=function(){return this.s<0},y.isPositive=y.ispos=function(){return this.s>0},y.isZero=function(){return 0===this.s},y.lessThan=y.lt=function(t){return 0>this.cmp(t)},y.lessThanOrEqualTo=y.lte=function(t){return 1>this.cmp(t)},y.logarithm=y.log=function(t){var e,r=this.constructor,n=r.precision,o=n+5;if(void 0===t)t=new r(10);else if((t=new r(t)).s<1||t.eq(i))throw Error(c+"NaN");if(this.s<1)throw Error(c+(this.s?"NaN":"-Infinity"));return this.eq(i)?new r(0):(u=!1,e=g(S(this,o),S(t,o),o),u=!0,A(e,n))},y.minus=y.sub=function(t){return t=new this.constructor(t),this.s==t.s?E(this,t):v(this,(t.s=-t.s,t))},y.modulo=y.mod=function(t){var e,r=this.constructor,n=r.precision;if(!(t=new r(t)).s)throw Error(c+"NaN");return this.s?(u=!1,e=g(this,t,0,1).times(t),u=!0,this.minus(e)):A(new r(this),n)},y.naturalExponential=y.exp=function(){return x(this)},y.naturalLogarithm=y.ln=function(){return S(this)},y.negated=y.neg=function(){var t=new this.constructor(this);return t.s=-t.s||0,t},y.plus=y.add=function(t){return t=new this.constructor(t),this.s==t.s?v(this,t):E(this,(t.s=-t.s,t))},y.precision=y.sd=function(t){var e,r,n;if(void 0!==t&&!!t!==t&&1!==t&&0!==t)throw Error(s+t);if(e=O(this)+1,r=7*(n=this.d.length-1)+1,n=this.d[n]){for(;n%10==0;n/=10)r--;for(n=this.d[0];n>=10;n/=10)r++}return t&&e>r?e:r},y.squareRoot=y.sqrt=function(){var t,e,r,n,o,i,a,s=this.constructor;if(this.s<1){if(!this.s)return new s(0);throw Error(c+"NaN")}for(t=O(this),u=!1,0==(o=Math.sqrt(+this))||o==1/0?(((e=b(this.d)).length+t)%2==0&&(e+="0"),o=Math.sqrt(e),t=f((t+1)/2)-(t<0||t%2),n=new s(e=o==1/0?"5e"+t:(e=o.toExponential()).slice(0,e.indexOf("e")+1)+t)):n=new s(o.toString()),o=a=(r=s.precision)+3;;)if(n=(i=n).plus(g(this,i,a+2)).times(.5),b(i.d).slice(0,a)===(e=b(n.d)).slice(0,a)){if(e=e.slice(a-3,a+1),o==a&&"4999"==e){if(A(i,r+1,0),i.times(i).eq(this)){n=i;break}}else if("9999"!=e)break;a+=4}return u=!0,A(n,r)},y.times=y.mul=function(t){var e,r,n,o,i,a,c,s,l,f=this.constructor,p=this.d,h=(t=new f(t)).d;if(!this.s||!t.s)return new f(0);for(t.s*=this.s,r=this.e+t.e,(s=p.length)<(l=h.length)&&(i=p,p=h,h=i,a=s,s=l,l=a),i=[],n=a=s+l;n--;)i.push(0);for(n=l;--n>=0;){for(e=0,o=s+n;o>n;)c=i[o]+h[n]*p[o-n-1]+e,i[o--]=c%1e7|0,e=c/1e7|0;i[o]=(i[o]+e)%1e7|0}for(;!i[--a];)i.pop();return e?++r:i.shift(),t.d=i,t.e=r,u?A(t,f.precision):t},y.toDecimalPlaces=y.todp=function(t,e){var r=this,n=r.constructor;return(r=new n(r),void 0===t)?r:(m(t,0,1e9),void 0===e?e=n.rounding:m(e,0,8),A(r,t+O(r)+1,e))},y.toExponential=function(t,e){var r,n=this,o=n.constructor;return void 0===t?r=k(n,!0):(m(t,0,1e9),void 0===e?e=o.rounding:m(e,0,8),r=k(n=A(new o(n),t+1,e),!0,t+1)),r},y.toFixed=function(t,e){var r,n,o=this.constructor;return void 0===t?k(this):(m(t,0,1e9),void 0===e?e=o.rounding:m(e,0,8),r=k((n=A(new o(this),t+O(this)+1,e)).abs(),!1,t+O(n)+1),this.isneg()&&!this.isZero()?"-"+r:r)},y.toInteger=y.toint=function(){var t=this.constructor;return A(new t(this),O(this)+1,t.rounding)},y.toNumber=function(){return+this},y.toPower=y.pow=function(t){var e,r,n,o,a,s,l=this,p=l.constructor,h=+(t=new p(t));if(!t.s)return new p(i);if(!(l=new p(l)).s){if(t.s<1)throw Error(c+"Infinity");return l}if(l.eq(i))return l;if(n=p.precision,t.eq(i))return A(l,n);if(s=(e=t.e)>=(r=t.d.length-1),a=l.s,s){if((r=h<0?-h:h)<=9007199254740991){for(o=new p(i),e=Math.ceil(n/7+4),u=!1;r%2&&M((o=o.times(l)).d,e),0!==(r=f(r/2));)M((l=l.times(l)).d,e);return u=!0,t.s<0?new p(i).div(o):A(o,n)}}else if(a<0)throw Error(c+"NaN");return a=a<0&&1&t.d[Math.max(e,r)]?-1:1,l.s=1,u=!1,o=t.times(S(l,n+12)),u=!0,(o=x(o)).s=a,o},y.toPrecision=function(t,e){var r,n,o=this,i=o.constructor;return void 0===t?(r=O(o),n=k(o,r<=i.toExpNeg||r>=i.toExpPos)):(m(t,1,1e9),void 0===e?e=i.rounding:m(e,0,8),r=O(o=A(new i(o),t,e)),n=k(o,t<=r||r<=i.toExpNeg,t)),n},y.toSignificantDigits=y.tosd=function(t,e){var r=this.constructor;return void 0===t?(t=r.precision,e=r.rounding):(m(t,1,1e9),void 0===e?e=r.rounding:m(e,0,8)),A(new r(this),t,e)},y.toString=y.valueOf=y.val=y.toJSON=function(){var t=O(this),e=this.constructor;return k(this,t<=e.toExpNeg||t>=e.toExpPos)};var g=function(){function t(t,e){var r,n=0,o=t.length;for(t=t.slice();o--;)r=t[o]*e+n,t[o]=r%1e7|0,n=r/1e7|0;return n&&t.unshift(n),t}function e(t,e,r,n){var o,i;if(r!=n)i=r>n?1:-1;else for(o=i=0;o<r;o++)if(t[o]!=e[o]){i=t[o]>e[o]?1:-1;break}return i}function r(t,e,r){for(var n=0;r--;)t[r]-=n,n=t[r]<e[r]?1:0,t[r]=1e7*n+t[r]-e[r];for(;!t[0]&&t.length>1;)t.shift()}return function(n,o,i,a){var u,s,l,f,p,h,d,y,v,m,b,g,x,w,j,S,P,E,k=n.constructor,M=n.s==o.s?1:-1,T=n.d,_=o.d;if(!n.s)return new k(n);if(!o.s)throw Error(c+"Division by zero");for(l=0,s=n.e-o.e,P=_.length,j=T.length,y=(d=new k(M)).d=[];_[l]==(T[l]||0);)++l;if(_[l]>(T[l]||0)&&--s,(g=null==i?i=k.precision:a?i+(O(n)-O(o))+1:i)<0)return new k(0);if(g=g/7+2|0,l=0,1==P)for(f=0,_=_[0],g++;(l<j||f)&&g--;l++)x=1e7*f+(T[l]||0),y[l]=x/_|0,f=x%_|0;else{for((f=1e7/(_[0]+1)|0)>1&&(_=t(_,f),T=t(T,f),P=_.length,j=T.length),w=P,m=(v=T.slice(0,P)).length;m<P;)v[m++]=0;(E=_.slice()).unshift(0),S=_[0],_[1]>=1e7/2&&++S;do f=0,(u=e(_,v,P,m))<0?(b=v[0],P!=m&&(b=1e7*b+(v[1]||0)),(f=b/S|0)>1?(f>=1e7&&(f=1e7-1),h=(p=t(_,f)).length,m=v.length,1==(u=e(p,v,h,m))&&(f--,r(p,P<h?E:_,h))):(0==f&&(u=f=1),p=_.slice()),(h=p.length)<m&&p.unshift(0),r(v,p,m),-1==u&&(m=v.length,(u=e(_,v,P,m))<1&&(f++,r(v,P<m?E:_,m))),m=v.length):0===u&&(f++,v=[0]),y[l++]=f,u&&v[0]?v[m++]=T[w]||0:(v=[T[w]],m=1);while((w++<j||void 0!==v[0])&&g--)}return y[0]||y.shift(),d.e=s,A(d,a?i+O(d)+1:i)}}();function x(t,e){var r,n,o,a,c,s=0,f=0,h=t.constructor,d=h.precision;if(O(t)>16)throw Error(l+O(t));if(!t.s)return new h(i);for(null==e?(u=!1,c=d):c=e,a=new h(.03125);t.abs().gte(.1);)t=t.times(a),f+=5;for(c+=Math.log(p(2,f))/Math.LN10*2+5|0,r=n=o=new h(i),h.precision=c;;){if(n=A(n.times(t),c),r=r.times(++s),b((a=o.plus(g(n,r,c))).d).slice(0,c)===b(o.d).slice(0,c)){for(;f--;)o=A(o.times(o),c);return h.precision=d,null==e?(u=!0,A(o,d)):o}o=a}}function O(t){for(var e=7*t.e,r=t.d[0];r>=10;r/=10)e++;return e}function w(t,e,r){if(e>t.LN10.sd())throw u=!0,r&&(t.precision=r),Error(c+"LN10 precision limit exceeded");return A(new t(t.LN10),e)}function j(t){for(var e="";t--;)e+="0";return e}function S(t,e){var r,n,o,a,s,l,f,p,h,d=1,y=t,v=y.d,m=y.constructor,x=m.precision;if(y.s<1)throw Error(c+(y.s?"NaN":"-Infinity"));if(y.eq(i))return new m(0);if(null==e?(u=!1,p=x):p=e,y.eq(10))return null==e&&(u=!0),w(m,p);if(p+=10,m.precision=p,n=(r=b(v)).charAt(0),!(15e14>Math.abs(a=O(y))))return f=w(m,p+2,x).times(a+""),y=S(new m(n+"."+r.slice(1)),p-10).plus(f),m.precision=x,null==e?(u=!0,A(y,x)):y;for(;n<7&&1!=n||1==n&&r.charAt(1)>3;)n=(r=b((y=y.times(t)).d)).charAt(0),d++;for(a=O(y),n>1?(y=new m("0."+r),a++):y=new m(n+"."+r.slice(1)),l=s=y=g(y.minus(i),y.plus(i),p),h=A(y.times(y),p),o=3;;){if(s=A(s.times(h),p),b((f=l.plus(g(s,new m(o),p))).d).slice(0,p)===b(l.d).slice(0,p))return l=l.times(2),0!==a&&(l=l.plus(w(m,p+2,x).times(a+""))),l=g(l,new m(d),p),m.precision=x,null==e?(u=!0,A(l,x)):l;l=f,o+=2}}function P(t,e){var r,n,o;for((r=e.indexOf("."))>-1&&(e=e.replace(".","")),(n=e.search(/e/i))>0?(r<0&&(r=n),r+=+e.slice(n+1),e=e.substring(0,n)):r<0&&(r=e.length),n=0;48===e.charCodeAt(n);)++n;for(o=e.length;48===e.charCodeAt(o-1);)--o;if(e=e.slice(n,o)){if(o-=n,r=r-n-1,t.e=f(r/7),t.d=[],n=(r+1)%7,r<0&&(n+=7),n<o){for(n&&t.d.push(+e.slice(0,n)),o-=7;n<o;)t.d.push(+e.slice(n,n+=7));n=7-(e=e.slice(n)).length}else n-=o;for(;n--;)e+="0";if(t.d.push(+e),u&&(t.e>d||t.e<-d))throw Error(l+r)}else t.s=0,t.e=0,t.d=[0];return t}function A(t,e,r){var n,o,i,a,c,s,h,y,v=t.d;for(a=1,i=v[0];i>=10;i/=10)a++;if((n=e-a)<0)n+=7,o=e,h=v[y=0];else{if((y=Math.ceil((n+1)/7))>=(i=v.length))return t;for(a=1,h=i=v[y];i>=10;i/=10)a++;n%=7,o=n-7+a}if(void 0!==r&&(c=h/(i=p(10,a-o-1))%10|0,s=e<0||void 0!==v[y+1]||h%i,s=r<4?(c||s)&&(0==r||r==(t.s<0?3:2)):c>5||5==c&&(4==r||s||6==r&&(n>0?o>0?h/p(10,a-o):0:v[y-1])%10&1||r==(t.s<0?8:7))),e<1||!v[0])return s?(i=O(t),v.length=1,e=e-i-1,v[0]=p(10,(7-e%7)%7),t.e=f(-e/7)||0):(v.length=1,v[0]=t.e=t.s=0),t;if(0==n?(v.length=y,i=1,y--):(v.length=y+1,i=p(10,7-n),v[y]=o>0?(h/p(10,a-o)%p(10,o)|0)*i:0),s)for(;;){if(0==y){1e7==(v[0]+=i)&&(v[0]=1,++t.e);break}if(v[y]+=i,1e7!=v[y])break;v[y--]=0,i=1}for(n=v.length;0===v[--n];)v.pop();if(u&&(t.e>d||t.e<-d))throw Error(l+O(t));return t}function E(t,e){var r,n,o,i,a,c,s,l,f,p,h=t.constructor,d=h.precision;if(!t.s||!e.s)return e.s?e.s=-e.s:e=new h(t),u?A(e,d):e;if(s=t.d,p=e.d,n=e.e,l=t.e,s=s.slice(),a=l-n){for((f=a<0)?(r=s,a=-a,c=p.length):(r=p,n=l,c=s.length),a>(o=Math.max(Math.ceil(d/7),c)+2)&&(a=o,r.length=1),r.reverse(),o=a;o--;)r.push(0);r.reverse()}else{for((f=(o=s.length)<(c=p.length))&&(c=o),o=0;o<c;o++)if(s[o]!=p[o]){f=s[o]<p[o];break}a=0}for(f&&(r=s,s=p,p=r,e.s=-e.s),c=s.length,o=p.length-c;o>0;--o)s[c++]=0;for(o=p.length;o>a;){if(s[--o]<p[o]){for(i=o;i&&0===s[--i];)s[i]=1e7-1;--s[i],s[o]+=1e7}s[o]-=p[o]}for(;0===s[--c];)s.pop();for(;0===s[0];s.shift())--n;return s[0]?(e.d=s,e.e=n,u?A(e,d):e):new h(0)}function k(t,e,r){var n,o=O(t),i=b(t.d),a=i.length;return e?(r&&(n=r-a)>0?i=i.charAt(0)+"."+i.slice(1)+j(n):a>1&&(i=i.charAt(0)+"."+i.slice(1)),i=i+(o<0?"e":"e+")+o):o<0?(i="0."+j(-o-1)+i,r&&(n=r-a)>0&&(i+=j(n))):o>=a?(i+=j(o+1-a),r&&(n=r-o-1)>0&&(i=i+"."+j(n))):((n=o+1)<a&&(i=i.slice(0,n)+"."+i.slice(n)),r&&(n=r-a)>0&&(o+1===a&&(i+="."),i+=j(n))),t.s<0?"-"+i:i}function M(t,e){if(t.length>e)return t.length=e,!0}function T(t){if(!t||"object"!=typeof t)throw Error(c+"Object expected");var e,r,n,o=["precision",1,1e9,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(e=0;e<o.length;e+=3)if(void 0!==(n=t[r=o[e]])){if(f(n)===n&&n>=o[e+1]&&n<=o[e+2])this[r]=n;else throw Error(s+r+": "+n)}if(void 0!==(n=t[r="LN10"])){if(n==Math.LN10)this[r]=new this(n);else throw Error(s+r+": "+n)}return this}(a=function t(e){var r,n,o;function i(t){if(!(this instanceof i))return new i(t);if(this.constructor=i,t instanceof i){this.s=t.s,this.e=t.e,this.d=(t=t.d)?t.slice():t;return}if("number"==typeof t){if(0*t!=0)throw Error(s+t);if(t>0)this.s=1;else if(t<0)t=-t,this.s=-1;else{this.s=0,this.e=0,this.d=[0];return}if(t===~~t&&t<1e7){this.e=0,this.d=[t];return}return P(this,t.toString())}if("string"!=typeof t)throw Error(s+t);if(45===t.charCodeAt(0)?(t=t.slice(1),this.s=-1):this.s=1,h.test(t))P(this,t);else throw Error(s+t)}if(i.prototype=y,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=t,i.config=i.set=T,void 0===e&&(e={}),e)for(r=0,o=["precision","rounding","toExpNeg","toExpPos","LN10"];r<o.length;)e.hasOwnProperty(n=o[r++])||(e[n]=this[n]);return i.config(e),i}(a)).default=a.Decimal=a,i=new a(1),void 0!==(n=(function(){return a}).call(e,r,e,t))&&(t.exports=n)}(0)},77625:function(t){"use strict";var e=Object.prototype.hasOwnProperty,r="~";function n(){}function o(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function i(t,e,n,i,a){if("function"!=typeof n)throw TypeError("The listener must be a function");var u=new o(n,i||t,a),c=r?r+e:e;return t._events[c]?t._events[c].fn?t._events[c]=[t._events[c],u]:t._events[c].push(u):(t._events[c]=u,t._eventsCount++),t}function a(t,e){0==--t._eventsCount?t._events=new n:delete t._events[e]}function u(){this._events=new n,this._eventsCount=0}Object.create&&(n.prototype=Object.create(null),new n().__proto__||(r=!1)),u.prototype.eventNames=function(){var t,n,o=[];if(0===this._eventsCount)return o;for(n in t=this._events)e.call(t,n)&&o.push(r?n.slice(1):n);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(t)):o},u.prototype.listeners=function(t){var e=r?r+t:t,n=this._events[e];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,i=n.length,a=Array(i);o<i;o++)a[o]=n[o].fn;return a},u.prototype.listenerCount=function(t){var e=r?r+t:t,n=this._events[e];return n?n.fn?1:n.length:0},u.prototype.emit=function(t,e,n,o,i,a){var u=r?r+t:t;if(!this._events[u])return!1;var c,s,l=this._events[u],f=arguments.length;if(l.fn){switch(l.once&&this.removeListener(t,l.fn,void 0,!0),f){case 1:return l.fn.call(l.context),!0;case 2:return l.fn.call(l.context,e),!0;case 3:return l.fn.call(l.context,e,n),!0;case 4:return l.fn.call(l.context,e,n,o),!0;case 5:return l.fn.call(l.context,e,n,o,i),!0;case 6:return l.fn.call(l.context,e,n,o,i,a),!0}for(s=1,c=Array(f-1);s<f;s++)c[s-1]=arguments[s];l.fn.apply(l.context,c)}else{var p,h=l.length;for(s=0;s<h;s++)switch(l[s].once&&this.removeListener(t,l[s].fn,void 0,!0),f){case 1:l[s].fn.call(l[s].context);break;case 2:l[s].fn.call(l[s].context,e);break;case 3:l[s].fn.call(l[s].context,e,n);break;case 4:l[s].fn.call(l[s].context,e,n,o);break;default:if(!c)for(p=1,c=Array(f-1);p<f;p++)c[p-1]=arguments[p];l[s].fn.apply(l[s].context,c)}}return!0},u.prototype.on=function(t,e,r){return i(this,t,e,r,!1)},u.prototype.once=function(t,e,r){return i(this,t,e,r,!0)},u.prototype.removeListener=function(t,e,n,o){var i=r?r+t:t;if(!this._events[i])return this;if(!e)return a(this,i),this;var u=this._events[i];if(u.fn)u.fn!==e||o&&!u.once||n&&u.context!==n||a(this,i);else{for(var c=0,s=[],l=u.length;c<l;c++)(u[c].fn!==e||o&&!u[c].once||n&&u[c].context!==n)&&s.push(u[c]);s.length?this._events[i]=1===s.length?s[0]:s:a(this,i)}return this},u.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&a(this,e)):(this._events=new n,this._eventsCount=0),this},u.prototype.off=u.prototype.removeListener,u.prototype.addListener=u.prototype.on,u.prefixed=r,u.EventEmitter=u,t.exports=u},94975:function(t,e,r){var n=r(39866)(r(74288),"DataView");t.exports=n},9855:function(t,e,r){var n=r(43596),o=r(35907),i=r(35355),a=r(39870),u=r(73372);function c(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}c.prototype.clear=n,c.prototype.delete=o,c.prototype.get=i,c.prototype.has=a,c.prototype.set=u,t.exports=c},99078:function(t,e,r){var n=r(62285),o=r(28706),i=r(63717),a=r(78410),u=r(13368);function c(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}c.prototype.clear=n,c.prototype.delete=o,c.prototype.get=i,c.prototype.has=a,c.prototype.set=u,t.exports=c},88675:function(t,e,r){var n=r(39866)(r(74288),"Map");t.exports=n},76219:function(t,e,r){var n=r(38764),o=r(78615),i=r(83391),a=r(53483),u=r(74724);function c(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}c.prototype.clear=n,c.prototype.delete=o,c.prototype.get=i,c.prototype.has=a,c.prototype.set=u,t.exports=c},69308:function(t,e,r){var n=r(39866)(r(74288),"Promise");t.exports=n},41497:function(t,e,r){var n=r(39866)(r(74288),"Set");t.exports=n},11549:function(t,e,r){var n=r(76219),o=r(54351),i=r(16096);function a(t){var e=-1,r=null==t?0:t.length;for(this.__data__=new n;++e<r;)this.add(t[e])}a.prototype.add=a.prototype.push=o,a.prototype.has=i,t.exports=a},85885:function(t,e,r){var n=r(99078),o=r(84092),i=r(31663),a=r(69135),u=r(39552),c=r(63960);function s(t){var e=this.__data__=new n(t);this.size=e.size}s.prototype.clear=o,s.prototype.delete=i,s.prototype.get=a,s.prototype.has=u,s.prototype.set=c,t.exports=s},23910:function(t,e,r){var n=r(74288).Symbol;t.exports=n},80098:function(t,e,r){var n=r(74288).Uint8Array;t.exports=n},10880:function(t,e,r){var n=r(39866)(r(74288),"WeakMap");t.exports=n},60493:function(t){t.exports=function(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}},78897:function(t){t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(!e(t[r],r,t))return!1;return!0}},42774:function(t){t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=0,i=[];++r<n;){var a=t[r];e(a,r,t)&&(i[o++]=a)}return i}},26685:function(t,e,r){var n=r(47909);t.exports=function(t,e){return!!(null==t?0:t.length)&&n(t,e,0)>-1}},56883:function(t){t.exports=function(t,e,r){for(var n=-1,o=null==t?0:t.length;++n<o;)if(r(e,t[n]))return!0;return!1}},28579:function(t,e,r){var n=r(89772),o=r(56569),i=r(25614),a=r(98051),u=r(84257),c=r(9792),s=Object.prototype.hasOwnProperty;t.exports=function(t,e){var r=i(t),l=!r&&o(t),f=!r&&!l&&a(t),p=!r&&!l&&!f&&c(t),h=r||l||f||p,d=h?n(t.length,String):[],y=d.length;for(var v in t)(e||s.call(t,v))&&!(h&&("length"==v||f&&("offset"==v||"parent"==v)||p&&("buffer"==v||"byteLength"==v||"byteOffset"==v)||u(v,y)))&&d.push(v);return d}},73819:function(t){t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length,o=Array(n);++r<n;)o[r]=e(t[r],r,t);return o}},73817:function(t){t.exports=function(t,e){for(var r=-1,n=e.length,o=t.length;++r<n;)t[o+r]=e[r];return t}},25253:function(t){t.exports=function(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1}},53417:function(t){t.exports=function(t){return t.split("")}},24457:function(t,e,r){var n=r(37560);t.exports=function(t,e){for(var r=t.length;r--;)if(n(t[r][0],e))return r;return -1}},83023:function(t,e,r){var n=r(4521);t.exports=function(t,e,r){"__proto__"==e&&n?n(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}},67676:function(t,e,r){var n=r(98060),o=r(97930)(n);t.exports=o},28935:function(t,e,r){var n=r(67676);t.exports=function(t,e){var r=!0;return n(t,function(t,n,o){return r=!!e(t,n,o)}),r}},67646:function(t,e,r){var n=r(78371);t.exports=function(t,e,r){for(var o=-1,i=t.length;++o<i;){var a=t[o],u=e(a);if(null!=u&&(void 0===c?u==u&&!n(u):r(u,c)))var c=u,s=a}return s}},8235:function(t){t.exports=function(t,e,r,n){for(var o=t.length,i=r+(n?1:-1);n?i--:++i<o;)if(e(t[i],i,t))return i;return -1}},72569:function(t,e,r){var n=r(73817),o=r(37134);t.exports=function t(e,r,i,a,u){var c=-1,s=e.length;for(i||(i=o),u||(u=[]);++c<s;){var l=e[c];r>0&&i(l)?r>1?t(l,r-1,i,a,u):n(u,l):a||(u[u.length]=l)}return u}},63321:function(t,e,r){var n=r(33023)();t.exports=n},98060:function(t,e,r){var n=r(63321),o=r(43228);t.exports=function(t,e){return t&&n(t,e,o)}},92167:function(t,e,r){var n=r(67906),o=r(70235);t.exports=function(t,e){e=n(e,t);for(var r=0,i=e.length;null!=t&&r<i;)t=t[o(e[r++])];return r&&r==i?t:void 0}},36452:function(t,e,r){var n=r(73817),o=r(25614);t.exports=function(t,e,r){var i=e(t);return o(t)?i:n(i,r(t))}},54506:function(t,e,r){var n=r(23910),o=r(4479),i=r(80910),a=n?n.toStringTag:void 0;t.exports=function(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":a&&a in Object(t)?o(t):i(t)}},58905:function(t){t.exports=function(t,e){return t>e}},93012:function(t){t.exports=function(t,e){return null!=t&&e in Object(t)}},47909:function(t,e,r){var n=r(8235),o=r(31953),i=r(35281);t.exports=function(t,e,r){return e==e?i(t,e,r):n(t,o,r)}},90370:function(t,e,r){var n=r(54506),o=r(10303);t.exports=function(t){return o(t)&&"[object Arguments]"==n(t)}},56318:function(t,e,r){var n=r(6791),o=r(10303);t.exports=function t(e,r,i,a,u){return e===r||(null!=e&&null!=r&&(o(e)||o(r))?n(e,r,i,a,t,u):e!=e&&r!=r)}},6791:function(t,e,r){var n=r(85885),o=r(97638),i=r(88030),a=r(64974),u=r(81690),c=r(25614),s=r(98051),l=r(9792),f="[object Arguments]",p="[object Array]",h="[object Object]",d=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,y,v,m){var b=c(t),g=c(e),x=b?p:u(t),O=g?p:u(e);x=x==f?h:x,O=O==f?h:O;var w=x==h,j=O==h,S=x==O;if(S&&s(t)){if(!s(e))return!1;b=!0,w=!1}if(S&&!w)return m||(m=new n),b||l(t)?o(t,e,r,y,v,m):i(t,e,x,r,y,v,m);if(!(1&r)){var P=w&&d.call(t,"__wrapped__"),A=j&&d.call(e,"__wrapped__");if(P||A){var E=P?t.value():t,k=A?e.value():e;return m||(m=new n),v(E,k,r,y,m)}}return!!S&&(m||(m=new n),a(t,e,r,y,v,m))}},62538:function(t,e,r){var n=r(85885),o=r(56318);t.exports=function(t,e,r,i){var a=r.length,u=a,c=!i;if(null==t)return!u;for(t=Object(t);a--;){var s=r[a];if(c&&s[2]?s[1]!==t[s[0]]:!(s[0]in t))return!1}for(;++a<u;){var l=(s=r[a])[0],f=t[l],p=s[1];if(c&&s[2]){if(void 0===f&&!(l in t))return!1}else{var h=new n;if(i)var d=i(f,p,l,t,e,h);if(!(void 0===d?o(p,f,3,i,h):d))return!1}}return!0}},31953:function(t){t.exports=function(t){return t!=t}},57595:function(t,e,r){var n=r(86757),o=r(79551),i=r(28302),a=r(1292),u=/^\[object .+?Constructor\]$/,c=Object.prototype,s=Function.prototype.toString,l=c.hasOwnProperty,f=RegExp("^"+s.call(l).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$");t.exports=function(t){return!(!i(t)||o(t))&&(n(t)?f:u).test(a(t))}},59332:function(t,e,r){var n=r(54506),o=r(13973),i=r(10303),a={};a["[object Float32Array]"]=a["[object Float64Array]"]=a["[object Int8Array]"]=a["[object Int16Array]"]=a["[object Int32Array]"]=a["[object Uint8Array]"]=a["[object Uint8ClampedArray]"]=a["[object Uint16Array]"]=a["[object Uint32Array]"]=!0,a["[object Arguments]"]=a["[object Array]"]=a["[object ArrayBuffer]"]=a["[object Boolean]"]=a["[object DataView]"]=a["[object Date]"]=a["[object Error]"]=a["[object Function]"]=a["[object Map]"]=a["[object Number]"]=a["[object Object]"]=a["[object RegExp]"]=a["[object Set]"]=a["[object String]"]=a["[object WeakMap]"]=!1,t.exports=function(t){return i(t)&&o(t.length)&&!!a[n(t)]}},88157:function(t,e,r){var n=r(25569),o=r(51501),i=r(79586),a=r(25614),u=r(22350);t.exports=function(t){return"function"==typeof t?t:null==t?i:"object"==typeof t?a(t)?o(t[0],t[1]):n(t):u(t)}},4578:function(t,e,r){var n=r(35365),o=r(77184),i=Object.prototype.hasOwnProperty;t.exports=function(t){if(!n(t))return o(t);var e=[];for(var r in Object(t))i.call(t,r)&&"constructor"!=r&&e.push(r);return e}},20121:function(t){t.exports=function(t,e){return t<e}},24240:function(t,e,r){var n=r(67676),o=r(5629);t.exports=function(t,e){var r=-1,i=o(t)?Array(t.length):[];return n(t,function(t,n,o){i[++r]=e(t,n,o)}),i}},25569:function(t,e,r){var n=r(62538),o=r(58424),i=r(47073);t.exports=function(t){var e=o(t);return 1==e.length&&e[0][2]?i(e[0][0],e[0][1]):function(r){return r===t||n(r,t,e)}}},51501:function(t,e,r){var n=r(56318),o=r(13735),i=r(17764),a=r(67352),u=r(45669),c=r(47073),s=r(70235);t.exports=function(t,e){return a(t)&&u(e)?c(s(t),e):function(r){var a=o(r,t);return void 0===a&&a===e?i(r,t):n(e,a,3)}}},84046:function(t,e,r){var n=r(73819),o=r(92167),i=r(88157),a=r(24240),u=r(89200),c=r(23305),s=r(80701),l=r(79586),f=r(25614);t.exports=function(t,e,r){e=e.length?n(e,function(t){return f(t)?function(e){return o(e,1===t.length?t[0]:t)}:t}):[l];var p=-1;return e=n(e,c(i)),u(a(t,function(t,r,o){return{criteria:n(e,function(e){return e(t)}),index:++p,value:t}}),function(t,e){return s(t,e,r)})}},18155:function(t){t.exports=function(t){return function(e){return null==e?void 0:e[t]}}},73584:function(t,e,r){var n=r(92167);t.exports=function(t){return function(e){return n(e,t)}}},19608:function(t){var e=Math.ceil,r=Math.max;t.exports=function(t,n,o,i){for(var a=-1,u=r(e((n-t)/(o||1)),0),c=Array(u);u--;)c[i?u:++a]=t,t+=o;return c}},44843:function(t,e,r){var n=r(79586),o=r(49478),i=r(98796);t.exports=function(t,e){return i(o(t,e,n),t+"")}},9810:function(t,e,r){var n=r(92353),o=r(4521),i=r(79586),a=o?function(t,e){return o(t,"toString",{configurable:!0,enumerable:!1,value:n(e),writable:!0})}:i;t.exports=a},99558:function(t){t.exports=function(t,e,r){var n=-1,o=t.length;e<0&&(e=-e>o?0:o+e),(r=r>o?o:r)<0&&(r+=o),o=e>r?0:r-e>>>0,e>>>=0;for(var i=Array(o);++n<o;)i[n]=t[n+e];return i}},12327:function(t,e,r){var n=r(67676);t.exports=function(t,e){var r;return n(t,function(t,n,o){return!(r=e(t,n,o))}),!!r}},89200:function(t){t.exports=function(t,e){var r=t.length;for(t.sort(e);r--;)t[r]=t[r].value;return t}},89772:function(t){t.exports=function(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}},65020:function(t,e,r){var n=r(23910),o=r(73819),i=r(25614),a=r(78371),u=1/0,c=n?n.prototype:void 0,s=c?c.toString:void 0;t.exports=function t(e){if("string"==typeof e)return e;if(i(e))return o(e,t)+"";if(a(e))return s?s.call(e):"";var r=e+"";return"0"==r&&1/e==-u?"-0":r}},55041:function(t,e,r){var n=r(5035),o=/^\s+/;t.exports=function(t){return t?t.slice(0,n(t)+1).replace(o,""):t}},23305:function(t){t.exports=function(t){return function(e){return t(e)}}},13826:function(t,e,r){var n=r(11549),o=r(26685),i=r(56883),a=r(65734),u=r(57600),c=r(27794);t.exports=function(t,e,r){var s=-1,l=o,f=t.length,p=!0,h=[],d=h;if(r)p=!1,l=i;else if(f>=200){var y=e?null:u(t);if(y)return c(y);p=!1,l=a,d=new n}else d=e?[]:h;t:for(;++s<f;){var v=t[s],m=e?e(v):v;if(v=r||0!==v?v:0,p&&m==m){for(var b=d.length;b--;)if(d[b]===m)continue t;e&&d.push(m),h.push(v)}else l(d,m,r)||(d!==h&&d.push(m),h.push(v))}return h}},65734:function(t){t.exports=function(t,e){return t.has(e)}},67906:function(t,e,r){var n=r(25614),o=r(67352),i=r(39365),a=r(3641);t.exports=function(t,e){return n(t)?t:o(t,e)?[t]:i(a(t))}},91684:function(t,e,r){var n=r(99558);t.exports=function(t,e,r){var o=t.length;return r=void 0===r?o:r,!e&&r>=o?t:n(t,e,r)}},1536:function(t,e,r){var n=r(78371);t.exports=function(t,e){if(t!==e){var r=void 0!==t,o=null===t,i=t==t,a=n(t),u=void 0!==e,c=null===e,s=e==e,l=n(e);if(!c&&!l&&!a&&t>e||a&&u&&s&&!c&&!l||o&&u&&s||!r&&s||!i)return 1;if(!o&&!a&&!l&&t<e||l&&r&&i&&!o&&!a||c&&r&&i||!u&&i||!s)return -1}return 0}},80701:function(t,e,r){var n=r(1536);t.exports=function(t,e,r){for(var o=-1,i=t.criteria,a=e.criteria,u=i.length,c=r.length;++o<u;){var s=n(i[o],a[o]);if(s){if(o>=c)return s;return s*("desc"==r[o]?-1:1)}}return t.index-e.index}},92077:function(t,e,r){var n=r(74288)["__core-js_shared__"];t.exports=n},97930:function(t,e,r){var n=r(5629);t.exports=function(t,e){return function(r,o){if(null==r)return r;if(!n(r))return t(r,o);for(var i=r.length,a=e?i:-1,u=Object(r);(e?a--:++a<i)&&!1!==o(u[a],a,u););return r}}},33023:function(t){t.exports=function(t){return function(e,r,n){for(var o=-1,i=Object(e),a=n(e),u=a.length;u--;){var c=a[t?u:++o];if(!1===r(i[c],c,i))break}return e}}},80675:function(t,e,r){var n=r(91684),o=r(14503),i=r(88551),a=r(3641);t.exports=function(t){return function(e){var r=o(e=a(e))?i(e):void 0,u=r?r[0]:e.charAt(0),c=r?n(r,1).join(""):e.slice(1);return u[t]()+c}}},82602:function(t,e,r){var n=r(88157),o=r(5629),i=r(43228);t.exports=function(t){return function(e,r,a){var u=Object(e);if(!o(e)){var c=n(r,3);e=i(e),r=function(t){return c(u[t],t,u)}}var s=t(e,r,a);return s>-1?u[c?e[s]:s]:void 0}}},35464:function(t,e,r){var n=r(19608),o=r(49639),i=r(175);t.exports=function(t){return function(e,r,a){return a&&"number"!=typeof a&&o(e,r,a)&&(r=a=void 0),e=i(e),void 0===r?(r=e,e=0):r=i(r),a=void 0===a?e<r?1:-1:i(a),n(e,r,a,t)}}},57600:function(t,e,r){var n=r(41497),o=r(93810),i=r(27794),a=n&&1/i(new n([,-0]))[1]==1/0?function(t){return new n(t)}:o;t.exports=a},4521:function(t,e,r){var n=r(39866),o=function(){try{var t=n(Object,"defineProperty");return t({},"",{}),t}catch(t){}}();t.exports=o},97638:function(t,e,r){var n=r(11549),o=r(25253),i=r(65734);t.exports=function(t,e,r,a,u,c){var s=1&r,l=t.length,f=e.length;if(l!=f&&!(s&&f>l))return!1;var p=c.get(t),h=c.get(e);if(p&&h)return p==e&&h==t;var d=-1,y=!0,v=2&r?new n:void 0;for(c.set(t,e),c.set(e,t);++d<l;){var m=t[d],b=e[d];if(a)var g=s?a(b,m,d,e,t,c):a(m,b,d,t,e,c);if(void 0!==g){if(g)continue;y=!1;break}if(v){if(!o(e,function(t,e){if(!i(v,e)&&(m===t||u(m,t,r,a,c)))return v.push(e)})){y=!1;break}}else if(!(m===b||u(m,b,r,a,c))){y=!1;break}}return c.delete(t),c.delete(e),y}},88030:function(t,e,r){var n=r(23910),o=r(80098),i=r(37560),a=r(97638),u=r(22523),c=r(27794),s=n?n.prototype:void 0,l=s?s.valueOf:void 0;t.exports=function(t,e,r,n,s,f,p){switch(r){case"[object DataView]":if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)break;t=t.buffer,e=e.buffer;case"[object ArrayBuffer]":if(t.byteLength!=e.byteLength||!f(new o(t),new o(e)))break;return!0;case"[object Boolean]":case"[object Date]":case"[object Number]":return i(+t,+e);case"[object Error]":return t.name==e.name&&t.message==e.message;case"[object RegExp]":case"[object String]":return t==e+"";case"[object Map]":var h=u;case"[object Set]":var d=1&n;if(h||(h=c),t.size!=e.size&&!d)break;var y=p.get(t);if(y)return y==e;n|=2,p.set(t,e);var v=a(h(t),h(e),n,s,f,p);return p.delete(t),v;case"[object Symbol]":if(l)return l.call(t)==l.call(e)}return!1}},64974:function(t,e,r){var n=r(28529),o=Object.prototype.hasOwnProperty;t.exports=function(t,e,r,i,a,u){var c=1&r,s=n(t),l=s.length;if(l!=n(e).length&&!c)return!1;for(var f=l;f--;){var p=s[f];if(!(c?p in e:o.call(e,p)))return!1}var h=u.get(t),d=u.get(e);if(h&&d)return h==e&&d==t;var y=!0;u.set(t,e),u.set(e,t);for(var v=c;++f<l;){var m=t[p=s[f]],b=e[p];if(i)var g=c?i(b,m,p,e,t,u):i(m,b,p,t,e,u);if(!(void 0===g?m===b||a(m,b,r,i,u):g)){y=!1;break}v||(v="constructor"==p)}if(y&&!v){var x=t.constructor,O=e.constructor;x!=O&&"constructor"in t&&"constructor"in e&&!("function"==typeof x&&x instanceof x&&"function"==typeof O&&O instanceof O)&&(y=!1)}return u.delete(t),u.delete(e),y}},17071:function(t,e,r){var n="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g;t.exports=n},28529:function(t,e,r){var n=r(36452),o=r(80466),i=r(43228);t.exports=function(t){return n(t,i,o)}},1507:function(t,e,r){var n=r(7545);t.exports=function(t,e){var r=t.__data__;return n(e)?r["string"==typeof e?"string":"hash"]:r.map}},58424:function(t,e,r){var n=r(45669),o=r(43228);t.exports=function(t){for(var e=o(t),r=e.length;r--;){var i=e[r],a=t[i];e[r]=[i,a,n(a)]}return e}},39866:function(t,e,r){var n=r(57595),o=r(3138);t.exports=function(t,e){var r=o(t,e);return n(r)?r:void 0}},62602:function(t,e,r){var n=r(45070)(Object.getPrototypeOf,Object);t.exports=n},4479:function(t,e,r){var n=r(23910),o=Object.prototype,i=o.hasOwnProperty,a=o.toString,u=n?n.toStringTag:void 0;t.exports=function(t){var e=i.call(t,u),r=t[u];try{t[u]=void 0;var n=!0}catch(t){}var o=a.call(t);return n&&(e?t[u]=r:delete t[u]),o}},80466:function(t,e,r){var n=r(42774),o=r(55716),i=Object.prototype.propertyIsEnumerable,a=Object.getOwnPropertySymbols,u=a?function(t){return null==t?[]:n(a(t=Object(t)),function(e){return i.call(t,e)})}:o;t.exports=u},81690:function(t,e,r){var n=r(94975),o=r(88675),i=r(69308),a=r(41497),u=r(10880),c=r(54506),s=r(1292),l="[object Map]",f="[object Promise]",p="[object Set]",h="[object WeakMap]",d="[object DataView]",y=s(n),v=s(o),m=s(i),b=s(a),g=s(u),x=c;(n&&x(new n(new ArrayBuffer(1)))!=d||o&&x(new o)!=l||i&&x(i.resolve())!=f||a&&x(new a)!=p||u&&x(new u)!=h)&&(x=function(t){var e=c(t),r="[object Object]"==e?t.constructor:void 0,n=r?s(r):"";if(n)switch(n){case y:return d;case v:return l;case m:return f;case b:return p;case g:return h}return e}),t.exports=x},3138:function(t){t.exports=function(t,e){return null==t?void 0:t[e]}},59592:function(t,e,r){var n=r(67906),o=r(56569),i=r(25614),a=r(84257),u=r(13973),c=r(70235);t.exports=function(t,e,r){e=n(e,t);for(var s=-1,l=e.length,f=!1;++s<l;){var p=c(e[s]);if(!(f=null!=t&&r(t,p)))break;t=t[p]}return f||++s!=l?f:!!(l=null==t?0:t.length)&&u(l)&&a(p,l)&&(i(t)||o(t))}},14503:function(t){var e=RegExp("[\\u200d\ud800-\udfff\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff\\ufe0e\\ufe0f]");t.exports=function(t){return e.test(t)}},43596:function(t,e,r){var n=r(20453);t.exports=function(){this.__data__=n?n(null):{},this.size=0}},35907:function(t){t.exports=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e}},35355:function(t,e,r){var n=r(20453),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;if(n){var r=e[t];return"__lodash_hash_undefined__"===r?void 0:r}return o.call(e,t)?e[t]:void 0}},39870:function(t,e,r){var n=r(20453),o=Object.prototype.hasOwnProperty;t.exports=function(t){var e=this.__data__;return n?void 0!==e[t]:o.call(e,t)}},73372:function(t,e,r){var n=r(20453);t.exports=function(t,e){var r=this.__data__;return this.size+=this.has(t)?0:1,r[t]=n&&void 0===e?"__lodash_hash_undefined__":e,this}},37134:function(t,e,r){var n=r(23910),o=r(56569),i=r(25614),a=n?n.isConcatSpreadable:void 0;t.exports=function(t){return i(t)||o(t)||!!(a&&t&&t[a])}},84257:function(t){var e=/^(?:0|[1-9]\d*)$/;t.exports=function(t,r){var n=typeof t;return!!(r=null==r?9007199254740991:r)&&("number"==n||"symbol"!=n&&e.test(t))&&t>-1&&t%1==0&&t<r}},49639:function(t,e,r){var n=r(37560),o=r(5629),i=r(84257),a=r(28302);t.exports=function(t,e,r){if(!a(r))return!1;var u=typeof e;return("number"==u?!!(o(r)&&i(e,r.length)):"string"==u&&e in r)&&n(r[e],t)}},67352:function(t,e,r){var n=r(25614),o=r(78371),i=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,a=/^\w*$/;t.exports=function(t,e){if(n(t))return!1;var r=typeof t;return!!("number"==r||"symbol"==r||"boolean"==r||null==t||o(t))||a.test(t)||!i.test(t)||null!=e&&t in Object(e)}},7545:function(t){t.exports=function(t){var e=typeof t;return"string"==e||"number"==e||"symbol"==e||"boolean"==e?"__proto__"!==t:null===t}},79551:function(t,e,r){var n,o=r(92077),i=(n=/[^.]+$/.exec(o&&o.keys&&o.keys.IE_PROTO||""))?"Symbol(src)_1."+n:"";t.exports=function(t){return!!i&&i in t}},35365:function(t){var e=Object.prototype;t.exports=function(t){var r=t&&t.constructor;return t===("function"==typeof r&&r.prototype||e)}},45669:function(t,e,r){var n=r(28302);t.exports=function(t){return t==t&&!n(t)}},62285:function(t){t.exports=function(){this.__data__=[],this.size=0}},28706:function(t,e,r){var n=r(24457),o=Array.prototype.splice;t.exports=function(t){var e=this.__data__,r=n(e,t);return!(r<0)&&(r==e.length-1?e.pop():o.call(e,r,1),--this.size,!0)}},63717:function(t,e,r){var n=r(24457);t.exports=function(t){var e=this.__data__,r=n(e,t);return r<0?void 0:e[r][1]}},78410:function(t,e,r){var n=r(24457);t.exports=function(t){return n(this.__data__,t)>-1}},13368:function(t,e,r){var n=r(24457);t.exports=function(t,e){var r=this.__data__,o=n(r,t);return o<0?(++this.size,r.push([t,e])):r[o][1]=e,this}},38764:function(t,e,r){var n=r(9855),o=r(99078),i=r(88675);t.exports=function(){this.size=0,this.__data__={hash:new n,map:new(i||o),string:new n}}},78615:function(t,e,r){var n=r(1507);t.exports=function(t){var e=n(this,t).delete(t);return this.size-=e?1:0,e}},83391:function(t,e,r){var n=r(1507);t.exports=function(t){return n(this,t).get(t)}},53483:function(t,e,r){var n=r(1507);t.exports=function(t){return n(this,t).has(t)}},74724:function(t,e,r){var n=r(1507);t.exports=function(t,e){var r=n(this,t),o=r.size;return r.set(t,e),this.size+=r.size==o?0:1,this}},22523:function(t){t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t,n){r[++e]=[n,t]}),r}},47073:function(t){t.exports=function(t,e){return function(r){return null!=r&&r[t]===e&&(void 0!==e||t in Object(r))}}},23787:function(t,e,r){var n=r(50967);t.exports=function(t){var e=n(t,function(t){return 500===r.size&&r.clear(),t}),r=e.cache;return e}},20453:function(t,e,r){var n=r(39866)(Object,"create");t.exports=n},77184:function(t,e,r){var n=r(45070)(Object.keys,Object);t.exports=n},39931:function(t,e,r){t=r.nmd(t);var n=r(17071),o=e&&!e.nodeType&&e,i=o&&t&&!t.nodeType&&t,a=i&&i.exports===o&&n.process,u=function(){try{var t=i&&i.require&&i.require("util").types;if(t)return t;return a&&a.binding&&a.binding("util")}catch(t){}}();t.exports=u},80910:function(t){var e=Object.prototype.toString;t.exports=function(t){return e.call(t)}},45070:function(t){t.exports=function(t,e){return function(r){return t(e(r))}}},49478:function(t,e,r){var n=r(60493),o=Math.max;t.exports=function(t,e,r){return e=o(void 0===e?t.length-1:e,0),function(){for(var i=arguments,a=-1,u=o(i.length-e,0),c=Array(u);++a<u;)c[a]=i[e+a];a=-1;for(var s=Array(e+1);++a<e;)s[a]=i[a];return s[e]=r(c),n(t,this,s)}}},74288:function(t,e,r){var n=r(17071),o="object"==typeof self&&self&&self.Object===Object&&self,i=n||o||Function("return this")();t.exports=i},54351:function(t){t.exports=function(t){return this.__data__.set(t,"__lodash_hash_undefined__"),this}},16096:function(t){t.exports=function(t){return this.__data__.has(t)}},27794:function(t){t.exports=function(t){var e=-1,r=Array(t.size);return t.forEach(function(t){r[++e]=t}),r}},98796:function(t,e,r){var n=r(9810),o=r(31610)(n);t.exports=o},31610:function(t){var e=Date.now;t.exports=function(t){var r=0,n=0;return function(){var o=e(),i=16-(o-n);if(n=o,i>0){if(++r>=800)return arguments[0]}else r=0;return t.apply(void 0,arguments)}}},84092:function(t,e,r){var n=r(99078);t.exports=function(){this.__data__=new n,this.size=0}},31663:function(t){t.exports=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r}},69135:function(t){t.exports=function(t){return this.__data__.get(t)}},39552:function(t){t.exports=function(t){return this.__data__.has(t)}},63960:function(t,e,r){var n=r(99078),o=r(88675),i=r(76219);t.exports=function(t,e){var r=this.__data__;if(r instanceof n){var a=r.__data__;if(!o||a.length<199)return a.push([t,e]),this.size=++r.size,this;r=this.__data__=new i(a)}return r.set(t,e),this.size=r.size,this}},35281:function(t){t.exports=function(t,e,r){for(var n=r-1,o=t.length;++n<o;)if(t[n]===e)return n;return -1}},88551:function(t,e,r){var n=r(53417),o=r(14503),i=r(26364);t.exports=function(t){return o(t)?i(t):n(t)}},39365:function(t,e,r){var n=r(23787),o=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,i=/\\(\\)?/g,a=n(function(t){var e=[];return 46===t.charCodeAt(0)&&e.push(""),t.replace(o,function(t,r,n,o){e.push(n?o.replace(i,"$1"):r||t)}),e});t.exports=a},70235:function(t,e,r){var n=r(78371),o=1/0;t.exports=function(t){if("string"==typeof t||n(t))return t;var e=t+"";return"0"==e&&1/t==-o?"-0":e}},1292:function(t){var e=Function.prototype.toString;t.exports=function(t){if(null!=t){try{return e.call(t)}catch(t){}try{return t+""}catch(t){}}return""}},5035:function(t){var e=/\s/;t.exports=function(t){for(var r=t.length;r--&&e.test(t.charAt(r)););return r}},26364:function(t){var e="\ud800-\udfff",r="[\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff]",n="\ud83c[\udffb-\udfff]",o="[^"+e+"]",i="(?:\ud83c[\udde6-\uddff]){2}",a="[\ud800-\udbff][\udc00-\udfff]",u="(?:"+r+"|"+n+")?",c="[\\ufe0e\\ufe0f]?",s="(?:\\u200d(?:"+[o,i,a].join("|")+")"+c+u+")*",l=RegExp(n+"(?="+n+")|(?:"+[o+r+"?",r,i,a,"["+e+"]"].join("|")+")"+(c+u+s),"g");t.exports=function(t){return t.match(l)||[]}},92353:function(t){t.exports=function(t){return function(){return t}}},7310:function(t,e,r){var n=r(28302),o=r(11121),i=r(6660),a=Math.max,u=Math.min;t.exports=function(t,e,r){var c,s,l,f,p,h,d=0,y=!1,v=!1,m=!0;if("function"!=typeof t)throw TypeError("Expected a function");function b(e){var r=c,n=s;return c=s=void 0,d=e,f=t.apply(n,r)}function g(t){var r=t-h,n=t-d;return void 0===h||r>=e||r<0||v&&n>=l}function x(){var t,r,n,i=o();if(g(i))return O(i);p=setTimeout(x,(t=i-h,r=i-d,n=e-t,v?u(n,l-r):n))}function O(t){return(p=void 0,m&&c)?b(t):(c=s=void 0,f)}function w(){var t,r=o(),n=g(r);if(c=arguments,s=this,h=r,n){if(void 0===p)return d=t=h,p=setTimeout(x,e),y?b(t):f;if(v)return clearTimeout(p),p=setTimeout(x,e),b(h)}return void 0===p&&(p=setTimeout(x,e)),f}return e=i(e)||0,n(r)&&(y=!!r.leading,l=(v="maxWait"in r)?a(i(r.maxWait)||0,e):l,m="trailing"in r?!!r.trailing:m),w.cancel=function(){void 0!==p&&clearTimeout(p),d=0,c=h=s=p=void 0},w.flush=function(){return void 0===p?f:O(o())},w}},37560:function(t){t.exports=function(t,e){return t===e||t!=t&&e!=e}},32242:function(t,e,r){var n=r(78897),o=r(28935),i=r(88157),a=r(25614),u=r(49639);t.exports=function(t,e,r){var c=a(t)?n:o;return r&&u(t,e,r)&&(e=void 0),c(t,i(e,3))}},84173:function(t,e,r){var n=r(82602)(r(12152));t.exports=n},12152:function(t,e,r){var n=r(8235),o=r(88157),i=r(85759),a=Math.max;t.exports=function(t,e,r){var u=null==t?0:t.length;if(!u)return -1;var c=null==r?0:i(r);return c<0&&(c=a(u+c,0)),n(t,o(e,3),c)}},11314:function(t,e,r){var n=r(72569),o=r(89238);t.exports=function(t,e){return n(o(t,e),1)}},13735:function(t,e,r){var n=r(92167);t.exports=function(t,e,r){var o=null==t?void 0:n(t,e);return void 0===o?r:o}},17764:function(t,e,r){var n=r(93012),o=r(59592);t.exports=function(t,e){return null!=t&&o(t,e,n)}},79586:function(t){t.exports=function(t){return t}},56569:function(t,e,r){var n=r(90370),o=r(10303),i=Object.prototype,a=i.hasOwnProperty,u=i.propertyIsEnumerable,c=n(function(){return arguments}())?n:function(t){return o(t)&&a.call(t,"callee")&&!u.call(t,"callee")};t.exports=c},25614:function(t){var e=Array.isArray;t.exports=e},5629:function(t,e,r){var n=r(86757),o=r(13973);t.exports=function(t){return null!=t&&o(t.length)&&!n(t)}},24342:function(t,e,r){var n=r(54506),o=r(10303);t.exports=function(t){return!0===t||!1===t||o(t)&&"[object Boolean]"==n(t)}},98051:function(t,e,r){t=r.nmd(t);var n=r(74288),o=r(7406),i=e&&!e.nodeType&&e,a=i&&t&&!t.nodeType&&t,u=a&&a.exports===i?n.Buffer:void 0,c=u?u.isBuffer:void 0;t.exports=c||o},21652:function(t,e,r){var n=r(56318);t.exports=function(t,e){return n(t,e)}},86757:function(t,e,r){var n=r(54506),o=r(28302);t.exports=function(t){if(!o(t))return!1;var e=n(t);return"[object Function]"==e||"[object GeneratorFunction]"==e||"[object AsyncFunction]"==e||"[object Proxy]"==e}},13973:function(t){t.exports=function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=9007199254740991}},82559:function(t,e,r){var n=r(22345);t.exports=function(t){return n(t)&&t!=+t}},77571:function(t){t.exports=function(t){return null==t}},22345:function(t,e,r){var n=r(54506),o=r(10303);t.exports=function(t){return"number"==typeof t||o(t)&&"[object Number]"==n(t)}},28302:function(t){t.exports=function(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}},10303:function(t){t.exports=function(t){return null!=t&&"object"==typeof t}},90231:function(t,e,r){var n=r(54506),o=r(62602),i=r(10303),a=Object.prototype,u=Function.prototype.toString,c=a.hasOwnProperty,s=u.call(Object);t.exports=function(t){if(!i(t)||"[object Object]"!=n(t))return!1;var e=o(t);if(null===e)return!0;var r=c.call(e,"constructor")&&e.constructor;return"function"==typeof r&&r instanceof r&&u.call(r)==s}},42715:function(t,e,r){var n=r(54506),o=r(25614),i=r(10303);t.exports=function(t){return"string"==typeof t||!o(t)&&i(t)&&"[object String]"==n(t)}},78371:function(t,e,r){var n=r(54506),o=r(10303);t.exports=function(t){return"symbol"==typeof t||o(t)&&"[object Symbol]"==n(t)}},9792:function(t,e,r){var n=r(59332),o=r(23305),i=r(39931),a=i&&i.isTypedArray,u=a?o(a):n;t.exports=u},43228:function(t,e,r){var n=r(28579),o=r(4578),i=r(5629);t.exports=function(t){return i(t)?n(t):o(t)}},86185:function(t){t.exports=function(t){var e=null==t?0:t.length;return e?t[e-1]:void 0}},89238:function(t,e,r){var n=r(73819),o=r(88157),i=r(24240),a=r(25614);t.exports=function(t,e){return(a(t)?n:i)(t,o(e,3))}},41443:function(t,e,r){var n=r(83023),o=r(98060),i=r(88157);t.exports=function(t,e){var r={};return e=i(e,3),o(t,function(t,o,i){n(r,o,e(t,o,i))}),r}},95645:function(t,e,r){var n=r(67646),o=r(58905),i=r(79586);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},50967:function(t,e,r){var n=r(76219);function o(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw TypeError("Expected a function");var r=function(){var n=arguments,o=e?e.apply(this,n):n[0],i=r.cache;if(i.has(o))return i.get(o);var a=t.apply(this,n);return r.cache=i.set(o,a)||i,a};return r.cache=new(o.Cache||n),r}o.Cache=n,t.exports=o},99008:function(t,e,r){var n=r(67646),o=r(20121),i=r(79586);t.exports=function(t){return t&&t.length?n(t,i,o):void 0}},93810:function(t){t.exports=function(){}},11121:function(t,e,r){var n=r(74288);t.exports=function(){return n.Date.now()}},22350:function(t,e,r){var n=r(18155),o=r(73584),i=r(67352),a=r(70235);t.exports=function(t){return i(t)?n(a(t)):o(t)}},99676:function(t,e,r){var n=r(35464)();t.exports=n},33645:function(t,e,r){var n=r(25253),o=r(88157),i=r(12327),a=r(25614),u=r(49639);t.exports=function(t,e,r){var c=a(t)?n:i;return r&&u(t,e,r)&&(e=void 0),c(t,o(e,3))}},34935:function(t,e,r){var n=r(72569),o=r(84046),i=r(44843),a=r(49639),u=i(function(t,e){if(null==t)return[];var r=e.length;return r>1&&a(t,e[0],e[1])?e=[]:r>2&&a(e[0],e[1],e[2])&&(e=[e[0]]),o(t,n(e,1),[])});t.exports=u},55716:function(t){t.exports=function(){return[]}},7406:function(t){t.exports=function(){return!1}},37065:function(t,e,r){var n=r(7310),o=r(28302);t.exports=function(t,e,r){var i=!0,a=!0;if("function"!=typeof t)throw TypeError("Expected a function");return o(r)&&(i="leading"in r?!!r.leading:i,a="trailing"in r?!!r.trailing:a),n(t,e,{leading:i,maxWait:e,trailing:a})}},175:function(t,e,r){var n=r(6660),o=1/0;t.exports=function(t){return t?(t=n(t))===o||t===-o?(t<0?-1:1)*17976931348623157e292:t==t?t:0:0===t?t:0}},85759:function(t,e,r){var n=r(175);t.exports=function(t){var e=n(t),r=e%1;return e==e?r?e-r:e:0}},6660:function(t,e,r){var n=r(55041),o=r(28302),i=r(78371),a=0/0,u=/^[-+]0x[0-9a-f]+$/i,c=/^0b[01]+$/i,s=/^0o[0-7]+$/i,l=parseInt;t.exports=function(t){if("number"==typeof t)return t;if(i(t))return a;if(o(t)){var e="function"==typeof t.valueOf?t.valueOf():t;t=o(e)?e+"":e}if("string"!=typeof t)return 0===t?t:+t;t=n(t);var r=c.test(t);return r||s.test(t)?l(t.slice(2),r?2:8):u.test(t)?a:+t}},3641:function(t,e,r){var n=r(65020);t.exports=function(t){return null==t?"":n(t)}},47230:function(t,e,r){var n=r(88157),o=r(13826);t.exports=function(t,e){return t&&t.length?o(t,n(e,2)):[]}},75551:function(t,e,r){var n=r(80675)("toUpperCase");t.exports=n},92934:function(t,e,r){"use strict";r.d(e,{Z:function(){return n}});let n=(0,r(79205).Z)("CircleCheck",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},93609:function(t,e,r){"use strict";r.d(e,{Z:function(){return n}});let n=(0,r(79205).Z)("Droplet",[["path",{d:"M12 22a7 7 0 0 0 7-7c0-2-1-3.9-3-5.5s-3.5-4-4-6.5c-.5 2.5-2 4.9-4 6.5C6 11.1 5 13 5 15a7 7 0 0 0 7 7z",key:"c7niix"}]])},33245:function(t,e,r){"use strict";r.d(e,{Z:function(){return n}});let n=(0,r(79205).Z)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},21047:function(t,e,r){"use strict";r.d(e,{Z:function(){return n}});let n=(0,r(79205).Z)("Minus",[["path",{d:"M5 12h14",key:"1ays0h"}]])},47737:function(t,e,r){"use strict";r.d(e,{Z:function(){return n}});let n=(0,r(79205).Z)("Mountain",[["path",{d:"m8 3 4 8 5-5 5 15H2L8 3z",key:"otkl63"}]])},99397:function(t,e,r){"use strict";r.d(e,{Z:function(){return n}});let n=(0,r(79205).Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},73247:function(t,e,r){"use strict";r.d(e,{Z:function(){return n}});let n=(0,r(79205).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},10866:function(t,e,r){"use strict";r.d(e,{Z:function(){return n}});let n=(0,r(79205).Z)("Settings2",[["path",{d:"M20 7h-9",key:"3s1dr2"}],["path",{d:"M14 17H5",key:"gfn3mx"}],["circle",{cx:"17",cy:"17",r:"3",key:"18b49y"}],["circle",{cx:"7",cy:"7",r:"3",key:"dfmy0x"}]])},54882:function(t,e,r){"use strict";r.d(e,{Z:function(){return n}});let n=(0,r(79205).Z)("Sprout",[["path",{d:"M7 20h10",key:"e6iznv"}],["path",{d:"M10 20c5.5-2.5.8-6.4 3-10",key:"161w41"}],["path",{d:"M9.5 9.4c1.1.8 1.8 2.2 2.3 3.7-2 .4-3.5.4-4.8-.3-1.2-.6-2.3-1.9-3-4.2 2.8-.5 4.4 0 5.5.8z",key:"9gtqwd"}],["path",{d:"M14.1 6a7 7 0 0 0-1.1 4c1.9-.1 3.3-.6 4.3-1.4 1-1 1.6-2.3 1.7-4.6-2.7.1-4 1-4.9 2z",key:"bkxnd2"}]])},18930:function(t,e,r){"use strict";r.d(e,{Z:function(){return n}});let n=(0,r(79205).Z)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},47755:function(t,e,r){"use strict";r.d(e,{Z:function(){return n}});let n=(0,r(79205).Z)("Trees",[["path",{d:"M10 10v.2A3 3 0 0 1 8.9 16H5a3 3 0 0 1-1-5.8V10a3 3 0 0 1 6 0Z",key:"1l6gj6"}],["path",{d:"M7 16v6",key:"1a82de"}],["path",{d:"M13 19v3",key:"13sx9i"}],["path",{d:"M12 19h8.3a1 1 0 0 0 .7-1.7L18 14h.3a1 1 0 0 0 .7-1.7L16 9h.2a1 1 0 0 0 .8-1.7L13 3l-1.4 1.5",key:"1sj9kv"}]])},62926:function(t,e,r){"use strict";r.d(e,{Z:function(){return n}});let n=(0,r(79205).Z)("Wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},71318:function(t,e,r){var n,o;void 0!==(o="function"==typeof(n=function(){var t,e,r,n={};n.version="0.2.0";var o=n.settings={minimum:.08,easing:"ease",positionUsing:"",speed:200,trickle:!0,trickleRate:.02,trickleSpeed:800,showSpinner:!0,barSelector:'[role="bar"]',spinnerSelector:'[role="spinner"]',parent:"body",template:'<div class="bar" role="bar"><div class="peg"></div></div><div class="spinner" role="spinner"><div class="spinner-icon"></div></div>'};function i(t,e,r){return t<e?e:t>r?r:t}n.configure=function(t){var e,r;for(e in t)void 0!==(r=t[e])&&t.hasOwnProperty(e)&&(o[e]=r);return this},n.status=null,n.set=function(t){var e=n.isStarted();t=i(t,o.minimum,1),n.status=1===t?null:t;var r=n.render(!e),c=r.querySelector(o.barSelector),s=o.speed,l=o.easing;return r.offsetWidth,a(function(e){var i,a;""===o.positionUsing&&(o.positionUsing=n.getPositioningCSS()),u(c,(i=t,(a="translate3d"===o.positionUsing?{transform:"translate3d("+(-1+i)*100+"%,0,0)"}:"translate"===o.positionUsing?{transform:"translate("+(-1+i)*100+"%,0)"}:{"margin-left":(-1+i)*100+"%"}).transition="all "+s+"ms "+l,a)),1===t?(u(r,{transition:"none",opacity:1}),r.offsetWidth,setTimeout(function(){u(r,{transition:"all "+s+"ms linear",opacity:0}),setTimeout(function(){n.remove(),e()},s)},s)):setTimeout(e,s)}),this},n.isStarted=function(){return"number"==typeof n.status},n.start=function(){n.status||n.set(0);var t=function(){setTimeout(function(){n.status&&(n.trickle(),t())},o.trickleSpeed)};return o.trickle&&t(),this},n.done=function(t){return t||n.status?n.inc(.3+.5*Math.random()).set(1):this},n.inc=function(t){var e=n.status;return e?("number"!=typeof t&&(t=(1-e)*i(Math.random()*e,.1,.95)),e=i(e+t,0,.994),n.set(e)):n.start()},n.trickle=function(){return n.inc(Math.random()*o.trickleRate)},t=0,e=0,n.promise=function(r){return r&&"resolved"!==r.state()&&(0===e&&n.start(),t++,e++,r.always(function(){0==--e?(t=0,n.done()):n.set((t-e)/t)})),this},n.render=function(t){if(n.isRendered())return document.getElementById("nprogress");s(document.documentElement,"nprogress-busy");var e=document.createElement("div");e.id="nprogress",e.innerHTML=o.template;var r,i=e.querySelector(o.barSelector),a=t?"-100":(-1+(n.status||0))*100,c=document.querySelector(o.parent);return u(i,{transition:"all 0 linear",transform:"translate3d("+a+"%,0,0)"}),!o.showSpinner&&(r=e.querySelector(o.spinnerSelector))&&p(r),c!=document.body&&s(c,"nprogress-custom-parent"),c.appendChild(e),e},n.remove=function(){l(document.documentElement,"nprogress-busy"),l(document.querySelector(o.parent),"nprogress-custom-parent");var t=document.getElementById("nprogress");t&&p(t)},n.isRendered=function(){return!!document.getElementById("nprogress")},n.getPositioningCSS=function(){var t=document.body.style,e="WebkitTransform"in t?"Webkit":"MozTransform"in t?"Moz":"msTransform"in t?"ms":"OTransform"in t?"O":"";return e+"Perspective" in t?"translate3d":e+"Transform" in t?"translate":"margin"};var a=(r=[],function(t){r.push(t),1==r.length&&function t(){var e=r.shift();e&&e(t)}()}),u=function(){var t=["Webkit","O","Moz","ms"],e={};function r(r,n,o){var i;n=e[i=(i=n).replace(/^-ms-/,"ms-").replace(/-([\da-z])/gi,function(t,e){return e.toUpperCase()})]||(e[i]=function(e){var r=document.body.style;if(e in r)return e;for(var n,o=t.length,i=e.charAt(0).toUpperCase()+e.slice(1);o--;)if((n=t[o]+i)in r)return n;return e}(i)),r.style[n]=o}return function(t,e){var n,o,i=arguments;if(2==i.length)for(n in e)void 0!==(o=e[n])&&e.hasOwnProperty(n)&&r(t,n,o);else r(t,i[1],i[2])}}();function c(t,e){return("string"==typeof t?t:f(t)).indexOf(" "+e+" ")>=0}function s(t,e){var r=f(t),n=r+e;c(r,e)||(t.className=n.substring(1))}function l(t,e){var r,n=f(t);c(t,e)&&(r=n.replace(" "+e+" "," "),t.className=r.substring(1,r.length-1))}function f(t){return(" "+(t.className||"")+" ").replace(/\s+/gi," ")}function p(t){t&&t.parentNode&&t.parentNode.removeChild(t)}return n})?n.call(e,r,e,t):n)&&(t.exports=o)},48049:function(t,e,r){"use strict";var n=r(14397);function o(){}function i(){}i.resetWarningCache=o,t.exports=function(){function t(t,e,r,o,i,a){if(a!==n){var u=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw u.name="Invariant Violation",u}}function e(){return t}t.isRequired=t;var r={array:t,bigint:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,elementType:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e,exact:e,checkPropTypes:i,resetWarningCache:o};return r.PropTypes=r,r}},40718:function(t,e,r){t.exports=r(48049)()},14397:function(t){"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},84735:function(t,e,r){"use strict";r.d(e,{ZP:function(){return tO}});var n=r(2265),o=r(40718),i=r.n(o),a=Object.getOwnPropertyNames,u=Object.getOwnPropertySymbols,c=Object.prototype.hasOwnProperty;function s(t,e){return function(r,n,o){return t(r,n,o)&&e(r,n,o)}}function l(t){return function(e,r,n){if(!e||!r||"object"!=typeof e||"object"!=typeof r)return t(e,r,n);var o=n.cache,i=o.get(e),a=o.get(r);if(i&&a)return i===r&&a===e;o.set(e,r),o.set(r,e);var u=t(e,r,n);return o.delete(e),o.delete(r),u}}function f(t){return a(t).concat(u(t))}var p=Object.hasOwn||function(t,e){return c.call(t,e)};function h(t,e){return t||e?t===e:t===e||t!=t&&e!=e}var d="_owner",y=Object.getOwnPropertyDescriptor,v=Object.keys;function m(t,e,r){var n=t.length;if(e.length!==n)return!1;for(;n-- >0;)if(!r.equals(t[n],e[n],n,n,t,e,r))return!1;return!0}function b(t,e){return h(t.getTime(),e.getTime())}function g(t,e,r){if(t.size!==e.size)return!1;for(var n,o,i={},a=t.entries(),u=0;(n=a.next())&&!n.done;){for(var c=e.entries(),s=!1,l=0;(o=c.next())&&!o.done;){var f=n.value,p=f[0],h=f[1],d=o.value,y=d[0],v=d[1];!s&&!i[l]&&(s=r.equals(p,y,u,l,t,e,r)&&r.equals(h,v,p,y,t,e,r))&&(i[l]=!0),l++}if(!s)return!1;u++}return!0}function x(t,e,r){var n,o=v(t),i=o.length;if(v(e).length!==i)return!1;for(;i-- >0;)if((n=o[i])===d&&(t.$$typeof||e.$$typeof)&&t.$$typeof!==e.$$typeof||!p(e,n)||!r.equals(t[n],e[n],n,n,t,e,r))return!1;return!0}function O(t,e,r){var n,o,i,a=f(t),u=a.length;if(f(e).length!==u)return!1;for(;u-- >0;)if((n=a[u])===d&&(t.$$typeof||e.$$typeof)&&t.$$typeof!==e.$$typeof||!p(e,n)||!r.equals(t[n],e[n],n,n,t,e,r)||(o=y(t,n),i=y(e,n),(o||i)&&(!o||!i||o.configurable!==i.configurable||o.enumerable!==i.enumerable||o.writable!==i.writable)))return!1;return!0}function w(t,e){return h(t.valueOf(),e.valueOf())}function j(t,e){return t.source===e.source&&t.flags===e.flags}function S(t,e,r){if(t.size!==e.size)return!1;for(var n,o,i={},a=t.values();(n=a.next())&&!n.done;){for(var u=e.values(),c=!1,s=0;(o=u.next())&&!o.done;)!c&&!i[s]&&(c=r.equals(n.value,o.value,n.value,o.value,t,e,r))&&(i[s]=!0),s++;if(!c)return!1}return!0}function P(t,e){var r=t.length;if(e.length!==r)return!1;for(;r-- >0;)if(t[r]!==e[r])return!1;return!0}var A=Array.isArray,E="function"==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView:null,k=Object.assign,M=Object.prototype.toString.call.bind(Object.prototype.toString),T=_();function _(t){void 0===t&&(t={});var e,r,n,o,i,a,u,c,f,p=t.circular,h=t.createInternalComparator,d=t.createState,y=t.strict,v=(r=(e=function(t){var e=t.circular,r=t.createCustomConfig,n=t.strict,o={areArraysEqual:n?O:m,areDatesEqual:b,areMapsEqual:n?s(g,O):g,areObjectsEqual:n?O:x,arePrimitiveWrappersEqual:w,areRegExpsEqual:j,areSetsEqual:n?s(S,O):S,areTypedArraysEqual:n?O:P};if(r&&(o=k({},o,r(o))),e){var i=l(o.areArraysEqual),a=l(o.areMapsEqual),u=l(o.areObjectsEqual),c=l(o.areSetsEqual);o=k({},o,{areArraysEqual:i,areMapsEqual:a,areObjectsEqual:u,areSetsEqual:c})}return o}(t)).areArraysEqual,n=e.areDatesEqual,o=e.areMapsEqual,i=e.areObjectsEqual,a=e.arePrimitiveWrappersEqual,u=e.areRegExpsEqual,c=e.areSetsEqual,f=e.areTypedArraysEqual,function(t,e,s){if(t===e)return!0;if(null==t||null==e||"object"!=typeof t||"object"!=typeof e)return t!=t&&e!=e;var l=t.constructor;if(l!==e.constructor)return!1;if(l===Object)return i(t,e,s);if(A(t))return r(t,e,s);if(null!=E&&E(t))return f(t,e,s);if(l===Date)return n(t,e,s);if(l===RegExp)return u(t,e,s);if(l===Map)return o(t,e,s);if(l===Set)return c(t,e,s);var p=M(t);return"[object Date]"===p?n(t,e,s):"[object RegExp]"===p?u(t,e,s):"[object Map]"===p?o(t,e,s):"[object Set]"===p?c(t,e,s):"[object Object]"===p?"function"!=typeof t.then&&"function"!=typeof e.then&&i(t,e,s):"[object Arguments]"===p?i(t,e,s):("[object Boolean]"===p||"[object Number]"===p||"[object String]"===p)&&a(t,e,s)}),T=h?h(v):function(t,e,r,n,o,i,a){return v(t,e,a)};return function(t){var e=t.circular,r=t.comparator,n=t.createState,o=t.equals,i=t.strict;if(n)return function(t,a){var u=n(),c=u.cache;return r(t,a,{cache:void 0===c?e?new WeakMap:void 0:c,equals:o,meta:u.meta,strict:i})};if(e)return function(t,e){return r(t,e,{cache:new WeakMap,equals:o,meta:void 0,strict:i})};var a={cache:void 0,equals:o,meta:void 0,strict:i};return function(t,e){return r(t,e,a)}}({circular:void 0!==p&&p,comparator:v,createState:d,equals:T,strict:void 0!==y&&y})}function C(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=-1;requestAnimationFrame(function n(o){if(r<0&&(r=o),o-r>e)t(o),r=-1;else{var i;i=n,"undefined"!=typeof requestAnimationFrame&&requestAnimationFrame(i)}})}function D(t){return(D="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function I(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function N(t){return(N="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function B(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function L(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?B(Object(r),!0).forEach(function(e){R(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):B(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function R(t,e,r){var n;return(n=function(t,e){if("object"!==N(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==N(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===N(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}_({strict:!0}),_({circular:!0}),_({circular:!0,strict:!0}),_({createInternalComparator:function(){return h}}),_({strict:!0,createInternalComparator:function(){return h}}),_({circular:!0,createInternalComparator:function(){return h}}),_({circular:!0,createInternalComparator:function(){return h},strict:!0});var z=function(t){return t},U=function(t,e){return Object.keys(e).reduce(function(r,n){return L(L({},r),{},R({},n,t(n,e[n])))},{})},$=function(t,e,r){return t.map(function(t){return"".concat(t.replace(/([A-Z])/g,function(t){return"-".concat(t.toLowerCase())})," ").concat(e,"ms ").concat(r)}).join(",")},F=function(t,e,r,n,o,i,a,u){};function Z(t,e){if(t){if("string"==typeof t)return W(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return W(t,e)}}function W(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var q=function(t,e){return[0,3*t,3*e-6*t,3*t-3*e+1]},Y=function(t,e){return t.map(function(t,r){return t*Math.pow(e,r)}).reduce(function(t,e){return t+e})},V=function(t,e){return function(r){return Y(q(t,e),r)}},X=function(){for(var t,e,r=arguments.length,n=Array(r),o=0;o<r;o++)n[o]=arguments[o];var i=n[0],a=n[1],u=n[2],c=n[3];if(1===n.length)switch(n[0]){case"linear":i=0,a=0,u=1,c=1;break;case"ease":i=.25,a=.1,u=.25,c=1;break;case"ease-in":i=.42,a=0,u=1,c=1;break;case"ease-out":i=.42,a=0,u=.58,c=1;break;case"ease-in-out":i=0,a=0,u=.58,c=1;break;default:var s=n[0].split("(");if("cubic-bezier"===s[0]&&4===s[1].split(")")[0].split(",").length){var l,f=function(t){if(Array.isArray(t))return t}(l=s[1].split(")")[0].split(",").map(function(t){return parseFloat(t)}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,s=!1;try{for(i=(r=r.call(t)).next;!(c=(n=i.call(r)).done)&&(u.push(n.value),4!==u.length);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(l,4)||Z(l,4)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();i=f[0],a=f[1],u=f[2],c=f[3]}else F(!1,"[configBezier]: arguments should be one of oneOf 'linear', 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', instead received %s",n)}F([i,u,a,c].every(function(t){return"number"==typeof t&&t>=0&&t<=1}),"[configBezier]: arguments should be x1, y1, x2, y2 of [0, 1] instead received %s",n);var p=V(i,u),h=V(a,c),d=(t=i,e=u,function(r){var n;return Y([].concat(function(t){if(Array.isArray(t))return W(t)}(n=q(t,e).map(function(t,e){return t*e}).slice(1))||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(n)||Z(n)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),[0]),r)}),y=function(t){for(var e=t>1?1:t,r=e,n=0;n<8;++n){var o,i=p(r)-e,a=d(r);if(1e-4>Math.abs(i-e)||a<1e-4)break;r=(o=r-i/a)>1?1:o<0?0:o}return h(r)};return y.isStepper=!1,y},H=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.stiff,r=void 0===e?100:e,n=t.damping,o=void 0===n?8:n,i=t.dt,a=void 0===i?17:i,u=function(t,e,n){var i=n+(-(t-e)*r-n*o)*a/1e3,u=n*a/1e3+t;return 1e-4>Math.abs(u-e)&&1e-4>Math.abs(i)?[e,0]:[u,i]};return u.isStepper=!0,u.dt=a,u},K=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];var n=e[0];if("string"==typeof n)switch(n){case"ease":case"ease-in-out":case"ease-out":case"ease-in":case"linear":return X(n);case"spring":return H();default:if("cubic-bezier"===n.split("(")[0])return X(n);F(!1,"[configEasing]: first argument should be one of 'ease', 'ease-in', 'ease-out', 'ease-in-out','cubic-bezier(x1,y1,x2,y2)', 'linear' and 'spring', instead  received %s",e)}return"function"==typeof n?n:(F(!1,"[configEasing]: first argument type should be function or string, instead received %s",e),null)};function G(t){return(G="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function J(t){return function(t){if(Array.isArray(t))return tn(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||tr(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Q(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tt(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Q(Object(r),!0).forEach(function(e){te(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Q(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function te(t,e,r){var n;return(n=function(t,e){if("object"!==G(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==G(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"===G(n)?n:String(n))in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tr(t,e){if(t){if("string"==typeof t)return tn(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tn(t,e)}}function tn(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var to=function(t,e,r){return t+(e-t)*r},ti=function(t){return t.from!==t.to},ta=function t(e,r,n){var o=U(function(t,r){if(ti(r)){var n,o=function(t){if(Array.isArray(t))return t}(n=e(r.from,r.to,r.velocity))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,s=!1;try{for(i=(r=r.call(t)).next;!(c=(n=i.call(r)).done)&&(u.push(n.value),2!==u.length);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(n,2)||tr(n,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o[1];return tt(tt({},r),{},{from:i,velocity:a})}return r},r);return n<1?U(function(t,e){return ti(e)?tt(tt({},e),{},{velocity:to(e.velocity,o[t].velocity,n),from:to(e.from,o[t].from,n)}):e},r):t(e,o,n-1)},tu=function(t,e,r,n,o){var i,a,u=[Object.keys(t),Object.keys(e)].reduce(function(t,e){return t.filter(function(t){return e.includes(t)})}),c=u.reduce(function(r,n){return tt(tt({},r),{},te({},n,[t[n],e[n]]))},{}),s=u.reduce(function(r,n){return tt(tt({},r),{},te({},n,{from:t[n],velocity:0,to:e[n]}))},{}),l=-1,f=function(){return null};return f=r.isStepper?function(n){i||(i=n);var a=(n-i)/r.dt;s=ta(r,s,a),o(tt(tt(tt({},t),e),U(function(t,e){return e.from},s))),i=n,Object.values(s).filter(ti).length&&(l=requestAnimationFrame(f))}:function(i){a||(a=i);var u=(i-a)/n,s=U(function(t,e){return to.apply(void 0,J(e).concat([r(u)]))},c);if(o(tt(tt(tt({},t),e),s)),u<1)l=requestAnimationFrame(f);else{var p=U(function(t,e){return to.apply(void 0,J(e).concat([r(1)]))},c);o(tt(tt(tt({},t),e),p))}},function(){return requestAnimationFrame(f),function(){cancelAnimationFrame(l)}}};function tc(t){return(tc="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var ts=["children","begin","duration","attributeName","easing","isActive","steps","from","to","canBegin","onAnimationEnd","shouldReAnimate","onAnimationReStart"];function tl(t){return function(t){if(Array.isArray(t))return tf(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return tf(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return tf(t,void 0)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function tf(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function tp(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function th(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tp(Object(r),!0).forEach(function(e){td(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tp(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function td(t,e,r){return(e=ty(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ty(t){var e=function(t,e){if("object"!==tc(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==tc(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===tc(e)?e:String(e)}function tv(t,e){return(tv=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tm(t,e){if(e&&("object"===tc(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return tb(t)}function tb(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function tg(t){return(tg=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}var tx=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&tv(t,e)}(i,t);var e,r,o=(e=function(){if("undefined"==typeof Reflect||!Reflect.construct||Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(t){return!1}}(),function(){var t,r=tg(i);return t=e?Reflect.construct(r,arguments,tg(this).constructor):r.apply(this,arguments),tm(this,t)});function i(t,e){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,i);var r,n=(r=o.call(this,t,e)).props,a=n.isActive,u=n.attributeName,c=n.from,s=n.to,l=n.steps,f=n.children,p=n.duration;if(r.handleStyleChange=r.handleStyleChange.bind(tb(r)),r.changeStyle=r.changeStyle.bind(tb(r)),!a||p<=0)return r.state={style:{}},"function"==typeof f&&(r.state={style:s}),tm(r);if(l&&l.length)r.state={style:l[0].style};else if(c){if("function"==typeof f)return r.state={style:c},tm(r);r.state={style:u?td({},u,c):c}}else r.state={style:{}};return r}return r=[{key:"componentDidMount",value:function(){var t=this.props,e=t.isActive,r=t.canBegin;this.mounted=!0,e&&r&&this.runAnimation(this.props)}},{key:"componentDidUpdate",value:function(t){var e=this.props,r=e.isActive,n=e.canBegin,o=e.attributeName,i=e.shouldReAnimate,a=e.to,u=e.from,c=this.state.style;if(n){if(!r){var s={style:o?td({},o,a):a};this.state&&c&&(o&&c[o]!==a||!o&&c!==a)&&this.setState(s);return}if(!T(t.to,a)||!t.canBegin||!t.isActive){var l=!t.canBegin||!t.isActive;this.manager&&this.manager.stop(),this.stopJSAnimation&&this.stopJSAnimation();var f=l||i?u:t.to;if(this.state&&c){var p={style:o?td({},o,f):f};(o&&c[o]!==f||!o&&c!==f)&&this.setState(p)}this.runAnimation(th(th({},this.props),{},{from:f,begin:0}))}}}},{key:"componentWillUnmount",value:function(){this.mounted=!1;var t=this.props.onAnimationEnd;this.unSubscribe&&this.unSubscribe(),this.manager&&(this.manager.stop(),this.manager=null),this.stopJSAnimation&&this.stopJSAnimation(),t&&t()}},{key:"handleStyleChange",value:function(t){this.changeStyle(t)}},{key:"changeStyle",value:function(t){this.mounted&&this.setState({style:t})}},{key:"runJSAnimation",value:function(t){var e=this,r=t.from,n=t.to,o=t.duration,i=t.easing,a=t.begin,u=t.onAnimationEnd,c=t.onAnimationStart,s=tu(r,n,K(i),o,this.changeStyle);this.manager.start([c,a,function(){e.stopJSAnimation=s()},o,u])}},{key:"runStepAnimation",value:function(t){var e=this,r=t.steps,n=t.begin,o=t.onAnimationStart,i=r[0],a=i.style,u=i.duration;return this.manager.start([o].concat(tl(r.reduce(function(t,n,o){if(0===o)return t;var i=n.duration,a=n.easing,u=void 0===a?"ease":a,c=n.style,s=n.properties,l=n.onAnimationEnd,f=o>0?r[o-1]:n,p=s||Object.keys(c);if("function"==typeof u||"spring"===u)return[].concat(tl(t),[e.runJSAnimation.bind(e,{from:f.style,to:c,duration:i,easing:u}),i]);var h=$(p,i,u),d=th(th(th({},f.style),c),{},{transition:h});return[].concat(tl(t),[d,i,l]).filter(z)},[a,Math.max(void 0===u?0:u,n)])),[t.onAnimationEnd]))}},{key:"runAnimation",value:function(t){if(!this.manager){var e,r,n;this.manager=(e=function(){return null},r=!1,n=function t(n){if(!r){if(Array.isArray(n)){if(!n.length)return;var o=function(t){if(Array.isArray(t))return t}(n)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(n)||function(t,e){if(t){if("string"==typeof t)return I(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return I(t,void 0)}}(n)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),i=o[0],a=o.slice(1);if("number"==typeof i){C(t.bind(null,a),i);return}t(i),C(t.bind(null,a));return}"object"===D(n)&&e(n),"function"==typeof n&&n()}},{stop:function(){r=!0},start:function(t){r=!1,n(t)},subscribe:function(t){return e=t,function(){e=function(){return null}}}})}var o=t.begin,i=t.duration,a=t.attributeName,u=t.to,c=t.easing,s=t.onAnimationStart,l=t.onAnimationEnd,f=t.steps,p=t.children,h=this.manager;if(this.unSubscribe=h.subscribe(this.handleStyleChange),"function"==typeof c||"function"==typeof p||"spring"===c){this.runJSAnimation(t);return}if(f.length>1){this.runStepAnimation(t);return}var d=a?td({},a,u):u,y=$(Object.keys(d),i,c);h.start([s,o,th(th({},d),{},{transition:y}),i,l])}},{key:"render",value:function(){var t=this.props,e=t.children,r=(t.begin,t.duration),o=(t.attributeName,t.easing,t.isActive),i=(t.steps,t.from,t.to,t.canBegin,t.onAnimationEnd,t.shouldReAnimate,t.onAnimationReStart,function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||(o[r]=t[r]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,ts)),a=n.Children.count(e),u=this.state.style;if("function"==typeof e)return e(u);if(!o||0===a||r<=0)return e;var c=function(t){var e=t.props,r=e.style,o=e.className;return(0,n.cloneElement)(t,th(th({},i),{},{style:th(th({},void 0===r?{}:r),u),className:o}))};return 1===a?c(n.Children.only(e)):n.createElement("div",null,n.Children.map(e,function(t){return c(t)}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ty(n.key),n)}}(i.prototype,r),Object.defineProperty(i,"prototype",{writable:!1}),i}(n.PureComponent);tx.displayName="Animate",tx.defaultProps={begin:0,duration:1e3,from:"",to:"",attributeName:"",easing:"ease",isActive:!0,canBegin:!0,steps:[],onAnimationEnd:function(){},onAnimationStart:function(){}},tx.propTypes={from:i().oneOfType([i().object,i().string]),to:i().oneOfType([i().object,i().string]),attributeName:i().string,duration:i().number,begin:i().number,easing:i().oneOfType([i().string,i().func]),steps:i().arrayOf(i().shape({duration:i().number.isRequired,style:i().object.isRequired,easing:i().oneOfType([i().oneOf(["ease","ease-in","ease-out","ease-in-out","linear"]),i().func]),properties:i().arrayOf("string"),onAnimationEnd:i().func})),children:i().oneOfType([i().node,i().func]),isActive:i().bool,canBegin:i().bool,onAnimationEnd:i().func,shouldReAnimate:i().bool,onAnimationStart:i().func,onAnimationReStart:i().func};var tO=tx},86530:function(t,e,r){"use strict";r.d(e,{$:function(){return tr}});var n=r(2265),o=r(61994),i=r(84735),a=r(21652),u=r.n(a),c=r(77571),s=r.n(c),l=r(9841),f=r(13137),p=r(20407),h=r(28302),d=r.n(h),y=r(86757),v=r.n(y),m=r(86185),b=r.n(m),g=r(26680),x=r(82944),O=r(85355);function w(t){return(w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var j=["valueAccessor"],S=["data","dataKey","clockWise","id","textBreakAll"];function P(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function A(){return(A=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function E(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function k(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?E(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=w(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=w(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==w(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):E(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function M(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var T=function(t){return Array.isArray(t.value)?b()(t.value):t.value};function _(t){var e=t.valueAccessor,r=void 0===e?T:e,o=M(t,j),i=o.data,a=o.dataKey,u=o.clockWise,c=o.id,f=o.textBreakAll,p=M(o,S);return i&&i.length?n.createElement(l.m,{className:"recharts-label-list"},i.map(function(t,e){var o=s()(a)?r(t,e):(0,O.F$)(t&&t.payload,a),i=s()(c)?{}:{id:"".concat(c,"-").concat(e)};return n.createElement(g._,A({},(0,x.L6)(t,!0),p,i,{parentViewBox:t.parentViewBox,value:o,textBreakAll:f,viewBox:g._.parseViewBox(s()(u)?t:k(k({},t),{},{clockWise:u})),key:"label-".concat(e),index:e}))})):null}_.displayName="LabelList",_.renderCallByParent=function(t,e){var r,o=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&o&&!t.label)return null;var i=t.children,a=(0,x.NN)(i,_).map(function(t,r){return(0,n.cloneElement)(t,{data:e,key:"labelList-".concat(r)})});return o?[(r=t.label)?!0===r?n.createElement(_,{key:"labelList-implicit",data:e}):n.isValidElement(r)||v()(r)?n.createElement(_,{key:"labelList-implicit",data:e,content:r}):d()(r)?n.createElement(_,A({data:e},r,{key:"labelList-implicit"})):null:null].concat(function(t){if(Array.isArray(t))return P(t)}(a)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(a)||function(t,e){if(t){if("string"==typeof t)return P(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return P(t,void 0)}}(a)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):a};var C=r(16630),D=r(34067),I=r(41637),N=r(69398),B=r(11638),L=["x","y"];function R(t){return(R="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function z(){return(z=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function U(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function $(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?U(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=R(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=R(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==R(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):U(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function F(t,e){var r=t.x,n=t.y,o=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,L),i=parseInt("".concat(r),10),a=parseInt("".concat(n),10),u=parseInt("".concat(e.height||o.height),10),c=parseInt("".concat(e.width||o.width),10);return $($($($($({},e),o),i?{x:i}:{}),a?{y:a}:{}),{},{height:u,width:c,name:e.name,radius:e.radius})}function Z(t){return n.createElement(B.bn,z({shapeType:"rectangle",propTransformer:F,activeClassName:"recharts-active-bar"},t))}var W=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return function(r,n){if("number"==typeof t)return t;var o="number"==typeof r;return o?t(r,n):(o||(0,N.Z)(!1),e)}},q=["value","background"];function Y(t){return(Y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function V(){return(V=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function X(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function H(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?X(Object(r),!0).forEach(function(e){tt(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):X(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function K(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,te(n.key),n)}}function G(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(G=function(){return!!t})()}function J(t){return(J=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function Q(t,e){return(Q=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tt(t,e,r){return(e=te(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function te(t){var e=function(t,e){if("object"!=Y(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=Y(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==Y(e)?e:e+""}var tr=function(t){var e,r;function a(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,a);for(var t,e,r,n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return e=a,r=[].concat(o),e=J(e),tt(t=function(t,e){if(e&&("object"===Y(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,G()?Reflect.construct(e,r||[],J(this).constructor):e.apply(this,r)),"state",{isAnimationFinished:!1}),tt(t,"id",(0,C.EL)("recharts-bar-")),tt(t,"handleAnimationEnd",function(){var e=t.props.onAnimationEnd;t.setState({isAnimationFinished:!0}),e&&e()}),tt(t,"handleAnimationStart",function(){var e=t.props.onAnimationStart;t.setState({isAnimationFinished:!1}),e&&e()}),t}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Q(t,e)}(a,t),e=[{key:"renderRectanglesStatically",value:function(t){var e=this,r=this.props,o=r.shape,i=r.dataKey,a=r.activeIndex,u=r.activeBar,c=(0,x.L6)(this.props,!1);return t&&t.map(function(t,r){var s=r===a,f=H(H(H({},c),t),{},{isActive:s,option:s?u:o,index:r,dataKey:i,onAnimationStart:e.handleAnimationStart,onAnimationEnd:e.handleAnimationEnd});return n.createElement(l.m,V({className:"recharts-bar-rectangle"},(0,I.bw)(e.props,t,r),{key:"rectangle-".concat(null==t?void 0:t.x,"-").concat(null==t?void 0:t.y,"-").concat(null==t?void 0:t.value)}),n.createElement(Z,f))})}},{key:"renderRectanglesWithAnimation",value:function(){var t=this,e=this.props,r=e.data,o=e.layout,a=e.isAnimationActive,u=e.animationBegin,c=e.animationDuration,s=e.animationEasing,f=e.animationId,p=this.state.prevData;return n.createElement(i.ZP,{begin:u,duration:c,isActive:a,easing:s,from:{t:0},to:{t:1},key:"bar-".concat(f),onAnimationEnd:this.handleAnimationEnd,onAnimationStart:this.handleAnimationStart},function(e){var i=e.t,a=r.map(function(t,e){var r=p&&p[e];if(r){var n=(0,C.k4)(r.x,t.x),a=(0,C.k4)(r.y,t.y),u=(0,C.k4)(r.width,t.width),c=(0,C.k4)(r.height,t.height);return H(H({},t),{},{x:n(i),y:a(i),width:u(i),height:c(i)})}if("horizontal"===o){var s=(0,C.k4)(0,t.height)(i);return H(H({},t),{},{y:t.y+t.height-s,height:s})}var l=(0,C.k4)(0,t.width)(i);return H(H({},t),{},{width:l})});return n.createElement(l.m,null,t.renderRectanglesStatically(a))})}},{key:"renderRectangles",value:function(){var t=this.props,e=t.data,r=t.isAnimationActive,n=this.state.prevData;return r&&e&&e.length&&(!n||!u()(n,e))?this.renderRectanglesWithAnimation():this.renderRectanglesStatically(e)}},{key:"renderBackground",value:function(){var t=this,e=this.props,r=e.data,o=e.dataKey,i=e.activeIndex,a=(0,x.L6)(this.props.background,!1);return r.map(function(e,r){e.value;var u=e.background,c=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(e,q);if(!u)return null;var s=H(H(H(H(H({},c),{},{fill:"#eee"},u),a),(0,I.bw)(t.props,e,r)),{},{onAnimationStart:t.handleAnimationStart,onAnimationEnd:t.handleAnimationEnd,dataKey:o,index:r,className:"recharts-bar-background-rectangle"});return n.createElement(Z,V({key:"background-bar-".concat(r),option:t.props.background,isActive:r===i},s))})}},{key:"renderErrorBar",value:function(t,e){if(this.props.isAnimationActive&&!this.state.isAnimationFinished)return null;var r=this.props,o=r.data,i=r.xAxis,a=r.yAxis,u=r.layout,c=r.children,s=(0,x.NN)(c,f.W);if(!s)return null;var p="vertical"===u?o[0].height/2:o[0].width/2,h=function(t,e){var r=Array.isArray(t.value)?t.value[1]:t.value;return{x:t.x,y:t.y,value:r,errorVal:(0,O.F$)(t,e)}};return n.createElement(l.m,{clipPath:t?"url(#clipPath-".concat(e,")"):null},s.map(function(t){return n.cloneElement(t,{key:"error-bar-".concat(e,"-").concat(t.props.dataKey),data:o,xAxis:i,yAxis:a,layout:u,offset:p,dataPointFormatter:h})}))}},{key:"render",value:function(){var t=this.props,e=t.hide,r=t.data,i=t.className,a=t.xAxis,u=t.yAxis,c=t.left,f=t.top,p=t.width,h=t.height,d=t.isAnimationActive,y=t.background,v=t.id;if(e||!r||!r.length)return null;var m=this.state.isAnimationFinished,b=(0,o.Z)("recharts-bar",i),g=a&&a.allowDataOverflow,x=u&&u.allowDataOverflow,O=g||x,w=s()(v)?this.id:v;return n.createElement(l.m,{className:b},g||x?n.createElement("defs",null,n.createElement("clipPath",{id:"clipPath-".concat(w)},n.createElement("rect",{x:g?c:c-p/2,y:x?f:f-h/2,width:g?p:2*p,height:x?h:2*h}))):null,n.createElement(l.m,{className:"recharts-bar-rectangles",clipPath:O?"url(#clipPath-".concat(w,")"):null},y?this.renderBackground():null,this.renderRectangles()),this.renderErrorBar(O,w),(!d||m)&&_.renderCallByParent(this.props,r))}}],r=[{key:"getDerivedStateFromProps",value:function(t,e){return t.animationId!==e.prevAnimationId?{prevAnimationId:t.animationId,curData:t.data,prevData:e.curData}:t.data!==e.curData?{curData:t.data}:null}}],e&&K(a.prototype,e),r&&K(a,r),Object.defineProperty(a,"prototype",{writable:!1}),a}(n.PureComponent);tt(tr,"displayName","Bar"),tt(tr,"defaultProps",{xAxisId:0,yAxisId:0,legendType:"rect",minPointSize:0,hide:!1,data:[],layout:"vertical",activeBar:!1,isAnimationActive:!D.x.isSsr,animationBegin:0,animationDuration:400,animationEasing:"ease"}),tt(tr,"getComposedData",function(t){var e=t.props,r=t.item,n=t.barPosition,o=t.bandSize,i=t.xAxis,a=t.yAxis,u=t.xAxisTicks,c=t.yAxisTicks,s=t.stackedData,l=t.dataStartIndex,f=t.displayedData,h=t.offset,d=(0,O.Bu)(n,r);if(!d)return null;var y=e.layout,v=r.type.defaultProps,m=void 0!==v?H(H({},v),r.props):r.props,b=m.dataKey,g=m.children,w=m.minPointSize,j="horizontal"===y?a:i,S=s?j.scale.domain():null,P=(0,O.Yj)({numericAxis:j}),A=(0,x.NN)(g,p.b),E=f.map(function(t,e){s?f=(0,O.Vv)(s[l+e],S):Array.isArray(f=(0,O.F$)(t,b))||(f=[P,f]);var n=W(w,tr.defaultProps.minPointSize)(f[1],e);if("horizontal"===y){var f,p,h,v,m,g,x,j=[a.scale(f[0]),a.scale(f[1])],E=j[0],k=j[1];p=(0,O.Fy)({axis:i,ticks:u,bandSize:o,offset:d.offset,entry:t,index:e}),h=null!==(x=null!=k?k:E)&&void 0!==x?x:void 0,v=d.size;var M=E-k;if(m=Number.isNaN(M)?0:M,g={x:p,y:a.y,width:v,height:a.height},Math.abs(n)>0&&Math.abs(m)<Math.abs(n)){var T=(0,C.uY)(m||n)*(Math.abs(n)-Math.abs(m));h-=T,m+=T}}else{var _=[i.scale(f[0]),i.scale(f[1])],D=_[0],I=_[1];if(p=D,h=(0,O.Fy)({axis:a,ticks:c,bandSize:o,offset:d.offset,entry:t,index:e}),v=I-D,m=d.size,g={x:i.x,y:h,width:i.width,height:m},Math.abs(n)>0&&Math.abs(v)<Math.abs(n)){var N=(0,C.uY)(v||n)*(Math.abs(n)-Math.abs(v));v+=N}}return H(H(H({},t),{},{x:p,y:h,width:v,height:m,value:s?f:f[1],payload:t,background:g},A&&A[e]&&A[e].props),{},{tooltipPayload:[(0,O.Qo)(r,t)],tooltipPosition:{x:p+v/2,y:h+m/2}})});return H({data:E,layout:y},h)})},13137:function(t,e,r){"use strict";r.d(e,{W:function(){return v}});var n=r(2265),o=r(69398),i=r(9841),a=r(82944),u=["offset","layout","width","dataKey","data","dataPointFormatter","xAxis","yAxis"];function c(t){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s(){return(s=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function l(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function f(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(f=function(){return!!t})()}function p(t){return(p=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function h(t,e){return(h=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function d(t,e,r){return(e=y(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function y(t){var e=function(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=c(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==c(e)?e:e+""}var v=function(t){var e;function r(){var t,e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=p(t),function(t,e){if(e&&("object"===c(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,f()?Reflect.construct(t,e||[],p(this).constructor):t.apply(this,e))}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&h(t,e)}(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.offset,r=t.layout,c=t.width,f=t.dataKey,p=t.data,h=t.dataPointFormatter,d=t.xAxis,y=t.yAxis,v=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,u),m=(0,a.L6)(v,!1);"x"===this.props.direction&&"number"!==d.type&&(0,o.Z)(!1);var b=p.map(function(t){var o,a,u=h(t,f),p=u.x,v=u.y,b=u.value,g=u.errorVal;if(!g)return null;var x=[];if(Array.isArray(g)){var O=function(t){if(Array.isArray(t))return t}(g)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,s=!1;try{for(i=(r=r.call(t)).next;!(c=(n=i.call(r)).done)&&(u.push(n.value),2!==u.length);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(g,2)||function(t,e){if(t){if("string"==typeof t)return l(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return l(t,2)}}(g,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();o=O[0],a=O[1]}else o=a=g;if("vertical"===r){var w=d.scale,j=v+e,S=j+c,P=j-c,A=w(b-o),E=w(b+a);x.push({x1:E,y1:S,x2:E,y2:P}),x.push({x1:A,y1:j,x2:E,y2:j}),x.push({x1:A,y1:S,x2:A,y2:P})}else if("horizontal"===r){var k=y.scale,M=p+e,T=M-c,_=M+c,C=k(b-o),D=k(b+a);x.push({x1:T,y1:D,x2:_,y2:D}),x.push({x1:M,y1:C,x2:M,y2:D}),x.push({x1:T,y1:C,x2:_,y2:C})}return n.createElement(i.m,s({className:"recharts-errorBar",key:"bar-".concat(x.map(function(t){return"".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))},m),x.map(function(t){return n.createElement("line",s({},t,{key:"line-".concat(t.x1,"-").concat(t.x2,"-").concat(t.y1,"-").concat(t.y2)}))}))});return n.createElement(i.m,{className:"recharts-errorBars"},b)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,y(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.Component);d(v,"defaultProps",{stroke:"black",strokeWidth:1.5,width:5,offset:0,layout:"horizontal"}),d(v,"displayName","ErrorBar")},44946:function(t,e,r){"use strict";r.d(e,{v:function(){return nP}});var n,o,i,a,u,c,s,l,f,p,h,d,y,v,m,b,g,x=r(2265),O=r(77571),w=r.n(O),j=r(86757),S=r.n(j),P=r(99676),A=r.n(P),E=r(13735),k=r.n(E),M=r(34935),T=r.n(M),_=r(37065),C=r.n(_),D=r(61994),I=r(69398),N=r(48777),B=r(9841),L=r(8147),R=r(22190),z=r(41637),U=r(82944);function $(){return($=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var F=function(t){var e=t.cx,r=t.cy,n=t.r,o=t.className,i=(0,D.Z)("recharts-dot",o);return e===+e&&r===+r&&n===+n?x.createElement("circle",$({},(0,U.L6)(t,!1),(0,z.Ym)(t),{className:i,cx:e,cy:r,r:n})):null},Z=r(73649),W=r(55284),q=r(58811),Y=r(85355),V=r(16630);function X(t){return(X="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function H(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function K(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?H(Object(r),!0).forEach(function(e){G(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):H(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function G(t,e,r){var n;return(n=function(t,e){if("object"!=X(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=X(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==X(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var J=["Webkit","Moz","O","ms"],Q=function(t,e){if(!t)return null;var r=t.replace(/(\w)/,function(t){return t.toUpperCase()}),n=J.reduce(function(t,n){return K(K({},t),{},G({},n+r,e))},{});return n[t]=e,n};function tt(t){return(tt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function te(){return(te=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tr(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tn(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tr(Object(r),!0).forEach(function(e){tc(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tr(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function to(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ts(n.key),n)}}function ti(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(ti=function(){return!!t})()}function ta(t){return(ta=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function tu(t,e){return(tu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tc(t,e,r){return(e=ts(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ts(t){var e=function(t,e){if("object"!=tt(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tt(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tt(e)?e:e+""}var tl=function(t){var e=t.data,r=t.startIndex,n=t.endIndex,o=t.x,i=t.width,a=t.travellerWidth;if(!e||!e.length)return{};var u=e.length,c=(0,W.x)().domain(A()(0,u)).range([o,o+i-a]),s=c.domain().map(function(t){return c(t)});return{isTextActive:!1,isSlideMoving:!1,isTravellerMoving:!1,isTravellerFocused:!1,startX:c(r),endX:c(n),scale:c,scaleValues:s}},tf=function(t){return t.changedTouches&&!!t.changedTouches.length},tp=function(t){var e,r;function n(t){var e,r,o;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),r=n,o=[t],r=ta(r),tc(e=function(t,e){if(e&&("object"===tt(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,ti()?Reflect.construct(r,o||[],ta(this).constructor):r.apply(this,o)),"handleDrag",function(t){e.leaveTimer&&(clearTimeout(e.leaveTimer),e.leaveTimer=null),e.state.isTravellerMoving?e.handleTravellerMove(t):e.state.isSlideMoving&&e.handleSlideDrag(t)}),tc(e,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&e.handleDrag(t.changedTouches[0])}),tc(e,"handleDragEnd",function(){e.setState({isTravellerMoving:!1,isSlideMoving:!1},function(){var t=e.props,r=t.endIndex,n=t.onDragEnd,o=t.startIndex;null==n||n({endIndex:r,startIndex:o})}),e.detachDragEndListener()}),tc(e,"handleLeaveWrapper",function(){(e.state.isTravellerMoving||e.state.isSlideMoving)&&(e.leaveTimer=window.setTimeout(e.handleDragEnd,e.props.leaveTimeOut))}),tc(e,"handleEnterSlideOrTraveller",function(){e.setState({isTextActive:!0})}),tc(e,"handleLeaveSlideOrTraveller",function(){e.setState({isTextActive:!1})}),tc(e,"handleSlideDragStart",function(t){var r=tf(t)?t.changedTouches[0]:t;e.setState({isTravellerMoving:!1,isSlideMoving:!0,slideMoveStartX:r.pageX}),e.attachDragEndListener()}),e.travellerDragStartHandlers={startX:e.handleTravellerDragStart.bind(e,"startX"),endX:e.handleTravellerDragStart.bind(e,"endX")},e.state={},e}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&tu(t,e)}(n,t),e=[{key:"componentWillUnmount",value:function(){this.leaveTimer&&(clearTimeout(this.leaveTimer),this.leaveTimer=null),this.detachDragEndListener()}},{key:"getIndex",value:function(t){var e=t.startX,r=t.endX,o=this.state.scaleValues,i=this.props,a=i.gap,u=i.data.length-1,c=n.getIndexInRange(o,Math.min(e,r)),s=n.getIndexInRange(o,Math.max(e,r));return{startIndex:c-c%a,endIndex:s===u?u:s-s%a}}},{key:"getTextOfTick",value:function(t){var e=this.props,r=e.data,n=e.tickFormatter,o=e.dataKey,i=(0,Y.F$)(r[t],o,t);return S()(n)?n(i,t):i}},{key:"attachDragEndListener",value:function(){window.addEventListener("mouseup",this.handleDragEnd,!0),window.addEventListener("touchend",this.handleDragEnd,!0),window.addEventListener("mousemove",this.handleDrag,!0)}},{key:"detachDragEndListener",value:function(){window.removeEventListener("mouseup",this.handleDragEnd,!0),window.removeEventListener("touchend",this.handleDragEnd,!0),window.removeEventListener("mousemove",this.handleDrag,!0)}},{key:"handleSlideDrag",value:function(t){var e=this.state,r=e.slideMoveStartX,n=e.startX,o=e.endX,i=this.props,a=i.x,u=i.width,c=i.travellerWidth,s=i.startIndex,l=i.endIndex,f=i.onChange,p=t.pageX-r;p>0?p=Math.min(p,a+u-c-o,a+u-c-n):p<0&&(p=Math.max(p,a-n,a-o));var h=this.getIndex({startX:n+p,endX:o+p});(h.startIndex!==s||h.endIndex!==l)&&f&&f(h),this.setState({startX:n+p,endX:o+p,slideMoveStartX:t.pageX})}},{key:"handleTravellerDragStart",value:function(t,e){var r=tf(e)?e.changedTouches[0]:e;this.setState({isSlideMoving:!1,isTravellerMoving:!0,movingTravellerId:t,brushMoveStartX:r.pageX}),this.attachDragEndListener()}},{key:"handleTravellerMove",value:function(t){var e=this.state,r=e.brushMoveStartX,n=e.movingTravellerId,o=e.endX,i=e.startX,a=this.state[n],u=this.props,c=u.x,s=u.width,l=u.travellerWidth,f=u.onChange,p=u.gap,h=u.data,d={startX:this.state.startX,endX:this.state.endX},y=t.pageX-r;y>0?y=Math.min(y,c+s-l-a):y<0&&(y=Math.max(y,c-a)),d[n]=a+y;var v=this.getIndex(d),m=v.startIndex,b=v.endIndex,g=function(){var t=h.length-1;return"startX"===n&&(o>i?m%p==0:b%p==0)||o<i&&b===t||"endX"===n&&(o>i?b%p==0:m%p==0)||o>i&&b===t};this.setState(tc(tc({},n,a+y),"brushMoveStartX",t.pageX),function(){f&&g()&&f(v)})}},{key:"handleTravellerMoveKeyboard",value:function(t,e){var r=this,n=this.state,o=n.scaleValues,i=n.startX,a=n.endX,u=this.state[e],c=o.indexOf(u);if(-1!==c){var s=c+t;if(-1!==s&&!(s>=o.length)){var l=o[s];"startX"===e&&l>=a||"endX"===e&&l<=i||this.setState(tc({},e,l),function(){r.props.onChange(r.getIndex({startX:r.state.startX,endX:r.state.endX}))})}}}},{key:"renderBackground",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,o=t.height,i=t.fill,a=t.stroke;return x.createElement("rect",{stroke:a,fill:i,x:e,y:r,width:n,height:o})}},{key:"renderPanorama",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,o=t.height,i=t.data,a=t.children,u=t.padding,c=x.Children.only(a);return c?x.cloneElement(c,{x:e,y:r,width:n,height:o,margin:u,compact:!0,data:i}):null}},{key:"renderTravellerLayer",value:function(t,e){var r,o,i=this,a=this.props,u=a.y,c=a.travellerWidth,s=a.height,l=a.traveller,f=a.ariaLabel,p=a.data,h=a.startIndex,d=a.endIndex,y=Math.max(t,this.props.x),v=tn(tn({},(0,U.L6)(this.props,!1)),{},{x:y,y:u,width:c,height:s}),m=f||"Min value: ".concat(null===(r=p[h])||void 0===r?void 0:r.name,", Max value: ").concat(null===(o=p[d])||void 0===o?void 0:o.name);return x.createElement(B.m,{tabIndex:0,role:"slider","aria-label":m,"aria-valuenow":t,className:"recharts-brush-traveller",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.travellerDragStartHandlers[e],onTouchStart:this.travellerDragStartHandlers[e],onKeyDown:function(t){["ArrowLeft","ArrowRight"].includes(t.key)&&(t.preventDefault(),t.stopPropagation(),i.handleTravellerMoveKeyboard("ArrowRight"===t.key?1:-1,e))},onFocus:function(){i.setState({isTravellerFocused:!0})},onBlur:function(){i.setState({isTravellerFocused:!1})},style:{cursor:"col-resize"}},n.renderTraveller(l,v))}},{key:"renderSlide",value:function(t,e){var r=this.props,n=r.y,o=r.height,i=r.stroke,a=r.travellerWidth;return x.createElement("rect",{className:"recharts-brush-slide",onMouseEnter:this.handleEnterSlideOrTraveller,onMouseLeave:this.handleLeaveSlideOrTraveller,onMouseDown:this.handleSlideDragStart,onTouchStart:this.handleSlideDragStart,style:{cursor:"move"},stroke:"none",fill:i,fillOpacity:.2,x:Math.min(t,e)+a,y:n,width:Math.max(Math.abs(e-t)-a,0),height:o})}},{key:"renderText",value:function(){var t=this.props,e=t.startIndex,r=t.endIndex,n=t.y,o=t.height,i=t.travellerWidth,a=t.stroke,u=this.state,c=u.startX,s=u.endX,l={pointerEvents:"none",fill:a};return x.createElement(B.m,{className:"recharts-brush-texts"},x.createElement(q.x,te({textAnchor:"end",verticalAnchor:"middle",x:Math.min(c,s)-5,y:n+o/2},l),this.getTextOfTick(e)),x.createElement(q.x,te({textAnchor:"start",verticalAnchor:"middle",x:Math.max(c,s)+i+5,y:n+o/2},l),this.getTextOfTick(r)))}},{key:"render",value:function(){var t=this.props,e=t.data,r=t.className,n=t.children,o=t.x,i=t.y,a=t.width,u=t.height,c=t.alwaysShowText,s=this.state,l=s.startX,f=s.endX,p=s.isTextActive,h=s.isSlideMoving,d=s.isTravellerMoving,y=s.isTravellerFocused;if(!e||!e.length||!(0,V.hj)(o)||!(0,V.hj)(i)||!(0,V.hj)(a)||!(0,V.hj)(u)||a<=0||u<=0)return null;var v=(0,D.Z)("recharts-brush",r),m=1===x.Children.count(n),b=Q("userSelect","none");return x.createElement(B.m,{className:v,onMouseLeave:this.handleLeaveWrapper,onTouchMove:this.handleTouchMove,style:b},this.renderBackground(),m&&this.renderPanorama(),this.renderSlide(l,f),this.renderTravellerLayer(l,"startX"),this.renderTravellerLayer(f,"endX"),(p||h||d||y||c)&&this.renderText())}}],r=[{key:"renderDefaultTraveller",value:function(t){var e=t.x,r=t.y,n=t.width,o=t.height,i=t.stroke,a=Math.floor(r+o/2)-1;return x.createElement(x.Fragment,null,x.createElement("rect",{x:e,y:r,width:n,height:o,fill:i,stroke:"none"}),x.createElement("line",{x1:e+1,y1:a,x2:e+n-1,y2:a,fill:"none",stroke:"#fff"}),x.createElement("line",{x1:e+1,y1:a+2,x2:e+n-1,y2:a+2,fill:"none",stroke:"#fff"}))}},{key:"renderTraveller",value:function(t,e){return x.isValidElement(t)?x.cloneElement(t,e):S()(t)?t(e):n.renderDefaultTraveller(e)}},{key:"getDerivedStateFromProps",value:function(t,e){var r=t.data,n=t.width,o=t.x,i=t.travellerWidth,a=t.updateId,u=t.startIndex,c=t.endIndex;if(r!==e.prevData||a!==e.prevUpdateId)return tn({prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n},r&&r.length?tl({data:r,width:n,x:o,travellerWidth:i,startIndex:u,endIndex:c}):{scale:null,scaleValues:null});if(e.scale&&(n!==e.prevWidth||o!==e.prevX||i!==e.prevTravellerWidth)){e.scale.range([o,o+n-i]);var s=e.scale.domain().map(function(t){return e.scale(t)});return{prevData:r,prevTravellerWidth:i,prevUpdateId:a,prevX:o,prevWidth:n,startX:e.scale(t.startIndex),endX:e.scale(t.endIndex),scaleValues:s}}return null}},{key:"getIndexInRange",value:function(t,e){for(var r=t.length,n=0,o=r-1;o-n>1;){var i=Math.floor((n+o)/2);t[i]>e?o=i:n=i}return e>=t[o]?o:n}}],e&&to(n.prototype,e),r&&to(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(x.PureComponent);tc(tp,"displayName","Brush"),tc(tp,"defaultProps",{height:40,travellerWidth:5,gap:1,fill:"#fff",stroke:"#666",padding:{top:1,right:1,bottom:1,left:1},leaveTimeOut:1e3,alwaysShowText:!1});var th=r(4094),td=r(38569),ty=r(26680),tv=function(t,e){var r=t.alwaysShow,n=t.ifOverflow;return r&&(n="extendDomain"),n===e},tm=r(41443),tb=r.n(tm),tg=r(32242),tx=r.n(tg),tO=r(86530);function tw(t){return(tw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tj(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tE(n.key),n)}}function tS(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tP(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tS(Object(r),!0).forEach(function(e){tA(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tS(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tA(t,e,r){return(e=tE(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function tE(t){var e=function(t,e){if("object"!=tw(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tw(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tw(e)?e:e+""}var tk=function(t,e){var r=t.x,n=t.y,o=e.x,i=e.y;return{x:Math.min(r,o),y:Math.min(n,i),width:Math.abs(o-r),height:Math.abs(i-n)}},tM=function(){var t,e;function r(t){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),this.scale=t}return t=[{key:"domain",get:function(){return this.scale.domain}},{key:"range",get:function(){return this.scale.range}},{key:"rangeMin",get:function(){return this.range()[0]}},{key:"rangeMax",get:function(){return this.range()[1]}},{key:"bandwidth",get:function(){return this.scale.bandwidth}},{key:"apply",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=e.bandAware,n=e.position;if(void 0!==t){if(n)switch(n){case"start":default:return this.scale(t);case"middle":var o=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+o;case"end":var i=this.bandwidth?this.bandwidth():0;return this.scale(t)+i}if(r){var a=this.bandwidth?this.bandwidth()/2:0;return this.scale(t)+a}return this.scale(t)}}},{key:"isInRange",value:function(t){var e=this.range(),r=e[0],n=e[e.length-1];return r<=n?t>=r&&t<=n:t>=n&&t<=r}}],e=[{key:"create",value:function(t){return new r(t)}}],t&&tj(r.prototype,t),e&&tj(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();tA(tM,"EPS",1e-4);var tT=function(t){var e=Object.keys(t).reduce(function(e,r){return tP(tP({},e),{},tA({},r,tM.create(t[r])))},{});return tP(tP({},e),{},{apply:function(t){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=r.bandAware,o=r.position;return tb()(t,function(t,r){return e[r].apply(t,{bandAware:n,position:o})})},isInRange:function(t){return tx()(t,function(t,r){return e[r].isInRange(t)})}})},t_=function(t){var e=t.width,r=t.height,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,o=(n%180+180)%180*Math.PI/180,i=Math.atan(r/e);return Math.abs(o>i&&o<Math.PI-i?r/Math.sin(o):e/Math.cos(o))},tC=r(1175);function tD(){return(tD=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function tI(t){return(tI="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function tN(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function tB(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?tN(Object(r),!0).forEach(function(e){tU(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):tN(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function tL(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(tL=function(){return!!t})()}function tR(t){return(tR=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function tz(t,e){return(tz=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function tU(t,e,r){return(e=t$(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function t$(t){var e=function(t,e){if("object"!=tI(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=tI(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==tI(e)?e:e+""}var tF=function(t){var e=t.x,r=t.y,n=t.xAxis,o=t.yAxis,i=tT({x:n.scale,y:o.scale}),a=i.apply({x:e,y:r},{bandAware:!0});return tv(t,"discard")&&!i.isInRange(a)?null:a},tZ=function(t){var e;function r(){var t,e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=tR(t),function(t,e){if(e&&("object"===tI(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,tL()?Reflect.construct(t,e||[],tR(this).constructor):t.apply(this,e))}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&tz(t,e)}(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x,n=t.y,o=t.r,i=t.alwaysShow,a=t.clipPathId,u=(0,V.P2)(e),c=(0,V.P2)(n);if((0,tC.Z)(void 0===i,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.'),!u||!c)return null;var s=tF(this.props);if(!s)return null;var l=s.x,f=s.y,p=this.props,h=p.shape,d=p.className,y=tB(tB({clipPath:tv(this.props,"hidden")?"url(#".concat(a,")"):void 0},(0,U.L6)(this.props,!0)),{},{cx:l,cy:f});return x.createElement(B.m,{className:(0,D.Z)("recharts-reference-dot",d)},r.renderDot(h,y),ty._.renderCallByParent(this.props,{x:l-o,y:f-o,width:2*o,height:2*o}))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,t$(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(x.Component);tU(tZ,"displayName","ReferenceDot"),tU(tZ,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#fff",stroke:"#ccc",fillOpacity:1,strokeWidth:1}),tU(tZ,"renderDot",function(t,e){return x.isValidElement(t)?x.cloneElement(t,e):S()(t)?t(e):x.createElement(F,tD({},e,{cx:e.cx,cy:e.cy,className:"recharts-reference-dot-dot"}))});var tW=r(33645),tq=r.n(tW);r(84173);var tY=r(50967),tV=r.n(tY)()(function(t){return{x:t.left,y:t.top,width:t.width,height:t.height}},function(t){return["l",t.left,"t",t.top,"w",t.width,"h",t.height].join("")}),tX=(0,x.createContext)(void 0),tH=(0,x.createContext)(void 0),tK=(0,x.createContext)(void 0),tG=(0,x.createContext)({}),tJ=(0,x.createContext)(void 0),tQ=(0,x.createContext)(0),t0=(0,x.createContext)(0),t1=function(t){var e=t.state,r=e.xAxisMap,n=e.yAxisMap,o=e.offset,i=t.clipPathId,a=t.children,u=t.width,c=t.height,s=tV(o);return x.createElement(tX.Provider,{value:r},x.createElement(tH.Provider,{value:n},x.createElement(tG.Provider,{value:o},x.createElement(tK.Provider,{value:s},x.createElement(tJ.Provider,{value:i},x.createElement(tQ.Provider,{value:c},x.createElement(t0.Provider,{value:u},a)))))))},t2=function(t){var e=(0,x.useContext)(tX);null!=e||(0,I.Z)(!1);var r=e[t];return null!=r||(0,I.Z)(!1),r},t5=function(t){var e=(0,x.useContext)(tH);null!=e||(0,I.Z)(!1);var r=e[t];return null!=r||(0,I.Z)(!1),r},t3=function(){return(0,x.useContext)(t0)},t6=function(){return(0,x.useContext)(tQ)};function t7(t){return(t7="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function t4(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(t4=function(){return!!t})()}function t8(t){return(t8=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function t9(t,e){return(t9=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function et(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ee(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?et(Object(r),!0).forEach(function(e){er(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):et(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function er(t,e,r){return(e=en(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function en(t){var e=function(t,e){if("object"!=t7(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=t7(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==t7(e)?e:e+""}function eo(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function ei(){return(ei=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var ea=function(t,e,r,n,o,i,a,u,c){var s=o.x,l=o.y,f=o.width,p=o.height;if(r){var h=c.y,d=t.y.apply(h,{position:i});if(tv(c,"discard")&&!t.y.isInRange(d))return null;var y=[{x:s+f,y:d},{x:s,y:d}];return"left"===u?y.reverse():y}if(e){var v=c.x,m=t.x.apply(v,{position:i});if(tv(c,"discard")&&!t.x.isInRange(m))return null;var b=[{x:m,y:l+p},{x:m,y:l}];return"top"===a?b.reverse():b}if(n){var g=c.segment.map(function(e){return t.apply(e,{position:i})});return tv(c,"discard")&&tq()(g,function(e){return!t.isInRange(e)})?null:g}return null};function eu(t){var e,r,n,o=t.x,i=t.y,a=t.segment,u=t.xAxisId,c=t.yAxisId,s=t.shape,l=t.className,f=t.alwaysShow,p=(0,x.useContext)(tJ),h=t2(u),d=t5(c),y=(0,x.useContext)(tK);if(!p||!y)return null;(0,tC.Z)(void 0===f,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var v=ea(tT({x:h.scale,y:d.scale}),(0,V.P2)(o),(0,V.P2)(i),a&&2===a.length,y,t.position,h.orientation,d.orientation,t);if(!v)return null;var m=function(t){if(Array.isArray(t))return t}(v)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,s=!1;try{for(i=(r=r.call(t)).next;!(c=(n=i.call(r)).done)&&(u.push(n.value),2!==u.length);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(v,2)||function(t,e){if(t){if("string"==typeof t)return eo(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return eo(t,2)}}(v,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),b=m[0],g=b.x,O=b.y,w=m[1],j=w.x,P=w.y,A=ee(ee({clipPath:tv(t,"hidden")?"url(#".concat(p,")"):void 0},(0,U.L6)(t,!0)),{},{x1:g,y1:O,x2:j,y2:P});return x.createElement(B.m,{className:(0,D.Z)("recharts-reference-line",l)},(e=s,r=A,x.isValidElement(e)?x.cloneElement(e,r):S()(e)?e(r):x.createElement("line",ei({},r,{className:"recharts-reference-line-line"}))),ty._.renderCallByParent(t,tk({x:(n={x1:g,y1:O,x2:j,y2:P}).x1,y:n.y1},{x:n.x2,y:n.y2})))}var ec=function(t){var e;function r(){var t,e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=t8(t),function(t,e){if(e&&("object"===t7(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,t4()?Reflect.construct(t,e||[],t8(this).constructor):t.apply(this,e))}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&t9(t,e)}(r,t),e=[{key:"render",value:function(){return x.createElement(eu,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,en(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(x.Component);function es(){return(es=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function el(t){return(el="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ef(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function ep(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ef(Object(r),!0).forEach(function(e){ev(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ef(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function eh(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(eh=function(){return!!t})()}function ed(t){return(ed=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function ey(t,e){return(ey=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function ev(t,e,r){return(e=em(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function em(t){var e=function(t,e){if("object"!=el(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=el(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==el(e)?e:e+""}er(ec,"displayName","ReferenceLine"),er(ec,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,fill:"none",stroke:"#ccc",fillOpacity:1,strokeWidth:1,position:"middle"});var eb=function(t,e,r,n,o){var i=o.x1,a=o.x2,u=o.y1,c=o.y2,s=o.xAxis,l=o.yAxis;if(!s||!l)return null;var f=tT({x:s.scale,y:l.scale}),p={x:t?f.x.apply(i,{position:"start"}):f.x.rangeMin,y:r?f.y.apply(u,{position:"start"}):f.y.rangeMin},h={x:e?f.x.apply(a,{position:"end"}):f.x.rangeMax,y:n?f.y.apply(c,{position:"end"}):f.y.rangeMax};return!tv(o,"discard")||f.isInRange(p)&&f.isInRange(h)?tk(p,h):null},eg=function(t){var e;function r(){var t,e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=ed(t),function(t,e){if(e&&("object"===el(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,eh()?Reflect.construct(t,e||[],ed(this).constructor):t.apply(this,e))}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&ey(t,e)}(r,t),e=[{key:"render",value:function(){var t=this.props,e=t.x1,n=t.x2,o=t.y1,i=t.y2,a=t.className,u=t.alwaysShow,c=t.clipPathId;(0,tC.Z)(void 0===u,'The alwaysShow prop is deprecated. Please use ifOverflow="extendDomain" instead.');var s=(0,V.P2)(e),l=(0,V.P2)(n),f=(0,V.P2)(o),p=(0,V.P2)(i),h=this.props.shape;if(!s&&!l&&!f&&!p&&!h)return null;var d=eb(s,l,f,p,this.props);if(!d&&!h)return null;var y=tv(this.props,"hidden")?"url(#".concat(c,")"):void 0;return x.createElement(B.m,{className:(0,D.Z)("recharts-reference-area",a)},r.renderRect(h,ep(ep({clipPath:y},(0,U.L6)(this.props,!0)),d)),ty._.renderCallByParent(this.props,d))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,em(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(x.Component);function ex(t){return function(t){if(Array.isArray(t))return eO(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return eO(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return eO(t,void 0)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function eO(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}ev(eg,"displayName","ReferenceArea"),ev(eg,"defaultProps",{isFront:!1,ifOverflow:"discard",xAxisId:0,yAxisId:0,r:10,fill:"#ccc",fillOpacity:.5,stroke:"none",strokeWidth:1}),ev(eg,"renderRect",function(t,e){return x.isValidElement(t)?x.cloneElement(t,e):S()(t)?t(e):x.createElement(Z.A,es({},e,{className:"recharts-reference-area-rect"}))});var ew=function(t,e,r,n,o){var i=(0,U.NN)(t,ec),a=(0,U.NN)(t,tZ),u=[].concat(ex(i),ex(a)),c=(0,U.NN)(t,eg),s="".concat(n,"Id"),l=n[0],f=e;if(u.length&&(f=u.reduce(function(t,e){if(e.props[s]===r&&tv(e.props,"extendDomain")&&(0,V.hj)(e.props[l])){var n=e.props[l];return[Math.min(t[0],n),Math.max(t[1],n)]}return t},f)),c.length){var p="".concat(l,"1"),h="".concat(l,"2");f=c.reduce(function(t,e){if(e.props[s]===r&&tv(e.props,"extendDomain")&&(0,V.hj)(e.props[p])&&(0,V.hj)(e.props[h])){var n=e.props[p],o=e.props[h];return[Math.min(t[0],n,o),Math.max(t[1],n,o)]}return t},f)}return o&&o.length&&(f=o.reduce(function(t,e){return(0,V.hj)(e)?[Math.min(t[0],e),Math.max(t[1],e)]:t},f)),f},ej=r(39206),eS=r(46485),eP=r(77625),eA=new(r.n(eP)()),eE="recharts.syncMouseEvents";function ek(t){return(ek="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function eM(t,e,r){return(e=eT(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function eT(t){var e=function(t,e){if("object"!=ek(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ek(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ek(e)?e:e+""}var e_=(n=function t(){(function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")})(this,t),eM(this,"activeIndex",0),eM(this,"coordinateList",[]),eM(this,"layout","horizontal")},o=[{key:"setDetails",value:function(t){var e,r=t.coordinateList,n=void 0===r?null:r,o=t.container,i=void 0===o?null:o,a=t.layout,u=void 0===a?null:a,c=t.offset,s=void 0===c?null:c,l=t.mouseHandlerCallback,f=void 0===l?null:l;this.coordinateList=null!==(e=null!=n?n:this.coordinateList)&&void 0!==e?e:[],this.container=null!=i?i:this.container,this.layout=null!=u?u:this.layout,this.offset=null!=s?s:this.offset,this.mouseHandlerCallback=null!=f?f:this.mouseHandlerCallback,this.activeIndex=Math.min(Math.max(this.activeIndex,0),this.coordinateList.length-1)}},{key:"focus",value:function(){this.spoofMouse()}},{key:"keyboardEvent",value:function(t){if(0!==this.coordinateList.length)switch(t.key){case"ArrowRight":if("horizontal"!==this.layout)return;this.activeIndex=Math.min(this.activeIndex+1,this.coordinateList.length-1),this.spoofMouse();break;case"ArrowLeft":if("horizontal"!==this.layout)return;this.activeIndex=Math.max(this.activeIndex-1,0),this.spoofMouse()}}},{key:"setIndex",value:function(t){this.activeIndex=t}},{key:"spoofMouse",value:function(){if("horizontal"===this.layout&&0!==this.coordinateList.length){var t,e,r=this.container.getBoundingClientRect(),n=r.x,o=r.y,i=r.height,a=this.coordinateList[this.activeIndex].coordinate,u=(null===(t=window)||void 0===t?void 0:t.scrollX)||0,c=(null===(e=window)||void 0===e?void 0:e.scrollY)||0,s=o+this.offset.top+i/2+c;this.mouseHandlerCallback({pageX:n+a+u,pageY:s})}}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,eT(n.key),n)}}(n.prototype,o),Object.defineProperty(n,"prototype",{writable:!1}),n),eC=r(11638);function eD(){}function eI(t,e,r){t._context.bezierCurveTo((2*t._x0+t._x1)/3,(2*t._y0+t._y1)/3,(t._x0+2*t._x1)/3,(t._y0+2*t._y1)/3,(t._x0+4*t._x1+e)/6,(t._y0+4*t._y1+r)/6)}function eN(t){this._context=t}function eB(t){this._context=t}function eL(t){this._context=t}eN.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){switch(this._point){case 3:eI(this,this._x1,this._y1);case 2:this._context.lineTo(this._x1,this._y1)}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,this._context.lineTo((5*this._x0+this._x1)/6,(5*this._y0+this._y1)/6);default:eI(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},eB.prototype={areaStart:eD,areaEnd:eD,lineStart:function(){this._x0=this._x1=this._x2=this._x3=this._x4=this._y0=this._y1=this._y2=this._y3=this._y4=NaN,this._point=0},lineEnd:function(){switch(this._point){case 1:this._context.moveTo(this._x2,this._y2),this._context.closePath();break;case 2:this._context.moveTo((this._x2+2*this._x3)/3,(this._y2+2*this._y3)/3),this._context.lineTo((this._x3+2*this._x2)/3,(this._y3+2*this._y2)/3),this._context.closePath();break;case 3:this.point(this._x2,this._y2),this.point(this._x3,this._y3),this.point(this._x4,this._y4)}},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._x2=t,this._y2=e;break;case 1:this._point=2,this._x3=t,this._y3=e;break;case 2:this._point=3,this._x4=t,this._y4=e,this._context.moveTo((this._x0+4*this._x1+t)/6,(this._y0+4*this._y1+e)/6);break;default:eI(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}},eL.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=NaN,this._point=0},lineEnd:function(){(this._line||0!==this._line&&3===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1;break;case 1:this._point=2;break;case 2:this._point=3;var r=(this._x0+4*this._x1+t)/6,n=(this._y0+4*this._y1+e)/6;this._line?this._context.lineTo(r,n):this._context.moveTo(r,n);break;case 3:this._point=4;default:eI(this,t,e)}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e}};class eR{constructor(t,e){this._context=t,this._x=e}areaStart(){this._line=0}areaEnd(){this._line=NaN}lineStart(){this._point=0}lineEnd(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line}point(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._x?this._context.bezierCurveTo(this._x0=(this._x0+t)/2,this._y0,this._x0,e,t,e):this._context.bezierCurveTo(this._x0,this._y0=(this._y0+e)/2,t,this._y0,t,e)}this._x0=t,this._y0=e}}function ez(t){this._context=t}function eU(t){this._context=t}function e$(t){return new eU(t)}function eF(t,e,r){var n=t._x1-t._x0,o=e-t._x1,i=(t._y1-t._y0)/(n||o<0&&-0),a=(r-t._y1)/(o||n<0&&-0);return((i<0?-1:1)+(a<0?-1:1))*Math.min(Math.abs(i),Math.abs(a),.5*Math.abs((i*o+a*n)/(n+o)))||0}function eZ(t,e){var r=t._x1-t._x0;return r?(3*(t._y1-t._y0)/r-e)/2:e}function eW(t,e,r){var n=t._x0,o=t._y0,i=t._x1,a=t._y1,u=(i-n)/3;t._context.bezierCurveTo(n+u,o+u*e,i-u,a-u*r,i,a)}function eq(t){this._context=t}function eY(t){this._context=new eV(t)}function eV(t){this._context=t}function eX(t){this._context=t}function eH(t){var e,r,n=t.length-1,o=Array(n),i=Array(n),a=Array(n);for(o[0]=0,i[0]=2,a[0]=t[0]+2*t[1],e=1;e<n-1;++e)o[e]=1,i[e]=4,a[e]=4*t[e]+2*t[e+1];for(o[n-1]=2,i[n-1]=7,a[n-1]=8*t[n-1]+t[n],e=1;e<n;++e)r=o[e]/i[e-1],i[e]-=r,a[e]-=r*a[e-1];for(o[n-1]=a[n-1]/i[n-1],e=n-2;e>=0;--e)o[e]=(a[e]-o[e+1])/i[e];for(e=0,i[n-1]=(t[n]+o[n-1])/2;e<n-1;++e)i[e]=2*t[e+1]-o[e+1];return[o,i]}function eK(t,e){this._context=t,this._t=e}ez.prototype={areaStart:eD,areaEnd:eD,lineStart:function(){this._point=0},lineEnd:function(){this._point&&this._context.closePath()},point:function(t,e){t=+t,e=+e,this._point?this._context.lineTo(t,e):(this._point=1,this._context.moveTo(t,e))}},eU.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._point=0},lineEnd:function(){(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:this._context.lineTo(t,e)}}},eq.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x0=this._x1=this._y0=this._y1=this._t0=NaN,this._point=0},lineEnd:function(){switch(this._point){case 2:this._context.lineTo(this._x1,this._y1);break;case 3:eW(this,this._t0,eZ(this,this._t0))}(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line=1-this._line},point:function(t,e){var r=NaN;if(e=+e,(t=+t)!==this._x1||e!==this._y1){switch(this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;break;case 2:this._point=3,eW(this,eZ(this,r=eF(this,t,e)),r);break;default:eW(this,this._t0,r=eF(this,t,e))}this._x0=this._x1,this._x1=t,this._y0=this._y1,this._y1=e,this._t0=r}}},(eY.prototype=Object.create(eq.prototype)).point=function(t,e){eq.prototype.point.call(this,e,t)},eV.prototype={moveTo:function(t,e){this._context.moveTo(e,t)},closePath:function(){this._context.closePath()},lineTo:function(t,e){this._context.lineTo(e,t)},bezierCurveTo:function(t,e,r,n,o,i){this._context.bezierCurveTo(e,t,n,r,i,o)}},eX.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=[],this._y=[]},lineEnd:function(){var t=this._x,e=this._y,r=t.length;if(r){if(this._line?this._context.lineTo(t[0],e[0]):this._context.moveTo(t[0],e[0]),2===r)this._context.lineTo(t[1],e[1]);else for(var n=eH(t),o=eH(e),i=0,a=1;a<r;++i,++a)this._context.bezierCurveTo(n[0][i],o[0][i],n[1][i],o[1][i],t[a],e[a])}(this._line||0!==this._line&&1===r)&&this._context.closePath(),this._line=1-this._line,this._x=this._y=null},point:function(t,e){this._x.push(+t),this._y.push(+e)}},eK.prototype={areaStart:function(){this._line=0},areaEnd:function(){this._line=NaN},lineStart:function(){this._x=this._y=NaN,this._point=0},lineEnd:function(){0<this._t&&this._t<1&&2===this._point&&this._context.lineTo(this._x,this._y),(this._line||0!==this._line&&1===this._point)&&this._context.closePath(),this._line>=0&&(this._t=1-this._t,this._line=1-this._line)},point:function(t,e){switch(t=+t,e=+e,this._point){case 0:this._point=1,this._line?this._context.lineTo(t,e):this._context.moveTo(t,e);break;case 1:this._point=2;default:if(this._t<=0)this._context.lineTo(this._x,e),this._context.lineTo(t,e);else{var r=this._x*(1-this._t)+t*this._t;this._context.lineTo(r,this._y),this._context.lineTo(r,e)}}this._x=t,this._y=e}};var eG=r(22516),eJ=r(76115),eQ=r(67790);function e0(t){return t[0]}function e1(t){return t[1]}function e2(t,e){var r=(0,eJ.Z)(!0),n=null,o=e$,i=null,a=(0,eQ.d)(u);function u(u){var c,s,l,f=(u=(0,eG.Z)(u)).length,p=!1;for(null==n&&(i=o(l=a())),c=0;c<=f;++c)!(c<f&&r(s=u[c],c,u))===p&&((p=!p)?i.lineStart():i.lineEnd()),p&&i.point(+t(s,c,u),+e(s,c,u));if(l)return i=null,l+""||null}return t="function"==typeof t?t:void 0===t?e0:(0,eJ.Z)(t),e="function"==typeof e?e:void 0===e?e1:(0,eJ.Z)(e),u.x=function(e){return arguments.length?(t="function"==typeof e?e:(0,eJ.Z)(+e),u):t},u.y=function(t){return arguments.length?(e="function"==typeof t?t:(0,eJ.Z)(+t),u):e},u.defined=function(t){return arguments.length?(r="function"==typeof t?t:(0,eJ.Z)(!!t),u):r},u.curve=function(t){return arguments.length?(o=t,null!=n&&(i=o(n)),u):o},u.context=function(t){return arguments.length?(null==t?n=i=null:i=o(n=t),u):n},u}function e5(t,e,r){var n=null,o=(0,eJ.Z)(!0),i=null,a=e$,u=null,c=(0,eQ.d)(s);function s(s){var l,f,p,h,d,y=(s=(0,eG.Z)(s)).length,v=!1,m=Array(y),b=Array(y);for(null==i&&(u=a(d=c())),l=0;l<=y;++l){if(!(l<y&&o(h=s[l],l,s))===v){if(v=!v)f=l,u.areaStart(),u.lineStart();else{for(u.lineEnd(),u.lineStart(),p=l-1;p>=f;--p)u.point(m[p],b[p]);u.lineEnd(),u.areaEnd()}}v&&(m[l]=+t(h,l,s),b[l]=+e(h,l,s),u.point(n?+n(h,l,s):m[l],r?+r(h,l,s):b[l]))}if(d)return u=null,d+""||null}function l(){return e2().defined(o).curve(a).context(i)}return t="function"==typeof t?t:void 0===t?e0:(0,eJ.Z)(+t),e="function"==typeof e?e:void 0===e?(0,eJ.Z)(0):(0,eJ.Z)(+e),r="function"==typeof r?r:void 0===r?e1:(0,eJ.Z)(+r),s.x=function(e){return arguments.length?(t="function"==typeof e?e:(0,eJ.Z)(+e),n=null,s):t},s.x0=function(e){return arguments.length?(t="function"==typeof e?e:(0,eJ.Z)(+e),s):t},s.x1=function(t){return arguments.length?(n=null==t?null:"function"==typeof t?t:(0,eJ.Z)(+t),s):n},s.y=function(t){return arguments.length?(e="function"==typeof t?t:(0,eJ.Z)(+t),r=null,s):e},s.y0=function(t){return arguments.length?(e="function"==typeof t?t:(0,eJ.Z)(+t),s):e},s.y1=function(t){return arguments.length?(r=null==t?null:"function"==typeof t?t:(0,eJ.Z)(+t),s):r},s.lineX0=s.lineY0=function(){return l().x(t).y(e)},s.lineY1=function(){return l().x(t).y(r)},s.lineX1=function(){return l().x(n).y(e)},s.defined=function(t){return arguments.length?(o="function"==typeof t?t:(0,eJ.Z)(!!t),s):o},s.curve=function(t){return arguments.length?(a=t,null!=i&&(u=a(i)),s):a},s.context=function(t){return arguments.length?(null==t?i=u=null:u=a(i=t),s):i},s}var e3=r(75551),e6=r.n(e3);function e7(t){return(e7="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function e4(){return(e4=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function e8(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function e9(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?e8(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=e7(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=e7(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==e7(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):e8(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var rt={curveBasisClosed:function(t){return new eB(t)},curveBasisOpen:function(t){return new eL(t)},curveBasis:function(t){return new eN(t)},curveBumpX:function(t){return new eR(t,!0)},curveBumpY:function(t){return new eR(t,!1)},curveLinearClosed:function(t){return new ez(t)},curveLinear:e$,curveMonotoneX:function(t){return new eq(t)},curveMonotoneY:function(t){return new eY(t)},curveNatural:function(t){return new eX(t)},curveStep:function(t){return new eK(t,.5)},curveStepAfter:function(t){return new eK(t,1)},curveStepBefore:function(t){return new eK(t,0)}},re=function(t){return t.x===+t.x&&t.y===+t.y},rr=function(t){return t.x},rn=function(t){return t.y},ro=function(t,e){if(S()(t))return t;var r="curve".concat(e6()(t));return("curveMonotone"===r||"curveBump"===r)&&e?rt["".concat(r).concat("vertical"===e?"Y":"X")]:rt[r]||e$},ri=function(t){var e,r=t.type,n=t.points,o=void 0===n?[]:n,i=t.baseLine,a=t.layout,u=t.connectNulls,c=void 0!==u&&u,s=ro(void 0===r?"linear":r,a),l=c?o.filter(function(t){return re(t)}):o;if(Array.isArray(i)){var f=c?i.filter(function(t){return re(t)}):i,p=l.map(function(t,e){return e9(e9({},t),{},{base:f[e]})});return(e="vertical"===a?e5().y(rn).x1(rr).x0(function(t){return t.base.x}):e5().x(rr).y1(rn).y0(function(t){return t.base.y})).defined(re).curve(s),e(p)}return(e="vertical"===a&&(0,V.hj)(i)?e5().y(rn).x1(rr).x0(i):(0,V.hj)(i)?e5().x(rr).y1(rn).y0(i):e2().x(rr).y(rn)).defined(re).curve(s),e(l)},ra=function(t){var e=t.className,r=t.points,n=t.path,o=t.pathRef;if((!r||!r.length)&&!n)return null;var i=r&&r.length?ri(t):n;return x.createElement("path",e4({},(0,U.L6)(t,!1),(0,z.Ym)(t),{className:(0,D.Z)("recharts-curve",e),d:i,ref:o}))};function ru(t){return(ru="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var rc=["x","y","top","left","width","height","className"];function rs(){return(rs=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function rl(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}var rf=function(t){var e=t.x,r=void 0===e?0:e,n=t.y,o=void 0===n?0:n,i=t.top,a=void 0===i?0:i,u=t.left,c=void 0===u?0:u,s=t.width,l=void 0===s?0:s,f=t.height,p=void 0===f?0:f,h=t.className,d=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?rl(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=ru(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=ru(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==ru(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rl(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({x:r,y:o,top:a,left:c,width:l,height:p},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,rc));return(0,V.hj)(r)&&(0,V.hj)(o)&&(0,V.hj)(l)&&(0,V.hj)(p)&&(0,V.hj)(a)&&(0,V.hj)(c)?x.createElement("path",rs({},(0,U.L6)(d,!0),{className:(0,D.Z)("recharts-cross",h),d:"M".concat(r,",").concat(a,"v").concat(p,"M").concat(c,",").concat(o,"h").concat(l)})):null};function rp(t){var e=t.cx,r=t.cy,n=t.radius,o=t.startAngle,i=t.endAngle;return{points:[(0,ej.op)(e,r,n,o),(0,ej.op)(e,r,n,i)],cx:e,cy:r,radius:n,startAngle:o,endAngle:i}}var rh=r(60474);function rd(t){return(rd="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ry(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function rv(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ry(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=rd(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=rd(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==rd(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ry(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function rm(t){var e,r,n,o,i=t.element,a=t.tooltipEventType,u=t.isActive,c=t.activeCoordinate,s=t.activePayload,l=t.offset,f=t.activeTooltipIndex,p=t.tooltipAxisBandSize,h=t.layout,d=t.chartName,y=null!==(r=i.props.cursor)&&void 0!==r?r:null===(n=i.type.defaultProps)||void 0===n?void 0:n.cursor;if(!i||!y||!u||!c||"ScatterChart"!==d&&"axis"!==a)return null;var v=ra;if("ScatterChart"===d)o=c,v=rf;else if("BarChart"===d)e=p/2,o={stroke:"none",fill:"#ccc",x:"horizontal"===h?c.x-e:l.left+.5,y:"horizontal"===h?l.top+.5:c.y-e,width:"horizontal"===h?p:l.width-1,height:"horizontal"===h?l.height-1:p},v=Z.A;else if("radial"===h){var m=rp(c),b=m.cx,g=m.cy,O=m.radius;o={cx:b,cy:g,startAngle:m.startAngle,endAngle:m.endAngle,innerRadius:O,outerRadius:O},v=rh.L}else o={points:function(t,e,r){var n,o,i,a;if("horizontal"===t)i=n=e.x,o=r.top,a=r.top+r.height;else if("vertical"===t)a=o=e.y,n=r.left,i=r.left+r.width;else if(null!=e.cx&&null!=e.cy){if("centric"!==t)return rp(e);var u=e.cx,c=e.cy,s=e.innerRadius,l=e.outerRadius,f=e.angle,p=(0,ej.op)(u,c,s,f),h=(0,ej.op)(u,c,l,f);n=p.x,o=p.y,i=h.x,a=h.y}return[{x:n,y:o},{x:i,y:a}]}(h,c,l)},v=ra;var w=rv(rv(rv(rv({stroke:"#ccc",pointerEvents:"none"},l),o),(0,U.L6)(y,!1)),{},{payload:s,payloadIndex:f,className:(0,D.Z)("recharts-tooltip-cursor",y.className)});return(0,x.isValidElement)(y)?(0,x.cloneElement)(y,w):(0,x.createElement)(v,w)}var rb=["item"],rg=["children","className","width","height","style","compact","title","desc"];function rx(t){return(rx="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function rO(){return(rO=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function rw(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(t,e)||rk(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function rj(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function rS(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(rS=function(){return!!t})()}function rP(t){return(rP=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function rA(t,e){return(rA=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function rE(t){return function(t){if(Array.isArray(t))return rM(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||rk(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function rk(t,e){if(t){if("string"==typeof t)return rM(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return rM(t,e)}}function rM(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function rT(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function r_(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?rT(Object(r),!0).forEach(function(e){rC(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):rT(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function rC(t,e,r){return(e=rD(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function rD(t){var e=function(t,e){if("object"!=rx(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=rx(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==rx(e)?e:e+""}var rI={xAxis:["bottom","top"],yAxis:["left","right"]},rN={width:"100%",height:"100%"},rB={x:0,y:0};function rL(t){return t}var rR=function(t,e,r,n){var o=e.find(function(t){return t&&t.index===r});if(o){if("horizontal"===t)return{x:o.coordinate,y:n.y};if("vertical"===t)return{x:n.x,y:o.coordinate};if("centric"===t){var i=o.coordinate,a=n.radius;return r_(r_(r_({},n),(0,ej.op)(n.cx,n.cy,a,i)),{},{angle:i,radius:a})}var u=o.coordinate,c=n.angle;return r_(r_(r_({},n),(0,ej.op)(n.cx,n.cy,u,c)),{},{angle:c,radius:u})}return rB},rz=function(t,e){var r=e.graphicalItems,n=e.dataStartIndex,o=e.dataEndIndex,i=(null!=r?r:[]).reduce(function(t,e){var r=e.props.data;return r&&r.length?[].concat(rE(t),rE(r)):t},[]);return i.length>0?i:t&&t.length&&(0,V.hj)(n)&&(0,V.hj)(o)?t.slice(n,o+1):[]};function rU(t){return"number"===t?[0,"auto"]:void 0}var r$=function(t,e,r,n){var o=t.graphicalItems,i=t.tooltipAxis,a=rz(e,t);return r<0||!o||!o.length||r>=a.length?null:o.reduce(function(o,u){var c,s,l=null!==(c=u.props.data)&&void 0!==c?c:e;if(l&&t.dataStartIndex+t.dataEndIndex!==0&&t.dataEndIndex-t.dataStartIndex>=r&&(l=l.slice(t.dataStartIndex,t.dataEndIndex+1)),i.dataKey&&!i.allowDuplicatedCategory){var f=void 0===l?a:l;s=(0,V.Ap)(f,i.dataKey,n)}else s=l&&l[r]||a[r];return s?[].concat(rE(o),[(0,Y.Qo)(u,s)]):o},[])},rF=function(t,e,r,n){var o=n||{x:t.chartX,y:t.chartY},i="horizontal"===r?o.x:"vertical"===r?o.y:"centric"===r?o.angle:o.radius,a=t.orderedTooltipTicks,u=t.tooltipAxis,c=t.tooltipTicks,s=(0,Y.VO)(i,a,c,u);if(s>=0&&c){var l=c[s]&&c[s].value,f=r$(t,e,s,l),p=rR(r,a,s,o);return{activeTooltipIndex:s,activeLabel:l,activePayload:f,activeCoordinate:p}}return null},rZ=function(t,e){var r=e.axes,n=e.graphicalItems,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,u=e.dataStartIndex,c=e.dataEndIndex,s=t.layout,l=t.children,f=t.stackOffset,p=(0,Y.NA)(s,o);return r.reduce(function(e,r){var h=void 0!==r.type.defaultProps?r_(r_({},r.type.defaultProps),r.props):r.props,d=h.type,y=h.dataKey,v=h.allowDataOverflow,m=h.allowDuplicatedCategory,b=h.scale,g=h.ticks,x=h.includeHidden,O=h[i];if(e[O])return e;var j=rz(t.data,{graphicalItems:n.filter(function(t){var e;return(i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i])===O}),dataStartIndex:u,dataEndIndex:c}),S=j.length;(function(t,e,r){if("number"===r&&!0===e&&Array.isArray(t)){var n=null==t?void 0:t[0],o=null==t?void 0:t[1];if(n&&o&&(0,V.hj)(n)&&(0,V.hj)(o))return!0}return!1})(h.domain,v,d)&&(k=(0,Y.LG)(h.domain,null,v),p&&("number"===d||"auto"!==b)&&(T=(0,Y.gF)(j,y,"category")));var P=rU(d);if(!k||0===k.length){var E,k,M,T,_,C=null!==(_=h.domain)&&void 0!==_?_:P;if(y){if(k=(0,Y.gF)(j,y,d),"category"===d&&p){var D=(0,V.bv)(k);m&&D?(M=k,k=A()(0,S)):m||(k=(0,Y.ko)(C,k,r).reduce(function(t,e){return t.indexOf(e)>=0?t:[].concat(rE(t),[e])},[]))}else if("category"===d)k=m?k.filter(function(t){return""!==t&&!w()(t)}):(0,Y.ko)(C,k,r).reduce(function(t,e){return t.indexOf(e)>=0||""===e||w()(e)?t:[].concat(rE(t),[e])},[]);else if("number"===d){var I=(0,Y.ZI)(j,n.filter(function(t){var e,r,n=i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i],o="hide"in t.props?t.props.hide:null===(r=t.type.defaultProps)||void 0===r?void 0:r.hide;return n===O&&(x||!o)}),y,o,s);I&&(k=I)}p&&("number"===d||"auto"!==b)&&(T=(0,Y.gF)(j,y,"category"))}else k=p?A()(0,S):a&&a[O]&&a[O].hasStack&&"number"===d?"expand"===f?[0,1]:(0,Y.EB)(a[O].stackGroups,u,c):(0,Y.s6)(j,n.filter(function(t){var e=i in t.props?t.props[i]:t.type.defaultProps[i],r="hide"in t.props?t.props.hide:t.type.defaultProps.hide;return e===O&&(x||!r)}),d,s,!0);"number"===d?(k=ew(l,k,O,o,g),C&&(k=(0,Y.LG)(C,k,v))):"category"===d&&C&&k.every(function(t){return C.indexOf(t)>=0})&&(k=C)}return r_(r_({},e),{},rC({},O,r_(r_({},h),{},{axisType:o,domain:k,categoricalDomain:T,duplicateDomain:M,originalDomain:null!==(E=h.domain)&&void 0!==E?E:P,isCategorical:p,layout:s})))},{})},rW=function(t,e){var r=e.graphicalItems,n=e.Axis,o=e.axisType,i=e.axisIdKey,a=e.stackGroups,u=e.dataStartIndex,c=e.dataEndIndex,s=t.layout,l=t.children,f=rz(t.data,{graphicalItems:r,dataStartIndex:u,dataEndIndex:c}),p=f.length,h=(0,Y.NA)(s,o),d=-1;return r.reduce(function(t,e){var y,v=(void 0!==e.type.defaultProps?r_(r_({},e.type.defaultProps),e.props):e.props)[i],m=rU("number");return t[v]?t:(d++,y=h?A()(0,p):a&&a[v]&&a[v].hasStack?ew(l,y=(0,Y.EB)(a[v].stackGroups,u,c),v,o):ew(l,y=(0,Y.LG)(m,(0,Y.s6)(f,r.filter(function(t){var e,r,n=i in t.props?t.props[i]:null===(e=t.type.defaultProps)||void 0===e?void 0:e[i],o="hide"in t.props?t.props.hide:null===(r=t.type.defaultProps)||void 0===r?void 0:r.hide;return n===v&&!o}),"number",s),n.defaultProps.allowDataOverflow),v,o),r_(r_({},t),{},rC({},v,r_(r_({axisType:o},n.defaultProps),{},{hide:!0,orientation:k()(rI,"".concat(o,".").concat(d%2),null),domain:y,originalDomain:m,isCategorical:h,layout:s}))))},{})},rq=function(t,e){var r=e.axisType,n=void 0===r?"xAxis":r,o=e.AxisComp,i=e.graphicalItems,a=e.stackGroups,u=e.dataStartIndex,c=e.dataEndIndex,s=t.children,l="".concat(n,"Id"),f=(0,U.NN)(s,o),p={};return f&&f.length?p=rZ(t,{axes:f,graphicalItems:i,axisType:n,axisIdKey:l,stackGroups:a,dataStartIndex:u,dataEndIndex:c}):i&&i.length&&(p=rW(t,{Axis:o,graphicalItems:i,axisType:n,axisIdKey:l,stackGroups:a,dataStartIndex:u,dataEndIndex:c})),p},rY=function(t){var e=(0,V.Kt)(t),r=(0,Y.uY)(e,!1,!0);return{tooltipTicks:r,orderedTooltipTicks:T()(r,function(t){return t.coordinate}),tooltipAxis:e,tooltipAxisBandSize:(0,Y.zT)(e,r)}},rV=function(t){var e=t.children,r=t.defaultShowTooltip,n=(0,U.sP)(e,tp),o=0,i=0;return t.data&&0!==t.data.length&&(i=t.data.length-1),n&&n.props&&(n.props.startIndex>=0&&(o=n.props.startIndex),n.props.endIndex>=0&&(i=n.props.endIndex)),{chartX:0,chartY:0,dataStartIndex:o,dataEndIndex:i,activeTooltipIndex:-1,isTooltipActive:!!r}},rX=function(t){return"horizontal"===t?{numericAxisName:"yAxis",cateAxisName:"xAxis"}:"vertical"===t?{numericAxisName:"xAxis",cateAxisName:"yAxis"}:"centric"===t?{numericAxisName:"radiusAxis",cateAxisName:"angleAxis"}:{numericAxisName:"angleAxis",cateAxisName:"radiusAxis"}},rH=function(t,e){var r=t.props,n=t.graphicalItems,o=t.xAxisMap,i=void 0===o?{}:o,a=t.yAxisMap,u=void 0===a?{}:a,c=r.width,s=r.height,l=r.children,f=r.margin||{},p=(0,U.sP)(l,tp),h=(0,U.sP)(l,R.D),d=Object.keys(u).reduce(function(t,e){var r=u[e],n=r.orientation;return r.mirror||r.hide?t:r_(r_({},t),{},rC({},n,t[n]+r.width))},{left:f.left||0,right:f.right||0}),y=Object.keys(i).reduce(function(t,e){var r=i[e],n=r.orientation;return r.mirror||r.hide?t:r_(r_({},t),{},rC({},n,k()(t,"".concat(n))+r.height))},{top:f.top||0,bottom:f.bottom||0}),v=r_(r_({},y),d),m=v.bottom;p&&(v.bottom+=p.props.height||tp.defaultProps.height),h&&e&&(v=(0,Y.By)(v,n,r,e));var b=c-v.left-v.right,g=s-v.top-v.bottom;return r_(r_({brushBottom:m},v),{},{width:Math.max(b,0),height:Math.max(g,0)})},rK=r(34067);function rG(t,e,r){if(e<1)return[];if(1===e&&void 0===r)return t;for(var n=[],o=0;o<t.length;o+=e){if(void 0!==r&&!0!==r(t[o]))return;n.push(t[o])}return n}function rJ(t,e,r,n,o){if(t*e<t*n||t*e>t*o)return!1;var i=r();return t*(e-t*i/2-n)>=0&&t*(e+t*i/2-o)<=0}function rQ(t){return(rQ="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function r0(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function r1(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?r0(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=rQ(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=rQ(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==rQ(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):r0(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var r2=["viewBox"],r5=["viewBox"],r3=["ticks"];function r6(t){return(r6="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function r7(){return(r7=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function r4(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function r8(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?r4(Object(r),!0).forEach(function(e){no(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):r4(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function r9(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function nt(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,ni(n.key),n)}}function ne(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(ne=function(){return!!t})()}function nr(t){return(nr=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function nn(t,e){return(nn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function no(t,e,r){return(e=ni(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function ni(t){var e=function(t,e){if("object"!=r6(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=r6(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==r6(e)?e:e+""}var na=function(t){var e,r;function n(t){var e,r,o;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,n),r=n,o=[t],r=nr(r),(e=function(t,e){if(e&&("object"===r6(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,ne()?Reflect.construct(r,o||[],nr(this).constructor):r.apply(this,o))).state={fontSize:"",letterSpacing:""},e}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&nn(t,e)}(n,t),e=[{key:"shouldComponentUpdate",value:function(t,e){var r=t.viewBox,n=r9(t,r2),o=this.props,i=o.viewBox,a=r9(o,r5);return!(0,eS.w)(r,i)||!(0,eS.w)(n,a)||!(0,eS.w)(e,this.state)}},{key:"componentDidMount",value:function(){var t=this.layerReference;if(t){var e=t.getElementsByClassName("recharts-cartesian-axis-tick-value")[0];e&&this.setState({fontSize:window.getComputedStyle(e).fontSize,letterSpacing:window.getComputedStyle(e).letterSpacing})}}},{key:"getTickLineCoord",value:function(t){var e,r,n,o,i,a,u=this.props,c=u.x,s=u.y,l=u.width,f=u.height,p=u.orientation,h=u.tickSize,d=u.mirror,y=u.tickMargin,v=d?-1:1,m=t.tickSize||h,b=(0,V.hj)(t.tickCoord)?t.tickCoord:t.coordinate;switch(p){case"top":e=r=t.coordinate,a=(n=(o=s+ +!d*f)-v*m)-v*y,i=b;break;case"left":n=o=t.coordinate,i=(e=(r=c+ +!d*l)-v*m)-v*y,a=b;break;case"right":n=o=t.coordinate,i=(e=(r=c+ +d*l)+v*m)+v*y,a=b;break;default:e=r=t.coordinate,a=(n=(o=s+ +d*f)+v*m)+v*y,i=b}return{line:{x1:e,y1:n,x2:r,y2:o},tick:{x:i,y:a}}}},{key:"getTickTextAnchor",value:function(){var t,e=this.props,r=e.orientation,n=e.mirror;switch(r){case"left":t=n?"start":"end";break;case"right":t=n?"end":"start";break;default:t="middle"}return t}},{key:"getTickVerticalAnchor",value:function(){var t=this.props,e=t.orientation,r=t.mirror,n="end";switch(e){case"left":case"right":n="middle";break;case"top":n=r?"start":"end";break;default:n=r?"end":"start"}return n}},{key:"renderAxisLine",value:function(){var t=this.props,e=t.x,r=t.y,n=t.width,o=t.height,i=t.orientation,a=t.mirror,u=t.axisLine,c=r8(r8(r8({},(0,U.L6)(this.props,!1)),(0,U.L6)(u,!1)),{},{fill:"none"});if("top"===i||"bottom"===i){var s=+("top"===i&&!a||"bottom"===i&&a);c=r8(r8({},c),{},{x1:e,y1:r+s*o,x2:e+n,y2:r+s*o})}else{var l=+("left"===i&&!a||"right"===i&&a);c=r8(r8({},c),{},{x1:e+l*n,y1:r,x2:e+l*n,y2:r+o})}return x.createElement("line",r7({},c,{className:(0,D.Z)("recharts-cartesian-axis-line",k()(u,"className"))}))}},{key:"renderTicks",value:function(t,e,r){var o=this,i=this.props,a=i.tickLine,u=i.stroke,c=i.tick,s=i.tickFormatter,l=i.unit,f=function(t,e,r){var n,o,i,a,u,c=t.tick,s=t.ticks,l=t.viewBox,f=t.minTickGap,p=t.orientation,h=t.interval,d=t.tickFormatter,y=t.unit,v=t.angle;if(!s||!s.length||!c)return[];if((0,V.hj)(h)||rK.x.isSsr)return rG(s,("number"==typeof h&&(0,V.hj)(h)?h:0)+1);var m="top"===p||"bottom"===p?"width":"height",b=y&&"width"===m?(0,th.xE)(y,{fontSize:e,letterSpacing:r}):{width:0,height:0},g=function(t,n){var o,i=S()(d)?d(t.value,n):t.value;return"width"===m?t_({width:(o=(0,th.xE)(i,{fontSize:e,letterSpacing:r})).width+b.width,height:o.height+b.height},v):(0,th.xE)(i,{fontSize:e,letterSpacing:r})[m]},x=s.length>=2?(0,V.uY)(s[1].coordinate-s[0].coordinate):1,O=(n="width"===m,o=l.x,i=l.y,a=l.width,u=l.height,1===x?{start:n?o:i,end:n?o+a:i+u}:{start:n?o+a:i+u,end:n?o:i});return"equidistantPreserveStart"===h?function(t,e,r,n,o){for(var i,a=(n||[]).slice(),u=e.start,c=e.end,s=0,l=1,f=u;l<=a.length;)if(i=function(){var e,i=null==n?void 0:n[s];if(void 0===i)return{v:rG(n,l)};var a=s,p=function(){return void 0===e&&(e=r(i,a)),e},h=i.coordinate,d=0===s||rJ(t,h,p,f,c);d||(s=0,f=u,l+=1),d&&(f=h+t*(p()/2+o),s+=l)}())return i.v;return[]}(x,O,g,s,f):("preserveStart"===h||"preserveStartEnd"===h?function(t,e,r,n,o,i){var a=(n||[]).slice(),u=a.length,c=e.start,s=e.end;if(i){var l=n[u-1],f=r(l,u-1),p=t*(l.coordinate+t*f/2-s);a[u-1]=l=r1(r1({},l),{},{tickCoord:p>0?l.coordinate-p*t:l.coordinate}),rJ(t,l.tickCoord,function(){return f},c,s)&&(s=l.tickCoord-t*(f/2+o),a[u-1]=r1(r1({},l),{},{isShow:!0}))}for(var h=i?u-1:u,d=function(e){var n,i=a[e],u=function(){return void 0===n&&(n=r(i,e)),n};if(0===e){var l=t*(i.coordinate-t*u()/2-c);a[e]=i=r1(r1({},i),{},{tickCoord:l<0?i.coordinate-l*t:i.coordinate})}else a[e]=i=r1(r1({},i),{},{tickCoord:i.coordinate});rJ(t,i.tickCoord,u,c,s)&&(c=i.tickCoord+t*(u()/2+o),a[e]=r1(r1({},i),{},{isShow:!0}))},y=0;y<h;y++)d(y);return a}(x,O,g,s,f,"preserveStartEnd"===h):function(t,e,r,n,o){for(var i=(n||[]).slice(),a=i.length,u=e.start,c=e.end,s=function(e){var n,s=i[e],l=function(){return void 0===n&&(n=r(s,e)),n};if(e===a-1){var f=t*(s.coordinate+t*l()/2-c);i[e]=s=r1(r1({},s),{},{tickCoord:f>0?s.coordinate-f*t:s.coordinate})}else i[e]=s=r1(r1({},s),{},{tickCoord:s.coordinate});rJ(t,s.tickCoord,l,u,c)&&(c=s.tickCoord-t*(l()/2+o),i[e]=r1(r1({},s),{},{isShow:!0}))},l=a-1;l>=0;l--)s(l);return i}(x,O,g,s,f)).filter(function(t){return t.isShow})}(r8(r8({},this.props),{},{ticks:t}),e,r),p=this.getTickTextAnchor(),h=this.getTickVerticalAnchor(),d=(0,U.L6)(this.props,!1),y=(0,U.L6)(c,!1),v=r8(r8({},d),{},{fill:"none"},(0,U.L6)(a,!1)),m=f.map(function(t,e){var r=o.getTickLineCoord(t),i=r.line,m=r.tick,b=r8(r8(r8(r8({textAnchor:p,verticalAnchor:h},d),{},{stroke:"none",fill:u},y),m),{},{index:e,payload:t,visibleTicksCount:f.length,tickFormatter:s});return x.createElement(B.m,r7({className:"recharts-cartesian-axis-tick",key:"tick-".concat(t.value,"-").concat(t.coordinate,"-").concat(t.tickCoord)},(0,z.bw)(o.props,t,e)),a&&x.createElement("line",r7({},v,i,{className:(0,D.Z)("recharts-cartesian-axis-tick-line",k()(a,"className"))})),c&&n.renderTickItem(c,b,"".concat(S()(s)?s(t.value,e):t.value).concat(l||"")))});return x.createElement("g",{className:"recharts-cartesian-axis-ticks"},m)}},{key:"render",value:function(){var t=this,e=this.props,r=e.axisLine,n=e.width,o=e.height,i=e.ticksGenerator,a=e.className;if(e.hide)return null;var u=this.props,c=u.ticks,s=r9(u,r3),l=c;return(S()(i)&&(l=i(c&&c.length>0?this.props:s)),n<=0||o<=0||!l||!l.length)?null:x.createElement(B.m,{className:(0,D.Z)("recharts-cartesian-axis",a),ref:function(e){t.layerReference=e}},r&&this.renderAxisLine(),this.renderTicks(l,this.state.fontSize,this.state.letterSpacing),ty._.renderCallByParent(this.props))}}],r=[{key:"renderTickItem",value:function(t,e,r){return x.isValidElement(t)?x.cloneElement(t,e):S()(t)?t(e):x.createElement(q.x,r7({},e,{className:"recharts-cartesian-axis-tick-value"}),r)}}],e&&nt(n.prototype,e),r&&nt(n,r),Object.defineProperty(n,"prototype",{writable:!1}),n}(x.Component);function nu(t){return(nu="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nc(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(nc=function(){return!!t})()}function ns(t){return(ns=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function nl(t,e){return(nl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function nf(t,e,r){return(e=np(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function np(t){var e=function(t,e){if("object"!=nu(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nu(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nu(e)?e:e+""}function nh(){return(nh=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function nd(t){var e=t.xAxisId,r=t3(),n=t6(),o=t2(e);return null==o?null:x.createElement(na,nh({},o,{className:(0,D.Z)("recharts-".concat(o.axisType," ").concat(o.axisType),o.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(t){return(0,Y.uY)(t,!0)}}))}no(na,"displayName","CartesianAxis"),no(na,"defaultProps",{x:0,y:0,width:0,height:0,viewBox:{x:0,y:0,width:0,height:0},orientation:"bottom",ticks:[],stroke:"#666",tickLine:!0,axisLine:!0,tick:!0,mirror:!1,minTickGap:5,tickSize:6,tickMargin:2,interval:"preserveEnd"});var ny=function(t){var e;function r(){var t,e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=ns(t),function(t,e){if(e&&("object"===nu(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,nc()?Reflect.construct(t,e||[],ns(this).constructor):t.apply(this,e))}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&nl(t,e)}(r,t),e=[{key:"render",value:function(){return x.createElement(nd,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,np(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(x.Component);function nv(t){return(nv="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nm(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(nm=function(){return!!t})()}function nb(t){return(nb=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function ng(t,e){return(ng=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function nx(t,e,r){return(e=nO(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function nO(t){var e=function(t,e){if("object"!=nv(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nv(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==nv(e)?e:e+""}function nw(){return(nw=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}nf(ny,"displayName","XAxis"),nf(ny,"defaultProps",{allowDecimals:!0,hide:!1,orientation:"bottom",width:0,height:30,mirror:!1,xAxisId:0,tickCount:5,type:"category",padding:{left:0,right:0},allowDataOverflow:!1,scale:"auto",reversed:!1,allowDuplicatedCategory:!0});var nj=function(t){var e=t.yAxisId,r=t3(),n=t6(),o=t5(e);return null==o?null:x.createElement(na,nw({},o,{className:(0,D.Z)("recharts-".concat(o.axisType," ").concat(o.axisType),o.className),viewBox:{x:0,y:0,width:r,height:n},ticksGenerator:function(t){return(0,Y.uY)(t,!0)}}))},nS=function(t){var e;function r(){var t,e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=nb(t),function(t,e){if(e&&("object"===nv(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,nm()?Reflect.construct(t,e||[],nb(this).constructor):t.apply(this,e))}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&ng(t,e)}(r,t),e=[{key:"render",value:function(){return x.createElement(nj,this.props)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,nO(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(x.Component);nx(nS,"displayName","YAxis"),nx(nS,"defaultProps",{allowDuplicatedCategory:!0,allowDecimals:!0,hide:!1,orientation:"left",width:60,height:0,mirror:!1,yAxisId:0,tickCount:5,type:"number",padding:{top:0,bottom:0},allowDataOverflow:!1,scale:"auto",reversed:!1});var nP=(a=(i={chartName:"BarChart",GraphicalChild:tO.$,defaultTooltipEventType:"axis",validateTooltipEventTypes:["axis","item"],axisComponents:[{axisType:"xAxis",AxisComp:ny},{axisType:"yAxis",AxisComp:nS}],formatAxisMap:function(t,e,r,n,o){var i=t.width,a=t.height,u=t.layout,c=t.children,s=Object.keys(e),l={left:r.left,leftMirror:r.left,right:i-r.right,rightMirror:i-r.right,top:r.top,topMirror:r.top,bottom:a-r.bottom,bottomMirror:a-r.bottom},f=!!(0,U.sP)(c,tO.$);return s.reduce(function(i,a){var c,s,p,h,d,y=e[a],v=y.orientation,m=y.domain,b=y.padding,g=void 0===b?{}:b,x=y.mirror,O=y.reversed,w="".concat(v).concat(x?"Mirror":"");if("number"===y.type&&("gap"===y.padding||"no-gap"===y.padding)){var j=m[1]-m[0],S=1/0,P=y.categoricalDomain.sort();if(P.forEach(function(t,e){e>0&&(S=Math.min((t||0)-(P[e-1]||0),S))}),Number.isFinite(S)){var A=S/j,E="vertical"===y.layout?r.height:r.width;if("gap"===y.padding&&(c=A*E/2),"no-gap"===y.padding){var k=(0,V.h1)(t.barCategoryGap,A*E),M=A*E/2;c=M-k-(M-k)/E*k}}}s="xAxis"===n?[r.left+(g.left||0)+(c||0),r.left+r.width-(g.right||0)-(c||0)]:"yAxis"===n?"horizontal"===u?[r.top+r.height-(g.bottom||0),r.top+(g.top||0)]:[r.top+(g.top||0)+(c||0),r.top+r.height-(g.bottom||0)-(c||0)]:y.range,O&&(s=[s[1],s[0]]);var T=(0,Y.Hq)(y,o,f),_=T.scale,C=T.realScaleType;_.domain(m).range(s),(0,Y.zF)(_);var D=(0,Y.g$)(_,tP(tP({},y),{},{realScaleType:C}));"xAxis"===n?(d="top"===v&&!x||"bottom"===v&&x,p=r.left,h=l[w]-d*y.height):"yAxis"===n&&(d="left"===v&&!x||"right"===v&&x,p=l[w]-d*y.width,h=r.top);var I=tP(tP(tP({},y),D),{},{realScaleType:C,x:p,y:h,scale:_,width:"xAxis"===n?r.width:y.width,height:"yAxis"===n?r.height:y.height});return I.bandSize=(0,Y.zT)(I,D),y.hide||"xAxis"!==n?y.hide||(l[w]+=(d?-1:1)*I.width):l[w]+=(d?-1:1)*I.height,tP(tP({},i),{},tA({},a,I))},{})}}).chartName,u=i.GraphicalChild,s=void 0===(c=i.defaultTooltipEventType)?"axis":c,f=void 0===(l=i.validateTooltipEventTypes)?["axis"]:l,p=i.axisComponents,h=i.legendContent,d=i.formatAxisMap,y=i.defaultProps,v=function(t,e){var r=e.graphicalItems,n=e.stackGroups,o=e.offset,i=e.updateId,a=e.dataStartIndex,u=e.dataEndIndex,c=t.barSize,s=t.layout,l=t.barGap,f=t.barCategoryGap,h=t.maxBarSize,d=rX(s),y=d.numericAxisName,v=d.cateAxisName,m=!!r&&!!r.length&&r.some(function(t){var e=(0,U.Gf)(t&&t.type);return e&&e.indexOf("Bar")>=0}),b=[];return r.forEach(function(r,d){var g=rz(t.data,{graphicalItems:[r],dataStartIndex:a,dataEndIndex:u}),x=void 0!==r.type.defaultProps?r_(r_({},r.type.defaultProps),r.props):r.props,O=x.dataKey,j=x.maxBarSize,S=x["".concat(y,"Id")],P=x["".concat(v,"Id")],A=p.reduce(function(t,r){var n=e["".concat(r.axisType,"Map")],o=x["".concat(r.axisType,"Id")];n&&n[o]||"zAxis"===r.axisType||(0,I.Z)(!1);var i=n[o];return r_(r_({},t),{},rC(rC({},r.axisType,i),"".concat(r.axisType,"Ticks"),(0,Y.uY)(i)))},{}),E=A[v],k=A["".concat(v,"Ticks")],M=n&&n[S]&&n[S].hasStack&&(0,Y.O3)(r,n[S].stackGroups),T=(0,U.Gf)(r.type).indexOf("Bar")>=0,_=(0,Y.zT)(E,k),C=[],D=m&&(0,Y.pt)({barSize:c,stackGroups:n,totalSize:"xAxis"===v?A[v].width:"yAxis"===v?A[v].height:void 0});if(T){var N,B,L=w()(j)?h:j,R=null!==(N=null!==(B=(0,Y.zT)(E,k,!0))&&void 0!==B?B:L)&&void 0!==N?N:0;C=(0,Y.qz)({barGap:l,barCategoryGap:f,bandSize:R!==_?R:_,sizeList:D[P],maxBarSize:L}),R!==_&&(C=C.map(function(t){return r_(r_({},t),{},{position:r_(r_({},t.position),{},{offset:t.position.offset-R/2})})}))}var z=r&&r.type&&r.type.getComposedData;z&&b.push({props:r_(r_({},z(r_(r_({},A),{},{displayedData:g,props:t,dataKey:O,item:r,bandSize:_,barPosition:C,offset:o,stackedData:M,layout:s,dataStartIndex:a,dataEndIndex:u}))),{},rC(rC(rC({key:r.key||"item-".concat(d)},y,A[y]),v,A[v]),"animationId",i)),childIndex:(0,U.$R)(r,t.children),item:r})}),b},m=function(t,e){var r=t.props,n=t.dataStartIndex,o=t.dataEndIndex,i=t.updateId;if(!(0,U.TT)({props:r}))return null;var c=r.children,s=r.layout,l=r.stackOffset,f=r.data,h=r.reverseStackOrder,y=rX(s),m=y.numericAxisName,b=y.cateAxisName,g=(0,U.NN)(c,u),x=(0,Y.wh)(f,g,"".concat(m,"Id"),"".concat(b,"Id"),l,h),O=p.reduce(function(t,e){var i="".concat(e.axisType,"Map");return r_(r_({},t),{},rC({},i,rq(r,r_(r_({},e),{},{graphicalItems:g,stackGroups:e.axisType===m&&x,dataStartIndex:n,dataEndIndex:o}))))},{}),w=rH(r_(r_({},O),{},{props:r,graphicalItems:g}),null==e?void 0:e.legendBBox);Object.keys(O).forEach(function(t){O[t]=d(r,O[t],w,t.replace("Map",""),a)});var j=rY(O["".concat(b,"Map")]),S=v(r,r_(r_({},O),{},{dataStartIndex:n,dataEndIndex:o,updateId:i,graphicalItems:g,stackGroups:x,offset:w}));return r_(r_({formattedGraphicalItems:S,graphicalItems:g,offset:w,stackGroups:x},j),O)},b=function(t){var e;function r(t){var e,n,o,i,u;return function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),i=r,u=[t],i=rP(i),rC(o=function(t,e){if(e&&("object"===rx(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,rS()?Reflect.construct(i,u||[],rP(this).constructor):i.apply(this,u)),"eventEmitterSymbol",Symbol("rechartsEventEmitter")),rC(o,"accessibilityManager",new e_),rC(o,"handleLegendBBoxUpdate",function(t){if(t){var e=o.state,r=e.dataStartIndex,n=e.dataEndIndex,i=e.updateId;o.setState(r_({legendBBox:t},m({props:o.props,dataStartIndex:r,dataEndIndex:n,updateId:i},r_(r_({},o.state),{},{legendBBox:t}))))}}),rC(o,"handleReceiveSyncEvent",function(t,e,r){o.props.syncId===t&&(r!==o.eventEmitterSymbol||"function"==typeof o.props.syncMethod)&&o.applySyncEvent(e)}),rC(o,"handleBrushChange",function(t){var e=t.startIndex,r=t.endIndex;if(e!==o.state.dataStartIndex||r!==o.state.dataEndIndex){var n=o.state.updateId;o.setState(function(){return r_({dataStartIndex:e,dataEndIndex:r},m({props:o.props,dataStartIndex:e,dataEndIndex:r,updateId:n},o.state))}),o.triggerSyncEvent({dataStartIndex:e,dataEndIndex:r})}}),rC(o,"handleMouseEnter",function(t){var e=o.getMouseInfo(t);if(e){var r=r_(r_({},e),{},{isTooltipActive:!0});o.setState(r),o.triggerSyncEvent(r);var n=o.props.onMouseEnter;S()(n)&&n(r,t)}}),rC(o,"triggeredAfterMouseMove",function(t){var e=o.getMouseInfo(t),r=e?r_(r_({},e),{},{isTooltipActive:!0}):{isTooltipActive:!1};o.setState(r),o.triggerSyncEvent(r);var n=o.props.onMouseMove;S()(n)&&n(r,t)}),rC(o,"handleItemMouseEnter",function(t){o.setState(function(){return{isTooltipActive:!0,activeItem:t,activePayload:t.tooltipPayload,activeCoordinate:t.tooltipPosition||{x:t.cx,y:t.cy}}})}),rC(o,"handleItemMouseLeave",function(){o.setState(function(){return{isTooltipActive:!1}})}),rC(o,"handleMouseMove",function(t){t.persist(),o.throttleTriggeredAfterMouseMove(t)}),rC(o,"handleMouseLeave",function(t){o.throttleTriggeredAfterMouseMove.cancel();var e={isTooltipActive:!1};o.setState(e),o.triggerSyncEvent(e);var r=o.props.onMouseLeave;S()(r)&&r(e,t)}),rC(o,"handleOuterEvent",function(t){var e,r=(0,U.Bh)(t),n=k()(o.props,"".concat(r));r&&S()(n)&&n(null!==(e=/.*touch.*/i.test(r)?o.getMouseInfo(t.changedTouches[0]):o.getMouseInfo(t))&&void 0!==e?e:{},t)}),rC(o,"handleClick",function(t){var e=o.getMouseInfo(t);if(e){var r=r_(r_({},e),{},{isTooltipActive:!0});o.setState(r),o.triggerSyncEvent(r);var n=o.props.onClick;S()(n)&&n(r,t)}}),rC(o,"handleMouseDown",function(t){var e=o.props.onMouseDown;S()(e)&&e(o.getMouseInfo(t),t)}),rC(o,"handleMouseUp",function(t){var e=o.props.onMouseUp;S()(e)&&e(o.getMouseInfo(t),t)}),rC(o,"handleTouchMove",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&o.throttleTriggeredAfterMouseMove(t.changedTouches[0])}),rC(o,"handleTouchStart",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&o.handleMouseDown(t.changedTouches[0])}),rC(o,"handleTouchEnd",function(t){null!=t.changedTouches&&t.changedTouches.length>0&&o.handleMouseUp(t.changedTouches[0])}),rC(o,"handleDoubleClick",function(t){var e=o.props.onDoubleClick;S()(e)&&e(o.getMouseInfo(t),t)}),rC(o,"handleContextMenu",function(t){var e=o.props.onContextMenu;S()(e)&&e(o.getMouseInfo(t),t)}),rC(o,"triggerSyncEvent",function(t){void 0!==o.props.syncId&&eA.emit(eE,o.props.syncId,t,o.eventEmitterSymbol)}),rC(o,"applySyncEvent",function(t){var e=o.props,r=e.layout,n=e.syncMethod,i=o.state.updateId,a=t.dataStartIndex,u=t.dataEndIndex;if(void 0!==t.dataStartIndex||void 0!==t.dataEndIndex)o.setState(r_({dataStartIndex:a,dataEndIndex:u},m({props:o.props,dataStartIndex:a,dataEndIndex:u,updateId:i},o.state)));else if(void 0!==t.activeTooltipIndex){var c=t.chartX,s=t.chartY,l=t.activeTooltipIndex,f=o.state,p=f.offset,h=f.tooltipTicks;if(!p)return;if("function"==typeof n)l=n(h,t);else if("value"===n){l=-1;for(var d=0;d<h.length;d++)if(h[d].value===t.activeLabel){l=d;break}}var y=r_(r_({},p),{},{x:p.left,y:p.top}),v=Math.min(c,y.x+y.width),b=Math.min(s,y.y+y.height),g=h[l]&&h[l].value,x=r$(o.state,o.props.data,l),O=h[l]?{x:"horizontal"===r?h[l].coordinate:v,y:"horizontal"===r?b:h[l].coordinate}:rB;o.setState(r_(r_({},t),{},{activeLabel:g,activeCoordinate:O,activePayload:x,activeTooltipIndex:l}))}else o.setState(t)}),rC(o,"renderCursor",function(t){var e,r=o.state,n=r.isTooltipActive,i=r.activeCoordinate,u=r.activePayload,c=r.offset,s=r.activeTooltipIndex,l=r.tooltipAxisBandSize,f=o.getTooltipEventType(),p=null!==(e=t.props.active)&&void 0!==e?e:n,h=o.props.layout,d=t.key||"_recharts-cursor";return x.createElement(rm,{key:d,activeCoordinate:i,activePayload:u,activeTooltipIndex:s,chartName:a,element:t,isActive:p,layout:h,offset:c,tooltipAxisBandSize:l,tooltipEventType:f})}),rC(o,"renderPolarAxis",function(t,e,r){var n=k()(t,"type.axisType"),i=k()(o.state,"".concat(n,"Map")),a=t.type.defaultProps,u=void 0!==a?r_(r_({},a),t.props):t.props,c=i&&i[u["".concat(n,"Id")]];return(0,x.cloneElement)(t,r_(r_({},c),{},{className:(0,D.Z)(n,c.className),key:t.key||"".concat(e,"-").concat(r),ticks:(0,Y.uY)(c,!0)}))}),rC(o,"renderPolarGrid",function(t){var e=t.props,r=e.radialLines,n=e.polarAngles,i=e.polarRadius,a=o.state,u=a.radiusAxisMap,c=a.angleAxisMap,s=(0,V.Kt)(u),l=(0,V.Kt)(c),f=l.cx,p=l.cy,h=l.innerRadius,d=l.outerRadius;return(0,x.cloneElement)(t,{polarAngles:Array.isArray(n)?n:(0,Y.uY)(l,!0).map(function(t){return t.coordinate}),polarRadius:Array.isArray(i)?i:(0,Y.uY)(s,!0).map(function(t){return t.coordinate}),cx:f,cy:p,innerRadius:h,outerRadius:d,key:t.key||"polar-grid",radialLines:r})}),rC(o,"renderLegend",function(){var t=o.state.formattedGraphicalItems,e=o.props,r=e.children,n=e.width,i=e.height,a=o.props.margin||{},u=n-(a.left||0)-(a.right||0),c=(0,td.z)({children:r,formattedGraphicalItems:t,legendWidth:u,legendContent:h});if(!c)return null;var s=c.item,l=rj(c,rb);return(0,x.cloneElement)(s,r_(r_({},l),{},{chartWidth:n,chartHeight:i,margin:a,onBBoxUpdate:o.handleLegendBBoxUpdate}))}),rC(o,"renderTooltip",function(){var t,e=o.props,r=e.children,n=e.accessibilityLayer,i=(0,U.sP)(r,L.u);if(!i)return null;var a=o.state,u=a.isTooltipActive,c=a.activeCoordinate,s=a.activePayload,l=a.activeLabel,f=a.offset,p=null!==(t=i.props.active)&&void 0!==t?t:u;return(0,x.cloneElement)(i,{viewBox:r_(r_({},f),{},{x:f.left,y:f.top}),active:p,label:l,payload:p?s:[],coordinate:c,accessibilityLayer:n})}),rC(o,"renderBrush",function(t){var e=o.props,r=e.margin,n=e.data,i=o.state,a=i.offset,u=i.dataStartIndex,c=i.dataEndIndex,s=i.updateId;return(0,x.cloneElement)(t,{key:t.key||"_recharts-brush",onChange:(0,Y.DO)(o.handleBrushChange,t.props.onChange),data:n,x:(0,V.hj)(t.props.x)?t.props.x:a.left,y:(0,V.hj)(t.props.y)?t.props.y:a.top+a.height+a.brushBottom-(r.bottom||0),width:(0,V.hj)(t.props.width)?t.props.width:a.width,startIndex:u,endIndex:c,updateId:"brush-".concat(s)})}),rC(o,"renderReferenceElement",function(t,e,r){if(!t)return null;var n=o.clipPathId,i=o.state,a=i.xAxisMap,u=i.yAxisMap,c=i.offset,s=t.type.defaultProps||{},l=t.props,f=l.xAxisId,p=void 0===f?s.xAxisId:f,h=l.yAxisId,d=void 0===h?s.yAxisId:h;return(0,x.cloneElement)(t,{key:t.key||"".concat(e,"-").concat(r),xAxis:a[p],yAxis:u[d],viewBox:{x:c.left,y:c.top,width:c.width,height:c.height},clipPathId:n})}),rC(o,"renderActivePoints",function(t){var e=t.item,n=t.activePoint,o=t.basePoint,i=t.childIndex,a=t.isRange,u=[],c=e.props.key,s=void 0!==e.item.type.defaultProps?r_(r_({},e.item.type.defaultProps),e.item.props):e.item.props,l=s.activeDot,f=r_(r_({index:i,dataKey:s.dataKey,cx:n.x,cy:n.y,r:4,fill:(0,Y.fk)(e.item),strokeWidth:2,stroke:"#fff",payload:n.payload,value:n.value},(0,U.L6)(l,!1)),(0,z.Ym)(l));return u.push(r.renderActiveDot(l,f,"".concat(c,"-activePoint-").concat(i))),o?u.push(r.renderActiveDot(l,r_(r_({},f),{},{cx:o.x,cy:o.y}),"".concat(c,"-basePoint-").concat(i))):a&&u.push(null),u}),rC(o,"renderGraphicChild",function(t,e,r){var n=o.filterFormatItem(t,e,r);if(!n)return null;var i=o.getTooltipEventType(),a=o.state,u=a.isTooltipActive,c=a.tooltipAxis,s=a.activeTooltipIndex,l=a.activeLabel,f=o.props.children,p=(0,U.sP)(f,L.u),h=n.props,d=h.points,y=h.isRange,v=h.baseLine,m=void 0!==n.item.type.defaultProps?r_(r_({},n.item.type.defaultProps),n.item.props):n.item.props,b=m.activeDot,g=m.hide,O=m.activeBar,j=m.activeShape,S={};"axis"!==i&&p&&"click"===p.props.trigger?S={onClick:(0,Y.DO)(o.handleItemMouseEnter,t.props.onClick)}:"axis"!==i&&(S={onMouseLeave:(0,Y.DO)(o.handleItemMouseLeave,t.props.onMouseLeave),onMouseEnter:(0,Y.DO)(o.handleItemMouseEnter,t.props.onMouseEnter)});var P=(0,x.cloneElement)(t,r_(r_({},n.props),S));if(!g&&u&&p&&(b||O||j)){if(s>=0){if(c.dataKey&&!c.allowDuplicatedCategory){var A="function"==typeof c.dataKey?function(t){return"function"==typeof c.dataKey?c.dataKey(t.payload):null}:"payload.".concat(c.dataKey.toString());k=(0,V.Ap)(d,A,l),M=y&&v&&(0,V.Ap)(v,A,l)}else k=null==d?void 0:d[s],M=y&&v&&v[s];if(j||O){var E=void 0!==t.props.activeIndex?t.props.activeIndex:s;return[(0,x.cloneElement)(t,r_(r_(r_({},n.props),S),{},{activeIndex:E})),null,null]}if(!w()(k))return[P].concat(rE(o.renderActivePoints({item:n,activePoint:k,basePoint:M,childIndex:s,isRange:y})))}else{var k,M,T,_=(null!==(T=o.getItemByXY(o.state.activeCoordinate))&&void 0!==T?T:{graphicalItem:P}).graphicalItem,C=_.item,D=void 0===C?t:C,I=_.childIndex,N=r_(r_(r_({},n.props),S),{},{activeIndex:I});return[(0,x.cloneElement)(D,N),null,null]}}return y?[P,null,null]:[P,null]}),rC(o,"renderCustomized",function(t,e,r){return(0,x.cloneElement)(t,r_(r_({key:"recharts-customized-".concat(r)},o.props),o.state))}),rC(o,"renderMap",{CartesianGrid:{handler:rL,once:!0},ReferenceArea:{handler:o.renderReferenceElement},ReferenceLine:{handler:rL},ReferenceDot:{handler:o.renderReferenceElement},XAxis:{handler:rL},YAxis:{handler:rL},Brush:{handler:o.renderBrush,once:!0},Bar:{handler:o.renderGraphicChild},Line:{handler:o.renderGraphicChild},Area:{handler:o.renderGraphicChild},Radar:{handler:o.renderGraphicChild},RadialBar:{handler:o.renderGraphicChild},Scatter:{handler:o.renderGraphicChild},Pie:{handler:o.renderGraphicChild},Funnel:{handler:o.renderGraphicChild},Tooltip:{handler:o.renderCursor,once:!0},PolarGrid:{handler:o.renderPolarGrid,once:!0},PolarAngleAxis:{handler:o.renderPolarAxis},PolarRadiusAxis:{handler:o.renderPolarAxis},Customized:{handler:o.renderCustomized}}),o.clipPathId="".concat(null!==(e=t.id)&&void 0!==e?e:(0,V.EL)("recharts"),"-clip"),o.throttleTriggeredAfterMouseMove=C()(o.triggeredAfterMouseMove,null!==(n=t.throttleDelay)&&void 0!==n?n:1e3/60),o.state={},o}return function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&rA(t,e)}(r,t),e=[{key:"componentDidMount",value:function(){var t,e;this.addListener(),this.accessibilityManager.setDetails({container:this.container,offset:{left:null!==(t=this.props.margin.left)&&void 0!==t?t:0,top:null!==(e=this.props.margin.top)&&void 0!==e?e:0},coordinateList:this.state.tooltipTicks,mouseHandlerCallback:this.triggeredAfterMouseMove,layout:this.props.layout}),this.displayDefaultTooltip()}},{key:"displayDefaultTooltip",value:function(){var t=this.props,e=t.children,r=t.data,n=t.height,o=t.layout,i=(0,U.sP)(e,L.u);if(i){var a=i.props.defaultIndex;if("number"==typeof a&&!(a<0)&&!(a>this.state.tooltipTicks.length-1)){var u=this.state.tooltipTicks[a]&&this.state.tooltipTicks[a].value,c=r$(this.state,r,a,u),s=this.state.tooltipTicks[a].coordinate,l=(this.state.offset.top+n)/2,f="horizontal"===o?{x:s,y:l}:{y:s,x:l},p=this.state.formattedGraphicalItems.find(function(t){return"Scatter"===t.item.type.name});p&&(f=r_(r_({},f),p.props.points[a].tooltipPosition),c=p.props.points[a].tooltipPayload);var h={activeTooltipIndex:a,isTooltipActive:!0,activeLabel:u,activePayload:c,activeCoordinate:f};this.setState(h),this.renderCursor(i),this.accessibilityManager.setIndex(a)}}}},{key:"getSnapshotBeforeUpdate",value:function(t,e){if(!this.props.accessibilityLayer)return null;if(this.state.tooltipTicks!==e.tooltipTicks&&this.accessibilityManager.setDetails({coordinateList:this.state.tooltipTicks}),this.props.layout!==t.layout&&this.accessibilityManager.setDetails({layout:this.props.layout}),this.props.margin!==t.margin){var r,n;this.accessibilityManager.setDetails({offset:{left:null!==(r=this.props.margin.left)&&void 0!==r?r:0,top:null!==(n=this.props.margin.top)&&void 0!==n?n:0}})}return null}},{key:"componentDidUpdate",value:function(t){(0,U.rL)([(0,U.sP)(t.children,L.u)],[(0,U.sP)(this.props.children,L.u)])||this.displayDefaultTooltip()}},{key:"componentWillUnmount",value:function(){this.removeListener(),this.throttleTriggeredAfterMouseMove.cancel()}},{key:"getTooltipEventType",value:function(){var t=(0,U.sP)(this.props.children,L.u);if(t&&"boolean"==typeof t.props.shared){var e=t.props.shared?"axis":"item";return f.indexOf(e)>=0?e:s}return s}},{key:"getMouseInfo",value:function(t){if(!this.container)return null;var e=this.container,r=e.getBoundingClientRect(),n=(0,th.os)(r),o={chartX:Math.round(t.pageX-n.left),chartY:Math.round(t.pageY-n.top)},i=r.width/e.offsetWidth||1,a=this.inRange(o.chartX,o.chartY,i);if(!a)return null;var u=this.state,c=u.xAxisMap,s=u.yAxisMap;if("axis"!==this.getTooltipEventType()&&c&&s){var l=(0,V.Kt)(c).scale,f=(0,V.Kt)(s).scale,p=l&&l.invert?l.invert(o.chartX):null,h=f&&f.invert?f.invert(o.chartY):null;return r_(r_({},o),{},{xValue:p,yValue:h})}var d=rF(this.state,this.props.data,this.props.layout,a);return d?r_(r_({},o),d):null}},{key:"inRange",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1,n=this.props.layout,o=t/r,i=e/r;if("horizontal"===n||"vertical"===n){var a=this.state.offset;return o>=a.left&&o<=a.left+a.width&&i>=a.top&&i<=a.top+a.height?{x:o,y:i}:null}var u=this.state,c=u.angleAxisMap,s=u.radiusAxisMap;if(c&&s){var l=(0,V.Kt)(c);return(0,ej.z3)({x:o,y:i},l)}return null}},{key:"parseEventsOfWrapper",value:function(){var t=this.props.children,e=this.getTooltipEventType(),r=(0,U.sP)(t,L.u),n={};return r&&"axis"===e&&(n="click"===r.props.trigger?{onClick:this.handleClick}:{onMouseEnter:this.handleMouseEnter,onDoubleClick:this.handleDoubleClick,onMouseMove:this.handleMouseMove,onMouseLeave:this.handleMouseLeave,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,onTouchEnd:this.handleTouchEnd,onContextMenu:this.handleContextMenu}),r_(r_({},(0,z.Ym)(this.props,this.handleOuterEvent)),n)}},{key:"addListener",value:function(){eA.on(eE,this.handleReceiveSyncEvent)}},{key:"removeListener",value:function(){eA.removeListener(eE,this.handleReceiveSyncEvent)}},{key:"filterFormatItem",value:function(t,e,r){for(var n=this.state.formattedGraphicalItems,o=0,i=n.length;o<i;o++){var a=n[o];if(a.item===t||a.props.key===t.key||e===(0,U.Gf)(a.item.type)&&r===a.childIndex)return a}return null}},{key:"renderClipPath",value:function(){var t=this.clipPathId,e=this.state.offset,r=e.left,n=e.top,o=e.height,i=e.width;return x.createElement("defs",null,x.createElement("clipPath",{id:t},x.createElement("rect",{x:r,y:n,height:o,width:i})))}},{key:"getXScales",value:function(){var t=this.state.xAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=rw(e,2),n=r[0],o=r[1];return r_(r_({},t),{},rC({},n,o.scale))},{}):null}},{key:"getYScales",value:function(){var t=this.state.yAxisMap;return t?Object.entries(t).reduce(function(t,e){var r=rw(e,2),n=r[0],o=r[1];return r_(r_({},t),{},rC({},n,o.scale))},{}):null}},{key:"getXScaleByAxisId",value:function(t){var e;return null===(e=this.state.xAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getYScaleByAxisId",value:function(t){var e;return null===(e=this.state.yAxisMap)||void 0===e||null===(e=e[t])||void 0===e?void 0:e.scale}},{key:"getItemByXY",value:function(t){var e=this.state,r=e.formattedGraphicalItems,n=e.activeItem;if(r&&r.length)for(var o=0,i=r.length;o<i;o++){var a=r[o],u=a.props,c=a.item,s=void 0!==c.type.defaultProps?r_(r_({},c.type.defaultProps),c.props):c.props,l=(0,U.Gf)(c.type);if("Bar"===l){var f=(u.data||[]).find(function(e){return(0,Z.X)(t,e)});if(f)return{graphicalItem:a,payload:f}}else if("RadialBar"===l){var p=(u.data||[]).find(function(e){return(0,ej.z3)(t,e)});if(p)return{graphicalItem:a,payload:p}}else if((0,eC.lT)(a,n)||(0,eC.V$)(a,n)||(0,eC.w7)(a,n)){var h=(0,eC.a3)({graphicalItem:a,activeTooltipItem:n,itemData:s.data}),d=void 0===s.activeIndex?h:s.activeIndex;return{graphicalItem:r_(r_({},a),{},{childIndex:d}),payload:(0,eC.w7)(a,n)?s.data[h]:a.props.data[h]}}}return null}},{key:"render",value:function(){var t,e,r=this;if(!(0,U.TT)(this))return null;var n=this.props,o=n.children,i=n.className,a=n.width,u=n.height,c=n.style,s=n.compact,l=n.title,f=n.desc,p=rj(n,rg),h=(0,U.L6)(p,!1);if(s)return x.createElement(t1,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},x.createElement(N.T,rO({},h,{width:a,height:u,title:l,desc:f}),this.renderClipPath(),(0,U.eu)(o,this.renderMap)));this.props.accessibilityLayer&&(h.tabIndex=null!==(t=this.props.tabIndex)&&void 0!==t?t:0,h.role=null!==(e=this.props.role)&&void 0!==e?e:"application",h.onKeyDown=function(t){r.accessibilityManager.keyboardEvent(t)},h.onFocus=function(){r.accessibilityManager.focus()});var d=this.parseEventsOfWrapper();return x.createElement(t1,{state:this.state,width:this.props.width,height:this.props.height,clipPathId:this.clipPathId},x.createElement("div",rO({className:(0,D.Z)("recharts-wrapper",i),style:r_({position:"relative",cursor:"default",width:a,height:u},c)},d,{ref:function(t){r.container=t}}),x.createElement(N.T,rO({},h,{width:a,height:u,title:l,desc:f,style:rN}),this.renderClipPath(),(0,U.eu)(o,this.renderMap)),this.renderLegend(),this.renderTooltip()))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,rD(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(x.Component),rC(b,"displayName",a),rC(b,"defaultProps",r_({layout:"horizontal",stackOffset:"none",barCategoryGap:"10%",barGap:4,margin:{top:5,right:5,bottom:5,left:5},reverseStackOrder:!1,syncMethod:"index"},y)),rC(b,"getDerivedStateFromProps",function(t,e){var r=t.dataKey,n=t.data,o=t.children,i=t.width,a=t.height,u=t.layout,c=t.stackOffset,s=t.margin,l=e.dataStartIndex,f=e.dataEndIndex;if(void 0===e.updateId){var p=rV(t);return r_(r_(r_({},p),{},{updateId:0},m(r_(r_({props:t},p),{},{updateId:0}),e)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:u,prevStackOffset:c,prevMargin:s,prevChildren:o})}if(r!==e.prevDataKey||n!==e.prevData||i!==e.prevWidth||a!==e.prevHeight||u!==e.prevLayout||c!==e.prevStackOffset||!(0,eS.w)(s,e.prevMargin)){var h=rV(t),d={chartX:e.chartX,chartY:e.chartY,isTooltipActive:e.isTooltipActive},y=r_(r_({},rF(e,n,u)),{},{updateId:e.updateId+1}),v=r_(r_(r_({},h),d),y);return r_(r_(r_({},v),m(r_({props:t},v),e)),{},{prevDataKey:r,prevData:n,prevWidth:i,prevHeight:a,prevLayout:u,prevStackOffset:c,prevMargin:s,prevChildren:o})}if(!(0,U.rL)(o,e.prevChildren)){var b,g,x,O,j=(0,U.sP)(o,tp),S=j&&null!==(b=null===(g=j.props)||void 0===g?void 0:g.startIndex)&&void 0!==b?b:l,P=j&&null!==(x=null===(O=j.props)||void 0===O?void 0:O.endIndex)&&void 0!==x?x:f,A=w()(n)||S!==l||P!==f?e.updateId+1:e.updateId;return r_(r_({updateId:A},m(r_(r_({props:t},e),{},{updateId:A,dataStartIndex:S,dataEndIndex:P}),e)),{},{prevChildren:o,dataStartIndex:S,dataEndIndex:P})}return null}),rC(b,"renderActiveDot",function(t,e,r){var n;return n=(0,x.isValidElement)(t)?(0,x.cloneElement)(t,e):S()(t)?t(e):x.createElement(F,e),x.createElement(B.m,{className:"recharts-active-dot",key:r},n)}),(g=(0,x.forwardRef)(function(t,e){return x.createElement(b,rO({},t,{ref:e}))})).displayName=b.displayName,g)},20407:function(t,e,r){"use strict";r.d(e,{b:function(){return n}});var n=function(t){return null};n.displayName="Cell"},26680:function(t,e,r){"use strict";r.d(e,{_:function(){return P}});var n=r(2265),o=r(77571),i=r.n(o),a=r(86757),u=r.n(a),c=r(28302),s=r.n(c),l=r(61994),f=r(58811),p=r(82944),h=r(16630),d=r(39206);function y(t){return(y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var v=["offset"];function m(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function b(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function g(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?b(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=y(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=y(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==y(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):b(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function x(){return(x=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var O=function(t){var e=t.value,r=t.formatter,n=i()(t.children)?e:t.children;return u()(r)?r(n):n},w=function(t,e,r){var o,a,u=t.position,c=t.viewBox,s=t.offset,f=t.className,p=c.cx,y=c.cy,v=c.innerRadius,m=c.outerRadius,b=c.startAngle,g=c.endAngle,O=c.clockWise,w=(v+m)/2,j=(0,h.uY)(g-b)*Math.min(Math.abs(g-b),360),S=j>=0?1:-1;"insideStart"===u?(o=b+S*s,a=O):"insideEnd"===u?(o=g-S*s,a=!O):"end"===u&&(o=g+S*s,a=O),a=j<=0?a:!a;var P=(0,d.op)(p,y,w,o),A=(0,d.op)(p,y,w,o+(a?1:-1)*359),E="M".concat(P.x,",").concat(P.y,"\n    A").concat(w,",").concat(w,",0,1,").concat(a?0:1,",\n    ").concat(A.x,",").concat(A.y),k=i()(t.id)?(0,h.EL)("recharts-radial-line-"):t.id;return n.createElement("text",x({},r,{dominantBaseline:"central",className:(0,l.Z)("recharts-radial-bar-label",f)}),n.createElement("defs",null,n.createElement("path",{id:k,d:E})),n.createElement("textPath",{xlinkHref:"#".concat(k)},e))},j=function(t){var e=t.viewBox,r=t.offset,n=t.position,o=e.cx,i=e.cy,a=e.innerRadius,u=e.outerRadius,c=(e.startAngle+e.endAngle)/2;if("outside"===n){var s=(0,d.op)(o,i,u+r,c),l=s.x;return{x:l,y:s.y,textAnchor:l>=o?"start":"end",verticalAnchor:"middle"}}if("center"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"middle"};if("centerTop"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"start"};if("centerBottom"===n)return{x:o,y:i,textAnchor:"middle",verticalAnchor:"end"};var f=(0,d.op)(o,i,(a+u)/2,c);return{x:f.x,y:f.y,textAnchor:"middle",verticalAnchor:"middle"}},S=function(t){var e=t.viewBox,r=t.parentViewBox,n=t.offset,o=t.position,i=e.x,a=e.y,u=e.width,c=e.height,l=c>=0?1:-1,f=l*n,p=l>0?"end":"start",d=l>0?"start":"end",y=u>=0?1:-1,v=y*n,m=y>0?"end":"start",b=y>0?"start":"end";if("top"===o)return g(g({},{x:i+u/2,y:a-l*n,textAnchor:"middle",verticalAnchor:p}),r?{height:Math.max(a-r.y,0),width:u}:{});if("bottom"===o)return g(g({},{x:i+u/2,y:a+c+f,textAnchor:"middle",verticalAnchor:d}),r?{height:Math.max(r.y+r.height-(a+c),0),width:u}:{});if("left"===o){var x={x:i-v,y:a+c/2,textAnchor:m,verticalAnchor:"middle"};return g(g({},x),r?{width:Math.max(x.x-r.x,0),height:c}:{})}if("right"===o){var O={x:i+u+v,y:a+c/2,textAnchor:b,verticalAnchor:"middle"};return g(g({},O),r?{width:Math.max(r.x+r.width-O.x,0),height:c}:{})}var w=r?{width:u,height:c}:{};return"insideLeft"===o?g({x:i+v,y:a+c/2,textAnchor:b,verticalAnchor:"middle"},w):"insideRight"===o?g({x:i+u-v,y:a+c/2,textAnchor:m,verticalAnchor:"middle"},w):"insideTop"===o?g({x:i+u/2,y:a+f,textAnchor:"middle",verticalAnchor:d},w):"insideBottom"===o?g({x:i+u/2,y:a+c-f,textAnchor:"middle",verticalAnchor:p},w):"insideTopLeft"===o?g({x:i+v,y:a+f,textAnchor:b,verticalAnchor:d},w):"insideTopRight"===o?g({x:i+u-v,y:a+f,textAnchor:m,verticalAnchor:d},w):"insideBottomLeft"===o?g({x:i+v,y:a+c-f,textAnchor:b,verticalAnchor:p},w):"insideBottomRight"===o?g({x:i+u-v,y:a+c-f,textAnchor:m,verticalAnchor:p},w):s()(o)&&((0,h.hj)(o.x)||(0,h.hU)(o.x))&&((0,h.hj)(o.y)||(0,h.hU)(o.y))?g({x:i+(0,h.h1)(o.x,u),y:a+(0,h.h1)(o.y,c),textAnchor:"end",verticalAnchor:"end"},w):g({x:i+u/2,y:a+c/2,textAnchor:"middle",verticalAnchor:"middle"},w)};function P(t){var e,r=t.offset,o=g({offset:void 0===r?5:r},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,v)),a=o.viewBox,c=o.position,s=o.value,d=o.children,y=o.content,m=o.className,b=o.textBreakAll;if(!a||i()(s)&&i()(d)&&!(0,n.isValidElement)(y)&&!u()(y))return null;if((0,n.isValidElement)(y))return(0,n.cloneElement)(y,o);if(u()(y)){if(e=(0,n.createElement)(y,o),(0,n.isValidElement)(e))return e}else e=O(o);var P="cx"in a&&(0,h.hj)(a.cx),A=(0,p.L6)(o,!0);if(P&&("insideStart"===c||"insideEnd"===c||"end"===c))return w(o,e,A);var E=P?j(o):S(o);return n.createElement(f.x,x({className:(0,l.Z)("recharts-label",void 0===m?"":m)},A,E,{breakAll:b}),e)}P.displayName="Label";var A=function(t){var e=t.cx,r=t.cy,n=t.angle,o=t.startAngle,i=t.endAngle,a=t.r,u=t.radius,c=t.innerRadius,s=t.outerRadius,l=t.x,f=t.y,p=t.top,d=t.left,y=t.width,v=t.height,m=t.clockWise,b=t.labelViewBox;if(b)return b;if((0,h.hj)(y)&&(0,h.hj)(v)){if((0,h.hj)(l)&&(0,h.hj)(f))return{x:l,y:f,width:y,height:v};if((0,h.hj)(p)&&(0,h.hj)(d))return{x:p,y:d,width:y,height:v}}return(0,h.hj)(l)&&(0,h.hj)(f)?{x:l,y:f,width:0,height:0}:(0,h.hj)(e)&&(0,h.hj)(r)?{cx:e,cy:r,startAngle:o||n||0,endAngle:i||n||0,innerRadius:c||0,outerRadius:s||u||a||0,clockWise:m}:t.viewBox?t.viewBox:{}};P.parseViewBox=A,P.renderCallByParent=function(t,e){var r,o,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2];if(!t||!t.children&&i&&!t.label)return null;var a=t.children,c=A(t),l=(0,p.NN)(a,P).map(function(t,r){return(0,n.cloneElement)(t,{viewBox:e||c,key:"label-".concat(r)})});return i?[(r=t.label,o=e||c,r?!0===r?n.createElement(P,{key:"label-implicit",viewBox:o}):(0,h.P2)(r)?n.createElement(P,{key:"label-implicit",viewBox:o,value:r}):(0,n.isValidElement)(r)?r.type===P?(0,n.cloneElement)(r,{key:"label-implicit",viewBox:o}):n.createElement(P,{key:"label-implicit",content:r,viewBox:o}):u()(r)?n.createElement(P,{key:"label-implicit",content:r,viewBox:o}):s()(r)?n.createElement(P,x({viewBox:o},r,{key:"label-implicit"})):null:null)].concat(function(t){if(Array.isArray(t))return m(t)}(l)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(l)||function(t,e){if(t){if("string"==typeof t)return m(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return m(t,void 0)}}(l)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()):l}},22190:function(t,e,r){"use strict";r.d(e,{D:function(){return D}});var n=r(2265),o=r(86757),i=r.n(o),a=r(61994),u=r(1175),c=r(48777),s=r(14870),l=r(41637);function f(t){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p(){return(p=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function h(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function d(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(d=function(){return!!t})()}function y(t){return(y=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function v(t,e){return(v=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function m(t,e,r){return(e=b(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function b(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}var g=function(t){var e;function r(){var t,e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=y(t),function(t,e){if(e&&("object"===f(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,d()?Reflect.construct(t,e||[],y(this).constructor):t.apply(this,e))}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&v(t,e)}(r,t),e=[{key:"renderIcon",value:function(t){var e=this.props.inactiveColor,r=32/6,o=32/3,i=t.inactive?e:t.color;if("plainline"===t.type)return n.createElement("line",{strokeWidth:4,fill:"none",stroke:i,strokeDasharray:t.payload.strokeDasharray,x1:0,y1:16,x2:32,y2:16,className:"recharts-legend-icon"});if("line"===t.type)return n.createElement("path",{strokeWidth:4,fill:"none",stroke:i,d:"M0,".concat(16,"h").concat(o,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(2*o,",").concat(16,"\n            H").concat(32,"M").concat(2*o,",").concat(16,"\n            A").concat(r,",").concat(r,",0,1,1,").concat(o,",").concat(16),className:"recharts-legend-icon"});if("rect"===t.type)return n.createElement("path",{stroke:"none",fill:i,d:"M0,".concat(4,"h").concat(32,"v").concat(24,"h").concat(-32,"z"),className:"recharts-legend-icon"});if(n.isValidElement(t.legendIcon)){var a=function(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?h(Object(r),!0).forEach(function(e){m(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}({},t);return delete a.legendIcon,n.cloneElement(t.legendIcon,a)}return n.createElement(s.v,{fill:i,cx:16,cy:16,size:32,sizeType:"diameter",type:t.type})}},{key:"renderItems",value:function(){var t=this,e=this.props,r=e.payload,o=e.iconSize,s=e.layout,f=e.formatter,h=e.inactiveColor,d={x:0,y:0,width:32,height:32},y={display:"horizontal"===s?"inline-block":"block",marginRight:10},v={display:"inline-block",verticalAlign:"middle",marginRight:4};return r.map(function(e,r){var s=e.formatter||f,b=(0,a.Z)(m(m({"recharts-legend-item":!0},"legend-item-".concat(r),!0),"inactive",e.inactive));if("none"===e.type)return null;var g=i()(e.value)?null:e.value;(0,u.Z)(!i()(e.value),'The name property is also required when using a function for the dataKey of a chart\'s cartesian components. Ex: <Bar name="Name of my Data"/>');var x=e.inactive?h:e.color;return n.createElement("li",p({className:b,style:y,key:"legend-item-".concat(r)},(0,l.bw)(t.props,e,r)),n.createElement(c.T,{width:o,height:o,viewBox:d,style:v},t.renderIcon(e)),n.createElement("span",{className:"recharts-legend-item-text",style:{color:x}},s?s(g,e,r):g))})}},{key:"render",value:function(){var t=this.props,e=t.payload,r=t.layout,o=t.align;return e&&e.length?n.createElement("ul",{className:"recharts-default-legend",style:{padding:0,margin:0,textAlign:"horizontal"===r?o:"left"}},this.renderItems()):null}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,b(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.PureComponent);m(g,"displayName","Legend"),m(g,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"middle",inactiveColor:"#ccc"});var x=r(16630),O=r(93528);function w(t){return(w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var j=["ref"];function S(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function P(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?S(Object(r),!0).forEach(function(e){T(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):S(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function A(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,_(n.key),n)}}function E(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(E=function(){return!!t})()}function k(t){return(k=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function M(t,e){return(M=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function T(t,e,r){return(e=_(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function _(t){var e=function(t,e){if("object"!=w(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=w(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==w(e)?e:e+""}function C(t){return t.value}var D=function(t){var e,r;function o(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,o);for(var t,e,r,n=arguments.length,i=Array(n),a=0;a<n;a++)i[a]=arguments[a];return e=o,r=[].concat(i),e=k(e),T(t=function(t,e){if(e&&("object"===w(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,E()?Reflect.construct(e,r||[],k(this).constructor):e.apply(this,r)),"lastBoundingBox",{width:-1,height:-1}),t}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&M(t,e)}(o,t),e=[{key:"componentDidMount",value:function(){this.updateBBox()}},{key:"componentDidUpdate",value:function(){this.updateBBox()}},{key:"getBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();return t.height=this.wrapperNode.offsetHeight,t.width=this.wrapperNode.offsetWidth,t}return null}},{key:"updateBBox",value:function(){var t=this.props.onBBoxUpdate,e=this.getBBox();e?(Math.abs(e.width-this.lastBoundingBox.width)>1||Math.abs(e.height-this.lastBoundingBox.height)>1)&&(this.lastBoundingBox.width=e.width,this.lastBoundingBox.height=e.height,t&&t(e)):(-1!==this.lastBoundingBox.width||-1!==this.lastBoundingBox.height)&&(this.lastBoundingBox.width=-1,this.lastBoundingBox.height=-1,t&&t(null))}},{key:"getBBoxSnapshot",value:function(){return this.lastBoundingBox.width>=0&&this.lastBoundingBox.height>=0?P({},this.lastBoundingBox):{width:0,height:0}}},{key:"getDefaultPosition",value:function(t){var e,r,n=this.props,o=n.layout,i=n.align,a=n.verticalAlign,u=n.margin,c=n.chartWidth,s=n.chartHeight;return t&&(void 0!==t.left&&null!==t.left||void 0!==t.right&&null!==t.right)||(e="center"===i&&"vertical"===o?{left:((c||0)-this.getBBoxSnapshot().width)/2}:"right"===i?{right:u&&u.right||0}:{left:u&&u.left||0}),t&&(void 0!==t.top&&null!==t.top||void 0!==t.bottom&&null!==t.bottom)||(r="middle"===a?{top:((s||0)-this.getBBoxSnapshot().height)/2}:"bottom"===a?{bottom:u&&u.bottom||0}:{top:u&&u.top||0}),P(P({},e),r)}},{key:"render",value:function(){var t=this,e=this.props,r=e.content,o=e.width,i=e.height,a=e.wrapperStyle,u=e.payloadUniqBy,c=e.payload,s=P(P({position:"absolute",width:o||"auto",height:i||"auto"},this.getDefaultPosition(a)),a);return n.createElement("div",{className:"recharts-legend-wrapper",style:s,ref:function(e){t.wrapperNode=e}},function(t,e){if(n.isValidElement(t))return n.cloneElement(t,e);if("function"==typeof t)return n.createElement(t,e);e.ref;var r=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(e,j);return n.createElement(g,r)}(r,P(P({},this.props),{},{payload:(0,O.z)(c,u,C)})))}}],r=[{key:"getWithHeight",value:function(t,e){var r=P(P({},this.defaultProps),t.props).layout;return"vertical"===r&&(0,x.hj)(t.props.height)?{height:t.props.height}:"horizontal"===r?{width:t.props.width||e}:null}}],e&&A(o.prototype,e),r&&A(o,r),Object.defineProperty(o,"prototype",{writable:!1}),o}(n.PureComponent);T(D,"displayName","Legend"),T(D,"defaultProps",{iconSize:14,layout:"horizontal",align:"center",verticalAlign:"bottom"})},47625:function(t,e,r){"use strict";r.d(e,{h:function(){return d}});var n=r(61994),o=r(2265),i=r(37065),a=r.n(i),u=r(16630),c=r(1175),s=r(82944);function l(t){return(l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function f(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function p(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?f(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=l(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=l(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==l(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):f(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var d=(0,o.forwardRef)(function(t,e){var r,i=t.aspect,l=t.initialDimension,f=void 0===l?{width:-1,height:-1}:l,d=t.width,y=void 0===d?"100%":d,v=t.height,m=void 0===v?"100%":v,b=t.minWidth,g=void 0===b?0:b,x=t.minHeight,O=t.maxHeight,w=t.children,j=t.debounce,S=void 0===j?0:j,P=t.id,A=t.className,E=t.onResize,k=t.style,M=(0,o.useRef)(null),T=(0,o.useRef)();T.current=E,(0,o.useImperativeHandle)(e,function(){return Object.defineProperty(M.current,"current",{get:function(){return console.warn("The usage of ref.current.current is deprecated and will no longer be supported."),M.current},configurable:!0})});var _=function(t){if(Array.isArray(t))return t}(r=(0,o.useState)({containerWidth:f.width,containerHeight:f.height}))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,s=!1;try{for(i=(r=r.call(t)).next;!(c=(n=i.call(r)).done)&&(u.push(n.value),2!==u.length);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(r,2)||function(t,e){if(t){if("string"==typeof t)return h(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return h(t,2)}}(r,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),C=_[0],D=_[1],I=(0,o.useCallback)(function(t,e){D(function(r){var n=Math.round(t),o=Math.round(e);return r.containerWidth===n&&r.containerHeight===o?r:{containerWidth:n,containerHeight:o}})},[]);(0,o.useEffect)(function(){var t=function(t){var e,r=t[0].contentRect,n=r.width,o=r.height;I(n,o),null===(e=T.current)||void 0===e||e.call(T,n,o)};S>0&&(t=a()(t,S,{trailing:!0,leading:!1}));var e=new ResizeObserver(t),r=M.current.getBoundingClientRect();return I(r.width,r.height),e.observe(M.current),function(){e.disconnect()}},[I,S]);var N=(0,o.useMemo)(function(){var t=C.containerWidth,e=C.containerHeight;if(t<0||e<0)return null;(0,c.Z)((0,u.hU)(y)||(0,u.hU)(m),"The width(%s) and height(%s) are both fixed numbers,\n       maybe you don't need to use a ResponsiveContainer.",y,m),(0,c.Z)(!i||i>0,"The aspect(%s) must be greater than zero.",i);var r=(0,u.hU)(y)?t:y,n=(0,u.hU)(m)?e:m;i&&i>0&&(r?n=r/i:n&&(r=n*i),O&&n>O&&(n=O)),(0,c.Z)(r>0||n>0,"The width(%s) and height(%s) of chart should be greater than 0,\n       please check the style of container, or the props width(%s) and height(%s),\n       or add a minWidth(%s) or minHeight(%s) or use aspect(%s) to control the\n       height and width.",r,n,y,m,g,x,i);var a=!Array.isArray(w)&&(0,s.Gf)(w.type).endsWith("Chart");return o.Children.map(w,function(t){return o.isValidElement(t)?(0,o.cloneElement)(t,p({width:r,height:n},a?{style:p({height:"100%",width:"100%",maxHeight:n,maxWidth:r},t.props.style)}:{})):t})},[i,w,m,O,x,g,C,y]);return o.createElement("div",{id:P?"".concat(P):void 0,className:(0,n.Z)("recharts-responsive-container",A),style:p(p({},void 0===k?{}:k),{},{width:y,height:m,minWidth:g,minHeight:x,maxHeight:O}),ref:M},N)})},58811:function(t,e,r){"use strict";r.d(e,{x:function(){return L}});var n=r(2265),o=r(77571),i=r.n(o),a=r(61994),u=r(16630),c=r(34067),s=r(82944),l=r(4094);function f(t){return(f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function p(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(t,e)||function(t,e){if(t){if("string"==typeof t)return h(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return h(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function d(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,function(t){var e=function(t,e){if("object"!=f(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=f(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==f(e)?e:e+""}(n.key),n)}}var y=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([*/])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,v=/(-?\d+(?:\.\d+)?[a-zA-Z%]*)([+-])(-?\d+(?:\.\d+)?[a-zA-Z%]*)/,m=/^px|cm|vh|vw|em|rem|%|mm|in|pt|pc|ex|ch|vmin|vmax|Q$/,b=/(-?\d+(?:\.\d+)?)([a-zA-Z%]+)?/,g={cm:96/2.54,mm:96/25.4,pt:96/72,pc:16,in:96,Q:96/101.6,px:1},x=Object.keys(g),O=function(){var t,e;function r(t,e){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),this.num=t,this.unit=e,this.num=t,this.unit=e,Number.isNaN(t)&&(this.unit=""),""===e||m.test(e)||(this.num=NaN,this.unit=""),x.includes(e)&&(this.num=t*g[e],this.unit="px")}return t=[{key:"add",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num+t.num,this.unit)}},{key:"subtract",value:function(t){return this.unit!==t.unit?new r(NaN,""):new r(this.num-t.num,this.unit)}},{key:"multiply",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num*t.num,this.unit||t.unit)}},{key:"divide",value:function(t){return""!==this.unit&&""!==t.unit&&this.unit!==t.unit?new r(NaN,""):new r(this.num/t.num,this.unit||t.unit)}},{key:"toString",value:function(){return"".concat(this.num).concat(this.unit)}},{key:"isNaN",value:function(){return Number.isNaN(this.num)}}],e=[{key:"parse",value:function(t){var e,n=p(null!==(e=b.exec(t))&&void 0!==e?e:[],3),o=n[1],i=n[2];return new r(parseFloat(o),null!=i?i:"")}}],t&&d(r.prototype,t),e&&d(r,e),Object.defineProperty(r,"prototype",{writable:!1}),r}();function w(t){if(t.includes("NaN"))return"NaN";for(var e=t;e.includes("*")||e.includes("/");){var r,n=p(null!==(r=y.exec(e))&&void 0!==r?r:[],4),o=n[1],i=n[2],a=n[3],u=O.parse(null!=o?o:""),c=O.parse(null!=a?a:""),s="*"===i?u.multiply(c):u.divide(c);if(s.isNaN())return"NaN";e=e.replace(y,s.toString())}for(;e.includes("+")||/.-\d+(?:\.\d+)?/.test(e);){var l,f=p(null!==(l=v.exec(e))&&void 0!==l?l:[],4),h=f[1],d=f[2],m=f[3],b=O.parse(null!=h?h:""),g=O.parse(null!=m?m:""),x="+"===d?b.add(g):b.subtract(g);if(x.isNaN())return"NaN";e=e.replace(v,x.toString())}return e}var j=/\(([^()]*)\)/;function S(t){var e=function(t){try{var e;return e=t.replace(/\s+/g,""),e=function(t){for(var e=t;e.includes("(");){var r=p(j.exec(e),2)[1];e=e.replace(j,w(r))}return e}(e),e=w(e)}catch(t){return"NaN"}}(t.slice(5,-1));return"NaN"===e?"":e}var P=["x","y","lineHeight","capHeight","scaleToFit","textAnchor","verticalAnchor","fill"],A=["dx","dy","angle","className","breakAll"];function E(){return(E=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function k(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function M(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(t,e)||function(t,e){if(t){if("string"==typeof t)return T(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return T(t,e)}}(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function T(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var _=/[ \f\n\r\t\v\u2028\u2029]+/,C=function(t){var e=t.children,r=t.breakAll,n=t.style;try{var o=[];i()(e)||(o=r?e.toString().split(""):e.toString().split(_));var a=o.map(function(t){return{word:t,width:(0,l.xE)(t,n).width}}),u=r?0:(0,l.xE)("\xa0",n).width;return{wordsWithComputedWidth:a,spaceWidth:u}}catch(t){return null}},D=function(t,e,r,n,o){var i,a=t.maxLines,c=t.children,s=t.style,l=t.breakAll,f=(0,u.hj)(a),p=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.reduce(function(t,e){var i=e.word,a=e.width,u=t[t.length-1];return u&&(null==n||o||u.width+a+r<Number(n))?(u.words.push(i),u.width+=a+r):t.push({words:[i],width:a}),t},[])},h=p(e);if(!f)return h;for(var d=function(t){var e=p(C({breakAll:l,style:s,children:c.slice(0,t)+"…"}).wordsWithComputedWidth);return[e.length>a||e.reduce(function(t,e){return t.width>e.width?t:e}).width>Number(n),e]},y=0,v=c.length-1,m=0;y<=v&&m<=c.length-1;){var b=Math.floor((y+v)/2),g=M(d(b-1),2),x=g[0],O=g[1],w=M(d(b),1)[0];if(x||w||(y=b+1),x&&w&&(v=b-1),!x&&w){i=O;break}m++}return i||h},I=function(t){return[{words:i()(t)?[]:t.toString().split(_)}]},N=function(t){var e=t.width,r=t.scaleToFit,n=t.children,o=t.style,i=t.breakAll,a=t.maxLines;if((e||r)&&!c.x.isSsr){var u=C({breakAll:i,children:n,style:o});return u?D({breakAll:i,children:n,maxLines:a,style:o},u.wordsWithComputedWidth,u.spaceWidth,e,r):I(n)}return I(n)},B="#808080",L=function(t){var e,r=t.x,o=void 0===r?0:r,i=t.y,c=void 0===i?0:i,l=t.lineHeight,f=void 0===l?"1em":l,p=t.capHeight,h=void 0===p?"0.71em":p,d=t.scaleToFit,y=void 0!==d&&d,v=t.textAnchor,m=t.verticalAnchor,b=t.fill,g=void 0===b?B:b,x=k(t,P),O=(0,n.useMemo)(function(){return N({breakAll:x.breakAll,children:x.children,maxLines:x.maxLines,scaleToFit:y,style:x.style,width:x.width})},[x.breakAll,x.children,x.maxLines,y,x.style,x.width]),w=x.dx,j=x.dy,M=x.angle,T=x.className,_=x.breakAll,C=k(x,A);if(!(0,u.P2)(o)||!(0,u.P2)(c))return null;var D=o+((0,u.hj)(w)?w:0),I=c+((0,u.hj)(j)?j:0);switch(void 0===m?"end":m){case"start":e=S("calc(".concat(h,")"));break;case"middle":e=S("calc(".concat((O.length-1)/2," * -").concat(f," + (").concat(h," / 2))"));break;default:e=S("calc(".concat(O.length-1," * -").concat(f,")"))}var L=[];if(y){var R=O[0].width,z=x.width;L.push("scale(".concat(((0,u.hj)(z)?z/R:1)/R,")"))}return M&&L.push("rotate(".concat(M,", ").concat(D,", ").concat(I,")")),L.length&&(C.transform=L.join(" ")),n.createElement("text",E({},(0,s.L6)(C,!0),{x:D,y:I,className:(0,a.Z)("recharts-text",T),textAnchor:void 0===v?"start":v,fill:g.includes("url")?B:g}),O.map(function(t,r){var o=t.words.join(_?"":" ");return n.createElement("tspan",{x:D,dy:0===r?e:f,key:"".concat(o,"-").concat(r)},o)}))}},8147:function(t,e,r){"use strict";r.d(e,{u:function(){return F}});var n=r(2265),o=r(34935),i=r.n(o),a=r(77571),u=r.n(a),c=r(61994),s=r(16630);function l(t){return(l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function f(){return(f=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function p(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function h(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function d(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?h(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=l(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=l(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==l(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):h(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function y(t){return Array.isArray(t)&&(0,s.P2)(t[0])&&(0,s.P2)(t[1])?t.join(" ~ "):t}var v=function(t){var e=t.separator,r=void 0===e?" : ":e,o=t.contentStyle,a=t.itemStyle,l=void 0===a?{}:a,h=t.labelStyle,v=t.payload,m=t.formatter,b=t.itemSorter,g=t.wrapperClassName,x=t.labelClassName,O=t.label,w=t.labelFormatter,j=t.accessibilityLayer,S=d({margin:0,padding:10,backgroundColor:"#fff",border:"1px solid #ccc",whiteSpace:"nowrap"},void 0===o?{}:o),P=d({margin:0},void 0===h?{}:h),A=!u()(O),E=A?O:"",k=(0,c.Z)("recharts-default-tooltip",g),M=(0,c.Z)("recharts-tooltip-label",x);return A&&w&&null!=v&&(E=w(O,v)),n.createElement("div",f({className:k,style:S},void 0!==j&&j?{role:"status","aria-live":"assertive"}:{}),n.createElement("p",{className:M,style:P},n.isValidElement(E)?E:"".concat(E)),function(){if(v&&v.length){var t=(b?i()(v,b):v).map(function(t,e){if("none"===t.type)return null;var o=d({display:"block",paddingTop:4,paddingBottom:4,color:t.color||"#000"},l),i=t.formatter||m||y,a=t.value,u=t.name,c=a,f=u;if(i&&null!=c&&null!=f){var h=i(a,u,t,e,v);if(Array.isArray(h)){var b=function(t){if(Array.isArray(t))return t}(h)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,s=!1;try{for(i=(r=r.call(t)).next;!(c=(n=i.call(r)).done)&&(u.push(n.value),2!==u.length);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(h,2)||function(t,e){if(t){if("string"==typeof t)return p(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return p(t,2)}}(h,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}();c=b[0],f=b[1]}else c=h}return n.createElement("li",{className:"recharts-tooltip-item",key:"tooltip-item-".concat(e),style:o},(0,s.P2)(f)?n.createElement("span",{className:"recharts-tooltip-item-name"},f):null,(0,s.P2)(f)?n.createElement("span",{className:"recharts-tooltip-item-separator"},r):null,n.createElement("span",{className:"recharts-tooltip-item-value"},c),n.createElement("span",{className:"recharts-tooltip-item-unit"},t.unit||""))});return n.createElement("ul",{className:"recharts-tooltip-item-list",style:{padding:0,margin:0}},t)}return null}())};function m(t){return(m="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function b(t,e,r){var n;return(n=function(t,e){if("object"!=m(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=m(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==m(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var g="recharts-tooltip-wrapper",x={visibility:"hidden"};function O(t){var e=t.allowEscapeViewBox,r=t.coordinate,n=t.key,o=t.offsetTopLeft,i=t.position,a=t.reverseDirection,u=t.tooltipDimension,c=t.viewBox,l=t.viewBoxDimension;if(i&&(0,s.hj)(i[n]))return i[n];var f=r[n]-u-o,p=r[n]+o;return e[n]?a[n]?f:p:a[n]?f<c[n]?Math.max(p,c[n]):Math.max(f,c[n]):p+u>c[n]+l?Math.max(f,c[n]):Math.max(p,c[n])}function w(t){return(w="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function j(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function S(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?j(Object(r),!0).forEach(function(e){k(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):j(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function P(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(P=function(){return!!t})()}function A(t){return(A=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function E(t,e){return(E=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function k(t,e,r){return(e=M(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function M(t){var e=function(t,e){if("object"!=w(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=w(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==w(e)?e:e+""}var T=function(t){var e;function r(){!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r);for(var t,e,n,o=arguments.length,i=Array(o),a=0;a<o;a++)i[a]=arguments[a];return e=r,n=[].concat(i),e=A(e),k(t=function(t,e){if(e&&("object"===w(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,P()?Reflect.construct(e,n||[],A(this).constructor):e.apply(this,n)),"state",{dismissed:!1,dismissedAtCoordinate:{x:0,y:0},lastBoundingBox:{width:-1,height:-1}}),k(t,"handleKeyDown",function(e){if("Escape"===e.key){var r,n,o,i;t.setState({dismissed:!0,dismissedAtCoordinate:{x:null!==(r=null===(n=t.props.coordinate)||void 0===n?void 0:n.x)&&void 0!==r?r:0,y:null!==(o=null===(i=t.props.coordinate)||void 0===i?void 0:i.y)&&void 0!==o?o:0}})}}),t}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&E(t,e)}(r,t),e=[{key:"updateBBox",value:function(){if(this.wrapperNode&&this.wrapperNode.getBoundingClientRect){var t=this.wrapperNode.getBoundingClientRect();(Math.abs(t.width-this.state.lastBoundingBox.width)>1||Math.abs(t.height-this.state.lastBoundingBox.height)>1)&&this.setState({lastBoundingBox:{width:t.width,height:t.height}})}else(-1!==this.state.lastBoundingBox.width||-1!==this.state.lastBoundingBox.height)&&this.setState({lastBoundingBox:{width:-1,height:-1}})}},{key:"componentDidMount",value:function(){document.addEventListener("keydown",this.handleKeyDown),this.updateBBox()}},{key:"componentWillUnmount",value:function(){document.removeEventListener("keydown",this.handleKeyDown)}},{key:"componentDidUpdate",value:function(){var t,e;this.props.active&&this.updateBBox(),this.state.dismissed&&((null===(t=this.props.coordinate)||void 0===t?void 0:t.x)!==this.state.dismissedAtCoordinate.x||(null===(e=this.props.coordinate)||void 0===e?void 0:e.y)!==this.state.dismissedAtCoordinate.y)&&(this.state.dismissed=!1)}},{key:"render",value:function(){var t,e,r,o,i,a,u,l,f,p,h,d,y,v,m,w,j,P,A,E=this,k=this.props,M=k.active,T=k.allowEscapeViewBox,_=k.animationDuration,C=k.animationEasing,D=k.children,I=k.coordinate,N=k.hasPayload,B=k.isAnimationActive,L=k.offset,R=k.position,z=k.reverseDirection,U=k.useTranslate3d,$=k.viewBox,F=k.wrapperStyle,Z=(d=(t={allowEscapeViewBox:T,coordinate:I,offsetTopLeft:L,position:R,reverseDirection:z,tooltipBox:this.state.lastBoundingBox,useTranslate3d:U,viewBox:$}).allowEscapeViewBox,y=t.coordinate,v=t.offsetTopLeft,m=t.position,w=t.reverseDirection,j=t.tooltipBox,P=t.useTranslate3d,A=t.viewBox,j.height>0&&j.width>0&&y?(r=(e={translateX:p=O({allowEscapeViewBox:d,coordinate:y,key:"x",offsetTopLeft:v,position:m,reverseDirection:w,tooltipDimension:j.width,viewBox:A,viewBoxDimension:A.width}),translateY:h=O({allowEscapeViewBox:d,coordinate:y,key:"y",offsetTopLeft:v,position:m,reverseDirection:w,tooltipDimension:j.height,viewBox:A,viewBoxDimension:A.height}),useTranslate3d:P}).translateX,o=e.translateY,f={transform:e.useTranslate3d?"translate3d(".concat(r,"px, ").concat(o,"px, 0)"):"translate(".concat(r,"px, ").concat(o,"px)")}):f=x,{cssProperties:f,cssClasses:(a=(i={translateX:p,translateY:h,coordinate:y}).coordinate,u=i.translateX,l=i.translateY,(0,c.Z)(g,b(b(b(b({},"".concat(g,"-right"),(0,s.hj)(u)&&a&&(0,s.hj)(a.x)&&u>=a.x),"".concat(g,"-left"),(0,s.hj)(u)&&a&&(0,s.hj)(a.x)&&u<a.x),"".concat(g,"-bottom"),(0,s.hj)(l)&&a&&(0,s.hj)(a.y)&&l>=a.y),"".concat(g,"-top"),(0,s.hj)(l)&&a&&(0,s.hj)(a.y)&&l<a.y)))}),W=Z.cssClasses,q=Z.cssProperties,Y=S(S({transition:B&&M?"transform ".concat(_,"ms ").concat(C):void 0},q),{},{pointerEvents:"none",visibility:!this.state.dismissed&&M&&N?"visible":"hidden",position:"absolute",top:0,left:0},F);return n.createElement("div",{tabIndex:-1,className:W,style:Y,ref:function(t){E.wrapperNode=t}},D)}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,M(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.PureComponent),_=r(34067),C=r(93528);function D(t){return(D="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function I(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function N(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?I(Object(r),!0).forEach(function(e){z(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):I(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function B(){try{var t=!Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){}))}catch(t){}return(B=function(){return!!t})()}function L(t){return(L=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function R(t,e){return(R=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}function z(t,e,r){return(e=U(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function U(t){var e=function(t,e){if("object"!=D(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=D(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==D(e)?e:e+""}function $(t){return t.dataKey}var F=function(t){var e;function r(){var t,e;return!function(t,e){if(!(t instanceof e))throw TypeError("Cannot call a class as a function")}(this,r),t=r,e=arguments,t=L(t),function(t,e){if(e&&("object"===D(e)||"function"==typeof e))return e;if(void 0!==e)throw TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,B()?Reflect.construct(t,e||[],L(this).constructor):t.apply(this,e))}return!function(t,e){if("function"!=typeof e&&null!==e)throw TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&R(t,e)}(r,t),e=[{key:"render",value:function(){var t,e=this,r=this.props,o=r.active,i=r.allowEscapeViewBox,a=r.animationDuration,u=r.animationEasing,c=r.content,s=r.coordinate,l=r.filterNull,f=r.isAnimationActive,p=r.offset,h=r.payload,d=r.payloadUniqBy,y=r.position,m=r.reverseDirection,b=r.useTranslate3d,g=r.viewBox,x=r.wrapperStyle,O=null!=h?h:[];l&&O.length&&(O=(0,C.z)(h.filter(function(t){return null!=t.value&&(!0!==t.hide||e.props.includeHidden)}),d,$));var w=O.length>0;return n.createElement(T,{allowEscapeViewBox:i,animationDuration:a,animationEasing:u,isAnimationActive:f,active:o,coordinate:s,hasPayload:w,offset:p,position:y,reverseDirection:m,useTranslate3d:b,viewBox:g,wrapperStyle:x},(t=N(N({},this.props),{},{payload:O}),n.isValidElement(c)?n.cloneElement(c,t):"function"==typeof c?n.createElement(c,t):n.createElement(v,t)))}}],function(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,U(n.key),n)}}(r.prototype,e),Object.defineProperty(r,"prototype",{writable:!1}),r}(n.PureComponent);z(F,"displayName","Tooltip"),z(F,"defaultProps",{accessibilityLayer:!1,allowEscapeViewBox:{x:!1,y:!1},animationDuration:400,animationEasing:"ease",contentStyle:{},coordinate:{x:0,y:0},cursor:!0,cursorStyle:{},filterNull:!0,isAnimationActive:!_.x.isSsr,itemStyle:{},labelStyle:{},offset:10,reverseDirection:{x:!1,y:!1},separator:" : ",trigger:"hover",useTranslate3d:!1,viewBox:{x:0,y:0,height:0,width:0},wrapperStyle:{}})},9841:function(t,e,r){"use strict";r.d(e,{m:function(){return c}});var n=r(2265),o=r(61994),i=r(82944),a=["children","className"];function u(){return(u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}var c=n.forwardRef(function(t,e){var r=t.children,c=t.className,s=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,a),l=(0,o.Z)("recharts-layer",c);return n.createElement("g",u({className:l},(0,i.L6)(s,!0),{ref:e}),r)})},48777:function(t,e,r){"use strict";r.d(e,{T:function(){return c}});var n=r(2265),o=r(61994),i=r(82944),a=["children","width","height","viewBox","className","style","title","desc"];function u(){return(u=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function c(t){var e=t.children,r=t.width,c=t.height,s=t.viewBox,l=t.className,f=t.style,p=t.title,h=t.desc,d=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,a),y=s||{width:r,height:c,x:0,y:0},v=(0,o.Z)("recharts-surface",l);return n.createElement("svg",u({},(0,i.L6)(d,!0,"svg"),{className:v,width:r,height:c,style:f,viewBox:"".concat(y.x," ").concat(y.y," ").concat(y.width," ").concat(y.height)}),n.createElement("title",null,p),n.createElement("desc",null,h),e)}},73649:function(t,e,r){"use strict";r.d(e,{A:function(){return y},X:function(){return h}});var n=r(2265),o=r(61994),i=r(84735),a=r(82944);function u(t){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function c(){return(c=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function s(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function l(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function f(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?l(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=u(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=u(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==u(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var p=function(t,e,r,n,o){var i,a=Math.min(Math.abs(r)/2,Math.abs(n)/2),u=n>=0?1:-1,c=r>=0?1:-1,s=n>=0&&r>=0||n<0&&r<0?1:0;if(a>0&&o instanceof Array){for(var l=[0,0,0,0],f=0;f<4;f++)l[f]=o[f]>a?a:o[f];i="M".concat(t,",").concat(e+u*l[0]),l[0]>0&&(i+="A ".concat(l[0],",").concat(l[0],",0,0,").concat(s,",").concat(t+c*l[0],",").concat(e)),i+="L ".concat(t+r-c*l[1],",").concat(e),l[1]>0&&(i+="A ".concat(l[1],",").concat(l[1],",0,0,").concat(s,",\n        ").concat(t+r,",").concat(e+u*l[1])),i+="L ".concat(t+r,",").concat(e+n-u*l[2]),l[2]>0&&(i+="A ".concat(l[2],",").concat(l[2],",0,0,").concat(s,",\n        ").concat(t+r-c*l[2],",").concat(e+n)),i+="L ".concat(t+c*l[3],",").concat(e+n),l[3]>0&&(i+="A ".concat(l[3],",").concat(l[3],",0,0,").concat(s,",\n        ").concat(t,",").concat(e+n-u*l[3])),i+="Z"}else if(a>0&&o===+o&&o>0){var p=Math.min(a,o);i="M ".concat(t,",").concat(e+u*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(s,",").concat(t+c*p,",").concat(e,"\n            L ").concat(t+r-c*p,",").concat(e,"\n            A ").concat(p,",").concat(p,",0,0,").concat(s,",").concat(t+r,",").concat(e+u*p,"\n            L ").concat(t+r,",").concat(e+n-u*p,"\n            A ").concat(p,",").concat(p,",0,0,").concat(s,",").concat(t+r-c*p,",").concat(e+n,"\n            L ").concat(t+c*p,",").concat(e+n,"\n            A ").concat(p,",").concat(p,",0,0,").concat(s,",").concat(t,",").concat(e+n-u*p," Z")}else i="M ".concat(t,",").concat(e," h ").concat(r," v ").concat(n," h ").concat(-r," Z");return i},h=function(t,e){if(!t||!e)return!1;var r=t.x,n=t.y,o=e.x,i=e.y,a=e.width,u=e.height;return!!(Math.abs(a)>0&&Math.abs(u)>0)&&r>=Math.min(o,o+a)&&r<=Math.max(o,o+a)&&n>=Math.min(i,i+u)&&n<=Math.max(i,i+u)},d={x:0,y:0,width:0,height:0,radius:0,isAnimationActive:!1,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},y=function(t){var e,r=f(f({},d),t),u=(0,n.useRef)(),l=function(t){if(Array.isArray(t))return t}(e=(0,n.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,s=!1;try{for(i=(r=r.call(t)).next;!(c=(n=i.call(r)).done)&&(u.push(n.value),2!==u.length);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(e,2)||function(t,e){if(t){if("string"==typeof t)return s(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return s(t,2)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),h=l[0],y=l[1];(0,n.useEffect)(function(){if(u.current&&u.current.getTotalLength)try{var t=u.current.getTotalLength();t&&y(t)}catch(t){}},[]);var v=r.x,m=r.y,b=r.width,g=r.height,x=r.radius,O=r.className,w=r.animationEasing,j=r.animationDuration,S=r.animationBegin,P=r.isAnimationActive,A=r.isUpdateAnimationActive;if(v!==+v||m!==+m||b!==+b||g!==+g||0===b||0===g)return null;var E=(0,o.Z)("recharts-rectangle",O);return A?n.createElement(i.ZP,{canBegin:h>0,from:{width:b,height:g,x:v,y:m},to:{width:b,height:g,x:v,y:m},duration:j,animationEasing:w,isActive:A},function(t){var e=t.width,o=t.height,s=t.x,l=t.y;return n.createElement(i.ZP,{canBegin:h>0,from:"0px ".concat(-1===h?1:h,"px"),to:"".concat(h,"px 0px"),attributeName:"strokeDasharray",begin:S,duration:j,isActive:P,easing:w},n.createElement("path",c({},(0,a.L6)(r,!0),{className:E,d:p(s,l,e,o,x),ref:u})))}):n.createElement("path",c({},(0,a.L6)(r,!0),{className:E,d:p(v,m,b,g,x)}))}},60474:function(t,e,r){"use strict";r.d(e,{L:function(){return v}});var n=r(2265),o=r(61994),i=r(82944),a=r(39206),u=r(16630);function c(t){return(c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s(){return(s=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function l(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function f(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?l(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=c(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=c(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==c(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var p=function(t){var e=t.cx,r=t.cy,n=t.radius,o=t.angle,i=t.sign,u=t.isExternal,c=t.cornerRadius,s=t.cornerIsExternal,l=c*(u?1:-1)+n,f=Math.asin(c/l)/a.Wk,p=s?o:o+i*f;return{center:(0,a.op)(e,r,l,p),circleTangency:(0,a.op)(e,r,n,p),lineTangency:(0,a.op)(e,r,l*Math.cos(f*a.Wk),s?o-i*f:o),theta:f}},h=function(t){var e,r=t.cx,n=t.cy,o=t.innerRadius,i=t.outerRadius,c=t.startAngle,s=(e=t.endAngle,(0,u.uY)(e-c)*Math.min(Math.abs(e-c),359.999)),l=c+s,f=(0,a.op)(r,n,i,c),p=(0,a.op)(r,n,i,l),h="M ".concat(f.x,",").concat(f.y,"\n    A ").concat(i,",").concat(i,",0,\n    ").concat(+(Math.abs(s)>180),",").concat(+(c>l),",\n    ").concat(p.x,",").concat(p.y,"\n  ");if(o>0){var d=(0,a.op)(r,n,o,c),y=(0,a.op)(r,n,o,l);h+="L ".concat(y.x,",").concat(y.y,"\n            A ").concat(o,",").concat(o,",0,\n            ").concat(+(Math.abs(s)>180),",").concat(+(c<=l),",\n            ").concat(d.x,",").concat(d.y," Z")}else h+="L ".concat(r,",").concat(n," Z");return h},d=function(t){var e=t.cx,r=t.cy,n=t.innerRadius,o=t.outerRadius,i=t.cornerRadius,a=t.forceCornerRadius,c=t.cornerIsExternal,s=t.startAngle,l=t.endAngle,f=(0,u.uY)(l-s),d=p({cx:e,cy:r,radius:o,angle:s,sign:f,cornerRadius:i,cornerIsExternal:c}),y=d.circleTangency,v=d.lineTangency,m=d.theta,b=p({cx:e,cy:r,radius:o,angle:l,sign:-f,cornerRadius:i,cornerIsExternal:c}),g=b.circleTangency,x=b.lineTangency,O=b.theta,w=c?Math.abs(s-l):Math.abs(s-l)-m-O;if(w<0)return a?"M ".concat(v.x,",").concat(v.y,"\n        a").concat(i,",").concat(i,",0,0,1,").concat(2*i,",0\n        a").concat(i,",").concat(i,",0,0,1,").concat(-(2*i),",0\n      "):h({cx:e,cy:r,innerRadius:n,outerRadius:o,startAngle:s,endAngle:l});var j="M ".concat(v.x,",").concat(v.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(y.x,",").concat(y.y,"\n    A").concat(o,",").concat(o,",0,").concat(+(w>180),",").concat(+(f<0),",").concat(g.x,",").concat(g.y,"\n    A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(x.x,",").concat(x.y,"\n  ");if(n>0){var S=p({cx:e,cy:r,radius:n,angle:s,sign:f,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),P=S.circleTangency,A=S.lineTangency,E=S.theta,k=p({cx:e,cy:r,radius:n,angle:l,sign:-f,isExternal:!0,cornerRadius:i,cornerIsExternal:c}),M=k.circleTangency,T=k.lineTangency,_=k.theta,C=c?Math.abs(s-l):Math.abs(s-l)-E-_;if(C<0&&0===i)return"".concat(j,"L").concat(e,",").concat(r,"Z");j+="L".concat(T.x,",").concat(T.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(M.x,",").concat(M.y,"\n      A").concat(n,",").concat(n,",0,").concat(+(C>180),",").concat(+(f>0),",").concat(P.x,",").concat(P.y,"\n      A").concat(i,",").concat(i,",0,0,").concat(+(f<0),",").concat(A.x,",").concat(A.y,"Z")}else j+="L".concat(e,",").concat(r,"Z");return j},y={cx:0,cy:0,innerRadius:0,outerRadius:0,startAngle:0,endAngle:0,cornerRadius:0,forceCornerRadius:!1,cornerIsExternal:!1},v=function(t){var e,r=f(f({},y),t),a=r.cx,c=r.cy,l=r.innerRadius,p=r.outerRadius,v=r.cornerRadius,m=r.forceCornerRadius,b=r.cornerIsExternal,g=r.startAngle,x=r.endAngle,O=r.className;if(p<l||g===x)return null;var w=(0,o.Z)("recharts-sector",O),j=p-l,S=(0,u.h1)(v,j,0,!0);return e=S>0&&360>Math.abs(g-x)?d({cx:a,cy:c,innerRadius:l,outerRadius:p,cornerRadius:Math.min(S,j/2),forceCornerRadius:m,cornerIsExternal:b,startAngle:g,endAngle:x}):h({cx:a,cy:c,innerRadius:l,outerRadius:p,startAngle:g,endAngle:x}),n.createElement("path",s({},(0,i.L6)(r,!0),{className:w,d:e,role:"img"}))}},14870:function(t,e,r){"use strict";r.d(e,{v:function(){return D}});var n=r(2265),o=r(75551),i=r.n(o);let a=Math.cos,u=Math.sin,c=Math.sqrt,s=Math.PI,l=2*s;var f={draw(t,e){let r=c(e/s);t.moveTo(r,0),t.arc(0,0,r,0,l)}};let p=c(1/3),h=2*p,d=u(s/10)/u(7*s/10),y=u(l/10)*d,v=-a(l/10)*d,m=c(3),b=c(3)/2,g=1/c(12),x=(g/2+1)*3;var O=r(76115),w=r(67790);c(3),c(3);var j=r(61994),S=r(82944);function P(t){return(P="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var A=["type","size","sizeType"];function E(){return(E=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function k(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function M(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?k(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=P(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=P(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==P(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):k(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var T={symbolCircle:f,symbolCross:{draw(t,e){let r=c(e/5)/2;t.moveTo(-3*r,-r),t.lineTo(-r,-r),t.lineTo(-r,-3*r),t.lineTo(r,-3*r),t.lineTo(r,-r),t.lineTo(3*r,-r),t.lineTo(3*r,r),t.lineTo(r,r),t.lineTo(r,3*r),t.lineTo(-r,3*r),t.lineTo(-r,r),t.lineTo(-3*r,r),t.closePath()}},symbolDiamond:{draw(t,e){let r=c(e/h),n=r*p;t.moveTo(0,-r),t.lineTo(n,0),t.lineTo(0,r),t.lineTo(-n,0),t.closePath()}},symbolSquare:{draw(t,e){let r=c(e),n=-r/2;t.rect(n,n,r,r)}},symbolStar:{draw(t,e){let r=c(.8908130915292852*e),n=y*r,o=v*r;t.moveTo(0,-r),t.lineTo(n,o);for(let e=1;e<5;++e){let i=l*e/5,c=a(i),s=u(i);t.lineTo(s*r,-c*r),t.lineTo(c*n-s*o,s*n+c*o)}t.closePath()}},symbolTriangle:{draw(t,e){let r=-c(e/(3*m));t.moveTo(0,2*r),t.lineTo(-m*r,-r),t.lineTo(m*r,-r),t.closePath()}},symbolWye:{draw(t,e){let r=c(e/x),n=r/2,o=r*g,i=r*g+r,a=-n;t.moveTo(n,o),t.lineTo(n,i),t.lineTo(a,i),t.lineTo(-.5*n-b*o,b*n+-.5*o),t.lineTo(-.5*n-b*i,b*n+-.5*i),t.lineTo(-.5*a-b*i,b*a+-.5*i),t.lineTo(-.5*n+b*o,-.5*o-b*n),t.lineTo(-.5*n+b*i,-.5*i-b*n),t.lineTo(-.5*a+b*i,-.5*i-b*a),t.closePath()}}},_=Math.PI/180,C=function(t,e,r){if("area"===e)return t;switch(r){case"cross":return 5*t*t/9;case"diamond":return .5*t*t/Math.sqrt(3);case"square":return t*t;case"star":var n=18*_;return 1.25*t*t*(Math.tan(n)-Math.tan(2*n)*Math.pow(Math.tan(n),2));case"triangle":return Math.sqrt(3)*t*t/4;case"wye":return(21-10*Math.sqrt(3))*t*t/8;default:return Math.PI*t*t/4}},D=function(t){var e,r=t.type,o=void 0===r?"circle":r,a=t.size,u=void 0===a?64:a,c=t.sizeType,s=void 0===c?"area":c,l=M(M({},function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,A)),{},{type:o,size:u,sizeType:s}),p=l.className,h=l.cx,d=l.cy,y=(0,S.L6)(l,!0);return h===+h&&d===+d&&u===+u?n.createElement("path",E({},y,{className:(0,j.Z)("recharts-symbols",p),transform:"translate(".concat(h,", ").concat(d,")"),d:(e=T["symbol".concat(i()(o))]||f,(function(t,e){let r=null,n=(0,w.d)(o);function o(){let o;if(r||(r=o=n()),t.apply(this,arguments).draw(r,+e.apply(this,arguments)),o)return r=null,o+""||null}return t="function"==typeof t?t:(0,O.Z)(t||f),e="function"==typeof e?e:(0,O.Z)(void 0===e?64:+e),o.type=function(e){return arguments.length?(t="function"==typeof e?e:(0,O.Z)(e),o):t},o.size=function(t){return arguments.length?(e="function"==typeof t?t:(0,O.Z)(+t),o):e},o.context=function(t){return arguments.length?(r=null==t?null:t,o):r},o})().type(e).size(C(u,s,o))())})):null};D.registerSymbol=function(t,e){T["symbol".concat(i()(t))]=e}},11638:function(t,e,r){"use strict";r.d(e,{bn:function(){return C},a3:function(){return z},lT:function(){return D},V$:function(){return I},w7:function(){return N}});var n=r(2265),o=r(86757),i=r.n(o),a=r(90231),u=r.n(a),c=r(24342),s=r.n(c),l=r(21652),f=r.n(l),p=r(73649),h=r(61994),d=r(84735),y=r(82944);function v(t){return(v="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function m(){return(m=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t}).apply(this,arguments)}function b(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function g(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function x(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?g(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=v(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=v(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==v(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var O=function(t,e,r,n,o){var i=r-n;return"M ".concat(t,",").concat(e)+"L ".concat(t+r,",").concat(e)+"L ".concat(t+r-i/2,",").concat(e+o)+"L ".concat(t+r-i/2-n,",").concat(e+o)+"L ".concat(t,",").concat(e," Z")},w={x:0,y:0,upperWidth:0,lowerWidth:0,height:0,isUpdateAnimationActive:!1,animationBegin:0,animationDuration:1500,animationEasing:"ease"},j=function(t){var e,r=x(x({},w),t),o=(0,n.useRef)(),i=function(t){if(Array.isArray(t))return t}(e=(0,n.useState)(-1))||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,s=!1;try{for(i=(r=r.call(t)).next;!(c=(n=i.call(r)).done)&&(u.push(n.value),2!==u.length);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(e,2)||function(t,e){if(t){if("string"==typeof t)return b(t,2);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return b(t,2)}}(e,2)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),a=i[0],u=i[1];(0,n.useEffect)(function(){if(o.current&&o.current.getTotalLength)try{var t=o.current.getTotalLength();t&&u(t)}catch(t){}},[]);var c=r.x,s=r.y,l=r.upperWidth,f=r.lowerWidth,p=r.height,v=r.className,g=r.animationEasing,j=r.animationDuration,S=r.animationBegin,P=r.isUpdateAnimationActive;if(c!==+c||s!==+s||l!==+l||f!==+f||p!==+p||0===l&&0===f||0===p)return null;var A=(0,h.Z)("recharts-trapezoid",v);return P?n.createElement(d.ZP,{canBegin:a>0,from:{upperWidth:0,lowerWidth:0,height:p,x:c,y:s},to:{upperWidth:l,lowerWidth:f,height:p,x:c,y:s},duration:j,animationEasing:g,isActive:P},function(t){var e=t.upperWidth,i=t.lowerWidth,u=t.height,c=t.x,s=t.y;return n.createElement(d.ZP,{canBegin:a>0,from:"0px ".concat(-1===a?1:a,"px"),to:"".concat(a,"px 0px"),attributeName:"strokeDasharray",begin:S,duration:j,easing:g},n.createElement("path",m({},(0,y.L6)(r,!0),{className:A,d:O(c,s,e,i,u),ref:o})))}):n.createElement("g",null,n.createElement("path",m({},(0,y.L6)(r,!0),{className:A,d:O(c,s,l,f,p)})))},S=r(60474),P=r(9841),A=r(14870),E=["option","shapeType","propTransformer","activeClassName","isActive"];function k(t){return(k="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function M(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function T(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?M(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=k(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=k(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==k(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):M(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function _(t){var e=t.shapeType,r=t.elementProps;switch(e){case"rectangle":return n.createElement(p.A,r);case"trapezoid":return n.createElement(j,r);case"sector":return n.createElement(S.L,r);case"symbols":if("symbols"===e)return n.createElement(A.v,r);break;default:return null}}function C(t){var e,r=t.option,o=t.shapeType,a=t.propTransformer,c=t.activeClassName,l=t.isActive,f=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(t,E);if((0,n.isValidElement)(r))e=(0,n.cloneElement)(r,T(T({},f),(0,n.isValidElement)(r)?r.props:r));else if(i()(r))e=r(f);else if(u()(r)&&!s()(r)){var p=(void 0===a?function(t,e){return T(T({},e),t)}:a)(r,f);e=n.createElement(_,{shapeType:o,elementProps:p})}else e=n.createElement(_,{shapeType:o,elementProps:f});return l?n.createElement(P.m,{className:void 0===c?"recharts-active-shape":c},e):e}function D(t,e){return null!=e&&"trapezoids"in t.props}function I(t,e){return null!=e&&"sectors"in t.props}function N(t,e){return null!=e&&"points"in t.props}function B(t,e){var r,n,o=t.x===(null==e||null===(r=e.labelViewBox)||void 0===r?void 0:r.x)||t.x===e.x,i=t.y===(null==e||null===(n=e.labelViewBox)||void 0===n?void 0:n.y)||t.y===e.y;return o&&i}function L(t,e){var r=t.endAngle===e.endAngle,n=t.startAngle===e.startAngle;return r&&n}function R(t,e){var r=t.x===e.x,n=t.y===e.y,o=t.z===e.z;return r&&n&&o}function z(t){var e,r,n,o=t.activeTooltipItem,i=t.graphicalItem,a=t.itemData,u=(D(i,o)?e="trapezoids":I(i,o)?e="sectors":N(i,o)&&(e="points"),e),c=D(i,o)?null===(r=o.tooltipPayload)||void 0===r||null===(r=r[0])||void 0===r||null===(r=r.payload)||void 0===r?void 0:r.payload:I(i,o)?null===(n=o.tooltipPayload)||void 0===n||null===(n=n[0])||void 0===n||null===(n=n.payload)||void 0===n?void 0:n.payload:N(i,o)?o.payload:{},s=a.filter(function(t,e){var r=f()(c,t),n=i.props[u].filter(function(t){var e;return(D(i,o)?e=B:I(i,o)?e=L:N(i,o)&&(e=R),e)(t,o)}),a=i.props[u].indexOf(n[n.length-1]);return r&&e===a});return a.indexOf(s[s.length-1])}},85355:function(t,e,r){"use strict";r.d(e,{By:function(){return n3},VO:function(){return n0},zF:function(){return on},DO:function(){return oe},Bu:function(){return oo},zT:function(){return om},qz:function(){return n5},pt:function(){return n2},Yj:function(){return of},Fy:function(){return ol},gF:function(){return nQ},s6:function(){return n4},EB:function(){return oh},fk:function(){return n1},wh:function(){return oc},O3:function(){return op},uY:function(){return n9},g$:function(){return os},Qo:function(){return og},F$:function(){return nJ},NA:function(){return n8},ko:function(){return ob},ZI:function(){return n7},Hq:function(){return or},LG:function(){return ov},Vv:function(){return oi}});var n,o,i,a,u,c,s,l={};r.r(l),r.d(l,{scaleBand:function(){return f.Z},scaleDiverging:function(){return function t(){var e=tN(rJ()(tv));return e.copy=function(){return rH(e,t())},tj.O.apply(e,arguments)}},scaleDivergingLog:function(){return function t(){var e=tW(rJ()).domain([.1,1,10]);return e.copy=function(){return rH(e,t()).base(e.base())},tj.O.apply(e,arguments)}},scaleDivergingPow:function(){return rQ},scaleDivergingSqrt:function(){return r0},scaleDivergingSymlog:function(){return function t(){var e=tV(rJ());return e.copy=function(){return rH(e,t()).constant(e.constant())},tj.O.apply(e,arguments)}},scaleIdentity:function(){return function t(e){var r;function n(t){return null==t||isNaN(t=+t)?r:t}return n.invert=n,n.domain=n.range=function(t){return arguments.length?(e=Array.from(t,td),n):e.slice()},n.unknown=function(t){return arguments.length?(r=t,n):r},n.copy=function(){return t(e).unknown(r)},e=arguments.length?Array.from(e,td):[0,1],tN(n)}},scaleImplicit:function(){return tX.O},scaleLinear:function(){return tB},scaleLog:function(){return function t(){let e=tW(tO()).domain([1,10]);return e.copy=()=>tx(e,t()).base(e.base()),tj.o.apply(e,arguments),e}},scaleOrdinal:function(){return tX.Z},scalePoint:function(){return f.x},scalePow:function(){return tQ},scaleQuantile:function(){return function t(){var e,r=[],n=[],o=[];function i(){var t=0,e=Math.max(1,n.length);for(o=Array(e-1);++t<e;)o[t-1]=function(t,e,r=j){if(!(!(n=t.length)||isNaN(e=+e))){if(e<=0||n<2)return+r(t[0],0,t);if(e>=1)return+r(t[n-1],n-1,t);var n,o=(n-1)*e,i=Math.floor(o),a=+r(t[i],i,t);return a+(+r(t[i+1],i+1,t)-a)*(o-i)}}(r,t/e);return a}function a(t){return null==t||isNaN(t=+t)?e:n[P(o,t)]}return a.invertExtent=function(t){var e=n.indexOf(t);return e<0?[NaN,NaN]:[e>0?o[e-1]:r[0],e<o.length?o[e]:r[r.length-1]]},a.domain=function(t){if(!arguments.length)return r.slice();for(let e of(r=[],t))null==e||isNaN(e=+e)||r.push(e);return r.sort(g),i()},a.range=function(t){return arguments.length?(n=Array.from(t),i()):n.slice()},a.unknown=function(t){return arguments.length?(e=t,a):e},a.quantiles=function(){return o.slice()},a.copy=function(){return t().domain(r).range(n).unknown(e)},tj.o.apply(a,arguments)}},scaleQuantize:function(){return function t(){var e,r=0,n=1,o=1,i=[.5],a=[0,1];function u(t){return null!=t&&t<=t?a[P(i,t,0,o)]:e}function c(){var t=-1;for(i=Array(o);++t<o;)i[t]=((t+1)*n-(t-o)*r)/(o+1);return u}return u.domain=function(t){return arguments.length?([r,n]=t,r=+r,n=+n,c()):[r,n]},u.range=function(t){return arguments.length?(o=(a=Array.from(t)).length-1,c()):a.slice()},u.invertExtent=function(t){var e=a.indexOf(t);return e<0?[NaN,NaN]:e<1?[r,i[0]]:e>=o?[i[o-1],n]:[i[e-1],i[e]]},u.unknown=function(t){return arguments.length&&(e=t),u},u.thresholds=function(){return i.slice()},u.copy=function(){return t().domain([r,n]).range(a).unknown(e)},tj.o.apply(tN(u),arguments)}},scaleRadial:function(){return function t(){var e,r=tw(),n=[0,1],o=!1;function i(t){var n,i=Math.sign(n=r(t))*Math.sqrt(Math.abs(n));return isNaN(i)?e:o?Math.round(i):i}return i.invert=function(t){return r.invert(t1(t))},i.domain=function(t){return arguments.length?(r.domain(t),i):r.domain()},i.range=function(t){return arguments.length?(r.range((n=Array.from(t,td)).map(t1)),i):n.slice()},i.rangeRound=function(t){return i.range(t).round(!0)},i.round=function(t){return arguments.length?(o=!!t,i):o},i.clamp=function(t){return arguments.length?(r.clamp(t),i):r.clamp()},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t(r.domain(),n).round(o).clamp(r.clamp()).unknown(e)},tj.o.apply(i,arguments),tN(i)}},scaleSequential:function(){return function t(){var e=tN(rX()(tv));return e.copy=function(){return rH(e,t())},tj.O.apply(e,arguments)}},scaleSequentialLog:function(){return function t(){var e=tW(rX()).domain([1,10]);return e.copy=function(){return rH(e,t()).base(e.base())},tj.O.apply(e,arguments)}},scaleSequentialPow:function(){return rK},scaleSequentialQuantile:function(){return function t(){var e=[],r=tv;function n(t){if(null!=t&&!isNaN(t=+t))return r((P(e,t,1)-1)/(e.length-1))}return n.domain=function(t){if(!arguments.length)return e.slice();for(let r of(e=[],t))null==r||isNaN(r=+r)||e.push(r);return e.sort(g),n},n.interpolator=function(t){return arguments.length?(r=t,n):r},n.range=function(){return e.map((t,n)=>r(n/(e.length-1)))},n.quantiles=function(t){return Array.from({length:t+1},(r,n)=>(function(t,e,r){if(!(!(n=(t=Float64Array.from(function*(t,e){if(void 0===e)for(let e of t)null!=e&&(e=+e)>=e&&(yield e);else{let r=-1;for(let n of t)null!=(n=e(n,++r,t))&&(n=+n)>=n&&(yield n)}}(t,void 0))).length)||isNaN(e=+e))){if(e<=0||n<2)return t5(t);if(e>=1)return t2(t);var n,o=(n-1)*e,i=Math.floor(o),a=t2((function t(e,r,n=0,o=1/0,i){if(r=Math.floor(r),n=Math.floor(Math.max(0,n)),o=Math.floor(Math.min(e.length-1,o)),!(n<=r&&r<=o))return e;for(i=void 0===i?t3:function(t=g){if(t===g)return t3;if("function"!=typeof t)throw TypeError("compare is not a function");return(e,r)=>{let n=t(e,r);return n||0===n?n:(0===t(r,r))-(0===t(e,e))}}(i);o>n;){if(o-n>600){let a=o-n+1,u=r-n+1,c=Math.log(a),s=.5*Math.exp(2*c/3),l=.5*Math.sqrt(c*s*(a-s)/a)*(u-a/2<0?-1:1),f=Math.max(n,Math.floor(r-u*s/a+l)),p=Math.min(o,Math.floor(r+(a-u)*s/a+l));t(e,r,f,p,i)}let a=e[r],u=n,c=o;for(t6(e,n,r),i(e[o],a)>0&&t6(e,n,o);u<c;){for(t6(e,u,c),++u,--c;0>i(e[u],a);)++u;for(;i(e[c],a)>0;)--c}0===i(e[n],a)?t6(e,n,c):t6(e,++c,o),c<=r&&(n=c+1),r<=c&&(o=c-1)}return e})(t,i).subarray(0,i+1));return a+(t5(t.subarray(i+1))-a)*(o-i)}})(e,n/t))},n.copy=function(){return t(r).domain(e)},tj.O.apply(n,arguments)}},scaleSequentialSqrt:function(){return rG},scaleSequentialSymlog:function(){return function t(){var e=tV(rX());return e.copy=function(){return rH(e,t()).constant(e.constant())},tj.O.apply(e,arguments)}},scaleSqrt:function(){return t0},scaleSymlog:function(){return function t(){var e=tV(tO());return e.copy=function(){return tx(e,t()).constant(e.constant())},tj.o.apply(e,arguments)}},scaleThreshold:function(){return function t(){var e,r=[.5],n=[0,1],o=1;function i(t){return null!=t&&t<=t?n[P(r,t,0,o)]:e}return i.domain=function(t){return arguments.length?(o=Math.min((r=Array.from(t)).length,n.length-1),i):r.slice()},i.range=function(t){return arguments.length?(n=Array.from(t),o=Math.min(r.length,n.length-1),i):n.slice()},i.invertExtent=function(t){var e=n.indexOf(t);return[r[e-1],r[e]]},i.unknown=function(t){return arguments.length?(e=t,i):e},i.copy=function(){return t().domain(r).range(n).unknown(e)},tj.o.apply(i,arguments)}},scaleTime:function(){return rY},scaleUtc:function(){return rV},tickFormat:function(){return tI}});var f=r(55284);let p=Math.sqrt(50),h=Math.sqrt(10),d=Math.sqrt(2);function y(t,e,r){let n,o,i;let a=(e-t)/Math.max(0,r),u=Math.floor(Math.log10(a)),c=a/Math.pow(10,u),s=c>=p?10:c>=h?5:c>=d?2:1;return(u<0?(n=Math.round(t*(i=Math.pow(10,-u)/s)),o=Math.round(e*i),n/i<t&&++n,o/i>e&&--o,i=-i):(n=Math.round(t/(i=Math.pow(10,u)*s)),o=Math.round(e/i),n*i<t&&++n,o*i>e&&--o),o<n&&.5<=r&&r<2)?y(t,e,2*r):[n,o,i]}function v(t,e,r){if(e=+e,t=+t,!((r=+r)>0))return[];if(t===e)return[t];let n=e<t,[o,i,a]=n?y(e,t,r):y(t,e,r);if(!(i>=o))return[];let u=i-o+1,c=Array(u);if(n){if(a<0)for(let t=0;t<u;++t)c[t]=-((i-t)/a);else for(let t=0;t<u;++t)c[t]=(i-t)*a}else if(a<0)for(let t=0;t<u;++t)c[t]=-((o+t)/a);else for(let t=0;t<u;++t)c[t]=(o+t)*a;return c}function m(t,e,r){return y(t=+t,e=+e,r=+r)[2]}function b(t,e,r){e=+e,t=+t,r=+r;let n=e<t,o=n?m(e,t,r):m(t,e,r);return(n?-1:1)*(o<0?-(1/o):o)}function g(t,e){return null==t||null==e?NaN:t<e?-1:t>e?1:t>=e?0:NaN}function x(t,e){return null==t||null==e?NaN:e<t?-1:e>t?1:e>=t?0:NaN}function O(t){let e,r,n;function o(t,n,o=0,i=t.length){if(o<i){if(0!==e(n,n))return i;do{let e=o+i>>>1;0>r(t[e],n)?o=e+1:i=e}while(o<i)}return o}return 2!==t.length?(e=g,r=(e,r)=>g(t(e),r),n=(e,r)=>t(e)-r):(e=t===g||t===x?t:w,r=t,n=t),{left:o,center:function(t,e,r=0,i=t.length){let a=o(t,e,r,i-1);return a>r&&n(t[a-1],e)>-n(t[a],e)?a-1:a},right:function(t,n,o=0,i=t.length){if(o<i){if(0!==e(n,n))return i;do{let e=o+i>>>1;0>=r(t[e],n)?o=e+1:i=e}while(o<i)}return o}}}function w(){return 0}function j(t){return null===t?NaN:+t}let S=O(g),P=S.right;function A(t,e,r){t.prototype=e.prototype=r,r.constructor=t}function E(t,e){var r=Object.create(t.prototype);for(var n in e)r[n]=e[n];return r}function k(){}S.left,O(j).center;var M="\\s*([+-]?\\d+)\\s*",T="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",_="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",C=/^#([0-9a-f]{3,8})$/,D=RegExp(`^rgb\\(${M},${M},${M}\\)$`),I=RegExp(`^rgb\\(${_},${_},${_}\\)$`),N=RegExp(`^rgba\\(${M},${M},${M},${T}\\)$`),B=RegExp(`^rgba\\(${_},${_},${_},${T}\\)$`),L=RegExp(`^hsl\\(${T},${_},${_}\\)$`),R=RegExp(`^hsla\\(${T},${_},${_},${T}\\)$`),z={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function U(){return this.rgb().formatHex()}function $(){return this.rgb().formatRgb()}function F(t){var e,r;return t=(t+"").trim().toLowerCase(),(e=C.exec(t))?(r=e[1].length,e=parseInt(e[1],16),6===r?Z(e):3===r?new Y(e>>8&15|e>>4&240,e>>4&15|240&e,(15&e)<<4|15&e,1):8===r?W(e>>24&255,e>>16&255,e>>8&255,(255&e)/255):4===r?W(e>>12&15|e>>8&240,e>>8&15|e>>4&240,e>>4&15|240&e,((15&e)<<4|15&e)/255):null):(e=D.exec(t))?new Y(e[1],e[2],e[3],1):(e=I.exec(t))?new Y(255*e[1]/100,255*e[2]/100,255*e[3]/100,1):(e=N.exec(t))?W(e[1],e[2],e[3],e[4]):(e=B.exec(t))?W(255*e[1]/100,255*e[2]/100,255*e[3]/100,e[4]):(e=L.exec(t))?J(e[1],e[2]/100,e[3]/100,1):(e=R.exec(t))?J(e[1],e[2]/100,e[3]/100,e[4]):z.hasOwnProperty(t)?Z(z[t]):"transparent"===t?new Y(NaN,NaN,NaN,0):null}function Z(t){return new Y(t>>16&255,t>>8&255,255&t,1)}function W(t,e,r,n){return n<=0&&(t=e=r=NaN),new Y(t,e,r,n)}function q(t,e,r,n){var o;return 1==arguments.length?((o=t)instanceof k||(o=F(o)),o)?new Y((o=o.rgb()).r,o.g,o.b,o.opacity):new Y:new Y(t,e,r,null==n?1:n)}function Y(t,e,r,n){this.r=+t,this.g=+e,this.b=+r,this.opacity=+n}function V(){return`#${G(this.r)}${G(this.g)}${G(this.b)}`}function X(){let t=H(this.opacity);return`${1===t?"rgb(":"rgba("}${K(this.r)}, ${K(this.g)}, ${K(this.b)}${1===t?")":`, ${t})`}`}function H(t){return isNaN(t)?1:Math.max(0,Math.min(1,t))}function K(t){return Math.max(0,Math.min(255,Math.round(t)||0))}function G(t){return((t=K(t))<16?"0":"")+t.toString(16)}function J(t,e,r,n){return n<=0?t=e=r=NaN:r<=0||r>=1?t=e=NaN:e<=0&&(t=NaN),new tt(t,e,r,n)}function Q(t){if(t instanceof tt)return new tt(t.h,t.s,t.l,t.opacity);if(t instanceof k||(t=F(t)),!t)return new tt;if(t instanceof tt)return t;var e=(t=t.rgb()).r/255,r=t.g/255,n=t.b/255,o=Math.min(e,r,n),i=Math.max(e,r,n),a=NaN,u=i-o,c=(i+o)/2;return u?(a=e===i?(r-n)/u+(r<n)*6:r===i?(n-e)/u+2:(e-r)/u+4,u/=c<.5?i+o:2-i-o,a*=60):u=c>0&&c<1?0:a,new tt(a,u,c,t.opacity)}function tt(t,e,r,n){this.h=+t,this.s=+e,this.l=+r,this.opacity=+n}function te(t){return(t=(t||0)%360)<0?t+360:t}function tr(t){return Math.max(0,Math.min(1,t||0))}function tn(t,e,r){return(t<60?e+(r-e)*t/60:t<180?r:t<240?e+(r-e)*(240-t)/60:e)*255}function to(t,e,r,n,o){var i=t*t,a=i*t;return((1-3*t+3*i-a)*e+(4-6*i+3*a)*r+(1+3*t+3*i-3*a)*n+a*o)/6}A(k,F,{copy(t){return Object.assign(new this.constructor,this,t)},displayable(){return this.rgb().displayable()},hex:U,formatHex:U,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return Q(this).formatHsl()},formatRgb:$,toString:$}),A(Y,q,E(k,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new Y(this.r*t,this.g*t,this.b*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new Y(this.r*t,this.g*t,this.b*t,this.opacity)},rgb(){return this},clamp(){return new Y(K(this.r),K(this.g),K(this.b),H(this.opacity))},displayable(){return -.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:V,formatHex:V,formatHex8:function(){return`#${G(this.r)}${G(this.g)}${G(this.b)}${G((isNaN(this.opacity)?1:this.opacity)*255)}`},formatRgb:X,toString:X})),A(tt,function(t,e,r,n){return 1==arguments.length?Q(t):new tt(t,e,r,null==n?1:n)},E(k,{brighter(t){return t=null==t?1.4285714285714286:Math.pow(1.4285714285714286,t),new tt(this.h,this.s,this.l*t,this.opacity)},darker(t){return t=null==t?.7:Math.pow(.7,t),new tt(this.h,this.s,this.l*t,this.opacity)},rgb(){var t=this.h%360+(this.h<0)*360,e=isNaN(t)||isNaN(this.s)?0:this.s,r=this.l,n=r+(r<.5?r:1-r)*e,o=2*r-n;return new Y(tn(t>=240?t-240:t+120,o,n),tn(t,o,n),tn(t<120?t+240:t-120,o,n),this.opacity)},clamp(){return new tt(te(this.h),tr(this.s),tr(this.l),H(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){let t=H(this.opacity);return`${1===t?"hsl(":"hsla("}${te(this.h)}, ${100*tr(this.s)}%, ${100*tr(this.l)}%${1===t?")":`, ${t})`}`}}));var ti=t=>()=>t;function ta(t,e){var r=e-t;return r?function(e){return t+e*r}:ti(isNaN(t)?e:t)}var tu=function t(e){var r,n=1==(r=+(r=e))?ta:function(t,e){var n,o,i;return e-t?(n=t,o=e,n=Math.pow(n,i=r),o=Math.pow(o,i)-n,i=1/i,function(t){return Math.pow(n+t*o,i)}):ti(isNaN(t)?e:t)};function o(t,e){var r=n((t=q(t)).r,(e=q(e)).r),o=n(t.g,e.g),i=n(t.b,e.b),a=ta(t.opacity,e.opacity);return function(e){return t.r=r(e),t.g=o(e),t.b=i(e),t.opacity=a(e),t+""}}return o.gamma=t,o}(1);function tc(t){return function(e){var r,n,o=e.length,i=Array(o),a=Array(o),u=Array(o);for(r=0;r<o;++r)n=q(e[r]),i[r]=n.r||0,a[r]=n.g||0,u[r]=n.b||0;return i=t(i),a=t(a),u=t(u),n.opacity=1,function(t){return n.r=i(t),n.g=a(t),n.b=u(t),n+""}}}function ts(t,e){return t=+t,e=+e,function(r){return t*(1-r)+e*r}}tc(function(t){var e=t.length-1;return function(r){var n=r<=0?r=0:r>=1?(r=1,e-1):Math.floor(r*e),o=t[n],i=t[n+1],a=n>0?t[n-1]:2*o-i,u=n<e-1?t[n+2]:2*i-o;return to((r-n/e)*e,a,o,i,u)}}),tc(function(t){var e=t.length;return function(r){var n=Math.floor(((r%=1)<0?++r:r)*e),o=t[(n+e-1)%e],i=t[n%e],a=t[(n+1)%e],u=t[(n+2)%e];return to((r-n/e)*e,o,i,a,u)}});var tl=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,tf=RegExp(tl.source,"g");function tp(t,e){var r,n,o=typeof e;return null==e||"boolean"===o?ti(e):("number"===o?ts:"string"===o?(n=F(e))?(e=n,tu):function(t,e){var r,n,o,i,a,u=tl.lastIndex=tf.lastIndex=0,c=-1,s=[],l=[];for(t+="",e+="";(o=tl.exec(t))&&(i=tf.exec(e));)(a=i.index)>u&&(a=e.slice(u,a),s[c]?s[c]+=a:s[++c]=a),(o=o[0])===(i=i[0])?s[c]?s[c]+=i:s[++c]=i:(s[++c]=null,l.push({i:c,x:ts(o,i)})),u=tf.lastIndex;return u<e.length&&(a=e.slice(u),s[c]?s[c]+=a:s[++c]=a),s.length<2?l[0]?(r=l[0].x,function(t){return r(t)+""}):(n=e,function(){return n}):(e=l.length,function(t){for(var r,n=0;n<e;++n)s[(r=l[n]).i]=r.x(t);return s.join("")})}:e instanceof F?tu:e instanceof Date?function(t,e){var r=new Date;return t=+t,e=+e,function(n){return r.setTime(t*(1-n)+e*n),r}}:!ArrayBuffer.isView(r=e)||r instanceof DataView?Array.isArray(e)?function(t,e){var r,n=e?e.length:0,o=t?Math.min(n,t.length):0,i=Array(o),a=Array(n);for(r=0;r<o;++r)i[r]=tp(t[r],e[r]);for(;r<n;++r)a[r]=e[r];return function(t){for(r=0;r<o;++r)a[r]=i[r](t);return a}}:"function"!=typeof e.valueOf&&"function"!=typeof e.toString||isNaN(e)?function(t,e){var r,n={},o={};for(r in(null===t||"object"!=typeof t)&&(t={}),(null===e||"object"!=typeof e)&&(e={}),e)r in t?n[r]=tp(t[r],e[r]):o[r]=e[r];return function(t){for(r in n)o[r]=n[r](t);return o}}:ts:function(t,e){e||(e=[]);var r,n=t?Math.min(e.length,t.length):0,o=e.slice();return function(i){for(r=0;r<n;++r)o[r]=t[r]*(1-i)+e[r]*i;return o}})(t,e)}function th(t,e){return t=+t,e=+e,function(r){return Math.round(t*(1-r)+e*r)}}function td(t){return+t}var ty=[0,1];function tv(t){return t}function tm(t,e){var r;return(e-=t=+t)?function(r){return(r-t)/e}:(r=isNaN(e)?NaN:.5,function(){return r})}function tb(t,e,r){var n=t[0],o=t[1],i=e[0],a=e[1];return o<n?(n=tm(o,n),i=r(a,i)):(n=tm(n,o),i=r(i,a)),function(t){return i(n(t))}}function tg(t,e,r){var n=Math.min(t.length,e.length)-1,o=Array(n),i=Array(n),a=-1;for(t[n]<t[0]&&(t=t.slice().reverse(),e=e.slice().reverse());++a<n;)o[a]=tm(t[a],t[a+1]),i[a]=r(e[a],e[a+1]);return function(e){var r=P(t,e,1,n)-1;return i[r](o[r](e))}}function tx(t,e){return e.domain(t.domain()).range(t.range()).interpolate(t.interpolate()).clamp(t.clamp()).unknown(t.unknown())}function tO(){var t,e,r,n,o,i,a=ty,u=ty,c=tp,s=tv;function l(){var t,e,r,c=Math.min(a.length,u.length);return s!==tv&&(t=a[0],e=a[c-1],t>e&&(r=t,t=e,e=r),s=function(r){return Math.max(t,Math.min(e,r))}),n=c>2?tg:tb,o=i=null,f}function f(e){return null==e||isNaN(e=+e)?r:(o||(o=n(a.map(t),u,c)))(t(s(e)))}return f.invert=function(r){return s(e((i||(i=n(u,a.map(t),ts)))(r)))},f.domain=function(t){return arguments.length?(a=Array.from(t,td),l()):a.slice()},f.range=function(t){return arguments.length?(u=Array.from(t),l()):u.slice()},f.rangeRound=function(t){return u=Array.from(t),c=th,l()},f.clamp=function(t){return arguments.length?(s=!!t||tv,l()):s!==tv},f.interpolate=function(t){return arguments.length?(c=t,l()):c},f.unknown=function(t){return arguments.length?(r=t,f):r},function(r,n){return t=r,e=n,l()}}function tw(){return tO()(tv,tv)}var tj=r(89999),tS=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function tP(t){var e;if(!(e=tS.exec(t)))throw Error("invalid format: "+t);return new tA({fill:e[1],align:e[2],sign:e[3],symbol:e[4],zero:e[5],width:e[6],comma:e[7],precision:e[8]&&e[8].slice(1),trim:e[9],type:e[10]})}function tA(t){this.fill=void 0===t.fill?" ":t.fill+"",this.align=void 0===t.align?">":t.align+"",this.sign=void 0===t.sign?"-":t.sign+"",this.symbol=void 0===t.symbol?"":t.symbol+"",this.zero=!!t.zero,this.width=void 0===t.width?void 0:+t.width,this.comma=!!t.comma,this.precision=void 0===t.precision?void 0:+t.precision,this.trim=!!t.trim,this.type=void 0===t.type?"":t.type+""}function tE(t,e){if((r=(t=e?t.toExponential(e-1):t.toExponential()).indexOf("e"))<0)return null;var r,n=t.slice(0,r);return[n.length>1?n[0]+n.slice(2):n,+t.slice(r+1)]}function tk(t){return(t=tE(Math.abs(t)))?t[1]:NaN}function tM(t,e){var r=tE(t,e);if(!r)return t+"";var n=r[0],o=r[1];return o<0?"0."+Array(-o).join("0")+n:n.length>o+1?n.slice(0,o+1)+"."+n.slice(o+1):n+Array(o-n.length+2).join("0")}tP.prototype=tA.prototype,tA.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};var tT={"%":(t,e)=>(100*t).toFixed(e),b:t=>Math.round(t).toString(2),c:t=>t+"",d:function(t){return Math.abs(t=Math.round(t))>=1e21?t.toLocaleString("en").replace(/,/g,""):t.toString(10)},e:(t,e)=>t.toExponential(e),f:(t,e)=>t.toFixed(e),g:(t,e)=>t.toPrecision(e),o:t=>Math.round(t).toString(8),p:(t,e)=>tM(100*t,e),r:tM,s:function(t,e){var r=tE(t,e);if(!r)return t+"";var o=r[0],i=r[1],a=i-(n=3*Math.max(-8,Math.min(8,Math.floor(i/3))))+1,u=o.length;return a===u?o:a>u?o+Array(a-u+1).join("0"):a>0?o.slice(0,a)+"."+o.slice(a):"0."+Array(1-a).join("0")+tE(t,Math.max(0,e+a-1))[0]},X:t=>Math.round(t).toString(16).toUpperCase(),x:t=>Math.round(t).toString(16)};function t_(t){return t}var tC=Array.prototype.map,tD=["y","z","a","f","p","n","\xb5","m","","k","M","G","T","P","E","Z","Y"];function tI(t,e,r,n){var o,u,c=b(t,e,r);switch((n=tP(null==n?",f":n)).type){case"s":var s=Math.max(Math.abs(t),Math.abs(e));return null!=n.precision||isNaN(u=Math.max(0,3*Math.max(-8,Math.min(8,Math.floor(tk(s)/3)))-tk(Math.abs(c))))||(n.precision=u),a(n,s);case"":case"e":case"g":case"p":case"r":null!=n.precision||isNaN(u=Math.max(0,tk(Math.abs(Math.max(Math.abs(t),Math.abs(e)))-(o=Math.abs(o=c)))-tk(o))+1)||(n.precision=u-("e"===n.type));break;case"f":case"%":null!=n.precision||isNaN(u=Math.max(0,-tk(Math.abs(c))))||(n.precision=u-("%"===n.type)*2)}return i(n)}function tN(t){var e=t.domain;return t.ticks=function(t){var r=e();return v(r[0],r[r.length-1],null==t?10:t)},t.tickFormat=function(t,r){var n=e();return tI(n[0],n[n.length-1],null==t?10:t,r)},t.nice=function(r){null==r&&(r=10);var n,o,i=e(),a=0,u=i.length-1,c=i[a],s=i[u],l=10;for(s<c&&(o=c,c=s,s=o,o=a,a=u,u=o);l-- >0;){if((o=m(c,s,r))===n)return i[a]=c,i[u]=s,e(i);if(o>0)c=Math.floor(c/o)*o,s=Math.ceil(s/o)*o;else if(o<0)c=Math.ceil(c*o)/o,s=Math.floor(s*o)/o;else break;n=o}return t},t}function tB(){var t=tw();return t.copy=function(){return tx(t,tB())},tj.o.apply(t,arguments),tN(t)}function tL(t,e){t=t.slice();var r,n=0,o=t.length-1,i=t[n],a=t[o];return a<i&&(r=n,n=o,o=r,r=i,i=a,a=r),t[n]=e.floor(i),t[o]=e.ceil(a),t}function tR(t){return Math.log(t)}function tz(t){return Math.exp(t)}function tU(t){return-Math.log(-t)}function t$(t){return-Math.exp(-t)}function tF(t){return isFinite(t)?+("1e"+t):t<0?0:t}function tZ(t){return(e,r)=>-t(-e,r)}function tW(t){let e,r;let n=t(tR,tz),o=n.domain,a=10;function u(){var i,u;return e=(i=a)===Math.E?Math.log:10===i&&Math.log10||2===i&&Math.log2||(i=Math.log(i),t=>Math.log(t)/i),r=10===(u=a)?tF:u===Math.E?Math.exp:t=>Math.pow(u,t),o()[0]<0?(e=tZ(e),r=tZ(r),t(tU,t$)):t(tR,tz),n}return n.base=function(t){return arguments.length?(a=+t,u()):a},n.domain=function(t){return arguments.length?(o(t),u()):o()},n.ticks=t=>{let n,i;let u=o(),c=u[0],s=u[u.length-1],l=s<c;l&&([c,s]=[s,c]);let f=e(c),p=e(s),h=null==t?10:+t,d=[];if(!(a%1)&&p-f<h){if(f=Math.floor(f),p=Math.ceil(p),c>0){for(;f<=p;++f)for(n=1;n<a;++n)if(!((i=f<0?n/r(-f):n*r(f))<c)){if(i>s)break;d.push(i)}}else for(;f<=p;++f)for(n=a-1;n>=1;--n)if(!((i=f>0?n/r(-f):n*r(f))<c)){if(i>s)break;d.push(i)}2*d.length<h&&(d=v(c,s,h))}else d=v(f,p,Math.min(p-f,h)).map(r);return l?d.reverse():d},n.tickFormat=(t,o)=>{if(null==t&&(t=10),null==o&&(o=10===a?"s":","),"function"!=typeof o&&(a%1||null!=(o=tP(o)).precision||(o.trim=!0),o=i(o)),t===1/0)return o;let u=Math.max(1,a*t/n.ticks().length);return t=>{let n=t/r(Math.round(e(t)));return n*a<a-.5&&(n*=a),n<=u?o(t):""}},n.nice=()=>o(tL(o(),{floor:t=>r(Math.floor(e(t))),ceil:t=>r(Math.ceil(e(t)))})),n}function tq(t){return function(e){return Math.sign(e)*Math.log1p(Math.abs(e/t))}}function tY(t){return function(e){return Math.sign(e)*Math.expm1(Math.abs(e))*t}}function tV(t){var e=1,r=t(tq(1),tY(e));return r.constant=function(r){return arguments.length?t(tq(e=+r),tY(e)):e},tN(r)}i=(o=function(t){var e,r,o,i=void 0===t.grouping||void 0===t.thousands?t_:(e=tC.call(t.grouping,Number),r=t.thousands+"",function(t,n){for(var o=t.length,i=[],a=0,u=e[0],c=0;o>0&&u>0&&(c+u+1>n&&(u=Math.max(1,n-c)),i.push(t.substring(o-=u,o+u)),!((c+=u+1)>n));)u=e[a=(a+1)%e.length];return i.reverse().join(r)}),a=void 0===t.currency?"":t.currency[0]+"",u=void 0===t.currency?"":t.currency[1]+"",c=void 0===t.decimal?".":t.decimal+"",s=void 0===t.numerals?t_:(o=tC.call(t.numerals,String),function(t){return t.replace(/[0-9]/g,function(t){return o[+t]})}),l=void 0===t.percent?"%":t.percent+"",f=void 0===t.minus?"−":t.minus+"",p=void 0===t.nan?"NaN":t.nan+"";function h(t){var e=(t=tP(t)).fill,r=t.align,o=t.sign,h=t.symbol,d=t.zero,y=t.width,v=t.comma,m=t.precision,b=t.trim,g=t.type;"n"===g?(v=!0,g="g"):tT[g]||(void 0===m&&(m=12),b=!0,g="g"),(d||"0"===e&&"="===r)&&(d=!0,e="0",r="=");var x="$"===h?a:"#"===h&&/[boxX]/.test(g)?"0"+g.toLowerCase():"",O="$"===h?u:/[%p]/.test(g)?l:"",w=tT[g],j=/[defgprs%]/.test(g);function S(t){var a,u,l,h=x,S=O;if("c"===g)S=w(t)+S,t="";else{var P=(t=+t)<0||1/t<0;if(t=isNaN(t)?p:w(Math.abs(t),m),b&&(t=function(t){e:for(var e,r=t.length,n=1,o=-1;n<r;++n)switch(t[n]){case".":o=e=n;break;case"0":0===o&&(o=n),e=n;break;default:if(!+t[n])break e;o>0&&(o=0)}return o>0?t.slice(0,o)+t.slice(e+1):t}(t)),P&&0==+t&&"+"!==o&&(P=!1),h=(P?"("===o?o:f:"-"===o||"("===o?"":o)+h,S=("s"===g?tD[8+n/3]:"")+S+(P&&"("===o?")":""),j){for(a=-1,u=t.length;++a<u;)if(48>(l=t.charCodeAt(a))||l>57){S=(46===l?c+t.slice(a+1):t.slice(a))+S,t=t.slice(0,a);break}}}v&&!d&&(t=i(t,1/0));var A=h.length+t.length+S.length,E=A<y?Array(y-A+1).join(e):"";switch(v&&d&&(t=i(E+t,E.length?y-S.length:1/0),E=""),r){case"<":t=h+t+S+E;break;case"=":t=h+E+t+S;break;case"^":t=E.slice(0,A=E.length>>1)+h+t+S+E.slice(A);break;default:t=E+h+t+S}return s(t)}return m=void 0===m?6:/[gprs]/.test(g)?Math.max(1,Math.min(21,m)):Math.max(0,Math.min(20,m)),S.toString=function(){return t+""},S}return{format:h,formatPrefix:function(t,e){var r=h(((t=tP(t)).type="f",t)),n=3*Math.max(-8,Math.min(8,Math.floor(tk(e)/3))),o=Math.pow(10,-n),i=tD[8+n/3];return function(t){return r(o*t)+i}}}}({thousands:",",grouping:[3],currency:["$",""]})).format,a=o.formatPrefix;var tX=r(36967);function tH(t){return function(e){return e<0?-Math.pow(-e,t):Math.pow(e,t)}}function tK(t){return t<0?-Math.sqrt(-t):Math.sqrt(t)}function tG(t){return t<0?-t*t:t*t}function tJ(t){var e=t(tv,tv),r=1;return e.exponent=function(e){return arguments.length?1==(r=+e)?t(tv,tv):.5===r?t(tK,tG):t(tH(r),tH(1/r)):r},tN(e)}function tQ(){var t=tJ(tO());return t.copy=function(){return tx(t,tQ()).exponent(t.exponent())},tj.o.apply(t,arguments),t}function t0(){return tQ.apply(null,arguments).exponent(.5)}function t1(t){return Math.sign(t)*t*t}function t2(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r<e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let o of t)null!=(o=e(o,++n,t))&&(r<o||void 0===r&&o>=o)&&(r=o)}return r}function t5(t,e){let r;if(void 0===e)for(let e of t)null!=e&&(r>e||void 0===r&&e>=e)&&(r=e);else{let n=-1;for(let o of t)null!=(o=e(o,++n,t))&&(r>o||void 0===r&&o>=o)&&(r=o)}return r}function t3(t,e){return(null==t||!(t>=t))-(null==e||!(e>=e))||(t<e?-1:t>e?1:0)}function t6(t,e,r){let n=t[e];t[e]=t[r],t[r]=n}let t7=new Date,t4=new Date;function t8(t,e,r,n){function o(e){return t(e=0==arguments.length?new Date:new Date(+e)),e}return o.floor=e=>(t(e=new Date(+e)),e),o.ceil=r=>(t(r=new Date(r-1)),e(r,1),t(r),r),o.round=t=>{let e=o(t),r=o.ceil(t);return t-e<r-t?e:r},o.offset=(t,r)=>(e(t=new Date(+t),null==r?1:Math.floor(r)),t),o.range=(r,n,i)=>{let a;let u=[];if(r=o.ceil(r),i=null==i?1:Math.floor(i),!(r<n)||!(i>0))return u;do u.push(a=new Date(+r)),e(r,i),t(r);while(a<r&&r<n);return u},o.filter=r=>t8(e=>{if(e>=e)for(;t(e),!r(e);)e.setTime(e-1)},(t,n)=>{if(t>=t){if(n<0)for(;++n<=0;)for(;e(t,-1),!r(t););else for(;--n>=0;)for(;e(t,1),!r(t););}}),r&&(o.count=(e,n)=>(t7.setTime(+e),t4.setTime(+n),t(t7),t(t4),Math.floor(r(t7,t4))),o.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?o.filter(n?e=>n(e)%t==0:e=>o.count(0,e)%t==0):o:null),o}let t9=t8(()=>{},(t,e)=>{t.setTime(+t+e)},(t,e)=>e-t);t9.every=t=>isFinite(t=Math.floor(t))&&t>0?t>1?t8(e=>{e.setTime(Math.floor(e/t)*t)},(e,r)=>{e.setTime(+e+r*t)},(e,r)=>(r-e)/t):t9:null,t9.range;let et=t8(t=>{t.setTime(t-t.getMilliseconds())},(t,e)=>{t.setTime(+t+1e3*e)},(t,e)=>(e-t)/1e3,t=>t.getUTCSeconds());et.range;let ee=t8(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds())},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getMinutes());ee.range;let er=t8(t=>{t.setUTCSeconds(0,0)},(t,e)=>{t.setTime(+t+6e4*e)},(t,e)=>(e-t)/6e4,t=>t.getUTCMinutes());er.range;let en=t8(t=>{t.setTime(t-t.getMilliseconds()-1e3*t.getSeconds()-6e4*t.getMinutes())},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getHours());en.range;let eo=t8(t=>{t.setUTCMinutes(0,0,0)},(t,e)=>{t.setTime(+t+36e5*e)},(t,e)=>(e-t)/36e5,t=>t.getUTCHours());eo.range;let ei=t8(t=>t.setHours(0,0,0,0),(t,e)=>t.setDate(t.getDate()+e),(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/864e5,t=>t.getDate()-1);ei.range;let ea=t8(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>t.getUTCDate()-1);ea.range;let eu=t8(t=>{t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+e)},(t,e)=>(e-t)/864e5,t=>Math.floor(t/864e5));function ec(t){return t8(e=>{e.setDate(e.getDate()-(e.getDay()+7-t)%7),e.setHours(0,0,0,0)},(t,e)=>{t.setDate(t.getDate()+7*e)},(t,e)=>(e-t-(e.getTimezoneOffset()-t.getTimezoneOffset())*6e4)/6048e5)}eu.range;let es=ec(0),el=ec(1),ef=ec(2),ep=ec(3),eh=ec(4),ed=ec(5),ey=ec(6);function ev(t){return t8(e=>{e.setUTCDate(e.getUTCDate()-(e.getUTCDay()+7-t)%7),e.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCDate(t.getUTCDate()+7*e)},(t,e)=>(e-t)/6048e5)}es.range,el.range,ef.range,ep.range,eh.range,ed.range,ey.range;let em=ev(0),eb=ev(1),eg=ev(2),ex=ev(3),eO=ev(4),ew=ev(5),ej=ev(6);em.range,eb.range,eg.range,ex.range,eO.range,ew.range,ej.range;let eS=t8(t=>{t.setDate(1),t.setHours(0,0,0,0)},(t,e)=>{t.setMonth(t.getMonth()+e)},(t,e)=>e.getMonth()-t.getMonth()+(e.getFullYear()-t.getFullYear())*12,t=>t.getMonth());eS.range;let eP=t8(t=>{t.setUTCDate(1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCMonth(t.getUTCMonth()+e)},(t,e)=>e.getUTCMonth()-t.getUTCMonth()+(e.getUTCFullYear()-t.getUTCFullYear())*12,t=>t.getUTCMonth());eP.range;let eA=t8(t=>{t.setMonth(0,1),t.setHours(0,0,0,0)},(t,e)=>{t.setFullYear(t.getFullYear()+e)},(t,e)=>e.getFullYear()-t.getFullYear(),t=>t.getFullYear());eA.every=t=>isFinite(t=Math.floor(t))&&t>0?t8(e=>{e.setFullYear(Math.floor(e.getFullYear()/t)*t),e.setMonth(0,1),e.setHours(0,0,0,0)},(e,r)=>{e.setFullYear(e.getFullYear()+r*t)}):null,eA.range;let eE=t8(t=>{t.setUTCMonth(0,1),t.setUTCHours(0,0,0,0)},(t,e)=>{t.setUTCFullYear(t.getUTCFullYear()+e)},(t,e)=>e.getUTCFullYear()-t.getUTCFullYear(),t=>t.getUTCFullYear());function ek(t,e,r,n,o,i){let a=[[et,1,1e3],[et,5,5e3],[et,15,15e3],[et,30,3e4],[i,1,6e4],[i,5,3e5],[i,15,9e5],[i,30,18e5],[o,1,36e5],[o,3,108e5],[o,6,216e5],[o,12,432e5],[n,1,864e5],[n,2,1728e5],[r,1,6048e5],[e,1,2592e6],[e,3,7776e6],[t,1,31536e6]];function u(e,r,n){let o=Math.abs(r-e)/n,i=O(([,,t])=>t).right(a,o);if(i===a.length)return t.every(b(e/31536e6,r/31536e6,n));if(0===i)return t9.every(Math.max(b(e,r,n),1));let[u,c]=a[o/a[i-1][2]<a[i][2]/o?i-1:i];return u.every(c)}return[function(t,e,r){let n=e<t;n&&([t,e]=[e,t]);let o=r&&"function"==typeof r.range?r:u(t,e,r),i=o?o.range(t,+e+1):[];return n?i.reverse():i},u]}eE.every=t=>isFinite(t=Math.floor(t))&&t>0?t8(e=>{e.setUTCFullYear(Math.floor(e.getUTCFullYear()/t)*t),e.setUTCMonth(0,1),e.setUTCHours(0,0,0,0)},(e,r)=>{e.setUTCFullYear(e.getUTCFullYear()+r*t)}):null,eE.range;let[eM,eT]=ek(eE,eP,em,eu,eo,er),[e_,eC]=ek(eA,eS,es,ei,en,ee);function eD(t){if(0<=t.y&&t.y<100){var e=new Date(-1,t.m,t.d,t.H,t.M,t.S,t.L);return e.setFullYear(t.y),e}return new Date(t.y,t.m,t.d,t.H,t.M,t.S,t.L)}function eI(t){if(0<=t.y&&t.y<100){var e=new Date(Date.UTC(-1,t.m,t.d,t.H,t.M,t.S,t.L));return e.setUTCFullYear(t.y),e}return new Date(Date.UTC(t.y,t.m,t.d,t.H,t.M,t.S,t.L))}function eN(t,e,r){return{y:t,m:e,d:r,H:0,M:0,S:0,L:0}}var eB={"-":"",_:" ",0:"0"},eL=/^\s*\d+/,eR=/^%/,ez=/[\\^$*+?|[\]().{}]/g;function eU(t,e,r){var n=t<0?"-":"",o=(n?-t:t)+"",i=o.length;return n+(i<r?Array(r-i+1).join(e)+o:o)}function e$(t){return t.replace(ez,"\\$&")}function eF(t){return RegExp("^(?:"+t.map(e$).join("|")+")","i")}function eZ(t){return new Map(t.map((t,e)=>[t.toLowerCase(),e]))}function eW(t,e,r){var n=eL.exec(e.slice(r,r+1));return n?(t.w=+n[0],r+n[0].length):-1}function eq(t,e,r){var n=eL.exec(e.slice(r,r+1));return n?(t.u=+n[0],r+n[0].length):-1}function eY(t,e,r){var n=eL.exec(e.slice(r,r+2));return n?(t.U=+n[0],r+n[0].length):-1}function eV(t,e,r){var n=eL.exec(e.slice(r,r+2));return n?(t.V=+n[0],r+n[0].length):-1}function eX(t,e,r){var n=eL.exec(e.slice(r,r+2));return n?(t.W=+n[0],r+n[0].length):-1}function eH(t,e,r){var n=eL.exec(e.slice(r,r+4));return n?(t.y=+n[0],r+n[0].length):-1}function eK(t,e,r){var n=eL.exec(e.slice(r,r+2));return n?(t.y=+n[0]+(+n[0]>68?1900:2e3),r+n[0].length):-1}function eG(t,e,r){var n=/^(Z)|([+-]\d\d)(?::?(\d\d))?/.exec(e.slice(r,r+6));return n?(t.Z=n[1]?0:-(n[2]+(n[3]||"00")),r+n[0].length):-1}function eJ(t,e,r){var n=eL.exec(e.slice(r,r+1));return n?(t.q=3*n[0]-3,r+n[0].length):-1}function eQ(t,e,r){var n=eL.exec(e.slice(r,r+2));return n?(t.m=n[0]-1,r+n[0].length):-1}function e0(t,e,r){var n=eL.exec(e.slice(r,r+2));return n?(t.d=+n[0],r+n[0].length):-1}function e1(t,e,r){var n=eL.exec(e.slice(r,r+3));return n?(t.m=0,t.d=+n[0],r+n[0].length):-1}function e2(t,e,r){var n=eL.exec(e.slice(r,r+2));return n?(t.H=+n[0],r+n[0].length):-1}function e5(t,e,r){var n=eL.exec(e.slice(r,r+2));return n?(t.M=+n[0],r+n[0].length):-1}function e3(t,e,r){var n=eL.exec(e.slice(r,r+2));return n?(t.S=+n[0],r+n[0].length):-1}function e6(t,e,r){var n=eL.exec(e.slice(r,r+3));return n?(t.L=+n[0],r+n[0].length):-1}function e7(t,e,r){var n=eL.exec(e.slice(r,r+6));return n?(t.L=Math.floor(n[0]/1e3),r+n[0].length):-1}function e4(t,e,r){var n=eR.exec(e.slice(r,r+1));return n?r+n[0].length:-1}function e8(t,e,r){var n=eL.exec(e.slice(r));return n?(t.Q=+n[0],r+n[0].length):-1}function e9(t,e,r){var n=eL.exec(e.slice(r));return n?(t.s=+n[0],r+n[0].length):-1}function rt(t,e){return eU(t.getDate(),e,2)}function re(t,e){return eU(t.getHours(),e,2)}function rr(t,e){return eU(t.getHours()%12||12,e,2)}function rn(t,e){return eU(1+ei.count(eA(t),t),e,3)}function ro(t,e){return eU(t.getMilliseconds(),e,3)}function ri(t,e){return ro(t,e)+"000"}function ra(t,e){return eU(t.getMonth()+1,e,2)}function ru(t,e){return eU(t.getMinutes(),e,2)}function rc(t,e){return eU(t.getSeconds(),e,2)}function rs(t){var e=t.getDay();return 0===e?7:e}function rl(t,e){return eU(es.count(eA(t)-1,t),e,2)}function rf(t){var e=t.getDay();return e>=4||0===e?eh(t):eh.ceil(t)}function rp(t,e){return t=rf(t),eU(eh.count(eA(t),t)+(4===eA(t).getDay()),e,2)}function rh(t){return t.getDay()}function rd(t,e){return eU(el.count(eA(t)-1,t),e,2)}function ry(t,e){return eU(t.getFullYear()%100,e,2)}function rv(t,e){return eU((t=rf(t)).getFullYear()%100,e,2)}function rm(t,e){return eU(t.getFullYear()%1e4,e,4)}function rb(t,e){var r=t.getDay();return eU((t=r>=4||0===r?eh(t):eh.ceil(t)).getFullYear()%1e4,e,4)}function rg(t){var e=t.getTimezoneOffset();return(e>0?"-":(e*=-1,"+"))+eU(e/60|0,"0",2)+eU(e%60,"0",2)}function rx(t,e){return eU(t.getUTCDate(),e,2)}function rO(t,e){return eU(t.getUTCHours(),e,2)}function rw(t,e){return eU(t.getUTCHours()%12||12,e,2)}function rj(t,e){return eU(1+ea.count(eE(t),t),e,3)}function rS(t,e){return eU(t.getUTCMilliseconds(),e,3)}function rP(t,e){return rS(t,e)+"000"}function rA(t,e){return eU(t.getUTCMonth()+1,e,2)}function rE(t,e){return eU(t.getUTCMinutes(),e,2)}function rk(t,e){return eU(t.getUTCSeconds(),e,2)}function rM(t){var e=t.getUTCDay();return 0===e?7:e}function rT(t,e){return eU(em.count(eE(t)-1,t),e,2)}function r_(t){var e=t.getUTCDay();return e>=4||0===e?eO(t):eO.ceil(t)}function rC(t,e){return t=r_(t),eU(eO.count(eE(t),t)+(4===eE(t).getUTCDay()),e,2)}function rD(t){return t.getUTCDay()}function rI(t,e){return eU(eb.count(eE(t)-1,t),e,2)}function rN(t,e){return eU(t.getUTCFullYear()%100,e,2)}function rB(t,e){return eU((t=r_(t)).getUTCFullYear()%100,e,2)}function rL(t,e){return eU(t.getUTCFullYear()%1e4,e,4)}function rR(t,e){var r=t.getUTCDay();return eU((t=r>=4||0===r?eO(t):eO.ceil(t)).getUTCFullYear()%1e4,e,4)}function rz(){return"+0000"}function rU(){return"%"}function r$(t){return+t}function rF(t){return Math.floor(+t/1e3)}function rZ(t){return new Date(t)}function rW(t){return t instanceof Date?+t:+new Date(+t)}function rq(t,e,r,n,o,i,a,u,c,s){var l=tw(),f=l.invert,p=l.domain,h=s(".%L"),d=s(":%S"),y=s("%I:%M"),v=s("%I %p"),m=s("%a %d"),b=s("%b %d"),g=s("%B"),x=s("%Y");function O(t){return(c(t)<t?h:u(t)<t?d:a(t)<t?y:i(t)<t?v:n(t)<t?o(t)<t?m:b:r(t)<t?g:x)(t)}return l.invert=function(t){return new Date(f(t))},l.domain=function(t){return arguments.length?p(Array.from(t,rW)):p().map(rZ)},l.ticks=function(e){var r=p();return t(r[0],r[r.length-1],null==e?10:e)},l.tickFormat=function(t,e){return null==e?O:s(e)},l.nice=function(t){var r=p();return t&&"function"==typeof t.range||(t=e(r[0],r[r.length-1],null==t?10:t)),t?p(tL(r,t)):l},l.copy=function(){return tx(l,rq(t,e,r,n,o,i,a,u,c,s))},l}function rY(){return tj.o.apply(rq(e_,eC,eA,eS,es,ei,en,ee,et,c).domain([new Date(2e3,0,1),new Date(2e3,0,2)]),arguments)}function rV(){return tj.o.apply(rq(eM,eT,eE,eP,em,ea,eo,er,et,s).domain([Date.UTC(2e3,0,1),Date.UTC(2e3,0,2)]),arguments)}function rX(){var t,e,r,n,o,i=0,a=1,u=tv,c=!1;function s(e){return null==e||isNaN(e=+e)?o:u(0===r?.5:(e=(n(e)-t)*r,c?Math.max(0,Math.min(1,e)):e))}function l(t){return function(e){var r,n;return arguments.length?([r,n]=e,u=t(r,n),s):[u(0),u(1)]}}return s.domain=function(o){return arguments.length?([i,a]=o,t=n(i=+i),e=n(a=+a),r=t===e?0:1/(e-t),s):[i,a]},s.clamp=function(t){return arguments.length?(c=!!t,s):c},s.interpolator=function(t){return arguments.length?(u=t,s):u},s.range=l(tp),s.rangeRound=l(th),s.unknown=function(t){return arguments.length?(o=t,s):o},function(o){return n=o,t=o(i),e=o(a),r=t===e?0:1/(e-t),s}}function rH(t,e){return e.domain(t.domain()).interpolator(t.interpolator()).clamp(t.clamp()).unknown(t.unknown())}function rK(){var t=tJ(rX());return t.copy=function(){return rH(t,rK()).exponent(t.exponent())},tj.O.apply(t,arguments)}function rG(){return rK.apply(null,arguments).exponent(.5)}function rJ(){var t,e,r,n,o,i,a,u=0,c=.5,s=1,l=1,f=tv,p=!1;function h(t){return isNaN(t=+t)?a:(t=.5+((t=+i(t))-e)*(l*t<l*e?n:o),f(p?Math.max(0,Math.min(1,t)):t))}function d(t){return function(e){var r,n,o;return arguments.length?([r,n,o]=e,f=function(t,e){void 0===e&&(e=t,t=tp);for(var r=0,n=e.length-1,o=e[0],i=Array(n<0?0:n);r<n;)i[r]=t(o,o=e[++r]);return function(t){var e=Math.max(0,Math.min(n-1,Math.floor(t*=n)));return i[e](t-e)}}(t,[r,n,o]),h):[f(0),f(.5),f(1)]}}return h.domain=function(a){return arguments.length?([u,c,s]=a,t=i(u=+u),e=i(c=+c),r=i(s=+s),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),l=e<t?-1:1,h):[u,c,s]},h.clamp=function(t){return arguments.length?(p=!!t,h):p},h.interpolator=function(t){return arguments.length?(f=t,h):f},h.range=d(tp),h.rangeRound=d(th),h.unknown=function(t){return arguments.length?(a=t,h):a},function(a){return i=a,t=a(u),e=a(c),r=a(s),n=t===e?0:.5/(e-t),o=e===r?0:.5/(r-e),l=e<t?-1:1,h}}function rQ(){var t=tJ(rJ());return t.copy=function(){return rH(t,rQ()).exponent(t.exponent())},tj.O.apply(t,arguments)}function r0(){return rQ.apply(null,arguments).exponent(.5)}function r1(t,e){if((o=t.length)>1)for(var r,n,o,i=1,a=t[e[0]],u=a.length;i<o;++i)for(n=a,a=t[e[i]],r=0;r<u;++r)a[r][1]+=a[r][0]=isNaN(n[r][1])?n[r][0]:n[r][1]}c=(u=function(t){var e=t.dateTime,r=t.date,n=t.time,o=t.periods,i=t.days,a=t.shortDays,u=t.months,c=t.shortMonths,s=eF(o),l=eZ(o),f=eF(i),p=eZ(i),h=eF(a),d=eZ(a),y=eF(u),v=eZ(u),m=eF(c),b=eZ(c),g={a:function(t){return a[t.getDay()]},A:function(t){return i[t.getDay()]},b:function(t){return c[t.getMonth()]},B:function(t){return u[t.getMonth()]},c:null,d:rt,e:rt,f:ri,g:rv,G:rb,H:re,I:rr,j:rn,L:ro,m:ra,M:ru,p:function(t){return o[+(t.getHours()>=12)]},q:function(t){return 1+~~(t.getMonth()/3)},Q:r$,s:rF,S:rc,u:rs,U:rl,V:rp,w:rh,W:rd,x:null,X:null,y:ry,Y:rm,Z:rg,"%":rU},x={a:function(t){return a[t.getUTCDay()]},A:function(t){return i[t.getUTCDay()]},b:function(t){return c[t.getUTCMonth()]},B:function(t){return u[t.getUTCMonth()]},c:null,d:rx,e:rx,f:rP,g:rB,G:rR,H:rO,I:rw,j:rj,L:rS,m:rA,M:rE,p:function(t){return o[+(t.getUTCHours()>=12)]},q:function(t){return 1+~~(t.getUTCMonth()/3)},Q:r$,s:rF,S:rk,u:rM,U:rT,V:rC,w:rD,W:rI,x:null,X:null,y:rN,Y:rL,Z:rz,"%":rU},O={a:function(t,e,r){var n=h.exec(e.slice(r));return n?(t.w=d.get(n[0].toLowerCase()),r+n[0].length):-1},A:function(t,e,r){var n=f.exec(e.slice(r));return n?(t.w=p.get(n[0].toLowerCase()),r+n[0].length):-1},b:function(t,e,r){var n=m.exec(e.slice(r));return n?(t.m=b.get(n[0].toLowerCase()),r+n[0].length):-1},B:function(t,e,r){var n=y.exec(e.slice(r));return n?(t.m=v.get(n[0].toLowerCase()),r+n[0].length):-1},c:function(t,r,n){return S(t,e,r,n)},d:e0,e:e0,f:e7,g:eK,G:eH,H:e2,I:e2,j:e1,L:e6,m:eQ,M:e5,p:function(t,e,r){var n=s.exec(e.slice(r));return n?(t.p=l.get(n[0].toLowerCase()),r+n[0].length):-1},q:eJ,Q:e8,s:e9,S:e3,u:eq,U:eY,V:eV,w:eW,W:eX,x:function(t,e,n){return S(t,r,e,n)},X:function(t,e,r){return S(t,n,e,r)},y:eK,Y:eH,Z:eG,"%":e4};function w(t,e){return function(r){var n,o,i,a=[],u=-1,c=0,s=t.length;for(r instanceof Date||(r=new Date(+r));++u<s;)37===t.charCodeAt(u)&&(a.push(t.slice(c,u)),null!=(o=eB[n=t.charAt(++u)])?n=t.charAt(++u):o="e"===n?" ":"0",(i=e[n])&&(n=i(r,o)),a.push(n),c=u+1);return a.push(t.slice(c,u)),a.join("")}}function j(t,e){return function(r){var n,o,i=eN(1900,void 0,1);if(S(i,t,r+="",0)!=r.length)return null;if("Q"in i)return new Date(i.Q);if("s"in i)return new Date(1e3*i.s+("L"in i?i.L:0));if(!e||"Z"in i||(i.Z=0),"p"in i&&(i.H=i.H%12+12*i.p),void 0===i.m&&(i.m="q"in i?i.q:0),"V"in i){if(i.V<1||i.V>53)return null;"w"in i||(i.w=1),"Z"in i?(n=(o=(n=eI(eN(i.y,0,1))).getUTCDay())>4||0===o?eb.ceil(n):eb(n),n=ea.offset(n,(i.V-1)*7),i.y=n.getUTCFullYear(),i.m=n.getUTCMonth(),i.d=n.getUTCDate()+(i.w+6)%7):(n=(o=(n=eD(eN(i.y,0,1))).getDay())>4||0===o?el.ceil(n):el(n),n=ei.offset(n,(i.V-1)*7),i.y=n.getFullYear(),i.m=n.getMonth(),i.d=n.getDate()+(i.w+6)%7)}else("W"in i||"U"in i)&&("w"in i||(i.w="u"in i?i.u%7:"W"in i?1:0),o="Z"in i?eI(eN(i.y,0,1)).getUTCDay():eD(eN(i.y,0,1)).getDay(),i.m=0,i.d="W"in i?(i.w+6)%7+7*i.W-(o+5)%7:i.w+7*i.U-(o+6)%7);return"Z"in i?(i.H+=i.Z/100|0,i.M+=i.Z%100,eI(i)):eD(i)}}function S(t,e,r,n){for(var o,i,a=0,u=e.length,c=r.length;a<u;){if(n>=c)return -1;if(37===(o=e.charCodeAt(a++))){if(!(i=O[(o=e.charAt(a++))in eB?e.charAt(a++):o])||(n=i(t,r,n))<0)return -1}else if(o!=r.charCodeAt(n++))return -1}return n}return g.x=w(r,g),g.X=w(n,g),g.c=w(e,g),x.x=w(r,x),x.X=w(n,x),x.c=w(e,x),{format:function(t){var e=w(t+="",g);return e.toString=function(){return t},e},parse:function(t){var e=j(t+="",!1);return e.toString=function(){return t},e},utcFormat:function(t){var e=w(t+="",x);return e.toString=function(){return t},e},utcParse:function(t){var e=j(t+="",!0);return e.toString=function(){return t},e}}}({dateTime:"%x, %X",date:"%-m/%-d/%Y",time:"%-I:%M:%S %p",periods:["AM","PM"],days:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],shortDays:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],months:["January","February","March","April","May","June","July","August","September","October","November","December"],shortMonths:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]})).format,u.parse,s=u.utcFormat,u.utcParse;var r2=r(22516),r5=r(76115);function r3(t){for(var e=t.length,r=Array(e);--e>=0;)r[e]=e;return r}function r6(t,e){return t[e]}function r7(t){let e=[];return e.key=t,e}var r4=r(95645),r8=r.n(r4),r9=r(99008),nt=r.n(r9),ne=r(77571),nr=r.n(ne),nn=r(86757),no=r.n(nn),ni=r(42715),na=r.n(ni),nu=r(13735),nc=r.n(nu),ns=r(11314),nl=r.n(ns),nf=r(82559),np=r.n(nf),nh=r(75551),nd=r.n(nh),ny=r(21652),nv=r.n(ny),nm=r(34935),nb=r.n(nm),ng=r(61134),nx=r.n(ng);function nO(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}var nw=function(t){return t},nj={},nS=function(t){return t===nj},nP=function(t){return function e(){return 0==arguments.length||1==arguments.length&&nS(arguments.length<=0?void 0:arguments[0])?e:t.apply(void 0,arguments)}},nA=function(t){return function t(e,r){return 1===e?r:nP(function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];var a=o.filter(function(t){return t!==nj}).length;return a>=e?r.apply(void 0,o):t(e-a,nP(function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];var i=o.map(function(t){return nS(t)?e.shift():t});return r.apply(void 0,((function(t){if(Array.isArray(t))return nO(t)})(i)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(i)||function(t,e){if(t){if("string"==typeof t)return nO(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nO(t,void 0)}}(i)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()).concat(e))}))})}(t.length,t)},nE=function(t,e){for(var r=[],n=t;n<e;++n)r[n-t]=n;return r},nk=nA(function(t,e){return Array.isArray(e)?e.map(t):Object.keys(e).map(function(t){return e[t]}).map(t)}),nM=function(){for(var t=arguments.length,e=Array(t),r=0;r<t;r++)e[r]=arguments[r];if(!e.length)return nw;var n=e.reverse(),o=n[0],i=n.slice(1);return function(){return i.reduce(function(t,e){return e(t)},o.apply(void 0,arguments))}},nT=function(t){return Array.isArray(t)?t.reverse():t.split("").reverse.join("")},n_=function(t){var e=null,r=null;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return e&&o.every(function(t,r){return t===e[r]})?r:(e=o,r=t.apply(void 0,o))}},nC=(nA(function(t,e,r){var n=+t;return n+r*(+e-n)}),nA(function(t,e,r){var n=e-+t;return(r-t)/(n=n||1/0)}),nA(function(t,e,r){var n=e-+t;return Math.max(0,Math.min(1,(r-t)/(n=n||1/0)))}),{rangeStep:function(t,e,r){for(var n=new(nx())(t),o=0,i=[];n.lt(e)&&o<1e5;)i.push(n.toNumber()),n=n.add(r),o++;return i},getDigitCount:function(t){return 0===t?1:Math.floor(new(nx())(t).abs().log(10).toNumber())+1}});function nD(t){return function(t){if(Array.isArray(t))return nB(t)}(t)||function(t){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||nN(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nI(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t)){var r=[],n=!0,o=!1,i=void 0;try{for(var a,u=t[Symbol.iterator]();!(n=(a=u.next()).done)&&(r.push(a.value),!e||r.length!==e);n=!0);}catch(t){o=!0,i=t}finally{try{n||null==u.return||u.return()}finally{if(o)throw i}}return r}}(t,e)||nN(t,e)||function(){throw TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nN(t,e){if(t){if("string"==typeof t)return nB(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nB(t,e)}}function nB(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nL(t){var e=nI(t,2),r=e[0],n=e[1],o=r,i=n;return r>n&&(o=n,i=r),[o,i]}function nR(t,e,r){if(t.lte(0))return new(nx())(0);var n=nC.getDigitCount(t.toNumber()),o=new(nx())(10).pow(n),i=t.div(o),a=1!==n?.05:.1,u=new(nx())(Math.ceil(i.div(a).toNumber())).add(r).mul(a).mul(o);return e?u:new(nx())(Math.ceil(u))}function nz(t,e,r){var n=1,o=new(nx())(t);if(!o.isint()&&r){var i=Math.abs(t);i<1?(n=new(nx())(10).pow(nC.getDigitCount(t)-1),o=new(nx())(Math.floor(o.div(n).toNumber())).mul(n)):i>1&&(o=new(nx())(Math.floor(t)))}else 0===t?o=new(nx())(Math.floor((e-1)/2)):r||(o=new(nx())(Math.floor(t)));var a=Math.floor((e-1)/2);return nM(nk(function(t){return o.add(new(nx())(t-a).mul(n)).toNumber()}),nE)(0,e)}var nU=n_(function(t){var e=nI(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),u=nI(nL([r,n]),2),c=u[0],s=u[1];if(c===-1/0||s===1/0){var l=s===1/0?[c].concat(nD(nE(0,o-1).map(function(){return 1/0}))):[].concat(nD(nE(0,o-1).map(function(){return-1/0})),[s]);return r>n?nT(l):l}if(c===s)return nz(c,o,i);var f=function t(e,r,n,o){var i,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:0;if(!Number.isFinite((r-e)/(n-1)))return{step:new(nx())(0),tickMin:new(nx())(0),tickMax:new(nx())(0)};var u=nR(new(nx())(r).sub(e).div(n-1),o,a),c=Math.ceil((i=e<=0&&r>=0?new(nx())(0):(i=new(nx())(e).add(r).div(2)).sub(new(nx())(i).mod(u))).sub(e).div(u).toNumber()),s=Math.ceil(new(nx())(r).sub(i).div(u).toNumber()),l=c+s+1;return l>n?t(e,r,n,o,a+1):(l<n&&(s=r>0?s+(n-l):s,c=r>0?c:c+(n-l)),{step:u,tickMin:i.sub(new(nx())(c).mul(u)),tickMax:i.add(new(nx())(s).mul(u))})}(c,s,a,i),p=f.step,h=f.tickMin,d=f.tickMax,y=nC.rangeStep(h,d.add(new(nx())(.1).mul(p)),p);return r>n?nT(y):y});n_(function(t){var e=nI(t,2),r=e[0],n=e[1],o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:6,i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=Math.max(o,2),u=nI(nL([r,n]),2),c=u[0],s=u[1];if(c===-1/0||s===1/0)return[r,n];if(c===s)return nz(c,o,i);var l=nR(new(nx())(s).sub(c).div(a-1),i,0),f=nM(nk(function(t){return new(nx())(c).add(new(nx())(t).mul(l)).toNumber()}),nE)(0,a).filter(function(t){return t>=c&&t<=s});return r>n?nT(f):f});var n$=n_(function(t,e){var r=nI(t,2),n=r[0],o=r[1],i=!(arguments.length>2)||void 0===arguments[2]||arguments[2],a=nI(nL([n,o]),2),u=a[0],c=a[1];if(u===-1/0||c===1/0)return[n,o];if(u===c)return[u];var s=nR(new(nx())(c).sub(u).div(Math.max(e,2)-1),i,0),l=[].concat(nD(nC.rangeStep(new(nx())(u),new(nx())(c).sub(new(nx())(.99).mul(s)),s)),[c]);return n>o?nT(l):l}),nF=r(13137),nZ=r(16630),nW=r(82944),nq=r(38569);function nY(t){return(nY="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function nV(t){return function(t){if(Array.isArray(t))return nX(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return nX(t,void 0);var r=Object.prototype.toString.call(t).slice(8,-1);if("Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r)return Array.from(t);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return nX(t,void 0)}}(t)||function(){throw TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function nX(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=Array(e);r<e;r++)n[r]=t[r];return n}function nH(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function nK(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?nH(Object(r),!0).forEach(function(e){nG(t,e,r[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):nH(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}function nG(t,e,r){var n;return(n=function(t,e){if("object"!=nY(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=nY(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(e,"string"),(e="symbol"==nY(n)?n:n+"")in t)?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function nJ(t,e,r){return nr()(t)||nr()(e)?r:(0,nZ.P2)(e)?nc()(t,e,r):no()(e)?e(t):r}function nQ(t,e,r,n){var o=nl()(t,function(t){return nJ(t,e)});if("number"===r){var i=o.filter(function(t){return(0,nZ.hj)(t)||parseFloat(t)});return i.length?[nt()(i),r8()(i)]:[1/0,-1/0]}return(n?o.filter(function(t){return!nr()(t)}):o).map(function(t){return(0,nZ.P2)(t)||t instanceof Date?t:""})}var n0=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2?arguments[2]:void 0,o=arguments.length>3?arguments[3]:void 0,i=-1,a=null!==(e=null==r?void 0:r.length)&&void 0!==e?e:0;if(a<=1)return 0;if(o&&"angleAxis"===o.axisType&&1e-6>=Math.abs(Math.abs(o.range[1]-o.range[0])-360))for(var u=o.range,c=0;c<a;c++){var s=c>0?n[c-1].coordinate:n[a-1].coordinate,l=n[c].coordinate,f=c>=a-1?n[0].coordinate:n[c+1].coordinate,p=void 0;if((0,nZ.uY)(l-s)!==(0,nZ.uY)(f-l)){var h=[];if((0,nZ.uY)(f-l)===(0,nZ.uY)(u[1]-u[0])){p=f;var d=l+u[1]-u[0];h[0]=Math.min(d,(d+s)/2),h[1]=Math.max(d,(d+s)/2)}else{p=s;var y=f+u[1]-u[0];h[0]=Math.min(l,(y+l)/2),h[1]=Math.max(l,(y+l)/2)}var v=[Math.min(l,(p+l)/2),Math.max(l,(p+l)/2)];if(t>v[0]&&t<=v[1]||t>=h[0]&&t<=h[1]){i=n[c].index;break}}else{var m=Math.min(s,f),b=Math.max(s,f);if(t>(m+l)/2&&t<=(b+l)/2){i=n[c].index;break}}}else for(var g=0;g<a;g++)if(0===g&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g>0&&g<a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2&&t<=(r[g].coordinate+r[g+1].coordinate)/2||g===a-1&&t>(r[g].coordinate+r[g-1].coordinate)/2){i=r[g].index;break}return i},n1=function(t){var e,r,n=t.type.displayName,o=null!==(e=t.type)&&void 0!==e&&e.defaultProps?nK(nK({},t.type.defaultProps),t.props):t.props,i=o.stroke,a=o.fill;switch(n){case"Line":r=i;break;case"Area":case"Radar":r=i&&"none"!==i?i:a;break;default:r=a}return r},n2=function(t){var e=t.barSize,r=t.totalSize,n=t.stackGroups,o=void 0===n?{}:n;if(!o)return{};for(var i={},a=Object.keys(o),u=0,c=a.length;u<c;u++)for(var s=o[a[u]].stackGroups,l=Object.keys(s),f=0,p=l.length;f<p;f++){var h=s[l[f]],d=h.items,y=h.cateAxisId,v=d.filter(function(t){return(0,nW.Gf)(t.type).indexOf("Bar")>=0});if(v&&v.length){var m=v[0].type.defaultProps,b=void 0!==m?nK(nK({},m),v[0].props):v[0].props,g=b.barSize,x=b[y];i[x]||(i[x]=[]);var O=nr()(g)?e:g;i[x].push({item:v[0],stackList:v.slice(1),barSize:nr()(O)?void 0:(0,nZ.h1)(O,r,0)})}}return i},n5=function(t){var e,r=t.barGap,n=t.barCategoryGap,o=t.bandSize,i=t.sizeList,a=void 0===i?[]:i,u=t.maxBarSize,c=a.length;if(c<1)return null;var s=(0,nZ.h1)(r,o,0,!0),l=[];if(a[0].barSize===+a[0].barSize){var f=!1,p=o/c,h=a.reduce(function(t,e){return t+e.barSize||0},0);(h+=(c-1)*s)>=o&&(h-=(c-1)*s,s=0),h>=o&&p>0&&(f=!0,p*=.9,h=c*p);var d={offset:((o-h)/2>>0)-s,size:0};e=a.reduce(function(t,e){var r={item:e.item,position:{offset:d.offset+d.size+s,size:f?p:e.barSize}},n=[].concat(nV(t),[r]);return d=n[n.length-1].position,e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:d})}),n},l)}else{var y=(0,nZ.h1)(n,o,0,!0);o-2*y-(c-1)*s<=0&&(s=0);var v=(o-2*y-(c-1)*s)/c;v>1&&(v>>=0);var m=u===+u?Math.min(v,u):v;e=a.reduce(function(t,e,r){var n=[].concat(nV(t),[{item:e.item,position:{offset:y+(v+s)*r+(v-m)/2,size:m}}]);return e.stackList&&e.stackList.length&&e.stackList.forEach(function(t){n.push({item:t,position:n[n.length-1].position})}),n},l)}return e},n3=function(t,e,r,n){var o=r.children,i=r.width,a=r.margin,u=i-(a.left||0)-(a.right||0),c=(0,nq.z)({children:o,legendWidth:u});if(c){var s=n||{},l=s.width,f=s.height,p=c.align,h=c.verticalAlign,d=c.layout;if(("vertical"===d||"horizontal"===d&&"middle"===h)&&"center"!==p&&(0,nZ.hj)(t[p]))return nK(nK({},t),{},nG({},p,t[p]+(l||0)));if(("horizontal"===d||"vertical"===d&&"center"===p)&&"middle"!==h&&(0,nZ.hj)(t[h]))return nK(nK({},t),{},nG({},h,t[h]+(f||0)))}return t},n6=function(t,e,r,n,o){var i=e.props.children,a=(0,nW.NN)(i,nF.W).filter(function(t){var e;return e=t.props.direction,!!nr()(o)||("horizontal"===n?"yAxis"===o:"vertical"===n||"x"===e?"xAxis"===o:"y"!==e||"yAxis"===o)});if(a&&a.length){var u=a.map(function(t){return t.props.dataKey});return t.reduce(function(t,e){var n=nJ(e,r);if(nr()(n))return t;var o=Array.isArray(n)?[nt()(n),r8()(n)]:[n,n],i=u.reduce(function(t,r){var n=nJ(e,r,0),i=o[0]-Math.abs(Array.isArray(n)?n[0]:n),a=o[1]+Math.abs(Array.isArray(n)?n[1]:n);return[Math.min(i,t[0]),Math.max(a,t[1])]},[1/0,-1/0]);return[Math.min(i[0],t[0]),Math.max(i[1],t[1])]},[1/0,-1/0])}return null},n7=function(t,e,r,n,o){var i=e.map(function(e){return n6(t,e,r,o,n)}).filter(function(t){return!nr()(t)});return i&&i.length?i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]):null},n4=function(t,e,r,n,o){var i=e.map(function(e){var i=e.props.dataKey;return"number"===r&&i&&n6(t,e,i,n)||nQ(t,i,r,o)});if("number"===r)return i.reduce(function(t,e){return[Math.min(t[0],e[0]),Math.max(t[1],e[1])]},[1/0,-1/0]);var a={};return i.reduce(function(t,e){for(var r=0,n=e.length;r<n;r++)a[e[r]]||(a[e[r]]=!0,t.push(e[r]));return t},[])},n8=function(t,e){return"horizontal"===t&&"xAxis"===e||"vertical"===t&&"yAxis"===e||"centric"===t&&"angleAxis"===e||"radial"===t&&"radiusAxis"===e},n9=function(t,e,r){if(!t)return null;var n=t.scale,o=t.duplicateDomain,i=t.type,a=t.range,u="scaleBand"===t.realScaleType?n.bandwidth()/2:2,c=(e||r)&&"category"===i&&n.bandwidth?n.bandwidth()/u:0;return(c="angleAxis"===t.axisType&&(null==a?void 0:a.length)>=2?2*(0,nZ.uY)(a[0]-a[1])*c:c,e&&(t.ticks||t.niceTicks))?(t.ticks||t.niceTicks).map(function(t){return{coordinate:n(o?o.indexOf(t):t)+c,value:t,offset:c}}).filter(function(t){return!np()(t.coordinate)}):t.isCategorical&&t.categoricalDomain?t.categoricalDomain.map(function(t,e){return{coordinate:n(t)+c,value:t,index:e,offset:c}}):n.ticks&&!r?n.ticks(t.tickCount).map(function(t){return{coordinate:n(t)+c,value:t,offset:c}}):n.domain().map(function(t,e){return{coordinate:n(t)+c,value:o?o[t]:t,index:e,offset:c}})},ot=new WeakMap,oe=function(t,e){if("function"!=typeof e)return t;ot.has(t)||ot.set(t,new WeakMap);var r=ot.get(t);if(r.has(e))return r.get(e);var n=function(){t.apply(void 0,arguments),e.apply(void 0,arguments)};return r.set(e,n),n},or=function(t,e,r){var n=t.scale,o=t.type,i=t.layout,a=t.axisType;if("auto"===n)return"radial"===i&&"radiusAxis"===a?{scale:f.Z(),realScaleType:"band"}:"radial"===i&&"angleAxis"===a?{scale:tB(),realScaleType:"linear"}:"category"===o&&e&&(e.indexOf("LineChart")>=0||e.indexOf("AreaChart")>=0||e.indexOf("ComposedChart")>=0&&!r)?{scale:f.x(),realScaleType:"point"}:"category"===o?{scale:f.Z(),realScaleType:"band"}:{scale:tB(),realScaleType:"linear"};if(na()(n)){var u="scale".concat(nd()(n));return{scale:(l[u]||f.x)(),realScaleType:l[u]?u:"point"}}return no()(n)?{scale:n}:{scale:f.x(),realScaleType:"point"}},on=function(t){var e=t.domain();if(e&&!(e.length<=2)){var r=e.length,n=t.range(),o=Math.min(n[0],n[1])-1e-4,i=Math.max(n[0],n[1])+1e-4,a=t(e[0]),u=t(e[r-1]);(a<o||a>i||u<o||u>i)&&t.domain([e[0],e[r-1]])}},oo=function(t,e){if(!t)return null;for(var r=0,n=t.length;r<n;r++)if(t[r].item===e)return t[r].position;return null},oi=function(t,e){if(!e||2!==e.length||!(0,nZ.hj)(e[0])||!(0,nZ.hj)(e[1]))return t;var r=Math.min(e[0],e[1]),n=Math.max(e[0],e[1]),o=[t[0],t[1]];return(!(0,nZ.hj)(t[0])||t[0]<r)&&(o[0]=r),(!(0,nZ.hj)(t[1])||t[1]>n)&&(o[1]=n),o[0]>n&&(o[0]=n),o[1]<r&&(o[1]=r),o},oa={sign:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0,a=0;a<e;++a){var u=np()(t[a][r][1])?t[a][r][0]:t[a][r][1];u>=0?(t[a][r][0]=o,t[a][r][1]=o+u,o=t[a][r][1]):(t[a][r][0]=i,t[a][r][1]=i+u,i=t[a][r][1])}},expand:function(t,e){if((n=t.length)>0){for(var r,n,o,i=0,a=t[0].length;i<a;++i){for(o=r=0;r<n;++r)o+=t[r][i][1]||0;if(o)for(r=0;r<n;++r)t[r][i][1]/=o}r1(t,e)}},none:r1,silhouette:function(t,e){if((r=t.length)>0){for(var r,n=0,o=t[e[0]],i=o.length;n<i;++n){for(var a=0,u=0;a<r;++a)u+=t[a][n][1]||0;o[n][1]+=o[n][0]=-u/2}r1(t,e)}},wiggle:function(t,e){if((o=t.length)>0&&(n=(r=t[e[0]]).length)>0){for(var r,n,o,i=0,a=1;a<n;++a){for(var u=0,c=0,s=0;u<o;++u){for(var l=t[e[u]],f=l[a][1]||0,p=(f-(l[a-1][1]||0))/2,h=0;h<u;++h){var d=t[e[h]];p+=(d[a][1]||0)-(d[a-1][1]||0)}c+=f,s+=p*f}r[a-1][1]+=r[a-1][0]=i,c&&(i-=s/c)}r[a-1][1]+=r[a-1][0]=i,r1(t,e)}},positive:function(t){var e=t.length;if(!(e<=0))for(var r=0,n=t[0].length;r<n;++r)for(var o=0,i=0;i<e;++i){var a=np()(t[i][r][1])?t[i][r][0]:t[i][r][1];a>=0?(t[i][r][0]=o,t[i][r][1]=o+a,o=t[i][r][1]):(t[i][r][0]=0,t[i][r][1]=0)}}},ou=function(t,e,r){var n=e.map(function(t){return t.props.dataKey}),o=oa[r];return(function(){var t=(0,r5.Z)([]),e=r3,r=r1,n=r6;function o(o){var i,a,u=Array.from(t.apply(this,arguments),r7),c=u.length,s=-1;for(let t of o)for(i=0,++s;i<c;++i)(u[i][s]=[0,+n(t,u[i].key,s,o)]).data=t;for(i=0,a=(0,r2.Z)(e(u));i<c;++i)u[a[i]].index=i;return r(u,a),u}return o.keys=function(e){return arguments.length?(t="function"==typeof e?e:(0,r5.Z)(Array.from(e)),o):t},o.value=function(t){return arguments.length?(n="function"==typeof t?t:(0,r5.Z)(+t),o):n},o.order=function(t){return arguments.length?(e=null==t?r3:"function"==typeof t?t:(0,r5.Z)(Array.from(t)),o):e},o.offset=function(t){return arguments.length?(r=null==t?r1:t,o):r},o})().keys(n).value(function(t,e){return+nJ(t,e,0)}).order(r3).offset(o)(t)},oc=function(t,e,r,n,o,i){if(!t)return null;var a=(i?e.reverse():e).reduce(function(t,e){var o,i=null!==(o=e.type)&&void 0!==o&&o.defaultProps?nK(nK({},e.type.defaultProps),e.props):e.props,a=i.stackId;if(i.hide)return t;var u=i[r],c=t[u]||{hasStack:!1,stackGroups:{}};if((0,nZ.P2)(a)){var s=c.stackGroups[a]||{numericAxisId:r,cateAxisId:n,items:[]};s.items.push(e),c.hasStack=!0,c.stackGroups[a]=s}else c.stackGroups[(0,nZ.EL)("_stackId_")]={numericAxisId:r,cateAxisId:n,items:[e]};return nK(nK({},t),{},nG({},u,c))},{});return Object.keys(a).reduce(function(e,i){var u=a[i];return u.hasStack&&(u.stackGroups=Object.keys(u.stackGroups).reduce(function(e,i){var a=u.stackGroups[i];return nK(nK({},e),{},nG({},i,{numericAxisId:r,cateAxisId:n,items:a.items,stackedData:ou(t,a.items,o)}))},{})),nK(nK({},e),{},nG({},i,u))},{})},os=function(t,e){var r=e.realScaleType,n=e.type,o=e.tickCount,i=e.originalDomain,a=e.allowDecimals,u=r||e.scale;if("auto"!==u&&"linear"!==u)return null;if(o&&"number"===n&&i&&("auto"===i[0]||"auto"===i[1])){var c=t.domain();if(!c.length)return null;var s=nU(c,o,a);return t.domain([nt()(s),r8()(s)]),{niceTicks:s}}return o&&"number"===n?{niceTicks:n$(t.domain(),o,a)}:null},ol=function(t){var e=t.axis,r=t.ticks,n=t.offset,o=t.bandSize,i=t.entry,a=t.index;if("category"===e.type)return r[a]?r[a].coordinate+n:null;var u=nJ(i,e.dataKey,e.domain[a]);return nr()(u)?null:e.scale(u)-o/2+n},of=function(t){var e=t.numericAxis,r=e.scale.domain();if("number"===e.type){var n=Math.min(r[0],r[1]),o=Math.max(r[0],r[1]);return n<=0&&o>=0?0:o<0?o:n}return r[0]},op=function(t,e){var r,n=(null!==(r=t.type)&&void 0!==r&&r.defaultProps?nK(nK({},t.type.defaultProps),t.props):t.props).stackId;if((0,nZ.P2)(n)){var o=e[n];if(o){var i=o.items.indexOf(t);return i>=0?o.stackedData[i]:null}}return null},oh=function(t,e,r){return Object.keys(t).reduce(function(n,o){var i=t[o].stackedData.reduce(function(t,n){var o=n.slice(e,r+1).reduce(function(t,e){return[nt()(e.concat([t[0]]).filter(nZ.hj)),r8()(e.concat([t[1]]).filter(nZ.hj))]},[1/0,-1/0]);return[Math.min(t[0],o[0]),Math.max(t[1],o[1])]},[1/0,-1/0]);return[Math.min(i[0],n[0]),Math.max(i[1],n[1])]},[1/0,-1/0]).map(function(t){return t===1/0||t===-1/0?0:t})},od=/^dataMin[\s]*-[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,oy=/^dataMax[\s]*\+[\s]*([0-9]+([.]{1}[0-9]+){0,1})$/,ov=function(t,e,r){if(no()(t))return t(e,r);if(!Array.isArray(t))return e;var n=[];if((0,nZ.hj)(t[0]))n[0]=r?t[0]:Math.min(t[0],e[0]);else if(od.test(t[0])){var o=+od.exec(t[0])[1];n[0]=e[0]-o}else no()(t[0])?n[0]=t[0](e[0]):n[0]=e[0];if((0,nZ.hj)(t[1]))n[1]=r?t[1]:Math.max(t[1],e[1]);else if(oy.test(t[1])){var i=+oy.exec(t[1])[1];n[1]=e[1]+i}else no()(t[1])?n[1]=t[1](e[1]):n[1]=e[1];return n},om=function(t,e,r){if(t&&t.scale&&t.scale.bandwidth){var n=t.scale.bandwidth();if(!r||n>0)return n}if(t&&e&&e.length>=2){for(var o=nb()(e,function(t){return t.coordinate}),i=1/0,a=1,u=o.length;a<u;a++){var c=o[a],s=o[a-1];i=Math.min((c.coordinate||0)-(s.coordinate||0),i)}return i===1/0?0:i}return r?void 0:0},ob=function(t,e,r){return!t||!t.length||nv()(t,nc()(r,"type.defaultProps.domain"))?e:t},og=function(t,e){var r=t.type.defaultProps?nK(nK({},t.type.defaultProps),t.props):t.props,n=r.dataKey,o=r.name,i=r.unit,a=r.formatter,u=r.tooltipType,c=r.chartType,s=r.hide;return nK(nK({},(0,nW.L6)(t,!1)),{},{dataKey:n,unit:i,formatter:a,name:o||n,color:n1(t),value:nJ(e,n),type:u,payload:e,chartType:c,hide:s})}},4094:function(t,e,r){"use strict";r.d(e,{os:function(){return f},xE:function(){return l}});var n=r(34067);function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function i(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function a(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?i(Object(r),!0).forEach(function(e){var n,i;n=e,i=r[e],(n=function(t){var e=function(t,e){if("object"!=o(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=o(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==o(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[n]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):i(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var u={widthCache:{},cacheCount:0},c={position:"absolute",top:"-20000px",left:0,padding:0,margin:0,border:"none",whiteSpace:"pre"},s="recharts_measurement_span",l=function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(null==t||n.x.isSsr)return{width:0,height:0};var o=(Object.keys(e=a({},r)).forEach(function(t){e[t]||delete e[t]}),e),i=JSON.stringify({text:t,copyStyle:o});if(u.widthCache[i])return u.widthCache[i];try{var l=document.getElementById(s);l||((l=document.createElement("span")).setAttribute("id",s),l.setAttribute("aria-hidden","true"),document.body.appendChild(l));var f=a(a({},c),o);Object.assign(l.style,f),l.textContent="".concat(t);var p=l.getBoundingClientRect(),h={width:p.width,height:p.height};return u.widthCache[i]=h,++u.cacheCount>2e3&&(u.cacheCount=0,u.widthCache={}),h}catch(t){return{width:0,height:0}}},f=function(t){return{top:t.top+window.scrollY-document.documentElement.clientTop,left:t.left+window.scrollX-document.documentElement.clientLeft}}},16630:function(t,e,r){"use strict";r.d(e,{Ap:function(){return O},EL:function(){return v},Kt:function(){return b},P2:function(){return d},bv:function(){return g},h1:function(){return m},hU:function(){return p},hj:function(){return h},k4:function(){return x},uY:function(){return f}});var n=r(42715),o=r.n(n),i=r(82559),a=r.n(i),u=r(13735),c=r.n(u),s=r(22345),l=r.n(s),f=function(t){return 0===t?0:t>0?1:-1},p=function(t){return o()(t)&&t.indexOf("%")===t.length-1},h=function(t){return l()(t)&&!a()(t)},d=function(t){return h(t)||o()(t)},y=0,v=function(t){var e=++y;return"".concat(t||"").concat(e)},m=function(t,e){var r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if(!h(t)&&!o()(t))return n;if(p(t)){var u=t.indexOf("%");r=e*parseFloat(t.slice(0,u))/100}else r=+t;return a()(r)&&(r=n),i&&r>e&&(r=e),r},b=function(t){if(!t)return null;var e=Object.keys(t);return e&&e.length?t[e[0]]:null},g=function(t){if(!Array.isArray(t))return!1;for(var e=t.length,r={},n=0;n<e;n++){if(r[t[n]])return!0;r[t[n]]=!0}return!1},x=function(t,e){return h(t)&&h(e)?function(r){return t+r*(e-t)}:function(){return e}};function O(t,e,r){return t&&t.length?t.find(function(t){return t&&("function"==typeof e?e(t):c()(t,e))===r}):null}},34067:function(t,e,r){"use strict";r.d(e,{x:function(){return n}});var n={isSsr:!("undefined"!=typeof window&&window.document&&window.document.createElement&&window.setTimeout),get:function(t){return n[t]},set:function(t,e){if("string"==typeof t)n[t]=e;else{var r=Object.keys(t);r&&r.length&&r.forEach(function(e){n[e]=t[e]})}}}},1175:function(t,e,r){"use strict";r.d(e,{Z:function(){return n}});var n=function(t,e){for(var r=arguments.length,n=Array(r>2?r-2:0),o=2;o<r;o++)n[o-2]=arguments[o]}},39206:function(t,e,r){"use strict";function n(t){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function o(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function i(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?o(Object(r),!0).forEach(function(e){var o,i;o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=n(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var o=r.call(t,e||"default");if("object"!=n(o))return o;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==n(e)?e:e+""}(o))in t?Object.defineProperty(t,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[o]=i}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):o(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}r.d(e,{Wk:function(){return a},op:function(){return u},z3:function(){return f}}),r(77571),r(2265),r(86757);var a=Math.PI/180,u=function(t,e,r,n){return{x:t+Math.cos(-a*n)*r,y:e+Math.sin(-a*n)*r}},c=function(t,e){var r=t.x,n=t.y;return Math.sqrt(Math.pow(r-e.x,2)+Math.pow(n-e.y,2))},s=function(t,e){var r=t.x,n=t.y,o=e.cx,i=e.cy,a=c({x:r,y:n},{x:o,y:i});if(a<=0)return{radius:a};var u=Math.acos((r-o)/a);return n>i&&(u=2*Math.PI-u),{radius:a,angle:180*u/Math.PI,angleInRadian:u}},l=function(t){var e=t.startAngle,r=t.endAngle,n=Math.min(Math.floor(e/360),Math.floor(r/360));return{startAngle:e-360*n,endAngle:r-360*n}},f=function(t,e){var r,n=s({x:t.x,y:t.y},e),o=n.radius,a=n.angle,u=e.innerRadius,c=e.outerRadius;if(o<u||o>c)return!1;if(0===o)return!0;var f=l(e),p=f.startAngle,h=f.endAngle,d=a;if(p<=h){for(;d>h;)d-=360;for(;d<p;)d+=360;r=d>=p&&d<=h}else{for(;d>p;)d-=360;for(;d<h;)d+=360;r=d>=h&&d<=p}return r?i(i({},e),{},{radius:o,angle:d+360*Math.min(Math.floor(e.startAngle/360),Math.floor(e.endAngle/360))}):null}},82944:function(t,e,r){"use strict";r.d(e,{$R:function(){return B},Bh:function(){return N},Gf:function(){return w},L6:function(){return _},NN:function(){return A},TT:function(){return k},eu:function(){return I},rL:function(){return C},sP:function(){return E}});var n=r(13735),o=r.n(n),i=r(77571),a=r.n(i),u=r(42715),c=r.n(u),s=r(86757),l=r.n(s),f=r(28302),p=r.n(f),h=r(2265),d=r(14326),y=r(16630),v=r(46485),m=r(41637),b=["children"],g=["children"];function x(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r={};for(var n in t)if(Object.prototype.hasOwnProperty.call(t,n)){if(e.indexOf(n)>=0)continue;r[n]=t[n]}return r}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],!(e.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}var O={click:"onClick",mousedown:"onMouseDown",mouseup:"onMouseUp",mouseover:"onMouseOver",mousemove:"onMouseMove",mouseout:"onMouseOut",mouseenter:"onMouseEnter",mouseleave:"onMouseLeave",touchcancel:"onTouchCancel",touchend:"onTouchEnd",touchmove:"onTouchMove",touchstart:"onTouchStart",contextmenu:"onContextMenu",dblclick:"onDoubleClick"},w=function(t){return"string"==typeof t?t:t?t.displayName||t.name||"Component":""},j=null,S=null,P=function t(e){if(e===j&&Array.isArray(S))return S;var r=[];return h.Children.forEach(e,function(e){a()(e)||((0,d.isFragment)(e)?r=r.concat(t(e.props.children)):r.push(e))}),S=r,j=e,r};function A(t,e){var r=[],n=[];return n=Array.isArray(e)?e.map(function(t){return w(t)}):[w(e)],P(t).forEach(function(t){var e=o()(t,"type.displayName")||o()(t,"type.name");-1!==n.indexOf(e)&&r.push(t)}),r}function E(t,e){var r=A(t,e);return r&&r[0]}var k=function(t){if(!t||!t.props)return!1;var e=t.props,r=e.width,n=e.height;return!!(0,y.hj)(r)&&!(r<=0)&&!!(0,y.hj)(n)&&!(n<=0)},M=["a","altGlyph","altGlyphDef","altGlyphItem","animate","animateColor","animateMotion","animateTransform","circle","clipPath","color-profile","cursor","defs","desc","ellipse","feBlend","feColormatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","filter","font","font-face","font-face-format","font-face-name","font-face-url","foreignObject","g","glyph","glyphRef","hkern","image","line","lineGradient","marker","mask","metadata","missing-glyph","mpath","path","pattern","polygon","polyline","radialGradient","rect","script","set","stop","style","svg","switch","symbol","text","textPath","title","tref","tspan","use","view","vkern"],T=function(t,e,r,n){var o,i=null!==(o=null===m.ry||void 0===m.ry?void 0:m.ry[n])&&void 0!==o?o:[];return!l()(t)&&(n&&i.includes(e)||m.Yh.includes(e))||r&&m.nv.includes(e)},_=function(t,e,r){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var n=t;if((0,h.isValidElement)(t)&&(n=t.props),!p()(n))return null;var o={};return Object.keys(n).forEach(function(t){var i;T(null===(i=n)||void 0===i?void 0:i[t],t,e,r)&&(o[t]=n[t])}),o},C=function t(e,r){if(e===r)return!0;var n=h.Children.count(e);if(n!==h.Children.count(r))return!1;if(0===n)return!0;if(1===n)return D(Array.isArray(e)?e[0]:e,Array.isArray(r)?r[0]:r);for(var o=0;o<n;o++){var i=e[o],a=r[o];if(Array.isArray(i)||Array.isArray(a)){if(!t(i,a))return!1}else if(!D(i,a))return!1}return!0},D=function(t,e){if(a()(t)&&a()(e))return!0;if(!a()(t)&&!a()(e)){var r=t.props||{},n=r.children,o=x(r,b),i=e.props||{},u=i.children,c=x(i,g);if(n&&u)return(0,v.w)(o,c)&&C(n,u);if(!n&&!u)return(0,v.w)(o,c)}return!1},I=function(t,e){var r=[],n={};return P(t).forEach(function(t,o){if(t&&t.type&&c()(t.type)&&M.indexOf(t.type)>=0)r.push(t);else if(t){var i=w(t.type),a=e[i]||{},u=a.handler,s=a.once;if(u&&(!s||!n[i])){var l=u(t,i,o);r.push(l),n[i]=!0}}}),r},N=function(t){var e=t&&t.type;return e&&O[e]?O[e]:null},B=function(t,e){return P(e).indexOf(t)}},46485:function(t,e,r){"use strict";function n(t,e){for(var r in t)if(({}).hasOwnProperty.call(t,r)&&(!({}).hasOwnProperty.call(e,r)||t[r]!==e[r]))return!1;for(var n in e)if(({}).hasOwnProperty.call(e,n)&&!({}).hasOwnProperty.call(t,n))return!1;return!0}r.d(e,{w:function(){return n}})},38569:function(t,e,r){"use strict";r.d(e,{z:function(){return s}});var n=r(22190),o=r(85355),i=r(82944);function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function u(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable})),r.push.apply(r,n)}return r}function c(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?u(Object(r),!0).forEach(function(e){var n,o;n=e,o=r[e],(n=function(t){var e=function(t,e){if("object"!=a(t)||!t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!=a(n))return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"==a(e)?e:e+""}(n))in t?Object.defineProperty(t,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):t[n]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))})}return t}var s=function(t){var e,r=t.children,a=t.formattedGraphicalItems,u=t.legendWidth,s=t.legendContent,l=(0,i.sP)(r,n.D);if(!l)return null;var f=n.D.defaultProps,p=void 0!==f?c(c({},f),l.props):{};return e=l.props&&l.props.payload?l.props&&l.props.payload:"children"===s?(a||[]).reduce(function(t,e){var r=e.item,n=e.props,o=n.sectors||n.data||[];return t.concat(o.map(function(t){return{type:l.props.iconType||r.props.legendType,value:t.name,color:t.fill,payload:t}}))},[]):(a||[]).map(function(t){var e=t.item,r=e.type.defaultProps,n=void 0!==r?c(c({},r),e.props):{},i=n.dataKey,a=n.name,u=n.legendType;return{inactive:n.hide,dataKey:i,type:p.iconType||u||"square",color:(0,o.fk)(e),value:a||i,payload:n}}),c(c(c({},p),n.D.getWithHeight(l,u)),{},{payload:e,item:l})}},93528:function(t,e,r){"use strict";r.d(e,{z:function(){return u}});var n=r(47230),o=r.n(n),i=r(86757),a=r.n(i);function u(t,e,r){return!0===e?o()(t,r):a()(e)?o()(t,e):t}},41637:function(t,e,r){"use strict";r.d(e,{Yh:function(){return u},Ym:function(){return f},bw:function(){return p},nv:function(){return l},ry:function(){return s}});var n=r(2265),o=r(28302),i=r.n(o);function a(t){return(a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var u=["aria-activedescendant","aria-atomic","aria-autocomplete","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colspan","aria-controls","aria-current","aria-describedby","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-modal","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext","className","color","height","id","lang","max","media","method","min","name","style","target","width","role","tabIndex","accentHeight","accumulate","additive","alignmentBaseline","allowReorder","alphabetic","amplitude","arabicForm","ascent","attributeName","attributeType","autoReverse","azimuth","baseFrequency","baselineShift","baseProfile","bbox","begin","bias","by","calcMode","capHeight","clip","clipPath","clipPathUnits","clipRule","colorInterpolation","colorInterpolationFilters","colorProfile","colorRendering","contentScriptType","contentStyleType","cursor","cx","cy","d","decelerate","descent","diffuseConstant","direction","display","divisor","dominantBaseline","dur","dx","dy","edgeMode","elevation","enableBackground","end","exponent","externalResourcesRequired","fill","fillOpacity","fillRule","filter","filterRes","filterUnits","floodColor","floodOpacity","focusable","fontFamily","fontSize","fontSizeAdjust","fontStretch","fontStyle","fontVariant","fontWeight","format","from","fx","fy","g1","g2","glyphName","glyphOrientationHorizontal","glyphOrientationVertical","glyphRef","gradientTransform","gradientUnits","hanging","horizAdvX","horizOriginX","href","ideographic","imageRendering","in2","in","intercept","k1","k2","k3","k4","k","kernelMatrix","kernelUnitLength","kerning","keyPoints","keySplines","keyTimes","lengthAdjust","letterSpacing","lightingColor","limitingConeAngle","local","markerEnd","markerHeight","markerMid","markerStart","markerUnits","markerWidth","mask","maskContentUnits","maskUnits","mathematical","mode","numOctaves","offset","opacity","operator","order","orient","orientation","origin","overflow","overlinePosition","overlineThickness","paintOrder","panose1","pathLength","patternContentUnits","patternTransform","patternUnits","pointerEvents","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","r","radius","refX","refY","renderingIntent","repeatCount","repeatDur","requiredExtensions","requiredFeatures","restart","result","rotate","rx","ry","seed","shapeRendering","slope","spacing","specularConstant","specularExponent","speed","spreadMethod","startOffset","stdDeviation","stemh","stemv","stitchTiles","stopColor","stopOpacity","strikethroughPosition","strikethroughThickness","string","stroke","strokeDasharray","strokeDashoffset","strokeLinecap","strokeLinejoin","strokeMiterlimit","strokeOpacity","strokeWidth","surfaceScale","systemLanguage","tableValues","targetX","targetY","textAnchor","textDecoration","textLength","textRendering","to","transform","u1","u2","underlinePosition","underlineThickness","unicode","unicodeBidi","unicodeRange","unitsPerEm","vAlphabetic","values","vectorEffect","version","vertAdvY","vertOriginX","vertOriginY","vHanging","vIdeographic","viewTarget","visibility","vMathematical","widths","wordSpacing","writingMode","x1","x2","x","xChannelSelector","xHeight","xlinkActuate","xlinkArcrole","xlinkHref","xlinkRole","xlinkShow","xlinkTitle","xlinkType","xmlBase","xmlLang","xmlns","xmlnsXlink","xmlSpace","y1","y2","y","yChannelSelector","z","zoomAndPan","ref","key","angle"],c=["points","pathLength"],s={svg:["viewBox","children"],polygon:c,polyline:c},l=["dangerouslySetInnerHTML","onCopy","onCopyCapture","onCut","onCutCapture","onPaste","onPasteCapture","onCompositionEnd","onCompositionEndCapture","onCompositionStart","onCompositionStartCapture","onCompositionUpdate","onCompositionUpdateCapture","onFocus","onFocusCapture","onBlur","onBlurCapture","onChange","onChangeCapture","onBeforeInput","onBeforeInputCapture","onInput","onInputCapture","onReset","onResetCapture","onSubmit","onSubmitCapture","onInvalid","onInvalidCapture","onLoad","onLoadCapture","onError","onErrorCapture","onKeyDown","onKeyDownCapture","onKeyPress","onKeyPressCapture","onKeyUp","onKeyUpCapture","onAbort","onAbortCapture","onCanPlay","onCanPlayCapture","onCanPlayThrough","onCanPlayThroughCapture","onDurationChange","onDurationChangeCapture","onEmptied","onEmptiedCapture","onEncrypted","onEncryptedCapture","onEnded","onEndedCapture","onLoadedData","onLoadedDataCapture","onLoadedMetadata","onLoadedMetadataCapture","onLoadStart","onLoadStartCapture","onPause","onPauseCapture","onPlay","onPlayCapture","onPlaying","onPlayingCapture","onProgress","onProgressCapture","onRateChange","onRateChangeCapture","onSeeked","onSeekedCapture","onSeeking","onSeekingCapture","onStalled","onStalledCapture","onSuspend","onSuspendCapture","onTimeUpdate","onTimeUpdateCapture","onVolumeChange","onVolumeChangeCapture","onWaiting","onWaitingCapture","onAuxClick","onAuxClickCapture","onClick","onClickCapture","onContextMenu","onContextMenuCapture","onDoubleClick","onDoubleClickCapture","onDrag","onDragCapture","onDragEnd","onDragEndCapture","onDragEnter","onDragEnterCapture","onDragExit","onDragExitCapture","onDragLeave","onDragLeaveCapture","onDragOver","onDragOverCapture","onDragStart","onDragStartCapture","onDrop","onDropCapture","onMouseDown","onMouseDownCapture","onMouseEnter","onMouseLeave","onMouseMove","onMouseMoveCapture","onMouseOut","onMouseOutCapture","onMouseOver","onMouseOverCapture","onMouseUp","onMouseUpCapture","onSelect","onSelectCapture","onTouchCancel","onTouchCancelCapture","onTouchEnd","onTouchEndCapture","onTouchMove","onTouchMoveCapture","onTouchStart","onTouchStartCapture","onPointerDown","onPointerDownCapture","onPointerMove","onPointerMoveCapture","onPointerUp","onPointerUpCapture","onPointerCancel","onPointerCancelCapture","onPointerEnter","onPointerEnterCapture","onPointerLeave","onPointerLeaveCapture","onPointerOver","onPointerOverCapture","onPointerOut","onPointerOutCapture","onGotPointerCapture","onGotPointerCaptureCapture","onLostPointerCapture","onLostPointerCaptureCapture","onScroll","onScrollCapture","onWheel","onWheelCapture","onAnimationStart","onAnimationStartCapture","onAnimationEnd","onAnimationEndCapture","onAnimationIteration","onAnimationIterationCapture","onTransitionEnd","onTransitionEndCapture"],f=function(t,e){if(!t||"function"==typeof t||"boolean"==typeof t)return null;var r=t;if((0,n.isValidElement)(t)&&(r=t.props),!i()(r))return null;var o={};return Object.keys(r).forEach(function(t){l.includes(t)&&(o[t]=e||function(e){return r[t](r,e)})}),o},p=function(t,e,r){if(!i()(t)||"object"!==a(t))return null;var n=null;return Object.keys(t).forEach(function(o){var i=t[o];l.includes(o)&&"function"==typeof i&&(n||(n={}),n[o]=function(t){return i(e,r,t),null})}),n}},59773:function(t,e){"use strict";var r=Symbol.for("react.element"),n=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),u=Symbol.for("react.provider"),c=Symbol.for("react.context"),s=Symbol.for("react.server_context"),l=Symbol.for("react.forward_ref"),f=Symbol.for("react.suspense"),p=Symbol.for("react.suspense_list"),h=Symbol.for("react.memo"),d=Symbol.for("react.lazy");Symbol.for("react.offscreen"),Symbol.for("react.module.reference"),e.isFragment=function(t){return function(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case r:switch(t=t.type){case o:case a:case i:case f:case p:return t;default:switch(t=t&&t.$$typeof){case s:case c:case l:case d:case h:case u:return t;default:return e}}case n:return e}}}(t)===o}},14326:function(t,e,r){"use strict";t.exports=r(59773)},55284:function(t,e,r){"use strict";r.d(e,{Z:function(){return i},x:function(){return a}});var n=r(89999),o=r(36967);function i(){var t,e,r=(0,o.Z)().unknown(void 0),a=r.domain,u=r.range,c=0,s=1,l=!1,f=0,p=0,h=.5;function d(){var r=a().length,n=s<c,o=n?s:c,i=n?c:s;t=(i-o)/Math.max(1,r-f+2*p),l&&(t=Math.floor(t)),o+=(i-o-t*(r-f))*h,e=t*(1-f),l&&(o=Math.round(o),e=Math.round(e));var d=(function(t,e,r){t=+t,e=+e,r=(o=arguments.length)<2?(e=t,t=0,1):o<3?1:+r;for(var n=-1,o=0|Math.max(0,Math.ceil((e-t)/r)),i=Array(o);++n<o;)i[n]=t+n*r;return i})(r).map(function(e){return o+t*e});return u(n?d.reverse():d)}return delete r.unknown,r.domain=function(t){return arguments.length?(a(t),d()):a()},r.range=function(t){return arguments.length?([c,s]=t,c=+c,s=+s,d()):[c,s]},r.rangeRound=function(t){return[c,s]=t,c=+c,s=+s,l=!0,d()},r.bandwidth=function(){return e},r.step=function(){return t},r.round=function(t){return arguments.length?(l=!!t,d()):l},r.padding=function(t){return arguments.length?(f=Math.min(1,p=+t),d()):f},r.paddingInner=function(t){return arguments.length?(f=Math.min(1,t),d()):f},r.paddingOuter=function(t){return arguments.length?(p=+t,d()):p},r.align=function(t){return arguments.length?(h=Math.max(0,Math.min(1,t)),d()):h},r.copy=function(){return i(a(),[c,s]).round(l).paddingInner(f).paddingOuter(p).align(h)},n.o.apply(d(),arguments)}function a(){return function t(e){var r=e.copy;return e.padding=e.paddingOuter,delete e.paddingInner,delete e.paddingOuter,e.copy=function(){return t(r())},e}(i.apply(null,arguments).paddingInner(1))}},89999:function(t,e,r){"use strict";function n(t,e){switch(arguments.length){case 0:break;case 1:this.range(t);break;default:this.range(e).domain(t)}return this}function o(t,e){switch(arguments.length){case 0:break;case 1:"function"==typeof t?this.interpolator(t):this.range(t);break;default:this.domain(t),"function"==typeof e?this.interpolator(e):this.range(e)}return this}r.d(e,{O:function(){return o},o:function(){return n}})},36967:function(t,e,r){"use strict";r.d(e,{Z:function(){return function t(){var e=new n,r=[],o=[],i=u;function c(t){let n=e.get(t);if(void 0===n){if(i!==u)return i;e.set(t,n=r.push(t)-1)}return o[n%o.length]}return c.domain=function(t){if(!arguments.length)return r.slice();for(let o of(r=[],e=new n,t))e.has(o)||e.set(o,r.push(o)-1);return c},c.range=function(t){return arguments.length?(o=Array.from(t),c):o.slice()},c.unknown=function(t){return arguments.length?(i=t,c):i},c.copy=function(){return t(r,o).unknown(i)},a.o.apply(c,arguments),c}},O:function(){return u}});class n extends Map{constructor(t,e=i){if(super(),Object.defineProperties(this,{_intern:{value:new Map},_key:{value:e}}),null!=t)for(let[e,r]of t)this.set(e,r)}get(t){return super.get(o(this,t))}has(t){return super.has(o(this,t))}set(t,e){return super.set(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):(t.set(n,r),r)}(this,t),e)}delete(t){return super.delete(function({_intern:t,_key:e},r){let n=e(r);return t.has(n)&&(r=t.get(n),t.delete(n)),r}(this,t))}}function o({_intern:t,_key:e},r){let n=e(r);return t.has(n)?t.get(n):r}function i(t){return null!==t&&"object"==typeof t?t.valueOf():t}var a=r(89999);let u=Symbol("implicit")},22516:function(t,e,r){"use strict";function n(t){return"object"==typeof t&&"length"in t?t:Array.from(t)}r.d(e,{Z:function(){return n}}),Array.prototype.slice},76115:function(t,e,r){"use strict";function n(t){return function(){return t}}r.d(e,{Z:function(){return n}})},67790:function(t,e,r){"use strict";r.d(e,{d:function(){return c}});let n=Math.PI,o=2*n,i=o-1e-6;function a(t){this._+=t[0];for(let e=1,r=t.length;e<r;++e)this._+=arguments[e]+t[e]}class u{constructor(t){this._x0=this._y0=this._x1=this._y1=null,this._="",this._append=null==t?a:function(t){let e=Math.floor(t);if(!(e>=0))throw Error(`invalid digits: ${t}`);if(e>15)return a;let r=10**e;return function(t){this._+=t[0];for(let e=1,n=t.length;e<n;++e)this._+=Math.round(arguments[e]*r)/r+t[e]}}(t)}moveTo(t,e){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}`}closePath(){null!==this._x1&&(this._x1=this._x0,this._y1=this._y0,this._append`Z`)}lineTo(t,e){this._append`L${this._x1=+t},${this._y1=+e}`}quadraticCurveTo(t,e,r,n){this._append`Q${+t},${+e},${this._x1=+r},${this._y1=+n}`}bezierCurveTo(t,e,r,n,o,i){this._append`C${+t},${+e},${+r},${+n},${this._x1=+o},${this._y1=+i}`}arcTo(t,e,r,o,i){if(t=+t,e=+e,r=+r,o=+o,(i=+i)<0)throw Error(`negative radius: ${i}`);let a=this._x1,u=this._y1,c=r-t,s=o-e,l=a-t,f=u-e,p=l*l+f*f;if(null===this._x1)this._append`M${this._x1=t},${this._y1=e}`;else if(p>1e-6){if(Math.abs(f*c-s*l)>1e-6&&i){let h=r-a,d=o-u,y=c*c+s*s,v=Math.sqrt(y),m=Math.sqrt(p),b=i*Math.tan((n-Math.acos((y+p-(h*h+d*d))/(2*v*m)))/2),g=b/m,x=b/v;Math.abs(g-1)>1e-6&&this._append`L${t+g*l},${e+g*f}`,this._append`A${i},${i},0,0,${+(f*h>l*d)},${this._x1=t+x*c},${this._y1=e+x*s}`}else this._append`L${this._x1=t},${this._y1=e}`}}arc(t,e,r,a,u,c){if(t=+t,e=+e,c=!!c,(r=+r)<0)throw Error(`negative radius: ${r}`);let s=r*Math.cos(a),l=r*Math.sin(a),f=t+s,p=e+l,h=1^c,d=c?a-u:u-a;null===this._x1?this._append`M${f},${p}`:(Math.abs(this._x1-f)>1e-6||Math.abs(this._y1-p)>1e-6)&&this._append`L${f},${p}`,r&&(d<0&&(d=d%o+o),d>i?this._append`A${r},${r},0,1,${h},${t-s},${e-l}A${r},${r},0,1,${h},${this._x1=f},${this._y1=p}`:d>1e-6&&this._append`A${r},${r},0,${+(d>=n)},${h},${this._x1=t+r*Math.cos(u)},${this._y1=e+r*Math.sin(u)}`)}rect(t,e,r,n){this._append`M${this._x0=this._x1=+t},${this._y0=this._y1=+e}h${r=+r}v${+n}h${-r}Z`}toString(){return this._}}function c(t){let e=3;return t.digits=function(r){if(!arguments.length)return e;if(null==r)e=null;else{let t=Math.floor(r);if(!(t>=0))throw RangeError(`invalid digits: ${r}`);e=t}return t},()=>new u(e)}u.prototype},67590:function(t,e,r){"use strict";function n(t,e){return(n=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t})(t,e)}r.d(e,{Z:function(){return p}});var o=r(2265);function i(t){return t&&t.stopPropagation&&t.stopPropagation(),t&&t.preventDefault&&t.preventDefault(),!1}function a(t){return null==t?[]:Array.isArray(t)?t.slice():[t]}function u(t){return null!==t&&1===t.length?t[0]:t.slice()}function c(t){Object.keys(t).forEach(e=>{"undefined"!=typeof document&&document.addEventListener(e,t[e],!1)})}function s(t,e){let r;return l(((r=t)<=e.min&&(r=e.min),r>=e.max&&(r=e.max),r),e)}function l(t,e){let r=(t-e.min)%e.step,n=t-r;return 2*Math.abs(r)>=e.step&&(n+=r>0?e.step:-e.step),parseFloat(n.toFixed(5))}let f=function(t){function e(e){var r;(r=t.call(this,e)||this).onKeyUp=()=>{r.onEnd()},r.onMouseUp=()=>{r.onEnd(r.getMouseEventMap())},r.onTouchEnd=t=>{t.preventDefault(),r.onEnd(r.getTouchEventMap())},r.onBlur=()=>{r.setState({index:-1},r.onEnd(r.getKeyDownEventMap()))},r.onMouseMove=t=>{r.setState({pending:!0});let e=r.getMousePosition(t),n=r.getDiffPosition(e[0]),o=r.getValueFromPosition(n);r.move(o)},r.onTouchMove=t=>{if(t.touches.length>1)return;r.setState({pending:!0});let e=r.getTouchPosition(t);if(void 0===r.isScrolling){let t=e[0]-r.startPosition[0],n=e[1]-r.startPosition[1];r.isScrolling=Math.abs(n)>Math.abs(t)}if(r.isScrolling)return void r.setState({index:-1});let n=r.getDiffPosition(e[0]),o=r.getValueFromPosition(n);r.move(o)},r.onKeyDown=t=>{if(!(t.ctrlKey||t.shiftKey||t.altKey||t.metaKey))switch(r.setState({pending:!0}),t.key){case"ArrowLeft":case"ArrowDown":case"Left":case"Down":t.preventDefault(),r.moveDownByStep();break;case"ArrowRight":case"ArrowUp":case"Right":case"Up":t.preventDefault(),r.moveUpByStep();break;case"Home":t.preventDefault(),r.move(r.props.min);break;case"End":t.preventDefault(),r.move(r.props.max);break;case"PageDown":t.preventDefault(),r.moveDownByStep(r.props.pageFn(r.props.step));break;case"PageUp":t.preventDefault(),r.moveUpByStep(r.props.pageFn(r.props.step))}},r.onSliderMouseDown=t=>{if(!r.props.disabled&&2!==t.button){if(r.setState({pending:!0}),!r.props.snapDragDisabled){let e=r.getMousePosition(t);r.forceValueFromPosition(e[0],t=>{r.start(t,e[0]),c(r.getMouseEventMap())})}i(t)}},r.onSliderClick=t=>{if(!r.props.disabled&&r.props.onSliderClick&&!r.hasMoved){let e=r.getMousePosition(t),n=s(r.calcValue(r.calcOffsetFromPosition(e[0])),r.props);r.props.onSliderClick(n)}},r.createOnKeyDown=t=>e=>{r.props.disabled||(r.start(t),c(r.getKeyDownEventMap()),i(e))},r.createOnMouseDown=t=>e=>{if(r.props.disabled||2===e.button)return;r.setState({pending:!0});let n=r.getMousePosition(e);r.start(t,n[0]),c(r.getMouseEventMap()),i(e)},r.createOnTouchStart=t=>e=>{if(r.props.disabled||e.touches.length>1)return;r.setState({pending:!0});let n=r.getTouchPosition(e);r.startPosition=n,r.isScrolling=void 0,r.start(t,n[0]),c(r.getTouchEventMap()),e.stopPropagation&&e.stopPropagation()},r.handleResize=()=>{let t=window.setTimeout(()=>{r.pendingResizeTimeouts.shift(),r.resize()},0);r.pendingResizeTimeouts.push(t)},r.renderThumb=(t,e)=>{let n=r.props.thumbClassName+" "+r.props.thumbClassName+"-"+e+" "+(r.state.index===e?r.props.thumbActiveClassName:""),o={ref:t=>{r["thumb"+e]=t},key:r.props.thumbClassName+"-"+e,className:n,style:t,onMouseDown:r.createOnMouseDown(e),onTouchStart:r.createOnTouchStart(e),onFocus:r.createOnKeyDown(e),tabIndex:0,role:"slider","aria-orientation":r.props.orientation,"aria-valuenow":r.state.value[e],"aria-valuemin":r.props.min,"aria-valuemax":r.props.max,"aria-label":Array.isArray(r.props.ariaLabel)?r.props.ariaLabel[e]:r.props.ariaLabel,"aria-labelledby":Array.isArray(r.props.ariaLabelledby)?r.props.ariaLabelledby[e]:r.props.ariaLabelledby,"aria-disabled":r.props.disabled},i={index:e,value:u(r.state.value),valueNow:r.state.value[e]};return r.props.ariaValuetext&&(o["aria-valuetext"]="string"==typeof r.props.ariaValuetext?r.props.ariaValuetext:r.props.ariaValuetext(i)),r.props.renderThumb(o,i)},r.renderTrack=(t,e,n)=>{let o={key:r.props.trackClassName+"-"+t,className:r.props.trackClassName+" "+r.props.trackClassName+"-"+t,style:r.buildTrackStyle(e,r.state.upperBound-n)},i={index:t,value:u(r.state.value)};return r.props.renderTrack(o,i)};let n=a(e.value);n.length||(n=a(e.defaultValue)),r.pendingResizeTimeouts=[];let l=[];for(let t=0;t<n.length;t+=1)n[t]=s(n[t],e),l.push(t);return r.resizeObserver=null,r.resizeElementRef=o.createRef(),r.state={index:-1,upperBound:0,sliderLength:0,value:n,zIndices:l},r}e.prototype=Object.create(t.prototype),e.prototype.constructor=e,n(e,t);var r=e.prototype;return r.componentDidMount=function(){"undefined"!=typeof window&&(this.resizeObserver=new ResizeObserver(this.handleResize),this.resizeObserver.observe(this.resizeElementRef.current),this.resize())},e.getDerivedStateFromProps=function(t,e){let r=a(t.value);return r.length?e.pending?null:{value:r.map(e=>s(e,t))}:null},r.componentDidUpdate=function(){0===this.state.upperBound&&this.resize()},r.componentWillUnmount=function(){this.clearPendingResizeTimeouts(),this.resizeObserver&&this.resizeObserver.disconnect()},r.onEnd=function(t){t&&function(t){Object.keys(t).forEach(e=>{"undefined"!=typeof document&&document.removeEventListener(e,t[e],!1)})}(t),this.hasMoved&&this.fireChangeEvent("onAfterChange"),this.setState({pending:!1}),this.hasMoved=!1},r.getValue=function(){return u(this.state.value)},r.getClosestIndex=function(t){let e=Number.MAX_VALUE,r=-1,{value:n}=this.state,o=n.length;for(let i=0;i<o;i+=1){let o=Math.abs(t-this.calcOffset(n[i]));o<e&&(e=o,r=i)}return r},r.getMousePosition=function(t){return[t["page"+this.axisKey()],t["page"+this.orthogonalAxisKey()]]},r.getTouchPosition=function(t){let e=t.touches[0];return[e["page"+this.axisKey()],e["page"+this.orthogonalAxisKey()]]},r.getKeyDownEventMap=function(){return{keydown:this.onKeyDown,keyup:this.onKeyUp,focusout:this.onBlur}},r.getMouseEventMap=function(){return{mousemove:this.onMouseMove,mouseup:this.onMouseUp}},r.getTouchEventMap=function(){return{touchmove:this.onTouchMove,touchend:this.onTouchEnd}},r.getValueFromPosition=function(t){let e=t/(this.state.sliderLength-this.state.thumbSize)*(this.props.max-this.props.min);return s(this.state.startValue+e,this.props)},r.getDiffPosition=function(t){let e=t-this.state.startPosition;return this.props.invert&&(e*=-1),e},r.resize=function(){let{slider:t,thumb0:e}=this;if(!t||!e)return;let r=this.sizeKey(),n=t.getBoundingClientRect(),o=t[r],i=n[this.posMaxKey()],a=n[this.posMinKey()],u=e.getBoundingClientRect()[r.replace("client","").toLowerCase()],c=o-u,s=Math.abs(i-a);this.state.upperBound===c&&this.state.sliderLength===s&&this.state.thumbSize===u||this.setState({upperBound:c,sliderLength:s,thumbSize:u})},r.calcOffset=function(t){let e=this.props.max-this.props.min;return 0===e?0:(t-this.props.min)/e*this.state.upperBound},r.calcValue=function(t){return t/this.state.upperBound*(this.props.max-this.props.min)+this.props.min},r.calcOffsetFromPosition=function(t){let{slider:e}=this,r=e.getBoundingClientRect(),n=r[this.posMaxKey()],o=r[this.posMinKey()],i=t-(window["page"+this.axisKey()+"Offset"]+(this.props.invert?n:o));return this.props.invert&&(i=this.state.sliderLength-i),i-=this.state.thumbSize/2},r.forceValueFromPosition=function(t,e){let r=this.calcOffsetFromPosition(t),n=this.getClosestIndex(r),o=s(this.calcValue(r),this.props),i=this.state.value.slice();i[n]=o;for(let t=0;t<i.length-1;t+=1)if(i[t+1]-i[t]<this.props.minDistance)return;this.fireChangeEvent("onBeforeChange"),this.hasMoved=!0,this.setState({value:i},()=>{e(n),this.fireChangeEvent("onChange")})},r.clearPendingResizeTimeouts=function(){do clearTimeout(this.pendingResizeTimeouts.shift());while(this.pendingResizeTimeouts.length)},r.start=function(t,e){let r=this["thumb"+t];r&&r.focus();let{zIndices:n}=this.state;n.splice(n.indexOf(t),1),n.push(t),this.setState(r=>({startValue:r.value[t],startPosition:void 0!==e?e:r.startPosition,index:t,zIndices:n}))},r.moveUpByStep=function(t){void 0===t&&(t=this.props.step);let e=this.state.value[this.state.index],r=s(this.props.invert&&"horizontal"===this.props.orientation?e-t:e+t,this.props);this.move(Math.min(r,this.props.max))},r.moveDownByStep=function(t){void 0===t&&(t=this.props.step);let e=this.state.value[this.state.index],r=s(this.props.invert&&"horizontal"===this.props.orientation?e+t:e-t,this.props);this.move(Math.max(r,this.props.min))},r.move=function(t){let e=this.state.value.slice(),{index:r}=this.state,{length:n}=e,o=e[r];if(t===o)return;this.hasMoved||this.fireChangeEvent("onBeforeChange"),this.hasMoved=!0;let{pearling:i,max:a,min:u,minDistance:c}=this.props;if(!i){if(r>0){let n=e[r-1];t<n+c&&(t=n+c)}if(r<n-1){let n=e[r+1];t>n-c&&(t=n-c)}}e[r]=t,i&&n>1&&(t>o?(this.pushSucceeding(e,c,r),function(t,e,r,n){for(let o=0;o<t;o+=1){let i=n-o*r;e[t-1-o]>i&&(e[t-1-o]=i)}}(n,e,c,a)):t<o&&(this.pushPreceding(e,c,r),function(t,e,r,n){for(let o=0;o<t;o+=1){let t=n+o*r;e[o]<t&&(e[o]=t)}}(n,e,c,u))),this.setState({value:e},this.fireChangeEvent.bind(this,"onChange"))},r.pushSucceeding=function(t,e,r){let n,o;for(o=t[n=r]+e;null!==t[n+1]&&o>t[n+1];n+=1,o=t[n]+e)t[n+1]=l(o,this.props)},r.pushPreceding=function(t,e,r){for(let n=r,o=t[n]-e;null!==t[n-1]&&o<t[n-1];n-=1,o=t[n]-e)t[n-1]=l(o,this.props)},r.axisKey=function(){return"vertical"===this.props.orientation?"Y":"X"},r.orthogonalAxisKey=function(){return"vertical"===this.props.orientation?"X":"Y"},r.posMinKey=function(){return"vertical"===this.props.orientation?this.props.invert?"bottom":"top":this.props.invert?"right":"left"},r.posMaxKey=function(){return"vertical"===this.props.orientation?this.props.invert?"top":"bottom":this.props.invert?"left":"right"},r.sizeKey=function(){return"vertical"===this.props.orientation?"clientHeight":"clientWidth"},r.fireChangeEvent=function(t){this.props[t]&&this.props[t](u(this.state.value),this.state.index)},r.buildThumbStyle=function(t,e){let r={position:"absolute",touchAction:"none",willChange:this.state.index>=0?this.posMinKey():void 0,zIndex:this.state.zIndices.indexOf(e)+1};return r[this.posMinKey()]=t+"px",r},r.buildTrackStyle=function(t,e){let r={position:"absolute",willChange:this.state.index>=0?this.posMinKey()+","+this.posMaxKey():void 0};return r[this.posMinKey()]=t,r[this.posMaxKey()]=e,r},r.buildMarkStyle=function(t){var e;return(e={position:"absolute"})[this.posMinKey()]=t,e},r.renderThumbs=function(t){let{length:e}=t,r=[];for(let n=0;n<e;n+=1)r[n]=this.buildThumbStyle(t[n],n);let n=[];for(let t=0;t<e;t+=1)n[t]=this.renderThumb(r[t],t);return n},r.renderTracks=function(t){let e=[],r=t.length-1;e.push(this.renderTrack(0,0,t[0]));for(let n=0;n<r;n+=1)e.push(this.renderTrack(n+1,t[n],t[n+1]));return e.push(this.renderTrack(r+1,t[r],this.state.upperBound)),e},r.renderMarks=function(){let{marks:t}=this.props,e=this.props.max-this.props.min+1;return"boolean"==typeof t?t=Array.from({length:e}).map((t,e)=>e):"number"==typeof t&&(t=Array.from({length:e}).map((t,e)=>e).filter(e=>e%t==0)),t.map(parseFloat).sort((t,e)=>t-e).map(t=>{let e=this.calcOffset(t),r={key:t,className:this.props.markClassName,style:this.buildMarkStyle(e)};return this.props.renderMark(r)})},r.render=function(){let t=[],{value:e}=this.state,r=e.length;for(let n=0;n<r;n+=1)t[n]=this.calcOffset(e[n],n);let n=this.props.withTracks?this.renderTracks(t):null,i=this.renderThumbs(t),a=this.props.marks?this.renderMarks():null;return o.createElement("div",{ref:t=>{this.slider=t,this.resizeElementRef.current=t},style:{position:"relative"},className:this.props.className+(this.props.disabled?" disabled":""),onMouseDown:this.onSliderMouseDown,onClick:this.onSliderClick},n,i,a)},e}(o.Component);f.displayName="ReactSlider",f.defaultProps={min:0,max:100,step:1,pageFn:t=>10*t,minDistance:0,defaultValue:0,orientation:"horizontal",className:"slider",thumbClassName:"thumb",thumbActiveClassName:"active",trackClassName:"track",markClassName:"mark",withTracks:!0,pearling:!1,disabled:!1,snapDragDisabled:!1,invert:!1,marks:[],renderThumb:t=>o.createElement("div",t),renderTrack:t=>o.createElement("div",t),renderMark:t=>o.createElement("span",t)};var p=f},69398:function(t,e,r){"use strict";function n(t,e){if(!t)throw Error("Invariant failed")}r.d(e,{Z:function(){return n}})}}]);