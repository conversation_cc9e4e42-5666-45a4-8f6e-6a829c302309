import { postCheckoutVerifyStripePayment } from "@/core/infrastructures/stripe/api";
import { PostCheckoutVerifyProperty } from "@/core/infrastructures/stripe/dto";
import { useMutation } from "@tanstack/react-query";

export default function useCheckoutVerifyBooking() {
  const mutation = useMutation({
    mutationFn: async (data: PostCheckoutVerifyProperty) =>
      await postCheckoutVerifyStripePayment(data),
  });
  return mutation;
}
