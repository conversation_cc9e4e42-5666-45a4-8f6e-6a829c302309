(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6559],{89337:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("BadgeCheck",[["path",{d:"M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z",key:"3c2336"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},40875:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},10407:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},91723:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},81197:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("Coffee",[["path",{d:"M10 2v2",key:"7u0qdc"}],["path",{d:"M14 2v2",key:"6buw04"}],["path",{d:"M16 8a1 1 0 0 1 1 1v8a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4V9a1 1 0 0 1 1-1h14a4 4 0 1 1 0 8h-1",key:"pwadti"}],["path",{d:"M6 2v2",key:"colzsn"}]])},49232:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("Coins",[["circle",{cx:"8",cy:"8",r:"6",key:"3yglwk"}],["path",{d:"M18.09 10.37A6 6 0 1 1 10.34 18",key:"t5s6rm"}],["path",{d:"M7 6h1v4",key:"1obek4"}],["path",{d:"m16.71 13.88.7.71-2.82 2.82",key:"1rbuyh"}]])},95252:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},69658:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("File",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}]])},61928:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("HeartHandshake",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}],["path",{d:"M12 5 9.04 7.96a2.17 2.17 0 0 0 0 3.08c.82.82 2.13.85 3 .07l2.07-1.9a2.82 2.82 0 0 1 3.79 0l2.96 2.66",key:"4oyue0"}],["path",{d:"m18 15-2-2",key:"60u0ii"}],["path",{d:"m15 18-2-2",key:"6p76be"}]])},88997:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},18133:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("Hotel",[["path",{d:"M10 22v-6.57",key:"1wmca3"}],["path",{d:"M12 11h.01",key:"z322tv"}],["path",{d:"M12 7h.01",key:"1ivr5q"}],["path",{d:"M14 15.43V22",key:"1q2vjd"}],["path",{d:"M15 16a5 5 0 0 0-6 0",key:"o9wqvi"}],["path",{d:"M16 11h.01",key:"xkw8gn"}],["path",{d:"M16 7h.01",key:"1kdx03"}],["path",{d:"M8 11h.01",key:"1dfujw"}],["path",{d:"M8 7h.01",key:"1vti4s"}],["rect",{x:"4",y:"2",width:"16",height:"20",rx:"2",key:"1uxh74"}]])},14938:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},75745:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("LandPlot",[["path",{d:"m12 8 6-3-6-3v10",key:"mvpnpy"}],["path",{d:"m8 11.99-5.5 3.14a1 1 0 0 0 0 1.74l8.5 4.86a2 2 0 0 0 2 0l8.5-4.86a1 1 0 0 0 0-1.74L16 12",key:"ek95tt"}],["path",{d:"m6.49 12.85 11.02 6.3",key:"1kt42w"}],["path",{d:"M17.51 12.85 6.5 19.15",key:"v55bdg"}]])},57716:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("Laptop",[["path",{d:"M20 16V7a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v9m16 0H4m16 0 1.28 2.55a1 1 0 0 1-.9 1.45H3.62a1 1 0 0 1-.9-1.45L4 16",key:"tarvll"}]])},58293:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},82718:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("MessageCircle",[["path",{d:"M7.9 20A9 9 0 1 0 4 16.1L2 22Z",key:"vv11sd"}]])},67410:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("Presentation",[["path",{d:"M2 3h20",key:"91anmk"}],["path",{d:"M21 3v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V3",key:"2k9sn8"}],["path",{d:"m7 21 5-5 5 5",key:"bip4we"}]])},56096:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("School",[["path",{d:"M14 22v-4a2 2 0 1 0-4 0v4",key:"hhkicm"}],["path",{d:"m18 10 4 2v8a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2v-8l4-2",key:"1vwozw"}],["path",{d:"M18 5v17",key:"1sw6gf"}],["path",{d:"m4 6 8-4 8 4",key:"1q0ilc"}],["path",{d:"M6 5v17",key:"1xfsm0"}],["circle",{cx:"12",cy:"9",r:"2",key:"1092wv"}]])},73247:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},16275:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},72227:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("TreePalm",[["path",{d:"M13 8c0-2.76-2.46-5-5.5-5S2 5.24 2 8h2l1-1 1 1h4",key:"foxbe7"}],["path",{d:"M13 7.14A5.82 5.82 0 0 1 16.5 6c3.04 0 5.5 2.24 5.5 5h-3l-1-1-1 1h-3",key:"18arnh"}],["path",{d:"M5.89 9.71c-2.15 2.15-2.3 5.47-.35 7.43l4.24-4.25.7-.7.71-.71 2.12-2.12c-1.95-1.96-5.27-1.8-7.42.35",key:"ywahnh"}],["path",{d:"M11 15.5c.5 2.5-.17 4.5-1 6.5h4c2-5.5-.5-12-1-14",key:"ft0feo"}]])},40340:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("Truck",[["path",{d:"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2",key:"wrbu53"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["path",{d:"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14",key:"lysw3i"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}]])},53795:function(e,t,n){"use strict";var r=n(33910),o=n(99138),i=n(14814);r.default,o.default,t.os=i.default},50628:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(52680),o=n(2265),i=n(12579),l=n(99448),a=n(92417),u=o&&o.__esModule?o:{default:o};let c=o.forwardRef(function(e,t){let{locale:n,localePrefix:o,...c}=e,s=i.default(),f=n||s,d=l.getLocalePrefix(f,o);return u.default.createElement(a.default,r.extends({ref:t,locale:f,localePrefixMode:o.mode,prefix:d},c))});c.displayName="ClientLink",t.default=c},99138:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(52680),o=n(2265),i=n(12579),l=n(99415),a=n(42571),u=n(50628),c=n(92219),s=n(6188),f=n(26900),d=o&&o.__esModule?o:{default:o};t.default=function(e){let t=l.receiveRoutingConfig(e),n=l.receiveLocaleCookie(e.localeCookie);function p(){let e=i.default();if(!t.locales.includes(e))throw Error(void 0);return e}let m=o.forwardRef(function(e,o){let{href:i,locale:l,...c}=e,s=p(),f=l||s;return d.default.createElement(u.default,r.extends({ref:o,href:a.compileLocalizedPathname({locale:f,pathname:i,params:"object"==typeof i?i.params:void 0,pathnames:t.pathnames}),locale:l,localeCookie:n,localePrefix:t.localePrefix},c))});function h(e){let{href:n,locale:r}=e;return a.compileLocalizedPathname({...a.normalizeNameOrNameWithParams(n),locale:r,pathnames:t.pathnames})}return m.displayName="Link",{Link:m,redirect:function(e){let n=h({href:e,locale:p()});for(var r=arguments.length,o=Array(r>1?r-1:0),i=1;i<r;i++)o[i-1]=arguments[i];return c.clientRedirect({pathname:n,localePrefix:t.localePrefix},...o)},permanentRedirect:function(e){let n=h({href:e,locale:p()});for(var r=arguments.length,o=Array(r>1?r-1:0),i=1;i<r;i++)o[i-1]=arguments[i];return c.clientPermanentRedirect({pathname:n,localePrefix:t.localePrefix},...o)},usePathname:function(){let e=s.default(t.localePrefix),n=p();return o.useMemo(()=>e?a.getRoute(n,e,t.pathnames):e,[n,e])},useRouter:function(){let e=f.default(t.localePrefix,n),r=p();return o.useMemo(()=>({...e,push(t){for(var n,o=arguments.length,i=Array(o>1?o-1:0),l=1;l<o;l++)i[l-1]=arguments[l];let a=h({href:t,locale:(null===(n=i[0])||void 0===n?void 0:n.locale)||r});return e.push(a,...i)},replace(t){for(var n,o=arguments.length,i=Array(o>1?o-1:0),l=1;l<o;l++)i[l-1]=arguments[l];let a=h({href:t,locale:(null===(n=i[0])||void 0===n?void 0:n.locale)||r});return e.replace(a,...i)},prefetch(t){for(var n,o=arguments.length,i=Array(o>1?o-1:0),l=1;l<o;l++)i[l-1]=arguments[l];let a=h({href:t,locale:(null===(n=i[0])||void 0===n?void 0:n.locale)||r});return e.prefetch(a,...i)}}),[e,r])},getPathname:h}}},14814:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(99376),o=n(2265),i=n(12579),l=n(44986),a=n(56146),u=n(42571),c=n(6188);t.default=function(e){function t(){return i.default()}let{Link:n,config:s,getPathname:f,...d}=l.default(t,e);return{...d,Link:n,usePathname:function(){let e=c.default(s.localePrefix),n=t();return o.useMemo(()=>e&&s.pathnames?u.getRoute(n,e,s.pathnames):e,[n,e])},useRouter:function(){let e=r.useRouter(),n=t(),i=r.usePathname();return o.useMemo(()=>{function t(e){return function(t,r){let{locale:o,...l}=r||{},u=[f({href:t,locale:o||n,domain:window.location.host})];Object.keys(l).length>0&&u.push(l),e(...u),a.default(s.localeCookie,i,n,o)}}return{...e,push:t(e.push),replace:t(e.replace),prefetch:t(e.prefetch)}},[n,i,e])},getPathname:f}}},33910:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(52680),o=n(2265),i=n(99415),l=n(50628),a=n(92219),u=n(6188),c=n(26900),s=o&&o.__esModule?o:{default:o};t.default=function(e){let t=i.receiveLocalePrefixConfig(null==e?void 0:e.localePrefix),n=i.receiveLocaleCookie(null==e?void 0:e.localeCookie),f=o.forwardRef(function(e,o){return s.default.createElement(l.default,r.extends({ref:o,localeCookie:n,localePrefix:t},e))});return f.displayName="Link",{Link:f,redirect:function(e){for(var n=arguments.length,r=Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return a.clientRedirect({pathname:e,localePrefix:t},...r)},permanentRedirect:function(e){for(var n=arguments.length,r=Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return a.clientPermanentRedirect({pathname:e,localePrefix:t},...r)},usePathname:function(){return u.default(t)},useRouter:function(){return c.default(t,n)}}}},92219:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(12579),o=n(90050);function i(e){return function(t){let n;try{n=r.default()}catch(e){throw e}for(var o=arguments.length,i=Array(o>1?o-1:0),l=1;l<o;l++)i[l-1]=arguments[l];return e({...t,locale:n},...i)}}let l=i(o.baseRedirect),a=i(o.basePermanentRedirect);t.clientPermanentRedirect=a,t.clientRedirect=l},6188:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(99376),o=n(2265),i=n(12579),l=n(99448);t.default=function(e){let t=r.usePathname(),n=i.default();return o.useMemo(()=>{if(!t)return t;let r=l.getLocalePrefix(n,e);return l.hasPathnamePrefixed(r,t)?l.unprefixPathname(t,r):t},[n,e,t])}},26900:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(99376),o=n(2265),i=n(12579),l=n(99448),a=n(56146),u=n(42571);t.default=function(e,t){let n=r.useRouter(),c=i.default(),s=r.usePathname();return o.useMemo(()=>{function r(n){return function(r,o){let{locale:i,...f}=o||{};a.default(t,s,c,i);let d=[function(t,n){let r=window.location.pathname,o=u.getBasePath(s);o&&(r=r.replace(o,""));let i=n||c,a=l.getLocalePrefix(i,e);return l.localizeHref(t,i,c,r,a)}(r,i)];return Object.keys(f).length>0&&d.push(f),n(...d)}}return{...n,push:r(n.push),replace:r(n.replace),prefetch:r(n.prefetch)}},[c,t,e,s,n])}},23740:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(52680),o=n(27648),i=n(99376),l=n(2265),a=n(12579),u=n(56146);function c(e){return e&&e.__esModule?e:{default:e}}var s=c(o),f=c(l),d=l.forwardRef(function(e,t){let{defaultLocale:n,href:o,locale:c,localeCookie:d,onClick:p,prefetch:m,unprefixed:h,...v}=e,y=a.default(),g=c!==y,b=c||y,w=function(){let[e,t]=l.useState();return l.useEffect(()=>{t(window.location.host)},[]),e}(),x=w&&h&&(h.domains[w]===b||!Object.keys(h.domains).includes(w)&&y===n&&!c)?h.pathname:o,k=i.usePathname();return g&&(m=!1),f.default.createElement(s.default,r.extends({ref:t,href:x,hrefLang:g?c:void 0,onClick:function(e){u.default(d,k,y,c),p&&p(e)},prefetch:m},v))});t.default=d},92417:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(52680),o=n(99376),i=n(2265),l=n(12579),a=n(99448),u=n(23740),c=i&&i.__esModule?i:{default:i};let s=i.forwardRef(function(e,t){let{href:n,locale:s,localeCookie:f,localePrefixMode:d,prefix:p,...m}=e,h=o.usePathname(),v=l.default(),y=s!==v,[g,b]=i.useState(()=>a.isLocalizableHref(n)&&("never"!==d||y)?a.prefixHref(n,p):n);return i.useEffect(()=>{h&&b(a.localizeHref(n,s,v,h,p))},[v,n,s,h,p]),c.default.createElement(u.default,r.extends({ref:t,href:g,locale:s,localeCookie:f},m))});s.displayName="ClientLink",t.default=s},44986:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(52680),o=n(99376),i=n(2265),l=n(99415),a=n(99448),u=n(23740),c=n(42571),s=i&&i.__esModule?i:{default:i};t.default=function(e,t){let n=l.receiveRoutingConfig(t||{}),f=n.pathnames,d="as-needed"===n.localePrefix.mode&&n.domains||void 0,p=i.forwardRef(function(t,o){let l,c,{href:p,locale:h,...v}=t;"object"==typeof p?(l=p.pathname,c=p.params):l=p;let y=a.isLocalizableHref(p),g=e(),b=g instanceof Promise?i.use(g):g,w=y?m({locale:h||b,href:null==f?l:{pathname:l,params:c}},null!=h||d||void 0):l;return s.default.createElement(u.default,r.extends({ref:o,defaultLocale:n.defaultLocale,href:"object"==typeof p?{...p,pathname:w}:w,locale:h,localeCookie:n.localeCookie,unprefixed:d&&y?{domains:n.domains.reduce((e,t)=>(e[t.domain]=t.defaultLocale,e),{}),pathname:m({locale:b,href:null==f?l:{pathname:l,params:c}},!1)}:void 0},v))});function m(e,t){let r;let{href:o,locale:i}=e;return null==f?"object"==typeof o?(r=o.pathname,o.query&&(r+=c.serializeSearchParams(o.query))):r=o:r=c.compileLocalizedPathname({locale:i,...c.normalizeNameOrNameWithParams(o),pathnames:n.pathnames}),c.applyPathnamePrefix(r,i,n,e.domain,t)}function h(e){return function(t){for(var n=arguments.length,r=Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return e(m(t,t.domain?void 0:d),...r)}}return{config:n,Link:p,redirect:h(o.redirect),permanentRedirect:h(o.permanentRedirect),getPathname:m}}},90050:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(99376),o=n(99448);function i(e){return function(t){let n=o.getLocalePrefix(t.locale,t.localePrefix),r="never"!==t.localePrefix.mode&&o.isLocalizableHref(t.pathname)?o.prefixPathname(n,t.pathname):t.pathname;for(var i=arguments.length,l=Array(i>1?i-1:0),a=1;a<i;a++)l[a-1]=arguments[a];return e(r,...l)}}let l=i(r.redirect),a=i(r.permanentRedirect);t.basePermanentRedirect=a,t.baseRedirect=l},56146:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(42571);t.default=function(e,t,n,o){if(!e||!(o!==n&&null!=o)||!t)return;let i=r.getBasePath(t),{name:l,...a}=e;a.path||(a.path=""!==i?i:"/");let u="".concat(l,"=").concat(o,";");for(let[e,t]of Object.entries(a))u+="".concat("maxAge"===e?"max-age":e),"boolean"!=typeof t&&(u+="="+t),u+=";";document.cookie=u}},42571:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var r=n(99448);function o(e){let t=new URLSearchParams;for(let[n,r]of Object.entries(e))Array.isArray(r)?r.forEach(e=>{t.append(n,String(e))}):t.set(n,String(r));return"?"+t.toString()}t.applyPathnamePrefix=function(e,t,n,o,i){let l;let{mode:a}=n.localePrefix;if(void 0!==i)l=i;else if(r.isLocalizableHref(e)){if("always"===a)l=!0;else if("as-needed"===a){let e=n.defaultLocale;if(n.domains){let t=n.domains.find(e=>e.domain===o);t&&(e=t.defaultLocale)}l=e!==t}}return l?r.prefixPathname(r.getLocalePrefix(t,n.localePrefix),e):e},t.compileLocalizedPathname=function(e){let{pathname:t,locale:n,params:i,pathnames:l,query:a}=e;function u(e){let t=l[e];return t||(t=e),t}function c(e){let t="string"==typeof e?e:e[n];return i&&Object.entries(i).forEach(e=>{let n,r,[o,i]=e;Array.isArray(i)?(n="(\\[)?\\[...".concat(o,"\\](\\])?"),r=i.map(e=>String(e)).join("/")):(n="\\[".concat(o,"\\]"),r=String(i)),t=t.replace(RegExp(n,"g"),r)}),t=t.replace(/\[\[\.\.\..+\]\]/g,""),t=r.normalizeTrailingSlash(t),a&&(t+=o(a)),t}if("string"==typeof t)return c(u(t));{let{pathname:e,...n}=t;return{...n,pathname:c(u(e))}}},t.getBasePath=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window.location.pathname;return"/"===e?t:t.replace(e,"")},t.getRoute=function(e,t,n){let o=r.getSortedPathnames(Object.keys(n)),i=decodeURI(t);for(let t of o){let o=n[t];if("string"==typeof o){if(r.matchesPathname(o,i))return t}else if(r.matchesPathname(o[e],i))return t}return t},t.normalizeNameOrNameWithParams=function(e){return"string"==typeof e?{pathname:e}:e},t.serializeSearchParams=o},99415:function(e,t){"use strict";function n(e){return!(null!=e&&!e)&&{name:"NEXT_LOCALE",maxAge:31536e3,sameSite:"lax",..."object"==typeof e&&e}}function r(e){return"object"==typeof e?e:{mode:e||"always"}}Object.defineProperty(t,"__esModule",{value:!0}),t.receiveLocaleCookie=n,t.receiveLocalePrefixConfig=r,t.receiveRoutingConfig=function(e){var t,o;return{...e,localePrefix:r(e.localePrefix),localeCookie:n(e.localeCookie),localeDetection:null===(t=e.localeDetection)||void 0===t||t,alternateLinks:null===(o=e.alternateLinks)||void 0===o||o}}},99448:function(e,t,n){"use strict";var r=n(25566);function o(e){return("object"==typeof e?null==e.host&&null==e.hostname:!/^[a-z]+:/i.test(e))&&!function(e){let t="object"==typeof e?e.pathname:e;return null!=t&&!t.startsWith("/")}(e)}function i(e,t){let n;return"string"==typeof e?n=l(t,e):(n={...e},e.pathname&&(n.pathname=l(t,e.pathname))),n}function l(e,t){let n=e;return/^\/(\?.*)?$/.test(t)&&(t=t.slice(1)),n+=t}function a(e,t){return t===e||t.startsWith("".concat(e,"/"))}function u(e){let t=function(){try{return"true"===r.env._next_intl_trailing_slash}catch(e){return!1}}();if("/"!==e){let n=e.endsWith("/");t&&!n?e+="/":!t&&n&&(e=e.slice(0,-1))}return e}function c(e){let t=e.replace(/\[\[(\.\.\.[^\]]+)\]\]/g,"?(.*)").replace(/\[(\.\.\.[^\]]+)\]/g,"(.+)").replace(/\[([^\]]+)\]/g,"([^/]+)");return new RegExp("^".concat(t,"$"))}function s(e){return e.includes("[[...")}function f(e){return e.includes("[...")}function d(e){return e.includes("[")}function p(e,t){let n=e.split("/"),r=t.split("/"),o=Math.max(n.length,r.length);for(let e=0;e<o;e++){let t=n[e],o=r[e];if(!t&&o)return -1;if(t&&!o)return 1;if(t||o){if(!d(t)&&d(o))return -1;if(d(t)&&!d(o))return 1;if(!f(t)&&f(o))return -1;if(f(t)&&!f(o))return 1;if(!s(t)&&s(o))return -1;if(s(t)&&!s(o))return 1}}return 0}Object.defineProperty(t,"__esModule",{value:!0}),t.getLocalePrefix=function(e,t){var n;return"never"!==t.mode&&(null===(n=t.prefixes)||void 0===n?void 0:n[e])||"/"+e},t.getSortedPathnames=function(e){return e.sort(p)},t.hasPathnamePrefixed=a,t.isLocalizableHref=o,t.localizeHref=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t,r=arguments.length>3?arguments[3]:void 0,l=arguments.length>4?arguments[4]:void 0;if(!o(e))return e;let u=a(l,r);return(t!==n||u)&&null!=l?i(e,l):e},t.matchesPathname=function(e,t){let n=u(e),r=u(t);return c(n).test(r)},t.normalizeTrailingSlash=u,t.prefixHref=i,t.prefixPathname=l,t.templateToRegex=c,t.unprefixPathname=function(e,t){return e.replace(new RegExp("^".concat(t)),"")||"/"}},75189:function(e,t,n){"use strict";let r,o,i;var l=Object.create,a=Object.defineProperty,u=Object.defineProperties,c=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyDescriptors,f=Object.getOwnPropertyNames,d=Object.getOwnPropertySymbols,p=Object.getPrototypeOf,m=Object.prototype.hasOwnProperty,h=Object.prototype.propertyIsEnumerable,v=(e,t,n)=>t in e?a(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,y=(e,t)=>{for(var n in t||(t={}))m.call(t,n)&&v(e,n,t[n]);if(d)for(var n of d(t))h.call(t,n)&&v(e,n,t[n]);return e},g=(e,t)=>u(e,s(t)),b=(e,t,n,r)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let o of f(t))m.call(e,o)||o===n||a(e,o,{get:()=>t[o],enumerable:!(r=c(t,o))||r.enumerable});return e},w={};((e,t)=>{for(var n in t)a(e,n,{get:t[n],enumerable:!0})})(w,{useRouter:()=>j}),e.exports=b(a({},"__esModule",{value:!0}),w);var x=n(99376),k=n(2265),P=(i=null!=(r=n(71318))?l(p(r)):{},b(!o&&r&&r.__esModule?i:a(i,"default",{value:r,enumerable:!0}),r)),j=a(()=>{let e=(0,x.useRouter)(),t=(0,x.usePathname)();(0,k.useEffect)(()=>{P.done()},[t]);let n=(0,k.useCallback)((n,r)=>{n!==t&&P.start(),e.replace(n,r)},[e,t]),r=(0,k.useCallback)((n,r)=>{n!==t&&P.start(),e.push(n,r)},[e,t]);return g(y({},e),{replace:n,push:r})},"name",{value:"useRouter",configurable:!0})},71318:function(e,t,n){var r,o;void 0!==(o="function"==typeof(r=function(){var e,t,n,r={};r.version="0.2.0";var o=r.settings={minimum:.08,easing:"ease",positionUsing:"",speed:200,trickle:!0,trickleRate:.02,trickleSpeed:800,showSpinner:!0,barSelector:'[role="bar"]',spinnerSelector:'[role="spinner"]',parent:"body",template:'<div class="bar" role="bar"><div class="peg"></div></div><div class="spinner" role="spinner"><div class="spinner-icon"></div></div>'};function i(e,t,n){return e<t?t:e>n?n:e}r.configure=function(e){var t,n;for(t in e)void 0!==(n=e[t])&&e.hasOwnProperty(t)&&(o[t]=n);return this},r.status=null,r.set=function(e){var t=r.isStarted();e=i(e,o.minimum,1),r.status=1===e?null:e;var n=r.render(!t),u=n.querySelector(o.barSelector),c=o.speed,s=o.easing;return n.offsetWidth,l(function(t){var i,l;""===o.positionUsing&&(o.positionUsing=r.getPositioningCSS()),a(u,(i=e,(l="translate3d"===o.positionUsing?{transform:"translate3d("+(-1+i)*100+"%,0,0)"}:"translate"===o.positionUsing?{transform:"translate("+(-1+i)*100+"%,0)"}:{"margin-left":(-1+i)*100+"%"}).transition="all "+c+"ms "+s,l)),1===e?(a(n,{transition:"none",opacity:1}),n.offsetWidth,setTimeout(function(){a(n,{transition:"all "+c+"ms linear",opacity:0}),setTimeout(function(){r.remove(),t()},c)},c)):setTimeout(t,c)}),this},r.isStarted=function(){return"number"==typeof r.status},r.start=function(){r.status||r.set(0);var e=function(){setTimeout(function(){r.status&&(r.trickle(),e())},o.trickleSpeed)};return o.trickle&&e(),this},r.done=function(e){return e||r.status?r.inc(.3+.5*Math.random()).set(1):this},r.inc=function(e){var t=r.status;return t?("number"!=typeof e&&(e=(1-t)*i(Math.random()*t,.1,.95)),t=i(t+e,0,.994),r.set(t)):r.start()},r.trickle=function(){return r.inc(Math.random()*o.trickleRate)},e=0,t=0,r.promise=function(n){return n&&"resolved"!==n.state()&&(0===t&&r.start(),e++,t++,n.always(function(){0==--t?(e=0,r.done()):r.set((e-t)/e)})),this},r.render=function(e){if(r.isRendered())return document.getElementById("nprogress");c(document.documentElement,"nprogress-busy");var t=document.createElement("div");t.id="nprogress",t.innerHTML=o.template;var n,i=t.querySelector(o.barSelector),l=e?"-100":(-1+(r.status||0))*100,u=document.querySelector(o.parent);return a(i,{transition:"all 0 linear",transform:"translate3d("+l+"%,0,0)"}),!o.showSpinner&&(n=t.querySelector(o.spinnerSelector))&&d(n),u!=document.body&&c(u,"nprogress-custom-parent"),u.appendChild(t),t},r.remove=function(){s(document.documentElement,"nprogress-busy"),s(document.querySelector(o.parent),"nprogress-custom-parent");var e=document.getElementById("nprogress");e&&d(e)},r.isRendered=function(){return!!document.getElementById("nprogress")},r.getPositioningCSS=function(){var e=document.body.style,t="WebkitTransform"in e?"Webkit":"MozTransform"in e?"Moz":"msTransform"in e?"ms":"OTransform"in e?"O":"";return t+"Perspective" in e?"translate3d":t+"Transform" in e?"translate":"margin"};var l=(n=[],function(e){n.push(e),1==n.length&&function e(){var t=n.shift();t&&t(e)}()}),a=function(){var e=["Webkit","O","Moz","ms"],t={};function n(n,r,o){var i;r=t[i=(i=r).replace(/^-ms-/,"ms-").replace(/-([\da-z])/gi,function(e,t){return t.toUpperCase()})]||(t[i]=function(t){var n=document.body.style;if(t in n)return t;for(var r,o=e.length,i=t.charAt(0).toUpperCase()+t.slice(1);o--;)if((r=e[o]+i)in n)return r;return t}(i)),n.style[r]=o}return function(e,t){var r,o,i=arguments;if(2==i.length)for(r in t)void 0!==(o=t[r])&&t.hasOwnProperty(r)&&n(e,r,o);else n(e,i[1],i[2])}}();function u(e,t){return("string"==typeof e?e:f(e)).indexOf(" "+t+" ")>=0}function c(e,t){var n=f(e),r=n+t;u(n,t)||(e.className=r.substring(1))}function s(e,t){var n,r=f(e);u(e,t)&&(n=r.replace(" "+t+" "," "),e.className=n.substring(1,n.length-1))}function f(e){return(" "+(e.className||"")+" ").replace(/\s+/gi," ")}function d(e){e&&e.parentNode&&e.parentNode.removeChild(e)}return r})?r.call(t,n,t,e):r)&&(e.exports=o)},73680:function(e,t,n){"use strict";n.d(t,{YI:function(){return A}});var r=n(57437);function o(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function i(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?o(Object(n),!0).forEach(function(t){var r,o;r=t,o=n[t],(r=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(r))in e?Object.defineProperty(e,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[r]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):o(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function l(e){return"span"===e._type&&"text"in e&&"string"==typeof e.text&&(typeof e.marks>"u"||Array.isArray(e.marks)&&e.marks.every(e=>"string"==typeof e))}function a(e){return"string"==typeof e._type&&"@"!==e._type[0]&&(!("markDefs"in e)||!e.markDefs||Array.isArray(e.markDefs)&&e.markDefs.every(e=>"string"==typeof e._key))&&"children"in e&&Array.isArray(e.children)&&e.children.every(e=>"object"==typeof e&&"_type"in e)}function u(e){return a(e)&&"listItem"in e&&"string"==typeof e.listItem&&(typeof e.level>"u"||"number"==typeof e.level)}function c(e){return"@list"===e._type}function s(e){return"@span"===e._type}function f(e){return"@text"===e._type}let d=["strong","em","code","underline","strike-through"];function p(e,t,n){if(!l(e)||!e.marks||!e.marks.length)return[];let r=e.marks.slice(),o={};return r.forEach(e=>{o[e]=1;for(let r=t+1;r<n.length;r++){let t=n[r];if(t&&l(t)&&Array.isArray(t.marks)&&-1!==t.marks.indexOf(e))o[e]++;else break}}),r.sort((e,t)=>(function(e,t,n){let r=e[t],o=e[n];if(r!==o)return o-r;let i=d.indexOf(t),l=d.indexOf(n);return i!==l?i-l:t.localeCompare(n)})(o,e,t))}function m(e,t,n){return{_type:"@list",_key:`${e._key||`${t}`}-parent`,mode:n,level:e.level||1,listItem:e.listItem,children:[e]}}function h(e,t){let n=t.level||1,r=t.listItem||"normal",o="string"==typeof t.listItem;if(c(e)&&(e.level||1)===n&&o&&(e.listItem||"normal")===r)return e;if(!("children"in e))return;let i=e.children[e.children.length-1];return i&&!l(i)?h(i,t):void 0}var v=n(2265);let y=["block","list","listItem","marks","types"],g=["listItem"],b=["_key"];function w(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function x(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?w(Object(n),!0).forEach(function(t){var r,o;r=t,o=n[t],(r=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(r))in e?Object.defineProperty(e,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[r]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):w(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function k(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n={};for(var r in e)if(({}).hasOwnProperty.call(e,r)){if(t.includes(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.includes(n)||({}).propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}let P={textDecoration:"underline"},j=(e,t)=>`[@portabletext/react] Unknown ${e}, specify a component for it in the \`components.${t}\` prop`,M=e=>j(`block type "${e}"`,"types"),O=e=>j(`mark type "${e}"`,"marks"),R=e=>j(`block style "${e}"`,"block"),_=e=>j(`list style "${e}"`,"list"),S=e=>j(`list item style "${e}"`,"listItem");function C(e){console.warn(e)}let N={display:"none"},E={types:{},block:{normal:({children:e})=>(0,r.jsx)("p",{children:e}),blockquote:({children:e})=>(0,r.jsx)("blockquote",{children:e}),h1:({children:e})=>(0,r.jsx)("h1",{children:e}),h2:({children:e})=>(0,r.jsx)("h2",{children:e}),h3:({children:e})=>(0,r.jsx)("h3",{children:e}),h4:({children:e})=>(0,r.jsx)("h4",{children:e}),h5:({children:e})=>(0,r.jsx)("h5",{children:e}),h6:({children:e})=>(0,r.jsx)("h6",{children:e})},marks:{em:({children:e})=>(0,r.jsx)("em",{children:e}),strong:({children:e})=>(0,r.jsx)("strong",{children:e}),code:({children:e})=>(0,r.jsx)("code",{children:e}),underline:({children:e})=>(0,r.jsx)("span",{style:P,children:e}),"strike-through":({children:e})=>(0,r.jsx)("del",{children:e}),link:({children:e,value:t})=>(0,r.jsx)("a",{href:t?.href,children:e})},list:{number:({children:e})=>(0,r.jsx)("ol",{children:e}),bullet:({children:e})=>(0,r.jsx)("ul",{children:e})},listItem:({children:e})=>(0,r.jsx)("li",{children:e}),hardBreak:()=>(0,r.jsx)("br",{}),unknownType:({value:e,isInline:t})=>{let n=M(e._type);return t?(0,r.jsx)("span",{style:N,children:n}):(0,r.jsx)("div",{style:N,children:n})},unknownMark:({markType:e,children:t})=>(0,r.jsx)("span",{className:`unknown__pt__mark__${e}`,children:t}),unknownList:({children:e})=>(0,r.jsx)("ul",{children:e}),unknownListItem:({children:e})=>(0,r.jsx)("li",{children:e}),unknownBlockStyle:({children:e})=>(0,r.jsx)("p",{children:e})};function T(e,t,n){let r=t[n],o=e[n];return"function"==typeof r||r&&"function"==typeof o?r:r?x(x({},o),r):o}function A({value:e,components:t,listNestingMode:n,onMissingComponent:o=C}){let l=o||D,a=function(e,t){let n;let r=[];for(let l=0;l<e.length;l++){let a=e[l];if(a){var o;if(!u(a)){r.push(a),n=void 0;continue}if(!n){n=m(a,l,t),r.push(n);continue}if(o=n,(a.level||1)===o.level&&a.listItem===o.listItem){n.children.push(a);continue}if((a.level||1)>n.level){let e=m(a,l,t);if("html"===t){let t=n.children[n.children.length-1],r=i(i({},t),{},{children:[...t.children,e]});n.children[n.children.length-1]=r}else n.children.push(e);n=e;continue}if((a.level||1)<n.level){let e=r[r.length-1],o=e&&h(e,a);if(o){(n=o).children.push(a);continue}n=m(a,l,t),r.push(n);continue}if(a.listItem!==n.listItem){let e=r[r.length-1],o=e&&h(e,{level:a.level||1});if(o&&o.listItem===a.listItem){(n=o).children.push(a);continue}n=m(a,l,t),r.push(n);continue}console.warn("Unknown state encountered for block",a),r.push(a)}}return r}(Array.isArray(e)?e:[e],n||"html"),c=(0,v.useMemo)(()=>t?function(e,t){let{block:n,list:r,listItem:o,marks:i,types:l}=t,a=k(t,y);return x(x({},e),{},{block:T(e,t,"block"),list:T(e,t,"list"),listItem:T(e,t,"listItem"),marks:T(e,t,"marks"),types:T(e,t,"types")},a)}(E,t):E,[t]),s=(0,v.useMemo)(()=>I(c,l),[c,l]),f=a.map((e,t)=>s({node:e,index:t,isInline:!1,renderNode:s}));return(0,r.jsx)(r.Fragment,{children:f})}let I=(e,t)=>function n(o){let{node:i,index:l,isInline:d}=o,p=i._key||`node-${l}`;return c(i)?function(o,i,l){let a=o.children.map((e,t)=>n({node:e._key?e:x(x({},e),{},{_key:`li-${i}-${t}`}),index:t,isInline:!1,renderNode:n})),u=e.list,c=("function"==typeof u?u:u[o.listItem])||e.unknownList;if(c===e.unknownList){let e=o.listItem||"bullet";t(_(e),{nodeType:"listStyle",type:e})}return(0,r.jsx)(c,{value:o,index:i,isInline:!1,renderNode:n,children:a},l)}(i,l,p):u(i)?function(o,i,l){let a=L({node:o,index:i,isInline:!1,renderNode:n}),u=e.listItem,c=("function"==typeof u?u:u[o.listItem])||e.unknownListItem;if(c===e.unknownListItem){let e=o.listItem||"bullet";t(S(e),{type:e,nodeType:"listItemStyle"})}let s=a.children;if(o.style&&"normal"!==o.style){let{listItem:e}=o;s=n({node:k(o,g),index:i,isInline:!1,renderNode:n})}return(0,r.jsx)(c,{value:o,index:i,isInline:!1,renderNode:n,children:s},l)}(i,l,p):s(i)?function(o,i,l){let{markDef:a,markType:u,markKey:c}=o,d=e.marks[u]||e.unknownMark,p=o.children.map((e,t)=>n({node:e,index:t,isInline:!0,renderNode:n}));return d===e.unknownMark&&t(O(u),{nodeType:"mark",type:u}),(0,r.jsx)(d,{text:function e(t){let n="";return t.children.forEach(t=>{f(t)?n+=t.text:s(t)&&(n+=e(t))}),n}(o),value:a,markType:u,markKey:c,renderNode:n,children:p},l)}(i,0,p):i._type in e.types?function(t,o,i,l){let a=e.types[t._type];return a?(0,r.jsx)(a,x({},{value:t,isInline:l,index:o,renderNode:n}),i):null}(i,l,p,d):a(i)?function(o,i,l,a){let u=L({node:o,index:i,isInline:a,renderNode:n}),{_key:c}=u,s=k(u,b),f=s.node.style||"normal",d=("function"==typeof e.block?e.block:e.block[f])||e.unknownBlockStyle;return d===e.unknownBlockStyle&&t(R(f),{nodeType:"blockStyle",type:f}),(0,r.jsx)(d,x(x({},s),{},{value:s.node,renderNode:n}),l)}(i,l,p,d):f(i)?function(t,n){if(t.text===`
`){let t=e.hardBreak;return t?(0,r.jsx)(t,{},n):`
`}return t.text}(i,p):function(o,i,l,a){t(M(o._type),{nodeType:"block",type:o._type});let u=e.unknownType;return(0,r.jsx)(u,x({},{value:o,isInline:a,index:i,renderNode:n}),l)}(i,l,p,d)};function L(e){let{node:t,index:n,isInline:r,renderNode:o}=e,i=(function(e){var t;let{children:n,markDefs:r=[]}=e;if(!n||!n.length)return[];let o=n.map(p),i={_type:"@span",children:[],markType:"<unknown>"},a=[i];for(let e=0;e<n.length;e++){let i=n[e];if(!i)continue;let u=o[e]||[],c=1;if(a.length>1)for(;c<a.length;c++){let e=(null==(t=a[c])?void 0:t.markKey)||"",n=u.indexOf(e);if(-1===n)break;u.splice(n,1)}let s=(a=a.slice(0,c))[a.length-1];if(s){for(let e of u){let t=r.find(t=>t._key===e),n=t?t._type:e,o={_type:"@span",_key:i._key,children:[],markDef:t,markType:n,markKey:e};s.children.push(o),a.push(o),s=o}if(l(i)){let e=i.text.split(`
`);for(let t=e.length;t-- >1;)e.splice(t,0,`
`);s.children=s.children.concat(e.map(e=>({_type:"@text",text:e})))}else s.children=s.children.concat(i)}}return i.children})(t).map((e,t)=>o({node:e,isInline:!0,index:t,renderNode:o}));return{_key:t._key||`block-${n}`,children:i,index:n,isInline:r,node:t}}function D(){}},62484:function(e,t,n){"use strict";function r(e,[t,n]){return Math.min(n,Math.max(t,e))}n.d(t,{u:function(){return r}})},6741:function(e,t,n){"use strict";function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}n.d(t,{M:function(){return r}})},10156:function(e,t,n){"use strict";n.d(t,{VY:function(){return el},h4:function(){return eo},ck:function(){return er},fC:function(){return en},xz:function(){return ei}});var r=n(2265),o=n(57437),i=n(71605),l=n(98575),a=n(6741),u=n(80886),c=n(82912),s=n(61188),f=e=>{var t,n;let o,i;let{present:a,children:u}=e,c=function(e){var t,n;let[o,i]=r.useState(),l=r.useRef({}),a=r.useRef(e),u=r.useRef("none"),[c,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=d(l.current);u.current="mounted"===c?e:"none"},[c]),(0,s.b)(()=>{let t=l.current,n=a.current;if(n!==e){let r=u.current,o=d(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),a.current=e}},[e,f]),(0,s.b)(()=>{if(o){var e;let t;let n=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=d(l.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!a.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(u.current=d(l.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:r.useCallback(e=>{e&&(l.current=getComputedStyle(e)),i(e)},[])}}(a),f="function"==typeof u?u({present:c.isPresent}):r.Children.only(u),p=(0,l.e)(c.ref,(o=null===(t=Object.getOwnPropertyDescriptor(f.props,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in o&&o.isReactWarning?f.ref:(o=null===(n=Object.getOwnPropertyDescriptor(f,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in o&&o.isReactWarning?f.props.ref:f.props.ref||f.ref);return"function"==typeof u||c.isPresent?r.cloneElement(f,{ref:p}):null};function d(e){return(null==e?void 0:e.animationName)||"none"}f.displayName="Presence";var p=n(99255),m="Collapsible",[h,v]=function(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return i.scopeName=e,[function(t,i){let l=r.createContext(i),a=n.length;n=[...n,i];let u=t=>{let{scope:n,children:i,...u}=t,c=n?.[e]?.[a]||l,s=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(c.Provider,{value:s,children:i})};return u.displayName=t+"Provider",[u,function(n,o){let u=o?.[e]?.[a]||l,c=r.useContext(u);if(c)return c;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(i,...t)]}(m),[y,g]=h(m),b=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,open:i,defaultOpen:l,disabled:a,onOpenChange:s,...f}=e,[d=!1,m]=(0,u.T)({prop:i,defaultProp:l,onChange:s});return(0,o.jsx)(y,{scope:n,disabled:a,contentId:(0,p.M)(),open:d,onOpenToggle:r.useCallback(()=>m(e=>!e),[m]),children:(0,o.jsx)(c.WV.div,{"data-state":M(d),"data-disabled":a?"":void 0,...f,ref:t})})});b.displayName=m;var w="CollapsibleTrigger",x=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,...r}=e,i=g(w,n);return(0,o.jsx)(c.WV.button,{type:"button","aria-controls":i.contentId,"aria-expanded":i.open||!1,"data-state":M(i.open),"data-disabled":i.disabled?"":void 0,disabled:i.disabled,...r,ref:t,onClick:(0,a.M)(e.onClick,i.onOpenToggle)})});x.displayName=w;var k="CollapsibleContent",P=r.forwardRef((e,t)=>{let{forceMount:n,...r}=e,i=g(k,e.__scopeCollapsible);return(0,o.jsx)(f,{present:n||i.open,children:e=>{let{present:n}=e;return(0,o.jsx)(j,{...r,ref:t,present:n})}})});P.displayName=k;var j=r.forwardRef((e,t)=>{let{__scopeCollapsible:n,present:i,children:a,...u}=e,f=g(k,n),[d,p]=r.useState(i),m=r.useRef(null),h=(0,l.e)(t,m),v=r.useRef(0),y=v.current,b=r.useRef(0),w=b.current,x=f.open||d,P=r.useRef(x),j=r.useRef();return r.useEffect(()=>{let e=requestAnimationFrame(()=>P.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,s.b)(()=>{let e=m.current;if(e){j.current=j.current||{transitionDuration:e.style.transitionDuration,animationName:e.style.animationName},e.style.transitionDuration="0s",e.style.animationName="none";let t=e.getBoundingClientRect();v.current=t.height,b.current=t.width,P.current||(e.style.transitionDuration=j.current.transitionDuration,e.style.animationName=j.current.animationName),p(i)}},[f.open,i]),(0,o.jsx)(c.WV.div,{"data-state":M(f.open),"data-disabled":f.disabled?"":void 0,id:f.contentId,hidden:!x,...u,ref:h,style:{"--radix-collapsible-content-height":y?"".concat(y,"px"):void 0,"--radix-collapsible-content-width":w?"".concat(w,"px"):void 0,...e.style},children:x&&a})});function M(e){return e?"open":"closed"}var O=n(29114),R="Accordion",_=["Home","End","ArrowDown","ArrowUp","ArrowLeft","ArrowRight"],[S,C,N]=(0,i.B)(R),[E,T]=function(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return i.scopeName=e,[function(t,i){let l=r.createContext(i),a=n.length;n=[...n,i];let u=t=>{let{scope:n,children:i,...u}=t,c=n?.[e]?.[a]||l,s=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(c.Provider,{value:s,children:i})};return u.displayName=t+"Provider",[u,function(n,o){let u=o?.[e]?.[a]||l,c=r.useContext(u);if(c)return c;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(i,...t)]}(R,[N,v]),A=v(),I=r.forwardRef((e,t)=>{let{type:n,...r}=e;return(0,o.jsx)(S.Provider,{scope:e.__scopeAccordion,children:"multiple"===n?(0,o.jsx)(Z,{...r,ref:t}):(0,o.jsx)(z,{...r,ref:t})})});I.displayName=R;var[L,D]=E(R),[W,U]=E(R,{collapsible:!1}),z=r.forwardRef((e,t)=>{let{value:n,defaultValue:i,onValueChange:l=()=>{},collapsible:a=!1,...c}=e,[s,f]=(0,u.T)({prop:n,defaultProp:i,onChange:l});return(0,o.jsx)(L,{scope:e.__scopeAccordion,value:s?[s]:[],onItemOpen:f,onItemClose:r.useCallback(()=>a&&f(""),[a,f]),children:(0,o.jsx)(W,{scope:e.__scopeAccordion,collapsible:a,children:(0,o.jsx)(F,{...c,ref:t})})})}),Z=r.forwardRef((e,t)=>{let{value:n,defaultValue:i,onValueChange:l=()=>{},...a}=e,[c=[],s]=(0,u.T)({prop:n,defaultProp:i,onChange:l}),f=r.useCallback(e=>s(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return[...t,e]}),[s]),d=r.useCallback(e=>s(function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];return t.filter(t=>t!==e)}),[s]);return(0,o.jsx)(L,{scope:e.__scopeAccordion,value:c,onItemOpen:f,onItemClose:d,children:(0,o.jsx)(W,{scope:e.__scopeAccordion,collapsible:!0,children:(0,o.jsx)(F,{...a,ref:t})})})}),[H,V]=E(R),F=r.forwardRef((e,t)=>{let{__scopeAccordion:n,disabled:i,dir:u,orientation:s="vertical",...f}=e,d=r.useRef(null),p=(0,l.e)(d,t),m=C(n),h="ltr"===(0,O.gm)(u),v=(0,a.M)(e.onKeyDown,e=>{var t;if(!_.includes(e.key))return;let n=e.target,r=m().filter(e=>{var t;return!(null===(t=e.ref.current)||void 0===t?void 0:t.disabled)}),o=r.findIndex(e=>e.ref.current===n),i=r.length;if(-1===o)return;e.preventDefault();let l=o,a=i-1,u=()=>{(l=o+1)>a&&(l=0)},c=()=>{(l=o-1)<0&&(l=a)};switch(e.key){case"Home":l=0;break;case"End":l=a;break;case"ArrowRight":"horizontal"===s&&(h?u():c());break;case"ArrowDown":"vertical"===s&&u();break;case"ArrowLeft":"horizontal"===s&&(h?c():u());break;case"ArrowUp":"vertical"===s&&c()}null===(t=r[l%i].ref.current)||void 0===t||t.focus()});return(0,o.jsx)(H,{scope:n,disabled:i,direction:u,orientation:s,children:(0,o.jsx)(S.Slot,{scope:n,children:(0,o.jsx)(c.WV.div,{...f,"data-orientation":s,ref:p,onKeyDown:i?void 0:v})})})}),q="AccordionItem",[$,B]=E(q),Y=r.forwardRef((e,t)=>{let{__scopeAccordion:n,value:r,...i}=e,l=V(q,n),a=D(q,n),u=A(n),c=(0,p.M)(),s=r&&a.value.includes(r)||!1,f=l.disabled||e.disabled;return(0,o.jsx)($,{scope:n,open:s,disabled:f,triggerId:c,children:(0,o.jsx)(b,{"data-orientation":l.orientation,"data-state":et(s),...u,...i,ref:t,disabled:f,open:s,onOpenChange:e=>{e?a.onItemOpen(r):a.onItemClose(r)}})})});Y.displayName=q;var K="AccordionHeader",X=r.forwardRef((e,t)=>{let{__scopeAccordion:n,...r}=e,i=V(R,n),l=B(K,n);return(0,o.jsx)(c.WV.h3,{"data-orientation":i.orientation,"data-state":et(l.open),"data-disabled":l.disabled?"":void 0,...r,ref:t})});X.displayName=K;var G="AccordionTrigger",J=r.forwardRef((e,t)=>{let{__scopeAccordion:n,...r}=e,i=V(R,n),l=B(G,n),a=U(G,n),u=A(n);return(0,o.jsx)(S.ItemSlot,{scope:n,children:(0,o.jsx)(x,{"aria-disabled":l.open&&!a.collapsible||void 0,"data-orientation":i.orientation,id:l.triggerId,...u,...r,ref:t})})});J.displayName=G;var Q="AccordionContent",ee=r.forwardRef((e,t)=>{let{__scopeAccordion:n,...r}=e,i=V(R,n),l=B(Q,n),a=A(n);return(0,o.jsx)(P,{role:"region","aria-labelledby":l.triggerId,"data-orientation":i.orientation,...a,...r,ref:t,style:{"--radix-accordion-content-height":"var(--radix-collapsible-content-height)","--radix-accordion-content-width":"var(--radix-collapsible-content-width)",...e.style}})});function et(e){return e?"open":"closed"}ee.displayName=Q;var en=I,er=Y,eo=X,ei=J,el=ee},71605:function(e,t,n){"use strict";n.d(t,{B:function(){return f}});var r=n(2265),o=n(73966),i=n(98575),l=n(57437),a=r.forwardRef((e,t)=>{let{children:n,...o}=e,i=r.Children.toArray(n),a=i.find(s);if(a){let e=a.props.children,n=i.map(t=>t!==a?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,l.jsx)(u,{...o,ref:t,children:r.isValidElement(e)?r.cloneElement(e,void 0,n):null})}return(0,l.jsx)(u,{...o,ref:t,children:n})});a.displayName="Slot";var u=r.forwardRef((e,t)=>{let{children:n,...o}=e;if(r.isValidElement(n)){let e,l;let a=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref;return r.cloneElement(n,{...function(e,t){let n={...t};for(let r in t){let o=e[r],i=t[r];/^on[A-Z]/.test(r)?o&&i?n[r]=(...e)=>{i(...e),o(...e)}:o&&(n[r]=o):"style"===r?n[r]={...o,...i}:"className"===r&&(n[r]=[o,i].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props),ref:t?(0,i.F)(t,a):a})}return r.Children.count(n)>1?r.Children.only(null):null});u.displayName="SlotClone";var c=({children:e})=>(0,l.jsx)(l.Fragment,{children:e});function s(e){return r.isValidElement(e)&&e.type===c}function f(e){let t=e+"CollectionProvider",[n,u]=(0,o.b)(t),[c,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),f=e=>{let{scope:t,children:n}=e,o=r.useRef(null),i=r.useRef(new Map).current;return(0,l.jsx)(c,{scope:t,itemMap:i,collectionRef:o,children:n})};f.displayName=t;let d=e+"CollectionSlot",p=r.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=s(d,n),u=(0,i.e)(t,o.collectionRef);return(0,l.jsx)(a,{ref:u,children:r})});p.displayName=d;let m=e+"CollectionItemSlot",h="data-radix-collection-item",v=r.forwardRef((e,t)=>{let{scope:n,children:o,...u}=e,c=r.useRef(null),f=(0,i.e)(t,c),d=s(m,n);return r.useEffect(()=>(d.itemMap.set(c,{ref:c,...u}),()=>void d.itemMap.delete(c))),(0,l.jsx)(a,{[h]:"",ref:f,children:o})});return v.displayName=m,[{Provider:f,Slot:p,ItemSlot:v},function(t){let n=s(e+"CollectionConsumer",t);return r.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(h,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},u]}},29114:function(e,t,n){"use strict";n.d(t,{gm:function(){return i}});var r=n(2265);n(57437);var o=r.createContext(void 0);function i(e){let t=r.useContext(o);return e||t||"ltr"}},71599:function(e,t,n){"use strict";n.d(t,{z:function(){return a}});var r=n(2265),o=n(54887),i=n(98575),l=n(61188),a=e=>{var t,n;let a,c;let{present:s,children:f}=e,d=function(e){var t,n;let[i,a]=r.useState(),c=r.useRef({}),s=r.useRef(e),f=r.useRef("none"),[d,p]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=u(c.current);f.current="mounted"===d?e:"none"},[d]),(0,l.b)(()=>{let t=c.current,n=s.current;if(n!==e){let r=f.current,o=u(t);e?p("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?p("UNMOUNT"):n&&r!==o?p("ANIMATION_OUT"):p("UNMOUNT"),s.current=e}},[e,p]),(0,l.b)(()=>{if(i){let e=e=>{let t=u(c.current).includes(e.animationName);e.target===i&&t&&o.flushSync(()=>p("ANIMATION_END"))},t=e=>{e.target===i&&(f.current=u(c.current))};return i.addEventListener("animationstart",t),i.addEventListener("animationcancel",e),i.addEventListener("animationend",e),()=>{i.removeEventListener("animationstart",t),i.removeEventListener("animationcancel",e),i.removeEventListener("animationend",e)}}p("ANIMATION_END")},[i,p]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{e&&(c.current=getComputedStyle(e)),a(e)},[])}}(s),p="function"==typeof f?f({present:d.isPresent}):r.Children.only(f),m=(0,i.e)(d.ref,(a=null===(t=Object.getOwnPropertyDescriptor(p.props,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in a&&a.isReactWarning?p.ref:(a=null===(n=Object.getOwnPropertyDescriptor(p,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in a&&a.isReactWarning?p.props.ref:p.props.ref||p.ref);return"function"==typeof f||d.isPresent?r.cloneElement(p,{ref:m}):null};function u(e){return(null==e?void 0:e.animationName)||"none"}a.displayName="Presence"},1353:function(e,t,n){"use strict";n.d(t,{Pc:function(){return x},ck:function(){return N},fC:function(){return C}});var r=n(2265),o=n(6741),i=n(71605),l=n(98575),a=n(73966),u=n(99255),c=n(82912),s=n(26606),f=n(80886),d=n(29114),p=n(57437),m="rovingFocusGroup.onEntryFocus",h={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[y,g,b]=(0,i.B)(v),[w,x]=(0,a.b)(v,[b]),[k,P]=w(v),j=r.forwardRef((e,t)=>(0,p.jsx)(y.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(y.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(M,{...e,ref:t})})}));j.displayName=v;var M=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:i,loop:a=!1,dir:u,currentTabStopId:v,defaultCurrentTabStopId:y,onCurrentTabStopIdChange:b,onEntryFocus:w,preventScrollOnEntryFocus:x=!1,...P}=e,j=r.useRef(null),M=(0,l.e)(t,j),O=(0,d.gm)(u),[R=null,_]=(0,f.T)({prop:v,defaultProp:y,onChange:b}),[C,N]=r.useState(!1),E=(0,s.W)(w),T=g(n),A=r.useRef(!1),[I,L]=r.useState(0);return r.useEffect(()=>{let e=j.current;if(e)return e.addEventListener(m,E),()=>e.removeEventListener(m,E)},[E]),(0,p.jsx)(k,{scope:n,orientation:i,dir:O,loop:a,currentTabStopId:R,onItemFocus:r.useCallback(e=>_(e),[_]),onItemShiftTab:r.useCallback(()=>N(!0),[]),onFocusableItemAdd:r.useCallback(()=>L(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>L(e=>e-1),[]),children:(0,p.jsx)(c.WV.div,{tabIndex:C||0===I?-1:0,"data-orientation":i,...P,ref:M,style:{outline:"none",...e.style},onMouseDown:(0,o.M)(e.onMouseDown,()=>{A.current=!0}),onFocus:(0,o.M)(e.onFocus,e=>{let t=!A.current;if(e.target===e.currentTarget&&t&&!C){let t=new CustomEvent(m,h);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=T().filter(e=>e.focusable);S([e.find(e=>e.active),e.find(e=>e.id===R),...e].filter(Boolean).map(e=>e.ref.current),x)}}A.current=!1}),onBlur:(0,o.M)(e.onBlur,()=>N(!1))})})}),O="RovingFocusGroupItem",R=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:i=!0,active:l=!1,tabStopId:a,...s}=e,f=(0,u.M)(),d=a||f,m=P(O,n),h=m.currentTabStopId===d,v=g(n),{onFocusableItemAdd:b,onFocusableItemRemove:w}=m;return r.useEffect(()=>{if(i)return b(),()=>w()},[i,b,w]),(0,p.jsx)(y.ItemSlot,{scope:n,id:d,focusable:i,active:l,children:(0,p.jsx)(c.WV.span,{tabIndex:h?0:-1,"data-orientation":m.orientation,...s,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{i?m.onItemFocus(d):e.preventDefault()}),onFocus:(0,o.M)(e.onFocus,()=>m.onItemFocus(d)),onKeyDown:(0,o.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){m.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return _[o]}(e,m.orientation,m.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let o=v().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)o.reverse();else if("prev"===t||"next"===t){var n,r;"prev"===t&&o.reverse();let i=o.indexOf(e.currentTarget);o=m.loop?(n=o,r=i+1,n.map((e,t)=>n[(r+t)%n.length])):o.slice(i+1)}setTimeout(()=>S(o))}})})})});R.displayName=O;var _={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function S(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var C=j,N=R},18756:function(e,t,n){"use strict";n.d(t,{Ns:function(){return K},fC:function(){return B},gb:function(){return P},q4:function(){return A},l_:function(){return Y}});var r=n(2265),o=n(82912),i=n(98575),l=n(61188),a=e=>{var t,n;let o,a;let{present:c,children:s}=e,f=function(e){var t,n;let[o,i]=r.useState(),a=r.useRef({}),c=r.useRef(e),s=r.useRef("none"),[f,d]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=u(a.current);s.current="mounted"===f?e:"none"},[f]),(0,l.b)(()=>{let t=a.current,n=c.current;if(n!==e){let r=s.current,o=u(t);e?d("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?d("UNMOUNT"):n&&r!==o?d("ANIMATION_OUT"):d("UNMOUNT"),c.current=e}},[e,d]),(0,l.b)(()=>{if(o){var e;let t;let n=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,r=e=>{let r=u(a.current).includes(e.animationName);if(e.target===o&&r&&(d("ANIMATION_END"),!c.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(s.current=u(a.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}d("ANIMATION_END")},[o,d]),{isPresent:["mounted","unmountSuspended"].includes(f),ref:r.useCallback(e=>{e&&(a.current=getComputedStyle(e)),i(e)},[])}}(c),d="function"==typeof s?s({present:f.isPresent}):r.Children.only(s),p=(0,i.e)(f.ref,(o=null===(t=Object.getOwnPropertyDescriptor(d.props,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in o&&o.isReactWarning?d.ref:(o=null===(n=Object.getOwnPropertyDescriptor(d,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in o&&o.isReactWarning?d.props.ref:d.props.ref||d.ref);return"function"==typeof s||f.isPresent?r.cloneElement(d,{ref:p}):null};function u(e){return(null==e?void 0:e.animationName)||"none"}a.displayName="Presence";var c=n(57437),s=n(26606),f=n(29114),d=n(62484),p=n(6741),m="ScrollArea",[h,v]=function(e,t=[]){let n=[],o=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return o.scopeName=e,[function(t,o){let i=r.createContext(o),l=n.length;n=[...n,o];let a=t=>{let{scope:n,children:o,...a}=t,u=n?.[e]?.[l]||i,s=r.useMemo(()=>a,Object.values(a));return(0,c.jsx)(u.Provider,{value:s,children:o})};return a.displayName=t+"Provider",[a,function(n,a){let u=a?.[e]?.[l]||i,c=r.useContext(u);if(c)return c;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(o,...t)]}(m),[y,g]=h(m),b=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,type:l="hover",dir:a,scrollHideDelay:u=600,...s}=e,[d,p]=r.useState(null),[m,h]=r.useState(null),[v,g]=r.useState(null),[b,w]=r.useState(null),[x,k]=r.useState(null),[P,j]=r.useState(0),[M,O]=r.useState(0),[R,_]=r.useState(!1),[S,C]=r.useState(!1),N=(0,i.e)(t,e=>p(e)),E=(0,f.gm)(a);return(0,c.jsx)(y,{scope:n,type:l,dir:E,scrollHideDelay:u,scrollArea:d,viewport:m,onViewportChange:h,content:v,onContentChange:g,scrollbarX:b,onScrollbarXChange:w,scrollbarXEnabled:R,onScrollbarXEnabledChange:_,scrollbarY:x,onScrollbarYChange:k,scrollbarYEnabled:S,onScrollbarYEnabledChange:C,onCornerWidthChange:j,onCornerHeightChange:O,children:(0,c.jsx)(o.WV.div,{dir:E,...s,ref:N,style:{position:"relative","--radix-scroll-area-corner-width":P+"px","--radix-scroll-area-corner-height":M+"px",...e.style}})})});b.displayName=m;var w="ScrollAreaViewport",x=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,children:l,asChild:a,nonce:u,...s}=e,f=g(w,n),d=r.useRef(null),p=(0,i.e)(t,d,f.onViewportChange);return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("style",{dangerouslySetInnerHTML:{__html:"\n[data-radix-scroll-area-viewport] {\n  scrollbar-width: none;\n  -ms-overflow-style: none;\n  -webkit-overflow-scrolling: touch;\n}\n[data-radix-scroll-area-viewport]::-webkit-scrollbar {\n  display: none;\n}\n:where([data-radix-scroll-area-viewport]) {\n  display: flex;\n  flex-direction: column;\n  align-items: stretch;\n}\n:where([data-radix-scroll-area-content]) {\n  flex-grow: 1;\n}\n"},nonce:u}),(0,c.jsx)(o.WV.div,{"data-radix-scroll-area-viewport":"",...s,asChild:a,ref:p,style:{overflowX:f.scrollbarXEnabled?"scroll":"hidden",overflowY:f.scrollbarYEnabled?"scroll":"hidden",...e.style},children:function(e,t){let{asChild:n,children:o}=e;if(!n)return"function"==typeof t?t(o):t;let i=r.Children.only(o);return r.cloneElement(i,{children:"function"==typeof t?t(i.props.children):t})}({asChild:a,children:l},e=>(0,c.jsx)("div",{"data-radix-scroll-area-content":"",ref:f.onContentChange,style:{minWidth:f.scrollbarXEnabled?"fit-content":void 0},children:e}))})]})});x.displayName=w;var k="ScrollAreaScrollbar",P=r.forwardRef((e,t)=>{let{forceMount:n,...o}=e,i=g(k,e.__scopeScrollArea),{onScrollbarXEnabledChange:l,onScrollbarYEnabledChange:a}=i,u="horizontal"===e.orientation;return r.useEffect(()=>(u?l(!0):a(!0),()=>{u?l(!1):a(!1)}),[u,l,a]),"hover"===i.type?(0,c.jsx)(j,{...o,ref:t,forceMount:n}):"scroll"===i.type?(0,c.jsx)(M,{...o,ref:t,forceMount:n}):"auto"===i.type?(0,c.jsx)(O,{...o,ref:t,forceMount:n}):"always"===i.type?(0,c.jsx)(R,{...o,ref:t}):null});P.displayName=k;var j=r.forwardRef((e,t)=>{let{forceMount:n,...o}=e,i=g(k,e.__scopeScrollArea),[l,u]=r.useState(!1);return r.useEffect(()=>{let e=i.scrollArea,t=0;if(e){let n=()=>{window.clearTimeout(t),u(!0)},r=()=>{t=window.setTimeout(()=>u(!1),i.scrollHideDelay)};return e.addEventListener("pointerenter",n),e.addEventListener("pointerleave",r),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",n),e.removeEventListener("pointerleave",r)}}},[i.scrollArea,i.scrollHideDelay]),(0,c.jsx)(a,{present:n||l,children:(0,c.jsx)(O,{"data-state":l?"visible":"hidden",...o,ref:t})})}),M=r.forwardRef((e,t)=>{var n,o;let{forceMount:i,...l}=e,u=g(k,e.__scopeScrollArea),s="horizontal"===e.orientation,f=q(()=>m("SCROLL_END"),100),[d,m]=(n="hidden",o={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},r.useReducer((e,t)=>{let n=o[e][t];return null!=n?n:e},n));return r.useEffect(()=>{if("idle"===d){let e=window.setTimeout(()=>m("HIDE"),u.scrollHideDelay);return()=>window.clearTimeout(e)}},[d,u.scrollHideDelay,m]),r.useEffect(()=>{let e=u.viewport,t=s?"scrollLeft":"scrollTop";if(e){let n=e[t],r=()=>{let r=e[t];n!==r&&(m("SCROLL"),f()),n=r};return e.addEventListener("scroll",r),()=>e.removeEventListener("scroll",r)}},[u.viewport,s,m,f]),(0,c.jsx)(a,{present:i||"hidden"!==d,children:(0,c.jsx)(R,{"data-state":"hidden"===d?"hidden":"visible",...l,ref:t,onPointerEnter:(0,p.M)(e.onPointerEnter,()=>m("POINTER_ENTER")),onPointerLeave:(0,p.M)(e.onPointerLeave,()=>m("POINTER_LEAVE"))})})}),O=r.forwardRef((e,t)=>{let n=g(k,e.__scopeScrollArea),{forceMount:o,...i}=e,[l,u]=r.useState(!1),s="horizontal"===e.orientation,f=q(()=>{if(n.viewport){let e=n.viewport.offsetWidth<n.viewport.scrollWidth,t=n.viewport.offsetHeight<n.viewport.scrollHeight;u(s?e:t)}},10);return $(n.viewport,f),$(n.content,f),(0,c.jsx)(a,{present:o||l,children:(0,c.jsx)(R,{"data-state":l?"visible":"hidden",...i,ref:t})})}),R=r.forwardRef((e,t)=>{let{orientation:n="vertical",...o}=e,i=g(k,e.__scopeScrollArea),l=r.useRef(null),a=r.useRef(0),[u,s]=r.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),f=z(u.viewport,u.content),d={...o,sizes:u,onSizesChange:s,hasThumb:!!(f>0&&f<1),onThumbChange:e=>l.current=e,onThumbPointerUp:()=>a.current=0,onThumbPointerDown:e=>a.current=e};function p(e,t){return function(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"ltr",o=Z(n),i=t||o/2,l=n.scrollbar.paddingStart+i,a=n.scrollbar.size-n.scrollbar.paddingEnd-(o-i),u=n.content-n.viewport;return V([l,a],"ltr"===r?[0,u]:[-1*u,0])(e)}(e,a.current,u,t)}return"horizontal"===n?(0,c.jsx)(_,{...d,ref:t,onThumbPositionChange:()=>{if(i.viewport&&l.current){let e=H(i.viewport.scrollLeft,u,i.dir);l.current.style.transform="translate3d(".concat(e,"px, 0, 0)")}},onWheelScroll:e=>{i.viewport&&(i.viewport.scrollLeft=e)},onDragScroll:e=>{i.viewport&&(i.viewport.scrollLeft=p(e,i.dir))}}):"vertical"===n?(0,c.jsx)(S,{...d,ref:t,onThumbPositionChange:()=>{if(i.viewport&&l.current){let e=H(i.viewport.scrollTop,u);l.current.style.transform="translate3d(0, ".concat(e,"px, 0)")}},onWheelScroll:e=>{i.viewport&&(i.viewport.scrollTop=e)},onDragScroll:e=>{i.viewport&&(i.viewport.scrollTop=p(e))}}):null}),_=r.forwardRef((e,t)=>{let{sizes:n,onSizesChange:o,...l}=e,a=g(k,e.__scopeScrollArea),[u,s]=r.useState(),f=r.useRef(null),d=(0,i.e)(t,f,a.onScrollbarXChange);return r.useEffect(()=>{f.current&&s(getComputedStyle(f.current))},[f]),(0,c.jsx)(E,{"data-orientation":"horizontal",...l,ref:d,sizes:n,style:{bottom:0,left:"rtl"===a.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===a.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":Z(n)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,n)=>{if(a.viewport){let r=a.viewport.scrollLeft+t.deltaX;e.onWheelScroll(r),r>0&&r<n&&t.preventDefault()}},onResize:()=>{f.current&&a.viewport&&u&&o({content:a.viewport.scrollWidth,viewport:a.viewport.offsetWidth,scrollbar:{size:f.current.clientWidth,paddingStart:U(u.paddingLeft),paddingEnd:U(u.paddingRight)}})}})}),S=r.forwardRef((e,t)=>{let{sizes:n,onSizesChange:o,...l}=e,a=g(k,e.__scopeScrollArea),[u,s]=r.useState(),f=r.useRef(null),d=(0,i.e)(t,f,a.onScrollbarYChange);return r.useEffect(()=>{f.current&&s(getComputedStyle(f.current))},[f]),(0,c.jsx)(E,{"data-orientation":"vertical",...l,ref:d,sizes:n,style:{top:0,right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":Z(n)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,n)=>{if(a.viewport){let r=a.viewport.scrollTop+t.deltaY;e.onWheelScroll(r),r>0&&r<n&&t.preventDefault()}},onResize:()=>{f.current&&a.viewport&&u&&o({content:a.viewport.scrollHeight,viewport:a.viewport.offsetHeight,scrollbar:{size:f.current.clientHeight,paddingStart:U(u.paddingTop),paddingEnd:U(u.paddingBottom)}})}})}),[C,N]=h(k),E=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,sizes:l,hasThumb:a,onThumbChange:u,onThumbPointerUp:f,onThumbPointerDown:d,onThumbPositionChange:m,onDragScroll:h,onWheelScroll:v,onResize:y,...b}=e,w=g(k,n),[x,P]=r.useState(null),j=(0,i.e)(t,e=>P(e)),M=r.useRef(null),O=r.useRef(""),R=w.viewport,_=l.content-l.viewport,S=(0,s.W)(v),N=(0,s.W)(m),E=q(y,10);function T(e){M.current&&h({x:e.clientX-M.current.left,y:e.clientY-M.current.top})}return r.useEffect(()=>{let e=e=>{let t=e.target;(null==x?void 0:x.contains(t))&&S(e,_)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[R,x,_,S]),r.useEffect(N,[l,N]),$(x,E),$(w.content,E),(0,c.jsx)(C,{scope:n,scrollbar:x,hasThumb:a,onThumbChange:(0,s.W)(u),onThumbPointerUp:(0,s.W)(f),onThumbPositionChange:N,onThumbPointerDown:(0,s.W)(d),children:(0,c.jsx)(o.WV.div,{...b,ref:j,style:{position:"absolute",...b.style},onPointerDown:(0,p.M)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),M.current=x.getBoundingClientRect(),O.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",w.viewport&&(w.viewport.style.scrollBehavior="auto"),T(e))}),onPointerMove:(0,p.M)(e.onPointerMove,T),onPointerUp:(0,p.M)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=O.current,w.viewport&&(w.viewport.style.scrollBehavior=""),M.current=null})})})}),T="ScrollAreaThumb",A=r.forwardRef((e,t)=>{let{forceMount:n,...r}=e,o=N(T,e.__scopeScrollArea);return(0,c.jsx)(a,{present:n||o.hasThumb,children:(0,c.jsx)(I,{ref:t,...r})})}),I=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,style:l,...a}=e,u=g(T,n),s=N(T,n),{onThumbPositionChange:f}=s,d=(0,i.e)(t,e=>s.onThumbChange(e)),m=r.useRef(),h=q(()=>{m.current&&(m.current(),m.current=void 0)},100);return r.useEffect(()=>{let e=u.viewport;if(e){let t=()=>{if(h(),!m.current){let t=F(e,f);m.current=t,f()}};return f(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[u.viewport,h,f]),(0,c.jsx)(o.WV.div,{"data-state":s.hasThumb?"visible":"hidden",...a,ref:d,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...l},onPointerDownCapture:(0,p.M)(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),n=e.clientX-t.left,r=e.clientY-t.top;s.onThumbPointerDown({x:n,y:r})}),onPointerUp:(0,p.M)(e.onPointerUp,s.onThumbPointerUp)})});A.displayName=T;var L="ScrollAreaCorner",D=r.forwardRef((e,t)=>{let n=g(L,e.__scopeScrollArea),r=!!(n.scrollbarX&&n.scrollbarY);return"scroll"!==n.type&&r?(0,c.jsx)(W,{...e,ref:t}):null});D.displayName=L;var W=r.forwardRef((e,t)=>{let{__scopeScrollArea:n,...i}=e,l=g(L,n),[a,u]=r.useState(0),[s,f]=r.useState(0),d=!!(a&&s);return $(l.scrollbarX,()=>{var e;let t=(null===(e=l.scrollbarX)||void 0===e?void 0:e.offsetHeight)||0;l.onCornerHeightChange(t),f(t)}),$(l.scrollbarY,()=>{var e;let t=(null===(e=l.scrollbarY)||void 0===e?void 0:e.offsetWidth)||0;l.onCornerWidthChange(t),u(t)}),d?(0,c.jsx)(o.WV.div,{...i,ref:t,style:{width:a,height:s,position:"absolute",right:"ltr"===l.dir?0:void 0,left:"rtl"===l.dir?0:void 0,bottom:0,...e.style}}):null});function U(e){return e?parseInt(e,10):0}function z(e,t){let n=e/t;return isNaN(n)?0:n}function Z(e){let t=z(e.viewport,e.content),n=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-n)*t,18)}function H(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",r=Z(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,i=t.scrollbar.size-o,l=t.content-t.viewport,a=(0,d.u)(e,"ltr"===n?[0,l]:[-1*l,0]);return V([0,l],[0,i-r])(a)}function V(e,t){return n=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let r=(t[1]-t[0])/(e[1]-e[0]);return t[0]+r*(n-e[0])}}var F=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:()=>{},n={left:e.scrollLeft,top:e.scrollTop},r=0;return!function o(){let i={left:e.scrollLeft,top:e.scrollTop},l=n.left!==i.left,a=n.top!==i.top;(l||a)&&t(),n=i,r=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(r)};function q(e,t){let n=(0,s.W)(e),o=r.useRef(0);return r.useEffect(()=>()=>window.clearTimeout(o.current),[]),r.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(n,t)},[n,t])}function $(e,t){let n=(0,s.W)(t);(0,l.b)(()=>{let t=0;if(e){let r=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(n)});return r.observe(e),()=>{window.cancelAnimationFrame(t),r.unobserve(e)}}},[e,n])}var B=b,Y=x,K=D},20271:function(e,t,n){"use strict";n.d(t,{VY:function(){return N},aV:function(){return S},fC:function(){return _},xz:function(){return C}});var r=n(2265),o=n(6741),i=n(73966),l=n(1353),a=n(71599),u=n(82912),c=n(29114),s=n(80886),f=n(99255),d=n(57437),p="Tabs",[m,h]=(0,i.b)(p,[l.Pc]),v=(0,l.Pc)(),[y,g]=m(p),b=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,onValueChange:o,defaultValue:i,orientation:l="horizontal",dir:a,activationMode:p="automatic",...m}=e,h=(0,c.gm)(a),[v,g]=(0,s.T)({prop:r,onChange:o,defaultProp:i});return(0,d.jsx)(y,{scope:n,baseId:(0,f.M)(),value:v,onValueChange:g,orientation:l,dir:h,activationMode:p,children:(0,d.jsx)(u.WV.div,{dir:h,"data-orientation":l,...m,ref:t})})});b.displayName=p;var w="TabsList",x=r.forwardRef((e,t)=>{let{__scopeTabs:n,loop:r=!0,...o}=e,i=g(w,n),a=v(n);return(0,d.jsx)(l.fC,{asChild:!0,...a,orientation:i.orientation,dir:i.dir,loop:r,children:(0,d.jsx)(u.WV.div,{role:"tablist","aria-orientation":i.orientation,...o,ref:t})})});x.displayName=w;var k="TabsTrigger",P=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,disabled:i=!1,...a}=e,c=g(k,n),s=v(n),f=O(c.baseId,r),p=R(c.baseId,r),m=r===c.value;return(0,d.jsx)(l.ck,{asChild:!0,...s,focusable:!i,active:m,children:(0,d.jsx)(u.WV.button,{type:"button",role:"tab","aria-selected":m,"aria-controls":p,"data-state":m?"active":"inactive","data-disabled":i?"":void 0,disabled:i,id:f,...a,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{i||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(r)}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(r)}),onFocus:(0,o.M)(e.onFocus,()=>{let e="manual"!==c.activationMode;m||i||!e||c.onValueChange(r)})})})});P.displayName=k;var j="TabsContent",M=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:o,forceMount:i,children:l,...c}=e,s=g(j,n),f=O(s.baseId,o),p=R(s.baseId,o),m=o===s.value,h=r.useRef(m);return r.useEffect(()=>{let e=requestAnimationFrame(()=>h.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,d.jsx)(a.z,{present:i||m,children:n=>{let{present:r}=n;return(0,d.jsx)(u.WV.div,{"data-state":m?"active":"inactive","data-orientation":s.orientation,role:"tabpanel","aria-labelledby":f,hidden:!r,id:p,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:h.current?"0s":void 0},children:r&&l})}})});function O(e,t){return"".concat(e,"-trigger-").concat(t)}function R(e,t){return"".concat(e,"-content-").concat(t)}M.displayName=j;var _=b,S=x,C=P,N=M}}]);