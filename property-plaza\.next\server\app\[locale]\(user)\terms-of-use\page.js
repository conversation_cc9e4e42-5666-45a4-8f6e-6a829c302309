"use strict";(()=>{var e={};e.id=8285,e.ids=[8285],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},61282:e=>{e.exports=require("child_process")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},19801:e=>{e.exports=require("os")},55315:e=>{e.exports=require("path")},86624:e=>{e.exports=require("querystring")},76162:e=>{e.exports=require("stream")},82452:e=>{e.exports=require("tls")},74175:e=>{e.exports=require("tty")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},6005:e=>{e.exports=require("node:crypto")},35257:(e,t,r)=>{r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>c,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>p}),r(46035),r(52250),r(7505),r(84448),r(81729),r(90996);var a=r(30170),s=r(45002),o=r(83876),i=r.n(o),l=r(66299),n={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>l[e]);r.d(t,n);let p=["",{children:["[locale]",{children:["(user)",{children:["terms-of-use",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,46035)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user)\\terms-of-use\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,52250)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,7505)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user)\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,80603))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,84448)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,81729)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,90996,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user)\\terms-of-use\\page.tsx"],u="/[locale]/(user)/terms-of-use/page",c={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/[locale]/(user)/terms-of-use/page",pathname:"/[locale]/terms-of-use",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},46035:(e,t,r)=>{r.r(t),r.d(t,{default:()=>f,generateMetadata:()=>h});var a=r(72051),s=r(52845),o=r(69385),i=r(79438),l=r(695);function n(){let e=(0,o.Z)("seeker");return a.jsx(i.Z,{id:"faq",className:"mt-12",children:a.jsx(l.Z,{title:e("termsOfUse.title")})})}var p=r(83266),d=r(29507),u=r(38785),c=r(94975);function m({content:e}){return a.jsx(i.Z,{children:a.jsx("article",{className:"prose prose-big max-w-3xl text-seekers-text mb-4",children:a.jsx(c.YI,{value:e.body,components:{block:{h1:({children:e})=>a.jsx("h2",{className:"text-2xl font-semibold text-seekers-text mt-4",children:e}),h2:({children:e})=>a.jsx("h3",{className:"text-xl font-semibold mt-4",children:e}),h3:({children:e})=>a.jsx("h3",{className:"text-lg font-semibold mt-4",children:e}),h4:({children:e})=>a.jsx("h3",{className:"",children:e}),normal:({children:e})=>a.jsx("p",{className:" leading-relaxed mt-2",children:e})},list:{number:({children:e})=>a.jsx("ol",{className:"list-decimal list-inside mt-2",children:e}),bullet:({children:e})=>a.jsx("ul",{className:"list-disc pl-4 mt-2",children:e})}}})})})}var x=r(92898);async function h(){let e=process.env.USER_DOMAIN||"https://www.property-plaza.com/",t=await (0,p.Z)(),r=await (0,d.Z)("seeker");return{title:r("metadata.termsOfUse.title"),description:r("metadata.termsOfUse.description"),openGraph:{title:r("metadata.termsOfUse.title"),description:r("metadata.termsOfUse.description"),images:[{url:e+"og.png",width:1200,height:630,alt:"Property Plaza"}],type:"website",url:e+t+x.Fk},twitter:{card:"summary_large_image",title:r("metadata.termsOfUse.title"),description:r("metadata.termsOfUse.description"),images:[e+"og.jpg"]},alternates:{canonical:e+t+x.Fk,languages:{id:e+`id${x.Fk}`,en:e+`en${x.Fk}`,"x-default":e+x.Fk.replace("/","")}}}}async function f(){let e=(0,s.cookies)();e.get("NEXT_LOCALE")?.value,await (0,d.Z)("seeker");let t=await (0,u._b)("en");return(0,a.jsxs)(a.Fragment,{children:[a.jsx(n,{}),a.jsx(m,{content:t[0]})]})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[9379,5063,4916,9467,4859,5268,3327,8530,6136,7146,4975,6666,9965,595,2232],()=>r(35257));module.exports=a})();