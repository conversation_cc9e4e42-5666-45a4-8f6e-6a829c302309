"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/verify/page",{

/***/ "(app-pages-browser)/./components/ui/optimized-video.tsx":
/*!*******************************************!*\
  !*** ./components/ui/optimized-video.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OptimizedVideo; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction OptimizedVideo(param) {\n    let { src, poster, className = \"\", autoPlay = true, muted = true, loop = true, playsInline = true, controls = false, preload = \"metadata\", lazy = true, fallbackContent, showCustomControls = false } = param;\n    _s();\n    const [shouldLoad, setShouldLoad] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!lazy);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [canPlay, setCanPlay] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasError, setHasError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const videoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!lazy) return;\n        const observer = new IntersectionObserver((param)=>{\n            let [entry] = param;\n            if (entry.isIntersecting) {\n                setShouldLoad(true);\n                observer.disconnect();\n            }\n        }, {\n            threshold: 0.1,\n            rootMargin: \"50px\" // Start loading 50px before video comes into view\n        });\n        if (videoRef.current) {\n            observer.observe(videoRef.current);\n        }\n        return ()=>observer.disconnect();\n    }, [\n        lazy\n    ]);\n    const handleLoadStart = ()=>{\n        setIsLoading(true);\n        setHasError(false);\n    };\n    const handleCanPlay = ()=>{\n        setIsLoading(false);\n    };\n    const handleError = ()=>{\n        setIsLoading(false);\n        setHasError(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: videoRef,\n        className: \"relative \".concat(className),\n        children: [\n            isLoading && shouldLoad && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gray-100 flex items-center justify-center z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 border-2 border-seekers-primary border-t-transparent rounded-full animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                            lineNumber: 83,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: \"Loading video...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                lineNumber: 81,\n                columnNumber: 9\n            }, this),\n            hasError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gray-100 flex items-center justify-center z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-4xl\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                            lineNumber: 93,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: \"Video could not be loaded\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 13\n                        }, this),\n                        fallbackContent\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                    lineNumber: 92,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                lineNumber: 91,\n                columnNumber: 9\n            }, this),\n            poster && !shouldLoad && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                src: poster,\n                alt: \"Video preview\",\n                fill: true,\n                className: \"object-cover rounded-lg\"\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                lineNumber: 102,\n                columnNumber: 9\n            }, this),\n            shouldLoad && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                autoPlay: autoPlay,\n                muted: muted,\n                loop: loop,\n                playsInline: playsInline,\n                controls: controls,\n                preload: preload,\n                poster: poster,\n                className: \"w-full h-full object-cover rounded-lg \".concat(controls ? \"cursor-pointer\" : \"\"),\n                onLoadStart: handleLoadStart,\n                onCanPlay: handleCanPlay,\n                onError: handleError,\n                style: {\n                    outline: \"none\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                        src: src,\n                        type: \"video/mp4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                        src: src.replace(\".mp4\", \".webm\"),\n                        type: \"video/webm\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-full bg-gray-100\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl\",\n                                    children: \"\\uD83D\\uDCF9\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                                    lineNumber: 134,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: \"Your browser doesn't support video playback\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 15\n                                }, this),\n                                fallbackContent\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                            lineNumber: 133,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                lineNumber: 112,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n        lineNumber: 78,\n        columnNumber: 5\n    }, this);\n}\n_s(OptimizedVideo, \"Zg8iB6wn/MI0qUbHEmOGw0Twf6Y=\");\n_c = OptimizedVideo;\nvar _c;\n$RefreshReg$(_c, \"OptimizedVideo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/optimized-video.tsx\n"));

/***/ })

});