(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6535],{12932:function(e,a,t){Promise.resolve().then(t.bind(t,77712)),Promise.resolve().then(t.bind(t,74953)),Promise.resolve().then(t.bind(t,12545)),Promise.resolve().then(t.bind(t,97867)),Promise.resolve().then(t.bind(t,31085)),Promise.resolve().then(t.bind(t,10575))},77712:function(e,a,t){"use strict";t.d(a,{default:function(){return h}});var r=t(57437),i=t(13465),n=t(42586),d=t(50408),s=t(2265),l=t(47521),o=t(39392),c=t(16593),u=t(2069),f=t(57612),b=t(8946),p=t.n(b),g=t(45558),m=t(62869),v=t(91430),x=t(30078);function h(e){var a,t,b,h,w,y;let{page:N,conversions:j,sortBy:_}=e,S=(0,n.useTranslations)("seeker"),R=(0,d.h)(),{seekers:z}=(0,x.L)(),k=(0,n.useLocale)(),{query:P}=function(e){var a;arguments.length>1&&void 0!==arguments[1]&&arguments[1];let t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"en",r=(0,u.Q)(),i=null==r?void 0:null===(a=r.data)||void 0===a?void 0:a.data,n=["favorite-seekers-listing",e],{failureCount:d,...s}=(0,c.a)({queryKey:n,queryFn:async()=>{var a,r,n;let d=e.max_price||(null==i?void 0:i.priceRange.max),s=e.min_price||(null==i?void 0:i.priceRange.min)||1,l=e.building_largest||(null==i?void 0:i.buildingSizeRange.max),c=e.building_smallest||(null==i?void 0:i.buildingSizeRange.min)||1,u=e.land_largest||(null==i?void 0:i.landSizeRange.max),b=e.land_smallest||(null==i?void 0:i.landSizeRange.min)||1,m=e.garden_largest||(null==i?void 0:i.gardenSizeRange.max),v=e.garden_smallest||(null==i?void 0:i.gardenSizeRange.min)||1,x=e.area;(null===(a=e.area)||void 0===a?void 0:a.zoom)==g.lJ.toString()&&(x=void 0);let h=(null===(r=e.type)||void 0===r?void 0:r.includes("all"))?void 0:p().uniq(null===(n=e.type)||void 0===n?void 0:n.flatMap(e=>e!==f.yJ.commercialSpace?e:[f.yJ.cafeOrRestaurants,f.yJ.shops,f.yJ.offices])),w={...e,type:h,search:"all"==e.search?void 0:e.search,min_price:s,max_price:d,building_largest:l,building_smallest:c,land_largest:u,land_smallest:b,garden_largest:m,garden_smallest:v,area:x||void 0,property_of_view:e.property_of_view,sort_by:e.sort_by};return e.min_price&&e.min_price!=(null==i?void 0:i.priceRange.min)||d!=(null==i?void 0:i.priceRange.max)||(w.max_price=void 0,w.min_price=void 0),e.building_smallest&&e.building_smallest!=(null==i?void 0:i.buildingSizeRange.min)||l!=(null==i?void 0:i.buildingSizeRange.max)||(w.building_largest=void 0,w.building_smallest=void 0),e.land_smallest&&e.land_smallest!=(null==i?void 0:i.landSizeRange.min)||u!=(null==i?void 0:i.landSizeRange.max)||(w.land_largest=void 0,w.land_smallest=void 0),e.garden_smallest&&e.garden_smallest!=(null==i?void 0:i.gardenSizeRange.min)||m!=(null==i?void 0:i.gardenSizeRange.max)||(w.garden_largest=void 0,w.garden_smallest=void 0),await (0,o.sK)(w,t)},enabled:!0,retry:!1});return{query:s,filterQueryKey:n}}({page:N||"1",per_page:"20",sort_by:_||"DATE_NEWEST"},(null==z?void 0:z.email)!="",k);return(0,s.useEffect)(()=>{if(P.isError)return R.setIsLoading(!1)},[P.isError]),(0,s.useEffect)(()=>{var e,a;R.setIsLoading(P.isPending),P.isSuccess&&(R.setData((null===(e=P.data)||void 0===e?void 0:e.data)||[]),R.setTotal((null===(a=P.data.meta)||void 0===a?void 0:a.total)||0))},[null===(a=P.data)||void 0===a?void 0:a.data]),(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("section",{className:"min-h-[calc(100vh-202px)]",children:(0,r.jsx)("div",{className:"grid gap-3 gap-x-3 gap-y-6 max-sm:my-4 md:grid-cols-2 xl:grid-cols-4",children:P.isPending?Array(12).fill(0).map((e,a)=>(0,r.jsx)(l.yZ,{},a)):R.data&&R.data.length>0?(0,r.jsx)(r.Fragment,{children:R.data.map((e,a)=>(0,r.jsx)("div",{children:(0,r.jsxs)(l.yd,{className:"space-y-3",data:{...e,isFavorite:!0},conversion:j,handleFavoriteListing:e=>{if(!e){let e=R.data.filter((e,t)=>t!==a);R.setData(e||[])}},children:[(0,r.jsx)(l.Zf,{heartSize:"large",allowFavoriteWhileInactive:!0}),(0,r.jsxs)("div",{className:"px-0.5 space-y-2",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{className:"text-base text-seekers-text font-semibold line-clamp-1",children:e.title}),(0,r.jsx)(l.$D,{className:"text-seekers-text"})]}),(0,r.jsx)(l.hj,{})]})]})},a))}):(0,r.jsxs)("div",{className:"col-span-4 flex flex-col justify-center items-center",children:[(0,r.jsx)("p",{className:"col-span-full text-center font-semibold max-w-md py-8",children:S("listing.misc.favoritePropertyNotFound")}),(0,r.jsx)(m.z,{variant:"default-seekers",asChild:!0,children:(0,r.jsx)(v.rU,{href:"/",children:S("cta.showAllProperty")})})]})})}),(0,r.jsx)("section",{className:"!my-12",children:P.isPending||(null===(b=P.data)||void 0===b?void 0:null===(t=b.data)||void 0===t?void 0:t.length)&&(null===(w=P.data)||void 0===w?void 0:null===(h=w.data)||void 0===h?void 0:h.length)<20&&1==P.data.meta.pageCount?(0,r.jsx)(r.Fragment,{}):(0,r.jsx)("div",{className:"w-fit mx-auto",children:(0,r.jsx)(i.g,{meta:null==P?void 0:null===(y=P.data)||void 0===y?void 0:y.meta,totalThreshold:20,disableRowPerPage:!0})})})]})}},12545:function(e,a,t){"use strict";t.d(a,{DD:function(){return S},TZ:function(){return z},Hz:function(){return k},Ks:function(){return C},nO:function(){return P},SidebarProvider:function(){return _},SidebarTrigger:function(){return R}});var r=t(57437),i=t(2265),n=t(98482),d=t(90535),s=t(94508),l=t(62869),o=t(95186),c=t(6512),u=t(92360),f=t(20653);let b=u.fC;u.xz,u.x8;let p=u.h_,g=i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)(u.aV,{className:(0,s.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...i,ref:a})});g.displayName=u.aV.displayName;let m=(0,d.j)("fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500 data-[state=open]:animate-in data-[state=closed]:animate-out",{variants:{side:{top:"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4 border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),v=i.forwardRef((e,a)=>{let{side:t="right",className:i,children:n,...d}=e;return(0,r.jsxs)(p,{children:[(0,r.jsx)(g,{}),(0,r.jsxs)(u.VY,{ref:a,className:(0,s.cn)(m({side:t}),i),...d,children:[(0,r.jsxs)(u.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary",children:[(0,r.jsx)(f.Pxu,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]}),n]})]})});v.displayName=u.VY.displayName,i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)(u.Dx,{ref:a,className:(0,s.cn)("text-lg font-semibold text-foreground",t),...i})}).displayName=u.Dx.displayName,i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)(u.dk,{ref:a,className:(0,s.cn)("text-sm text-muted-foreground",t),...i})}).displayName=u.dk.displayName;var x=t(93022),h=t(81103),w=t(92451),y=t(10407);let N=i.createContext(null);function j(){let e=i.useContext(N);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}let _=i.forwardRef((e,a)=>{let{defaultOpen:t=!0,open:n,onOpenChange:d,className:l,style:o,children:c,...u}=e,f=function(){let[e,a]=i.useState(void 0);return i.useEffect(()=>{let e=window.matchMedia("(max-width: ".concat(767,"px)")),t=()=>{a(window.innerWidth<768)};return e.addEventListener("change",t),a(window.innerWidth<768),()=>e.removeEventListener("change",t)},[]),!!e}(),[b,p]=i.useState(!1),[g,m]=i.useState(t),v=null!=n?n:g,x=i.useCallback(e=>{let a="function"==typeof e?e(v):e;d?d(a):m(a),document.cookie="".concat("sidebar:state","=").concat(a,"; path=/; max-age=").concat(604800)},[d,v]),w=i.useCallback(()=>f?p(e=>!e):x(e=>!e),[f,x,p]);i.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),w())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[w]);let y=v?"expanded":"collapsed",j=i.useMemo(()=>({state:y,open:v,setOpen:x,isMobile:f,openMobile:b,setOpenMobile:p,toggleSidebar:w}),[y,v,x,f,b,p,w]);return(0,r.jsx)(N.Provider,{value:j,children:(0,r.jsx)(h.TooltipProvider,{delayDuration:0,children:(0,r.jsx)("div",{style:{"--sidebar-width":"16rem","--sidebar-width-icon":"0",...o},className:(0,s.cn)("group/sidebar-wrapper flex w-full has-[[data-variant=inset]]:bg-sidebar",l),ref:a,...u,children:c})})})});_.displayName="SidebarProvider";let S=i.forwardRef((e,a)=>{let{side:t="left",variant:i="sidebar",collapsible:n="offcanvas",className:d,children:l,...o}=e,{isMobile:c,state:u,openMobile:f,setOpenMobile:p}=j();return"none"===n?(0,r.jsx)("div",{className:(0,s.cn)("flex h-full w-[--sidebar-width] flex-col bg-sidebar text-sidebar-foreground",d),ref:a,...o,children:l}):c?(0,r.jsx)(b,{open:f,onOpenChange:p,...o,children:(0,r.jsx)(v,{"data-sidebar":"sidebar","data-mobile":"true",className:"w-[--sidebar-width] bg-sidebar p-0 text-sidebar-foreground [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:t,children:(0,r.jsx)("div",{className:"flex h-full w-full flex-col",children:l})})}):(0,r.jsxs)("div",{ref:a,className:(0,s.cn)("group peer hidden text-sidebar-foreground md:block","sidebar"==i&&"h-full"),"data-state":u,"data-collapsible":"collapsed"===u?n:"","data-variant":i,"data-side":t,children:[(0,r.jsx)("div",{className:(0,s.cn)("relative h-full w-[--sidebar-width] bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===i||"inset"===i?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4))]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon]")}),(0,r.jsx)("div",{className:(0,s.cn)("fixed inset-y-0 z-10 hidden h-full w-[--sidebar-width] transition-[left,right,width] duration-200 ease-linear md:flex","left"===t?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===i||"inset"===i?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)_+_theme(spacing.4)_+2px)]":"group-data-[collapsible=icon]:w-[--sidebar-width-icon] group-data-[side=left]:border-r group-data-[side=right]:border-l",d),...o,children:(0,r.jsx)("div",{"data-sidebar":"sidebar",className:"flex h-full w-full flex-col bg-sidebar group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:border-sidebar-border group-data-[variant=floating]:shadow",children:l})})]})});S.displayName="Sidebar";let R=i.forwardRef((e,a)=>{let{className:t,onClick:i,...n}=e,{toggleSidebar:d,open:o}=j();return(0,r.jsxs)(l.z,{ref:a,"data-sidebar":"trigger",variant:"ghost",size:"icon",className:(0,s.cn)("h-7 w-7",t),onClick:e=>{null==i||i(e),d()},...n,children:[o?(0,r.jsx)(w.Z,{}):(0,r.jsx)(y.Z,{}),(0,r.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})});R.displayName="SidebarTrigger",i.forwardRef((e,a)=>{let{className:t,...i}=e,{toggleSidebar:n}=j();return(0,r.jsx)("button",{ref:a,"data-sidebar":"rail","aria-label":"Toggle Sidebar",tabIndex:-1,onClick:n,title:"Toggle Sidebar",className:(0,s.cn)("absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] hover:after:bg-sidebar-border group-data-[side=left]:-right-4 group-data-[side=right]:left-0 sm:flex","[[data-side=left]_&]:cursor-w-resize [[data-side=right]_&]:cursor-e-resize","[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize","group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full group-data-[collapsible=offcanvas]:hover:bg-sidebar","[[data-side=left][data-collapsible=offcanvas]_&]:w-0","[[data-side=right][data-collapsible=offcanvas]_&]:w-0",t),...i})}).displayName="SidebarRail",i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)("main",{ref:a,className:(0,s.cn)("relative flex min-h-full flex-1 flex-col bg-background","peer-data-[variant=inset]:min-h-[calc(100svh-theme(spacing.4))] md:peer-data-[variant=inset]:m-2 md:peer-data-[state=collapsed]:peer-data-[variant=inset]:ml-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow",t),...i})}).displayName="SidebarInset",i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)(o.I,{ref:a,"data-sidebar":"input",className:(0,s.cn)("h-8 w-full bg-background shadow-none focus-visible:ring-2 focus-visible:ring-sidebar-ring",t),...i})}).displayName="SidebarInput",i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)("div",{ref:a,"data-sidebar":"header",className:(0,s.cn)("flex flex-col gap-2 p-2",t),...i})}).displayName="SidebarHeader",i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)("div",{ref:a,"data-sidebar":"footer",className:(0,s.cn)("flex flex-col gap-2 p-2",t),...i})}).displayName="SidebarFooter",i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)(c.Separator,{ref:a,"data-sidebar":"separator",className:(0,s.cn)("mx-2 w-auto bg-sidebar-border",t),...i})}).displayName="SidebarSeparator";let z=i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)("div",{ref:a,"data-sidebar":"content",className:(0,s.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",t),...i})});z.displayName="SidebarContent";let k=i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)("div",{ref:a,"data-sidebar":"group",className:(0,s.cn)("relative flex w-full min-w-0 flex-col p-2",t),...i})});k.displayName="SidebarGroup";let P=i.forwardRef((e,a)=>{let{className:t,asChild:i=!1,...d}=e,l=i?n.g7:"div";return(0,r.jsx)(l,{ref:a,"data-sidebar":"group-label",className:(0,s.cn)("flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium text-sidebar-foreground/70 outline-none ring-sidebar-ring transition-[margin,opa] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",t),...d})});P.displayName="SidebarGroupLabel",i.forwardRef((e,a)=>{let{className:t,asChild:i=!1,...d}=e,l=i?n.g7:"button";return(0,r.jsx)(l,{ref:a,"data-sidebar":"group-action",className:(0,s.cn)("absolute right-3 top-3.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","group-data-[collapsible=icon]:hidden",t),...d})}).displayName="SidebarGroupAction";let C=i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)("div",{ref:a,"data-sidebar":"group-content",className:(0,s.cn)("relative pl-8 before:absolute before:left-4 before:top-0 before:h-full before:w-px before:bg-border",t),...i})});C.displayName="SidebarGroupContent",i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)("ul",{ref:a,"data-sidebar":"menu",className:(0,s.cn)("flex w-full min-w-0 flex-col gap-1",t),...i})}).displayName="SidebarMenu",i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)("li",{ref:a,"data-sidebar":"menu-item",className:(0,s.cn)("group/menu-item relative",t),...i})}).displayName="SidebarMenuItem";let E=(0,d.j)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-none ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-[[data-sidebar=menu-action]]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:!size-8 group-data-[collapsible=icon]:!p-2 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:!p-0"}},defaultVariants:{variant:"default",size:"default"}});i.forwardRef((e,a)=>{let{asChild:t=!1,isActive:i=!1,variant:d="default",size:l="default",tooltip:o,className:c,...u}=e,f=t?n.g7:"button",{isMobile:b,state:p}=j(),g=(0,r.jsx)(f,{ref:a,"data-sidebar":"menu-button","data-size":l,"data-active":i,className:(0,s.cn)(E({variant:d,size:l}),c),...u});return o?("string"==typeof o&&(o={children:o}),(0,r.jsxs)(h.Tooltip,{children:[(0,r.jsx)(h.TooltipTrigger,{asChild:!0,children:g}),(0,r.jsx)(h.TooltipContent,{side:"right",align:"center",hidden:"collapsed"!==p||b,...o})]})):g}).displayName="SidebarMenuButton",i.forwardRef((e,a)=>{let{className:t,asChild:i=!1,showOnHover:d=!1,...l}=e,o=i?n.g7:"button";return(0,r.jsx)(o,{ref:a,"data-sidebar":"menu-action",className:(0,s.cn)("absolute right-1 top-1.5 flex aspect-square w-5 items-center justify-center rounded-md p-0 text-sidebar-foreground outline-none ring-sidebar-ring transition-transform hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 peer-hover/menu-button:text-sidebar-accent-foreground [&>svg]:size-4 [&>svg]:shrink-0","after:absolute after:-inset-2 after:md:hidden","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",d&&"group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 peer-data-[active=true]/menu-button:text-sidebar-accent-foreground md:opacity-0",t),...l})}).displayName="SidebarMenuAction",i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)("div",{ref:a,"data-sidebar":"menu-badge",className:(0,s.cn)("pointer-events-none absolute right-1 flex h-5 min-w-5 select-none items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums text-sidebar-foreground","peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground","peer-data-[size=sm]/menu-button:top-1","peer-data-[size=default]/menu-button:top-1.5","peer-data-[size=lg]/menu-button:top-2.5","group-data-[collapsible=icon]:hidden",t),...i})}).displayName="SidebarMenuBadge",i.forwardRef((e,a)=>{let{className:t,showIcon:n=!1,...d}=e,l=i.useMemo(()=>"".concat(Math.floor(40*Math.random())+50,"%"),[]);return(0,r.jsxs)("div",{ref:a,"data-sidebar":"menu-skeleton",className:(0,s.cn)("flex h-8 items-center gap-2 rounded-md px-2",t),...d,children:[n&&(0,r.jsx)(x.O,{className:"size-4 rounded-md","data-sidebar":"menu-skeleton-icon"}),(0,r.jsx)(x.O,{className:"h-4 max-w-[--skeleton-width] flex-1","data-sidebar":"menu-skeleton-text",style:{"--skeleton-width":l}})]})}).displayName="SidebarMenuSkeleton",i.forwardRef((e,a)=>{let{className:t,...i}=e;return(0,r.jsx)("ul",{ref:a,"data-sidebar":"menu-sub",className:(0,s.cn)("mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l border-sidebar-border px-2.5 py-0.5","group-data-[collapsible=icon]:hidden",t),...i})}).displayName="SidebarMenuSub",i.forwardRef((e,a)=>{let{...t}=e;return(0,r.jsx)("li",{ref:a,...t})}).displayName="SidebarMenuSubItem",i.forwardRef((e,a)=>{let{asChild:t=!1,size:i="md",isActive:d,className:l,...o}=e,c=t?n.g7:"a";return(0,r.jsx)(c,{ref:a,"data-sidebar":"menu-sub-button","data-size":i,"data-active":d,className:(0,s.cn)("flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 text-sidebar-foreground outline-none ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0 [&>svg]:text-sidebar-accent-foreground","data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground","sm"===i&&"text-xs","md"===i&&"text-sm","group-data-[collapsible=icon]:hidden",l),...o})}).displayName="SidebarMenuSubButton"},10575:function(e,a,t){"use strict";t.d(a,{default:function(){return d}});var r=t(49988),i=t(2265),n=t(69362);function d(e){let{locale:a,...t}=e;if(!a)throw Error("Failed to determine locale in `NextIntlClientProvider`, please provide the `locale` prop explicitly.\n\nSee https://next-intl-docs.vercel.app/docs/configuration#locale");return i.createElement(n.IntlProvider,(0,r.g)({locale:a},t))}}},function(e){e.O(0,[6990,8310,7699,680,1866,6290,8094,2586,2957,4956,3448,8658,6088,9623,3398,6205,4216,3682,3145,1298,4461,7060,4797,6245,8750,3675,8100,3267,9389,2971,2117,1744],function(){return e(e.s=12932)}),_N_E=e.O()}]);