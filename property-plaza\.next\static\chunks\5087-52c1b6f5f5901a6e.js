(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5087],{58996:function(e){var t;t=function(){"use strict";function e(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);t&&(i=i.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,i)}return r}function t(t){for(var r=1;r<arguments.length;r++){var i=null!=arguments[r]?arguments[r]:{};r%2?e(Object(i),!0).forEach(function(e){var r,a;r=e,a=i[e],(r=n(r))in t?Object.defineProperty(t,r,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[r]=a}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):e(Object(i)).forEach(function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))})}return t}function r(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,n(i.key),i)}}function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(e[i]=r[i])}return e}).apply(this,arguments)}function n(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var i=r.call(e,t||"default");if("object"!=typeof i)return i;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}var a,o,l,u,c,s,f,h={exports:{}};"undefined"!=typeof window&&(o=(a=window).HTMLCanvasElement&&a.HTMLCanvasElement.prototype,u=(l=a.Blob&&function(){try{return new Blob,!0}catch(e){return!1}}())&&a.Uint8Array&&function(){try{return 100===new Blob([new Uint8Array(100)]).size}catch(e){return!1}}(),c=a.BlobBuilder||a.WebKitBlobBuilder||a.MozBlobBuilder||a.MSBlobBuilder,s=/^data:((.*?)(;charset=.*?)?)(;base64)?,/,f=(l||c)&&a.atob&&a.ArrayBuffer&&a.Uint8Array&&function(e){var t,r,i,n,a,o,f,h,d;if(!(t=e.match(s)))throw Error("invalid data URI");for(h=0,r=t[2]?t[1]:"text/plain"+(t[3]||";charset=US-ASCII"),i=!!t[4],n=e.slice(t[0].length),f=new Uint8Array(o=new ArrayBuffer((a=i?atob(n):decodeURIComponent(n)).length));h<a.length;h+=1)f[h]=a.charCodeAt(h);return l?new Blob([u?f:o],{type:r}):((d=new c).append(o),d.getBlob(r))},a.HTMLCanvasElement&&!o.toBlob&&(o.mozGetAsFile?o.toBlob=function(e,t,r){var i=this;setTimeout(function(){r&&o.toDataURL&&f?e(f(i.toDataURL(t,r))):e(i.mozGetAsFile("blob",t))})}:o.toDataURL&&f&&(o.msToBlob?o.toBlob=function(e,t,r){var i=this;setTimeout(function(){(t&&"image/png"!==t||r)&&o.toDataURL&&f?e(f(i.toDataURL(t,r))):e(i.msToBlob(t))})}:o.toBlob=function(e,t,r){var i=this;setTimeout(function(){e(f(i.toDataURL(t,r)))})})),h.exports?h.exports=f:a.dataURLtoBlob=f);var d=h.exports,p={strict:!0,checkOrientation:!0,retainExif:!1,maxWidth:1/0,maxHeight:1/0,minWidth:0,minHeight:0,width:void 0,height:void 0,resize:"none",quality:.8,mimeType:"auto",convertTypes:["image/png"],convertSize:5e6,beforeDraw:null,drew:null,success:null,error:null},m="undefined"!=typeof window&&void 0!==window.document?window:{},v=function(e){return e>0&&e<1/0},g=Array.prototype.slice;function b(e){return Array.from?Array.from(e):g.call(e)}var y=/^image\/.+$/;function w(e){return y.test(e)}var x=String.fromCharCode,j=m.btoa;function k(e,t){for(var r=[],i=new Uint8Array(e);i.length>0;)r.push(x.apply(null,b(i.subarray(0,8192)))),i=i.subarray(8192);return"data:".concat(t,";base64,").concat(j(r.join("")))}var U=/\.\d*(?:0|9){12}\d*$/;function B(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e11;return U.test(e)?Math.round(e*t)/t:e}function O(e){var t=e.aspectRatio,r=e.height,i=e.width,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"none",a=v(i),o=v(r);if(a&&o){var l=r*t;("contain"===n||"none"===n)&&l>i||"cover"===n&&l<i?r=i/t:i=r*t}else a?r=i/t:o&&(i=r*t);return{width:i,height:r}}var E=m.ArrayBuffer,A=m.FileReader,R=m.URL||m.webkitURL,T=/\.\w+$/,M=m.Compressor;return function(){var e,n;function a(e,r){(function(e,t){if(!(e instanceof t))throw TypeError("Cannot call a class as a function")})(this,a),this.file=e,this.exif=[],this.image=new Image,this.options=t(t({},p),r),this.aborted=!1,this.result=null,this.init()}return e=[{key:"init",value:function(){var e=this,t=this.file,r=this.options;if(!("undefined"!=typeof Blob&&(t instanceof Blob||"[object Blob]"===Object.prototype.toString.call(t)))){this.fail(Error("The first argument must be a File or Blob object."));return}var n=t.type;if(!w(n)){this.fail(Error("The first argument must be an image File or Blob object."));return}if(!R||!A){this.fail(Error("The current browser does not support image compression."));return}E||(r.checkOrientation=!1,r.retainExif=!1);var a="image/jpeg"===n,o=a&&r.checkOrientation,l=a&&r.retainExif;if(!R||o||l){var u=new A;this.reader=u,u.onload=function(r){var a=r.target.result,u={},c=1;o&&(c=function(e){var t=new DataView(e);try{if(255===t.getUint8(0)&&216===t.getUint8(1))for(var r=t.byteLength,i=2;i+1<r;){if(255===t.getUint8(i)&&225===t.getUint8(i+1)){s=i;break}i+=1}if(s){var n=s+4,a=s+10;if("Exif"===function(e,t,r){var i,n="";for(r+=t,i=t;i<r;i+=1)n+=x(e.getUint8(i));return n}(t,n,4)){var o=t.getUint16(a);if(((c=18761===o)||19789===o)&&42===t.getUint16(a+2,c)){var l=t.getUint32(a+4,c);l>=8&&(f=a+l)}}}if(f){var u,c,s,f,h,d,p=t.getUint16(f,c);for(d=0;d<p;d+=1)if(h=f+12*d+2,274===t.getUint16(h,c)){h+=8,u=t.getUint16(h,c),t.setUint16(h,1,c);break}}}catch(e){u=1}return u}(a))>1&&i(u,function(e){var t=0,r=1,i=1;switch(e){case 2:r=-1;break;case 3:t=-180;break;case 4:i=-1;break;case 5:t=90,i=-1;break;case 6:t=90;break;case 7:t=90,r=-1;break;case 8:t=-90}return{rotate:t,scaleX:r,scaleY:i}}(c)),l&&(e.exif=function(e){for(var t=b(new Uint8Array(e)),r=t.length,i=[],n=0;n+3<r;){var a=t[n],o=t[n+1];if(255===a&&218===o)break;if(255===a&&216===o)n+=2;else{var l=256*t[n+2]+t[n+3],u=n+l+2,c=t.slice(n,u);i.push(c),n=u}}return i.reduce(function(e,t){return 255===t[0]&&225===t[1]?e.concat(t):e},[])}(a)),o||l?!R||c>1?u.url=k(a,n):u.url=R.createObjectURL(t):u.url=a,e.load(u)},u.onabort=function(){e.fail(Error("Aborted to read the image with FileReader."))},u.onerror=function(){e.fail(Error("Failed to read the image with FileReader."))},u.onloadend=function(){e.reader=null},o||l?u.readAsArrayBuffer(t):u.readAsDataURL(t)}else this.load({url:R.createObjectURL(t)})}},{key:"load",value:function(e){var r=this,i=this.file,n=this.image;n.onload=function(){r.draw(t(t({},e),{},{naturalWidth:n.naturalWidth,naturalHeight:n.naturalHeight}))},n.onabort=function(){r.fail(Error("Aborted to load the image."))},n.onerror=function(){r.fail(Error("Failed to load the image."))},m.navigator&&/(?:iPad|iPhone|iPod).*?AppleWebKit/i.test(m.navigator.userAgent)&&(n.crossOrigin="anonymous"),n.alt=i.name,n.src=e.url}},{key:"draw",value:function(e){var t=this,r=e.naturalWidth,i=e.naturalHeight,n=e.rotate,a=void 0===n?0:n,o=e.scaleX,l=e.scaleY,u=this.file,c=this.image,s=this.options,f=document.createElement("canvas"),h=f.getContext("2d"),p=Math.abs(a)%180==90,m=("contain"===s.resize||"cover"===s.resize)&&v(s.width)&&v(s.height),g=Math.max(s.maxWidth,0)||1/0,y=Math.max(s.maxHeight,0)||1/0,x=Math.max(s.minWidth,0)||0,j=Math.max(s.minHeight,0)||0,U=r/i,E=s.width,R=s.height;if(p){var T=[y,g];g=T[0],y=T[1];var M=[j,x];x=M[0],j=M[1];var C=[R,E];E=C[0],R=C[1]}m&&(U=E/R);var P=O({aspectRatio:U,width:g,height:y},"contain");g=P.width,y=P.height;var L=O({aspectRatio:U,width:x,height:j},"cover");if(x=L.width,j=L.height,m){var S=O({aspectRatio:U,width:E,height:R},s.resize);E=S.width,R=S.height}else{var D=O({aspectRatio:U,width:E,height:R}),W=D.width;E=void 0===W?r:W;var z=D.height;R=void 0===z?i:z}E=Math.floor(B(Math.min(Math.max(E,x),g))),R=Math.floor(B(Math.min(Math.max(R,j),y)));var F=-E/2,N=-R/2,H=E,I=R,_=[];if(m){var $=0,Z=0,V=r,q=i,X=O({aspectRatio:U,width:r,height:i},{contain:"cover",cover:"contain"}[s.resize]);V=X.width,q=X.height,$=(r-V)/2,Z=(i-q)/2,_.push($,Z,V,q)}if(_.push(F,N,H,I),p){var Y=[R,E];E=Y[0],R=Y[1]}f.width=E,f.height=R,w(s.mimeType)||(s.mimeType=u.type);var G="transparent";u.size>s.convertSize&&s.convertTypes.indexOf(s.mimeType)>=0&&(s.mimeType="image/jpeg");var K="image/jpeg"===s.mimeType;if(K&&(G="#fff"),h.fillStyle=G,h.fillRect(0,0,E,R),s.beforeDraw&&s.beforeDraw.call(this,h,f),!this.aborted&&(h.save(),h.translate(E/2,R/2),h.rotate(a*Math.PI/180),h.scale(void 0===o?1:o,void 0===l?1:l),h.drawImage.apply(h,[c].concat(_)),h.restore(),s.drew&&s.drew.call(this,h,f),!this.aborted)){var J=function(e){if(!t.aborted){var n=function(e){return t.done({naturalWidth:r,naturalHeight:i,result:e})};if(e&&K&&s.retainExif&&t.exif&&t.exif.length>0){var a=function(e){return n(d(k(function(e,t){var r=b(new Uint8Array(e));if(255!==r[2]||224!==r[3])return e;var i=256*r[4]+r[5];return new Uint8Array([255,216].concat(t,r.slice(4+i)))}(e,t.exif),s.mimeType)))};if(e.arrayBuffer)e.arrayBuffer().then(a).catch(function(){t.fail(Error("Failed to read the compressed image with Blob.arrayBuffer()."))});else{var o=new A;t.reader=o,o.onload=function(e){a(e.target.result)},o.onabort=function(){t.fail(Error("Aborted to read the compressed image with FileReader."))},o.onerror=function(){t.fail(Error("Failed to read the compressed image with FileReader."))},o.onloadend=function(){t.reader=null},o.readAsArrayBuffer(e)}}else n(e)}};f.toBlob?f.toBlob(J,s.mimeType,s.quality):J(d(f.toDataURL(s.mimeType,s.quality)))}}},{key:"done",value:function(e){var t=e.naturalWidth,r=e.naturalHeight,i=e.result,n=this.file,a=this.image,o=this.options;if(R&&0===a.src.indexOf("blob:")&&R.revokeObjectURL(a.src),i){if(o.strict&&!o.retainExif&&i.size>n.size&&o.mimeType===n.type&&!(o.width>t||o.height>r||o.minWidth>t||o.minHeight>r||o.maxWidth<t||o.maxHeight<r))i=n;else{var l,u,c=new Date;i.lastModified=c.getTime(),i.lastModifiedDate=c,i.name=n.name,i.name&&i.type!==n.type&&(i.name=i.name.replace(T,("jpeg"===(u=w(l=i.type)?l.substr(6):"")&&(u="jpg"),".".concat(u))))}}else i=n;this.result=i,o.success&&o.success.call(this,i)}},{key:"fail",value:function(e){var t=this.options;if(t.error)t.error.call(this,e);else throw e}},{key:"abort",value:function(){this.aborted||(this.aborted=!0,this.reader?this.reader.abort():this.image.complete?this.fail(Error("The compression process has been aborted.")):(this.image.onload=null,this.image.onabort()))}}],n=[{key:"noConflict",value:function(){return window.Compressor=M,a}},{key:"setDefaults",value:function(e){i(p,e)}}],e&&r(a.prototype,e),n&&r(a,n),Object.defineProperty(a,"prototype",{writable:!1}),a}()},e.exports=t()},30401:function(e,t,r){"use strict";r.d(t,{Z:function(){return i}});let i=(0,r(79205).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},45675:function(e,t,r){"use strict";r.d(t,{Z:function(){return i}});let i=(0,r(79205).Z)("Pencil",[["path",{d:"M21.174 6.812a1 1 0 0 0-3.986-3.987L3.842 16.174a2 2 0 0 0-.5.83l-1.321 4.352a.5.5 0 0 0 .623.622l4.353-1.32a2 2 0 0 0 .83-.497z",key:"1a8usu"}],["path",{d:"m15 5 4 4",key:"1mk7zo"}]])},32489:function(e,t,r){"use strict";r.d(t,{Z:function(){return i}});let i=(0,r(79205).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},10575:function(e,t,r){"use strict";r.d(t,{default:function(){return o}});var i=r(49988),n=r(2265),a=r(69362);function o(e){let{locale:t,...r}=e;if(!t)throw Error("Failed to determine locale in `NextIntlClientProvider`, please provide the `locale` prop explicitly.\n\nSee https://next-intl-docs.vercel.app/docs/configuration#locale");return n.createElement(a.IntlProvider,(0,i.g)({locale:t},r))}},70650:function(e,t,r){"use strict";r.d(t,{Ee:function(){return w},F$:function(){return v},NY:function(){return x},fC:function(){return y}});var i=r(2265),n=r(73966),a=r(26606),o=r(61188),l=r(82912),u=r(57437),c="Avatar",[s,f]=(0,n.b)(c),[h,d]=s(c),p=i.forwardRef((e,t)=>{let{__scopeAvatar:r,...n}=e,[a,o]=i.useState("idle");return(0,u.jsx)(h,{scope:r,imageLoadingStatus:a,onImageLoadingStatusChange:o,children:(0,u.jsx)(l.WV.span,{...n,ref:t})})});p.displayName=c;var m="AvatarImage",v=i.forwardRef((e,t)=>{let{__scopeAvatar:r,src:n,onLoadingStatusChange:c=()=>{},...s}=e,f=d(m,r),h=function(e){let[t,r]=i.useState("idle");return(0,o.b)(()=>{if(!e){r("error");return}let t=!0,i=new window.Image,n=e=>()=>{t&&r(e)};return r("loading"),i.onload=n("loaded"),i.onerror=n("error"),i.src=e,()=>{t=!1}},[e]),t}(n),p=(0,a.W)(e=>{c(e),f.onImageLoadingStatusChange(e)});return(0,o.b)(()=>{"idle"!==h&&p(h)},[h,p]),"loaded"===h?(0,u.jsx)(l.WV.img,{...s,ref:t,src:n}):null});v.displayName=m;var g="AvatarFallback",b=i.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:n,...a}=e,o=d(g,r),[c,s]=i.useState(void 0===n);return i.useEffect(()=>{if(void 0!==n){let e=window.setTimeout(()=>s(!0),n);return()=>window.clearTimeout(e)}},[n]),c&&"loaded"!==o.imageLoadingStatus?(0,u.jsx)(l.WV.span,{...a,ref:t}):null});b.displayName=g;var y=p,w=v,x=b},73966:function(e,t,r){"use strict";r.d(t,{b:function(){return a}});var i=r(2265),n=r(57437);function a(e,t=[]){let r=[],a=()=>{let t=r.map(e=>i.createContext(e));return function(r){let n=r?.[e]||t;return i.useMemo(()=>({[`__scope${e}`]:{...r,[e]:n}}),[r,n])}};return a.scopeName=e,[function(t,a){let o=i.createContext(a),l=r.length;function u(t){let{scope:r,children:a,...u}=t,c=r?.[e][l]||o,s=i.useMemo(()=>u,Object.values(u));return(0,n.jsx)(c.Provider,{value:s,children:a})}return r=[...r,a],u.displayName=t+"Provider",[u,function(r,n){let u=n?.[e][l]||o,c=i.useContext(u);if(c)return c;if(void 0!==a)return a;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let n=r.reduce((t,{useScope:r,scopeName:i})=>{let n=r(e)[`__scope${i}`];return{...t,...n}},{});return i.useMemo(()=>({[`__scope${t.scopeName}`]:n}),[n])}};return r.scopeName=t.scopeName,r}(a,...t)]}}}]);