"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7668],{62869:function(e,t,r){r.d(t,{z:function(){return l}});var n=r(57437),i=r(2265),o=r(98482),s=r(90535),a=r(94508),u=r(51817);let c=(0,s.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-seekers-primary text-white shadow hover:bg-seekers-primary/90","default-seekers":"bg-seekers-primary text-white shadow hover:bg-seekers-primary-light/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),l=i.forwardRef((e,t)=>{let{className:r,variant:i,size:s,asChild:l=!1,loading:d=!1,...f}=e,m=l?o.g7:"button";return(0,n.jsx)(m,{className:(0,a.cn)(c({variant:i,size:s,className:r})),ref:t,disabled:d||f.disabled,...f,children:d?(0,n.jsx)(u.Z,{className:(0,a.cn)("h-4 w-4 animate-spin")}):f.children})});l.displayName="Button"},15681:function(e,t,r){r.d(t,{NI:function(){return g},Wi:function(){return d},l0:function(){return c},lX:function(){return v},pf:function(){return h},xJ:function(){return p},zG:function(){return b}});var n=r(57437),i=r(2265),o=r(98482),s=r(29501),a=r(94508),u=r(26815);let c=s.RV,l=i.createContext({}),d=e=>{let{...t}=e;return(0,n.jsx)(l.Provider,{value:{name:t.name},children:(0,n.jsx)(s.Qr,{...t})})},f=()=>{let e=i.useContext(l),t=i.useContext(m),{getFieldState:r,formState:n}=(0,s.Gc)(),o=r(e.name,n);if(!e)throw Error("useFormField should be used within <FormField>");let{id:a}=t;return{id:a,name:e.name,formItemId:"".concat(a,"-form-item"),formDescriptionId:"".concat(a,"-form-item-description"),formMessageId:"".concat(a,"-form-item-message"),...o}},m=i.createContext({}),p=i.forwardRef((e,t)=>{let{className:r,...o}=e,s=i.useId();return(0,n.jsx)(m.Provider,{value:{id:s},children:(0,n.jsx)("div",{ref:t,className:(0,a.cn)("space-y-2",r),...o})})});p.displayName="FormItem";let v=i.forwardRef((e,t)=>{let{className:r,...i}=e,{error:o,formItemId:s}=f();return(0,n.jsx)(u._,{ref:t,className:(0,a.cn)(o&&"text-destructive",r),htmlFor:s,...i})});v.displayName="FormLabel";let g=i.forwardRef((e,t)=>{let{...r}=e,{error:i,formItemId:s,formDescriptionId:a,formMessageId:u}=f();return(0,n.jsx)(o.g7,{ref:t,id:s,"aria-describedby":i?"".concat(a," ").concat(u):"".concat(a),"aria-invalid":!!i,...r})});g.displayName="FormControl";let h=i.forwardRef((e,t)=>{let{className:r,...i}=e,{formDescriptionId:o}=f();return(0,n.jsx)("p",{ref:t,id:o,className:(0,a.cn)("text-[0.8rem] text-muted-foreground",r),...i})});h.displayName="FormDescription";let b=i.forwardRef((e,t)=>{let{className:r,children:i,...o}=e,{error:s,formMessageId:u}=f(),c=s?String(null==s?void 0:s.message):i;return c?(0,n.jsx)("p",{ref:t,id:u,className:(0,a.cn)("text-[0.8rem] font-medium text-destructive",r),...o,children:c}):null});b.displayName="FormMessage"},95186:function(e,t,r){r.d(t,{I:function(){return s}});var n=r(57437),i=r(2265),o=r(94508);let s=i.forwardRef((e,t)=>{let{className:r,type:i,...s}=e;return(0,n.jsx)("input",{type:i,className:(0,o.cn)("flex max-sm:h-8 h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-inset focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...s})});s.displayName="Input"},26815:function(e,t,r){r.d(t,{_:function(){return c}});var n=r(57437),i=r(2265),o=r(6394),s=r(90535),a=r(94508);let u=(0,s.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,n.jsx)(o.f,{ref:t,className:(0,a.cn)(u(),r),...i})});c.displayName=o.f.displayName},6512:function(e,t,r){r.d(t,{Separator:function(){return a}});var n=r(57437),i=r(2265),o=r(90759),s=r(94508);let a=i.forwardRef((e,t)=>{let{className:r,orientation:i="horizontal",decorative:a=!0,...u}=e;return(0,n.jsx)(o.f,{ref:t,decorative:a,orientation:i,className:(0,s.cn)("shrink-0 bg-border","horizontal"===i?"h-[1px] w-full":"h-full w-[1px]",r),...u})});a.displayName=o.f.displayName},24596:function(e,t,r){r.d(t,{K:function(){return c}});var n=r(31389),i=r(29827),o=r(21770),s=r(77647),a=r(35153),u=r(42586);function c(){let e=(0,i.NL)(),{toast:t}=(0,a.pm)(),r=(0,u.useTranslations)("universal");return(0,o.D)({mutationFn:e=>(0,n.f_)(e),onSuccess:()=>{e.invalidateQueries({queryKey:[s.J]}),t({title:r("success.updateUser")})},onError:e=>{let n=e.response.data;t({title:r("error.foundError"),description:n.message,variant:"destructive"})}})}},77647:function(e,t,r){r.d(t,{J:function(){return u},l:function(){return c}});var n=r(25367),i=r(6404),o=r(30078),s=r(16593),a=r(64131);let u="my-detail";function c(){let e=!(arguments.length>0)||void 0===arguments[0]||arguments[0],{setSeekers:t,clearUser:r,setRole:c}=(0,o.L)(e=>e),l=a.Z.get(i.LA);return(0,s.a)({queryKey:[u,l||"0"],queryFn:async()=>{if(!l)return o._;try{let e=await (0,n.O4)();return t(e),c("SEEKER"),e}catch(e){return r(),o._}},refetchOnWindowFocus:!1,retry:!1,enabled:e})}},49607:function(e,t,r){r.d(t,{apiClient:function(){return u},v:function(){return c}});var n=r(6404),i=r(83464),o=r(64131),s=r(51983);let a=new(r.n(s)()).Agent({rejectUnauthorized:!1}),u=i.Z.create({baseURL:"https://dev.property-plaza.id/api/v1",headers:{Authorization:o.Z.get(n.LA)?"Bearer "+o.Z.get(n.LA):""},httpsAgent:a}),c=i.Z.create({baseURL:"/api/",httpsAgent:a})},89047:function(e,t,r){r.d(t,{B9:function(){return n},Dn:function(){return i}});let n={archiver:"Achiever",finder:"Finder",free:"Free"},i={contactOwner:"contact-owner",photos:"photos",mapLocation:"map-location",advanceAndSaveFilter:"advance-and-save-filter",savedListing:"saved-listings",favoriteProperties:"favorite-properties"}},45558:function(e,t,r){r.d(t,{$8:function(){return a},ek:function(){return i},lJ:function(){return s},nH:function(){return o},pb:function(){return u}});var n=r(89047);function i(e){return n.B9.free.includes(e)?n.B9.free:n.B9.finder.includes(e)?n.B9.finder:n.B9.archiver.includes(e)?n.B9.archiver:n.B9.free}function o(e){return e==n.B9.free?0:e==n.B9.finder?5:e==n.B9.archiver?10:0}let s=10,a={max:13,min:10};function u(e){return e==n.B9.free?a:e==n.B9.finder?{max:14,min:s}:e==n.B9.archiver?{max:15,min:s}:a}},31389:function(e,t,r){r.d(t,{a$:function(){return i},f_:function(){return o},jo:function(){return s}});var n=r(49607);let i=async(e,t)=>n.apiClient.post("auth/register",e,{headers:{"g-token":t||""}}),o=async e=>n.apiClient.put("users/update",e),s=async e=>n.apiClient.get("auth/me",e)},25367:function(e,t,r){r.d(t,{O4:function(){return s}});var n=r(31389),i=r(45558);r(6404);var o=r(74442);async function s(e){try{let t=await (0,n.jo)(e);return function(e){var t,r,n,o,s,a,u,c,l,d;let f=(0,i.ek)((null===(t=e.accounts.subscription)||void 0===t?void 0:t.detail.name)||"");return{accounts:{about:e.accounts.about,citizenship:e.accounts.citizenship||"",credit:{amount:(null===(r=e.accounts.credit)||void 0===r?void 0:r.amount)||0,updatedAt:(null===(n=e.accounts.credit)||void 0===n?void 0:n.updated_at)||""},facebookSocial:e.accounts.facebook_social||"",firstName:e.accounts.first_name,image:e.accounts.image,isSubscriber:e.accounts.is_subscriber,language:e.accounts.language,lastName:e.accounts.last_name,membership:f,twitterSocial:e.accounts.twitter_social||"",address:e.accounts.address||"",chat:{current:0,max:(0,i.nH)(f)},zoomFeature:(0,i.pb)(f)},has2FA:e.is_2fa,email:e.email,code:e.code,isActive:e.is_active,phoneNumber:e.phone_number,phoneCode:e.phone_code,type:e.type,setting:{messageNotif:null===(o=e.accounts.settings)||void 0===o?void 0:o.message_notif,newsletterNotif:null===(s=e.accounts.settings)||void 0===s?void 0:s.newsletter_notif,priceAlertNotif:null===(a=e.accounts.settings)||void 0===a?void 0:a.price_alert_notif,propertyNotif:null===(u=e.accounts.settings)||void 0===u?void 0:u.property_notif,soundNotif:null===(c=e.accounts.settings)||void 0===c?void 0:c.sound_notif,specialOfferNotif:null===(l=e.accounts.settings)||void 0===l?void 0:l.special_offer_notif,surveyNotif:null===(d=e.accounts.settings)||void 0===d?void 0:d.survey_notif}}}(t.data.data)}catch(e){throw Error((0,o.q)(e))}}},74442:function(e,t,r){r.d(t,{q:function(){return i}});var n=r(83464);function i(e){if(n.Z.isAxiosError(e)){var t,r;if((null===(t=e.response)||void 0===t?void 0:t.status)===401)throw Error("Unauthorized: Invalid token or missing credentials");if((null===(r=e.response)||void 0===r?void 0:r.status)===404)throw Error("Not Found: The requested resource could not be found");if(e.response)throw Error("Request failed with status code ".concat(e.response.status,": ").concat(e.response.statusText));if(e.request)throw Error("No response received: Possible network error or wrong endpoint");else throw Error("Error during request setup: ".concat(e.message))}throw Error(e)}},35153:function(e,t,r){r.d(t,{pm:function(){return f}});var n=r(2265);let i=0,o=new Map,s=e=>{if(o.has(e))return;let t=setTimeout(()=>{o.delete(e),l({type:"REMOVE_TOAST",toastId:e})},1e6);o.set(e,t)},a=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?s(r):e.toasts.forEach(e=>{s(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},u=[],c={toasts:[]};function l(e){c=a(c,e),u.forEach(e=>{e(c)})}function d(e){let{...t}=e,r=(i=(i+1)%Number.MAX_SAFE_INTEGER).toString(),n=()=>l({type:"DISMISS_TOAST",toastId:r});return l({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||n()}}}),{id:r,dismiss:n,update:e=>l({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function f(){let[e,t]=n.useState(c);return n.useEffect(()=>(u.push(t),()=>{let e=u.indexOf(t);e>-1&&u.splice(e,1)}),[e]),{...e,toast:d,dismiss:e=>l({type:"DISMISS_TOAST",toastId:e})}}},6404:function(e,t,r){r.d(t,{$_:function(){return p},Ge:function(){return f},K6:function(){return d},LA:function(){return n},QY:function(){return m},Y:function(){return v},Z9:function(){return o},ac:function(){return a},gr:function(){return i},nM:function(){return s},t8:function(){return l},vQ:function(){return c},xm:function(){return u}});let n="tkn",i="SEEKER",o=8,s=1,a=30,u=300,c=10,l="cookies-collection-status",d="necessary-cookies-collection-status",f="functional-cookies-collection-status",m="analytic-cookies-collection-status",p="marketing-cookies-collection-status",v={type:"t",minPrice:"minp",maxPrice:"maxp",landLargest:"landl",landSmallest:"lands",buildingLargest:"buildl",buildingSmallest:"builds",gardenLargest:"gardenl",gardenSmallest:"gardens",yearsOfBuild:"yob",bedroomTotal:"bedt",bathroomTotal:"batht",rentalOffer:"ro",propertyCondition:"pc",electircity:"el",parking:"pk",swimmingPool:"sp",typeLiving:"tl",furnished:"fs",view:"v",minimumContract:"minc",category:"c",subCategory:"sc",feature:"feat",propertyLocation:"pl",zoom:"z",viewMode:"viewMode"}},94508:function(e,t,r){r.d(t,{E6:function(){return d},ET:function(){return p},Fg:function(){return m},cn:function(){return a},g6:function(){return f},pl:function(){return v},uf:function(){return l},xG:function(){return c},yT:function(){return g}});var n=r(61994),i=r(77398),o=r.n(i),s=r(53335);function a(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.m6)((0,n.W)(t))}r(25566);let u=e=>{switch(e){case"USD":default:return"en-us";case"IDR":return"id-ID";case"GBP":return"en-GB";case"AUD":return"en-AU";case"EUR":return"nl-NL"}},c=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return arguments.length>2&&void 0!==arguments[2]&&arguments[2],new Intl.NumberFormat(u(t),{style:"currency",currency:t,minimumFractionDigits:0,maximumFractionDigits:"IDR"==t?0:2}).format(e)},l=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"en-US";return new Intl.NumberFormat(t,{style:"decimal",minimumFractionDigits:0,maximumFractionDigits:0}).format(e)};function d(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}function f(e){let t=o()(e),r=o()();return t.isSame(r,"day")?t.format("HH:mm"):t.format("DD/MM/YY")}function m(e){return e.replaceAll(/[^a-zA-Z0-9\s]/g,"-").replaceAll(/\s/g,"--")}let p=(e,t)=>e.includes(t)?e.filter(e=>e!==t):[...e,t];function v(e,t){return e.some(e=>t.includes(e))}let g=e=>e.charAt(0).toUpperCase()+e.slice(1)},30078:function(e,t,r){r.d(t,{L:function(){return l},_:function(){return c}});var n=r(45558),i=r(59625),o=r(89134),s=r(64131),a=r(89047);let u={getItem:e=>{let t=s.Z.get(e);return t?JSON.parse(t):null},setItem:(e,t)=>{s.Z.set(e,JSON.stringify(t),{expires:7})},removeItem:e=>{s.Z.remove(e)}},c={accounts:{about:"",citizenship:"",credit:{amount:0,updatedAt:""},facebookSocial:"",firstName:"",image:"",isSubscriber:!1,language:"",lastName:"",membership:a.B9.free,twitterSocial:"",address:"",chat:{current:0,max:0},zoomFeature:n.$8},has2FA:!1,email:"",code:"",isActive:!1,phoneNumber:"",phoneCode:"",type:"SEEKER",setting:{messageNotif:!1,newsletterNotif:!1,priceAlertNotif:!1,propertyNotif:!1,soundNotif:!1,specialOfferNotif:!1,surveyNotif:!1}},l=(0,i.Ue)()((0,o.tJ)(e=>({role:void 0,setRole:t=>e({role:t}),seekers:c,setSeekers:t=>e({seekers:t}),tempSubscribtionLevel:0,setTempSubscribtionLevel:t=>e({tempSubscribtionLevel:t}),clearUser:()=>e(()=>({seekers:c}))}),{name:"user",storage:(0,o.FL)(()=>u)}))}}]);