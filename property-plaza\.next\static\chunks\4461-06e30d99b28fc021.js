"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4461],{24336:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("ArrowDownUp",[["path",{d:"m3 16 4 4 4-4",key:"1co6wj"}],["path",{d:"M7 20V4",key:"1yoxec"}],["path",{d:"m21 8-4-4-4 4",key:"1c9v7m"}],["path",{d:"M17 4v16",key:"7dpous"}]])},30401:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},87769:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("EyeOff",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]])},42208:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},55736:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("LayoutGrid",[["rect",{width:"7",height:"7",x:"3",y:"3",rx:"1",key:"1g98yp"}],["rect",{width:"7",height:"7",x:"14",y:"3",rx:"1",key:"6d4xhi"}],["rect",{width:"7",height:"7",x:"14",y:"14",rx:"1",key:"nxv5o0"}],["rect",{width:"7",height:"7",x:"3",y:"14",rx:"1",key:"1bb6yr"}]])},45886:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("MessagesSquare",[["path",{d:"M14 9a2 2 0 0 1-2 2H6l-4 4V4c0-1.1.9-2 2-2h8a2 2 0 0 1 2 2z",key:"jj09z8"}],["path",{d:"M18 9h2a2 2 0 0 1 2 2v11l-4-4h-6a2 2 0 0 1-2-2v-1",key:"1cx29u"}]])},92369:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},32489:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},12119:function(e,t,n){Object.defineProperty(t,"$",{enumerable:!0,get:function(){return o}});let r=n(83079);function o(e){let{createServerReference:t}=n(6671);return t(e,r.callServer)}},70650:function(e,t,n){n.d(t,{Ee:function(){return w},F$:function(){return v},NY:function(){return S},fC:function(){return b}});var r=n(2265),o=n(73966),a=n(26606),l=n(61188),i=n(82912),u=n(57437),c="Avatar",[s,d]=(0,o.b)(c),[p,f]=s(c),m=r.forwardRef((e,t)=>{let{__scopeAvatar:n,...o}=e,[a,l]=r.useState("idle");return(0,u.jsx)(p,{scope:n,imageLoadingStatus:a,onImageLoadingStatusChange:l,children:(0,u.jsx)(i.WV.span,{...o,ref:t})})});m.displayName=c;var h="AvatarImage",v=r.forwardRef((e,t)=>{let{__scopeAvatar:n,src:o,onLoadingStatusChange:c=()=>{},...s}=e,d=f(h,n),p=function(e){let[t,n]=r.useState("idle");return(0,l.b)(()=>{if(!e){n("error");return}let t=!0,r=new window.Image,o=e=>()=>{t&&n(e)};return n("loading"),r.onload=o("loaded"),r.onerror=o("error"),r.src=e,()=>{t=!1}},[e]),t}(o),m=(0,a.W)(e=>{c(e),d.onImageLoadingStatusChange(e)});return(0,l.b)(()=>{"idle"!==p&&m(p)},[p,m]),"loaded"===p?(0,u.jsx)(i.WV.img,{...s,ref:t,src:o}):null});v.displayName=h;var g="AvatarFallback",y=r.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:o,...a}=e,l=f(g,n),[c,s]=r.useState(void 0===o);return r.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>s(!0),o);return()=>window.clearTimeout(e)}},[o]),c&&"loaded"!==l.imageLoadingStatus?(0,u.jsx)(i.WV.span,{...a,ref:t}):null});y.displayName=g;var b=m,w=v,S=y},35934:function(e,t,n){n.d(t,{VM:function(){return h},Ww:function(){return m},uZ:function(){return v}});var r=n(2265),o=Object.defineProperty,a=Object.defineProperties,l=Object.getOwnPropertyDescriptors,i=Object.getOwnPropertySymbols,u=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable,s=(e,t,n)=>t in e?o(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,d=(e,t)=>{for(var n in t||(t={}))u.call(t,n)&&s(e,n,t[n]);if(i)for(var n of i(t))c.call(t,n)&&s(e,n,t[n]);return e},p=(e,t)=>a(e,l(t)),f=(e,t)=>{var n={};for(var r in e)u.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&i)for(var r of i(e))0>t.indexOf(r)&&c.call(e,r)&&(n[r]=e[r]);return n},m="^\\d+$",h=r.createContext({}),v=r.forwardRef((e,t)=>{let n;var o,a,l,i,u,{value:c,onChange:s,maxLength:v,textAlign:b="left",pattern:w=m,inputMode:S="numeric",onComplete:E,pushPasswordManagerStrategy:k="increase-width",containerClassName:x,noScriptCSSFallback:O=y,render:P,children:j}=e,C=f(e,["value","onChange","maxLength","textAlign","pattern","inputMode","onComplete","pushPasswordManagerStrategy","containerClassName","noScriptCSSFallback","render","children"]);let[M,R]=r.useState("string"==typeof C.defaultValue?C.defaultValue:""),D=null!=c?c:M,W=(n=r.useRef(),r.useEffect(()=>{n.current=D}),n.current),T=r.useCallback(e=>{null==s||s(e),R(e)},[s]),A=r.useMemo(()=>w?"string"==typeof w?new RegExp(w):w:null,[w]),N=r.useRef(null),I=r.useRef(null),Z=r.useRef({value:D,onChange:T,isIOS:"undefined"!=typeof window&&(null==(a=null==(o=null==window?void 0:window.CSS)?void 0:o.supports)?void 0:a.call(o,"-webkit-touch-callout","none"))}),_=r.useRef({prev:[null==(l=N.current)?void 0:l.selectionStart,null==(i=N.current)?void 0:i.selectionEnd,null==(u=N.current)?void 0:u.selectionDirection]});r.useImperativeHandle(t,()=>N.current,[]),r.useEffect(()=>{let e=N.current,t=I.current;if(!e||!t)return;function n(){if(document.activeElement!==e){V(null),G(null);return}let t=e.selectionStart,n=e.selectionEnd,r=e.selectionDirection,o=e.maxLength,a=e.value,l=_.current.prev,i=-1,u=-1,c;if(0!==a.length&&null!==t&&null!==n){let e=t===n,r=t===a.length&&a.length<o;if(e&&!r){if(0===t)i=0,u=1,c="forward";else if(t===o)i=t-1,u=t,c="backward";else if(o>1&&a.length>1){let e=0;if(null!==l[0]&&null!==l[1]){c=t<l[1]?"backward":"forward";let n=l[0]===l[1]&&l[0]<o;"backward"!==c||n||(e=-1)}i=e+t,u=e+t+1}}-1!==i&&-1!==u&&i!==u&&N.current.setSelectionRange(i,u,c)}let s=-1!==i?i:t,d=-1!==u?u:n,p=null!=c?c:r;V(s),G(d),_.current.prev=[s,d,p]}if(Z.current.value!==e.value&&Z.current.onChange(e.value),_.current.prev=[e.selectionStart,e.selectionEnd,e.selectionDirection],document.addEventListener("selectionchange",n,{capture:!0}),n(),document.activeElement===e&&H(!0),!document.getElementById("input-otp-style")){let e=document.createElement("style");if(e.id="input-otp-style",document.head.appendChild(e),e.sheet){let t="background: transparent !important; color: transparent !important; border-color: transparent !important; opacity: 0 !important; box-shadow: none !important; -webkit-box-shadow: none !important; -webkit-text-fill-color: transparent !important;";g(e.sheet,"[data-input-otp]::selection { background: transparent !important; color: transparent !important; }"),g(e.sheet,`[data-input-otp]:autofill { ${t} }`),g(e.sheet,`[data-input-otp]:-webkit-autofill { ${t} }`),g(e.sheet,"@supports (-webkit-touch-callout: none) { [data-input-otp] { letter-spacing: -.6em !important; font-weight: 100 !important; font-stretch: ultra-condensed; font-optical-sizing: none !important; left: -1px !important; right: 1px !important; } }"),g(e.sheet,"[data-input-otp] + * { pointer-events: all !important; }")}}let r=()=>{t&&t.style.setProperty("--root-height",`${e.clientHeight}px`)};r();let o=new ResizeObserver(r);return o.observe(e),()=>{document.removeEventListener("selectionchange",n,{capture:!0}),o.disconnect()}},[]);let[B,F]=r.useState(!1),[L,H]=r.useState(!1),[z,V]=r.useState(null),[$,G]=r.useState(null);r.useEffect(()=>{var e;setTimeout(e=()=>{var e,t,n,r;null==(e=N.current)||e.dispatchEvent(new Event("input"));let o=null==(t=N.current)?void 0:t.selectionStart,a=null==(n=N.current)?void 0:n.selectionEnd,l=null==(r=N.current)?void 0:r.selectionDirection;null!==o&&null!==a&&(V(o),G(a),_.current.prev=[o,a,l])},0),setTimeout(e,10),setTimeout(e,50)},[D,L]),r.useEffect(()=>{void 0!==W&&D!==W&&W.length<v&&D.length===v&&(null==E||E(D))},[v,E,W,D]);let U=function({containerRef:e,inputRef:t,pushPasswordManagerStrategy:n,isFocused:o}){let a=r.useRef({done:!1,refocused:!1}),[l,i]=r.useState(!1),[u,c]=r.useState(!1),[s,d]=r.useState(!1),p=r.useMemo(()=>"none"!==n&&("increase-width"===n||"experimental-no-flickering"===n)&&l&&u,[l,u,n]),f=r.useCallback(()=>{let r=e.current,o=t.current;if(!r||!o||s||"none"===n)return;let l=r.getBoundingClientRect().left+r.offsetWidth,u=r.getBoundingClientRect().top+r.offsetHeight/2;if(!(0===document.querySelectorAll('[data-lastpass-icon-root],com-1password-button,[data-dashlanecreated],[style$="2147483647 !important;"]').length&&document.elementFromPoint(l-18,u)===r)&&(i(!0),d(!0),!a.current.refocused&&document.activeElement===o)){let e=[o.selectionStart,o.selectionEnd];o.blur(),o.focus(),o.setSelectionRange(e[0],e[1]),a.current.refocused=!0}},[e,t,s,n]);return r.useEffect(()=>{let t=e.current;if(!t||"none"===n)return;function r(){c(window.innerWidth-t.getBoundingClientRect().right>=40)}r();let o=setInterval(r,1e3);return()=>{clearInterval(o)}},[e,n]),r.useEffect(()=>{let e=o||document.activeElement===t.current;if("none"===n||!e)return;let r=setTimeout(f,0),a=setTimeout(f,2e3),l=setTimeout(f,5e3),i=setTimeout(()=>{d(!0)},6e3);return()=>{clearTimeout(r),clearTimeout(a),clearTimeout(l),clearTimeout(i)}},[t,o,n,f]),{hasPWMBadge:l,willPushPWMBadge:p,PWM_BADGE_SPACE_WIDTH:"40px"}}({containerRef:I,inputRef:N,pushPasswordManagerStrategy:k,isFocused:L}),q=r.useCallback(e=>{let t=e.currentTarget.value.slice(0,v);if(t.length>0&&A&&!A.test(t)){e.preventDefault();return}"string"==typeof W&&t.length<W.length&&document.dispatchEvent(new Event("selectionchange")),T(t)},[v,T,W,A]),X=r.useCallback(()=>{var e;if(N.current){let t=Math.min(N.current.value.length,v-1),n=N.current.value.length;null==(e=N.current)||e.setSelectionRange(t,n),V(t),G(n)}H(!0)},[v]),Y=r.useCallback(e=>{var t,n;let r=N.current;if(!Z.current.isIOS||!e.clipboardData||!r)return;let o=e.clipboardData.getData("text/plain");e.preventDefault();let a=null==(t=N.current)?void 0:t.selectionStart,l=null==(n=N.current)?void 0:n.selectionEnd,i=(a!==l?D.slice(0,a)+o+D.slice(l):D.slice(0,a)+o+D.slice(a)).slice(0,v);if(i.length>0&&A&&!A.test(i))return;r.value=i,T(i);let u=Math.min(i.length,v-1),c=i.length;r.setSelectionRange(u,c),V(u),G(c)},[v,T,A,D]),J=r.useMemo(()=>({position:"relative",cursor:C.disabled?"default":"text",userSelect:"none",WebkitUserSelect:"none",pointerEvents:"none"}),[C.disabled]),K=r.useMemo(()=>({position:"absolute",inset:0,width:U.willPushPWMBadge?`calc(100% + ${U.PWM_BADGE_SPACE_WIDTH})`:"100%",clipPath:U.willPushPWMBadge?`inset(0 ${U.PWM_BADGE_SPACE_WIDTH} 0 0)`:void 0,height:"100%",display:"flex",textAlign:b,opacity:"1",color:"transparent",pointerEvents:"all",background:"transparent",caretColor:"transparent",border:"0 solid transparent",outline:"0 solid transparent",boxShadow:"none",lineHeight:"1",letterSpacing:"-.5em",fontSize:"var(--root-height)",fontFamily:"monospace",fontVariantNumeric:"tabular-nums"}),[U.PWM_BADGE_SPACE_WIDTH,U.willPushPWMBadge,b]),Q=r.useMemo(()=>r.createElement("input",p(d({autoComplete:C.autoComplete||"one-time-code"},C),{"data-input-otp":!0,"data-input-otp-mss":z,"data-input-otp-mse":$,inputMode:S,pattern:null==A?void 0:A.source,style:K,maxLength:v,value:D,ref:N,onPaste:e=>{var t;Y(e),null==(t=C.onPaste)||t.call(C,e)},onChange:q,onMouseOver:e=>{var t;F(!0),null==(t=C.onMouseOver)||t.call(C,e)},onMouseLeave:e=>{var t;F(!1),null==(t=C.onMouseLeave)||t.call(C,e)},onFocus:e=>{var t;X(),null==(t=C.onFocus)||t.call(C,e)},onBlur:e=>{var t;H(!1),null==(t=C.onBlur)||t.call(C,e)}})),[q,X,Y,S,K,v,$,z,C,null==A?void 0:A.source,D]),ee=r.useMemo(()=>({slots:Array.from({length:v}).map((e,t)=>{let n=L&&null!==z&&null!==$&&(z===$&&t===z||t>=z&&t<$),r=void 0!==D[t]?D[t]:null;return{char:r,isActive:n,hasFakeCaret:n&&null===r}}),isFocused:L,isHovering:!C.disabled&&B}),[L,B,v,$,z,C.disabled,D]),et=r.useMemo(()=>P?P(ee):r.createElement(h.Provider,{value:ee},j),[j,ee,P]);return r.createElement(r.Fragment,null,null!==O&&r.createElement("noscript",null,r.createElement("style",null,O)),r.createElement("div",{ref:I,"data-input-otp-container":!0,style:J,className:x},et,r.createElement("div",{style:{position:"absolute",inset:0,pointerEvents:"none"}},Q)))});function g(e,t){try{e.insertRule(t)}catch(e){console.error("input-otp could not insert CSS rule:",t)}}v.displayName="Input";var y=`
[data-input-otp] {
  --nojs-bg: white !important;
  --nojs-fg: black !important;

  background-color: var(--nojs-bg) !important;
  color: var(--nojs-fg) !important;
  caret-color: var(--nojs-fg) !important;
  letter-spacing: .25em !important;
  text-align: center !important;
  border: 1px solid var(--nojs-fg) !important;
  border-radius: 4px !important;
  width: 100% !important;
}
@media (prefers-color-scheme: dark) {
  [data-input-otp] {
    --nojs-bg: black !important;
    --nojs-fg: white !important;
  }
}`},67620:function(e,t,n){n.d(t,{CL:function(){return r.useReCaptcha}}),n(22248),n(93753);var r=n(16471);n(56302)},46231:function(e,t,n){n.d(t,{w_:function(){return s}});var r=n(2265),o={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},a=r.createContext&&r.createContext(o),l=["attr","size","title"];function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(this,arguments)}function u(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function c(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?u(Object(n),!0).forEach(function(t){var r,o;r=t,o=n[t],(r=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(r))in e?Object.defineProperty(e,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):e[r]=o}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):u(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function s(e){return t=>r.createElement(d,i({attr:c({},e.attr)},t),function e(t){return t&&t.map((t,n)=>r.createElement(t.tag,c({key:n},t.attr),e(t.child)))}(e.child))}function d(e){var t=t=>{var n,{attr:o,size:a,title:u}=e,s=function(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n={};for(var r in e)if(Object.prototype.hasOwnProperty.call(e,r)){if(t.indexOf(r)>=0)continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],!(t.indexOf(n)>=0)&&Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(e,l),d=a||t.size||"1em";return t.className&&(n=t.className),e.className&&(n=(n?n+" ":"")+e.className),r.createElement("svg",i({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},t.attr,o,s,{className:n,style:c(c({color:e.color||t.color},t.style),e.style),height:d,width:d,xmlns:"http://www.w3.org/2000/svg"}),u&&r.createElement("title",null,u),e.children)};return void 0!==a?r.createElement(a.Consumer,null,e=>t(e)):t(o)}}}]);