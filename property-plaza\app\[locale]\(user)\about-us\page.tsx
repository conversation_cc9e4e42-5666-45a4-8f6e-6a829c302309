import { Metadata } from "next"
import AboutUsContent from "./about-us-content"
import { getLocale, getTranslations } from "next-intl/server"
import { aboutUsUrl } from "@/lib/constanta/route"
import { routing } from "@/lib/locale/routing"

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations("seeker")
  const locale = await getLocale() || routing.defaultLocale
  const baseUrl = process.env.USER_DOMAIN || "https://www.property-plaza.com/"

  return {
    title: t('metadata.aboutUs.title'),
    description: t('metadata.aboutUs.description'),
    keywords: t('metadata.aboutUs.keyword'),
    openGraph: {
      title: t('metadata.aboutUs.title'),
      description: t('metadata.aboutUs.description'),
      images: [
        {
          url: baseUrl + "og.png",
          width: 1200,
          height: 630,
          alt: "Property Plaza Team"
        }
      ],
      type: "website",
      siteName: "Property Plaza",
      url: baseUrl + aboutUsUrl.replace("/", "")
    },
    twitter: {
      card: "summary_large_image",
      title: t('metadata.aboutUs.title'),
      description: t('metadata.aboutUs.description'),
      images: [
        {
          url: baseUrl + "og.png",
          width: 1200,
          height: 630,
          alt: "Property Plaza",
        }
      ],
      site: baseUrl + aboutUsUrl.replace("/", "")
    },
    alternates: {
      canonical: baseUrl + locale + aboutUsUrl,
      languages: {
        en: baseUrl + "en" + aboutUsUrl,
        id: baseUrl + "id" + aboutUsUrl,
        "x-default": baseUrl + aboutUsUrl.replace("/", ""),
      }
    },
    robots: {
      index: true,
      follow: true
    }
  }

}

export default function AboutUsPage() {
  return <>
    <AboutUsContent />
  </>
} 