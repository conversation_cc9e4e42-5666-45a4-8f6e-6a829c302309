import { BaseMetadataProps, BaseProps } from "@/types/base"
import CreatePasswordForm from "./form/create-password.form"
import { redirect } from "next/navigation"
import { getTranslations } from "next-intl/server"
import { Metadata } from "next"


export async function generateMetadata({ params, searchParams }: BaseMetadataProps<{}>): Promise<Metadata> {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const t = await getTranslations("seeker")
  return {
    title: t('metadata.rootLayout.title'),
    description: t('metadata.rootLayout.description'),
    alternates: {
      languages: {
        "id": process.env.USER_DOMAIN + "/id",
        "en": process.env.USER_DOMAIN + "/en",
        "x-default": process.env.USER_DOMAIN + "/en",
      },
    },
    robots: {
      index: false,
      follow: false
    },

  }
}

export default function ResetPasswordPage({ searchParams }: BaseProps) {
  const { email, token } = searchParams
  if (!email && !token) return redirect("/")
  return (
    <div className="container flex items-center justify-center min-h-screen py-10">
      <div className="max-w-sm max-sm:p-4">
        {/* <Content /> */}
        <CreatePasswordForm email={email as string} token={token as string} />
      </div>
    </div>
  )
}