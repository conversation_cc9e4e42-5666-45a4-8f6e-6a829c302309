"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6205],{50032:function(t,e,n){n.d(e,{x7:function(){return tc},Me:function(){return tr},oo:function(){return ts},RR:function(){return tl},Cp:function(){return tu},dr:function(){return ta},cv:function(){return ti},uY:function(){return to},dp:function(){return tf}});let r=["top","right","bottom","left"],i=Math.min,o=Math.max,l=Math.round,f=Math.floor,u=t=>({x:t,y:t}),c={left:"right",right:"left",bottom:"top",top:"bottom"},a={start:"end",end:"start"};function s(t,e){return"function"==typeof t?t(e):t}function d(t){return t.split("-")[0]}function h(t){return t.split("-")[1]}function p(t){return"x"===t?"y":"x"}function m(t){return"y"===t?"height":"width"}function g(t){return["top","bottom"].includes(d(t))?"y":"x"}function y(t){return t.replace(/start|end/g,t=>a[t])}function w(t){return t.replace(/left|right|bottom|top/g,t=>c[t])}function x(t){return"number"!=typeof t?{top:0,right:0,bottom:0,left:0,...t}:{top:t,right:t,bottom:t,left:t}}function v(t){let{x:e,y:n,width:r,height:i}=t;return{width:r,height:i,top:n,left:e,right:e+r,bottom:n+i,x:e,y:n}}function b(t,e,n){let r,{reference:i,floating:o}=t,l=g(e),f=p(g(e)),u=m(f),c=d(e),a="y"===l,s=i.x+i.width/2-o.width/2,y=i.y+i.height/2-o.height/2,w=i[u]/2-o[u]/2;switch(c){case"top":r={x:s,y:i.y-o.height};break;case"bottom":r={x:s,y:i.y+i.height};break;case"right":r={x:i.x+i.width,y:y};break;case"left":r={x:i.x-o.width,y:y};break;default:r={x:i.x,y:i.y}}switch(h(e)){case"start":r[f]-=w*(n&&a?-1:1);break;case"end":r[f]+=w*(n&&a?-1:1)}return r}let R=async(t,e,n)=>{let{placement:r="bottom",strategy:i="absolute",middleware:o=[],platform:l}=n,f=o.filter(Boolean),u=await (null==l.isRTL?void 0:l.isRTL(e)),c=await l.getElementRects({reference:t,floating:e,strategy:i}),{x:a,y:s}=b(c,r,u),d=r,h={},p=0;for(let n=0;n<f.length;n++){let{name:o,fn:m}=f[n],{x:g,y:y,data:w,reset:x}=await m({x:a,y:s,initialPlacement:r,placement:d,strategy:i,middlewareData:h,rects:c,platform:l,elements:{reference:t,floating:e}});a=null!=g?g:a,s=null!=y?y:s,h={...h,[o]:{...h[o],...w}},x&&p<=50&&(p++,"object"==typeof x&&(x.placement&&(d=x.placement),x.rects&&(c=!0===x.rects?await l.getElementRects({reference:t,floating:e,strategy:i}):x.rects),{x:a,y:s}=b(c,d,u)),n=-1)}return{x:a,y:s,placement:d,strategy:i,middlewareData:h}};async function A(t,e){var n;void 0===e&&(e={});let{x:r,y:i,platform:o,rects:l,elements:f,strategy:u}=t,{boundary:c="clippingAncestors",rootBoundary:a="viewport",elementContext:d="floating",altBoundary:h=!1,padding:p=0}=s(e,t),m=x(p),g=f[h?"floating"===d?"reference":"floating":d],y=v(await o.getClippingRect({element:null==(n=await (null==o.isElement?void 0:o.isElement(g)))||n?g:g.contextElement||await (null==o.getDocumentElement?void 0:o.getDocumentElement(f.floating)),boundary:c,rootBoundary:a,strategy:u})),w="floating"===d?{x:r,y:i,width:l.floating.width,height:l.floating.height}:l.reference,b=await (null==o.getOffsetParent?void 0:o.getOffsetParent(f.floating)),R=await (null==o.isElement?void 0:o.isElement(b))&&await (null==o.getScale?void 0:o.getScale(b))||{x:1,y:1},A=v(o.convertOffsetParentRelativeRectToViewportRelativeRect?await o.convertOffsetParentRelativeRectToViewportRelativeRect({elements:f,rect:w,offsetParent:b,strategy:u}):w);return{top:(y.top-A.top+m.top)/R.y,bottom:(A.bottom-y.bottom+m.bottom)/R.y,left:(y.left-A.left+m.left)/R.x,right:(A.right-y.right+m.right)/R.x}}function L(t,e){return{top:t.top-e.height,right:t.right-e.width,bottom:t.bottom-e.height,left:t.left-e.width}}function T(t){return r.some(e=>t[e]>=0)}async function E(t,e){let{placement:n,platform:r,elements:i}=t,o=await (null==r.isRTL?void 0:r.isRTL(i.floating)),l=d(n),f=h(n),u="y"===g(n),c=["left","top"].includes(l)?-1:1,a=o&&u?-1:1,p=s(e,t),{mainAxis:m,crossAxis:y,alignmentAxis:w}="number"==typeof p?{mainAxis:p,crossAxis:0,alignmentAxis:null}:{mainAxis:0,crossAxis:0,alignmentAxis:null,...p};return f&&"number"==typeof w&&(y="end"===f?-1*w:w),u?{x:y*a,y:m*c}:{x:m*c,y:y*a}}function O(t){return k(t)?(t.nodeName||"").toLowerCase():"#document"}function S(t){var e;return(null==t||null==(e=t.ownerDocument)?void 0:e.defaultView)||window}function C(t){var e;return null==(e=(k(t)?t.ownerDocument:t.document)||window.document)?void 0:e.documentElement}function k(t){return t instanceof Node||t instanceof S(t).Node}function P(t){return t instanceof Element||t instanceof S(t).Element}function D(t){return t instanceof HTMLElement||t instanceof S(t).HTMLElement}function H(t){return"undefined"!=typeof ShadowRoot&&(t instanceof ShadowRoot||t instanceof S(t).ShadowRoot)}function F(t){let{overflow:e,overflowX:n,overflowY:r,display:i}=B(t);return/auto|scroll|overlay|hidden|clip/.test(e+r+n)&&!["inline","contents"].includes(i)}function M(t){return[":popover-open",":modal"].some(e=>{try{return t.matches(e)}catch(t){return!1}})}function W(t){let e=z(),n=P(t)?B(t):t;return"none"!==n.transform||"none"!==n.perspective||!!n.containerType&&"normal"!==n.containerType||!e&&!!n.backdropFilter&&"none"!==n.backdropFilter||!e&&!!n.filter&&"none"!==n.filter||["transform","perspective","filter"].some(t=>(n.willChange||"").includes(t))||["paint","layout","strict","content"].some(t=>(n.contain||"").includes(t))}function z(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function V(t){return["html","body","#document"].includes(O(t))}function B(t){return S(t).getComputedStyle(t)}function N(t){return P(t)?{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop}:{scrollLeft:t.scrollX,scrollTop:t.scrollY}}function _(t){if("html"===O(t))return t;let e=t.assignedSlot||t.parentNode||H(t)&&t.host||C(t);return H(e)?e.host:e}function j(t,e,n){var r;void 0===e&&(e=[]),void 0===n&&(n=!0);let i=function t(e){let n=_(e);return V(n)?e.ownerDocument?e.ownerDocument.body:e.body:D(n)&&F(n)?n:t(n)}(t),o=i===(null==(r=t.ownerDocument)?void 0:r.body),l=S(i);if(o){let t=Y(l);return e.concat(l,l.visualViewport||[],F(i)?i:[],t&&n?j(t):[])}return e.concat(i,j(i,[],n))}function Y(t){return t.parent&&Object.getPrototypeOf(t.parent)?t.frameElement:null}function I(t){let e=B(t),n=parseFloat(e.width)||0,r=parseFloat(e.height)||0,i=D(t),o=i?t.offsetWidth:n,f=i?t.offsetHeight:r,u=l(n)!==o||l(r)!==f;return u&&(n=o,r=f),{width:n,height:r,$:u}}function $(t){return P(t)?t:t.contextElement}function q(t){let e=$(t);if(!D(e))return u(1);let n=e.getBoundingClientRect(),{width:r,height:i,$:o}=I(e),f=(o?l(n.width):n.width)/r,c=(o?l(n.height):n.height)/i;return f&&Number.isFinite(f)||(f=1),c&&Number.isFinite(c)||(c=1),{x:f,y:c}}let X=u(0);function G(t){let e=S(t);return z()&&e.visualViewport?{x:e.visualViewport.offsetLeft,y:e.visualViewport.offsetTop}:X}function J(t,e,n,r){var i;void 0===e&&(e=!1),void 0===n&&(n=!1);let o=t.getBoundingClientRect(),l=$(t),f=u(1);e&&(r?P(r)&&(f=q(r)):f=q(t));let c=(void 0===(i=n)&&(i=!1),r&&(!i||r===S(l))&&i)?G(l):u(0),a=(o.left+c.x)/f.x,s=(o.top+c.y)/f.y,d=o.width/f.x,h=o.height/f.y;if(l){let t=S(l),e=r&&P(r)?S(r):r,n=t,i=Y(n);for(;i&&r&&e!==n;){let t=q(i),e=i.getBoundingClientRect(),r=B(i),o=e.left+(i.clientLeft+parseFloat(r.paddingLeft))*t.x,l=e.top+(i.clientTop+parseFloat(r.paddingTop))*t.y;a*=t.x,s*=t.y,d*=t.x,h*=t.y,a+=o,s+=l,i=Y(n=S(i))}}return v({width:d,height:h,x:a,y:s})}function K(t){return J(C(t)).left+N(t).scrollLeft}function Q(t,e,n){let r;if("viewport"===e)r=function(t,e){let n=S(t),r=C(t),i=n.visualViewport,o=r.clientWidth,l=r.clientHeight,f=0,u=0;if(i){o=i.width,l=i.height;let t=z();(!t||t&&"fixed"===e)&&(f=i.offsetLeft,u=i.offsetTop)}return{width:o,height:l,x:f,y:u}}(t,n);else if("document"===e)r=function(t){let e=C(t),n=N(t),r=t.ownerDocument.body,i=o(e.scrollWidth,e.clientWidth,r.scrollWidth,r.clientWidth),l=o(e.scrollHeight,e.clientHeight,r.scrollHeight,r.clientHeight),f=-n.scrollLeft+K(t),u=-n.scrollTop;return"rtl"===B(r).direction&&(f+=o(e.clientWidth,r.clientWidth)-i),{width:i,height:l,x:f,y:u}}(C(t));else if(P(e))r=function(t,e){let n=J(t,!0,"fixed"===e),r=n.top+t.clientTop,i=n.left+t.clientLeft,o=D(t)?q(t):u(1),l=t.clientWidth*o.x;return{width:l,height:t.clientHeight*o.y,x:i*o.x,y:r*o.y}}(e,n);else{let n=G(t);r={...e,x:e.x-n.x,y:e.y-n.y}}return v(r)}function U(t){return"static"===B(t).position}function Z(t,e){return D(t)&&"fixed"!==B(t).position?e?e(t):t.offsetParent:null}function tt(t,e){let n=S(t);if(M(t))return n;if(!D(t)){let e=_(t);for(;e&&!V(e);){if(P(e)&&!U(e))return e;e=_(e)}return n}let r=Z(t,e);for(;r&&["table","td","th"].includes(O(r))&&U(r);)r=Z(r,e);return r&&V(r)&&U(r)&&!W(r)?n:r||function(t){let e=_(t);for(;D(e)&&!V(e);){if(W(e))return e;if(M(e))break;e=_(e)}return null}(t)||n}let te=async function(t){let e=this.getOffsetParent||tt,n=this.getDimensions,r=await n(t.floating);return{reference:function(t,e,n){let r=D(e),i=C(e),o="fixed"===n,l=J(t,!0,o,e),f={scrollLeft:0,scrollTop:0},c=u(0);if(r||!r&&!o){if(("body"!==O(e)||F(i))&&(f=N(e)),r){let t=J(e,!0,o,e);c.x=t.x+e.clientLeft,c.y=t.y+e.clientTop}else i&&(c.x=K(i))}return{x:l.left+f.scrollLeft-c.x,y:l.top+f.scrollTop-c.y,width:l.width,height:l.height}}(t.reference,await e(t.floating),t.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},tn={convertOffsetParentRelativeRectToViewportRelativeRect:function(t){let{elements:e,rect:n,offsetParent:r,strategy:i}=t,o="fixed"===i,l=C(r),f=!!e&&M(e.floating);if(r===l||f&&o)return n;let c={scrollLeft:0,scrollTop:0},a=u(1),s=u(0),d=D(r);if((d||!d&&!o)&&(("body"!==O(r)||F(l))&&(c=N(r)),D(r))){let t=J(r);a=q(r),s.x=t.x+r.clientLeft,s.y=t.y+r.clientTop}return{width:n.width*a.x,height:n.height*a.y,x:n.x*a.x-c.scrollLeft*a.x+s.x,y:n.y*a.y-c.scrollTop*a.y+s.y}},getDocumentElement:C,getClippingRect:function(t){let{element:e,boundary:n,rootBoundary:r,strategy:l}=t,f=[..."clippingAncestors"===n?M(e)?[]:function(t,e){let n=e.get(t);if(n)return n;let r=j(t,[],!1).filter(t=>P(t)&&"body"!==O(t)),i=null,o="fixed"===B(t).position,l=o?_(t):t;for(;P(l)&&!V(l);){let e=B(l),n=W(l);n||"fixed"!==e.position||(i=null),(o?!n&&!i:!n&&"static"===e.position&&!!i&&["absolute","fixed"].includes(i.position)||F(l)&&!n&&function t(e,n){let r=_(e);return!(r===n||!P(r)||V(r))&&("fixed"===B(r).position||t(r,n))}(t,l))?r=r.filter(t=>t!==l):i=e,l=_(l)}return e.set(t,r),r}(e,this._c):[].concat(n),r],u=f[0],c=f.reduce((t,n)=>{let r=Q(e,n,l);return t.top=o(r.top,t.top),t.right=i(r.right,t.right),t.bottom=i(r.bottom,t.bottom),t.left=o(r.left,t.left),t},Q(e,u,l));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:tt,getElementRects:te,getClientRects:function(t){return Array.from(t.getClientRects())},getDimensions:function(t){let{width:e,height:n}=I(t);return{width:e,height:n}},getScale:q,isElement:P,isRTL:function(t){return"rtl"===B(t).direction}};function tr(t,e,n,r){let l;void 0===r&&(r={});let{ancestorScroll:u=!0,ancestorResize:c=!0,elementResize:a="function"==typeof ResizeObserver,layoutShift:s="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,h=$(t),p=u||c?[...h?j(h):[],...j(e)]:[];p.forEach(t=>{u&&t.addEventListener("scroll",n,{passive:!0}),c&&t.addEventListener("resize",n)});let m=h&&s?function(t,e){let n,r=null,l=C(t);function u(){var t;clearTimeout(n),null==(t=r)||t.disconnect(),r=null}return!function c(a,s){void 0===a&&(a=!1),void 0===s&&(s=1),u();let{left:d,top:h,width:p,height:m}=t.getBoundingClientRect();if(a||e(),!p||!m)return;let g=f(h),y=f(l.clientWidth-(d+p)),w={rootMargin:-g+"px "+-y+"px "+-f(l.clientHeight-(h+m))+"px "+-f(d)+"px",threshold:o(0,i(1,s))||1},x=!0;function v(t){let e=t[0].intersectionRatio;if(e!==s){if(!x)return c();e?c(!1,e):n=setTimeout(()=>{c(!1,1e-7)},1e3)}x=!1}try{r=new IntersectionObserver(v,{...w,root:l.ownerDocument})}catch(t){r=new IntersectionObserver(v,w)}r.observe(t)}(!0),u}(h,n):null,g=-1,y=null;a&&(y=new ResizeObserver(t=>{let[r]=t;r&&r.target===h&&y&&(y.unobserve(e),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var t;null==(t=y)||t.observe(e)})),n()}),h&&!d&&y.observe(h),y.observe(e));let w=d?J(t):null;return d&&function e(){let r=J(t);w&&(r.x!==w.x||r.y!==w.y||r.width!==w.width||r.height!==w.height)&&n(),w=r,l=requestAnimationFrame(e)}(),n(),()=>{var t;p.forEach(t=>{u&&t.removeEventListener("scroll",n),c&&t.removeEventListener("resize",n)}),null==m||m(),null==(t=y)||t.disconnect(),y=null,d&&cancelAnimationFrame(l)}}let ti=function(t){return void 0===t&&(t=0),{name:"offset",options:t,async fn(e){var n,r;let{x:i,y:o,placement:l,middlewareData:f}=e,u=await E(e,t);return l===(null==(n=f.offset)?void 0:n.placement)&&null!=(r=f.arrow)&&r.alignmentOffset?{}:{x:i+u.x,y:o+u.y,data:{...u,placement:l}}}}},to=function(t){return void 0===t&&(t={}),{name:"shift",options:t,async fn(e){let{x:n,y:r,placement:l}=e,{mainAxis:f=!0,crossAxis:u=!1,limiter:c={fn:t=>{let{x:e,y:n}=t;return{x:e,y:n}}},...a}=s(t,e),h={x:n,y:r},m=await A(e,a),y=g(d(l)),w=p(y),x=h[w],v=h[y];if(f){let t="y"===w?"top":"left",e="y"===w?"bottom":"right",n=x+m[t],r=x-m[e];x=o(n,i(x,r))}if(u){let t="y"===y?"top":"left",e="y"===y?"bottom":"right",n=v+m[t],r=v-m[e];v=o(n,i(v,r))}let b=c.fn({...e,[w]:x,[y]:v});return{...b,data:{x:b.x-n,y:b.y-r}}}}},tl=function(t){return void 0===t&&(t={}),{name:"flip",options:t,async fn(e){var n,r,i,o,l;let{placement:f,middlewareData:u,rects:c,initialPlacement:a,platform:x,elements:v}=e,{mainAxis:b=!0,crossAxis:R=!0,fallbackPlacements:L,fallbackStrategy:T="bestFit",fallbackAxisSideDirection:E="none",flipAlignment:O=!0,...S}=s(t,e);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let C=d(f),k=g(a),P=d(a)===a,D=await (null==x.isRTL?void 0:x.isRTL(v.floating)),H=L||(P||!O?[w(a)]:function(t){let e=w(t);return[y(t),e,y(e)]}(a)),F="none"!==E;!L&&F&&H.push(...function(t,e,n,r){let i=h(t),o=function(t,e,n){let r=["left","right"],i=["right","left"];switch(t){case"top":case"bottom":if(n)return e?i:r;return e?r:i;case"left":case"right":return e?["top","bottom"]:["bottom","top"];default:return[]}}(d(t),"start"===n,r);return i&&(o=o.map(t=>t+"-"+i),e&&(o=o.concat(o.map(y)))),o}(a,O,E,D));let M=[a,...H],W=await A(e,S),z=[],V=(null==(r=u.flip)?void 0:r.overflows)||[];if(b&&z.push(W[C]),R){let t=function(t,e,n){void 0===n&&(n=!1);let r=h(t),i=p(g(t)),o=m(i),l="x"===i?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return e.reference[o]>e.floating[o]&&(l=w(l)),[l,w(l)]}(f,c,D);z.push(W[t[0]],W[t[1]])}if(V=[...V,{placement:f,overflows:z}],!z.every(t=>t<=0)){let t=((null==(i=u.flip)?void 0:i.index)||0)+1,e=M[t];if(e)return{data:{index:t,overflows:V},reset:{placement:e}};let n=null==(o=V.filter(t=>t.overflows[0]<=0).sort((t,e)=>t.overflows[1]-e.overflows[1])[0])?void 0:o.placement;if(!n)switch(T){case"bestFit":{let t=null==(l=V.filter(t=>{if(F){let e=g(t.placement);return e===k||"y"===e}return!0}).map(t=>[t.placement,t.overflows.filter(t=>t>0).reduce((t,e)=>t+e,0)]).sort((t,e)=>t[1]-e[1])[0])?void 0:l[0];t&&(n=t);break}case"initialPlacement":n=a}if(f!==n)return{reset:{placement:n}}}return{}}}},tf=function(t){return void 0===t&&(t={}),{name:"size",options:t,async fn(e){let n,r;let{placement:l,rects:f,platform:u,elements:c}=e,{apply:a=()=>{},...p}=s(t,e),m=await A(e,p),y=d(l),w=h(l),x="y"===g(l),{width:v,height:b}=f.floating;"top"===y||"bottom"===y?(n=y,r=w===(await (null==u.isRTL?void 0:u.isRTL(c.floating))?"start":"end")?"left":"right"):(r=y,n="end"===w?"top":"bottom");let R=b-m.top-m.bottom,L=v-m.left-m.right,T=i(b-m[n],R),E=i(v-m[r],L),O=!e.middlewareData.shift,S=T,C=E;if(x?C=w||O?i(E,L):L:S=w||O?i(T,R):R,O&&!w){let t=o(m.left,0),e=o(m.right,0),n=o(m.top,0),r=o(m.bottom,0);x?C=v-2*(0!==t||0!==e?t+e:o(m.left,m.right)):S=b-2*(0!==n||0!==r?n+r:o(m.top,m.bottom))}await a({...e,availableWidth:C,availableHeight:S});let k=await u.getDimensions(c.floating);return v!==k.width||b!==k.height?{reset:{rects:!0}}:{}}}},tu=function(t){return void 0===t&&(t={}),{name:"hide",options:t,async fn(e){let{rects:n}=e,{strategy:r="referenceHidden",...i}=s(t,e);switch(r){case"referenceHidden":{let t=L(await A(e,{...i,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:t,referenceHidden:T(t)}}}case"escaped":{let t=L(await A(e,{...i,altBoundary:!0}),n.floating);return{data:{escapedOffsets:t,escaped:T(t)}}}default:return{}}}}},tc=t=>({name:"arrow",options:t,async fn(e){let{x:n,y:r,placement:l,rects:f,platform:u,elements:c,middlewareData:a}=e,{element:d,padding:y=0}=s(t,e)||{};if(null==d)return{};let w=x(y),v={x:n,y:r},b=p(g(l)),R=m(b),A=await u.getDimensions(d),L="y"===b,T=L?"clientHeight":"clientWidth",E=f.reference[R]+f.reference[b]-v[b]-f.floating[R],O=v[b]-f.reference[b],S=await (null==u.getOffsetParent?void 0:u.getOffsetParent(d)),C=S?S[T]:0;C&&await (null==u.isElement?void 0:u.isElement(S))||(C=c.floating[T]||f.floating[R]);let k=C/2-A[R]/2-1,P=i(w[L?"top":"left"],k),D=i(w[L?"bottom":"right"],k),H=C-A[R]-D,F=C/2-A[R]/2+(E/2-O/2),M=o(P,i(F,H)),W=!a.arrow&&null!=h(l)&&F!==M&&f.reference[R]/2-(F<P?P:D)-A[R]/2<0,z=W?F<P?F-P:F-H:0;return{[b]:v[b]+z,data:{[b]:M,centerOffset:F-M-z,...W&&{alignmentOffset:z}},reset:W}}}),ta=function(t){return void 0===t&&(t={}),{options:t,fn(e){let{x:n,y:r,placement:i,rects:o,middlewareData:l}=e,{offset:f=0,mainAxis:u=!0,crossAxis:c=!0}=s(t,e),a={x:n,y:r},h=g(i),m=p(h),y=a[m],w=a[h],x=s(f,e),v="number"==typeof x?{mainAxis:x,crossAxis:0}:{mainAxis:0,crossAxis:0,...x};if(u){let t="y"===m?"height":"width",e=o.reference[m]-o.floating[t]+v.mainAxis,n=o.reference[m]+o.reference[t]-v.mainAxis;y<e?y=e:y>n&&(y=n)}if(c){var b,R;let t="y"===m?"width":"height",e=["top","left"].includes(d(i)),n=o.reference[h]-o.floating[t]+(e&&(null==(b=l.offset)?void 0:b[h])||0)+(e?0:v.crossAxis),r=o.reference[h]+o.reference[t]+(e?0:(null==(R=l.offset)?void 0:R[h])||0)-(e?v.crossAxis:0);w<n?w=n:w>r&&(w=r)}return{[m]:y,[h]:w}}}},ts=(t,e,n)=>{let r=new Map,i={platform:tn,...n},o={...i.platform,_c:r};return R(t,e,{...i,platform:o})}},97859:function(t,e,n){n.d(e,{Cp:function(){return w},RR:function(){return g},YF:function(){return s},cv:function(){return h},dp:function(){return y},dr:function(){return m},uY:function(){return p},x7:function(){return x}});var r=n(50032),i=n(2265),o=n(54887),l="undefined"!=typeof document?i.useLayoutEffect:i.useEffect;function f(t,e){let n,r,i;if(t===e)return!0;if(typeof t!=typeof e)return!1;if("function"==typeof t&&t.toString()===e.toString())return!0;if(t&&e&&"object"==typeof t){if(Array.isArray(t)){if((n=t.length)!==e.length)return!1;for(r=n;0!=r--;)if(!f(t[r],e[r]))return!1;return!0}if((n=(i=Object.keys(t)).length)!==Object.keys(e).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(e,i[r]))return!1;for(r=n;0!=r--;){let n=i[r];if(("_owner"!==n||!t.$$typeof)&&!f(t[n],e[n]))return!1}return!0}return t!=t&&e!=e}function u(t){return"undefined"==typeof window?1:(t.ownerDocument.defaultView||window).devicePixelRatio||1}function c(t,e){let n=u(t);return Math.round(e*n)/n}function a(t){let e=i.useRef(t);return l(()=>{e.current=t}),e}function s(t){void 0===t&&(t={});let{placement:e="bottom",strategy:n="absolute",middleware:s=[],platform:d,elements:{reference:h,floating:p}={},transform:m=!0,whileElementsMounted:g,open:y}=t,[w,x]=i.useState({x:0,y:0,strategy:n,placement:e,middlewareData:{},isPositioned:!1}),[v,b]=i.useState(s);f(v,s)||b(s);let[R,A]=i.useState(null),[L,T]=i.useState(null),E=i.useCallback(t=>{t!==k.current&&(k.current=t,A(t))},[]),O=i.useCallback(t=>{t!==P.current&&(P.current=t,T(t))},[]),S=h||R,C=p||L,k=i.useRef(null),P=i.useRef(null),D=i.useRef(w),H=null!=g,F=a(g),M=a(d),W=a(y),z=i.useCallback(()=>{if(!k.current||!P.current)return;let t={placement:e,strategy:n,middleware:v};M.current&&(t.platform=M.current),(0,r.oo)(k.current,P.current,t).then(t=>{let e={...t,isPositioned:!1!==W.current};V.current&&!f(D.current,e)&&(D.current=e,o.flushSync(()=>{x(e)}))})},[v,e,n,M,W]);l(()=>{!1===y&&D.current.isPositioned&&(D.current.isPositioned=!1,x(t=>({...t,isPositioned:!1})))},[y]);let V=i.useRef(!1);l(()=>(V.current=!0,()=>{V.current=!1}),[]),l(()=>{if(S&&(k.current=S),C&&(P.current=C),S&&C){if(F.current)return F.current(S,C,z);z()}},[S,C,z,F,H]);let B=i.useMemo(()=>({reference:k,floating:P,setReference:E,setFloating:O}),[E,O]),N=i.useMemo(()=>({reference:S,floating:C}),[S,C]),_=i.useMemo(()=>{let t={position:n,left:0,top:0};if(!N.floating)return t;let e=c(N.floating,w.x),r=c(N.floating,w.y);return m?{...t,transform:"translate("+e+"px, "+r+"px)",...u(N.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:e,top:r}},[n,m,N.floating,w.x,w.y]);return i.useMemo(()=>({...w,update:z,refs:B,elements:N,floatingStyles:_}),[w,z,B,N,_])}let d=t=>({name:"arrow",options:t,fn(e){let{element:n,padding:i}="function"==typeof t?t(e):t;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?(0,r.x7)({element:n.current,padding:i}).fn(e):{}:n?(0,r.x7)({element:n,padding:i}).fn(e):{}}}),h=(t,e)=>({...(0,r.cv)(t),options:[t,e]}),p=(t,e)=>({...(0,r.uY)(t),options:[t,e]}),m=(t,e)=>({...(0,r.dr)(t),options:[t,e]}),g=(t,e)=>({...(0,r.RR)(t),options:[t,e]}),y=(t,e)=>({...(0,r.dp)(t),options:[t,e]}),w=(t,e)=>({...(0,r.Cp)(t),options:[t,e]}),x=(t,e)=>({...d(t),options:[t,e]})},90420:function(t,e,n){n.d(e,{t:function(){return o}});var r=n(2265),i=n(61188);function o(t){let[e,n]=r.useState(void 0);return(0,i.b)(()=>{if(t){n({width:t.offsetWidth,height:t.offsetHeight});let e=new ResizeObserver(e=>{let r,i;if(!Array.isArray(e)||!e.length)return;let o=e[0];if("borderBoxSize"in o){let t=o.borderBoxSize,e=Array.isArray(t)?t[0]:t;r=e.inlineSize,i=e.blockSize}else r=t.offsetWidth,i=t.offsetHeight;n({width:r,height:i})});return e.observe(t,{box:"border-box"}),()=>e.unobserve(t)}n(void 0)},[t]),e}}}]);