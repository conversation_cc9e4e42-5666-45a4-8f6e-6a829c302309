exports.id=8465,exports.ids=[8465],exports.modules={29507:(e,t,n)=>{"use strict";n.d(t,{Z:()=>a});var r=n(26269),i=n(95817),o=n(60434),a=(0,r.cache)(async function(e){let t,n;"string"==typeof e?t=e:e&&(n=e.locale,t=e.namespace);let r=await (0,o.Z)(n);return(0,i.eX)({...r,namespace:t,messages:r.messages})})},54772:(e,t)=>{"use strict";function n(e){for(let t=0;t<e.length;t++){let n=e[t];if("function"!=typeof n)throw Error(`A "use server" file can only export async functions, found ${typeof n}.
Read more: https://nextjs.org/docs/messages/invalid-use-server-value`)}}Object.defineProperty(t,"h",{enumerable:!0,get:function(){return n}})},94214:(e,t,n)=>{"use strict";Object.defineProperty(t,"j",{enumerable:!0,get:function(){return i}});let r=n(48278);function i(e,t){return(0,r.registerServerReference)(t,e,null)}},46221:(e,t,n)=>{"use strict";var r=n(69757),i={stream:!0},o=new Map;function a(e){var t=globalThis.__next_require__(e);return"function"!=typeof t.then||"fulfilled"===t.status?null:(t.then(function(e){t.status="fulfilled",t.value=e},function(e){t.status="rejected",t.reason=e}),t)}function u(){}var s=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.Dispatcher,l=Symbol.for("react.element"),c=Symbol.for("react.lazy"),f=Symbol.iterator,d=Array.isArray,h=Object.getPrototypeOf,p=Object.prototype,y=new WeakMap;function g(e,t,n,r){var i=1,o=0,a=null;e=JSON.stringify(e,function e(u,s){if(null===s)return null;if("object"==typeof s){if("function"==typeof s.then){null===a&&(a=new FormData),o++;var l,c,g=i++;return s.then(function(r){r=JSON.stringify(r,e);var i=a;i.append(t+g,r),0==--o&&n(i)},function(e){r(e)}),"$@"+g.toString(16)}if(d(s))return s;if(s instanceof FormData){null===a&&(a=new FormData);var m=a,v=t+(u=i++)+"_";return s.forEach(function(e,t){m.append(v+t,e)}),"$K"+u.toString(16)}if(s instanceof Map)return s=JSON.stringify(Array.from(s),e),null===a&&(a=new FormData),u=i++,a.append(t+u,s),"$Q"+u.toString(16);if(s instanceof Set)return s=JSON.stringify(Array.from(s),e),null===a&&(a=new FormData),u=i++,a.append(t+u,s),"$W"+u.toString(16);if(null===(c=s)||"object"!=typeof c?null:"function"==typeof(c=f&&c[f]||c["@@iterator"])?c:null)return Array.from(s);if((u=h(s))!==p&&(null===u||null!==h(u)))throw Error("Only plain objects, and a few built-ins, can be passed to Server Actions. Classes or null prototypes are not supported.");return s}if("string"==typeof s)return"Z"===s[s.length-1]&&this[u]instanceof Date?"$D"+s:s="$"===s[0]?"$"+s:s;if("boolean"==typeof s)return s;if("number"==typeof s)return Number.isFinite(l=s)?0===l&&-1/0==1/l?"$-0":l:1/0===l?"$Infinity":-1/0===l?"$-Infinity":"$NaN";if(void 0===s)return"$undefined";if("function"==typeof s){if(void 0!==(s=y.get(s)))return s=JSON.stringify(s,e),null===a&&(a=new FormData),u=i++,a.set(t+u,s),"$F"+u.toString(16);throw Error("Client Functions cannot be passed directly to Server Functions. Only Functions passed from the Server can be passed back again.")}if("symbol"==typeof s){if(Symbol.for(u=s.description)!==s)throw Error("Only global symbols received from Symbol.for(...) can be passed to Server Functions. The symbol Symbol.for("+s.description+") cannot be found among global symbols.");return"$S"+u}if("bigint"==typeof s)return"$n"+s.toString(10);throw Error("Type "+typeof s+" is not supported as an argument to a Server Function.")}),null===a?n(e):(a.set(t+"0",e),0===o&&n(a))}var m=new WeakMap;function v(e){var t=y.get(this);if(!t)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");var n=null;if(null!==t.bound){if((n=m.get(t))||(r=t,a=new Promise(function(e,t){i=e,o=t}),g(r,"",function(e){if("string"==typeof e){var t=new FormData;t.append("0",e),e=t}a.status="fulfilled",a.value=e,i(e)},function(e){a.status="rejected",a.reason=e,o(e)}),n=a,m.set(t,n)),"rejected"===n.status)throw n.reason;if("fulfilled"!==n.status)throw n;t=n.value;var r,i,o,a,u=new FormData;t.forEach(function(t,n){u.append("$ACTION_"+e+":"+n,t)}),n=u,t="$ACTION_REF_"+e}else t="$ACTION_ID_"+t.id;return{name:t,method:"POST",encType:"multipart/form-data",data:n}}function b(e,t){var n=y.get(this);if(!n)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");if(n.id!==e)return!1;var r=n.bound;if(null===r)return 0===t;switch(r.status){case"fulfilled":return r.value.length===t;case"pending":throw r;case"rejected":throw r.reason;default:throw"string"!=typeof r.status&&(r.status="pending",r.then(function(e){r.status="fulfilled",r.value=e},function(e){r.status="rejected",r.reason=e})),r}}function w(e,t,n){Object.defineProperties(e,{$$FORM_ACTION:{value:void 0===n?v:function(){var e=y.get(this);if(!e)throw Error("Tried to encode a Server Action from a different instance than the encoder is from. This is a bug in React.");var t=e.bound;return null===t&&(t=Promise.resolve([])),n(e.id,t)}},$$IS_SIGNATURE_EQUAL:{value:b},bind:{value:T}}),y.set(e,t)}var S=Function.prototype.bind,_=Array.prototype.slice;function T(){var e=S.apply(this,arguments),t=y.get(this);if(t){var n=_.call(arguments,1),r=null;r=null!==t.bound?Promise.resolve(t.bound).then(function(e){return e.concat(n)}):Promise.resolve(n),Object.defineProperties(e,{$$FORM_ACTION:{value:this.$$FORM_ACTION},$$IS_SIGNATURE_EQUAL:{value:b},bind:{value:T}}),y.set(e,{id:t.id,bound:r})}return e}function A(e,t,n,r){this.status=e,this.value=t,this.reason=n,this._response=r}function O(e){switch(e.status){case"resolved_model":R(e);break;case"resolved_module":j(e)}switch(e.status){case"fulfilled":return e.value;case"pending":case"blocked":case"cyclic":throw e;default:throw e.reason}}function E(e,t){for(var n=0;n<e.length;n++)(0,e[n])(t)}function D(e,t,n){switch(e.status){case"fulfilled":E(t,e.value);break;case"pending":case"blocked":case"cyclic":e.value=t,e.reason=n;break;case"rejected":n&&E(n,e.reason)}}function N(e,t){if("pending"===e.status||"blocked"===e.status){var n=e.reason;e.status="rejected",e.reason=t,null!==n&&E(n,t)}}function M(e,t){if("pending"===e.status||"blocked"===e.status){var n=e.value,r=e.reason;e.status="resolved_module",e.value=t,null!==n&&(j(e),D(e,n,r))}}A.prototype=Object.create(Promise.prototype),A.prototype.then=function(e,t){switch(this.status){case"resolved_model":R(this);break;case"resolved_module":j(this)}switch(this.status){case"fulfilled":e(this.value);break;case"pending":case"blocked":case"cyclic":e&&(null===this.value&&(this.value=[]),this.value.push(e)),t&&(null===this.reason&&(this.reason=[]),this.reason.push(t));break;default:t(this.reason)}};var k=null,C=null;function R(e){var t=k,n=C;k=e,C=null;var r=e.value;e.status="cyclic",e.value=null,e.reason=null;try{var i=JSON.parse(r,e._response._fromJSON);if(null!==C&&0<C.deps)C.value=i,e.status="blocked",e.value=null,e.reason=null;else{var o=e.value;e.status="fulfilled",e.value=i,null!==o&&E(o,i)}}catch(t){e.status="rejected",e.reason=t}finally{k=t,C=n}}function j(e){try{var t=e.value,n=globalThis.__next_require__(t[0]);if(4===t.length&&"function"==typeof n.then){if("fulfilled"===n.status)n=n.value;else throw n.reason}var r="*"===t[2]?n:""===t[2]?n.__esModule?n.default:n:n[t[2]];e.status="fulfilled",e.value=r}catch(t){e.status="rejected",e.reason=t}}function P(e,t){e._chunks.forEach(function(e){"pending"===e.status&&N(e,t)})}function I(e,t){var n=e._chunks,r=n.get(t);return r||(r=new A("pending",null,null,e),n.set(t,r)),r}function $(e,t){if("resolved_model"===(e=I(e,t)).status&&R(e),"fulfilled"===e.status)return e.value;throw e.reason}function F(){throw Error('Trying to call a function from "use server" but the callServer option was not implemented in your router runtime.')}function L(){throw Error("Server Functions cannot be called during initial render. This would create a fetch waterfall. Try to use a Server Component to pass data to Client Components instead.")}function U(e){var t,n=e.ssrManifest.moduleMap;return(n={_bundlerConfig:n,_moduleLoading:e.ssrManifest.moduleLoading,_callServer:void 0!==L?L:F,_encodeFormAction:e.encodeFormAction,_nonce:e="string"==typeof e.nonce?e.nonce:void 0,_chunks:new Map,_stringDecoder:new TextDecoder,_fromJSON:null,_rowState:0,_rowID:0,_rowTag:0,_rowLength:0,_buffer:[]})._fromJSON=(t=n,function(e,n){return"string"==typeof n?function(e,t,n,r){if("$"===r[0]){if("$"===r)return l;switch(r[1]){case"$":return r.slice(1);case"L":return{$$typeof:c,_payload:e=I(e,t=parseInt(r.slice(2),16)),_init:O};case"@":if(2===r.length)return new Promise(function(){});return I(e,t=parseInt(r.slice(2),16));case"S":return Symbol.for(r.slice(2));case"F":return t=$(e,t=parseInt(r.slice(2),16)),function(e,t){function n(){var e=Array.prototype.slice.call(arguments),n=t.bound;return n?"fulfilled"===n.status?r(t.id,n.value.concat(e)):Promise.resolve(n).then(function(n){return r(t.id,n.concat(e))}):r(t.id,e)}var r=e._callServer;return w(n,t,e._encodeFormAction),n}(e,t);case"Q":return new Map(e=$(e,t=parseInt(r.slice(2),16)));case"W":return new Set(e=$(e,t=parseInt(r.slice(2),16)));case"I":return 1/0;case"-":return"$-0"===r?-0:-1/0;case"N":return NaN;case"u":return;case"D":return new Date(Date.parse(r.slice(2)));case"n":return BigInt(r.slice(2));default:switch((e=I(e,r=parseInt(r.slice(1),16))).status){case"resolved_model":R(e);break;case"resolved_module":j(e)}switch(e.status){case"fulfilled":return e.value;case"pending":case"blocked":case"cyclic":var i;return r=k,e.then(function(e,t,n,r){if(C){var i=C;r||i.deps++}else i=C={deps:r?0:1,value:null};return function(r){t[n]=r,i.deps--,0===i.deps&&"blocked"===e.status&&(r=e.value,e.status="fulfilled",e.value=i.value,null!==r&&E(r,i.value))}}(r,t,n,"cyclic"===e.status),(i=r,function(e){return N(i,e)})),null;default:throw e.reason}}}return r}(t,this,e,n):"object"==typeof n&&null!==n?e=n[0]===l?{$$typeof:l,type:n[1],key:n[2],ref:null,props:n[3],_owner:null}:n:n}),n}function q(e,t){function r(t){P(e,t)}var l=t.getReader();l.read().then(function t(c){var f=c.value;if(c.done)P(e,Error("Connection closed."));else{var d=0,h=e._rowState,p=e._rowID,y=e._rowTag,g=e._rowLength;c=e._buffer;for(var m=f.length;d<m;){var v=-1;switch(h){case 0:58===(v=f[d++])?h=1:p=p<<4|(96<v?v-87:v-48);continue;case 1:84===(h=f[d])?(y=h,h=2,d++):64<h&&91>h?(y=h,h=3,d++):(y=0,h=3);continue;case 2:44===(v=f[d++])?h=4:g=g<<4|(96<v?v-87:v-48);continue;case 3:v=f.indexOf(10,d);break;case 4:(v=d+g)>f.length&&(v=-1)}var b=f.byteOffset+d;if(-1<v){d=new Uint8Array(f.buffer,b,v-d),g=e,b=y;var w=g._stringDecoder;y="";for(var S=0;S<c.length;S++)y+=w.decode(c[S],i);switch(y+=w.decode(d),b){case 73:!function(e,t,r){var i=e._chunks,l=i.get(t);r=JSON.parse(r,e._fromJSON);var c=function(e,t){if(e){var n=e[t[0]];if(e=n[t[2]])n=e.name;else{if(!(e=n["*"]))throw Error('Could not find the module "'+t[0]+'" in the React SSR Manifest. This is probably a bug in the React Server Components bundler.');n=t[2]}return 4===t.length?[e.id,e.chunks,n,1]:[e.id,e.chunks,n]}return t}(e._bundlerConfig,r);if(function(e,t,n){if(null!==e)for(var r=1;r<t.length;r+=2){var i=s.current;if(i){var o=i.preinitScript,a=e.prefix+t[r],u=e.crossOrigin;u="string"==typeof u?"use-credentials"===u?u:"":void 0,o.call(i,a,{crossOrigin:u,nonce:n})}}}(e._moduleLoading,r[1],e._nonce),r=function(e){for(var t=e[1],r=[],i=0;i<t.length;){var s=t[i++];t[i++];var l=o.get(s);if(void 0===l){l=n.e(s),r.push(l);var c=o.set.bind(o,s,null);l.then(c,u),o.set(s,l)}else null!==l&&r.push(l)}return 4===e.length?0===r.length?a(e[0]):Promise.all(r).then(function(){return a(e[0])}):0<r.length?Promise.all(r):null}(c)){if(l){var f=l;f.status="blocked"}else f=new A("blocked",null,null,e),i.set(t,f);r.then(function(){return M(f,c)},function(e){return N(f,e)})}else l?M(l,c):i.set(t,new A("resolved_module",c,null,e))}(g,p,y);break;case 72:if(p=y[0],g=JSON.parse(y=y.slice(1),g._fromJSON),y=s.current)switch(p){case"D":y.prefetchDNS(g);break;case"C":"string"==typeof g?y.preconnect(g):y.preconnect(g[0],g[1]);break;case"L":p=g[0],d=g[1],3===g.length?y.preload(p,d,g[2]):y.preload(p,d);break;case"m":"string"==typeof g?y.preloadModule(g):y.preloadModule(g[0],g[1]);break;case"S":"string"==typeof g?y.preinitStyle(g):y.preinitStyle(g[0],0===g[1]?void 0:g[1],3===g.length?g[2]:void 0);break;case"X":"string"==typeof g?y.preinitScript(g):y.preinitScript(g[0],g[1]);break;case"M":"string"==typeof g?y.preinitModuleScript(g):y.preinitModuleScript(g[0],g[1])}break;case 69:d=(y=JSON.parse(y)).digest,(y=Error("An error occurred in the Server Components render. The specific message is omitted in production builds to avoid leaking sensitive details. A digest property is included on this error instance which may provide additional details about the nature of the error.")).stack="Error: "+y.message,y.digest=d,(b=(d=g._chunks).get(p))?N(b,y):d.set(p,new A("rejected",null,y,g));break;case 84:g._chunks.set(p,new A("fulfilled",y,null,g));break;case 68:case 87:throw Error("Failed to read a RSC payload created by a development version of React on the server while using a production version on the client. Always use matching versions on the server and the client.");default:(b=(d=g._chunks).get(p))?(g=b,p=y,"pending"===g.status&&(y=g.value,d=g.reason,g.status="resolved_model",g.value=p,null!==y&&(R(g),D(g,y,d)))):d.set(p,new A("resolved_model",y,null,g))}d=v,3===h&&d++,g=p=y=h=0,c.length=0}else{f=new Uint8Array(f.buffer,b,f.byteLength-d),c.push(f),g-=f.byteLength;break}}return e._rowState=h,e._rowID=p,e._rowTag=y,e._rowLength=g,l.read().then(t).catch(r)}}).catch(r)}t.createFromFetch=function(e,t){var n=U(t);return e.then(function(e){q(n,e.body)},function(e){P(n,e)}),I(n,0)},t.createFromReadableStream=function(e,t){return q(t=U(t),e),I(t,0)},t.createServerReference=function(e){return function(e,t,n){function r(){var n=Array.prototype.slice.call(arguments);return t(e,n)}return w(r,{id:e,bound:null},n),r}(e,L)},t.encodeReply=function(e){return new Promise(function(t,n){g(e,"",t,n)})}},31030:(e,t,n)=>{"use strict";e.exports=n(46221)},87594:()=>{},47245:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"DetachedPromise",{enumerable:!0,get:function(){return n}});class n{constructor(){let e,t;this.promise=new Promise((n,r)=>{e=n,t=r}),this.resolve=e,this.reject=t}}},90078:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{atLeastOneTask:function(){return i},scheduleImmediate:function(){return r},scheduleOnNextTick:function(){return n}});let n=e=>{Promise.resolve().then(()=>{process.nextTick(e)})},r=e=>{setImmediate(e)};function i(){return new Promise(e=>r(e))}},30887:(e,t)=>{"use strict";let n,r;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{arrayBufferToString:function(){return o},decrypt:function(){return s},encrypt:function(){return u},generateEncryptionKeyBase64:function(){return l},getActionEncryptionKey:function(){return p},getClientReferenceManifestSingleton:function(){return h},getServerModuleMap:function(){return d},setReferenceManifestsSingleton:function(){return f},stringToUint8Array:function(){return a}});let i=null;function o(e){let t=new Uint8Array(e),n=t.byteLength;if(n<65535)return String.fromCharCode.apply(null,t);let r="";for(let e=0;e<n;e++)r+=String.fromCharCode(t[e]);return r}function a(e){let t=e.length,n=new Uint8Array(t);for(let r=0;r<t;r++)n[r]=e.charCodeAt(r);return n}function u(e,t,n){return crypto.subtle.encrypt({name:"AES-GCM",iv:t},e,n)}function s(e,t,n){return crypto.subtle.decrypt({name:"AES-GCM",iv:t},e,n)}async function l(e){if(e&&void 0!==r)return r;i||(i=new Promise(async(e,t)=>{try{let t=await crypto.subtle.generateKey({name:"AES-GCM",length:256},!0,["encrypt","decrypt"]),n=await crypto.subtle.exportKey("raw",t),r=btoa(o(n));e([t,r])}catch(e){t(e)}}));let[t,a]=await i;return n=t,e&&(r=a),a}let c=Symbol.for("next.server.action-manifests");function f({clientReferenceManifest:e,serverActionsManifest:t,serverModuleMap:n}){globalThis[c]={clientReferenceManifest:e,serverActionsManifest:t,serverModuleMap:n}}function d(){let e=globalThis[c];if(!e)throw Error("Missing manifest for Server Actions. This is a bug in Next.js");return e.serverModuleMap}function h(){let e=globalThis[c];if(!e)throw Error("Missing manifest for Server Actions. This is a bug in Next.js");return e.clientReferenceManifest}async function p(){if(n)return n;let e=globalThis[c];if(!e)throw Error("Missing manifest for Server Actions. This is a bug in Next.js");let t=process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY||e.serverActionsManifest.encryptionKey;if(void 0===t)throw Error("Missing encryption key for Server Actions");return n=await crypto.subtle.importKey("raw",a(atob(t)),"AES-GCM",!0,["encrypt","decrypt"])}},84674:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{decryptActionBoundArgs:function(){return d},encryptActionBoundArgs:function(){return f}}),n(87594);let r=n(48278),i=n(31030),o=n(36260),a=n(30887),u=new TextEncoder,s=new TextDecoder;async function l(e,t){let n=await (0,a.getActionEncryptionKey)();if(void 0===n)throw Error("Missing encryption key for Server Action. This is a bug in Next.js");let r=atob(t),i=r.slice(0,16),o=r.slice(16),u=s.decode(await (0,a.decrypt)(n,(0,a.stringToUint8Array)(i),(0,a.stringToUint8Array)(o)));if(!u.startsWith(e))throw Error("Invalid Server Action payload: failed to decrypt.");return u.slice(e.length)}async function c(e,t){let n=await (0,a.getActionEncryptionKey)();if(void 0===n)throw Error("Missing encryption key for Server Action. This is a bug in Next.js");let r=new Uint8Array(16);crypto.getRandomValues(r);let i=(0,a.arrayBufferToString)(r.buffer),o=await (0,a.encrypt)(n,r,u.encode(e+t));return btoa(i+(0,a.arrayBufferToString)(o))}async function f(e,t){let n=(0,a.getClientReferenceManifestSingleton)(),i=await (0,o.streamToString)((0,r.renderToReadableStream)(t,n.clientModules));return await c(e,i)}async function d(e,t){let n=await l(e,await t),o=await (0,i.createFromReadableStream)(new ReadableStream({start(e){e.enqueue(u.encode(n)),e.close()}}),{ssrManifest:{moduleLoading:{},moduleMap:{}}}),s=(0,a.getServerModuleMap)();return await (0,r.decodeReply)(await (0,i.encodeReply)(o),s)}},43976:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ENCODED_TAGS",{enumerable:!0,get:function(){return n}});let n={OPENING:{HTML:new Uint8Array([60,104,116,109,108]),BODY:new Uint8Array([60,98,111,100,121])},CLOSED:{HEAD:new Uint8Array([60,47,104,101,97,100,62]),BODY:new Uint8Array([60,47,98,111,100,121,62]),HTML:new Uint8Array([60,47,104,116,109,108,62]),BODY_AND_HTML:new Uint8Array([60,47,98,111,100,121,62,60,47,104,116,109,108,62])}}},36260:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{chainStreams:function(){return f},continueDynamicDataResume:function(){return A},continueDynamicHTMLResume:function(){return T},continueDynamicPrerender:function(){return S},continueFizzStream:function(){return w},continueStaticPrerender:function(){return _},createBufferedTransformStream:function(){return p},createRootLayoutValidatorStream:function(){return b},renderToInitialFizzStream:function(){return y},streamFromString:function(){return d},streamToString:function(){return h}});let r=n(79929),i=n(93550),o=n(47245),a=n(90078),u=n(43976),s=n(38139);function l(){}let c=new TextEncoder;function f(...e){if(0===e.length)throw Error("Invariant: chainStreams requires at least one stream");if(1===e.length)return e[0];let{readable:t,writable:n}=new TransformStream,r=e[0].pipeTo(n,{preventClose:!0}),i=1;for(;i<e.length-1;i++){let t=e[i];r=r.then(()=>t.pipeTo(n,{preventClose:!0}))}let o=e[i];return(r=r.then(()=>o.pipeTo(n))).catch(l),t}function d(e){return new ReadableStream({start(t){t.enqueue(c.encode(e)),t.close()}})}async function h(e){let t=new TextDecoder("utf-8",{fatal:!0}),n="";for await(let r of e)n+=t.decode(r,{stream:!0});return n+t.decode()}function p(){let e,t=[],n=0,r=r=>{if(e)return;let i=new o.DetachedPromise;e=i,(0,a.scheduleImmediate)(()=>{try{let e=new Uint8Array(n),i=0;for(let n=0;n<t.length;n++){let r=t[n];e.set(r,i),i+=r.byteLength}t.length=0,n=0,r.enqueue(e)}catch{}finally{e=void 0,i.resolve()}})};return new TransformStream({transform(e,i){t.push(e),n+=e.byteLength,r(i)},flush(){if(e)return e.promise}})}function y({ReactDOMServer:e,element:t,streamOptions:n}){return(0,r.getTracer)().trace(i.AppRenderSpan.renderToReadableStream,async()=>e.renderToReadableStream(t,n))}function g(e){let t=!1,n=!1,r=!1;return new TransformStream({async transform(i,o){if(r=!0,n){o.enqueue(i);return}let l=await e();if(t){if(l){let e=c.encode(l);o.enqueue(e)}o.enqueue(i),n=!0}else{let e=(0,s.indexOfUint8Array)(i,u.ENCODED_TAGS.CLOSED.HEAD);if(-1!==e){if(l){let t=c.encode(l),n=new Uint8Array(i.length+t.length);n.set(i.slice(0,e)),n.set(t,e),n.set(i.slice(e),e+t.length),o.enqueue(n)}else o.enqueue(i);n=!0,t=!0}}t?(0,a.scheduleImmediate)(()=>{n=!1}):o.enqueue(i)},async flush(t){if(r){let n=await e();n&&t.enqueue(c.encode(n))}}})}function m(e){let t=null,n=!1;async function r(r){if(t)return;let i=e.getReader();await (0,a.atLeastOneTask)();try{for(;;){let{done:e,value:t}=await i.read();if(e){n=!0;return}r.enqueue(t)}}catch(e){r.error(e)}}return new TransformStream({transform(e,n){n.enqueue(e),t||(t=r(n))},flush(e){if(!n)return t||r(e)}})}function v(e){let t=!1,n=c.encode(e);return new TransformStream({transform(r,i){if(t)return i.enqueue(r);let o=(0,s.indexOfUint8Array)(r,n);if(o>-1){if(t=!0,r.length===e.length)return;let n=r.slice(0,o);if(i.enqueue(n),r.length>e.length+o){let t=r.slice(o+e.length);i.enqueue(t)}}else i.enqueue(r)},flush(e){e.enqueue(n)}})}function b(){let e=!1,t=!1;return new TransformStream({async transform(n,r){!e&&(0,s.indexOfUint8Array)(n,u.ENCODED_TAGS.OPENING.HTML)>-1&&(e=!0),!t&&(0,s.indexOfUint8Array)(n,u.ENCODED_TAGS.OPENING.BODY)>-1&&(t=!0),r.enqueue(n)},flush(n){let r=[];e||r.push("html"),t||r.push("body"),r.length&&n.enqueue(c.encode(`<script>self.__next_root_layout_missing_tags=${JSON.stringify(r)}</script>`))}})}async function w(e,{suffix:t,inlinedDataStream:n,isStaticGeneration:r,getServerInsertedHTML:i,serverInsertedHTMLToHead:u,validateRootLayout:s}){let l="</body></html>",f=t?t.split(l,1)[0]:null;return r&&"allReady"in e&&await e.allReady,function(e,t){let n=e;for(let e of t)e&&(n=n.pipeThrough(e));return n}(e,[p(),i&&!u?new TransformStream({transform:async(e,t)=>{let n=await i();n&&t.enqueue(c.encode(n)),t.enqueue(e)}}):null,null!=f&&f.length>0?function(e){let t,n=!1,r=n=>{let r=new o.DetachedPromise;t=r,(0,a.scheduleImmediate)(()=>{try{n.enqueue(c.encode(e))}catch{}finally{t=void 0,r.resolve()}})};return new TransformStream({transform(e,t){t.enqueue(e),n||(n=!0,r(t))},flush(r){if(t)return t.promise;n||r.enqueue(c.encode(e))}})}(f):null,n?m(n):null,s?b():null,v(l),i&&u?g(i):null])}async function S(e,{getServerInsertedHTML:t}){return e.pipeThrough(p()).pipeThrough(new TransformStream({transform(e,t){(0,s.isEquivalentUint8Arrays)(e,u.ENCODED_TAGS.CLOSED.BODY_AND_HTML)||(0,s.isEquivalentUint8Arrays)(e,u.ENCODED_TAGS.CLOSED.BODY)||(0,s.isEquivalentUint8Arrays)(e,u.ENCODED_TAGS.CLOSED.HTML)||(e=(0,s.removeFromUint8Array)(e,u.ENCODED_TAGS.CLOSED.BODY),e=(0,s.removeFromUint8Array)(e,u.ENCODED_TAGS.CLOSED.HTML),t.enqueue(e))}})).pipeThrough(g(t))}async function _(e,{inlinedDataStream:t,getServerInsertedHTML:n}){return e.pipeThrough(p()).pipeThrough(g(n)).pipeThrough(m(t)).pipeThrough(v("</body></html>"))}async function T(e,{inlinedDataStream:t,getServerInsertedHTML:n}){return e.pipeThrough(p()).pipeThrough(g(n)).pipeThrough(m(t)).pipeThrough(v("</body></html>"))}async function A(e,{inlinedDataStream:t}){return e.pipeThrough(m(t)).pipeThrough(v("</body></html>"))}},38139:(e,t)=>{"use strict";function n(e,t){if(0===t.length)return 0;if(0===e.length||t.length>e.length)return -1;for(let n=0;n<=e.length-t.length;n++){let r=!0;for(let i=0;i<t.length;i++)if(e[n+i]!==t[i]){r=!1;break}if(r)return n}return -1}function r(e,t){if(e.length!==t.length)return!1;for(let n=0;n<e.length;n++)if(e[n]!==t[n])return!1;return!0}function i(e,t){let r=n(e,t);if(0===r)return e.subarray(t.length);if(!(r>-1))return e;{let n=new Uint8Array(e.length-t.length);return n.set(e.slice(0,r)),n.set(e.slice(r+t.length),r),n}}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{indexOfUint8Array:function(){return n},isEquivalentUint8Arrays:function(){return r},removeFromUint8Array:function(){return i}})}};