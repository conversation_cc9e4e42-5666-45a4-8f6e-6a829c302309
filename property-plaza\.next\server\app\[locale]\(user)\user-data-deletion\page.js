"use strict";(()=>{var e={};e.id=1155,e.ids=[1155],e.modules={72934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{e.exports=require("assert")},78893:e=>{e.exports=require("buffer")},61282:e=>{e.exports=require("child_process")},84770:e=>{e.exports=require("crypto")},17702:e=>{e.exports=require("events")},92048:e=>{e.exports=require("fs")},32615:e=>{e.exports=require("http")},35240:e=>{e.exports=require("https")},98216:e=>{e.exports=require("net")},19801:e=>{e.exports=require("os")},55315:e=>{e.exports=require("path")},86624:e=>{e.exports=require("querystring")},76162:e=>{e.exports=require("stream")},82452:e=>{e.exports=require("tls")},74175:e=>{e.exports=require("tty")},17360:e=>{e.exports=require("url")},21764:e=>{e.exports=require("util")},71568:e=>{e.exports=require("zlib")},6005:e=>{e.exports=require("node:crypto")},25323:(e,t,r)=>{r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>c,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>p}),r(16853),r(52250),r(7505),r(84448),r(81729),r(90996);var a=r(30170),s=r(45002),o=r(83876),l=r.n(o),i=r(66299),n={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);r.d(t,n);let p=["",{children:["[locale]",{children:["(user)",{children:["user-data-deletion",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,16853)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user)\\user-data-deletion\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,52250)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,7505)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user)\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,80603))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,84448)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,81729)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,90996,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user)\\user-data-deletion\\page.tsx"],u="/[locale]/(user)/user-data-deletion/page",c={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/[locale]/(user)/user-data-deletion/page",pathname:"/[locale]/user-data-deletion",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},16853:(e,t,r)=>{r.r(t),r.d(t,{default:()=>y,generateMetadata:()=>P});var a=r(72051),s=r(52845),o=r(29507),l=r(83266),i=r(38785),n=r(79438),p=r(94975);function d({content:e}){return a.jsx(n.Z,{children:a.jsx("article",{className:"prose prose-big max-w-3xl text-seekers-text mb-4",children:a.jsx(p.YI,{value:e.body,components:{block:{h1:({children:e})=>a.jsx("h2",{className:"text-2xl font-semibold text-seekers-text mt-4",children:e}),h2:({children:e})=>a.jsx("h3",{className:"text-xl font-semibold mt-4",children:e}),h3:({children:e})=>a.jsx("h3",{className:"text-lg font-semibold mt-4",children:e}),h4:({children:e})=>a.jsx("h3",{className:"",children:e}),normal:({children:e})=>a.jsx("p",{className:" leading-relaxed mt-2",children:e})},list:{number:({children:e})=>a.jsx("ol",{className:"list-decimal list-inside mt-2",children:e}),bullet:({children:e})=>a.jsx("ul",{className:"list-disc pl-4 mt-2",children:e})}}})})})}var u=r(69385),c=r(695);function m(){let e=(0,u.Z)("seeker");return a.jsx(n.Z,{id:"user-data-deletion",className:"mt-12",children:a.jsx(c.Z,{title:e("userDataDeletion.title")})})}var x=r(92898),h=r(93844);async function P(){let e=await (0,o.Z)("seeker"),t=await (0,l.Z)()||h.DI.defaultLocale,r=process.env.USER_DOMAIN||"https://www.property-plaza.com/";return{title:e("metadata.userDataDeletion.title"),description:e("metadata.userDataDeletion.description"),alternates:{canonical:r+t+x.$U,languages:{id:r+`id${x.$U}`,en:r+`en${x.$U}`,"x-default":r+x.$U.replace("/","")}},openGraph:{title:e("metadata.userDataDeletion.title"),description:e("metadata.userDataDeletion.description"),images:[{url:r+"og.png",width:1200,height:630,alt:"Property Plaza"}],type:"website",url:r+t+x.$U,countryName:"Indonesia",emails:"<EMAIL>",locale:t,alternateLocale:h.DI.locales,siteName:"Property plaza"},applicationName:"Property plaza",twitter:{card:"summary_large_image",title:e("metadata.userDataDeletion.title"),description:e("metadata.userDataDeletion.description"),images:[r+"og.jpg"]},robots:{index:!1,follow:!1,nocache:!1}}}async function y(){let e=(0,s.cookies)();e.get("NEXT_LOCALE")?.value,await (0,o.Z)("seeker");let t=await (0,i.b0)("en");return(0,a.jsxs)(a.Fragment,{children:[a.jsx(m,{}),a.jsx(d,{content:t[0]})]})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[9379,5063,4916,9467,4859,5268,3327,8530,6136,7146,4975,6666,9965,595,2232],()=>r(25323));module.exports=a})();