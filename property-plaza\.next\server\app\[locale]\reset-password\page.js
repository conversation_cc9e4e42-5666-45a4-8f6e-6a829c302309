(()=>{var e={};e.id=5097,e.ids=[5097],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},3941:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d}),r(29375),r(84448),r(81729),r(90996);var s=r(30170),a=r(45002),i=r(83876),l=r.n(i),o=r(66299),n={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);r.d(t,n);let d=["",{children:["[locale]",{children:["reset-password",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,29375)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\reset-password\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,80603))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,84448)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,81729)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,90996,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\reset-password\\page.tsx"],u="/[locale]/reset-password/page",m={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/[locale]/reset-password/page",pathname:"/[locale]/reset-password",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},79389:(e,t,r)=>{Promise.resolve().then(r.bind(r,41822)),Promise.resolve().then(r.bind(r,26793)),Promise.resolve().then(r.bind(r,70697))},84244:(e,t,r)=>{"use strict";r.d(t,{E:()=>o,i:()=>l});var s=r(16718),a=r(84879),i=r(47751);let l=/^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[\W_])[A-Za-z\d\W_]{8,}$/;function o(){let e=(0,a.useTranslations)("seeker");return i.z.object({firstName:i.z.string().min(s.nM,{message:e("form.utility.minimumLength",{field:e("form.field.firstName"),length:s.nM})}).max(s.ac,{message:e("form.utility.maximumLength",{field:e("form.field.firstName"),length:s.ac})}),lastName:i.z.string().min(s.nM,{message:e("form.utility.minimumLength",{field:e("form.field.lastName"),length:s.nM})}).max(s.ac,{message:e("form.utility.maximumLength",{field:e("form.field.lastName"),length:s.ac})}),contact:i.z.string().email({message:e("form.utility.enterValidField",{field:` ${e("form.field.email")}`})}),password:i.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.password")})}),confirmPassword:i.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.confirmPassword")})})}).refine(e=>e.password==e.confirmPassword,{message:e("form.utility.fieldNotMatch",{field:`${e("form.field.password")}`}),path:["confirmPassword"]})}},41822:(e,t,r)=>{"use strict";r.d(t,{default:()=>N});var s=r(97247),a=r(34178),i=r(10906),l=r(84879),o=r(16718),n=r(47751),d=r(84244),c=r(6649),u=r(88111);r(28964);var m=r(2704),p=r(34631),f=r(52208),x=r(82328),h=r(58053),g=r(40896),w=r(80818);function v({email:e,token:t,isSeeker:r=!0}){let a=(0,l.useTranslations)("universal"),{removeQueryParam:v}=(0,g.Z)(),{toast:b}=(0,i.pm)(),y=(0,w.useRouter)(),P=function(){let e=(0,l.useTranslations)("universal");return n.z.object({password:n.z.string().min(1,{message:e("form.utility.fieldRequired",{field:e("form.field.password")})}).min(o.Z9,{message:e("form.utility.minimumLength",{length:o.Z9,field:e("form.field.password")})}).refine(e=>d.i.test(e),{message:e("form.utility.passwordWeak")}),confirmPassword:n.z.string().min(1,{message:e("form.utility.fieldRequired",{field:e("form.field.confirmPassword")})})}).refine(e=>e.password==e.confirmPassword,{message:e("form.utility.fieldNotMatch",{field:`${e("form.field.password")} ${e("conjuntion.and")} ${e("form.field.confirmPassword")}`}),path:["confirmPassword"]})}(),j=(0,u.D)({mutationFn:e=>(0,c.PQ)(e)});(0,u.D)({mutationFn:e=>(0,c.AS)(e)});let N=(0,m.cI)({resolver:(0,p.F)(P),defaultValues:{password:"",confirmPassword:""}});async function z(r){let s={email:e,token:t,password:r.password,confirm_password:r.confirmPassword};try{await j.mutateAsync(s)}catch(e){b({title:a("error.resetPassword.title"),description:e.response.data.message,variant:"destructive"})}y.push("/")}return s.jsx(f.l0,{...N,children:(0,s.jsxs)("form",{onSubmit:N.handleSubmit(z),className:"space-y-8",children:[s.jsx("div",{className:"space-y-2 text-center",children:s.jsx("h1",{className:"text-2xl font-semibold text-center",children:a("form.title.resetPassword")})}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx(x.Z,{form:N,name:"password",label:a("form.label.password"),placeholder:a("form.placeholder.basePlaceholder",{field:`${a("form.field.password")}`})}),s.jsx(x.Z,{form:N,name:"confirmPassword",label:a("form.label.confirmPassword"),placeholder:a("form.placeholder.basePlaceholder",{field:`${a("form.field.confirmPassword")}`})})]}),s.jsx(h.z,{className:"w-full",variant:"default-seekers",loading:j.isPending,children:a("cta.changePassword")})]})})}var b=r(84262),y=r(4955),P=r(37129);function j({isDialog:e,isSeeker:t,onGoBack:r}){let o=(0,l.useTranslations)("universal"),{toast:n}=(0,i.pm)(),d=(0,b.t)(),c=(0,P.N)(),u=(0,a.useRouter)(),x=(0,m.cI)({resolver:(0,p.F)(d),defaultValues:{email:""}});async function g(e){let t={email:e.email};try{await c.mutateAsync(t)}catch(e){n({title:o("error.requestForgetPassword.title"),description:e.response.data.message,variant:"destructive"})}}return s.jsx("div",{className:"w-full space-y-6",children:c.isSuccess?(0,s.jsxs)("div",{className:"flex flex-col gap-6 items-center",children:[(0,s.jsxs)("div",{className:"space-y-2 text-center",children:[s.jsx("h1",{className:"text-2xl font-semibold ",children:o("success.requestForgotPassword.title")}),s.jsx("p",{className:"text-neutral-500",children:o("success.requestForgotPassword.description")})]}),s.jsx(h.z,{variant:"link",onClick:()=>u.push("/"),asChild:!0,children:o("cta.goBack")})]}):s.jsx(f.l0,{...x,children:(0,s.jsxs)("form",{onSubmit:x.handleSubmit(g),className:"space-y-8",children:[(0,s.jsxs)("div",{className:"space-y-2 text-center",children:[s.jsx("h1",{className:"text-2xl font-semibold text-center",children:o("form.title.resetPassword")}),s.jsx("p",{className:"text-neutral-500",children:o("form.description.resetPassword")})]}),s.jsx(y.Z,{type:"email",form:x,name:"email",variant:"float",label:o("form.label.email"),labelClassName:"text-xs text-seekers-text-light font-normal",placeholder:""}),(0,s.jsxs)("div",{className:"space-y-2",children:[t?s.jsx(s.Fragment,{}):s.jsx(h.z,{className:"w-full",variant:"default-seekers",loading:c.isPending,children:o("cta.sendResetPassword")}),e?s.jsx(h.z,{type:"button",className:"w-full text-neutral-600",variant:"link",onClick:()=>r?.(),children:o("cta.goBack")}):s.jsx(h.z,{type:"button",variant:"link",onClick:()=>u.back(),className:"w-full text-neutral-600",children:o("cta.goBack")})]})]})})})}function N(){let e=(0,a.useSearchParams)(),t=e.get("email"),r=e.get("token");return t&&r?s.jsx(v,{email:t,token:r}):s.jsx(j,{})}},84262:(e,t,r)=>{"use strict";r.d(t,{t:()=>i});var s=r(84879),a=r(47751);function i(){let e=(0,s.useTranslations)("universal");return a.z.object({email:a.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.email")})}).email()})}},93572:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l});var s=r(97247),a=r(52208),i=r(25008);function l({children:e,description:t,label:r,containerClassName:l,labelClassName:o,variant:n="default"}){return(0,s.jsxs)("div",{className:"w-full",children:[(0,s.jsxs)(a.xJ,{className:(0,i.cn)("w-full relative","float"==n?"border rounded-xl focus-within:border-neutral-light px-4 py-1 !space-y-0 focus-visible:outline-none focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 disabled:cursor-not-allowed":"",l),onClick:e=>e.stopPropagation(),children:[r&&s.jsx(a.lX,{className:o,children:r}),s.jsx(a.NI,{className:"group relative w-full",children:e}),t&&s.jsx(a.pf,{children:t}),"default"==n&&s.jsx(a.zG,{})]}),"float"==n&&s.jsx(a.zG,{})]})}},4955:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});var s=r(97247),a=r(52208),i=r(70170),l=r(93572),o=r(25008);function n({form:e,label:t,name:r,placeholder:n,description:d,type:c,inputProps:u,children:m,labelClassName:p,containerClassName:f,inputContainer:x,variant:h="default"}){return s.jsx(a.Wi,{control:e.control,name:r,render:({field:e})=>s.jsx(l.Z,{label:t,description:d,labelClassName:(0,o.cn)("float"==h?"absolute -top-2 left-2 px-1 text-xs bg-background z-10":"",p),containerClassName:f,variant:h,children:(0,s.jsxs)("div",{className:(0,o.cn)("flex gap-2 w-full overflow-hidden","float"==h?"":"border rounded-sm focus-within:border-neutral-light",x),children:[s.jsx(i.I,{type:c,placeholder:n,...e,...u,className:(0,o.cn)("border-none focus:outline-none shadow-none focus-visible:ring-0 w-full","float"==h?"px-0":"",u?.className)}),m]})})})}},82328:(e,t,r)=>{"use strict";r.d(t,{Z:()=>m});var s=r(97247),a=r(52208),i=r(70170),l=r(93572),o=r(28964),n=r(58053),d=r(58406),c=r(70457),u=r(25008);function m({form:e,label:t,name:r,placeholder:m,description:p,inputProps:f,labelClassName:x,containerClassName:h,inputContainer:g,variant:w="default"}){let[v,b]=(0,o.useState)(!1);return s.jsx(a.Wi,{control:e.control,name:r,render:({field:e})=>s.jsx(l.Z,{label:t,description:p,labelClassName:(0,u.cn)("float"==w?"absolute -top-2 left-2 px-1 text-xs bg-background z-10":"",x),containerClassName:h,variant:w,children:(0,s.jsxs)("div",{className:(0,u.cn)("flex gap-2 w-full overflow-hidden","float"==w?"":"border rounded-sm focus-within:border-neutral-light",g),children:[s.jsx(i.I,{type:v?"text":"password",placeholder:m,...e,...f,className:(0,u.cn)("border-none focus:outline-none shadow-none focus-visible:ring-0 w-full","float"==w?"px-0":"",f?.className)}),s.jsx(n.z,{size:"icon",variant:"ghost",className:"bg-transparent hover:bg-transparent text-seekers-text-light",type:"button",onClick:e=>{e.preventDefault(),e.stopPropagation(),b(e=>!e)},children:v?s.jsx(d.Z,{className:"w-4 h-4"}):s.jsx(c.Z,{className:"w-4 h-4"})})]})})})}},52208:(e,t,r)=>{"use strict";r.d(t,{NI:()=>h,Wi:()=>u,l0:()=>d,lX:()=>x,pf:()=>g,xJ:()=>f,zG:()=>w});var s=r(97247),a=r(28964),i=r(12341),l=r(2704),o=r(25008),n=r(22394);let d=l.RV,c=a.createContext({}),u=({...e})=>s.jsx(c.Provider,{value:{name:e.name},children:s.jsx(l.Qr,{...e})}),m=()=>{let e=a.useContext(c),t=a.useContext(p),{getFieldState:r,formState:s}=(0,l.Gc)(),i=r(e.name,s);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=t;return{id:o,name:e.name,formItemId:`${o}-form-item`,formDescriptionId:`${o}-form-item-description`,formMessageId:`${o}-form-item-message`,...i}},p=a.createContext({}),f=a.forwardRef(({className:e,...t},r)=>{let i=a.useId();return s.jsx(p.Provider,{value:{id:i},children:s.jsx("div",{ref:r,className:(0,o.cn)("space-y-2",e),...t})})});f.displayName="FormItem";let x=a.forwardRef(({className:e,...t},r)=>{let{error:a,formItemId:i}=m();return s.jsx(n._,{ref:r,className:(0,o.cn)(a&&"text-destructive",e),htmlFor:i,...t})});x.displayName="FormLabel";let h=a.forwardRef(({...e},t)=>{let{error:r,formItemId:a,formDescriptionId:l,formMessageId:o}=m();return s.jsx(i.g7,{ref:t,id:a,"aria-describedby":r?`${l} ${o}`:`${l}`,"aria-invalid":!!r,...e})});h.displayName="FormControl";let g=a.forwardRef(({className:e,...t},r)=>{let{formDescriptionId:a}=m();return s.jsx("p",{ref:r,id:a,className:(0,o.cn)("text-[0.8rem] text-muted-foreground",e),...t})});g.displayName="FormDescription";let w=a.forwardRef(({className:e,children:t,...r},a)=>{let{error:i,formMessageId:l}=m(),n=i?String(i?.message):t;return n?s.jsx("p",{ref:a,id:l,className:(0,o.cn)("text-[0.8rem] font-medium text-destructive",e),...r,children:n}):null});w.displayName="FormMessage"},70170:(e,t,r)=>{"use strict";r.d(t,{I:()=>l});var s=r(97247),a=r(28964),i=r(25008);let l=a.forwardRef(({className:e,type:t,...r},a)=>s.jsx("input",{type:t,className:(0,i.cn)("flex max-sm:h-8 h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-inset focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...r}));l.displayName="Input"},22394:(e,t,r)=>{"use strict";r.d(t,{_:()=>d});var s=r(97247),a=r(28964),i=r(40768),l=r(87972),o=r(25008);let n=(0,l.j)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef(({className:e,...t},r)=>s.jsx(i.f,{ref:r,className:(0,o.cn)(n(),e),...t}));d.displayName=i.f.displayName},37129:(e,t,r)=>{"use strict";r.d(t,{N:()=>i});var s=r(6649),a=r(88111);function i(){return(0,a.D)({mutationFn:e=>(0,s.vJ)(e)})}},74993:(e,t,r)=>{"use strict";r.d(t,{apiClient:()=>n,v:()=>d});var s=r(16718),a=r(10863),i=r(72266),l=r(35240);let o=new(r.n(l)()).Agent({rejectUnauthorized:!1}),n=a.Z.create({baseURL:"https://dev.property-plaza.id/api/v1",headers:{Authorization:i.Z.get(s.LA)?"Bearer "+i.Z.get(s.LA):""},httpsAgent:o}),d=a.Z.create({baseURL:"/api/",httpsAgent:o})},87721:(e,t,r)=>{"use strict";r.d(t,{AS:()=>d,Af:()=>p,Ew:()=>m,PQ:()=>c,kS:()=>i,rb:()=>u,u8:()=>l,vJ:()=>n,x4:()=>a,zl:()=>o});var s=r(74993);let a=(e,t)=>s.apiClient.post("auth/login",e,{headers:{"g-token":t||""}}),i=()=>s.apiClient.post("auth/logout"),l=e=>s.apiClient.post("notifications/email",e),o=e=>s.apiClient.post("auth/otp-verification",e),n=e=>s.apiClient.post("auth/forgot-password",e),d=e=>s.apiClient.get(`auth/verify-reset-password?email=${e.email}&token=${e.token}`),c=e=>s.apiClient.post("auth/reset-password",e),u=(e,t)=>s.apiClient.post("auth/create-password",e,t),m=e=>s.apiClient.post("users/security",e),p=e=>s.apiClient.post("auth/totp-verification",e)},6649:(e,t,r)=>{"use strict";r.d(t,{AS:()=>s.AS,Af:()=>s.Af,Ew:()=>s.Ew,PQ:()=>s.PQ,kS:()=>s.kS,rb:()=>s.rb,u8:()=>s.u8,vJ:()=>s.vJ,x4:()=>s.x4,zl:()=>s.zl});var s=r(87721)},40896:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l});var s=r(34178),a=r(28964),i=r(80818);function l(){let e=(0,i.useRouter)(),t=(0,s.usePathname)(),r=(0,s.useSearchParams)(),l=(0,a.useCallback)(s=>{let a=new URLSearchParams(r.toString());s.forEach(e=>a.set(e.name,e.value)),e.push(t+"?"+a.toString())},[r,e,t]),o=(0,a.useCallback)((e,t)=>{let s=new URLSearchParams(r.toString());return s.set(e,t),s.toString()},[r]);return{searchParams:r,createQueryString:(s,a)=>{let i=new URLSearchParams(r.toString());i.set(s,a),e.push(t+"?"+i.toString())},generateQueryString:o,removeQueryParam:(t,s)=>{let a=new URLSearchParams(r.toString());t.forEach(e=>{a.delete(e)});let i=`${window.location.pathname}?${a.toString()}`;if(s)return window.location.href=i;e.push(i)},createMultipleQueryString:l,pathname:t,updateQuery:(s,a)=>{let i=new URLSearchParams(r.toString());i.set(s,a),e.push(t+"?"+i.toString())}}}},29375:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o,generateMetadata:()=>l});var s=r(72051),a=r(29507);let i=(0,r(45347).createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\app\[locale]\reset-password\content.tsx#default`);async function l({params:e,searchParams:t}){let r=await (0,a.Z)("seeker");return{title:r("metadata.rootLayout.title"),description:r("metadata.rootLayout.description"),alternates:{languages:{id:process.env.USER_DOMAIN+"/id",en:process.env.USER_DOMAIN+"/en","x-default":process.env.USER_DOMAIN+"/en"},canonical:{url:"https://property-plaza.com"}},robots:{index:!1,follow:!1}}}function o(){return s.jsx("div",{className:"container flex items-center justify-center min-h-screen py-10",children:s.jsx("div",{className:"max-w-sm max-sm:p-4",children:s.jsx(i,{})})})}},29507:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l});var s=r(26269),a=r(95817),i=r(60434),l=(0,s.cache)(async function(e){let t,r;"string"==typeof e?t=e:e&&(r=e.locale,t=e.namespace);let s=await (0,i.Z)(r);return(0,a.eX)({...s,namespace:t,messages:s.messages})})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[9379,5063,4916,9467,6666],()=>r(3941));module.exports=s})();