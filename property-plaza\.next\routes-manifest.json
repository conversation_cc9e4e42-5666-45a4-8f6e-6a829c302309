{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/[locale]", "regex": "^/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)(?:/)?$"}, {"page": "/[locale]/about-us", "regex": "^/([^/]+?)/about\\-us(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/about\\-us(?:/)?$"}, {"page": "/[locale]/billing", "regex": "^/([^/]+?)/billing(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/billing(?:/)?$"}, {"page": "/[locale]/contact-us", "regex": "^/([^/]+?)/contact\\-us(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/contact\\-us(?:/)?$"}, {"page": "/[locale]/create-password", "regex": "^/([^/]+?)/create\\-password(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/create\\-password(?:/)?$"}, {"page": "/[locale]/favorites", "regex": "^/([^/]+?)/favorites(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/favorites(?:/)?$"}, {"page": "/[locale]/icon.ico", "regex": "^/([^/]+?)/icon\\.ico(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/icon\\.ico(?:/)?$"}, {"page": "/[locale]/message", "regex": "^/([^/]+?)/message(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/message(?:/)?$"}, {"page": "/[locale]/notification", "regex": "^/([^/]+?)/notification(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/notification(?:/)?$"}, {"page": "/[locale]/plan", "regex": "^/([^/]+?)/plan(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/plan(?:/)?$"}, {"page": "/[locale]/posts/[slug]", "regex": "^/([^/]+?)/posts/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPslug": "nxtPslug"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/posts/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/[locale]/privacy-policy", "regex": "^/([^/]+?)/privacy\\-policy(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/privacy\\-policy(?:/)?$"}, {"page": "/[locale]/profile", "regex": "^/([^/]+?)/profile(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/profile(?:/)?$"}, {"page": "/[locale]/reset-password", "regex": "^/([^/]+?)/reset\\-password(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/reset\\-password(?:/)?$"}, {"page": "/[locale]/s/[query]", "regex": "^/([^/]+?)/s/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPquery": "nxtPquery"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/s/(?<nxtPquery>[^/]+?)(?:/)?$"}, {"page": "/[locale]/security", "regex": "^/([^/]+?)/security(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/security(?:/)?$"}, {"page": "/[locale]/stripe-test", "regex": "^/([^/]+?)/stripe\\-test(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/stripe\\-test(?:/)?$"}, {"page": "/[locale]/subscription", "regex": "^/([^/]+?)/subscription(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/subscription(?:/)?$"}, {"page": "/[locale]/terms-of-use", "regex": "^/([^/]+?)/terms\\-of\\-use(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/terms\\-of\\-use(?:/)?$"}, {"page": "/[locale]/user-data-deletion", "regex": "^/([^/]+?)/user\\-data\\-deletion(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/user\\-data\\-deletion(?:/)?$"}, {"page": "/[locale]/verify", "regex": "^/([^/]+?)/verify(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/verify(?:/)?$"}, {"page": "/[locale]/verify/success", "regex": "^/([^/]+?)/verify/success(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/verify/success(?:/)?$"}, {"page": "/[locale]/[title]", "regex": "^/([^/]+?)/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPtitle": "nxtPtitle"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/(?<nxtPtitle>[^/]+?)(?:/)?$"}], "staticRoutes": [{"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/sitemap.xml", "regex": "^/sitemap\\.xml(?:/)?$", "routeKeys": {}, "namedRegex": "^/sitemap\\.xml(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}