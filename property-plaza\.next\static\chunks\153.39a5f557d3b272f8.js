"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[153],{10153:function(e,n,t){t.r(n),t.d(n,{default:function(){return s}});var r=t(99376),u=t(2265);function s(){let e=(0,r.useRouter)();return(0,u.useEffect)(()=>{let n=new AbortController,{signal:t}=n;return window.addEventListener("online",()=>e.refresh(),{passive:!0,signal:t}),()=>n.abort()},[e]),null}s.displayName="RefreshOnReconnect"}}]);