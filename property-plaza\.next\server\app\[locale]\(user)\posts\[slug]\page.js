(()=>{var e={};e.id=3979,e.ids=[3979],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},86624:e=>{"use strict";e.exports=require("querystring")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},6005:e=>{"use strict";e.exports=require("node:crypto")},18686:(e,A,t)=>{"use strict";t.r(A),t.d(A,{GlobalError:()=>l.a,__next_app__:()=>u,originalPathname:()=>c,pages:()=>d,routeModule:()=>p,tree:()=>o}),t(5361),t(52250),t(7505),t(84448),t(81729),t(90996);var s=t(30170),a=t(45002),r=t(83876),l=t.n(r),i=t(66299),n={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);t.d(A,n);let o=["",{children:["[locale]",{children:["(user)",{children:["posts",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,5361)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user)\\posts\\[slug]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,52250)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,7505)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user)\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,80603))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,84448)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,81729)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,90996,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user)\\posts\\[slug]\\page.tsx"],c="/[locale]/(user)/posts/[slug]/page",u={require:t,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/[locale]/(user)/posts/[slug]/page",pathname:"/[locale]/posts/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},73814:(e,A,t)=>{let s={ace39bf07124e0ba39e8486050a53bc79b0621b3:()=>Promise.resolve().then(t.bind(t,18714)).then(e=>e.default),"21a7b89139c1ae9f09080bde8b72017631c6bb15":()=>Promise.resolve().then(t.bind(t,54750)).then(e=>e.revalidateSyncTags),"6ebeb7e19dda4f3641cdd24f3ee5eadb0810a726":()=>Promise.resolve().then(t.bind(t,54750)).then(e=>e.setPerspectiveCookie),"23977280e679cbd5490718534d869d8b006b3dfa":()=>Promise.resolve().then(t.bind(t,8466)).then(e=>e.revalidateRootLayout)};async function a(e,...A){return(await s[e]()).apply(null,A)}e.exports={ace39bf07124e0ba39e8486050a53bc79b0621b3:a.bind(null,"ace39bf07124e0ba39e8486050a53bc79b0621b3"),"21a7b89139c1ae9f09080bde8b72017631c6bb15":a.bind(null,"21a7b89139c1ae9f09080bde8b72017631c6bb15"),"6ebeb7e19dda4f3641cdd24f3ee5eadb0810a726":a.bind(null,"6ebeb7e19dda4f3641cdd24f3ee5eadb0810a726"),"23977280e679cbd5490718534d869d8b006b3dfa":a.bind(null,"23977280e679cbd5490718534d869d8b006b3dfa")}},75588:(e,A,t)=>{Promise.resolve().then(t.bind(t,38819)),Promise.resolve().then(t.bind(t,81578)),Promise.resolve().then(t.bind(t,84059)),Promise.resolve().then(t.bind(t,78781)),Promise.resolve().then(t.bind(t,91860)),Promise.resolve().then(t.bind(t,33626)),Promise.resolve().then(t.bind(t,26793)),Promise.resolve().then(t.bind(t,70697)),Promise.resolve().then(t.bind(t,92941)),Promise.resolve().then(t.t.bind(t,15889,23)),Promise.resolve().then(t.bind(t,62648))},80895:(e,A,t)=>{Promise.resolve().then(t.bind(t,74997)),Promise.resolve().then(t.bind(t,24638)),Promise.resolve().then(t.bind(t,94968)),Promise.resolve().then(t.bind(t,26793)),Promise.resolve().then(t.bind(t,70697)),Promise.resolve().then(t.bind(t,92941)),Promise.resolve().then(t.bind(t,84716)),Promise.resolve().then(t.t.bind(t,15889,23)),Promise.resolve().then(t.bind(t,84033))},38819:(e,A,t)=>{"use strict";t.d(A,{default:()=>p});var s=t(97247),a=t(75476),r=t(55961),l=t(15238),i=t(50555),n=t(58053),o=t(84879);function d({open:e,setOpen:A,trigger:t}){let d=(0,o.useTranslations)("universal");return(0,s.jsxs)(i.Z,{open:e,setOpen:A,openTrigger:t,children:[s.jsx(l.Z,{children:s.jsx("h3",{className:"text-base font-bold text-seekers-text",children:d("popup.followInstagram.title")})}),s.jsx("div",{children:s.jsx("p",{children:d("popup.followInstagram.description")})}),s.jsx(r.Z,{children:s.jsx(n.z,{asChild:!0,className:"w-full",variant:"default-seekers",children:s.jsx(a.rU,{href:"https://www.instagram.com/join.propertyplaza/",children:d("cta.followUsOnInstagram")})})})]})}var c=t(92199),u=t(28964);function p(){let{successSignUp:e,setSuccessSignUp:A,loading:t}=(0,c.I)(),[a,r]=(0,u.useState)(!1),[l,i]=(0,u.useState)(!0);return s.jsx(s.Fragment,{children:s.jsx(d,{open:a,setOpen:e=>{A(e),r(e)},trigger:s.jsx(s.Fragment,{})})})}},74997:(e,A,t)=>{"use strict";t.d(A,{default:()=>d});var s=t(97247),a=t(18427),r=t(90532),l=t(28556);function i({properties:e,conversions:A}){return(0,s.jsxs)(r.lr,{opts:{align:"end"},children:[s.jsx(r.KI,{className:"w-full h-full -ml-4 -z-20",children:e.map((e,t)=>s.jsx(r.d$,{className:"basis-1/2 lg:basis-1/3 ",children:s.jsx(l.ZP,{data:e,conversion:A,forceLazyloading:!0,maxImage:3},t)},t))}),(0,s.jsxs)("div",{className:"flex absolute    top-[128px] max-sm:-translate-y-1/2  max-sm:left-0    w-full justify-between px-3 max-sm:hidden lg:hidden",children:[s.jsx(r.am,{onClick:e=>e.stopPropagation(),className:"-left-1.5 md:-left-3 transition duration-75 ease-in w-8 h-8 md:w-10 md:h-10 !bg-white/70 border-white-70",iconClassName:"w-6"}),s.jsx(r.Pz,{onClick:e=>e.stopPropagation(),className:"-right-1.5 md:-right-3 transition duration-75 ease-in w-8 h-8 md:w-10 md:h-10 !bg-white/70 border-white-70",iconClassName:"w-6"})]})]})}var n=t(59683),o=t(9190);function d({propertyIds:e,conversions:A,locale:t="en"}){let{firstTimeVisible:r,isVisible:l,sectionRef:d,setFirstTimeVisible:c}=(0,a.Z)(),u=function(e,A,t="en"){return(0,o.a)({queryKey:["batch-listings",...e],queryFn:async()=>await (0,n.f4)(e,t),enabled:A})}(e,l&&r,t);return s.jsx("section",{ref:d,children:s.jsx(i,{properties:u.data?.data||[],conversions:A})})}t(28964)},81578:(e,A,t)=>{"use strict";t.d(A,{default:()=>l});var s=t(97247),a=t(23866),r=t(92894);function l(){let{setSeekers:e,setRole:A}=(0,r.L)(e=>e);return(0,a.l)(),s.jsx(s.Fragment,{})}t(28964)},91897:(e,A,t)=>{"use strict";t.d(A,{O:()=>r});var s=t(97247),a=t(25008);function r({className:e,...A}){return s.jsx("div",{className:(0,a.cn)("animate-pulse rounded-md bg-primary/10",e),...A})}},18427:(e,A,t)=>{"use strict";t.d(A,{Z:()=>a});var s=t(28964);function a(){let[e,A]=(0,s.useState)(!1),[t,a]=(0,s.useState)(!0);return{isVisible:e,sectionRef:(0,s.useRef)(null),firstTimeVisible:t,setFirstTimeVisible:a}}},33918:(e,A,t)=>{"use strict";t.d(A,{Z:()=>s});let s=(0,t(26323).Z)("Bath",[["path",{d:"M9 6 6.5 3.5a1.5 1.5 0 0 0-1-.5C4.683 3 4 3.683 4 4.5V17a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-5",key:"1r8yf5"}],["line",{x1:"10",x2:"8",y1:"5",y2:"7",key:"h5g8z4"}],["line",{x1:"2",x2:"22",y1:"12",y2:"12",key:"1dnqot"}],["line",{x1:"7",x2:"7",y1:"19",y2:"21",key:"16jp00"}],["line",{x1:"17",x2:"17",y1:"19",y2:"21",key:"1pxrnk"}]])},71340:(e,A,t)=>{"use strict";t.d(A,{Z:()=>s});let s=(0,t(26323).Z)("BedDouble",[["path",{d:"M2 20v-8a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v8",key:"1k78r4"}],["path",{d:"M4 10V6a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v4",key:"fb3tl2"}],["path",{d:"M12 4v6",key:"1dcgq2"}],["path",{d:"M2 18h20",key:"ajqnye"}]])},52250:(e,A,t)=>{"use strict";t.r(A),t.d(A,{default:()=>c});var s=t(72051),a=t(81413),r=t(98798),l=t(56886);t(26269);var i=t(35254),n=t(52845);let o=(0,t(45347).createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\app\[locale]\(user)\pop-up.tsx#default`);var d=t(86677);async function c({children:e}){let A=await (0,n.cookies)(),t=A.get("seekers-settings")?.value||"",c=t?JSON.parse(t):void 0,u=A.get("NEXT_LOCALE")?.value;return(0,s.jsxs)(s.Fragment,{children:[s.jsx(d.Z,{isSeeker:!0}),s.jsx(i.Z,{}),s.jsx(r.Z,{}),s.jsx("div",{className:"w-full sticky top-0 z-10 bg-white !mt-0",children:s.jsx(l.Z,{currency_:c?.state?.currency,localeId:u})}),s.jsx("div",{className:"!mt-0 relative min-h-screen max-sm:max-w-screen-sm",children:e}),s.jsx("div",{className:"!mt-0",children:s.jsx(a.Z,{})}),s.jsx(o,{})]})}},7505:(e,A,t)=>{"use strict";t.r(A),t.d(A,{default:()=>a});var s=t(41288);function a(){(0,s.redirect)("/")}},5361:(e,A,t)=>{"use strict";t.r(A),t.d(A,{default:()=>I,generateMetadata:()=>k,revalidate:()=>Q});var s=t(72051),a=t(38785),r=t(94975),l=t(59624),i=t(26767),n=t.n(i),o=t(79438),d=t(69385),c=t(27221);let u={src:"/_next/static/media/about-us-image.267ed102.webp",height:1279,width:1920,blurDataURL:"data:image/webp;base64,UklGRj4AAABXRUJQVlA4IDIAAACQAQCdASoIAAUAAkA4JZgAAudZtgAA/uYL+8l5/VVhaFlCK8//pnoGQ+Tg9E15n5nAAA==",blurWidth:8,blurHeight:5},p=(0,t(86449).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var m=t(93844);function g({posts:e,title:A,locale:t}){return(0,s.jsxs)("section",{className:"p-4 rounded-lg bg-seekers-primary/10 space-y-4 ",children:[s.jsx("p",{className:"font-semibold text-lg",children:A}),e.map((e,A)=>(0,s.jsxs)(m.rU,{className:"text-seekers-primary flex gap-2.5 group",href:e.url,children:[s.jsx(p,{className:"w-3 group-hover:translate-x-2 transition-transform duration-100 ease-linear"}),e.title]},A))]})}let x=(0,t(45347).createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\app\[locale]\(user)\posts\[slug]\property-recommendation.tsx#default`);function h({blogContent:e,conversions:A}){let t=(0,d.Z)("seeker"),i=function(){for(var e=arguments.length,A=Array(e),t=0;t<e;t++)A[t]=arguments[t];return(0,c.Z)("useLocale").locale}();return s.jsx(o.Z,{className:"lg:max-w-screen-sm xl:max-w-screen-md mx-auto min-h-screen space-y-6 py-6 max-sm:pt-0",children:(0,s.jsxs)(s.Fragment,{children:[s.jsx("div",{className:"relative w-full aspect-video rounded-xl overflow-hidden max-sm:rounded-none",children:s.jsx(l.default,{style:{objectFit:"cover"},src:e.mainImage?.asset?.url||u,alt:"",fill:!0})}),(0,s.jsxs)("div",{className:"space-y-3 max-sm:px-4",children:[s.jsx("h1",{className:"text-3xl font-bold text-seekers-text",children:e.title}),(0,s.jsxs)("div",{className:"flex gap-2",children:[s.jsx("div",{className:"aspect-square h-full min-w-10 relative rounded-full overflow-hidden",children:s.jsx(l.default,{src:e.author?.image?.asset?.url||u,fill:!0,alt:"",style:{objectFit:"cover"}})}),(0,s.jsxs)("div",{children:[s.jsx("p",{className:"text-seekers-text",children:e.author.name||t("blog.author.defaultName")}),s.jsx("p",{className:"text-seekers-text-light",children:n()(e.publishedAt).format("MMM DD, YYYY")})]})]})]}),s.jsx("section",{className:" text-seekers-text text-base max-sm:px-4",children:s.jsx(r.YI,{value:e.body,components:{block:{h1:({children:e})=>s.jsx("h2",{className:"text-2xl mb-10 text-seekers-text",children:e}),h2:({children:e})=>s.jsx("h3",{className:"text-xl mt-8 font-bold",children:e}),h3:({children:e})=>s.jsx("h4",{className:"text-lg mt-4 font-bold",children:e}),h4:({children:e})=>s.jsx("h5",{className:"",children:e}),normal:({children:e})=>s.jsx("p",{className:" leading-relaxed my-2",children:e})},list:{number:({children:e})=>s.jsx("ol",{className:"list-decimal list-inside",children:e}),bullet:({children:e})=>s.jsx("ul",{className:"list-disc pl-4",children:e})},types:{image:({value:e})=>{let A=(0,a.t3)(e).width(800).quality(80).url();return(0,s.jsxs)(s.Fragment,{children:[s.jsx("div",{className:"my-6 relative w-full aspect-video",children:s.jsx(l.default,{src:A,alt:e.alt||"Sanity image",className:"rounded-md aspect-video",fill:!0})}),s.jsx("p",{className:"text-xs text-center text-seekers-text-light -mt-5",children:e.caption})]})},"post-recommendation":({value:e})=>s.jsx(g,{title:e.title,locale:i,posts:e.posts}),"property-recommendation":({value:e})=>{let t=e.multiplePropertyId.split(" ");return s.jsx(x,{propertyIds:t,conversions:A,locale:i})}}}})})]})})}function b({moreContent:e}){let A=(0,d.Z)("seeker");return(0,s.jsxs)(o.Z,{className:"xl:max-w-screen-lg space-y-6 pb-6",children:[s.jsx("h2",{className:"text-2xl font-bold text-seekers-text",children:A("blog.moreArticles.title")}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4 gap-4  h-fit",children:e.map(e=>e.mainImage?.asset?.url?s.jsx(y,{slug:e.slug.current,alt:e.title,description:e.metadata,imageUrl:e.mainImage?.asset?.url||"data:image/jpeg;base64,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",publishedAt:e.publishedAt,title:e.title},e._id):s.jsx(s.Fragment,{}))})]})}function y({alt:e,imageUrl:A,publishedAt:t,title:a,slug:r}){return(0,s.jsxs)(m.rU,{href:r,className:"w-full space-y-4 text-seekers-text",children:[s.jsx("div",{className:"relative aspect-[4/3] w-full rounded-xl overflow-hidden",children:s.jsx(l.default,{src:A,alt:e,fill:!0,style:{objectFit:"cover"}})}),(0,s.jsxs)("div",{className:"px-0.5",children:[s.jsx("p",{className:"text-seekers-text-light",children:n()(t).format("DD MMM YYYY")}),s.jsx("h3",{className:"text-base font-semibold",children:a})]})]})}var f=t(41288),j=t(35243),v=t(53189);function w({title:e}){let A=(0,d.Z)("seeker");return s.jsx(o.Z,{className:"xl:max-w-screen-lg mx-auto space-y-6 pt-4 max-sm:pt-0",children:s.jsx(j.aG,{className:"",children:(0,s.jsxs)(j.Jb,{className:"space-x-4 sm:gap-0",children:[s.jsx(j.gN,{className:"text-seekers-text font-medium text-sm",children:(0,s.jsxs)(m.rU,{href:"/",className:"flex gap-2.5 items-center",children:[s.jsx(v.Z,{className:"w-4 h-4",strokeWidth:1}),A("misc.home")]})}),s.jsx(j.bg,{className:"text-seekers-text font-medium text-sm w-3 h-fit text-center",children:"/"}),s.jsx(j.gN,{className:"capitalize text-seekers-text font-medium text-sm",children:e})]})})})}var B=t(70276),N=t(83266),C=t(92898);let Q=3600;async function k({params:e}){let A=await (0,N.Z)(),t=await (0,a.zQ)(e.slug),s=process.env.USER_DOMAIN||"https://www.property-plaza.com/";return{title:t?.title||e.slug.replaceAll("-"," "),description:t?.metadata||"",openGraph:{title:t?.title||e.slug.replaceAll("-"," "),description:t?.metadata||"",images:[{url:t.mainImage.asset.url,width:1200,height:630,alt:t.title}],type:"article",url:s+A+C.o2+"/"+e.slug},alternates:{canonical:s+A+C.o2+"/"+e.slug,languages:{en:s+"en"+C.o2+"/"+e.slug,id:s+"id"+C.o2+"/"+e.slug,"x-default":s+C.o2.replace("/","")+"/"+e.slug}},twitter:{card:"summary_large_image",title:t?.title||e.slug,description:t?.metadata||"",images:[s+"og.jpg"]},robots:{index:!0,follow:!0}}}async function I({params:e}){let A=await (0,a.zQ)(e.slug);if(!A)return(0,f.notFound)();let t=await (0,a.lU)(A?.category?._id||"real-estate",A._id.toString()),r=await (0,B.T)();return(0,s.jsxs)(s.Fragment,{children:[s.jsx(w,{title:A.title}),s.jsx(h,{blogContent:A,conversions:r.data}),s.jsx(b,{moreContent:t})]})}},35254:(e,A,t)=>{"use strict";t.d(A,{Z:()=>s});let s=(0,t(45347).createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\app\[locale]\(user)\setup-seekers.tsx#default`)},35243:(e,A,t)=>{"use strict";t.d(A,{Jb:()=>o,aG:()=>n,bg:()=>c,gN:()=>d});var s=t(72051),a=t(26269),r=t(21322),l=t(37170),i=t(95598);let n=a.forwardRef(({...e},A)=>s.jsx("nav",{ref:A,"aria-label":"breadcrumb",...e}));n.displayName="Breadcrumb";let o=a.forwardRef(({className:e,...A},t)=>s.jsx("ol",{ref:t,className:(0,l.cn)("flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5",e),...A}));o.displayName="BreadcrumbList";let d=a.forwardRef(({className:e,...A},t)=>s.jsx("li",{ref:t,className:(0,l.cn)("inline-flex items-center gap-1.5",e),...A}));d.displayName="BreadcrumbItem",a.forwardRef(({asChild:e,className:A,...t},a)=>{let i=e?r.g7:"a";return s.jsx(i,{ref:a,className:(0,l.cn)("transition-colors hover:text-foreground",A),...t})}).displayName="BreadcrumbLink",a.forwardRef(({className:e,...A},t)=>s.jsx("span",{ref:t,role:"link","aria-disabled":"true","aria-current":"page",className:(0,l.cn)("font-normal text-foreground",e),...A})).displayName="BreadcrumbPage";let c=({children:e,className:A,...t})=>s.jsx("li",{role:"presentation","aria-hidden":"true",className:(0,l.cn)("[&>svg]:w-3.5 [&>svg]:h-3.5",A),...t,children:e??s.jsx(i.XCv,{})});c.displayName="BreadcrumbSeparator"},38785:(e,A,t)=>{"use strict";t.d(A,{g_:()=>y,oK:()=>v,lU:()=>j,zQ:()=>f,YY:()=>B,_b:()=>w,b0:()=>N,t3:()=>h});var s=t(73027),a=t.n(s),r=t(55700);let l={projectId:"r294h68q",dataset:"production",apiVersion:"2024-01-05",useCdn:!1,token:process.env.SANITY_API_KEY,perspective:"published"};var i=t(1601);let n=`{
_id,
  title,
  metadata,
  slug,
  tags,
  author->{
    _id,
    name,
    slug,
    image{asset -> {url}},
    bio
  },
  coverImage{
  asset->{url}},
  mainImage{
  asset->{url}},
  publishedAt,
  category -> {
  _id,
  title
  },
  body
}`;(0,i.Z)`*[_type == "post" && author != "hidden"] ${n}`;let o=(0,i.Z)`*[_type == "post" && author != "hidden"][0...2] ${n}`,d=(0,i.Z)`*[_type == "post" && slug.current == $slug][0]  ${n}
  

`;(0,i.Z)`*[_type == "post" && $slug in tags[]->slug.current] ${n}`,(0,i.Z)`*[_type == "post" && author->slug.current == $slug] ${n}`,(0,i.Z)`*[_type == "post" && category->slug.current == $slug] ${n}`;let c=(0,i.Z)`
  *[_type == "post" && _id != $id] | order(date asc, _updatedAt asc) [0...4] 
    ${n}
  `,u=(0,i.Z)`*[_type == "seoContent" && language == $language]{title,body}`,p=(0,i.Z)`*[_type == "termsOfUse" && language == $language]{title,body}`,m=(0,i.Z)`*[_type == "privacyPolicy" && language == $language]{title,body}`,g=(0,i.Z)`*[_type == "userDataDeletion" && language == $language]{title,body}`,x=(0,r.eI)(l);function h(e){return a()(l).image(e)}async function b({query:e,qParams:A,tags:t}){return x.fetch(e,A,{next:{tags:t,revalidate:3600}})}let y=async()=>await b({query:o,qParams:{},tags:["post","author","category"]}),f=async e=>await b({query:d,qParams:{slug:e},tags:["post","author","category"]}),j=async(e,A)=>await b({query:c,qParams:{slug:e,id:A},tags:[]}),v=async e=>await b({query:u,qParams:{language:e},tags:[]}),w=async e=>await b({query:p,qParams:{language:e},tags:[]}),B=async e=>await b({query:m,qParams:{language:e},tags:[]}),N=async e=>await b({query:g,qParams:{language:e},tags:[]})},18714:(e,A,t)=>{"use strict";t.r(A),t.d(A,{default:()=>r});var s=t(28713);t(9640);var a=t(53020);async function r(e,A,t){let s=(0,a.cookies)(),r=s.get("tkn")?.value;try{let s=await fetch(e,{method:A,headers:{Authorization:`Bearer ${r}`,"Content-Type":"application/json"},...t});if(!s.ok)return{data:null,meta:void 0,error:{status:s.status,name:s.statusText,message:await s.text()||"Unexpected error",details:{}}};let a=await s.json();if(a.error)return{data:null,meta:void 0,error:a.error};return{data:a.data,meta:a.meta,error:void 0}}catch(e){return{data:null,meta:void 0,error:{status:500,details:{cause:e.cause},message:e.message,name:e.name}}}}(0,t(83557).h)([r]),(0,s.j)("ace39bf07124e0ba39e8486050a53bc79b0621b3",r)},84033:(e,A,t)=>{"use strict";t.r(A),t.d(A,{default:()=>s});let s={src:"/_next/static/media/about-us-image.267ed102.webp",height:1279,width:1920,blurDataURL:"data:image/webp;base64,UklGRj4AAABXRUJQVlA4IDIAAACQAQCdASoIAAUAAkA4JZgAAudZtgAA/uYL+8l5/VVhaFlCK8//pnoGQ+Tg9E15n5nAAA==",blurWidth:8,blurHeight:5}}};var A=require("../../../../../webpack-runtime.js");A.C(e);var t=e=>A(A.s=e),s=A.X(0,[9379,5063,4916,9467,4859,5268,3327,8530,3832,6136,7146,4975,6666,9965,595,4805],()=>t(18686));module.exports=s})();