"use strict";exports.id=4283,exports.ids=[4283],exports.modules={34283:(t,e,a)=>{a.r(e),a.d(e,{default:()=>n});var s=a(72051),r=a(38785);let o=(0,a(45347).createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\components\footer\seekers-seo-content.tsx#default`);var u=a(52845);async function n(){let t=(0,u.cookies)(),e=t.get("NEXT_LOCALE")?.value,a=await (0,r.oK)(e?.toLowerCase()||"en");return s.jsx(o,{content:a[0]})}},38785:(t,e,a)=>{a.d(e,{g_:()=>P,oK:()=>b,lU:()=>Z,zQ:()=>h,YY:()=>w,_b:()=>v,b0:()=>f,t3:()=>$});var s=a(73027),r=a.n(s),o=a(55700);let u={projectId:"r294h68q",dataset:"production",apiVersion:"2024-01-05",useCdn:!1,token:process.env.SANITY_API_KEY,perspective:"published"};var n=a(1601);let g=`{
_id,
  title,
  metadata,
  slug,
  tags,
  author->{
    _id,
    name,
    slug,
    image{asset -> {url}},
    bio
  },
  coverImage{
  asset->{url}},
  mainImage{
  asset->{url}},
  publishedAt,
  category -> {
  _id,
  title
  },
  body
}`;(0,n.Z)`*[_type == "post" && author != "hidden"] ${g}`;let l=(0,n.Z)`*[_type == "post" && author != "hidden"][0...2] ${g}`,i=(0,n.Z)`*[_type == "post" && slug.current == $slug][0]  ${g}
  

`;(0,n.Z)`*[_type == "post" && $slug in tags[]->slug.current] ${g}`,(0,n.Z)`*[_type == "post" && author->slug.current == $slug] ${g}`,(0,n.Z)`*[_type == "post" && category->slug.current == $slug] ${g}`;let y=(0,n.Z)`
  *[_type == "post" && _id != $id] | order(date asc, _updatedAt asc) [0...4] 
    ${g}
  `,p=(0,n.Z)`*[_type == "seoContent" && language == $language]{title,body}`,c=(0,n.Z)`*[_type == "termsOfUse" && language == $language]{title,body}`,d=(0,n.Z)`*[_type == "privacyPolicy" && language == $language]{title,body}`,_=(0,n.Z)`*[_type == "userDataDeletion" && language == $language]{title,body}`,m=(0,o.eI)(u);function $(t){return r()(u).image(t)}async function q({query:t,qParams:e,tags:a}){return m.fetch(t,e,{next:{tags:a,revalidate:3600}})}let P=async()=>await q({query:l,qParams:{},tags:["post","author","category"]}),h=async t=>await q({query:i,qParams:{slug:t},tags:["post","author","category"]}),Z=async(t,e)=>await q({query:y,qParams:{slug:t,id:e},tags:[]}),b=async t=>await q({query:p,qParams:{language:t},tags:[]}),v=async t=>await q({query:c,qParams:{language:t},tags:[]}),w=async t=>await q({query:d,qParams:{language:t},tags:[]}),f=async t=>await q({query:_,qParams:{language:t},tags:[]})}};