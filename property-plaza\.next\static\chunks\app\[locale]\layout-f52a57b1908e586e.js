(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1203],{37147:function(e,t,n){Promise.resolve().then(n.bind(n,65009)),Promise.resolve().then(n.t.bind(n,70226,23)),Promise.resolve().then(n.bind(n,9984)),Promise.resolve().then(n.bind(n,18682)),Promise.resolve().then(n.bind(n,54434)),Promise.resolve().then(n.bind(n,58622)),Promise.resolve().then(n.bind(n,3e3)),Promise.resolve().then(n.bind(n,59556)),Promise.resolve().then(n.bind(n,97867)),Promise.resolve().then(n.bind(n,31085)),Promise.resolve().then(n.bind(n,10575)),Promise.resolve().then(n.bind(n,22248)),Promise.resolve().then(n.bind(n,93753)),Promise.resolve().then(n.bind(n,16471)),Promise.resolve().then(n.bind(n,56302)),Promise.resolve().then(n.t.bind(n,68421,23)),Promise.resolve().then(n.t.bind(n,36539,23))},65009:function(e,t,n){"use strict";n.d(t,{default:function(){return r}});var i=n(57437),a=n(99376),s=n(2265);function r(){let e=(0,a.usePathname)(),t=(0,a.useSearchParams)();return(0,s.useEffect)(()=>{n.e(9953).then(n.t.bind(n,49953,23)).then(e=>e.default).then(n=>{n.init("1183232953419795"),n.pageView(),n.track("open website",{pathname:e,property_detail:t.get("code")?{title:e,code:t.get("code")}:null})})},[e,t]),(0,i.jsx)(i.Fragment,{})}},9984:function(e,t,n){"use strict";n.d(t,{default:function(){return p}});var i=n(57437),a=n(6404),s=n(48614),r=n(40521),o=n(64131),d=n(48166),l=n(62869),c=n(99376),u=n(2265),f=n(1828);function p(){let e=(0,c.usePathname)(),t=o.Z.get(a.t8),[n,f]=(0,u.useState)(!1),[p,g]=(0,u.useState)(!1),[v,h]=(0,u.useState)({necessary:!0,functional:!1,analytic:!1,marketing:!1}),y=e=>{["all","necessary","custom"].includes(e)&&(o.Z.set(a.t8,"true"),"necessary"==e?h(e=>({...e,necessary:!0})):"all"==e&&h(e=>({analytic:!0,functional:!0,marketing:!0,necessary:!0})),o.Z.set(a.K6,v.necessary.toString()),o.Z.set(a.Ge,v.functional.toString()),o.Z.set(a.QY,v.analytic.toString()),o.Z.set(a.$_,v.marketing.toString()),f(!1))};return(0,u.useEffect)(()=>{t?f(!1):f(!0)},[t]),(0,i.jsx)(s.M,{children:!n||e.includes("create-password")&&e.includes("reset-password")?null:(0,i.jsxs)(r.E.div,{initial:{y:20,opacity:0},animate:{y:0,opacity:1},exit:{y:20,opacity:0},transition:{duration:.7,ease:"easeInOut"},className:"fixed max-w-sm w-full bottom-4 left-4 bg-background p-8 z-20 shadow-md rounded-2xl space-y-4",children:[(0,i.jsxs)("p",{className:"inline-flex items-center gap-2 font-semibold",children:[(0,i.jsx)("span",{children:(0,i.jsx)(d.Z,{})}),"Manage Cookie Preferences"]}),p?(0,i.jsxs)("section",{className:"space-y-4",children:[(0,i.jsx)(m,{title:"Necessary",onValueChange:e=>h(t=>({...t,necessary:e})),description:"These cookies are essential for the website to function properly. They enable core functionalities such as security, network management, and accessibility. You cannot disable these cookies.",value:v.necessary,disabled:!0}),(0,i.jsx)(m,{title:"Analytics",onValueChange:e=>h(t=>({...t,analytic:e})),description:"These cookies allow the website to remember your preferences and provide enhanced features like saved language settings or embedded videos.",value:v.analytic}),(0,i.jsx)(m,{title:"Functional",onValueChange:e=>h(t=>({...t,functional:e})),description:"These cookies are essential for the website to function properly. They enable core functionalities such as security, network management, and accessibility. You cannot disable these cookies.",value:v.functional}),(0,i.jsx)(m,{title:"Marketing",onValueChange:e=>h(t=>({...t,marketing:e})),description:"These cookies are used to deliver relevant ads and track your activity across websites to personalize your advertising experience.",value:v.marketing})]}):(0,i.jsx)("section",{children:(0,i.jsx)("p",{className:"text-seekers-text-light",children:"We use cookies to optimize your experience on our website. You can choose which categories of cookies to allow below."})}),(0,i.jsxs)("div",{className:"inline-flex gap-2",children:[(0,i.jsx)(l.z,{onClick:()=>y("all"),children:"Accept all"}),p?(0,i.jsx)(l.z,{variant:"ghost",onClick:()=>y("custom"),children:"Save preferences"}):(0,i.jsx)(l.z,{variant:"ghost",onClick:()=>g(!0),children:"Manage preferences"})]})]})})}function m(e){let{title:t,value:n,disabled:a,description:s,onValueChange:r}=e;return(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsxs)("div",{className:"flex justify-between",children:[(0,i.jsx)("p",{className:"font-semibold",children:t}),(0,i.jsx)(f.r,{checked:n,disabled:a,onCheckedChange:e=>r(e)})]}),(0,i.jsx)("p",{className:"text-xs",children:s})]})}},18682:function(e,t,n){"use strict";n.d(t,{default:function(){return o}});var i=n(57437),a=n(77398);n(33702);var s=n(42586),r=n(2265);function o(){let e=(0,s.useLocale)();return(0,r.useEffect)(()=>{a.locale(e)},[e]),(0,i.jsx)(i.Fragment,{})}},54434:function(e,t,n){"use strict";n.d(t,{default:function(){return s}});var i=n(57437),a=n(1322);function s(e){let{children:t}=e;return(0,i.jsx)(a.un,{apiKey:"AIzaSyCOm6xsEL7MViTvzxhjmP6BRWPpCdCOtgM",children:t})}},58622:function(e,t,n){"use strict";n.d(t,{default:function(){return m}});var i=n(57437),a=n(78645),s=n(34755),r=n(35153),o=n(96261),d=n(59625),l=n(89134);let c=(0,d.Ue)()((0,l.tJ)(e=>({hasNotificationSound:void 0,setNotificationSound:t=>e({hasNotificationSound:t}),isLoading:!0,setIsLoading:t=>e({isLoading:t}),editingStatus:[],setEditingStatus:(t,n)=>e(e=>{let i=e.editingStatus;if("add"==n)return i.includes(t)?{...e}:(i.push(t),{...e,editingStatus:i});if("remove"==n){let n=i.filter(e=>e!==t);return{...e,editingStatus:n}}return{...e}}),removeEditingStatus:()=>e({editingStatus:[]})}),{name:"settings",storage:(0,l.FL)(()=>localStorage),onRehydrateStorage:()=>e=>{e&&e.setIsLoading(!1)}}));var u=n(2265);let f=()=>{let{setNotificationSound:e}=c(e=>e),[t,n]=(0,u.useState)(null);return(0,u.useEffect)(()=>{let e=new Audio;e.src="/sounds/notification.mp3",n(e)},[]),{enableSoundNotification:t=>{e(t)},playSound:()=>{let e=new Audio;e.src="/sounds/notification.mp3",e.volume=1,e.play().then(()=>{}).catch(e=>{console.error("sound error",e)})},popUpNotification:(e,t)=>{if(!("Notification"in window)){console.warn("This browser does not support desktop notifications.");return}"granted"===Notification.permission?new Notification(e,{body:t||""}):"default"===Notification.permission?Notification.requestPermission().then(n=>{"granted"===n?new Notification(e,{body:t||""}):console.warn("Notification permission denied.")}):"denied"===Notification.permission&&console.warn("Notifications are denied by the user.")}}};var p=n(42586);function m(e){let{isSeeker:t=!1}=e,n=(0,p.useTranslations)("seeker"),{toast:d}=(0,r.pm)(),[l,m]=(0,u.useState)(!1),{updatechatDetail:g,updateSpecificAllChat:v}=(0,o.R)(e=>e),{hasNotificationSound:h,isLoading:y}=c(e=>e),{enableSoundNotification:x,playSound:b,popUpNotification:w}=f();return(0,u.useEffect)(()=>{s.W.connected||s.W.connect();let e=e=>{let t=(0,a.Z5)(e);d({title:n("message.newMessage")+t.displayName,description:t.text}),window.dispatchEvent(new CustomEvent("newMessage",{detail:t}))};return s.W.on("newChatNotif",e),()=>{s.W.off("newChatNotif",e)}},[]),(0,u.useEffect)(()=>{let e=e=>{b(),w(n("message.newMessage")+e.detail.displayName,e.detail.text),g(e.detail),v(e.detail)};return window.addEventListener("newMessage",e),()=>{window.removeEventListener("newMessage",e)}},[b]),(0,u.useEffect)(()=>{y||(void 0==h?m(!0):m(!1))},[h]),(0,i.jsx)(i.Fragment,{})}},3e3:function(e,t,n){"use strict";n.d(t,{default:function(){return o}});var i=n(57437),a=n(21623),s=n(29827);let r=new a.S;function o(e){let{children:t}=e;return(0,i.jsx)(s.aH,{client:r,children:t})}},62869:function(e,t,n){"use strict";n.d(t,{z:function(){return c}});var i=n(57437),a=n(2265),s=n(98482),r=n(90535),o=n(94508),d=n(51817);let l=(0,r.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-seekers-primary text-white shadow hover:bg-seekers-primary/90","default-seekers":"bg-seekers-primary text-white shadow hover:bg-seekers-primary-light/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef((e,t)=>{let{className:n,variant:a,size:r,asChild:c=!1,loading:u=!1,...f}=e,p=c?s.g7:"button";return(0,i.jsx)(p,{className:(0,o.cn)(l({variant:a,size:r,className:n})),ref:t,disabled:u||f.disabled,...f,children:u?(0,i.jsx)(d.Z,{className:(0,o.cn)("h-4 w-4 animate-spin")}):f.children})});c.displayName="Button"},1828:function(e,t,n){"use strict";n.d(t,{r:function(){return o}});var i=n(57437),a=n(2265),s=n(50721),r=n(94508);let o=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,i.jsx)(s.fC,{className:(0,r.cn)("peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-seekers-primary data-[state=unchecked]:bg-input",n),...a,ref:t,children:(0,i.jsx)(s.bU,{className:(0,r.cn)("pointer-events-none block h-4 w-4 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0")})})});o.displayName=s.fC.displayName},59556:function(e,t,n){"use strict";n.d(t,{Toaster:function(){return h}});var i=n(57437),a=n(2265),s=n(20653),r=n(41915),o=n(90535),d=n(94508);let l=r.zt,c=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,i.jsx)(r.l_,{ref:t,className:(0,d.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4   sm:right-0  sm:flex-col md:max-w-[420px]",n),...a})});c.displayName=r.l_.displayName;let u=(0,o.j)("group pointer-events-auto relative flex w-full items-center justify-between space-x-2 overflow-hidden rounded-md border p-4 pr-6 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background",destructive:"destructive group border-destructive bg-destructive/20 backdrop-blur-sm text-destructive"}},defaultVariants:{variant:"default"}}),f=a.forwardRef((e,t)=>{let{className:n,variant:a,...s}=e;return(0,i.jsx)(r.fC,{ref:t,className:(0,d.cn)(u({variant:a}),n),...s})});f.displayName=r.fC.displayName,a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,i.jsx)(r.aU,{ref:t,className:(0,d.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium transition-colors hover:bg-secondary focus:outline-none focus:ring-1 focus:ring-ring disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",n),...a})}).displayName=r.aU.displayName;let p=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,i.jsx)(r.x8,{ref:t,className:(0,d.cn)("absolute right-1 top-1 rounded-md p-1 text-primary-lighter opacity-0 transition-opacity hover:text-primary focus:opacity-100 focus:outline-none focus:ring-1 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",n),"toast-close":"",...a,children:(0,i.jsx)(s.Pxu,{className:"h-4 w-4"})})});p.displayName=r.x8.displayName;let m=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,i.jsx)(r.Dx,{ref:t,className:(0,d.cn)("text-sm font-semibold [&+div]:text-xs",n),...a})});m.displayName=r.Dx.displayName;let g=a.forwardRef((e,t)=>{let{className:n,...a}=e;return(0,i.jsx)(r.dk,{ref:t,className:(0,d.cn)("text-sm opacity-90",n),...a})});g.displayName=r.dk.displayName;var v=n(35153);function h(){let{toasts:e}=(0,v.pm)();return(0,i.jsxs)(l,{children:[e.map(function(e){let{id:t,title:n,description:a,action:s,...r}=e;return(0,i.jsxs)(f,{...r,children:[(0,i.jsxs)("div",{className:"grid gap-1",children:[n&&(0,i.jsx)(m,{children:n}),a&&(0,i.jsx)(g,{children:a})]}),s,(0,i.jsx)(p,{})]},t)}),(0,i.jsx)(c,{})]})}},78645:function(e,t,n){"use strict";n.d(t,{Z5:function(){return o},eN:function(){return d},ug:function(){return r}});var i=n(77398),a=n.n(i),s=n(33254);function r(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"en";return e.map(e=>{var n,i,a,r,o,d,l;let c=e.messages[0];if(c)return console.log(t,(0,s.P)(null===(n=e.ref_data)||void 0===n?void 0:n.title,t)),{code:e.code,category:e.category,roomId:e.participants.room_id,lastMessages:{createdAt:(null==c?void 0:c.created_at)||e.created_at,displayAs:(null==c?void 0:c.display_as)||"",displayName:(null==c?void 0:c.display_name)||"",text:(null==c?void 0:c.text)||"",isRead:(null==c?void 0:c.is_read)||!1,isSent:(null==c?void 0:c.is_send)||!1,id:(null==c?void 0:c.id)||"",code:(null==c?void 0:c.code)||""},participant:{email:(null===(i=e.participants.info)||void 0===i?void 0:i.email)||"",fullName:(null===(a=e.participants.info)||void 0===a?void 0:a.display_name)||"",phoneNumber:(null===(r=e.participants.info)||void 0===r?void 0:r.phone_number)||"",image:e.participants.info.image||"",id:(null===(o=e.participants.info)||void 0===o?void 0:o.id)||"",category:e.category,status:e.status,property:{title:(0,s.P)(null===(d=e.ref_data)||void 0===d?void 0:d.title,t)||void 0,image:(null===(l=e.ref_data)||void 0===l?void 0:l.images[0].image)||void 0},otherProperty:[]},status:e.status,updatedAt:e.updated_at}}).filter(e=>void 0!==e).sort((e,t)=>a()(t.lastMessages.createdAt).unix()-a()(e.lastMessages.createdAt).unix())}function o(e){return{createdAt:e.created_at,displayAs:e.display_as,displayName:e.display_name,isRead:e.is_read,isSent:e.is_send,text:e.text,id:e.id,code:e.code}}function d(e){var t,n,i,a,r,o,d,l,c,u,f,p;let m=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"en";console.log(e.messages);let g=e.messages[(null===(t=e.messages)||void 0===t?void 0:t.length)-1]||void 0,v=e.messages.map(e=>({createdAt:e.created_at,displayAs:e.display_as,displayName:e.display_name,isRead:e.is_read,isSent:e.is_send,text:e.text,id:e.id,code:e.code})),h=null===(i=e.ref_data)||void 0===i?void 0:null===(n=i.extended_list)||void 0===n?void 0:n.map(e=>{var t;return{id:e.code,image:(null===(t=e.images[0])||void 0===t?void 0:t.image)||"",title:(0,s.P)(e.title,m)}});return{code:e.code,category:e.category,roomId:e.participants.room_id,lastMessages:{createdAt:g.created_at,displayAs:g.display_as,displayName:g.display_name,text:g.text,isRead:g.is_read,isSent:g.is_send,id:g.id,code:g.code||""},participant:{email:(null===(a=e.participants.info)||void 0===a?void 0:a.email)||"",fullName:(null===(r=e.participants.info)||void 0===r?void 0:r.display_name)||"",phoneNumber:(null===(o=e.participants.info)||void 0===o?void 0:o.phone_number)||"",image:(null===(d=e.participants.info)||void 0===d?void 0:d.image)||"",id:(null===(l=e.participants.info)||void 0===l?void 0:l.id)||"",category:e.category,status:e.status,property:{id:(null===(c=e.ref_data)||void 0===c?void 0:c.code)||"",image:(null===(f=e.ref_data)||void 0===f?void 0:null===(u=f.images[0])||void 0===u?void 0:u.image)||"",title:(0,s.P)(null===(p=e.ref_data)||void 0===p?void 0:p.title,m)||""},moreProperty:h||[]},allMessages:v,updatedAt:e.updated_at}}},33254:function(e,t,n){"use strict";function i(e){return{nextPage:e.next_page,page:e.page,pageCount:e.page_count,perPage:e.per_page,prevPage:e.prev_page,total:e.total}}function a(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"en";if(!e)return"";if("string"==typeof e)return e;let n=e.find(e=>e.lang===t);return(null==n?void 0:n.value)||e[0].value}n.d(t,{N:function(){return i},P:function(){return a}})},34755:function(e,t,n){"use strict";n.d(t,{W:function(){return r}});var i=n(68680),a=n(64131),s=n(6404);let r=(0,i.ZP)("https://dev.property-plaza.id/",{extraHeaders:{"auth-token":a.Z.get(s.LA)||""},autoConnect:!1})},35153:function(e,t,n){"use strict";n.d(t,{pm:function(){return f}});var i=n(2265);let a=0,s=new Map,r=e=>{if(s.has(e))return;let t=setTimeout(()=>{s.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);s.set(e,t)},o=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:n}=t;return n?r(n):e.toasts.forEach(e=>{r(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===n||void 0===n?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},d=[],l={toasts:[]};function c(e){l=o(l,e),d.forEach(e=>{e(l)})}function u(e){let{...t}=e,n=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),i=()=>c({type:"DISMISS_TOAST",toastId:n});return c({type:"ADD_TOAST",toast:{...t,id:n,open:!0,onOpenChange:e=>{e||i()}}}),{id:n,dismiss:i,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:n}})}}function f(){let[e,t]=i.useState(l);return i.useEffect(()=>(d.push(t),()=>{let e=d.indexOf(t);e>-1&&d.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},6404:function(e,t,n){"use strict";n.d(t,{$_:function(){return m},Ge:function(){return f},K6:function(){return u},LA:function(){return i},QY:function(){return p},Y:function(){return g},Z9:function(){return s},ac:function(){return o},gr:function(){return a},nM:function(){return r},t8:function(){return c},vQ:function(){return l},xm:function(){return d}});let i="tkn",a="SEEKER",s=8,r=1,o=30,d=300,l=10,c="cookies-collection-status",u="necessary-cookies-collection-status",f="functional-cookies-collection-status",p="analytic-cookies-collection-status",m="marketing-cookies-collection-status",g={type:"t",minPrice:"minp",maxPrice:"maxp",landLargest:"landl",landSmallest:"lands",buildingLargest:"buildl",buildingSmallest:"builds",gardenLargest:"gardenl",gardenSmallest:"gardens",yearsOfBuild:"yob",bedroomTotal:"bedt",bathroomTotal:"batht",rentalOffer:"ro",propertyCondition:"pc",electircity:"el",parking:"pk",swimmingPool:"sp",typeLiving:"tl",furnished:"fs",view:"v",minimumContract:"minc",category:"c",subCategory:"sc",feature:"feat",propertyLocation:"pl",zoom:"z",viewMode:"viewMode"}},94508:function(e,t,n){"use strict";n.d(t,{E6:function(){return u},ET:function(){return m},Fg:function(){return p},cn:function(){return o},g6:function(){return f},pl:function(){return g},uf:function(){return c},xG:function(){return l},yT:function(){return v}});var i=n(61994),a=n(77398),s=n.n(a),r=n(53335);function o(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,r.m6)((0,i.W)(t))}n(25566);let d=e=>{switch(e){case"USD":default:return"en-us";case"IDR":return"id-ID";case"GBP":return"en-GB";case"AUD":return"en-AU";case"EUR":return"nl-NL"}},l=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return arguments.length>2&&void 0!==arguments[2]&&arguments[2],new Intl.NumberFormat(d(t),{style:"currency",currency:t,minimumFractionDigits:0,maximumFractionDigits:"IDR"==t?0:2}).format(e)},c=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"en-US";return new Intl.NumberFormat(t,{style:"decimal",minimumFractionDigits:0,maximumFractionDigits:0}).format(e)};function u(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}function f(e){let t=s()(e),n=s()();return t.isSame(n,"day")?t.format("HH:mm"):t.format("DD/MM/YY")}function p(e){return e.replaceAll(/[^a-zA-Z0-9\s]/g,"-").replaceAll(/\s/g,"--")}let m=(e,t)=>e.includes(t)?e.filter(e=>e!==t):[...e,t];function g(e,t){return e.some(e=>t.includes(e))}let v=e=>e.charAt(0).toUpperCase()+e.slice(1)},96261:function(e,t,n){"use strict";n.d(t,{R:function(){return o}});var i=n(77398),a=n.n(i),s=n(59625),r=n(89134);let o=(0,s.Ue)()((0,r.tJ)(e=>({currentLayout:"list",setlayout:t=>e({currentLayout:t}),roomId:void 0,setRoomId:t=>e({roomId:t}),chatDetail:[],setchatDetail:t=>e({chatDetail:t}),updatechatDetail:t=>e(e=>{let n=e.chatDetail.length;return e.chatDetail[n-1].id==t.id?{}:{chatDetail:[...e.chatDetail,t]}}),participant:void 0,setParticipant:t=>e({participant:t}),allChat:[],setAllChat:t=>e({allChat:t}),updateSpecificAllChat:(t,n,i)=>e(e=>{let{allChat:s}=e,r=e=>e.sort((e,t)=>a()(t.lastMessages.createdAt).unix()-a()(e.lastMessages.createdAt).unix());if(n)return{allChat:r([...s,t])};if(i){let e=s.findIndex(e=>e.code===i);if("roomId"in t){if(e<0)return{allChat:r([...s,t])};{let n=[...s];return n[e]=t,{allChat:r(n)}}}if("id"in t)return e>=0?{allChat:r(s.map((n,i)=>i===e?{...n,lastMessages:t}:n))}:{allChat:s}}if("roomId"in t){let e=s.findIndex(e=>e.code===t.code);if(e<0)return{allChat:r([...s,t].sort((e,t)=>a()(t.lastMessages.createdAt).unix()-a()(e.lastMessages.createdAt).unix()))};{let n=[...s];return n[e]=t,{allChat:r(n)}}}if("id"in t){let e=s.findIndex(e=>e.code===t.code);if(e>=0)return{allChat:r(s.map((n,i)=>i===e?{...n,lastMessages:t}:n))}}return{allChat:s}}),updateParticipantStatus:t=>e(e=>e.participant?{participant:{...e.participant,status:t}}:{})}),{name:"messagingLayout",storage:(0,r.FL)(()=>sessionStorage)}))},70226:function(){}},function(e){e.O(0,[5646,5488,6990,8310,6290,8094,2586,4956,6088,1298,4413,2920,1322,5247,2971,2117,1744],function(){return e(e.s=37147)}),_N_E=e.O()}]);