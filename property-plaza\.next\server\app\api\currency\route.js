"use strict";(()=>{var e={};e.id=6155,e.ids=[6155],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},4609:(e,r,t)=>{t.r(r),t.d(r,{originalPathname:()=>C,patchFetch:()=>P,requestAsyncStorage:()=>d,routeModule:()=>i,serverHooks:()=>y,staticGenerationAsyncStorage:()=>l});var a={};t.r(a),t.d(a,{GET:()=>c});var n=t(73278),s=t(45002),o=t(54877),u=t(71309);let p=process.env.NEXT_PUBLIC_CURRENCY_API+`latest?apikey=${process.env.CURRENCY_API_KEY}`;async function c(e){try{let e=await fetch(p+"&currencies=EUR%2CUSD%2CCAD%2CIDR%2CGBP%2CAUD&base_currency=IDR",{next:{revalidate:3600}}).then(e=>e.json());return u.NextResponse.json({data:e},{status:200})}catch(e){return u.NextResponse.json({error:"Failed get currency data"},{status:500})}}let i=new n.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/currency/route",pathname:"/api/currency",filename:"route",bundlePath:"app/api/currency/route"},resolvedPagePath:"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\api\\currency\\route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:d,staticGenerationAsyncStorage:l,serverHooks:y}=i,C="/api/currency/route";function P(){return(0,o.patchFetch)({serverHooks:y,staticGenerationAsyncStorage:l})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[9379,4833],()=>t(4609));module.exports=a})();