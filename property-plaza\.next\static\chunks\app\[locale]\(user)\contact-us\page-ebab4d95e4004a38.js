(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[831],{50903:function(e,t,n){Promise.resolve().then(n.bind(n,91483)),Promise.resolve().then(n.bind(n,97867)),Promise.resolve().then(n.bind(n,31085))},91483:function(e,t,n){"use strict";n.d(t,{default:function(){return c}});var r=n(57437),s=n(2265),a=n(42586),i=n(62869),o=n(33145),l=n(35153),u=n(29985);function c(){let e=(0,a.useTranslations)("ContactUs"),{toast:t}=(0,l.pm)(),[n,c]=(0,s.useState)({name:"",email:"",subject:"",message:""}),[d,m]=(0,s.useState)(!1),f=async n=>{n.preventDefault(),m(!0);try{await new Promise(e=>setTimeout(e,1e3)),t({description:e("messageSent")}),c({name:"",email:"",subject:"",message:""})}catch(e){t({variant:"destructive",description:"Er is iets misgegaan. Probeer het later opnieuw."})}finally{m(!1)}};return(0,r.jsxs)("div",{className:"w-full bg-white",children:[(0,r.jsxs)("section",{"aria-label":"Contact Hero",className:"relative w-full h-[300px]",children:[(0,r.jsx)(o.default,{src:u.default,alt:"Contact Property Plaza",fill:!0,className:"object-cover",priority:!0}),(0,r.jsx)("div",{className:"absolute inset-0 bg-black/40 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center px-4",children:[(0,r.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-white mb-4",children:e("title")}),(0,r.jsx)("p",{className:"text-xl text-white max-w-3xl mx-auto",children:e("subtitle")})]})})]}),(0,r.jsx)("div",{className:"max-w-7xl mx-auto px-4 py-12",children:(0,r.jsx)("div",{className:"max-w-3xl mx-auto",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-8",children:[(0,r.jsx)("h2",{className:"text-2xl font-semibold mb-6",children:e("sendMessage")}),(0,r.jsxs)("form",{onSubmit:f,className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:e("nameField")}),(0,r.jsx)("input",{type:"text",id:"name",name:"name",required:!0,className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-seekers-primary focus:border-transparent",value:n.name,onChange:e=>c(t=>({...t,name:e.target.value}))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:e("emailField")}),(0,r.jsx)("input",{type:"email",id:"email",name:"email",required:!0,className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-seekers-primary focus:border-transparent",value:n.email,onChange:e=>c(t=>({...t,email:e.target.value}))})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"subject",className:"block text-sm font-medium text-gray-700 mb-2",children:e("subjectField")}),(0,r.jsx)("input",{type:"text",id:"subject",name:"subject",required:!0,className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-seekers-primary focus:border-transparent",value:n.subject,onChange:e=>c(t=>({...t,subject:e.target.value}))})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{htmlFor:"message",className:"block text-sm font-medium text-gray-700 mb-2",children:e("messageField")}),(0,r.jsx)("textarea",{id:"message",name:"message",rows:6,required:!0,className:"w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-seekers-primary focus:border-transparent",value:n.message,onChange:e=>c(t=>({...t,message:e.target.value}))})]}),(0,r.jsx)("div",{children:(0,r.jsx)(i.z,{type:"submit",variant:"default-seekers",className:"w-full md:w-auto",disabled:d,children:d?"Verzenden...":e("submitButton")})})]})]})})})]})}},62869:function(e,t,n){"use strict";n.d(t,{z:function(){return c}});var r=n(57437),s=n(2265),a=n(98482),i=n(90535),o=n(94508),l=n(51817);let u=(0,i.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-seekers-primary text-white shadow hover:bg-seekers-primary/90","default-seekers":"bg-seekers-primary text-white shadow hover:bg-seekers-primary-light/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),c=s.forwardRef((e,t)=>{let{className:n,variant:s,size:i,asChild:c=!1,loading:d=!1,...m}=e,f=c?a.g7:"button";return(0,r.jsx)(f,{className:(0,o.cn)(u({variant:s,size:i,className:n})),ref:t,disabled:d||m.disabled,...m,children:d?(0,r.jsx)(l.Z,{className:(0,o.cn)("h-4 w-4 animate-spin")}):m.children})});c.displayName="Button"},35153:function(e,t,n){"use strict";n.d(t,{pm:function(){return m}});var r=n(2265);let s=0,a=new Map,i=e=>{if(a.has(e))return;let t=setTimeout(()=>{a.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);a.set(e,t)},o=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:n}=t;return n?i(n):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===n||void 0===n?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],u={toasts:[]};function c(e){u=o(u,e),l.forEach(e=>{e(u)})}function d(e){let{...t}=e,n=(s=(s+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>c({type:"DISMISS_TOAST",toastId:n});return c({type:"ADD_TOAST",toast:{...t,id:n,open:!0,onOpenChange:e=>{e||r()}}}),{id:n,dismiss:r,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:n}})}}function m(){let[e,t]=r.useState(u);return r.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:d,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},94508:function(e,t,n){"use strict";n.d(t,{E6:function(){return d},ET:function(){return h},Fg:function(){return f},cn:function(){return o},g6:function(){return m},pl:function(){return p},uf:function(){return c},xG:function(){return u},yT:function(){return g}});var r=n(61994),s=n(77398),a=n.n(s),i=n(53335);function o(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,i.m6)((0,r.W)(t))}n(25566);let l=e=>{switch(e){case"USD":default:return"en-us";case"IDR":return"id-ID";case"GBP":return"en-GB";case"AUD":return"en-AU";case"EUR":return"nl-NL"}},u=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"USD";return arguments.length>2&&void 0!==arguments[2]&&arguments[2],new Intl.NumberFormat(l(t),{style:"currency",currency:t,minimumFractionDigits:0,maximumFractionDigits:"IDR"==t?0:2}).format(e)},c=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"en-US";return new Intl.NumberFormat(t,{style:"decimal",minimumFractionDigits:0,maximumFractionDigits:0}).format(e)};function d(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)}function m(e){let t=a()(e),n=a()();return t.isSame(n,"day")?t.format("HH:mm"):t.format("DD/MM/YY")}function f(e){return e.replaceAll(/[^a-zA-Z0-9\s]/g,"-").replaceAll(/\s/g,"--")}let h=(e,t)=>e.includes(t)?e.filter(e=>e!==t):[...e,t];function p(e,t){return e.some(e=>t.includes(e))}let g=e=>e.charAt(0).toUpperCase()+e.slice(1)},49988:function(e,t,n){"use strict";function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}).apply(null,arguments)}n.d(t,{g:function(){return r}})},97867:function(e,t,n){"use strict";n.d(t,{default:function(){return l}});var r=n(49988),s=n(27648),a=n(99376),i=n(2265),o=n(48706),l=(0,i.forwardRef)(function(e,t){let{defaultLocale:n,href:l,locale:u,localeCookie:c,onClick:d,prefetch:m,unprefixed:f,...h}=e,p=(0,o.Z)(),g=u!==p,b=u||p,x=function(){let[e,t]=(0,i.useState)();return(0,i.useEffect)(()=>{t(window.location.host)},[]),e}(),v=x&&f&&(f.domains[x]===b||!Object.keys(f.domains).includes(x)&&p===n&&!u)?f.pathname:l,y=(0,a.usePathname)();return g&&(m&&console.error("The `prefetch` prop is currently not supported when using the `locale` prop on `Link` to switch the locale.`"),m=!1),i.createElement(s.default,(0,r.g)({ref:t,href:v,hrefLang:g?u:void 0,onClick:function(e){(function(e,t,n,r){if(!e||!(r!==n&&null!=r)||!t)return;let s=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window.location.pathname;return"/"===e?t:t.replace(e,"")}(t),{name:a,...i}=e;i.path||(i.path=""!==s?s:"/");let o="".concat(a,"=").concat(r,";");for(let[e,t]of Object.entries(i))o+="".concat("maxAge"===e?"max-age":e),"boolean"!=typeof t&&(o+="="+t),o+=";";document.cookie=o})(c,y,p,u),d&&d(e)},prefetch:m},h))})},31085:function(e,t,n){"use strict";n.d(t,{default:function(){return d}});var r=n(49988),s=n(99376),a=n(2265),i=n(48706);function o(e){return("object"==typeof e?null==e.host&&null==e.hostname:!/^[a-z]+:/i.test(e))&&!function(e){let t="object"==typeof e?e.pathname:e;return null!=t&&!t.startsWith("/")}(e)}function l(e,t){let n;return"string"==typeof e?n=u(t,e):(n={...e},e.pathname&&(n.pathname=u(t,e.pathname))),n}function u(e,t){let n=e;return/^\/(\?.*)?$/.test(t)&&(t=t.slice(1)),n+=t}n(25566);var c=n(97867);let d=(0,a.forwardRef)(function(e,t){let{href:n,locale:u,localeCookie:d,localePrefixMode:m,prefix:f,...h}=e,p=(0,s.usePathname)(),g=(0,i.Z)(),b=u!==g,[x,v]=(0,a.useState)(()=>o(n)&&("never"!==m||b)?l(n,f):n);return(0,a.useEffect)(()=>{p&&v(function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t,r=arguments.length>3?arguments[3]:void 0,s=arguments.length>4?arguments[4]:void 0;if(!o(e))return e;let a=r===s||r.startsWith("".concat(s,"/"));return(t!==n||a)&&null!=s?l(e,s):e}(n,u,g,p,f))},[g,n,u,p,f]),a.createElement(c.default,(0,r.g)({ref:t,href:x,locale:u,localeCookie:d},h))});d.displayName="ClientLink"},48706:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var r=n(99376),s=n(526);let a="locale";function i(){let e;let t=(0,r.useParams)();try{e=(0,s.useLocale)()}catch(n){if("string"!=typeof(null==t?void 0:t[a]))throw n;e=t[a]}return e}},29985:function(e,t,n){"use strict";n.r(t),t.default={src:"/_next/static/media/about-us-image.267ed102.webp",height:1279,width:1920,blurDataURL:"data:image/webp;base64,UklGRj4AAABXRUJQVlA4IDIAAACQAQCdASoIAAUAAkA4JZgAAudZtgAA/uYL+8l5/VVhaFlCK8//pnoGQ+Tg9E15n5nAAA==",blurWidth:8,blurHeight:5}}},function(e){e.O(0,[6990,6290,8094,2586,3145,2971,2117,1744],function(){return e(e.s=50903)}),_N_E=e.O()}]);