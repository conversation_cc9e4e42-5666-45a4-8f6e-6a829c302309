(()=>{var e={};e.id=8321,e.ids=[8321],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},73624:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>c}),s(61159),s(55695),s(3929),s(84448),s(81729),s(90996);var a=s(30170),r=s(45002),l=s(83876),i=s.n(l),n=s(66299),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let c=["",{children:["[locale]",{children:["(user-profile)",{children:["message",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,61159)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user-profile)\\message\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,55695)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user-profile)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,3929)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user-profile)\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,80603))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,84448)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,81729)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(s.t.bind(s,90996,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user-profile)\\message\\page.tsx"],m="/[locale]/(user-profile)/message/page",u={require:s,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/[locale]/(user-profile)/message/page",pathname:"/[locale]/message",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},89449:(e,t,s)=>{let a={ace39bf07124e0ba39e8486050a53bc79b0621b3:()=>Promise.resolve().then(s.bind(s,18714)).then(e=>e.default)};async function r(e,...t){return(await a[e]()).apply(null,t)}e.exports={ace39bf07124e0ba39e8486050a53bc79b0621b3:r.bind(null,"ace39bf07124e0ba39e8486050a53bc79b0621b3")}},43096:(e,t,s)=>{Promise.resolve().then(s.bind(s,97617)),Promise.resolve().then(s.bind(s,66149)),Promise.resolve().then(s.bind(s,73584)),Promise.resolve().then(s.bind(s,17027)),Promise.resolve().then(s.bind(s,26793)),Promise.resolve().then(s.bind(s,70697)),Promise.resolve().then(s.bind(s,92941))},97617:(e,t,s)=>{"use strict";s.d(t,{ChatDetail:()=>E});var a=s(97247),r=s(58053),l=s(74831),i=s(83078),n=s(10906),o=s(16718),c=s(30250),d=s(2095),m=s(77940),u=s(84879),x=s(28964),p=s.n(x),h=s(27402),f=s(88964),g=s(34523),y=s.n(g),j=s(74993);let v=e=>j.v.post("/translate",e);var b=s(88111),N=s(25008),w=s(165);function k({createdAt:e,displayAs:t,isRead:s,isSent:l,text:i,code:o,isSeeker:c}){let d=(0,u.useTranslations)("component"),{toast:m}=(0,n.pm)(),[p,h]=(0,x.useState)(!1),[f,g]=(0,x.useState)(null),j=(0,b.D)({mutationFn:async e=>await v({text:e})}),k=t==o,P=k?"items-end":"items-start",R=async()=>{if(null!==f){h(e=>!e);return}try{let e=await j.mutateAsync(i);h(e=>!e);let t=e.data.translatedText.translatedText;g(t)}catch(e){m({title:"there's issue with translations"})}};return(0,a.jsxs)(w.E.div,{initial:{opacity:0,translateY:20},animate:{opacity:1,translateY:0},transition:{duration:.2,ease:"easeInOut"},className:`w-full flex flex-col ${P} gap-2`,children:[(0,a.jsxs)("div",{className:(0,N.cn)("text-sm p-4 rounded-sm bg-background max-w-[256px] md:max-w-[348px]",c?"bg-seekers-primary/5":""),children:[a.jsx("div",{children:p&&f||i}),!k&&a.jsx(r.z,{variant:"ghost",className:"h-fit w-fit px-0 text-neutral-light font-normal",size:"sm",onClick:()=>R(),children:d(p?"cta.seeOriginal":"cta.seeTranslation")})]}),a.jsx("div",{className:`flex gap-1 ${P} text-xs text-neutral`,children:a.jsx("p",{children:y()(e).format("HH:mm")})})]})}var P=s(92894);function R({messages:e,chatEnded:t}){let s=(0,u.useTranslations)("seeker"),r="",{code:l}=(0,P.L)(e=>e.seekers);return(0,a.jsxs)(a.Fragment,{children:[e.map((e,t)=>{let i=e.createdAt,n=y()(),o=""===r||y()(r).isBefore(y()(i),"day"),c=y()(i).isSame(n,"day")?s("misc.today"):y()(i).format("DD-MMM-YY");return r=i,(0,a.jsxs)(p().Fragment,{children:[o&&a.jsx("div",{className:"w-full flex justify-center sticky top-0",children:a.jsx(f.C,{variant:"outline",className:"border-neutral-300 text-neutral-500 bg-seekers-primary-lighter min-w-[84px] text-center flex items-center justify-center",children:c})}),a.jsx("div",{className:"w-full space-y-2 my-2",children:a.jsx(k,{...e,code:l,isSeeker:!0})})]},t)}),t&&a.jsx(k,{code:"000",text:s("message.textChatEnded"),createdAt:e[e.length-1]?.createdAt||y()().format("DD-MM-YYYY"),displayAs:"",displayName:"",id:"000",isRead:!0,isSent:!0,status:""})]})}var C=s(81775),z=s(70170),S=s(61600);function E(){let e=(0,u.useTranslations)("seeker");(0,u.useLocale)();let{currentLayout:t,roomId:s,chatDetail:p,participant:g,setlayout:y}=(0,c.R)(),j=(0,x.useRef)(null),v=(0,x.useRef)(null),b=(0,x.useRef)(null),[w,k]=(0,x.useState)(""),{toast:P}=(0,n.pm)(),E=(0,i.R)(),T=()=>{s&&""!=w.trim()&&(w.length>o.xm&&P({title:e("info.messageTooLong.title"),description:e("info.messageTooLong.description",{count:o.xm})}),E.sendMessage(w,s),k(""))},_=e=>{if(e.length>=o.xm){k(e.slice(0,o.xm));return}k(e)};return a.jsx("div",{className:` flex flex-col w-full max-sm:bg-white bg-seekers-primary-light/10 h-full pb-4 px-6 pr-3 md:max-h-full  md:rounded-lg relative 
  ${s||"detail-chat"==t?"max-sm:w-screen max-sm:left-0 max-sm:px-2 max-sm:bottom-0 max-sm:h-screen max-sm:fixed max-sm:inset-0 max-sm:z-20":"hidden"}`,children:s?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{ref:j,className:"flex bg-seekers-primary-light/0 w-full absolute gap-2 items-center left-0 md:px-6 pl-1 px-3 py-4 top-0 z-20",children:[a.jsx(r.z,{variant:"ghost",size:"icon",className:"lg:hidden",onClick:()=>E.leaveRoom(),children:a.jsx(m.Z,{className:"h-5 w-5"})}),(0,a.jsxs)("div",{className:"flex h-10 w-fit cursor-pointer gap-2",onClick:()=>y("detail-user"),children:[(0,a.jsxs)(S.qE,{className:(0,N.cn)("w-10 h-10 rounded-full bg-seekers-text-lighter"),children:[a.jsx(S.F$,{src:g?.property?.image||g?.image||"",className:"border"}),a.jsx(S.Q5,{className:"bg-transparent text-white",children:(0,a.jsxs)("span",{children:[g?.fullName[0][0],g?.fullName[g?.fullName.length/2]?.[0]||""]})})]}),(0,a.jsxs)("div",{children:[a.jsx("h2",{children:a.jsx(h.b,{category:"",name:g?.property?.title||g?.fullName||""})}),(0,a.jsxs)("div",{className:"flex flex-wrap gap-2 items-center",children:[g?.property?.title&&a.jsx("p",{className:"text-xs",children:g?.fullName}),(0,l.Yj)(g?.status||"")?a.jsx(f.C,{variant:"outline",className:"border-seekers-text-lighter text-seekers-text-light mt-1",children:e("message.waitingResponse")}):(0,l.GB)(g?.status||"")?a.jsx(f.C,{variant:"outline",className:"border-seekers-text-lighter text-seekers-text-light mt-1",children:e("message.chatEnded")}):a.jsx(a.Fragment,{})]})]})]}),a.jsx("div",{className:"flex-grow"})]}),a.jsx("div",{className:"flex-1 mt-[90px] overflow-hidden",children:(0,a.jsxs)(C.x,{className:"h-full max-sm:px-3 overflow-y-auto pr-3",children:[a.jsx(R,{messages:p}),a.jsx("div",{ref:b,className:"h-10"})]})}),a.jsx("div",{ref:v,className:"bg-seekers-primary-light/0 w-full absolute bottom-0 left-0 max-sm:px-3 pt-2 px-6 py-4",children:(0,a.jsxs)("div",{className:"flex bg-background border rounded-sm focus-within:border-neutral-light items-end overflow-hidden",children:[a.jsx(z.I,{value:w,maxLength:o.xm,onChange:e=>_(e.target.value),onKeyDown:e=>"Enter"===e.key?T():()=>{},className:(0,N.cn)("border-none focus:outline-none shadow-none focus-visible:ring-0"),disabled:(0,l.Yj)(g?.status||""),placeholder:e("form.placeholder.basePlaceholder",{field:e("form.field.message").toLowerCase()})}),(0,a.jsxs)("p",{className:"text-[10px] text-seekers-text-light px-2",children:[w.length,"/",o.xm]}),a.jsx(r.z,{type:"submit",variant:"default-seekers",className:"rounded-none text-white w-12",size:"icon",onClick:T,disabled:(0,l.Yj)(g?.status||""),children:a.jsx(d.kcA,{})})]})})]}):a.jsx(a.Fragment,{})})}},66149:(e,t,s)=>{"use strict";s.d(t,{default:()=>W});var a=s(97247),r=s(23236),l=s(40896),i=s(30250),n=s(84879),o=s(28964),c=s(58053),d=s(6047),m=s(70170),u=s(74831),x=s(15916),p=s(2095),h=s(50555),f=s(98969),g=s(16718),y=s(47751),j=s(2704),v=s(34631),b=s(52208),N=s(52164),w=s(79470),k=s(10906),P=s(41755);function R({submitHandler:e}){let t=(0,n.useTranslations)("seeker"),s=(0,n.useLocale)(),l=(0,P.NL)(),i=function(){let e=(0,n.useTranslations)("seeker");return y.z.object({text:y.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.message")})}).min(g.vQ,{message:e("form.utility.minimumLength",{field:e("form.field.message"),length:g.vQ})}).max(g.xm,{message:e("form.utility.maximumLength",{field:e("form.field.message"),length:g.xm})})})}(),o=(0,w.$)(s),d=(0,j.cI)({resolver:(0,v.F)(i),defaultValues:{text:""}}),{toast:m}=(0,k.pm)();async function u(s){if(s.text.trim().length<g.vQ){m({title:t("error.messageTooShort.title"),description:t("error.messageTooShort.description"),variant:"destructive"});return}let a={category:"CUSTOMER_SUPPORT",requested_by:"CLIENT",message:s.text};try{await o.mutateAsync(a),l.invalidateQueries({queryKey:[r.J]}),m({title:t("success.sendMessageToCs.title"),description:t("success.sendMessageToCs.description")}),e()}catch(e){m({title:t("error.failedSendMessage.title"),description:e.response?.data.message||"",variant:"destructive"})}}return a.jsx("div",{className:"w-full space-y-2",children:(0,a.jsxs)(b.l0,{...d,children:[a.jsx("form",{onSubmit:d.handleSubmit(u),className:"z-50",children:a.jsx(N.Z,{form:d,label:"",name:"text",placeholder:t("form.placeholder.example.requestHelpToCs")})}),a.jsx(c.z,{variant:"default-seekers",loading:o.isPending,onClick:()=>u(d.getValues()),className:"min-w-40 max-sm:w-full",children:t("cta.sendRequest")})]})})}var C=s(15238);function z({customTrigger:e}){let t=(0,n.useTranslations)("seeker"),[s,r]=(0,o.useState)(!1);return(0,a.jsxs)(h.Z,{open:s,setOpen:r,openTrigger:e||a.jsx(c.z,{className:"w-full border-seekers-primary text-seekers-textz hover:bg-seekers-primary/30",size:"sm",variant:"outline",children:t("cta.chatCustomerService")}),children:[a.jsx(C.Z,{className:"text-start px-0",children:a.jsx(f.$N,{className:"font-semibold",children:t("message.chatCs.title")})}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("p",{children:t("message.chatCs.description")}),a.jsx(R,{submitHandler:()=>r(!1)})]})]})}var S=s(88459);function E(){let e=(0,n.useTranslations)("seeker"),t=u.d9,[s,r]=(0,o.useState)("");(0,x.N)(s);let{createQueryString:i,searchParams:h,removeQueryParam:f}=(0,l.Z)(),g=e=>{let t=h.get("filter")||"";if(!t){i("filter",e);return}if(t?.includes(e)){let s=t.replaceAll(","," ").replace(e,"").trim(),a=s.replace(/\s+/g," ");if(s.length<1)f(["filter"]);else{let e=a.split(" ");e.filter(e=>""!==e),i("filter",e.toString())}}else{let s=t.split(",");s.push(e),i("filter",s.toString())}};return(0,a.jsxs)("div",{className:"flex gap-2 justify-between w-full ",children:[a.jsx(m.I,{placeholder:e("message.searchChat"),className:"flex-grow h-8 border-seekers-text-lighter placeholder:text-seekers-text-lighter",value:s,onChange:e=>r(e.target.value)}),(0,a.jsxs)(d.h_,{children:[a.jsx(d.$F,{asChild:!0,children:(0,a.jsxs)(c.z,{variant:"outline",size:"sm",className:"ml-auto h-8 flex text-seekers-text border-seekers-text-lighter",children:[a.jsx(p.hsZ,{className:"mr-2 h-4 w-4"}),e("cta.filter")]})}),a.jsx(d.AW,{align:"end",className:"w-fit",children:Object.values(t).map(t=>a.jsx(d.bO,{className:"capitalize",checked:!h.get("filter")?.includes(t),onCheckedChange:()=>g(t),children:function(e,t){switch(e){case u.jy.accountManager:return t("message.category.accountManager");case u.jy.customerSupport:return t("message.category.customerSupport");case u.jy.seekers:return t("message.category.seekers");case u.d9.owner:return t("message.category.owner");default:return""}}(t,e)},t))})]}),a.jsx(z,{customTrigger:a.jsx("div",{className:"w-16 h-8",children:a.jsx(S.Z,{trigger:a.jsx(c.z,{size:"icon",className:"w-full h-full border border-seekers-text-lighter",variant:"outline",children:a.jsx(p.AMp,{className:"!h-3 !w-3"})}),content:a.jsx("p",{className:"text-seekers-primary",children:e("misc.chatCustomerSupport")})})})})]})}var T=s(81775),_=s(91897),q=s(92894),A=s(27402),I=s(25008),M=s(83078),Z=s(61600),O=s(30922);function F({participant:e,lastMessages:t,code:s,category:r}){let{setRoomId:l,setlayout:n,roomId:o,setParticipant:c}=(0,i.R)(e=>e),{leaveRoom:d}=(0,M.R)(),{code:m}=(0,q.L)(e=>e.seekers),u=t.displayAs!==m,x=(e?.fullName||"").trim().split(" "),p=x.length;return t?a.jsx("div",{className:`rounded-lg w-full overscroll-none ${o==s?"bg-seekers-primary-lighter/30":""} hover:bg-seekers-primary-lighter/60 py-4 px-2`,onClick:()=>{s!=o&&(O.W.emit("joinRoomChat",{code:o},()=>{}),d(),l(s),n("detail-chat"),c({category:e?.category||"",email:e?.email||"",fullName:e?.fullName||"",id:e?.id||"",phoneNumber:e?.phoneNumber||"",status:e?.status||"",image:e?.image||"",property:{image:e?.property?.image||"",title:e?.property?.title||""}}))},children:(0,a.jsxs)("div",{className:"flex gap-2 ",children:[a.jsx("div",{className:"min-w-9 !w-9 !h-9 aspect-square rounded-full bg-neutral-200 relative overflow-hidden",children:(0,a.jsxs)(Z.qE,{className:(0,I.cn)("w-full rounded-full bg-seekers-text-lighter"),children:[a.jsx(Z.F$,{src:e?.property?.image||e?.image||"",className:"border"}),a.jsx(Z.Q5,{className:"bg-transparent text-white",children:(0,a.jsxs)("span",{children:[x[0][0],x[p/2]?.[0]||""]})})]})}),(0,a.jsxs)("div",{className:"flex-grow",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[a.jsx(A.b,{name:e?.property?.title||e?.fullName,category:""}),a.jsx("p",{className:"text-[10px] text-seekers-text-light",children:(0,I.g6)(t?.createdAt)})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[a.jsx("p",{className:"text-xs text-seekers-text-light line-clamp-1",children:t?.text}),!t.isRead&&u?a.jsx("div",{className:"w-2 aspect-square rounded-full bg-seekers-primary"}):a.jsx(a.Fragment,{})]})]})]})}):a.jsx(a.Fragment,{})}function W(){let e=(0,n.useTranslations)("seeker"),{currentLayout:t,allChat:s,setAllChat:c,setRoomId:d}=(0,i.R)(),m=(0,n.useLocale)(),{searchParams:u}=(0,l.Z)(),x=u.get("search")||"",p=(0,r.Z)({search:x},m);return(0,o.useEffect)(()=>{if(!p.data?.data)return;let e=u.get("filter")||"",t=p.data?.data;t.length<1&&d(""),e?c(t.filter(t=>!e||!e?.includes(t.category))):c(t)},[p.data?.data,u.get("search"),u.get("filter")]),(0,a.jsxs)("div",{className:`space-y-8 max-sm:space-y-4 md:max-w-xs flex flex-col max-sm:h-fit md:overflow-hidden ${"list"==t?"":"max-lg:hidden"} min-w-[300px] md:max-lg:min-w-full max-sm:w-full`,children:[a.jsx(E,{}),(0,a.jsxs)(T.x,{className:"overflow-y-auto md:flex-grow",children:[p.isPending?a.jsx("div",{className:"w-full space-y-2",children:[,,,].fill(0).map((e,t)=>a.jsx(_.O,{className:"w-full h-9"},t))}):a.jsx(a.Fragment,{children:s.length<1?a.jsx("p",{className:"text-center text-seekers-text-light",children:e("message.notMessageList")}):s.map((e,t)=>a.jsx(F,{...e},t))}),a.jsx("div",{className:"md:hidden h-20"})]})]})}},73584:(e,t,s)=>{"use strict";s.d(t,{default:()=>h});var a=s(97247),r=s(61600),l=s(58053),i=s(81775),n=s(25008),o=s(30250),c=s(77940),d=s(37013);let m=(0,s(26323).Z)("ArrowUpRight",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]]);var u=s(84879),x=s(44597),p=s(75476);function h(){let e=(0,u.useTranslations)("seeker"),{currentLayout:t,setlayout:s}=(0,o.R)(),{participant:h}=(0,o.R)();return(0,a.jsxs)("div",{className:(0,n.cn)("relative","detail-user"==t?"md:rounded-lg w-full h-screen md:min-w-[300px] md:w-[300px] md:h-full bg-seekers-primary-light/10 flex flex-col items-center space-y-4 py-8 px-6 relative max-sm:fixed max-sm:top-0 max-sm:left-0 max-sm:z-50 max-sm:bg-background":"hidden"),children:[a.jsx(l.z,{variant:"ghost",className:"shadow-none absolute left-1 md:hidden top-[18px]",size:"icon",onClick:()=>s("detail-chat"),children:a.jsx(c.Z,{})}),a.jsx(l.z,{variant:"ghost",className:"shadow-none absolute max-sm:hidden right-3 top-1",size:"icon",onClick:()=>s("detail-chat"),children:a.jsx(d.Z,{})}),(0,a.jsxs)(i.x,{className:"w-full h-full ",children:[(0,a.jsxs)(r.qE,{className:(0,n.cn)("w-28 h-28 rounded-xl bg-seekers-text-lighter mx-auto"),children:[a.jsx(r.F$,{src:h?.property?.image||h?.image||"",className:"border"}),a.jsx(r.Q5,{className:"bg-transparent text-2xl text-white",children:(0,a.jsxs)("span",{children:[h?.fullName[0][0],h?.fullName[h.fullName.length/2]?.[0]||""]})})]}),a.jsx(p.rU,{className:"flex justify-center !mt-2 items-center",href:`/${h?.property?.title?.replaceAll(" ","-")}?code=${h?.property?.id}`,target:"_blank",children:a.jsx(m,{className:"h-5 text-seekers-text w-5"})}),(0,a.jsxs)("div",{children:[a.jsx("h2",{className:"text-base text-center font-semibold",children:h?.property?.title}),a.jsx("p",{className:"text-center text-neutral-600",children:h?.fullName}),h?.middleman?(0,a.jsxs)(a.Fragment,{children:[a.jsx("p",{className:"text-center text-seekers-text-lighter text-xs",children:e("misc.representedBy")}),a.jsx("p",{className:"text-center",children:h.middleman.name})]}):a.jsx(a.Fragment,{})]}),(0,a.jsxs)("div",{className:"h-fit w-full py-4 space-y-4",children:[a.jsx("h2",{className:"text-center text-seekers-text-light text-sm",children:e("misc.otherProperties")}),a.jsx("div",{className:"w-full space-y-2",children:h?.moreProperty?.map(e=>a.jsxs(p.rU,{className:"flex w-full gap-2",href:e.title+"?code="+e.id,target:"_blank",children:[a.jsx(x.default,{className:"h-10 rounded-full w-10 min-w-10",src:e.image||"",alt:"",width:48,height:48}),a.jsx("p",{className:"font-semibold line-clamp-2",children:e.title})]},e.id))})]})]})]})}},27402:(e,t,s)=>{"use strict";s.d(t,{b:()=>r});var a=s(97247);let r=({name:e,category:t})=>a.jsx("p",{className:"font-semibold flex-grow line-clamp-1",children:e})},52164:(e,t,s)=>{"use strict";s.d(t,{Z:()=>c});var a=s(97247),r=s(52208),l=s(93572),i=s(28964),n=s(25008);let o=i.forwardRef(({className:e,...t},s)=>a.jsx("textarea",{className:(0,n.cn)("flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...t}));function c({form:e,label:t,name:s,placeholder:i,description:n,inputProps:c}){return a.jsx(r.Wi,{control:e.control,name:s,render:({field:e})=>a.jsx(l.Z,{label:t,description:n,children:a.jsx(o,{placeholder:i,className:"resize-none",...e,...c,rows:10})})})}o.displayName="Textarea"},88964:(e,t,s)=>{"use strict";s.d(t,{C:()=>n});var a=s(97247);s(28964);var r=s(87972),l=s(25008);let i=(0,r.j)("cursor-pointer inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs h-fit font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground  hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground  hover:bg-destructive/80",outline:"text-foreground",seekers:"border-transparent bg-seekers-primary/30 text-seekers-primary hover:bg-seekers-primary/80 rounded-full px-4 py-1.5"}},defaultVariants:{variant:"default"}});function n({className:e,variant:t,...s}){return a.jsx("div",{className:(0,l.cn)(i({variant:t}),e,"pointer-events-none"),...s})}},81775:(e,t,s)=>{"use strict";s.d(t,{B:()=>o,x:()=>n});var a=s(97247),r=s(28964),l=s(75500),i=s(25008);let n=r.forwardRef(({className:e,children:t,...s},r)=>(0,a.jsxs)(l.fC,{ref:r,className:(0,i.cn)("relative overflow-hidden",e),...s,children:[a.jsx(l.l_,{className:"h-full w-full rounded-[inherit]",children:t}),a.jsx(o,{}),a.jsx(l.Ns,{})]}));n.displayName=l.fC.displayName;let o=r.forwardRef(({className:e,orientation:t="vertical",...s},r)=>a.jsx(l.gb,{ref:r,orientation:t,className:(0,i.cn)("flex touch-none select-none transition-colors","vertical"===t&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===t&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",e),...s,children:a.jsx(l.q4,{className:"relative flex-1 rounded-full bg-border"})}));o.displayName=l.gb.displayName},79470:(e,t,s)=>{"use strict";s.d(t,{$:()=>i});var a=s(7392),r=s(60491),l=s(88111);let i=(e="en")=>(0,l.D)({mutationFn:e=>(0,a.zn)(e),onSuccess:async t=>{let s=t.data.data;return await (0,r.ix)(s.code,e)}})},23236:(e,t,s)=>{"use strict";s.d(t,{J:()=>l,Z:()=>i});var a=s(60491),r=s(9190);let l="chat-list";function i(e,t="en"){let{search:s,status:i}=e;return console.log(t),(0,r.a)({queryKey:[l,s,i,t],queryFn:async()=>await (0,a.b5)({search:s||""},t),retry:0})}},74831:(e,t,s)=>{"use strict";s.d(t,{GB:()=>n,Yj:()=>i,d9:()=>r,jy:()=>a});let a={accountManager:"LISTING",customerSupport:"CUSTOMER_SUPPORT",seekers:"SEEKER_OWNER"},r={customerSupport:"CUSTOMER_SUPPORT",owner:"SEEKER_OWNER"},l={waitingResponse:"WAITING_FOR_RESPONSE",endedResponse:"CONVERSATION_ENDED"},i=e=>e==l.waitingResponse||e==l.endedResponse,n=e=>e==l.endedResponse},7392:(e,t,s)=>{"use strict";s.d(t,{ev:()=>i,rm:()=>n,zG:()=>l,zn:()=>r});var a=s(74993);let r=e=>a.apiClient.post("room-chats",e),l=e=>a.apiClient.get(`room-chats?search=${e.search}`),i=e=>a.apiClient.get(`room-chats/${e}`),n=e=>a.apiClient.put(`room-chats/${e}`)},60491:(e,t,s)=>{"use strict";s.d(t,{b5:()=>i,ix:()=>n});var a=s(29178),r=s(7392),l=s(23734);async function i(e,t="en"){try{let s=(await (0,r.zG)(e)).data.data;return{data:(0,l.ug)(s,t),meta:void 0}}catch(e){return console.log(e),{error:(0,a.q)(e)}}}async function n(e,t="en"){try{if(!e)return{error:"Id required"};let s=(await (0,r.ev)(e)).data.data;return{data:(0,l.eN)(s,t),meta:void 0}}catch(e){return console.log(e),{error:(0,a.q)(e)}}}},83078:(e,t,s)=>{"use strict";s.d(t,{R:()=>x});var a=s(30250),r=s(28964),l=s(23734),i=s(30922),n=s(7392),o=s(88111);let c=()=>(0,o.D)({mutationFn:e=>(0,n.rm)(e)});var d=s(60491),m=s(9190),u=s(84879);let x=()=>{let{roomId:e,participant:t,updateSpecificAllChat:s,setchatDetail:n,setParticipant:o,updatechatDetail:x,setRoomId:p,setlayout:h}=(0,a.R)(e=>e),f=c(),g=function(e,t=!0,s="en"){return(0,m.a)({queryKey:["chat-detail",e],queryFn:()=>(0,d.ix)(e,s),retry:0,enabled:t})}(e||"",void 0!==e,(0,u.useLocale)());return(0,r.useEffect)(()=>(i.W.connected||i.W.connect(),()=>{i.W.disconnect()}),[]),(0,r.useEffect)(()=>{if(!e||!g.data?.data)return;let t=g.data.data;n(t.allMessages||[]),o(t.participant)},[g?.data?.data,e,o,n]),{sendMessage:(t,a)=>{i.W.connected||i.W.connect(),i.W.emit("sendMessage",{code:e,requested_by:"CLIENT",message:t,receiver:a},t=>{let a=(0,l.Z5)(t);x(a),s(a,!1,e)})},createChatRoom:e=>{i.W.connected||i.W.connect(),i.W.emit("createRoomChat",{requested_by:"CLIENT",receiver:e},e=>{})},leaveRoom:()=>{p(""),n([]),o(),h("list"),i.W.emit("leaveRoomChat",{code:e},()=>{p("")})},ownerUpdateMessageStatus:async()=>{e&&t?.category==="SEEKER_OWNER"&&"WAITING_FOR_RESPONSE"===t.status&&await f.mutateAsync(e)},joinRoom:t=>{e&&i.W.emit("leaveRoomChat",{code:e}),i.W.emit("joinRoomChat",{code:t}),g.refetch()}}}},61159:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>v,generateMetadata:()=>j});var a=s(72051),r=s(79438),l=s(35243),i=s(4459),n=s(37170),o=s(53189),c=s(69385),d=s(93844);function m(){let e=(0,c.Z)("seeker");return(0,a.jsxs)(r.Z,{className:(0,n.cn)("mx-auto flex gap-2 items-end max-sm:px-0 space-y-6 pt-6 max-sm:pt-0 w-full"),children:[a.jsx(i.vP,{className:"items-end -ml-2"}),a.jsx(l.aG,{className:"",children:(0,a.jsxs)(l.Jb,{className:"space-x-4 sm:gap-0",children:[a.jsx(l.gN,{className:"text-seekers-text font-medium text-sm",children:(0,a.jsxs)(d.rU,{href:"/",className:"flex gap-2.5 items-center",children:[a.jsx(o.Z,{className:"w-4 h-4",strokeWidth:1}),e("misc.home")]})}),a.jsx(l.bg,{className:"text-seekers-text font-medium text-sm w-3 h-fit text-center",children:"/"}),a.jsx(l.gN,{className:"capitalize text-seekers-text font-medium text-sm",children:e("accountAndProfile.message")})]})})]})}var u=s(29507),x=s(83266),p=s(45347);let h=(0,p.createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\app\[locale]\(user-profile)\message\chat-list.tsx#default`),f=(0,p.createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\app\[locale]\(user-profile)\message\chat-detail.tsx#ChatDetail`),g=(0,p.createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\app\[locale]\(user-profile)\message\participant-detail.tsx#default`);var y=s(92898);async function j(){let e=await (0,u.Z)("seeker"),t=await (0,x.Z)()||d.DI.defaultLocale,s=process.env.USER_DOMAIN||"https://www.property-plaza.com/";return{title:e("metadata.message.title"),description:e("metadata.message.description"),alternates:{canonical:s+t+y.in,languages:{en:s+"en"+y.in,id:s+"id"+y.in,"x-default":s+y.in.replace("/","")}},openGraph:{title:e("metadata.message.title"),description:e("metadata.message.description"),images:[{url:s+"og.png",width:1200,height:630,alt:"Property Plaza"}],type:"website",url:s+t+y.in,countryName:"Indonesia",emails:"<EMAIL>",locale:t,alternateLocale:d.DI.locales,siteName:"Property plaza"},applicationName:"Property plaza",twitter:{card:"summary_large_image",title:e("metadata.message.title"),description:e("metadata.message.description"),images:[s+"og.jpg"]},robots:{index:!1,follow:!1,nocache:!1}}}function v(){return(0,a.jsxs)("div",{className:"h-full max-sm:space-y-6 md:flex md:flex-col items-start md:overflow-hidden max-h-full md:gap-6 ",children:[a.jsx(m,{}),(0,a.jsxs)(r.Z,{className:"flex gap-8 space-y-0 w-full max-sm:pb-4 md:max-h-[calc(100%-68px-24px)] flex-grow max-md:px-0 md:pr-0",children:[" ",a.jsx(h,{}),a.jsx(f,{}),a.jsx(g,{})]})]})}},18714:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var a=s(28713);s(9640);var r=s(53020);async function l(e,t,s){let a=(0,r.cookies)(),l=a.get("tkn")?.value;try{let a=await fetch(e,{method:t,headers:{Authorization:`Bearer ${l}`,"Content-Type":"application/json"},...s});if(!a.ok)return{data:null,meta:void 0,error:{status:a.status,name:a.statusText,message:await a.text()||"Unexpected error",details:{}}};let r=await a.json();if(r.error)return{data:null,meta:void 0,error:r.error};return{data:r.data,meta:r.meta,error:void 0}}catch(e){return{data:null,meta:void 0,error:{status:500,details:{cause:e.cause},message:e.message,name:e.name}}}}(0,s(83557).h)([l]),(0,a.j)("ace39bf07124e0ba39e8486050a53bc79b0621b3",l)},29507:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});var a=s(26269),r=s(95817),l=s(60434),i=(0,a.cache)(async function(e){let t,s;"string"==typeof e?t=e:e&&(s=e.locale,t=e.namespace);let a=await (0,l.Z)(s);return(0,r.eX)({...a,namespace:t,messages:a.messages})})}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[9379,5063,4916,9467,4859,5268,3832,5500,6666,9965,7496],()=>s(73624));module.exports=a})();