"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3682],{10407:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(79205).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},93920:function(e,t,n){n.d(t,{VY:function(){return ew},zt:function(){return ey},fC:function(){return eg},xz:function(){return ex}});var r,o=n(2265);function i(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}function l(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function a(...e){return o.useCallback(function(...e){return t=>{let n=!1,r=e.map(e=>{let r=l(e,t);return n||"function"!=typeof r||(n=!0),r});if(n)return()=>{for(let t=0;t<r.length;t++){let n=r[t];"function"==typeof n?n():l(e[t],null)}}}}(...e),e)}var u=n(57437);function s(e,t=[]){let n=[],r=()=>{let t=n.map(e=>o.createContext(e));return function(n){let r=n?.[e]||t;return o.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let i=o.createContext(r),l=n.length;n=[...n,r];let a=t=>{let{scope:n,children:r,...a}=t,s=n?.[e]?.[l]||i,d=o.useMemo(()=>a,Object.values(a));return(0,u.jsx)(s.Provider,{value:d,children:r})};return a.displayName=t+"Provider",[a,function(n,a){let u=a?.[e]?.[l]||i,s=o.useContext(u);if(s)return s;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return o.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}var d=n(54887),c=n(98482),p=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let n=o.forwardRef((e,n)=>{let{asChild:r,...o}=e,i=r?c.g7:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,u.jsx)(i,{...o,ref:n})});return n.displayName=`Primitive.${t}`,{...e,[t]:n}},{}),f=n(26606),v=n(91096),m="dismissableLayer.update",h=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),y=o.forwardRef((e,t)=>{var n,l;let{disableOutsidePointerEvents:s=!1,onEscapeKeyDown:d,onPointerDownOutside:c,onFocusOutside:y,onInteractOutside:w,onDismiss:b,...E}=e,C=o.useContext(h),[T,N]=o.useState(null),P=null!==(l=null==T?void 0:T.ownerDocument)&&void 0!==l?l:null===(n=globalThis)||void 0===n?void 0:n.document,[,O]=o.useState({}),R=a(t,e=>N(e)),L=Array.from(C.layers),[j]=[...C.layersWithOutsidePointerEventsDisabled].slice(-1),A=L.indexOf(j),M=T?L.indexOf(T):-1,k=C.layersWithOutsidePointerEventsDisabled.size>0,D=M>=A,S=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,f.W)(e),i=o.useRef(!1),l=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){x("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",l.current),l.current=t,n.addEventListener("click",l.current,{once:!0})):t()}else n.removeEventListener("click",l.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",l.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...C.branches].some(e=>e.contains(t));!D||n||(null==c||c(e),null==w||w(e),e.defaultPrevented||null==b||b())},P),_=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,f.W)(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&x("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;[...C.branches].some(e=>e.contains(t))||(null==y||y(e),null==w||w(e),e.defaultPrevented||null==b||b())},P);return(0,v.e)(e=>{M!==C.layers.size-1||(null==d||d(e),!e.defaultPrevented&&b&&(e.preventDefault(),b()))},P),o.useEffect(()=>{if(T)return s&&(0===C.layersWithOutsidePointerEventsDisabled.size&&(r=P.body.style.pointerEvents,P.body.style.pointerEvents="none"),C.layersWithOutsidePointerEventsDisabled.add(T)),C.layers.add(T),g(),()=>{s&&1===C.layersWithOutsidePointerEventsDisabled.size&&(P.body.style.pointerEvents=r)}},[T,P,s,C]),o.useEffect(()=>()=>{T&&(C.layers.delete(T),C.layersWithOutsidePointerEventsDisabled.delete(T),g())},[T,C]),o.useEffect(()=>{let e=()=>O({});return document.addEventListener(m,e),()=>document.removeEventListener(m,e)},[]),(0,u.jsx)(p.div,{...E,ref:R,style:{pointerEvents:k?D?"auto":"none":void 0,...e.style},onFocusCapture:i(e.onFocusCapture,_.onFocusCapture),onBlurCapture:i(e.onBlurCapture,_.onBlurCapture),onPointerDownCapture:i(e.onPointerDownCapture,S.onPointerDownCapture)})});function g(){let e=new CustomEvent(m);document.dispatchEvent(e)}function x(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});(t&&i.addEventListener(e,t,{once:!0}),o)?i&&d.flushSync(()=>i.dispatchEvent(l)):i.dispatchEvent(l)}y.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(h),r=o.useRef(null),i=a(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,u.jsx)(p.div,{...e,ref:i})}).displayName="DismissableLayerBranch";var w=n(99255),b=n(97859),E=n(50032),C=o.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,u.jsx)(p.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,u.jsx)("polygon",{points:"0,0 30,0 15,10"})})});C.displayName="Arrow";var T=n(61188),N=n(90420),P="Popper",[O,R]=s(P),[L,j]=O(P),A=e=>{let{__scopePopper:t,children:n}=e,[r,i]=o.useState(null);return(0,u.jsx)(L,{scope:t,anchor:r,onAnchorChange:i,children:n})};A.displayName=P;var M="PopperAnchor",k=o.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...i}=e,l=j(M,n),s=o.useRef(null),d=a(t,s);return o.useEffect(()=>{l.onAnchorChange((null==r?void 0:r.current)||s.current)}),r?null:(0,u.jsx)(p.div,{...i,ref:d})});k.displayName=M;var D="PopperContent",[S,_]=O(D),I=o.forwardRef((e,t)=>{var n,r,i,l,s,d,c,v;let{__scopePopper:m,side:h="bottom",sideOffset:y=0,align:g="center",alignOffset:x=0,arrowPadding:w=0,avoidCollisions:C=!0,collisionBoundary:P=[],collisionPadding:O=0,sticky:R="partial",hideWhenDetached:L=!1,updatePositionStrategy:A="optimized",onPlaced:M,...k}=e,_=j(D,m),[I,W]=o.useState(null),F=a(t,e=>W(e)),[B,Y]=o.useState(null),$=(0,N.t)(B),X=null!==(c=null==$?void 0:$.width)&&void 0!==c?c:0,V=null!==(v=null==$?void 0:$.height)&&void 0!==v?v:0,Z="number"==typeof O?O:{top:0,right:0,bottom:0,left:0,...O},q=Array.isArray(P)?P:[P],G=q.length>0,J={padding:Z,boundary:q.filter(U),altBoundary:G},{refs:K,floatingStyles:Q,placement:ee,isPositioned:et,middlewareData:en}=(0,b.YF)({strategy:"fixed",placement:h+("center"!==g?"-"+g:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return(0,E.Me)(...t,{animationFrame:"always"===A})},elements:{reference:_.anchor},middleware:[(0,b.cv)({mainAxis:y+V,alignmentAxis:x}),C&&(0,b.uY)({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?(0,b.dr)():void 0,...J}),C&&(0,b.RR)({...J}),(0,b.dp)({...J,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:l}=n.reference,a=t.floating.style;a.setProperty("--radix-popper-available-width","".concat(r,"px")),a.setProperty("--radix-popper-available-height","".concat(o,"px")),a.setProperty("--radix-popper-anchor-width","".concat(i,"px")),a.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}}),B&&(0,b.x7)({element:B,padding:w}),z({arrowWidth:X,arrowHeight:V}),L&&(0,b.Cp)({strategy:"referenceHidden",...J})]}),[er,eo]=H(ee),ei=(0,f.W)(M);(0,T.b)(()=>{et&&(null==ei||ei())},[et,ei]);let el=null===(n=en.arrow)||void 0===n?void 0:n.x,ea=null===(r=en.arrow)||void 0===r?void 0:r.y,eu=(null===(i=en.arrow)||void 0===i?void 0:i.centerOffset)!==0,[es,ed]=o.useState();return(0,T.b)(()=>{I&&ed(window.getComputedStyle(I).zIndex)},[I]),(0,u.jsx)("div",{ref:K.setFloating,"data-radix-popper-content-wrapper":"",style:{...Q,transform:et?Q.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:es,"--radix-popper-transform-origin":[null===(l=en.transformOrigin)||void 0===l?void 0:l.x,null===(s=en.transformOrigin)||void 0===s?void 0:s.y].join(" "),...(null===(d=en.hide)||void 0===d?void 0:d.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,u.jsx)(S,{scope:m,placedSide:er,onArrowChange:Y,arrowX:el,arrowY:ea,shouldHideArrow:eu,children:(0,u.jsx)(p.div,{"data-side":er,"data-align":eo,...k,ref:F,style:{...k.style,animation:et?void 0:"none"}})})})});I.displayName=D;var W="PopperArrow",F={top:"bottom",right:"left",bottom:"top",left:"right"},B=o.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=_(W,n),i=F[o.placedSide];return(0,u.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,u.jsx)(C,{...r,ref:t,style:{...r.style,display:"block"}})})});function U(e){return null!==e}B.displayName=W;var z=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,l;let{placement:a,rects:u,middlewareData:s}=t,d=(null===(n=s.arrow)||void 0===n?void 0:n.centerOffset)!==0,c=d?0:e.arrowWidth,p=d?0:e.arrowHeight,[f,v]=H(a),m={start:"0%",center:"50%",end:"100%"}[v],h=(null!==(i=null===(r=s.arrow)||void 0===r?void 0:r.x)&&void 0!==i?i:0)+c/2,y=(null!==(l=null===(o=s.arrow)||void 0===o?void 0:o.y)&&void 0!==l?l:0)+p/2,g="",x="";return"bottom"===f?(g=d?m:"".concat(h,"px"),x="".concat(-p,"px")):"top"===f?(g=d?m:"".concat(h,"px"),x="".concat(u.floating.height+p,"px")):"right"===f?(g="".concat(-p,"px"),x=d?m:"".concat(y,"px")):"left"===f&&(g="".concat(u.floating.width+p,"px"),x=d?m:"".concat(y,"px")),{data:{x:g,y:x}}}});function H(e){let[t,n="center"]=e.split("-");return[t,n]}o.forwardRef((e,t)=>{var n,r;let{container:i,...l}=e,[a,s]=o.useState(!1);(0,T.b)(()=>s(!0),[]);let c=i||a&&(null===(r=globalThis)||void 0===r?void 0:null===(n=r.document)||void 0===n?void 0:n.body);return c?d.createPortal((0,u.jsx)(p.div,{...l,ref:t}),c):null}).displayName="Portal";var Y=e=>{var t,n;let r,i;let{present:l,children:u}=e,s=function(e){var t,n;let[r,i]=o.useState(),l=o.useRef({}),a=o.useRef(e),u=o.useRef("none"),[s,d]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},o.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return o.useEffect(()=>{let e=$(l.current);u.current="mounted"===s?e:"none"},[s]),(0,T.b)(()=>{let t=l.current,n=a.current;if(n!==e){let r=u.current,o=$(t);e?d("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?d("UNMOUNT"):n&&r!==o?d("ANIMATION_OUT"):d("UNMOUNT"),a.current=e}},[e,d]),(0,T.b)(()=>{if(r){var e;let t;let n=null!==(e=r.ownerDocument.defaultView)&&void 0!==e?e:window,o=e=>{let o=$(l.current).includes(e.animationName);if(e.target===r&&o&&(d("ANIMATION_END"),!a.current)){let e=r.style.animationFillMode;r.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=e)})}},i=e=>{e.target===r&&(u.current=$(l.current))};return r.addEventListener("animationstart",i),r.addEventListener("animationcancel",o),r.addEventListener("animationend",o),()=>{n.clearTimeout(t),r.removeEventListener("animationstart",i),r.removeEventListener("animationcancel",o),r.removeEventListener("animationend",o)}}d("ANIMATION_END")},[r,d]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:o.useCallback(e=>{e&&(l.current=getComputedStyle(e)),i(e)},[])}}(l),d="function"==typeof u?u({present:s.isPresent}):o.Children.only(u),c=a(s.ref,(r=null===(t=Object.getOwnPropertyDescriptor(d.props,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in r&&r.isReactWarning?d.ref:(r=null===(n=Object.getOwnPropertyDescriptor(d,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning?d.props.ref:d.props.ref||d.ref);return"function"==typeof u||s.isPresent?o.cloneElement(d,{ref:c}):null};function $(e){return(null==e?void 0:e.animationName)||"none"}Y.displayName="Presence";var X=n(80886),V=o.forwardRef((e,t)=>(0,u.jsx)(p.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));V.displayName="VisuallyHidden";var[Z,q]=s("Tooltip",[R]),G=R(),J="TooltipProvider",K="tooltip.open",[Q,ee]=Z(J),et=e=>{let{__scopeTooltip:t,delayDuration:n=700,skipDelayDuration:r=300,disableHoverableContent:i=!1,children:l}=e,[a,s]=o.useState(!0),d=o.useRef(!1),c=o.useRef(0);return o.useEffect(()=>{let e=c.current;return()=>window.clearTimeout(e)},[]),(0,u.jsx)(Q,{scope:t,isOpenDelayed:a,delayDuration:n,onOpen:o.useCallback(()=>{window.clearTimeout(c.current),s(!1)},[]),onClose:o.useCallback(()=>{window.clearTimeout(c.current),c.current=window.setTimeout(()=>s(!0),r)},[r]),isPointerInTransitRef:d,onPointerInTransitChange:o.useCallback(e=>{d.current=e},[]),disableHoverableContent:i,children:l})};et.displayName=J;var en="Tooltip",[er,eo]=Z(en),ei=e=>{let{__scopeTooltip:t,children:n,open:r,defaultOpen:i=!1,onOpenChange:l,disableHoverableContent:a,delayDuration:s}=e,d=ee(en,e.__scopeTooltip),c=G(t),[p,f]=o.useState(null),v=(0,w.M)(),m=o.useRef(0),h=null!=a?a:d.disableHoverableContent,y=null!=s?s:d.delayDuration,g=o.useRef(!1),[x=!1,b]=(0,X.T)({prop:r,defaultProp:i,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(K))):d.onClose(),null==l||l(e)}}),E=o.useMemo(()=>x?g.current?"delayed-open":"instant-open":"closed",[x]),C=o.useCallback(()=>{window.clearTimeout(m.current),m.current=0,g.current=!1,b(!0)},[b]),T=o.useCallback(()=>{window.clearTimeout(m.current),m.current=0,b(!1)},[b]),N=o.useCallback(()=>{window.clearTimeout(m.current),m.current=window.setTimeout(()=>{g.current=!0,b(!0),m.current=0},y)},[y,b]);return o.useEffect(()=>()=>{m.current&&(window.clearTimeout(m.current),m.current=0)},[]),(0,u.jsx)(A,{...c,children:(0,u.jsx)(er,{scope:t,contentId:v,open:x,stateAttribute:E,trigger:p,onTriggerChange:f,onTriggerEnter:o.useCallback(()=>{d.isOpenDelayed?N():C()},[d.isOpenDelayed,N,C]),onTriggerLeave:o.useCallback(()=>{h?T():(window.clearTimeout(m.current),m.current=0)},[T,h]),onOpen:C,onClose:T,disableHoverableContent:h,children:n})})};ei.displayName=en;var el="TooltipTrigger",ea=o.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,l=eo(el,n),s=ee(el,n),d=G(n),c=a(t,o.useRef(null),l.onTriggerChange),f=o.useRef(!1),v=o.useRef(!1),m=o.useCallback(()=>f.current=!1,[]);return o.useEffect(()=>()=>document.removeEventListener("pointerup",m),[m]),(0,u.jsx)(k,{asChild:!0,...d,children:(0,u.jsx)(p.button,{"aria-describedby":l.open?l.contentId:void 0,"data-state":l.stateAttribute,...r,ref:c,onPointerMove:i(e.onPointerMove,e=>{"touch"===e.pointerType||v.current||s.isPointerInTransitRef.current||(l.onTriggerEnter(),v.current=!0)}),onPointerLeave:i(e.onPointerLeave,()=>{l.onTriggerLeave(),v.current=!1}),onPointerDown:i(e.onPointerDown,()=>{f.current=!0,document.addEventListener("pointerup",m,{once:!0})}),onFocus:i(e.onFocus,()=>{f.current||l.onOpen()}),onBlur:i(e.onBlur,l.onClose),onClick:i(e.onClick,l.onClose)})})});ea.displayName=el;var[eu,es]=Z("TooltipPortal",{forceMount:void 0}),ed="TooltipContent",ec=o.forwardRef((e,t)=>{let n=es(ed,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...i}=e,l=eo(ed,e.__scopeTooltip);return(0,u.jsx)(Y,{present:r||l.open,children:l.disableHoverableContent?(0,u.jsx)(em,{side:o,...i,ref:t}):(0,u.jsx)(ep,{side:o,...i,ref:t})})}),ep=o.forwardRef((e,t)=>{let n=eo(ed,e.__scopeTooltip),r=ee(ed,e.__scopeTooltip),i=o.useRef(null),l=a(t,i),[s,d]=o.useState(null),{trigger:c,onClose:p}=n,f=i.current,{onPointerInTransitChange:v}=r,m=o.useCallback(()=>{d(null),v(!1)},[v]),h=o.useCallback((e,t)=>{let n=e.currentTarget,r={x:e.clientX,y:e.clientY},o=function(e,t){let n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),i=Math.abs(t.left-e.x);switch(Math.min(n,r,o,i)){case i:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw Error("unreachable")}}(r,n.getBoundingClientRect());d(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:e.y>t.y?1:0),function(e){if(e.length<=1)return e.slice();let t=[];for(let n=0;n<e.length;n++){let r=e[n];for(;t.length>=2;){let e=t[t.length-1],n=t[t.length-2];if((e.x-n.x)*(r.y-n.y)>=(e.y-n.y)*(r.x-n.x))t.pop();else break}t.push(r)}t.pop();let n=[];for(let t=e.length-1;t>=0;t--){let r=e[t];for(;n.length>=2;){let e=n[n.length-1],t=n[n.length-2];if((e.x-t.x)*(r.y-t.y)>=(e.y-t.y)*(r.x-t.x))n.pop();else break}n.push(r)}return(n.pop(),1===t.length&&1===n.length&&t[0].x===n[0].x&&t[0].y===n[0].y)?t:t.concat(n)}(t)}([...function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n})}return r}(r,o),...function(e){let{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}(t.getBoundingClientRect())])),v(!0)},[v]);return o.useEffect(()=>()=>m(),[m]),o.useEffect(()=>{if(c&&f){let e=e=>h(e,f),t=e=>h(e,c);return c.addEventListener("pointerleave",e),f.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),f.removeEventListener("pointerleave",t)}}},[c,f,h,m]),o.useEffect(()=>{if(s){let e=e=>{let t=e.target,n={x:e.clientX,y:e.clientY},r=(null==c?void 0:c.contains(t))||(null==f?void 0:f.contains(t)),o=!function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let l=t[e].x,a=t[e].y,u=t[i].x,s=t[i].y;a>r!=s>r&&n<(u-l)*(r-a)/(s-a)+l&&(o=!o)}return o}(n,s);r?m():o&&(m(),p())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,f,s,p,m]),(0,u.jsx)(em,{...e,ref:l})}),[ef,ev]=Z(en,{isInside:!1}),em=o.forwardRef((e,t)=>{let{__scopeTooltip:n,children:r,"aria-label":i,onEscapeKeyDown:l,onPointerDownOutside:a,...s}=e,d=eo(ed,n),p=G(n),{onClose:f}=d;return o.useEffect(()=>(document.addEventListener(K,f),()=>document.removeEventListener(K,f)),[f]),o.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(d.trigger))&&f()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,f]),(0,u.jsx)(y,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:l,onPointerDownOutside:a,onFocusOutside:e=>e.preventDefault(),onDismiss:f,children:(0,u.jsxs)(I,{"data-state":d.stateAttribute,...p,...s,ref:t,style:{...s.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,u.jsx)(c.A4,{children:r}),(0,u.jsx)(ef,{scope:n,isInside:!0,children:(0,u.jsx)(V,{id:d.contentId,role:"tooltip",children:i||r})})]})})});ec.displayName=ed;var eh="TooltipArrow";o.forwardRef((e,t)=>{let{__scopeTooltip:n,...r}=e,o=G(n);return ev(eh,n).isInside?null:(0,u.jsx)(B,{...o,...r,ref:t})}).displayName=eh;var ey=et,eg=ei,ex=ea,ew=ec}}]);