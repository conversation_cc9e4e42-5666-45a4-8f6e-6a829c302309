"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4216],{4216:function(e,t,n){let r;n.d(t,{d:function(){return H}});var o=n(92360),a=n(2265);let i=a.createContext({drawerRef:{current:null},overlayRef:{current:null},scaleBackground:()=>{},onPress:()=>{},onRelease:()=>{},onDrag:()=>{},onNestedDrag:()=>{},onNestedOpenChange:()=>{},onNestedRelease:()=>{},openProp:void 0,dismissible:!1,handleOnly:!1,isOpen:!1,isDragging:!1,keyboardIsOpen:{current:!1},snapPointsOffset:null,snapPoints:null,modal:!1,shouldFade:!1,activeSnapPoint:null,onOpenChange:()=>{},setActiveSnapPoint:()=>{},visible:!1,closeDrawer:()=>{},setVisible:()=>{},direction:"bottom"}),l=()=>{let e=a.useContext(i);if(!e)throw Error("useDrawerContext must be used within a Drawer.Root");return e};!function(e){if(!e||"undefined"==typeof document)return;let t=document.head||document.getElementsByTagName("head")[0],n=document.createElement("style");n.type="text/css",t.appendChild(n),n.styleSheet?n.styleSheet.cssText=e:n.appendChild(document.createTextNode(e))}("[vaul-drawer]{touch-action:none;will-change:transform;transition:transform .5s cubic-bezier(.32, .72, 0, 1)}[vaul-drawer][vaul-drawer-direction=bottom]{transform:translate3d(0,100%,0)}[vaul-drawer][vaul-drawer-direction=top]{transform:translate3d(0,-100%,0)}[vaul-drawer][vaul-drawer-direction=left]{transform:translate3d(-100%,0,0)}[vaul-drawer][vaul-drawer-direction=right]{transform:translate3d(100%,0,0)}.vaul-dragging .vaul-scrollable [vault-drawer-direction=top]{overflow-y:hidden!important}.vaul-dragging .vaul-scrollable [vault-drawer-direction=bottom]{overflow-y:hidden!important}.vaul-dragging .vaul-scrollable [vault-drawer-direction=left]{overflow-x:hidden!important}.vaul-dragging .vaul-scrollable [vault-drawer-direction=right]{overflow-x:hidden!important}[vaul-drawer][vaul-drawer-visible=true][vaul-drawer-direction=top]{transform:translate3d(0,var(--snap-point-height,0),0)}[vaul-drawer][vaul-drawer-visible=true][vaul-drawer-direction=bottom]{transform:translate3d(0,var(--snap-point-height,0),0)}[vaul-drawer][vaul-drawer-visible=true][vaul-drawer-direction=left]{transform:translate3d(var(--snap-point-height,0),0,0)}[vaul-drawer][vaul-drawer-visible=true][vaul-drawer-direction=right]{transform:translate3d(var(--snap-point-height,0),0,0)}[vaul-overlay]{opacity:0;transition:opacity .5s cubic-bezier(.32, .72, 0, 1)}[vaul-overlay][vaul-drawer-visible=true]{opacity:1}[vaul-drawer]::after{content:'';position:absolute;background:inherit;background-color:inherit}[vaul-drawer][vaul-drawer-direction=top]::after{top:initial;bottom:100%;left:0;right:0;height:200%}[vaul-drawer][vaul-drawer-direction=bottom]::after{top:100%;bottom:initial;left:0;right:0;height:200%}[vaul-drawer][vaul-drawer-direction=left]::after{left:initial;right:100%;top:0;bottom:0;width:200%}[vaul-drawer][vaul-drawer-direction=right]::after{left:100%;right:initial;top:0;bottom:0;width:200%}[vaul-handle]{display:block;position:relative;opacity:.8;margin-left:auto;margin-right:auto;height:5px;width:56px;border-radius:1rem;touch-action:pan-y;cursor:grab}[vaul-handle]:active,[vaul-handle]:hover{opacity:1}[vaul-handle]:active{cursor:grabbing}[vaul-handle-hitarea]{position:absolute;left:50%;top:50%;transform:translate(-50%,-50%);width:max(100%,2.75rem);height:max(100%,2.75rem);touch-action:inherit}[vaul-overlay][vaul-snap-points=true]:not([vaul-snap-points-overlay=true]):not([data-state=closed]){opacity:0}[vaul-overlay][vaul-snap-points-overlay=true]:not([vaul-drawer-visible=false]){opacity:1}@media (hover:hover) and (pointer:fine){[vaul-drawer]{user-select:none}}@media (pointer:fine){[vaul-handle-hitarea]:{width:100%;height:100%}}");let u="undefined"!=typeof window?a.useLayoutEffect:a.useEffect;function c(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];for(let e of t)"function"==typeof e&&e(...n)}}function s(){return d(/^iPhone/)||d(/^iPad/)||d(/^Mac/)&&navigator.maxTouchPoints>1}function d(e){return"undefined"!=typeof window&&null!=window.navigator?e.test(window.navigator.platform):void 0}let f="undefined"!=typeof document&&window.visualViewport;function m(e){let t=window.getComputedStyle(e);return/(auto|scroll)/.test(t.overflow+t.overflowX+t.overflowY)}function p(e){for(m(e)&&(e=e.parentElement);e&&!m(e);)e=e.parentElement;return e||document.scrollingElement||document.documentElement}let h=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]),w=0;function g(e,t,n){let r=e.style[t];return e.style[t]=n,()=>{e.style[t]=r}}function v(e,t,n,r){return e.addEventListener(t,n,r),()=>{e.removeEventListener(t,n,r)}}function b(e){let t=document.scrollingElement||document.documentElement;for(;e&&e!==t;){let t=p(e);if(t!==document.documentElement&&t!==document.body&&t!==e){let n=t.getBoundingClientRect().top,r=e.getBoundingClientRect().top;e.getBoundingClientRect().bottom>t.getBoundingClientRect().bottom&&(t.scrollTop+=r-n)}e=t.parentElement}}function y(e){return e instanceof HTMLInputElement&&!h.has(e.type)||e instanceof HTMLTextAreaElement||e instanceof HTMLElement&&e.isContentEditable}function E(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return a.useCallback(function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return e=>t.forEach(t=>{"function"==typeof t?t(e):null!=t&&(t.current=e)})}(...t),t)}let x=null,R=new WeakMap;function D(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(!e||!(e instanceof HTMLElement))return;let r={};Object.entries(t).forEach(t=>{let[n,o]=t;if(n.startsWith("--")){e.style.setProperty(n,o);return}r[n]=e.style[n],e.style[n]=o}),n||R.set(e,r)}function T(e,t){if(!e||!(e instanceof HTMLElement))return;let n=R.get(e);n&&(t?e.style[t]=n[t]:Object.entries(n).forEach(t=>{let[n,r]=t;e.style[n]=r}))}let A=e=>{switch(e){case"top":case"bottom":return!0;case"left":case"right":return!1;default:return e}};function C(e,t){if(!e)return null;let n=window.getComputedStyle(e),r=n.transform||n.webkitTransform||n.mozTransform,o=r.match(/^matrix3d\((.+)\)$/);return o?parseFloat(o[1].split(", ")[A(t)?13:12]):(o=r.match(/^matrix\((.+)\)$/))?parseFloat(o[1].split(", ")[A(t)?5:4]):null}let O={DURATION:.5,EASE:[.32,.72,0,1]};function M(e){let t=a.useRef(e);return a.useEffect(()=>{t.current=e}),a.useMemo(()=>function(){for(var e=arguments.length,n=Array(e),r=0;r<e;r++)n[r]=arguments[r];return null==t.current?void 0:t.current.call(t,...n)},[])}let N="vaul-dragging";function P(e){var t;let{open:n,onOpenChange:l,children:d,shouldScaleBackground:m,onDrag:h,onRelease:E,snapPoints:R,nested:P=!1,setBackgroundColorOnScale:S=!0,closeThreshold:k=.25,scrollLockTimeout:I=100,dismissible:H=!0,handleOnly:z=!1,fadeFromIndex:U=R&&R.length-1,activeSnapPoint:L,setActiveSnapPoint:W,fixed:j,modal:B=!0,onClose:F,noBodyStyles:Y,direction:q="bottom",preventScrollRestoration:V=!0,disablePreventScroll:X=!1}=e,[_=!1,$]=a.useState(!1),[K,G]=a.useState(!1),[J,Q]=a.useState(!1),[Z,ee]=a.useState(!1),[et,en]=a.useState(!1),[er,eo]=a.useState(!1),ea=a.useRef(null),ei=a.useRef(null),el=a.useRef(null),eu=a.useRef(null),ec=a.useRef(null),es=a.useRef(!1),ed=a.useRef(null),ef=a.useRef(0),em=a.useRef(!1),ep=a.useRef(0),eh=a.useRef(null),ew=a.useRef((null==(t=eh.current)?void 0:t.getBoundingClientRect().height)||0),eg=a.useRef(0),ev=a.useCallback(e=>{R&&e===eR.length-1&&(ei.current=new Date)},[]),{activeSnapPoint:eb,activeSnapPointIndex:ey,setActiveSnapPoint:eE,onRelease:ex,snapPointsOffset:eR,onDrag:eD,shouldFade:eT,getPercentageDragged:eA}=function(e){let{activeSnapPointProp:t,setActiveSnapPointProp:n,snapPoints:r,drawerRef:o,overlayRef:i,fadeFromIndex:l,onSnapPointChange:u,direction:c="bottom"}=e,[s,d]=function(e){let{prop:t,defaultProp:n,onChange:r=()=>{}}=e,[o,i]=function(e){let{defaultProp:t,onChange:n}=e,r=a.useState(t),[o]=r,i=a.useRef(o),l=M(n);return a.useEffect(()=>{i.current!==o&&(l(o),i.current=o)},[o,i,l]),r}({defaultProp:n,onChange:r}),l=void 0!==t,u=l?t:o,c=M(r);return[u,a.useCallback(e=>{if(l){let n="function"==typeof e?e(t):e;n!==t&&c(n)}else i(e)},[l,t,i,c])]}({prop:t,defaultProp:null==r?void 0:r[0],onChange:n}),f=a.useMemo(()=>s===(null==r?void 0:r[r.length-1])||null,[r,s]),m=r&&r.length>0&&(l||0===l)&&!Number.isNaN(l)&&r[l]===s||!r,p=a.useMemo(()=>null==r?void 0:r.findIndex(e=>e===s),[r,s]),h=a.useMemo(()=>{var e;return null!=(e=null==r?void 0:r.map(e=>{let t="undefined"!=typeof window,n="string"==typeof e,r=0;if(n&&(r=parseInt(e,10)),A(c)){let o=n?r:t?e*window.innerHeight:0;return t?"bottom"===c?window.innerHeight-o:-window.innerHeight+o:o}let o=n?r:t?e*window.innerWidth:0;return t?"right"===c?window.innerWidth-o:-window.innerWidth+o:o}))?e:[]},[r]),w=a.useMemo(()=>null!==p?null==h?void 0:h[p]:null,[h,p]),g=a.useCallback(e=>{var t;let n=null!=(t=null==h?void 0:h.findIndex(t=>t===e))?t:null;u(n),D(o.current,{transition:"transform ".concat(O.DURATION,"s cubic-bezier(").concat(O.EASE.join(","),")"),transform:A(c)?"translate3d(0, ".concat(e,"px, 0)"):"translate3d(".concat(e,"px, 0, 0)")}),h&&n!==h.length-1&&n!==l?D(i.current,{transition:"opacity ".concat(O.DURATION,"s cubic-bezier(").concat(O.EASE.join(","),")"),opacity:"0"}):D(i.current,{transition:"opacity ".concat(O.DURATION,"s cubic-bezier(").concat(O.EASE.join(","),")"),opacity:"1"}),d(null!==n?null==r?void 0:r[n]:null)},[o.current,r,h,l,i,d]);return a.useEffect(()=>{if(s||t){var e;let n=null!=(e=null==r?void 0:r.findIndex(e=>e===t||e===s))?e:-1;h&&-1!==n&&"number"==typeof h[n]&&g(h[n])}},[s,t,r,h,g]),{isLastSnapPoint:f,activeSnapPoint:s,shouldFade:m,getPercentageDragged:function(e,t){if(!r||"number"!=typeof p||!h||void 0===l)return null;let n=p===l-1;if(p>=l&&t)return 0;if(n&&!t)return 1;if(!m&&!n)return null;let o=n?p+1:p-1,a=e/Math.abs(n?h[o]-h[o-1]:h[o+1]-h[o]);return n?1-a:a},setActiveSnapPoint:d,activeSnapPointIndex:p,onRelease:function(e){let{draggedDistance:t,closeDrawer:n,velocity:o,dismissible:a}=e;if(void 0===l)return;let u="bottom"===c||"right"===c?(null!=w?w:0)-t:(null!=w?w:0)+t,s=p===l-1,d=0===p,m=t>0;if(s&&D(i.current,{transition:"opacity ".concat(O.DURATION,"s cubic-bezier(").concat(O.EASE.join(","),")")}),o>2&&!m){a?n():g(h[0]);return}if(o>2&&m&&h&&r){g(h[r.length-1]);return}let v=null==h?void 0:h.reduce((e,t)=>"number"!=typeof e||"number"!=typeof t?e:Math.abs(t-u)<Math.abs(e-u)?t:e),b=A(c)?window.innerHeight:window.innerWidth;if(o>.4&&Math.abs(t)<.4*b){let e=m?1:-1;if(e>0&&f){g(h[r.length-1]);return}if(d&&e<0&&a&&n(),null===p)return;g(h[p+e]);return}g(v)},onDrag:function(e){let{draggedDistance:t}=e;if(null===w)return;let n="bottom"===c||"right"===c?w-t:w+t;("bottom"===c||"right"===c)&&n<h[h.length-1]||("top"===c||"left"===c)&&n>h[h.length-1]||D(o.current,{transform:A(c)?"translate3d(0, ".concat(n,"px, 0)"):"translate3d(".concat(n,"px, 0, 0)")})},snapPointsOffset:h}}({snapPoints:R,activeSnapPointProp:L,setActiveSnapPointProp:W,drawerRef:eh,fadeFromIndex:U,overlayRef:ea,onSnapPointChange:ev,direction:q});!function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},{isDisabled:t}=e;u(()=>{if(!t){let e,t,n,o,a,i;return 1==++w&&(r=s()?(t=0,n=window.pageXOffset,o=window.pageYOffset,a=c(g(document.documentElement,"paddingRight","".concat(window.innerWidth-document.documentElement.clientWidth,"px"))),window.scrollTo(0,0),i=c(v(document,"touchstart",n=>{((e=p(n.target))!==document.documentElement||e!==document.body)&&(t=n.changedTouches[0].pageY)},{passive:!1,capture:!0}),v(document,"touchmove",n=>{if(!e||e===document.documentElement||e===document.body){n.preventDefault();return}let r=n.changedTouches[0].pageY,o=e.scrollTop,a=e.scrollHeight-e.clientHeight;0!==a&&((o<=0&&r>t||o>=a&&r<t)&&n.preventDefault(),t=r)},{passive:!1,capture:!0}),v(document,"touchend",e=>{let t=e.target;y(t)&&t!==document.activeElement&&(e.preventDefault(),t.style.transform="translateY(-2000px)",t.focus(),requestAnimationFrame(()=>{t.style.transform=""}))},{passive:!1,capture:!0}),v(document,"focus",e=>{let t=e.target;y(t)&&(t.style.transform="translateY(-2000px)",requestAnimationFrame(()=>{t.style.transform="",f&&(f.height<window.innerHeight?requestAnimationFrame(()=>{b(t)}):f.addEventListener("resize",()=>b(t),{once:!0}))}))},!0),v(window,"scroll",()=>{window.scrollTo(0,0)})),()=>{a(),i(),window.scrollTo(n,o)}):c(g(document.documentElement,"paddingRight","".concat(window.innerWidth-document.documentElement.clientWidth,"px")))),()=>{0==--w&&r()}}},[t])}({isDisabled:!_||et||!B||er||!K||X});let{restorePositionSetting:eC}=function(e){let{isOpen:t,modal:n,nested:r,hasBeenOpened:o,preventScrollRestoration:i,noBodyStyles:l}=e,[u,c]=a.useState(()=>"undefined"!=typeof window?window.location.href:""),s=a.useRef(0),d=a.useCallback(()=>{if(null===x&&t&&!l){x={position:document.body.style.position,top:document.body.style.top,left:document.body.style.left,height:document.body.style.height,right:"unset"};let{scrollX:e,innerHeight:t}=window;document.body.style.setProperty("position","fixed","important"),Object.assign(document.body.style,{top:"".concat(-s.current,"px"),left:"".concat(-e,"px"),right:"0px",height:"auto"}),window.setTimeout(()=>window.requestAnimationFrame(()=>{let e=t-window.innerHeight;e&&s.current>=t&&(document.body.style.top="".concat(-(s.current+e),"px"))}),300)}},[t]),f=a.useCallback(()=>{if(null!==x&&!l){let e=-parseInt(document.body.style.top,10),t=-parseInt(document.body.style.left,10);Object.assign(document.body.style,x),window.requestAnimationFrame(()=>{if(i&&u!==window.location.href){c(window.location.href);return}window.scrollTo(t,e)}),x=null}},[u]);return a.useEffect(()=>{function e(){s.current=window.scrollY}return e(),window.addEventListener("scroll",e),()=>{window.removeEventListener("scroll",e)}},[]),a.useEffect(()=>{r||!o||(t?(window.matchMedia("(display-mode: standalone)").matches||d(),n||window.setTimeout(()=>{f()},500)):f())},[t,o,u,n,r,d,f]),{restorePositionSetting:f}}({isOpen:_,modal:B,nested:P,hasBeenOpened:K,preventScrollRestoration:V,noBodyStyles:Y});function eO(){return(window.innerWidth-26)/window.innerWidth}function eM(e,t){var n;let r=e,o=null==(n=window.getSelection())?void 0:n.toString(),a=eh.current?C(eh.current,q):null,i=new Date;if(r.hasAttribute("data-vaul-no-drag")||r.closest("[data-vaul-no-drag]"))return!1;if("right"===q||"left"===q)return!0;if(ei.current&&i.getTime()-ei.current.getTime()<500)return!1;if(null!==a&&("bottom"===q?a>0:a<0))return!0;if(o&&o.length>0)return!1;if(ec.current&&i.getTime()-ec.current.getTime()<I&&0===a||t)return ec.current=i,!1;for(;r;){if(r.scrollHeight>r.clientHeight){if(0!==r.scrollTop)return ec.current=new Date,!1;if("dialog"===r.getAttribute("role"))break}r=r.parentNode}return!0}function eN(){eh.current&&(et&&eh.current&&(eh.current.classList.remove(N),es.current=!1,en(!1),eu.current=new Date),null==F||F(),D(eh.current,{transform:A(q)?"translate3d(0, ".concat("bottom"===q?"100%":"-100%",", 0)"):"translate3d(".concat("right"===q?"100%":"-100%",", 0, 0)"),transition:"transform ".concat(O.DURATION,"s cubic-bezier(").concat(O.EASE.join(","),")")}),D(ea.current,{opacity:"0",transition:"opacity ".concat(O.DURATION,"s cubic-bezier(").concat(O.EASE.join(","),")")}),eS(!1),setTimeout(()=>{Q(!1),$(!1)},300),setTimeout(()=>{R&&eE(R[0])},1e3*O.DURATION))}function eP(){if(!eh.current)return;let e=document.querySelector("[vaul-drawer-wrapper]"),t=C(eh.current,q);D(eh.current,{transform:"translate3d(0, 0, 0)",transition:"transform ".concat(O.DURATION,"s cubic-bezier(").concat(O.EASE.join(","),")")}),D(ea.current,{transition:"opacity ".concat(O.DURATION,"s cubic-bezier(").concat(O.EASE.join(","),")"),opacity:"1"}),m&&t&&t>0&&_&&D(e,{borderRadius:"".concat(8,"px"),overflow:"hidden",...A(q)?{transform:"scale(".concat(eO(),") translate3d(0, calc(env(safe-area-inset-top) + 14px), 0)"),transformOrigin:"top"}:{transform:"scale(".concat(eO(),") translate3d(calc(env(safe-area-inset-top) + 14px), 0, 0)"),transformOrigin:"left"},transitionProperty:"transform, border-radius",transitionDuration:"".concat(O.DURATION,"s"),transitionTimingFunction:"cubic-bezier(".concat(O.EASE.join(","),")")},!0)}function eS(e){let t=document.querySelector("[vaul-drawer-wrapper]");t&&m&&(e?(S&&!Y&&(D(document.body,{background:document.body.style.backgroundColor||document.body.style.background}),D(document.body,{background:"black"},!0)),D(t,{borderRadius:"".concat(8,"px"),overflow:"hidden",...A(q)?{transform:"scale(".concat(eO(),") translate3d(0, calc(env(safe-area-inset-top) + 14px), 0)"),transformOrigin:"top"}:{transform:"scale(".concat(eO(),") translate3d(calc(env(safe-area-inset-top) + 14px), 0, 0)"),transformOrigin:"left"},transitionProperty:"transform, border-radius",transitionDuration:"".concat(O.DURATION,"s"),transitionTimingFunction:"cubic-bezier(".concat(O.EASE.join(","),")")})):(T(t,"overflow"),T(t,"transform"),T(t,"borderRadius"),D(t,{transitionProperty:"transform, border-radius",transitionDuration:"".concat(O.DURATION,"s"),transitionTimingFunction:"cubic-bezier(".concat(O.EASE.join(","),")")})))}return a.useEffect(()=>()=>{eS(!1),eC()},[]),a.useEffect(()=>{var e;function t(){if(eh.current&&(y(document.activeElement)||em.current)){var e;let t=(null==(e=window.visualViewport)?void 0:e.height)||0,n=window.innerHeight-t,r=eh.current.getBoundingClientRect().height||0;eg.current||(eg.current=r);let o=eh.current.getBoundingClientRect().top;if(Math.abs(ep.current-n)>60&&(em.current=!em.current),R&&R.length>0&&eR&&ey&&(n+=eR[ey]||0),ep.current=n,r>t||em.current){let e=eh.current.getBoundingClientRect().height,r=e;e>t&&(r=t-26),j?eh.current.style.height="".concat(e-Math.max(n,0),"px"):eh.current.style.height="".concat(Math.max(r,t-o),"px")}else eh.current.style.height="".concat(eg.current,"px");R&&R.length>0&&!em.current?eh.current.style.bottom="0px":eh.current.style.bottom="".concat(Math.max(n,0),"px")}}return null==(e=window.visualViewport)||e.addEventListener("resize",t),()=>{var e;return null==(e=window.visualViewport)?void 0:e.removeEventListener("resize",t)}},[ey,R,eR]),a.useEffect(()=>{if(!_&&m){let e=setTimeout(()=>{T(document.body)},200);return()=>clearTimeout(e)}},[_,m]),u(()=>{n?($(!0),G(!0)):eN()},[n]),a.useEffect(()=>{Z&&(null==l||l(_))},[_]),a.useEffect(()=>{ee(!0)},[]),a.useEffect(()=>{_&&(D(document.documentElement,{scrollBehavior:"auto"}),ei.current=new Date,eS(!0))},[_]),a.useEffect(()=>{if(eh.current&&J){var e;let t=null==eh?void 0:null==(e=eh.current)?void 0:e.querySelectorAll("*");null==t||t.forEach(e=>{(e.scrollHeight>e.clientHeight||e.scrollWidth>e.clientWidth)&&e.classList.add("vaul-scrollable")})}},[J]),a.createElement(o.fC,{modal:B,onOpenChange:e=>{if(void 0!==n){null==l||l(e);return}e?(G(!0),$(e)):eN()},open:_},a.createElement(i.Provider,{value:{visible:J,activeSnapPoint:eb,snapPoints:R,setActiveSnapPoint:eE,drawerRef:eh,overlayRef:ea,scaleBackground:eS,onOpenChange:l,onPress:function(e){var t;(H||R)&&(!eh.current||eh.current.contains(e.target))&&(ew.current=(null==(t=eh.current)?void 0:t.getBoundingClientRect().height)||0,en(!0),el.current=new Date,s()&&window.addEventListener("touchend",()=>es.current=!1,{once:!0}),e.target.setPointerCapture(e.pointerId),ef.current=A(q)?e.clientY:e.clientX)},setVisible:Q,onRelease:function(e){var t;if(!et||!eh.current)return;eh.current.classList.remove(N),es.current=!1,en(!1),eu.current=new Date;let n=C(eh.current,q);if(!eM(e.target,!1)||!n||Number.isNaN(n)||null===el.current)return;let r=eu.current.getTime()-el.current.getTime(),o=ef.current-(A(q)?e.clientY:e.clientX),a=Math.abs(o)/r;if(a>.05&&(eo(!0),setTimeout(()=>{eo(!1)},200)),R){ex({draggedDistance:o*("bottom"===q||"right"===q?1:-1),closeDrawer:eN,velocity:a,dismissible:H}),null==E||E(e,!0);return}if("bottom"===q||"right"===q?o>0:o<0){eP(),null==E||E(e,!0);return}if(a>.4||n>=Math.min(null!=(t=eh.current.getBoundingClientRect().height)?t:0,window.innerHeight)*k){eN(),null==E||E(e,!1);return}null==E||E(e,!0),eP()},onDrag:function(e){if(eh.current&&et){let t="bottom"===q||"right"===q?1:-1,n=(ef.current-(A(q)?e.clientY:e.clientX))*t,r=n>0,o=R&&!H&&!r;if(o&&0===ey)return;let a=Math.abs(n),i=document.querySelector("[vaul-drawer-wrapper]"),l=a/ew.current,u=eA(a,r);if(null!==u&&(l=u),o&&l>=1||!es.current&&!eM(e.target,r))return;if(eh.current.classList.add(N),es.current=!0,D(eh.current,{transition:"none"}),D(ea.current,{transition:"none"}),R&&eD({draggedDistance:n}),r&&!R){let e=Math.min(-(8*(Math.log(n+1)-2)*1),0)*t;D(eh.current,{transform:A(q)?"translate3d(0, ".concat(e,"px, 0)"):"translate3d(".concat(e,"px, 0, 0)")});return}let c=1-l;if((eT||U&&ey===U-1)&&(null==h||h(e,l),D(ea.current,{opacity:"".concat(c),transition:"none"},!0)),i&&ea.current&&m){let e=Math.min(eO()+l*(1-eO()),1),t=8-8*l,n=Math.max(0,14-14*l);D(i,{borderRadius:"".concat(t,"px"),transform:A(q)?"scale(".concat(e,") translate3d(0, ").concat(n,"px, 0)"):"scale(".concat(e,") translate3d(").concat(n,"px, 0, 0)"),transition:"none"},!0)}if(!R){let e=a*t;D(eh.current,{transform:A(q)?"translate3d(0, ".concat(e,"px, 0)"):"translate3d(".concat(e,"px, 0, 0)")})}}},dismissible:H,handleOnly:z,isOpen:_,isDragging:et,shouldFade:eT,closeDrawer:eN,onNestedDrag:function(e,t){if(t<0)return;let n=A(q)?window.innerHeight:window.innerWidth,r=(n-16)/n,o=r+t*(1-r),a=-16+16*t;D(eh.current,{transform:A(q)?"scale(".concat(o,") translate3d(0, ").concat(a,"px, 0)"):"scale(".concat(o,") translate3d(").concat(a,"px, 0, 0)"),transition:"none"})},onNestedOpenChange:function(e){let t=e?(window.innerWidth-16)/window.innerWidth:1;ed.current&&window.clearTimeout(ed.current),D(eh.current,{transition:"transform ".concat(O.DURATION,"s cubic-bezier(").concat(O.EASE.join(","),")"),transform:"scale(".concat(t,") translate3d(0, ").concat(e?-16:0,"px, 0)")}),!e&&eh.current&&(ed.current=setTimeout(()=>{let e=C(eh.current,q);D(eh.current,{transition:"none",transform:A(q)?"translate3d(0, ".concat(e,"px, 0)"):"translate3d(".concat(e,"px, 0, 0)")})},500))},onNestedRelease:function(e,t){let n=A(q)?window.innerHeight:window.innerWidth,r=t?(n-16)/n:1,o=t?-16:0;t&&D(eh.current,{transition:"transform ".concat(O.DURATION,"s cubic-bezier(").concat(O.EASE.join(","),")"),transform:A(q)?"scale(".concat(r,") translate3d(0, ").concat(o,"px, 0)"):"scale(".concat(r,") translate3d(").concat(o,"px, 0, 0)")})},keyboardIsOpen:em,openProp:n,modal:B,snapPointsOffset:eR,direction:q}},d))}let S=a.forwardRef(function(e,t){let{preventCycle:n=!1,children:r,...o}=e,{visible:i,closeDrawer:u,isDragging:c,snapPoints:s,activeSnapPoint:d,setActiveSnapPoint:f,dismissible:m,handleOnly:p,onPress:h,onDrag:w}=l(),g=a.useRef(null),v=a.useRef(!1);function b(){window.clearTimeout(g.current),v.current=!1}return a.createElement("div",{onClick:function(){if(v.current){b();return}window.setTimeout(()=>{!function(){if(c||n||v.current){b();return}if(b(),(!s||0===s.length)&&m||d===s[s.length-1]&&m){u();return}let e=s.findIndex(e=>e===d);-1!==e&&f(s[e+1])}()},120)},onDoubleClick:()=>{v.current=!0,u()},onPointerCancel:b,onPointerDown:e=>{p&&h(e),g.current=window.setTimeout(()=>{v.current=!0},250)},onPointerMove:e=>{p&&w(e)},ref:t,"vaul-drawer-visible":i?"true":"false","vaul-handle":"","aria-hidden":"true",...o},a.createElement("span",{"vaul-handle-hitarea":"","aria-hidden":"true"},r))});S.displayName="Drawer.Handle";let k=a.forwardRef(function(e,t){let{children:n,...r}=e,{overlayRef:i,snapPoints:u,onRelease:c,shouldFade:s,isOpen:d,visible:f}=l(),m=E(t,i),p=u&&u.length>0;return a.createElement(o.aV,{onMouseUp:c,ref:m,"vaul-drawer-visible":f?"true":"false","vaul-overlay":"","vaul-snap-points":d&&p?"true":"false","vaul-snap-points-overlay":d&&s?"true":"false",...r})});k.displayName="Drawer.Overlay";let I=a.forwardRef(function(e,t){let{onOpenAutoFocus:n,onPointerDownOutside:r,onAnimationEnd:i,style:u,...c}=e,{drawerRef:s,onPress:d,onRelease:f,onDrag:m,dismissible:p,keyboardIsOpen:h,snapPointsOffset:w,visible:g,closeDrawer:v,modal:b,openProp:y,onOpenChange:x,setVisible:R,handleOnly:D,direction:T}=l(),A=E(t,s),C=a.useRef(null),O=a.useRef(!1),M=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;if(O.current)return!0;let r=Math.abs(e.y),o=Math.abs(e.x),a=o>r,i=["bottom","right"].includes(t)?1:-1;if("left"===t||"right"===t){if(!(e.x*i<0)&&o>=0&&o<=n)return a}else if(!(e.y*i<0)&&r>=0&&r<=n)return!a;return O.current=!0,!0};return a.useEffect(()=>{R(!0)},[]),a.createElement(o.VY,{"vaul-drawer":"","vaul-drawer-direction":T,"vaul-drawer-visible":g?"true":"false",...c,ref:A,style:w&&w.length>0?{"--snap-point-height":"".concat(w[0],"px"),...u}:u,onOpenAutoFocus:e=>{if(n)n(e);else{var t;e.preventDefault(),null==(t=s.current)||t.focus()}},onPointerDown:e=>{D||(null==c.onPointerDown||c.onPointerDown.call(c,e),C.current={x:e.clientX,y:e.clientY},d(e))},onPointerDownOutside:e=>{if(null==r||r(e),!b||e.defaultPrevented){e.preventDefault();return}h.current&&(h.current=!1),e.preventDefault(),null==x||x(!1),p&&void 0===y&&v()},onFocusOutside:e=>{if(!b){e.preventDefault();return}},onEscapeKeyDown:e=>{if(!b){e.preventDefault();return}},onPointerMove:e=>{if(D||(null==c.onPointerMove||c.onPointerMove.call(c,e),!C.current))return;let t=e.clientY-C.current.y,n=e.clientX-C.current.x,r="touch"===e.pointerType?10:2;M({x:n,y:t},T,r)?m(e):(Math.abs(n)>r||Math.abs(t)>r)&&(C.current=null)},onPointerUp:e=>{null==c.onPointerUp||c.onPointerUp.call(c,e),C.current=null,O.current=!1,f(e)}})});I.displayName="Drawer.Content";let H={Root:P,NestedRoot:function(e){let{onDrag:t,onOpenChange:n,...r}=e,{onNestedDrag:o,onNestedOpenChange:i,onNestedRelease:u}=l();if(!o)throw Error("Drawer.NestedRoot must be placed in another drawer");return a.createElement(P,{nested:!0,onClose:()=>{i(!1)},onDrag:(e,n)=>{o(e,n),null==t||t(e,n)},onOpenChange:e=>{e&&i(e),null==n||n(e)},onRelease:u,...r})},Content:I,Handle:S,Overlay:k,Trigger:o.xz,Portal:o.h_,Close:o.x8,Title:o.Dx,Description:o.dk}}}]);