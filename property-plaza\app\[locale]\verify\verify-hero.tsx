"use client"
import MainContentLayout from "@/components/seekers-content-layout/main-content-layout";
import { Button } from "@/components/ui/button";
import { useTranslations } from "next-intl";
import { Link } from "@/lib/locale/routing";
import OptimizedVideo from "@/components/ui/optimized-video";

export default function VerifyHero() {
  const t = useTranslations("verify");

  return (
    <section className="bg-gradient-to-br from-seekers-primary/5 to-seekers-primary/10 py-6 md:py-10 lg:py-16" aria-labelledby="hero-title">
      <MainContentLayout>
        <div className="flex flex-col lg:grid lg:grid-cols-2 gap-4 md:gap-6 lg:gap-8 items-center">
          {/* Left Content */}
          <div className="space-y-3 md:space-y-4 lg:space-y-5 order-2 lg:order-1 text-center lg:text-left">
            <div className="flex items-center justify-center lg:justify-start gap-2 text-red-600 font-semibold">
              <span className="text-xl md:text-2xl">🚨</span>
              <span className="text-sm md:text-base">{t("hero.badge")}</span>
            </div>

            <h1 id="hero-title" className="text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold text-seekers-text leading-tight">
              {t("hero.title")}
            </h1>

            <p className="text-base md:text-lg text-seekers-text-light leading-relaxed">
              {t("hero.subtitle")}
            </p>

            <div className="space-y-3 md:space-y-4">
              {[
                t("hero.benefits.0"),
                t("hero.benefits.1"),
                t("hero.benefits.2"),
                t("hero.benefits.3")
              ].map((benefit, index) => (
                <div key={index} className="flex items-start lg:items-center gap-3 justify-center lg:justify-start">
                  <span className="text-seekers-primary text-lg mt-0.5 lg:mt-0">•</span>
                  <span className="text-sm md:text-base text-seekers-text font-medium text-left">
                    {benefit}
                  </span>
                </div>
              ))}
            </div>

            <div className="bg-red-50 border border-red-200 rounded-lg p-3 md:p-4">
              <p className="text-red-800 font-medium text-sm md:text-base text-center lg:text-left">
                {t("hero.warning")} {t("hero.cta")}
              </p>
            </div>

            <div className="pt-4 md:pt-6 text-center">
              <Button
                size="lg"
                className="bg-seekers-primary hover:bg-seekers-primary/90 text-white px-6 md:px-8 py-3 md:py-4 text-base md:text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-200 w-full sm:w-auto"
                asChild
              >
                <Link href={"#booking-form"}>
                  {t('cta.bookInspection')}
                </Link>
              </Button>
              <p className="text-xs md:text-sm text-seekers-text-light mt-2 md:mt-3">
                {t('footnote')}
              </p>
            </div>
          </div>

          {/* Right Content - Villa Inspection Video */}
          <div className="relative flex justify-center order-1 lg:order-2 w-full">
            <div className="w-full max-w-xs sm:max-w-sm md:max-w-md lg:max-w-xs xl:max-w-sm"
              style={{ aspectRatio: '9/16' }}>
              <OptimizedVideo
                src="https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4"
                poster="https://images.pexels.com/photos/1396122/pexels-photo-1396122.jpeg?auto=compress&cs=tinysrgb&w=400&h=711"
                className="w-full h-full shadow-lg"
                autoPlay={true}
                muted={true}
                loop={true}
                playsInline={true}
                controls={false}
                preload="metadata"
                lazy={false}
                fallbackContent={
                  <div className="text-center space-y-2 p-4">
                    <div className="text-4xl">📱</div>
                    <p className="text-neutral-500 font-medium">Villa Inspection Video</p>
                    <p className="text-sm text-neutral-400 leading-relaxed">
                      Professional villa inspection<br />
                      process and red flags<br />
                      identification
                    </p>
                  </div>
                }
              />
            </div>
          </div>
        </div>
      </MainContentLayout>
    </section>
  )
}
