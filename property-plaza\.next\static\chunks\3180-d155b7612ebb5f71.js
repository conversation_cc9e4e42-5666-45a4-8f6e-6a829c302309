(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3180],{90492:function(e,t,a){var r={"./en.json":[83,83],"./id.json":[29031,9031],"./nl.json":[28958,8958]};function n(e){if(!a.o(r,e))return Promise.resolve().then(function(){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=r[e],n=t[0];return a.e(t[1]).then(function(){return a.t(n,19)})}n.keys=function(){return Object.keys(r)},n.id=90492,e.exports=n},92324:function(e,t,a){"use strict";a.d(t,{Z:function(){return A}});var r=a(57437),n=a(57612),s=a(94508),i=a(72227),l=a(18133),o=a(14938),c=a(95252),d=a(81197),u=a(67410),m=a(16275),h=a(56096),f=a(75745),p=a(86595);function A(e){let{category:t,className:a}=e;switch(t){case n.yJ.villa:case n.yJ.villas:return(0,r.jsx)(i.Z,{className:(0,s.cn)("!w-6 !h-6",a)});case n.yJ.apartment:return(0,r.jsx)(l.Z,{className:(0,s.cn)("!w-6 !h-6",a)});case n.yJ.homestay:case n.yJ.guestHouse:case n.yJ.rooms:return(0,r.jsx)(o.Z,{className:(0,s.cn)("!w-6 !h-6",a)});case n.yJ.ruko:case n.yJ.commercialSpace:return(0,r.jsx)(c.Z,{className:(0,s.cn)("!w-6 !h-6",a)});case n.yJ.cafeOrRestaurants:return(0,r.jsx)(d.Z,{className:(0,s.cn)("!w-6 !h-6",a)});case n.yJ.offices:return(0,r.jsx)(u.Z,{className:(0,s.cn)("!w-6 !h-6",a)});case n.yJ.shops:return(0,r.jsx)(m.Z,{className:(0,s.cn)("!w-6 !h-6",a)});case n.yJ.shellAndCore:return(0,r.jsx)(h.Z,{className:(0,s.cn)("!w-6 !h-6",a)});case n.yJ.lands:return(0,r.jsx)(f.Z,{className:(0,s.cn)("!w-6 !h-6",a)});default:return(0,r.jsx)(p.Z,{className:(0,s.cn)("!w-6 !h-6",a)})}}},85970:function(e,t,a){"use strict";a.d(t,{Z:function(){return o}});var r=a(57437),n=a(57860),s=a(26110),i=a(17814),l=a(94508);function o(e){let{children:t,className:a}=e;return(0,n.a)("(min-width:1024px)")?(0,r.jsx)(s.cN,{className:(0,l.cn)("px-0",a),children:t}):(0,r.jsx)(i.ze,{className:(0,l.cn)("px-0",a),children:t})}},84002:function(e,t,a){"use strict";a.d(t,{Z:function(){return l}});var r=a(57437),n=a(57860),s=a(17814),i=a(26110);function l(e){let{children:t,className:a}=e;return(0,n.a)("(min-width:1024px)")?(0,r.jsx)(i.$N,{className:a,children:t}):(0,r.jsx)(s.iI,{className:a,children:t})}},43180:function(e,t,a){"use strict";a.d(t,{default:function(){return eC}});var r=a(57437),n=a(51179),s=a(27668),i=a(33145),l=a(69729),o=a(62869),c=a(73247),d=a(58293),u=a(2265),m=a(48614),h=a(40521),f=a(74316),p=a(94508),A=a(53647),x=a(28959);let g=[{id:"1",content:"IDR",value:"IDR"},{id:"2",content:"EUR",value:"EUR"},{id:"3",content:"GBP",value:"GBP"},{id:"4",content:"AUD",value:"AUD"},{id:"5",content:"USD",value:"USD"}],y=(0,u.forwardRef)((e,t)=>{let{triggerClassName:a,showCaret:n=!1,defaultCurrency:s="IDR",onClick:i}=e,{currency:l,setCurrency:o,isLoading:c}=(0,x.R)(),[d,m]=(0,u.useState)(s),[h,f]=(0,u.useState)(!1);return(0,u.useEffect)(()=>{if(c)return m(s);m(l)},[l,c,s]),(0,r.jsx)("div",{className:"max-sm:w-fit w-full",children:(0,r.jsxs)(A.Ph,{defaultValue:s,value:d,onValueChange:o,open:h,onOpenChange:e=>{f(e),e&&i&&i(new MouseEvent("click",{bubbles:!0,cancelable:!0}))},children:[(0,r.jsx)(A.i4,{ref:t,showCaret:n,className:"rounded-full border flex items-center justify-center border-seekers-text-lighter shadow-md h-10 px-2 !w-full ".concat(a),onClick:e=>{e.stopPropagation(),null==i||i(e)},children:(0,r.jsx)(A.ki,{className:"text-xs text-center"})}),(0,r.jsx)(A.Bw,{children:g.map(e=>(0,r.jsx)(A.Ql,{value:e.value,children:e.content},e.id))})]})})});y.displayName="CurrencyForm";var v=a(31056);function w(e){let{code:t}=e,a=v[t];return(0,r.jsx)(a,{className:"border border-colortext-foreground rounded-full w-4 h-4 aspect-square my-auto"})}var j=a(42586),N=a(53795),b=a(87301),C=a(91430);(0,b.cF)(async e=>{let{requestLocale:t}=e,r=await t;return{locale:await t,messages:(await a(90492)("./".concat(r,".json"))).default,defaultLocale:C.DI.defaultLocale,locales:C.DI.locales}});let{Link:k,redirect:E,usePathname:S,useRouter:R}=(0,N.os)({defaultLocale:"en",locales:["en","id"],pathnames:{"/":"/","/pathnames":{en:"/pathnames",de:"/pfadnamen",id:"/nama-jalur"}},localePrefix:"as-needed"});var T=a(71363);let _=(0,u.forwardRef)((e,t)=>{let{triggerClassName:a,showCaret:n=!1,defaultValue:s="en",onClick:i}=e,{changeLanguage:l,locale:o}=function(){let e=(0,j.useLocale)(),t=R(),a=S(),{generateQueryString:r}=(0,T.Z)();return{changeLanguage:e=>{let n=r("","");(0,u.startTransition)(()=>{t.replace(a+"?"+n,{locale:e}),t.refresh()})},locale:e}}(),c=[{id:"2",content:(0,r.jsx)(w,{code:"US"}),value:"EN"},{id:"1",content:(0,r.jsx)(w,{code:"ID"}),value:"ID"}],[d,m]=(0,u.useState)(!1);return(0,r.jsx)("div",{className:"max-sm:w-fit w-full",onClick:e=>e.stopPropagation(),children:(0,r.jsxs)(A.Ph,{defaultValue:s,onValueChange:l,value:o.toUpperCase(),open:d,onOpenChange:e=>{m(e),e&&i&&i(new MouseEvent("click",{bubbles:!0,cancelable:!0}))},children:[(0,r.jsx)(A.i4,{ref:t,showCaret:n,className:"rounded-full border border-seekers-text-lighter shadow-md h-10 w-14 px-2 flex items-center justify-center ".concat(a),onClick:e=>{e.stopPropagation(),null==i||i(e)},children:(0,r.jsx)(A.ki,{className:"text-xs"})}),(0,r.jsx)(A.Bw,{children:c.map(e=>(0,r.jsx)(A.Ql,{className:"",value:e.value,children:e.content},e.id))})]})})});_.displayName="SeekersLocaleForm";var U=a(64131),B=a(6404),Z=a(30078),z=a(70633),D=a(29827),L=a(21770),O=a(77647),P=a(79318),F=a(93166),I=a(26110),V=a(84190),J=a(61296),M=a(97496),H=a(71517);function Y(e){let{localeId:t="EN",currency_:a="EUR"}=e,n=(0,u.useRef)(null),s=(0,u.useRef)(null),i=(0,u.useRef)(null),l=(0,Z.L)(e=>e.role),[o,c]=(0,u.useState)(!1),[d,m]=(0,u.useState)(0),[f,A]=(0,u.useState)(null);return(0,u.useEffect)(()=>{let e=e=>{var t,a,r;let l=e.target,o=null===(t=i.current)||void 0===t?void 0:t.contains(l),d=null===(a=s.current)||void 0===a?void 0:a.contains(l);if(o){A("currency"),c(!0);return}if(d){A("language"),c(!0);return}(null===(r=n.current)||void 0===r?void 0:r.contains(l))||(c(!1),A(null))};return window.addEventListener("mousedown",e),()=>{window.removeEventListener("mousedown",e)}},[]),(0,u.useEffect)(()=>{let e=()=>{d!==window.scrollY-4&&(m(window.scrollY),c(!1))};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[d,c]),(0,r.jsxs)("div",{ref:n,className:"flex gap-2 items-center w-[166px] justify-between overflow-hidden h-[52px]",children:[(0,r.jsx)("div",{className:"w-fit",children:(0,r.jsx)(h.E.div,{className:"overflow-hidden shadow-md rounded-full border border-seekers-text-lighter flex",initial:{width:"110px"},animate:{width:o?"166px":"112px"},transition:{duration:.1},children:(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2 py-2 w-full",children:[(0,r.jsx)(y,{triggerClassName:(0,p.cn)("rounded-full border-none shadow-none !justify-between flex-grow !min-w-8 py-0 pr-0 !h-5 focus:ring-0",o?"w-full":"pl-3 max-w-[48px]"),defaultCurrency:a,ref:i,onClick:e=>{e.stopPropagation(),A("currency"),c(!0)},showCaret:o&&"currency"===f}),(0,r.jsx)("div",{className:"w-[2px] h-[24px] bg-seekers-text-lighter"}),(0,r.jsx)(_,{triggerClassName:(0,p.cn)("rounded-full flex-grow !justify-between !min-w-8 border-none shadow-none py-0 pl-0 !h-5 focus:ring-0",o?"w-full":"pl-2 max-w-[32px]"),defaultValue:t,ref:s,onClick:e=>{e.stopPropagation(),A("language"),c(!0)},showCaret:o&&"language"===f})]})})}),(0,r.jsx)(r.Fragment,{children:U.Z.get(B.LA)&&"SEEKER"==l?(0,r.jsx)(G,{trigger:(0,r.jsx)("button",{className:"border relative border-seekers-text-lighter shadow-md rounded-full overflow-hidden !h-10 !w-10",children:(0,r.jsx)(J.Z,{url:""})})}):(0,r.jsx)("div",{children:(0,r.jsx)(M.default,{triggerClassName:(0,p.cn)("!w-10 rounded-full overflow-hidden")})})})]})}function G(e){let{trigger:t}=e,a=(0,j.useTranslations)("seeker"),n=()=>{let e=document.getElementById("open-logout-dialog");null==e||e.click()};return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(V.h_,{modal:!1,children:[(0,r.jsx)(V.$F,{asChild:!0,children:t}),(0,r.jsxs)(V.AW,{align:"end",className:"!w-[256px]",children:[(0,r.jsx)(V.Xi,{asChild:!0,children:(0,r.jsx)(C.rU,{href:H.Fq,children:a("accountAndProfile.profile")})}),(0,r.jsx)(V.Xi,{asChild:!0,children:(0,r.jsx)(C.rU,{href:H.Y8,children:a("accountAndProfile.favorite")})}),(0,r.jsx)(V.Xi,{className:"w-full",asChild:!0,children:(0,r.jsx)(C.rU,{href:H.in,children:(0,r.jsx)("div",{className:"flex justify-between items-center w-full ",children:a("accountAndProfile.message")})})}),(0,r.jsx)(V.Xi,{onClick:e=>{e.preventDefault(),n()},children:a("accountAndProfile.logout.title")})]})]}),(0,r.jsx)(q,{trigger:(0,r.jsx)("button",{id:"open-logout-dialog"})})]})}function q(e){let{trigger:t}=e,[a,n]=(0,u.useState)(!1),s=function(){arguments.length>0&&void 0!==arguments[0]&&arguments[0];let e=(0,D.NL)();return(0,L.D)({mutationFn:()=>(0,z.kS)(),onSuccess:()=>{U.Z.remove(B.LA),U.Z.remove("user"),e.invalidateQueries({queryKey:[O.J],refetchType:"none"}),window.location.assign("/")},onError:e=>{U.Z.remove(B.LA),U.Z.remove("user"),window.location.assign("/")}})}("seekers"),i=(0,j.useTranslations)("seeker");return(0,r.jsxs)(P.Z,{open:a,setOpen:n,openTrigger:t,dialogClassName:"max-w-md",children:[(0,r.jsxs)(F.Z,{className:"text-start px-0",children:[(0,r.jsx)("h2",{className:"max-sm:text-center font-semibold",children:i("accountAndProfile.logout.title")}),(0,r.jsx)("p",{className:"max-sm:text-center max-sm:mb-4",children:i("owner.accountAndProfile.logout.description")})]}),(0,r.jsxs)(I.cN,{children:[(0,r.jsx)(o.z,{variant:"default-seekers",loading:s.isPending,className:"min-w-20 max-sm:order-last",onClick:()=>n(!1),children:i("cta.cancel")}),(0,r.jsx)(o.z,{variant:"ghost",onClick:()=>{if(U.Z.get(B.LA))s.mutate();else{window.location.assign("");return}},loading:s.isPending,className:"min-w-20",children:i("cta.logout")})]})]})}var Q=a(7729);let K=Q.fC;Q.xz;let W=Q.ee,X=u.forwardRef((e,t)=>{let{className:a,align:n="center",sideOffset:s=4,...i}=e;return(0,r.jsx)(Q.h_,{children:(0,r.jsx)(Q.VY,{ref:t,align:n,sideOffset:s,className:(0,p.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...i})})});X.displayName=Q.VY.displayName;var $=a(26815),ee=a(95186),et=a(32489),ea=a(39392),er=a(16593),en=a(16850),es=a(10407),ei=a(32660),el=a(30401),eo=a(83774),ec={src:"/_next/static/media/canggu.84e6fbe6.png",height:512,width:512,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAG1BMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACUUeIgAAAACXRSTlMBCyAwTkBmWH4H4C9lAAAACXBIWXMAAA7DAAAOwwHHb6hkAAAAGXRFWHRTb2Z0d2FyZQB3d3cuaW5rc2NhcGUub3Jnm+48GgAAADJJREFUeJwly7kNADAMw0Ba8rf/xIER1keAiOBSVhrwZE8B5e6yTuymdGZHpbOO8H3/fRYyAJhuEV+lAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},ed={src:"/_next/static/media/ubud.81668090.png",height:512,width:512,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAIVBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABt0UjBAAAAC3RSTlNrAVtEKhuADk09jhuwZCAAAAAJcEhZcwAADsQAAA7EAZUrDhsAAAAZdEVYdFNvZnR3YXJlAHd3dy5pbmtzY2FwZS5vcmeb7jwaAAAAMklEQVR4nAXBBwEAMAzDMCf94w94EiqPJDFOK2MoqsNxFNnYgQ43JNrkNWLtOD8I2AR9HbEA0czfdCsAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},eu={src:"/_next/static/media/nusa-dua.9acfd1fe.png",height:512,width:512,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAG1BMVEUAAAAAAAAAAABMaXEAAAAAAAAAAAAAAAAAAABReBoRAAAACXRSTlNsU34AN11hPRckhWnFAAAACXBIWXMAAA7EAAAOxAGVKw4bAAAAGXRFWHRTb2Z0d2FyZQB3d3cuaW5rc2NhcGUub3Jnm+48GgAAADJJREFUeJwdyUESwDAMArElgJ3/v7jT6CpsJ7bxTZtrzGN0CkVsJYnFGWny18Ii5jzzARpnALRddCEHAAAAAElFTkSuQmCC",blurWidth:8,blurHeight:8},em={src:"/_next/static/media/uluwatu.71df2404.png",height:512,width:512,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAHlBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAABMaXEAAAAAAAAKNf92AAAACnRSTlM5b3xVi2MwACERUm+ZFgAAAAlwSFlzAAAOxAAADsQBlSsOGwAAABl0RVh0U29mdHdhcmUAd3d3Lmlua3NjYXBlLm9yZ5vuPBoAAAAzSURBVHicBcGHAQAwDMIwQwbp/w9XIhEoIde7fSGlXVV4nt3xAzW0oMwdLtQGt5jyjGs+Je8A7/g673gAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8},eh={src:"/_next/static/media/seminyak.639fb2f5.png",height:512,width:512,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAMAAADz0U65AAAAIVBMVEUAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABt0UjBAAAAC3RSTlMCQTd4Wx9LESZpobATef0AAAAJcEhZcwAADsQAAA7EAZUrDhsAAAAZdEVYdFNvZnR3YXJlAHd3dy5pbmtzY2FwZS5vcmeb7jwaAAAANUlEQVR4nBXJtxHAQBADsSXPq/+CNY8UgKcbQBHxAd7NiwKXIi6BsVKql26VYaTufVeZp/kBHFQA0r0TCjUAAAAASUVORK5CYII=",blurWidth:8,blurHeight:8};function ef(e){let{locationName:t}=e;switch(t){case"canggu":return(0,r.jsx)(i.default,{src:ec,alt:"canggu",width:36,className:"aspect-square "});case"ubud":return(0,r.jsx)(i.default,{src:ed,alt:"ubud",width:32,className:"aspect-square "});case"seminyak":return(0,r.jsx)(i.default,{src:eh,alt:"Seminyak",width:32,className:"aspect-square"});case"uluwatu":return(0,r.jsx)(i.default,{src:em,alt:"uluwatu",width:32,className:"aspect-square "});case"Nusa Dua":return(0,r.jsx)(i.default,{src:eu,alt:"nusa dua",width:32,className:"aspect-square "});default:return(0,r.jsx)(r.Fragment,{})}}function ep(e){var t,a,s,i,l,o;let{showContent:c}=e,d=(0,j.useTranslations)("seeker"),{query:m}=(0,f.V)(e=>e),h=function(e){let{search:t}=e;return(0,er.a)({queryKey:["location-suggestion",t],queryFn:async()=>await (0,ea.IW)(e),retry:!1})}({search:(0,en.N)(m,500)}),{handleSetQuery:p,seekersSearch:A,banjars:x,showBanjars:g,selectedLocation:y,handleSelectLocation:v,handleBackToLocations:w,handleSetBanjar:N,filteredLocations:b,getMatchingBanjars:C}=(0,n.Z)();return(0,u.useEffect)(()=>{var e,t;if(h.isPending)return null==c?void 0:c(!0);b.length<=0&&(null===(t=h.data)||void 0===t?void 0:null===(e=t.data)||void 0===e?void 0:e.length)<=0?null==c||c(!1):null==c||c(!0)},[m.length,b.length,null===(a=h.data)||void 0===a?void 0:null===(t=a.data)||void 0===t?void 0:t.length,h.isPending,c]),(0,r.jsxs)(r.Fragment,{children:[g?(0,r.jsx)(r.Fragment,{children:(0,r.jsxs)("div",{className:"mt-4",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 mb-4  px-3 max-sm:px-0",children:[(0,r.jsx)("button",{onClick:e=>{e.preventDefault(),e.stopPropagation(),w()},className:"text-seekers-text-light hover:text-seekers-text",children:(0,r.jsx)(ei.Z,{className:"h-4 w-4"})}),(0,r.jsx)("span",{className:"font-medium capitalize",children:y})]}),(0,r.jsx)("div",{className:"grid grid-cols-2 gap-4 px-3 max-sm:px-0",children:x[y].map(e=>(0,r.jsx)("div",{className:"relative ",children:(0,r.jsxs)("button",{onClick:t=>{t.preventDefault(),t.stopPropagation(),N(e)},className:"w-full border border-gray-200 rounded-full py-3 px-4 flex items-center gap-3\n                                      ".concat(A.query.includes(e)?"bg-gray-50":"bg-white"),children:[(0,r.jsx)("div",{className:"w-4 h-4 rounded-full border flex items-center justify-center\n                                      ".concat(A.query.includes(e)?"border-seekers-primary bg-seekers-primary":"border-gray-300"),children:A.query.includes(e)&&(0,r.jsx)(el.Z,{className:"h-3 w-3 text-white"})}),(0,r.jsx)("span",{className:"text-sm text-seekers-text-light",children:e})]})},e))})]})}):(0,r.jsx)(r.Fragment,{children:b.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsx)("h2",{children:d("misc.region")}),b.map(e=>{var t;let a=e.name.replace(", Bali",""),n=(null===(t=x[e.value])||void 0===t?void 0:t.length)>0;return(0,r.jsx)("div",{children:(0,r.jsxs)("button",{className:"w-full flex items-center justify-between p-3 hover:bg-gray-100 transition-colors rounded-lg",onClick:t=>{t.preventDefault(),t.stopPropagation(),n?v(e.value):p(e.value)},children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)(ef,{locationName:e.value}),(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("div",{className:"font-medium",children:a}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.description})]})]}),n&&(0,r.jsx)(es.Z,{className:"h-4 w-4 text-gray-400"})]})},e.value)})]})}),m.length>=3&&!g&&(b.some(e=>C(e.value).length>0)||((null===(i=h.data)||void 0===i?void 0:null===(s=i.data)||void 0===s?void 0:s.length)||0)>0)&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"text-xs text-gray-500 font-medium mt-4 mb-2 px-3",children:d("misc.areas")}),(0,r.jsxs)("div",{children:[b.map(e=>{let t=C(e.value);return 0===t.length?null:t.map(t=>(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 p-3 hover:bg-gray-50 transition-colors rounded-lg",onClick:()=>{N(t,!0)},children:[(0,r.jsx)("div",{className:"w-8 h-8 flex items-center justify-center",children:(0,r.jsx)(eo.Z,{className:"w-4 h-4 text-seekers-primary"})}),(0,r.jsxs)("div",{className:"text-left",children:[(0,r.jsx)("div",{className:"font-medium",children:t}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.name})]})]},"".concat(e.name,"-").concat(t)))}),null===(o=h.data)||void 0===o?void 0:null===(l=o.data)||void 0===l?void 0:l.map((e,t)=>(0,r.jsxs)("button",{className:"w-full flex items-center gap-3 p-3 hover:bg-gray-50 transition-colors rounded-lg",onClick:()=>{N(e)},children:[(0,r.jsx)("div",{className:"w-8 h-8 flex items-center justify-center",children:(0,r.jsx)(eo.Z,{className:"w-4 h-4 text-seekers-primary"})}),(0,r.jsx)("div",{className:"text-left",children:(0,r.jsx)("div",{className:"font-medium",children:e})})]},t))]})]})]})}function eA(e){let{customTrigger:t,isUseAnimation:a=!0}=e,s=(0,j.useTranslations)("seeker"),[i,l]=(0,u.useState)(!1),{isOpen:c,setLocationInputFocused:d,query:m}=(0,f.V)(e=>e),[A,x]=(0,u.useState)(!0),{handleSetQuery:g,seekersSearch:y,handleSearch:v}=(0,n.Z)(),w=(0,u.useRef)(null),N=e=>{l(e),d(e)};return(0,r.jsx)("div",{className:(0,p.cn)(a?c?"w-full":"w-fit":"w-full"),onClick:()=>{var e;N(!0),null===(e=w.current)||void 0===e||e.focus()},children:(0,r.jsxs)(K,{open:i&&A,onOpenChange:N,children:[t?(0,r.jsx)(W,{asChild:!0,children:t}):(0,r.jsx)(W,{className:"w-full px-4",children:(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)($._,{className:"text-xs font-medium text-seekers-text",children:s("navbar.search.locationTitle")}),(0,r.jsxs)(h.E.div,{animate:{height:c?20:0,opacity:c?100:0,width:c?"100%":0},transition:{duration:.3},className:"flex justify-between",children:[(0,r.jsx)(ee.I,{ref:w,onFocus:e=>N(!0),onChange:e=>{g(e.target.value),x(!0)},value:m,placeholder:s("form.placeholder.seekersFindPropertyLocation"),onKeyDown:e=>{e.stopPropagation(),"Enter"===e.key&&(v(),l(!1))},className:"border-0 placeholder:text-seekers-text-lighter focus:outline-none shadow-none focus-visible:ring-0 focus-visible:border-b w-full rounded-none pb-2 !p-0 h-fit"}),(0,r.jsx)(o.z,{variant:"ghost",onClick:e=>{e.stopPropagation(),e.preventDefault(),y.setQuery("")},size:"icon",className:(0,p.cn)("-mt-2",y.query.length>0?"":"hidden"),children:(0,r.jsx)(et.Z,{})})]})]})}),(0,r.jsx)(X,{className:"w-full border-seekers-text-lighter/20",align:"start",onOpenAutoFocus:e=>e.preventDefault(),children:(0,r.jsx)(ep,{showContent:x})})]})})}var ex=a(40875),eg=a(85970),ey=a(84002),ev=a(92324);function ew(){let e=(0,j.useTranslations)("seeker"),{handleSearch:t}=(0,n.Z)(),[a,s]=(0,u.useState)(!1),[i,l]=(0,u.useState)("location"),{query:d}=(0,f.V)(e=>e),{handleSetType:m,seekersSearch:h,propertyType:A,handleSetQuery:x}=(0,n.Z)();return(0,en.N)(d,500),(0,r.jsxs)(P.Z,{open:a,setOpen:s,drawerClassName:"relative",openTrigger:(0,r.jsxs)("div",{className:"w-full border h-10 pl-4 pr-1 flex items-center justify-between text-seekers-text-light text-xs rounded-full border-seekers-text-lighter shadow-md",children:[(0,r.jsx)("span",{className:"line-clamp-1",children:e("listing.search.placeholder")}),(0,r.jsx)(o.z,{variant:"default-seekers",className:"rounded-full !h-8 !w-[2.25rem]",size:"icon",children:(0,r.jsx)(c.Z,{className:"!w-4 !h-4",strokeWidth:3})})]}),children:[(0,r.jsxs)("div",{className:"flex flex-col h-[calc(80vh-24px)] pb-16",children:[(0,r.jsxs)("div",{className:"flex-shrink-0 bg-white z-10 border-b",children:[(0,r.jsxs)(F.Z,{className:"px-0 !text-center",children:[(0,r.jsx)(ey.Z,{className:"font-semibold p-0",children:e("listing.search.title")}),(0,r.jsx)(I.Be,{children:e("misc.findYourPerfectProperty")})]}),(0,r.jsxs)("div",{className:"px-4 mb-4 relative",children:[(0,r.jsx)(ee.I,{type:"text",placeholder:"Search destinations",className:"w-full px-3 py-2 !text-sm !h-10",value:d,onChange:e=>{x(e.target.value)},onKeyDown:e=>{e.stopPropagation(),"Enter"===e.key&&(t(),s(!1))}}),(0,r.jsx)(et.Z,{className:"w-4 h-4 absolute right-7 top-1/2 -translate-y-1/2 text-seekers-text-light",onClick:()=>x("")})]})]}),(0,r.jsx)("div",{className:"flex-grow overflow-y-auto",children:(0,r.jsxs)("div",{className:"p-4 space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",onClick:()=>l("location"),children:[(0,r.jsx)("div",{className:"text-[#B88E57] font-medium mb-2",children:e("navbar.search.locationTitle")}),(0,r.jsx)(o.z,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0",children:(0,r.jsx)(ex.Z,{className:(0,p.cn)("h-4 w-4 transition-transform","location"==i?"transform rotate-180":"")})})]}),"location"==i&&(0,r.jsx)(ep,{})]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",onClick:()=>l("category"),children:[(0,r.jsx)("div",{className:"text-[#B88E57] font-medium mb-2",children:e("navbar.search.category")}),(0,r.jsx)(o.z,{variant:"ghost",size:"sm",className:"h-6 w-6 p-0",children:(0,r.jsx)(ex.Z,{className:(0,p.cn)("h-4 w-4 transition-transform","category"==i?"transform rotate-180":"")})})]}),"category"==i&&(0,r.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2",children:A.map(e=>(0,r.jsxs)("div",{className:"   border-seekers-text-lighter   hover:bg-seekers-background    gap-2 text-xs    text-seekers-text-light    flex flex-col    justify-center   items-center   p-4   relative   border   rounded-lg   ",onClick:t=>{t.preventDefault(),t.stopPropagation(),m(e.value)},children:[(0,r.jsx)("div",{className:(0,p.cn)("absolute top-3 left-3 rounded-full w-4 h-4 flex items-center justify-center",h.propertyType.includes(e.value)?"bg-seekers-primary":"border border-seekers-text-lighter"),children:(0,r.jsx)(el.Z,{className:(0,p.cn)(h.propertyType.includes(e.value)?"w-3 h-3 text-white":"hidden")})}),(0,r.jsx)(ev.Z,{category:e.value,className:"!w-6 !h-6"}),(0,r.jsx)("span",{className:"text-center",children:e.content})]},e.id))})]})]})})]}),(0,r.jsx)(eg.Z,{className:"absolute bottom-0 w-[calc(100%-32px)]",children:(0,r.jsxs)("div",{className:"flex justify-between items-center gap-4",children:[(0,r.jsx)(o.z,{variant:"link",className:"px-0 text-seekers-primary",onClick:()=>s(!1),children:e("cta.clearAll")}),(0,r.jsxs)(o.z,{className:"flex gap-2",variant:"default-seekers",onClick:()=>{t(),s(!1)},children:[(0,r.jsx)(c.Z,{}),e("cta.search")]})]})})]})}var ej=a(19404);function eN(e){let{value:t,textOnly:a=!1}=e,s=(0,j.useTranslations)("seeker"),{propertyTypeFormatHelper:i}=(0,n.Z)(),l=i(t.split(","));return t.includes("all")?a?s("listing.filter.category.all.title"):(0,r.jsx)("p",{children:s("listing.filter.category.all.title")}):a?l.toString().replace(","," ".concat(s("conjuntion.and")," ")):(0,r.jsx)(r.Fragment,{children:l.length>2?(0,r.jsx)(ej.Z,{trigger:(0,r.jsx)("div",{children:(0,r.jsxs)("p",{children:[l[0]," ",s("conjuntion.and")," ",(0,r.jsxs)("span",{children:["+ ",l.length-1," ",s("misc.more")]})]})}),content:l.toString().replaceAll(",",", "),contentClassName:"text-seekers-text"}):(0,r.jsx)("div",{children:(0,r.jsx)("p",{children:l.toString().replace(","," ".concat(s("conjuntion.and")," "))})})})}function eb(e){let{customTrigger:t}=e,a=(0,j.useTranslations)(),[s,i]=(0,u.useState)(!1),{isOpen:l,setCategoryInputFocused:c}=(0,f.V)(e=>e),{handleSetType:d,seekersSearch:m,propertyType:A}=(0,n.Z)();return(0,r.jsxs)(V.h_,{modal:!1,open:s,onOpenChange:e=>{i(e),c(e)},children:[(0,r.jsx)(V.$F,{asChild:!0,children:t||(0,r.jsxs)("div",{className:(0,p.cn)("px-2",l?"w-full":"w-0"),children:[(0,r.jsx)($._,{className:"text-xs font-medium text-seekers-text",children:a("seeker.navbar.search.category")}),(0,r.jsxs)(h.E.div,{animate:{height:l?20:0,opacity:l?100:0,width:l?"100%":0},transition:{duration:.3},className:"flex justify-between",children:[(0,r.jsx)(o.z,{variant:"ghost",className:"w-full h-fit font-normal p-0 overflow-hidden justify-start hover:bg-transparent",children:m.propertyType.length<1?(0,r.jsx)("p",{className:"text-seekers-text-lighter",children:a("seeker.navbar.search.propertyType")}):(0,r.jsx)(eN,{value:m.propertyType.toString()})}),(0,r.jsx)(o.z,{variant:"ghost",onClick:e=>{e.stopPropagation(),e.preventDefault(),m.clearCategory()},size:"icon",className:(0,p.cn)("-mt-2",m.propertyType.length>0?"":"hidden"),children:(0,r.jsx)(et.Z,{})})]})]})}),(0,r.jsx)(V.AW,{className:(0,p.cn)("border-seekers-text-lighter/20 grid grid-cols-2 sm:grid-cols-3 gap-3 p-4",l?"w-fit":"w-0"),align:"start",children:A.map(e=>(0,r.jsxs)("div",{className:"   border-seekers-text-lighter   hover:bg-seekers-background    gap-2 text-xs    text-seekers-text-light    flex flex-col    justify-center   items-center   md:w-28 p-4   relative   border   rounded-lg   ",onClick:t=>{t.preventDefault(),t.stopPropagation(),d(e.value)},"data-inside-dropdown":!0,children:[(0,r.jsx)("div",{className:(0,p.cn)("absolute top-3 left-3 rounded-full w-4 h-4 flex items-center justify-center",m.propertyType.includes(e.value)?"bg-seekers-primary":"border border-seekers-text-lighter"),children:(0,r.jsx)(el.Z,{className:(0,p.cn)(m.propertyType.includes(e.value)?"w-3 h-3 text-white":"hidden")})}),(0,r.jsx)(ev.Z,{category:e.value,className:"!w-6 !h-6"}),(0,r.jsx)("span",{className:"text-center",children:e.content})]},e.id))})]})}function eC(e){let{localeId:t="EN",currency_:a="EUR"}=e,{handleSearch:A}=(0,n.Z)(),[x,g]=(0,u.useState)(!1),y=(0,u.useRef)(null),v=(0,u.useRef)(null),{isOpen:w,setIsOpen:j,categoryInputFocused:N,locationInputFocused:b}=(0,f.V)(e=>e);return(0,u.useEffect)(()=>{let e=()=>{if(g(!1),window.scrollY<200){j(!0);return}window.scrollY>200&&(N||b)?j(!0):j(!1)};return window.addEventListener("scroll",e),()=>{window.removeEventListener("scroll",e)}},[N,w,b,j]),(0,u.useEffect)(()=>{if(N||b){j(!0);return}},[N,b,j]),(0,r.jsx)(m.M,{children:(0,r.jsxs)("nav",{ref:v,className:"w-full max-xl:space-y-4 border-b shadow-sm shadow-neutral-600/20 bg-white md:h-[90px] lg:h-[114px]",children:[(0,r.jsx)(s.Z,{className:"!h-full relative py-4 max-lg:space-y-4 xl:py-6 space-y-8 ",children:(0,r.jsxs)("div",{className:"w-full flex justify-between items-center flex-wrap gap-y-6",children:[(0,r.jsx)(k,{href:"/",children:(0,r.jsx)(i.default,{src:l.default,alt:"Property-Plaza",width:164,height:24})}),(0,r.jsxs)(h.E.div,{className:"flex gap-2 rounded-full p-2 border border-seekers-text-lighter shadow-md items-center max-lg:hidden pl-4",initial:{opacity:1,width:"60%"},animate:{width:w?"60%":"30%"},transition:{duration:.3},children:[(0,r.jsxs)("div",{className:"flex flex-grow items-center overflow-hidden divide-x-2 divide-seekers-text-lighter",children:[(0,r.jsx)("div",{className:"flex-grow min-w-[49%] max-w-[50%] pr-8",children:(0,r.jsx)(eA,{})}),(0,r.jsx)("div",{className:"flex-grow min-w-[49%] max-w-[50%] pl-8",children:(0,r.jsx)(eb,{})})]}),(0,r.jsx)(h.E.div,{initial:{height:48,width:48},animate:{height:w?48:36,width:w?48:36},transition:{duration:.3},children:(0,r.jsx)(o.z,{variant:"default-seekers",onClick:()=>A(),className:"rounded-full w-full h-full !aspect-square",size:"icon",children:(0,r.jsx)(c.Z,{className:"!w-5 !h-5",strokeWidth:3})})})]}),(0,r.jsx)("div",{className:"lg:hidden max-sm:w-full md:max-lg:w-[50%] max-sm:order-last flex gap-2",children:(0,r.jsx)(ew,{})}),(0,r.jsx)("div",{className:"md:hidden flex gap-1 w-[164px] justify-end",children:(0,r.jsx)(o.z,{variant:"ghost",className:"px-0 pl-4",onClick:()=>g(e=>!e),children:(0,r.jsx)(d.Z,{className:"!h-6 !w-6"})})}),(0,r.jsx)("div",{className:"max-md:hidden flex gap-2 items-center w-fit justify-end min-w-[136px]",children:(0,r.jsx)(Y,{currency_:a,localeId:t})})]})}),(0,r.jsx)("div",{className:(0,p.cn)(x?"fixed w-screen h-full bg-seekers-text/30 top-0 left-0 -z-10 !mt-0":"hidden")}),(0,r.jsx)("div",{ref:y,className:"absolute top-12 z-30 bg-background left-0 w-full flex gap-2 items-center justify-end  ".concat(x?"h-fit  py-4 px-4":"h-0"," overflow-hidden transition-all ease-linear duration-75 transform"),children:(0,r.jsx)(Y,{currency_:a,localeId:t})})]})})}},27668:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});var r=a(57437),n=a(94508);function s(e){return(0,r.jsx)("div",{...e,ref:e.ref,className:(0,n.cn)("xl:max-w-screen-2xl max-md:px-4 px-8 mx-auto space-y-12",e.className),children:e.children})}},19404:function(e,t,a){"use strict";a.d(t,{Z:function(){return s}});var r=a(57437);a(2265);var n=a(81103);function s(e){let{content:t,trigger:a,contentClassName:s}=e;return(0,r.jsx)(n.TooltipProvider,{delayDuration:100,children:(0,r.jsxs)(n.Tooltip,{children:[(0,r.jsx)(n.TooltipTrigger,{asChild:!0,children:a}),(0,r.jsx)(n.TooltipContent,{className:s,children:t})]})})}},84190:function(e,t,a){"use strict";a.d(t,{$F:function(){return c},AW:function(){return d},Ju:function(){return h},VD:function(){return f},Xi:function(){return u},bO:function(){return m},h_:function(){return o}});var r=a(57437),n=a(2265),s=a(28119),i=a(20653),l=a(94508);let o=s.fC,c=s.xz;s.ZA,s.Uv,s.Tr,s.Ee,n.forwardRef((e,t)=>{let{className:a,inset:n,children:o,...c}=e;return(0,r.jsxs)(s.fF,{ref:t,className:(0,l.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",n&&"pl-8",a),...c,children:[o,(0,r.jsx)(i.XCv,{className:"ml-auto h-4 w-4"})]})}).displayName=s.fF.displayName,n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)(s.tu,{ref:t,className:(0,l.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...n})}).displayName=s.tu.displayName;let d=n.forwardRef((e,t)=>{let{className:a,sideOffset:n=4,...i}=e;return(0,r.jsx)(s.Uv,{children:(0,r.jsx)(s.VY,{ref:t,sideOffset:n,className:(0,l.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...i})})});d.displayName=s.VY.displayName;let u=n.forwardRef((e,t)=>{let{className:a,inset:n,...i}=e;return(0,r.jsx)(s.ck,{ref:t,className:(0,l.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",n&&"pl-8",a),...i})});u.displayName=s.ck.displayName;let m=n.forwardRef((e,t)=>{let{className:a,children:n,checked:o,checkboxPosition:c="start",...d}=e;return(0,r.jsxs)(s.oC,{ref:t,className:(0,l.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5  text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50","start"==c?"pl-8 pr-2":"pl-2 pr-8",a),checked:o,...d,children:[(0,r.jsx)("span",{className:(0,l.cn)("absolute flex h-3.5 w-3.5 items-center justify-center","start"==c?"left-2":"right-2"),children:(0,r.jsx)(s.wU,{children:(0,r.jsx)(i.nQG,{className:"h-4 w-4"})})}),n]})});m.displayName=s.oC.displayName,n.forwardRef((e,t)=>{let{className:a,children:n,...o}=e;return(0,r.jsxs)(s.Rk,{ref:t,className:(0,l.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...o,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(s.wU,{children:(0,r.jsx)(i.jXb,{className:"h-4 w-4 fill-current"})})}),n]})}).displayName=s.Rk.displayName;let h=n.forwardRef((e,t)=>{let{className:a,inset:n,...i}=e;return(0,r.jsx)(s.__,{ref:t,className:(0,l.cn)("px-2 py-1.5 text-sm font-semibold",n&&"pl-8",a),...i})});h.displayName=s.__.displayName;let f=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)(s.Z0,{ref:t,className:(0,l.cn)("-mx-1 my-1 h-px bg-muted",a),...n})});f.displayName=s.Z0.displayName},53647:function(e,t,a){"use strict";a.d(t,{Bw:function(){return h},Ph:function(){return o},Ql:function(){return f},i4:function(){return d},ki:function(){return c}});var r=a(57437),n=a(2265),s=a(20653),i=a(74797),l=a(94508);let o=i.fC;i.ZA;let c=i.B4,d=n.forwardRef((e,t)=>{let{className:a,children:n,showCaret:o=!0,...c}=e;return(0,r.jsxs)(i.xz,{ref:t,className:(0,l.cn)("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...c,children:[n,o&&(0,r.jsx)(i.JO,{asChild:!0,children:(0,r.jsx)(s.jnn,{className:"h-4 w-4 opacity-50"})})]})});d.displayName=i.xz.displayName;let u=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)(i.u_,{ref:t,className:(0,l.cn)("flex cursor-default items-center justify-center py-1",a),...n,children:(0,r.jsx)(s.g8U,{})})});u.displayName=i.u_.displayName;let m=n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)(i.$G,{ref:t,className:(0,l.cn)("flex cursor-default items-center justify-center py-1",a),...n,children:(0,r.jsx)(s.v4q,{})})});m.displayName=i.$G.displayName;let h=n.forwardRef((e,t)=>{let{className:a,children:n,position:s="popper",...o}=e;return(0,r.jsx)(i.h_,{children:(0,r.jsxs)(i.VY,{ref:t,className:(0,l.cn)("relative z-50 max-h-96 w-fit overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:s,...o,children:[(0,r.jsx)(u,{}),(0,r.jsx)(i.l_,{className:(0,l.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:n}),(0,r.jsx)(m,{})]})})});h.displayName=i.VY.displayName,n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)(i.__,{ref:t,className:(0,l.cn)("px-2 py-1.5 text-sm font-semibold",a),...n})}).displayName=i.__.displayName;let f=n.forwardRef((e,t)=>{let{className:a,children:n,...o}=e;return(0,r.jsxs)(i.ck,{ref:t,className:(0,l.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...o,children:[(0,r.jsx)("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(i.wU,{children:(0,r.jsx)(s.nQG,{className:"h-4 w-4"})})}),(0,r.jsx)(i.eT,{children:n})]})});f.displayName=i.ck.displayName,n.forwardRef((e,t)=>{let{className:a,...n}=e;return(0,r.jsx)(i.Z0,{ref:t,className:(0,l.cn)("-mx-1 my-1 h-px bg-muted",a),...n})}).displayName=i.Z0.displayName},81103:function(e,t,a){"use strict";a.d(t,{Tooltip:function(){return o},TooltipContent:function(){return d},TooltipProvider:function(){return l},TooltipTrigger:function(){return c}});var r=a(57437),n=a(2265),s=a(93920),i=a(94508);let l=s.zt,o=s.fC,c=s.xz,d=n.forwardRef((e,t)=>{let{className:a,sideOffset:n=4,...l}=e;return(0,r.jsx)(s.VY,{ref:t,sideOffset:n,className:(0,i.cn)("z-50 overflow-hidden rounded-md bg-background px-3 py-1.5 text-xs text-primary border-primary animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",a),...l})});d.displayName=s.VY.displayName},57612:function(e,t,a){"use strict";a.d(t,{JS:function(){return i},e:function(){return n},i6:function(){return s},p5:function(){return l},yJ:function(){return r}});let r={villas:"VILLA",apartment:"APARTMENT",rooms:"ROOM",commercialSpace:"COMMERCIAL_SPACE",cafeOrRestaurants:"CAFE_RESTAURANT",offices:"OFFICE",shops:"SHOP",shellAndCore:"SHELL_CORE",lands:"LAND",guestHouse:"GUESTHOUSE",homestay:"HOMESTAY",ruko:"RUKO",villa:"VILLA"},n={all:"ANY",mountain:"MOUNTAIN",ocean:"OCEAN",ricefield:"RICEFIELD",jungle:"JUNGLE"},s={anything:"ANY",placeToLive:"PLACE_TO_LIVE",business:"BUSINESS",land:"LAND"},i={plumbing:"PLUMBING",subleaseAllowed:"SUBLEASE_ALLOWED",balcony:"BALCONY",gazebo:"GAZEBO",recentlyRenovated:"RECENTLY_RENOVATED",airCondition:"AIR_CONDITION",constructionNearby:"CONSTRUCTION_NEARBY",rooftopTerrace:"ROOFTOP_TERRACE",terrace:"TERRACE",petAllowed:"PET_ALLOWED",garden:"GARDEN_BACKYARD",bathub:"BATHUB"},l={small:{min:1,max:300,key:"small"},medium:{min:301,max:1e3,key:"medium"},large:{min:1001,max:1e5,key:"large"}}},55436:function(e,t,a){"use strict";a.d(t,{D4:function(){return n},FH:function(){return r}});let r=e=>e.toLowerCase().includes("day")?"DAY":e.toLowerCase().includes("week")?"WEEK":e.toLowerCase().includes("month")?"MONTH":e.toLowerCase().includes("year")?"YEAR":"MONTH",n=e=>"ONLINE"==e},98766:function(e,t,a){"use strict";a.d(t,{F4:function(){return o},JG:function(){return d},KC:function(){return i},Mz:function(){return c},R6:function(){return l},T_:function(){return s},x0:function(){return n}});var r=a(49607);a(56083),a(55102);let n=e=>r.apiClient.post("properties/favorite",e),s=e=>(0,r.apiClient)("/properties/filter-location?search=".concat(e.search)),i=(e,t)=>r.apiClient.post("properties/filter",e,{headers:{"g-token":t||""}}),l=()=>r.apiClient.get("filter-parameter"),o=e=>{let{page:t,per_page:a,search:n,sort_by:s}=e;return r.apiClient.get("users/favorite?page=".concat(t,"&per_page=").concat(a,"&search=").concat(n,"&sort_by=").concat(s))},c=e=>r.apiClient.put("users/filter-setting",e),d=e=>r.apiClient.post("properties/batch-property",e)},39392:function(e,t,a){"use strict";a.d(t,{f4:function(){return d},sK:function(){return f},_o:function(){return h},p:function(){return u},IW:function(){return m}});var r=a(33254),n=a(98766);a(55436);var s=a(8946),i=a.n(s),l=a(67481);function o(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"en";return e.map(e=>{var a,n,s,i,l,o;return{code:e.code,geolocation:c(e.location.latitude,e.location.longitude),location:e.location.district+", "+e.location.city+", "+e.location.province,price:e.availability.price,thumbnail:(o=e.code,e.images.map((e,t)=>({id:o+t,image:e.image,isHighlight:e.is_highlight})).sort((e,t)=>+t.isHighlight-+e.isHighlight)),title:(0,r.P)(e.title,t),listingDetail:{bathRoom:e.detail.bathroom_total,bedRoom:e.detail.bedroom_total,buildingSize:e.detail.building_size,landSize:e.detail.land_size,cascoStatus:e.detail.casco_status,gardenSize:e.detail.garden_size},availability:{availableAt:e.availability.available_at||"",maxDuration:(null===(a=e.availability.duration_max_unit)||void 0===a?void 0:a.value)&&e.availability.duration_max?{value:e.availability.duration_max||1,suffix:null===(n=e.availability.duration_max_unit)||void 0===n?void 0:n.value}:null,minDuration:(null===(s=e.availability.duration_min_unit)||void 0===s?void 0:s.value)&&e.availability.duration_min?{value:e.availability.duration_min||1,suffix:null===(i=e.availability.duration_min_unit)||void 0===i?void 0:i.value}:null,type:e.availability.type.value||""},sellingPoint:e.features.selling_points,category:e.detail.option.type,isFavorite:(null==e?void 0:null===(l=e._count)||void 0===l?void 0:l.favorites)>0,status:e.status}})}let c=function(e,t){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10,r=1/111320*a;return[e+.4*r,t+.4*r]};async function d(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"en";try{let a=await (0,n.JG)({property_list:e});return{data:o(a.data.data,t),locale:t}}catch(e){var a;return{error:null!==(a=e.data.error)&&void 0!==a?a:"An unknown error occurred"}}}async function u(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"en";try{let a=await (0,n.KC)(e);try{let t=Object.fromEntries(Object.entries(e).filter(e=>{let[t,a]=e;return void 0!==a}));2!==Object.keys(t).length&&await (0,n.Mz)(e)}catch(e){}return{data:o(a.data.data.items,t),meta:(0,r.N)(a.data.data.meta)}}catch(e){var a;return{error:null!==(a=e.data.error)&&void 0!==a?a:"An unknown error occurred"}}}async function m(e){if(e.search.length<3)return{data:[]};try{let t=await (0,n.T_)(e);return{data:function(e,t){let a=[];return t.forEach(t=>{Object.values(t).forEach(t=>{(function(e,t){let a=(0,l.Z)(e.toLowerCase(),t.toLowerCase());return 1-a/Math.max(e.length,t.length)})(t,e)>0&&a.push(t)})}),i().uniq(a)}(e.search,t.data.data)}}catch(e){var t;return{error:null!==(t=e.data.error)&&void 0!==t?t:"An unknown error occurred"}}}async function h(){var e,t;try{return{data:{priceRange:{min:(e=(await (0,n.R6)()).data.data).price_range._min.price,max:e.price_range._max.price},buildingSizeRange:{max:e.size_range._max.building_size,min:e.size_range._min.building_size},gardenSizeRange:{max:e.size_range._max.garden_size,min:e.size_range._min.garden_size},landSizeRange:{max:e.size_range._max.land_size,min:e.size_range._min.land_size},furnishingOptions:e.furnishing_options[0].childrens.map(e=>({title:e.title,value:e.value})),livingOptions:e.living_options[0].childrens.map(e=>({title:e.title,value:e.value})),parkingOptions:e.parking_options[0].childrens.map(e=>({title:e.title,value:e.value})),poolOptions:e.pool_options[0].childrens.map(e=>({title:e.title,value:e.value}))},meta:void 0}}catch(e){return{error:null!==(t=e.data.error)&&void 0!==t?t:"An unknown error occurred"}}}async function f(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"en";try{let a=await (0,n.F4)({page:+e.page,per_page:+e.per_page,search:e.search||"",sort_by:e.sort_by});return{data:o(a.data.data.items,t),meta:(0,r.N)(a.data.data.meta)}}catch(e){var a;return{error:null!==(a=e.data.error)&&void 0!==a?a:"An unknown error occurred"}}}},33254:function(e,t,a){"use strict";function r(e){return{nextPage:e.next_page,page:e.page,pageCount:e.page_count,perPage:e.per_page,prevPage:e.prev_page,total:e.total}}function n(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"en";if(!e)return"";if("string"==typeof e)return e;let a=e.find(e=>e.lang===t);return(null==a?void 0:a.value)||e[0].value}a.d(t,{N:function(){return r},P:function(){return n}})},16850:function(e,t,a){"use strict";a.d(t,{N:function(){return n}});var r=a(2265);let n=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:500,[a,n]=(0,r.useState)(e);return(0,r.useEffect)(()=>{let a=setTimeout(()=>{n(e)},t);return()=>{clearTimeout(a)}},[e,t]),a}},71363:function(e,t,a){"use strict";a.d(t,{Z:function(){return i}});var r=a(99376),n=a(2265),s=a(75189);function i(){let e=(0,s.useRouter)(),t=(0,r.usePathname)(),a=(0,r.useSearchParams)(),i=(0,n.useCallback)(r=>{let n=new URLSearchParams(a.toString());r.forEach(e=>n.set(e.name,e.value)),e.push(t+"?"+n.toString())},[a,e,t]),l=(0,n.useCallback)((e,t)=>{let r=new URLSearchParams(a.toString());return r.set(e,t),r.toString()},[a]);return{searchParams:a,createQueryString:(r,n)=>{let s=new URLSearchParams(a.toString());s.set(r,n),e.push(t+"?"+s.toString())},generateQueryString:l,removeQueryParam:(t,r)=>{let n=new URLSearchParams(a.toString());t.forEach(e=>{n.delete(e)});let s="".concat(window.location.pathname,"?").concat(n.toString());if(r)return window.location.href=s;e.push(s)},createMultipleQueryString:i,pathname:t,updateQuery:(r,n)=>{let s=new URLSearchParams(a.toString());s.set(r,n),e.push(t+"?"+s.toString())}}}},51179:function(e,t,a){"use strict";a.d(t,{Z:function(){return u}});var r=a(74316),n=a(2265),s=a(71517),i=a(94508),l=a(75189),o=a(42586),c=a(57612),d=a(6404);function u(){let e=(0,o.useTranslations)(),t=(0,r.V)(e=>e),[a,u]=(0,n.useState)(!1),[m,h]=(0,n.useState)(null),f=(0,l.useRouter)(),p=[{content:e("seeker.listing.category.villa"),id:"1",value:c.yJ.villas},{content:e("seeker.listing.category.apartment"),id:"2",value:c.yJ.apartment},{content:e("seeker.listing.category.guestHouse"),id:"3",value:c.yJ.rooms},{content:e("seeker.listing.category.commercial"),id:"4",value:c.yJ.commercialSpace},{content:e("seeker.listing.category.cafeAndRestaurent"),id:"5",value:c.yJ.cafeOrRestaurants},{content:e("seeker.listing.category.office"),id:"6",value:c.yJ.offices},{content:e("seeker.listing.category.shops"),id:"7",value:c.yJ.shops},{content:e("seeker.listing.category.shellAndCore"),id:"8",value:c.yJ.shellAndCore},{content:e("seeker.listing.category.land"),id:"9",value:c.yJ.lands}],A=[{name:"Canggu, Bali",description:"Popular surf spot & digital nomad hub",icon:"Canggu",value:"canggu"},{name:"Ubud, Bali",description:"Cultural heart with rice terraces",value:"ubud",icon:"Ubud"},{name:"Seminyak, Bali",description:"Upscale beach resort area",icon:"Seminyak",value:"seminyak"},{name:"Uluwatu, Bali",description:"Clifftop temples & luxury resorts",icon:"Uluwatu",value:"uluwatu"},{name:"Nusa Dua, Bali",description:"Gated resort area with pristine beaches",icon:"NusaDua",value:"Nusa Dua"}],x={canggu:["Babakan","Batu Bolong","Berawa","Cemagi","Cempaka","Echo Beach","Kayu Tulang","Munggu","Nelayan","North Canggu","Nyanyi","Padonan","Pantai Lima","Pererenan","Seseh","Tiying Tutul","Tumbak Bayuh"].sort(),ubud:["Bentuyung","Junjungan","Kedewatan","Nyuh Kuning","Penestanan","Sambahan","Sanggingan","Taman Kaja","Tegallantang","Ubud Center"],uluwatu:["Balangan","Bingin","Green Bowl","Karang Boma","Nyang Nyang","Padang Padang","Pecatu","Suluban"],nusaDua:["Benoa","BTDC Area","Bualu","Kampial","Peminge","Sawangan","Tanjung Benoa"],seminyak:[]},g=(0,n.useMemo)(()=>!t.query||a?A:A.filter(e=>{let a=e.name.replace(", Bali","").toLowerCase(),r=t.query.toLowerCase();return!!a.includes(r)||(x[e.value]||[]).some(e=>e.toLowerCase().includes(r))}),[t.query,a]);return{seekersSearch:t,handleSetQuery:e=>{let a=e.split(","),r=e.length;a.length>3&&","==e.charAt(r-1)||t.setQuery(e)},handleSetType:e=>{(!(t.propertyType.length>=3)||t.propertyType.includes(e))&&t.setPropertyType(e)},propertyType:p,handleSearch:(e,a)=>{e&&t.setQuery(e),a&&t.setPropertyTypeFromArray(a);let r=e||t.query,n=a||t.propertyType;""!==t.activeSearch.query&&t.setSearchHistory({propertyType:t.activeSearch.propertyType,query:t.activeSearch.query}),t.setActiveSearch({query:r,propertyType:n});let l=(0,i.Fg)(r);f.push(s.rr+"/"+(l||"all")+"?"+d.Y.type+"="+(n.toString()||"all"))},propertyTypeFormatHelper:e=>e.map(e=>{let t=p.find(t=>t.value==e);return null==t?void 0:t.content}),locations:A,banjars:x,getMatchingBanjars:e=>{let a=t.query;return a?(x[e]||[]).filter(e=>e.toLowerCase().includes(a.toLowerCase())):[]},showBanjars:a,setShowBanjars:u,selectedLocation:m,setSelectedLocation:h,handleSelectLocation:e=>{u(!0),h(e),t.setQuery(e)},handleBackToLocations:()=>{u(!1);let e=t.query.replace(m||"","");t.setQuery(e)},handleSetBanjar:function(e){let a=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=t.query.split(",").filter(e=>""!==e.trim()&&e!==m);if(r.includes(e)){let a=r.filter(t=>t!==e);t.setQuery(a.toString());return}if(!(r.length>=3)||""===r[r.length-1]){if(a){let t=r.length;r[t-1]=e}else r.push(e);t.setQuery(r.toString())}},filteredLocations:g}}},91430:function(e,t,a){"use strict";a.d(t,{DI:function(){return n},jD:function(){return l},rU:function(){return s}});var r=a(53795);let n={locales:["en","id"],defaultLocale:"en"},{Link:s,redirect:i,usePathname:l,useRouter:o}=(0,r.os)(n)},74316:function(e,t,a){"use strict";a.d(t,{V:function(){return o}});var r=a(94508),n=a(77398),s=a.n(n),i=a(59625),l=a(89134);let o=(0,i.Ue)()((0,l.tJ)(e=>({activeSearch:{propertyType:[],query:""},propertyType:[],query:"",searchHistory:[],isOpen:!0,locationInputFocused:!1,categoryInputFocused:!1,setActiveSearch:t=>e({activeSearch:t}),setPropertyType:t=>e(e=>({propertyType:(0,r.ET)(e.propertyType,t)})),setQuery:t=>e({query:t}),setSearchHistory:t=>e(e=>{let a={...t,validUntil:s()().add(7,"days").format("DD-MMM-YYYY")};if(e.searchHistory.findIndex(e=>e.query==a.query)>=0)return e;let r=[...e.searchHistory,a];if(e.searchHistory.length<5)return e.searchHistory=r,e;let n=r.slice(1,4);return e.searchHistory=[...n,a],e}),setIsOpen:t=>e({isOpen:t}),setCategoryInputFocused:t=>e({categoryInputFocused:t}),setLocationInputFocused:t=>e({locationInputFocused:t}),clearSearch:()=>e({query:"",propertyType:[]}),setPropertyTypeFromArray:t=>e({propertyType:t}),clearCategory:()=>e({propertyType:[]})}),{name:"seeker-search",storage:(0,l.FL)(()=>localStorage),onRehydrateStorage(e){if(!e)return;let t=e.searchHistory.filter(e=>{let t=s()(e.validUntil);return s()().isSameOrBefore(t)});e.searchHistory=t}}))},69729:function(e,t,a){"use strict";a.r(t),t.default={src:"/_next/static/media/property-seekers-main-logo.2a8a0666.png",height:128,width:473,blurDataURL:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAACCAMAAABSSm3fAAAADFBMVEW1i1SxiVK1jFW1jFUX2lE4AAAABHRSTlM+F2AiCpN2vgAAAAlwSFlzAAALEwAACxMBAJqcGAAAABhJREFUeJwFwQEBAAAIwyDm+3cWoFrOYT0AhwAQ9FQy9wAAAABJRU5ErkJggg==",blurWidth:8,blurHeight:2}}}]);