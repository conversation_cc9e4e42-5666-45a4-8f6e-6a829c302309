(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1849],{61666:function(e,t,n){Promise.resolve().then(n.bind(n,77068)),Promise.resolve().then(n.bind(n,33162)),Promise.resolve().then(n.bind(n,12545)),Promise.resolve().then(n.bind(n,97867)),Promise.resolve().then(n.bind(n,31085)),Promise.resolve().then(n.bind(n,10575))},77068:function(e,t,n){"use strict";n.d(t,{default:function(){return b}});var a=n(57437),s=n(16831),r=n(62869),l=n(93022),i=n(49607);let o=e=>i.apiClient.post("file-storage",e,{headers:{"Content-Type":"multipart/form-data"}});var d=n(35153),c=n(21770),u=n(42586),f=n(24596),m=n(77647),h=n(58996),x=n.n(h);async function p(e,t){try{return await new Promise((n,a)=>{new(x())(e,{quality:.5,mimeType:"image/webp",success:async e=>{n(e)},error:async e=>{a(e)},...t})})}catch(e){return e}}var N=n(30078),g=n(70650),j=n(2265);function b(){let e=(0,u.useTranslations)("seeker"),{toast:t}=(0,d.pm)(),{image:n,firstName:i,lastName:h}=(0,N.L)(e=>e.seekers.accounts),x=(0,m.l)(),b=function(){let{toast:e}=(0,d.pm)(),t=(0,u.useTranslations)();return(0,c.D)({mutationFn:e=>o(e),onSuccess:e=>e.data.data.url,onError:n=>{let a=n.response.data;e({title:t("misc.foundError"),description:a.message,variant:"destructive"})}})}(),v=(0,f.K)(),w=async n=>{let a=n.target.files;if(null==a||(null==a?void 0:a.length)==0){t({title:e("misc.noImageUploaded")});return}let s=await p(a[0],{width:240,height:240,resize:"cover"});if(void 0==s)return;let r=new FormData;r.append("file",s);try{let e=await b.mutateAsync(r);if(e){let t=e.data.data.url;v.mutate({image:t})}}catch(e){return}},y=(0,j.useRef)(null);return(0,a.jsxs)("div",{className:"relative flex justify-start items-center gap-4",children:[x.isLoading?(0,a.jsx)(l.O,{className:"w-[96px] h-[96px] rounded-full"}):(0,a.jsxs)(s.qE,{className:"w-[96px] h-[96px] relative border",children:[n?(0,a.jsx)(g.F$,{src:n,width:96,height:96,alt:"example",className:"rounded-full"}):(0,a.jsxs)(s.Q5,{children:[i.charAt(0),h.charAt(0)]}),(0,a.jsx)("input",{type:"file",className:"hidden",accept:"image/*",ref:y,onChange:w})]}),(0,a.jsx)(r.z,{onClick:()=>{var e;return null===(e=y.current)||void 0===e?void 0:e.click()},variant:"outline",loading:b.isPending||v.isPending,className:"shadow-none",children:e("cta.changePhoto")})]})}},33162:function(e,t,n){"use strict";n.d(t,{default:function(){return R}});var a=n(57437),s=n(42586),r=n(83037),l=n.n(r),i=n(31229),o=n(29501),d=n(13590),c=n(30078),u=n(2265),f=n(62869),m=n(61729),h=n(15681),x=n(95186),p=n(45675),N=n(32489),g=n(30401);function j(e){let{setDisabledStatus:t,isFormDisabled:n}=e;return(0,a.jsx)(a.Fragment,{children:n?(0,a.jsx)(f.z,{variant:"ghost",className:" rounded-none bg-neutral-lightest",onClick:()=>t(!1,!1),children:(0,a.jsx)(p.Z,{className:"w-4 h-4"})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.z,{variant:"ghost",className:"rounded-none bg-neutral-lightest",onClick:()=>{t(!0,!1)},children:(0,a.jsx)(N.Z,{className:"w-4 h-4"})}),(0,a.jsx)(f.z,{variant:"ghost",className:"rounded-none bg-neutral-lightest",onClick:()=>t(!0,!0),children:(0,a.jsx)(g.Z,{className:"w-4 h-4"})})]})})}var b=n(75422),v=n(94508);function w(e){let{form:t,label:n,name:s,placeholder:r,description:l,children:i,isEditable:o,inputProps:d}=e,[c,f]=(0,u.useState)(!0),m=(e,n)=>{n||t.reset(),f(e)};return(0,a.jsx)(h.Wi,{control:t.control,name:s,render:e=>{let{field:t}=e;return(0,a.jsx)(b.Z,{label:n,description:l,children:(0,a.jsxs)("div",{className:"flex items-center w-full border rounded-sm focus-within:border-neutral-light overflow-hidden",children:[(0,a.jsx)(x.I,{type:"email",placeholder:r,...t,...d,disabled:c,className:(0,v.cn)("border-none focus:outline-none shadow-none focus-visible:ring-0",null==d?void 0:d.className)}),i,null!=o?o:(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(j,{setDisabledStatus:m,isFormDisabled:c})})]})})}})}function y(e){let{form:t,label:n,name:s,placeholder:r,description:l,children:i,isEditable:o,inputProps:d}=e,[c,f]=(0,u.useState)(!0),m=(e,n)=>{n||t.reset(),f(e)};return(0,a.jsx)(h.Wi,{control:t.control,name:s,render:e=>{let{field:t}=e;return(0,a.jsx)(b.Z,{label:n,description:l,children:(0,a.jsxs)("div",{className:"flex items-center w-full border rounded-sm focus-within:border-neutral-light overflow-hidden",children:[(0,a.jsx)(x.I,{type:"tel",placeholder:r,...t,...d,disabled:c,className:(0,v.cn)("border-none focus:outline-none shadow-none focus-visible:ring-0",null==d?void 0:d.className)}),i,null!=o?o:(0,a.jsx)(a.Fragment,{children:(0,a.jsx)(j,{setDisabledStatus:m,isFormDisabled:c})})]})})}})}var k=n(24596),C=n(49651),z=n.n(C),Z=n(85970),P=n(93166),V=n(84002),F=n(79318),D=n(26110);function E(e){let{currentVal:t,onChange:n,trigger:r,type:i}=e,o=(0,s.useTranslations)("seeker"),[d,c]=(0,u.useState)(t),[m,h]=(0,u.useState)(!1),[p,N]=(0,u.useState)(null);(0,u.useEffect)(()=>{c(t)},[t]);let g=e=>/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(String(e).toLowerCase()),j=e=>{c(e.target.value),N(null)};return(0,a.jsxs)(F.Z,{open:m,openTrigger:r,setOpen:h,children:[(0,a.jsxs)(P.Z,{children:[(0,a.jsx)(V.Z,{className:"text-base font-bold text-seekers-text",children:o("settings.personalInfo.change".concat(i.charAt(0).toUpperCase()+i.slice(1),".title"))}),(0,a.jsx)(D.Be,{className:"max-w-sm",children:o("settings.personalInfo.change".concat(i.charAt(0).toUpperCase()+i.slice(1),".description"))})]}),(0,a.jsxs)("div",{className:" py-4 w-full",children:[(0,a.jsx)("div",{className:"items-center gap-4",children:(0,a.jsx)(x.I,{id:"new-value",type:"email"===i?"email":"tel",className:"col-span-3 w-full border border-input",value:d,onChange:e=>j(e)})}),p&&(0,a.jsx)("p",{className:"text-red-500 text-sm",children:p})]}),(0,a.jsx)(Z.Z,{children:(0,a.jsx)(f.z,{type:"submit",variant:"default-seekers",onClick:()=>{if("email"===i){if(!g(d)){N(o("form.utility.invalidFormat",{field:o("form.label.email")}));return}}else if("phone"===i&&!l()(d).isValid){N(o("form.utility.invalidFormat",{field:o("form.label.phoneNumber")}));return}n(d),h(!1)},children:o("cta.confirm")})})]})}function R(){let e=function(){let e=(0,s.useTranslations)("seeker");return i.z.object({id:i.z.string(),firstName:i.z.string().min(1,{message:e("form.utility.fieldRequired",{field:e("form.field.firstName")})}),lastName:i.z.string().min(1,{message:e("form.utility.fieldRequired",{field:e("form.field.lastName")})}),email:i.z.string().email({message:e("form.utility.enterValidField",{field:" ".concat(e("form.field.email"))})}).refine(e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),{message:e("form.utility.enterValidField",{field:" ".concat(e("form.field.email"))})}),phoneNumber:i.z.string().refine(e=>l()(e).isValid,{message:e("form.utility.enterValidField",{field:"".concat(e("form.field.phoneNumber")," ")})})})}(),t=(0,s.useTranslations)("seeker"),{email:n,phoneCode:r,phoneNumber:x,code:p,accounts:{firstName:N,lastName:g}}=(0,c.L)(e=>e.seekers),j=(0,k.K)(),b=(0,o.cI)({resolver:(0,d.F)(e),defaultValues:{id:"",firstName:"",lastName:"",email:"",phoneNumber:""}});(0,u.useEffect)(()=>{n&&b.setValue("email",n),p&&b.setValue("id",p),N&&b.setValue("firstName",N),g&&b.setValue("lastName",g),r&&x&&b.setValue("phoneNumber",r+x)},[n,N,g,x,r,p,b]);let v=async e=>{var t;let n=l()(e.phoneNumber),a={phone_number:null===(t=n.phoneNumber)||void 0===t?void 0:t.replace(n.countryCode,""),phone_code:n.countryCode||"",first_name:e.firstName,last_name:e.lastName};await j.mutateAsync(a)};return(0,a.jsx)(h.l0,{...b,children:(0,a.jsxs)("form",{onSubmit:b.handleSubmit(v),children:[(0,a.jsxs)("div",{className:"grid max-sm:grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)(m.Z,{form:b,label:t("form.label.firstName"),name:"firstName",placeholder:t("form.placeholder.basePlaceholder",{field:t("form.field.firstName")}),type:"string"}),(0,a.jsx)(m.Z,{form:b,label:t("form.label.lastName"),name:"lastName",placeholder:t("form.placeholder.basePlaceholder",{field:t("form.field.lastName")}),type:"string"}),(0,a.jsx)("div",{className:"flex gap-2 justify-between items-end",children:(0,a.jsx)(w,{form:b,label:t("form.label.email"),name:"email",placeholder:"",isEditable:!1,inputProps:{className:"min-w-full max-w-full"}})}),(0,a.jsxs)("div",{className:"flex gap-2 items-end",children:[(0,a.jsx)(y,{form:b,label:t("form.label.phoneNumber"),name:"phoneNumber",placeholder:"",isEditable:!1,inputProps:{className:"inputProps"}}),(0,a.jsx)(E,{onChange:e=>b.setValue("phoneNumber",e),currentVal:b.getValues("phoneNumber"),trigger:(0,a.jsx)(f.z,{type:"button",variant:"outline",className:"shadow-none h-[34px] md:h-[38px]",children:t("cta.change")}),type:"phone"})]})]}),(0,a.jsx)(f.z,{className:"my-8 min-w-40 ",variant:"default-seekers",type:"submit",loading:j.isPending,children:t("cta.saveChanges")})]})})}z().PhoneNumberUtil.getInstance()},85970:function(e,t,n){"use strict";n.d(t,{Z:function(){return o}});var a=n(57437),s=n(57860),r=n(26110),l=n(17814),i=n(94508);function o(e){let{children:t,className:n}=e;return(0,s.a)("(min-width:1024px)")?(0,a.jsx)(r.cN,{className:(0,i.cn)("px-0",n),children:t}):(0,a.jsx)(l.ze,{className:(0,i.cn)("px-0",n),children:t})}},93166:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var a=n(57437),s=n(57860),r=n(26110),l=n(17814);function i(e){let{children:t,className:n}=e;return(0,s.a)("(min-width:1024px)")?(0,a.jsx)(r.fK,{className:n,children:t}):(0,a.jsx)(l.OX,{className:n,children:t})}},84002:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});var a=n(57437),s=n(57860),r=n(17814),l=n(26110);function i(e){let{children:t,className:n}=e;return(0,s.a)("(min-width:1024px)")?(0,a.jsx)(l.$N,{className:n,children:t}):(0,a.jsx)(r.iI,{className:n,children:t})}},79318:function(e,t,n){"use strict";n.d(t,{Z:function(){return o}});var a=n(57437),s=n(26110),r=n(57860),l=n(94508);n(2265);var i=n(17814);function o(e){let{children:t,openTrigger:n,open:o,setOpen:d,dialogClassName:c,drawerClassName:u,dialogOverlayClassName:f}=e;return(0,r.a)("(min-width:1024px)")?(0,a.jsxs)(s.Vq,{open:o,onOpenChange:d,children:[(0,a.jsx)(s.hg,{asChild:!0,children:n}),(0,a.jsxs)(s.PK,{children:[(0,a.jsx)(s.t9,{className:f}),(0,a.jsx)(s.cZ,{className:(0,l.cn)("max-sm:w-screen md:w-[450px]  md:min-w-[450px] max-sm:h-screen flex flex-col justify-start",c),children:t})]})]}):(0,a.jsxs)(i.dy,{open:o,onOpenChange:d,children:[(0,a.jsx)(i.Qz,{asChild:!0,children:n}),(0,a.jsx)(i.sc,{children:(0,a.jsx)("div",{className:(0,l.cn)("p-4 overflow-auto",u),children:t})})]})}},75422:function(e,t,n){"use strict";n.d(t,{Z:function(){return l}});var a=n(57437),s=n(15681),r=n(94508);function l(e){let{children:t,description:n,label:l,containerClassName:i,labelClassName:o,variant:d="default"}=e;return(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsxs)(s.xJ,{className:(0,r.cn)("w-full relative","float"==d?"border rounded-xl focus-within:border-neutral-light px-4 py-1 !space-y-0 focus-visible:outline-none focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2 disabled:cursor-not-allowed":"",i),onClick:e=>e.stopPropagation(),children:[l&&(0,a.jsx)(s.lX,{className:o,children:l}),(0,a.jsx)(s.NI,{className:"group relative w-full",children:t}),n&&(0,a.jsx)(s.pf,{children:n}),"default"==d&&(0,a.jsx)(s.zG,{})]}),"float"==d&&(0,a.jsx)(s.zG,{})]})}},61729:function(e,t,n){"use strict";n.d(t,{Z:function(){return o}});var a=n(57437),s=n(15681),r=n(95186),l=n(75422),i=n(94508);function o(e){let{form:t,label:n,name:o,placeholder:d,description:c,type:u,inputProps:f,children:m,labelClassName:h,containerClassName:x,inputContainer:p,variant:N="default"}=e;return(0,a.jsx)(s.Wi,{control:t.control,name:o,render:e=>{let{field:t}=e;return(0,a.jsx)(l.Z,{label:n,description:c,labelClassName:(0,i.cn)("float"==N?"absolute -top-2 left-2 px-1 text-xs bg-background z-10":"",h),containerClassName:x,variant:N,children:(0,a.jsxs)("div",{className:(0,i.cn)("flex gap-2 w-full overflow-hidden","float"==N?"":"border rounded-sm focus-within:border-neutral-light",p),children:[(0,a.jsx)(r.I,{type:u,placeholder:d,...t,...f,className:(0,i.cn)("border-none focus:outline-none shadow-none focus-visible:ring-0 w-full","float"==N?"px-0":"",null==f?void 0:f.className)}),m]})})}})}},16831:function(e,t,n){"use strict";n.d(t,{F$:function(){return o},Q5:function(){return d},qE:function(){return i}});var a=n(57437),s=n(2265),r=n(70650),l=n(94508);let i=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,a.jsx)(r.fC,{ref:t,className:(0,l.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",n),...s})});i.displayName=r.fC.displayName;let o=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,a.jsx)(r.Ee,{ref:t,className:(0,l.cn)("aspect-square h-full w-full",n),...s})});o.displayName=r.Ee.displayName;let d=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,a.jsx)(r.NY,{ref:t,className:(0,l.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",n),...s})});d.displayName=r.NY.displayName},26110:function(e,t,n){"use strict";n.d(t,{$N:function(){return x},Be:function(){return p},PK:function(){return c},Vq:function(){return o},cN:function(){return h},cZ:function(){return f},fK:function(){return m},hg:function(){return d},t9:function(){return u}});var a=n(57437),s=n(2265),r=n(92360),l=n(20653),i=n(94508);let o=r.fC,d=r.xz,c=r.h_;r.x8;let u=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,a.jsx)(r.aV,{ref:t,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",n),...s})});u.displayName=r.aV.displayName;let f=s.forwardRef((e,t)=>{let{className:n,children:s,...o}=e;return(0,a.jsxs)(c,{children:[(0,a.jsx)(u,{}),(0,a.jsxs)(r.VY,{ref:t,className:(0,i.cn)("fixed left-[50%] top-[50%] w-full z-50 grid translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",n),...o,children:[s,(0,a.jsxs)(r.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(l.Pxu,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});f.displayName=r.VY.displayName;let m=e=>{let{className:t,...n}=e;return(0,a.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-start sm:text-left",t),...n})};m.displayName="DialogHeader";let h=e=>{let{className:t,...n}=e;return(0,a.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...n})};h.displayName="DialogFooter";let x=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,a.jsx)(r.Dx,{ref:t,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",n),...s})});x.displayName=r.Dx.displayName;let p=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,a.jsx)(r.dk,{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",n),...s})});p.displayName=r.dk.displayName},17814:function(e,t,n){"use strict";n.d(t,{OX:function(){return f},Qz:function(){return o},dy:function(){return i},iI:function(){return h},sc:function(){return u},u6:function(){return x},ze:function(){return m}});var a=n(57437),s=n(2265),r=n(4216),l=n(94508);let i=e=>{let{shouldScaleBackground:t=!0,...n}=e;return(0,a.jsx)(r.d.Root,{shouldScaleBackground:t,...n})};i.displayName="Drawer";let o=r.d.Trigger,d=r.d.Portal;r.d.Close;let c=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,a.jsx)(r.d.Overlay,{ref:t,className:(0,l.cn)("fixed inset-0 z-50 bg-black/80",n),...s})});c.displayName=r.d.Overlay.displayName;let u=s.forwardRef((e,t)=>{let{className:n,children:s,...i}=e;return(0,a.jsxs)(d,{children:[(0,a.jsx)(c,{}),(0,a.jsxs)(r.d.Content,{ref:t,className:(0,l.cn)("fixed inset-x-0 bottom-0 z-50 mt-24 flex h-auto max-h-[97%] flex-col rounded-t-[10px] border bg-background",n),...i,children:[(0,a.jsx)("div",{className:"mx-auto h-2 w-[100px] rounded-full bg-muted"}),s]})]})});u.displayName="DrawerContent";let f=e=>{let{className:t,...n}=e;return(0,a.jsx)("div",{className:(0,l.cn)("grid gap-1.5 p-4 text-center sm:text-left",t),...n})};f.displayName="DrawerHeader";let m=e=>{let{className:t,...n}=e;return(0,a.jsx)("div",{className:(0,l.cn)("mt-auto flex flex-col gap-2 p-4",t),...n})};m.displayName="DrawerFooter";let h=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,a.jsx)(r.d.Title,{ref:t,className:(0,l.cn)("font-semibold leading-none tracking-tight",n),...s})});h.displayName=r.d.Title.displayName;let x=s.forwardRef((e,t)=>{let{className:n,...s}=e;return(0,a.jsx)(r.d.Description,{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",n),...s})});x.displayName=r.d.Description.displayName},57860:function(e,t,n){"use strict";n.d(t,{a:function(){return s}});var a=n(2265);function s(e){let[t,n]=(0,a.useState)(!1);return(0,a.useEffect)(()=>{function t(e){n(e.matches)}let a=matchMedia(e);return a.addEventListener("change",t),n(a.matches),()=>a.removeEventListener("change",t)},[e]),t}}},function(e){e.O(0,[6990,8310,8760,6290,8094,2586,2957,4956,3448,8658,6088,9623,3398,6205,4216,3682,1456,5087,2545,7668,2971,2117,1744],function(){return e(e.s=61666)}),_N_E=e.O()}]);