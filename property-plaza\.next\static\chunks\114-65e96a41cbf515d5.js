"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[114],{10575:function(e,t,r){r.d(t,{default:function(){return s}});var n=r(49988),o=r(2265),i=r(69362);function s(e){let{locale:t,...r}=e;if(!t)throw Error("Failed to determine locale in `NextIntlClientProvider`, please provide the `locale` prop explicitly.\n\nSee https://next-intl-docs.vercel.app/docs/configuration#locale");return o.createElement(i.IntlProvider,(0,n.g)({locale:t},r))}},30166:function(e,t,r){r.d(t,{default:function(){return o.a}});var n=r(55775),o=r.n(n)},55775:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}});let n=r(47043);r(57437),r(2265);let o=n._(r(15602));function i(e,t){var r;let n={loading:e=>{let{error:t,isLoading:r,pastDelay:n}=e;return null}};"function"==typeof e&&(n.loader=e);let i={...n,...t};return(0,o.default)({...i,modules:null==(r=i.loadableGenerated)?void 0:r.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},81523:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return o}});let n=r(18993);function o(e){let{reason:t,children:r}=e;if("undefined"==typeof window)throw new n.BailoutToCSRError(t);return r}},15602:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return c}});let n=r(57437),o=r(2265),i=r(81523),s=r(70049);function a(e){return{default:e&&"default"in e?e.default:e}}let u={loader:()=>Promise.resolve(a(()=>null)),loading:null,ssr:!0},c=function(e){let t={...u,...e},r=(0,o.lazy)(()=>t.loader().then(a)),c=t.loading;function l(e){let a=c?(0,n.jsx)(c,{isLoading:!0,pastDelay:!0,error:null}):null,u=t.ssr?(0,n.jsxs)(n.Fragment,{children:["undefined"==typeof window?(0,n.jsx)(s.PreloadCss,{moduleIds:t.modules}):null,(0,n.jsx)(r,{...e})]}):(0,n.jsx)(i.BailoutToCSR,{reason:"next/dynamic",children:(0,n.jsx)(r,{...e})});return(0,n.jsx)(o.Suspense,{fallback:a,children:u})}return l.displayName="LoadableComponent",l}},70049:function(e,t,r){Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadCss",{enumerable:!0,get:function(){return i}});let n=r(57437),o=r(20544);function i(e){let{moduleIds:t}=e;if("undefined"!=typeof window)return null;let r=(0,o.getExpectedRequestStore)("next/dynamic css"),i=[];if(r.reactLoadableManifest&&t){let e=r.reactLoadableManifest;for(let r of t){if(!e[r])continue;let t=e[r].files.filter(e=>e.endsWith(".css"));i.push(...t)}}return 0===i.length?null:(0,n.jsx)(n.Fragment,{children:i.map(e=>(0,n.jsx)("link",{precedence:"dynamic",rel:"stylesheet",href:r.assetPrefix+"/_next/"+encodeURI(e),as:"style"},e))})}},78743:function(e,t,r){r.d(t,{y:function(){return l}});var n=r(19142),o=r(37219),i=r(27723),s=r(95767),a=r(80124),u=r(2070),c=r(99287),l=function(){function e(e){e&&(this._subscribe=e)}return e.prototype.lift=function(t){var r=new e;return r.source=this,r.operator=t,r},e.prototype.subscribe=function(e,t,r){var i,s=this,a=(i=e)&&i instanceof n.Lv||i&&(0,u.m)(i.next)&&(0,u.m)(i.error)&&(0,u.m)(i.complete)&&(0,o.Nn)(i)?e:new n.Hp(e,t,r);return(0,c.x)(function(){var e=s.operator,t=s.source;a.add(e?e.call(a,t):t?s._subscribe(a):s._trySubscribe(a))}),a},e.prototype._trySubscribe=function(e){try{return this._subscribe(e)}catch(t){e.error(t)}},e.prototype.forEach=function(e,t){var r=this;return new(t=f(t))(function(t,o){var i=new n.Hp({next:function(t){try{e(t)}catch(e){o(e),i.unsubscribe()}},error:o,complete:t});r.subscribe(i)})},e.prototype._subscribe=function(e){var t;return null===(t=this.source)||void 0===t?void 0:t.subscribe(e)},e.prototype[i.L]=function(){return this},e.prototype.pipe=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return(0,s.U)(e)(this)},e.prototype.toPromise=function(e){var t=this;return new(e=f(e))(function(e,r){var n;t.subscribe(function(e){return n=e},function(e){return r(e)},function(){return e(n)})})},e.create=function(t){return new e(t)},e}();function f(e){var t;return null!==(t=null!=e?e:a.v.Promise)&&void 0!==t?t:Promise}},19142:function(e,t,r){r.d(t,{Hp:function(){return g},Lv:function(){return h}});var n=r(5853),o=r(2070),i=r(37219),s=r(80124),a=r(67444),u=r(8365),c=l("C",void 0,void 0);function l(e,t,r){return{kind:e,value:t,error:r}}var f=r(94814),d=r(99287),h=function(e){function t(t){var r=e.call(this)||this;return r.isStopped=!1,t?(r.destination=t,(0,i.Nn)(t)&&t.add(r)):r.destination=w,r}return(0,n.ZT)(t,e),t.create=function(e,t,r){return new g(e,t,r)},t.prototype.next=function(e){this.isStopped?v(l("N",e,void 0),this):this._next(e)},t.prototype.error=function(e){this.isStopped?v(l("E",void 0,e),this):(this.isStopped=!0,this._error(e))},t.prototype.complete=function(){this.isStopped?v(c,this):(this.isStopped=!0,this._complete())},t.prototype.unsubscribe=function(){this.closed||(this.isStopped=!0,e.prototype.unsubscribe.call(this),this.destination=null)},t.prototype._next=function(e){this.destination.next(e)},t.prototype._error=function(e){try{this.destination.error(e)}finally{this.unsubscribe()}},t.prototype._complete=function(){try{this.destination.complete()}finally{this.unsubscribe()}},t}(i.w0),p=Function.prototype.bind;function y(e,t){return p.call(e,t)}var m=function(){function e(e){this.partialObserver=e}return e.prototype.next=function(e){var t=this.partialObserver;if(t.next)try{t.next(e)}catch(e){b(e)}},e.prototype.error=function(e){var t=this.partialObserver;if(t.error)try{t.error(e)}catch(e){b(e)}else b(e)},e.prototype.complete=function(){var e=this.partialObserver;if(e.complete)try{e.complete()}catch(e){b(e)}},e}(),g=function(e){function t(t,r,n){var i,a,u=e.call(this)||this;return(0,o.m)(t)||!t?i={next:null!=t?t:void 0,error:null!=r?r:void 0,complete:null!=n?n:void 0}:u&&s.v.useDeprecatedNextContext?((a=Object.create(t)).unsubscribe=function(){return u.unsubscribe()},i={next:t.next&&y(t.next,a),error:t.error&&y(t.error,a),complete:t.complete&&y(t.complete,a)}):i=t,u.destination=new m(i),u}return(0,n.ZT)(t,e),t}(h);function b(e){s.v.useDeprecatedSynchronousErrorHandling?(0,d.O)(e):(0,a.h)(e)}function v(e,t){var r=s.v.onStoppedNotification;r&&f.z.setTimeout(function(){return r(e,t)})}var w={closed:!0,next:u.Z,error:function(e){throw e},complete:u.Z}},37219:function(e,t,r){r.d(t,{Lc:function(){return u},w0:function(){return a},Nn:function(){return c}});var n=r(5853),o=r(2070),i=(0,r(37169).d)(function(e){return function(t){e(this),this.message=t?t.length+" errors occurred during unsubscription:\n"+t.map(function(e,t){return t+1+") "+e.toString()}).join("\n  "):"",this.name="UnsubscriptionError",this.errors=t}}),s=r(30325),a=function(){var e;function t(e){this.initialTeardown=e,this.closed=!1,this._parentage=null,this._finalizers=null}return t.prototype.unsubscribe=function(){if(!this.closed){this.closed=!0;var e,t,r,s,a,u=this._parentage;if(u){if(this._parentage=null,Array.isArray(u))try{for(var c=(0,n.XA)(u),f=c.next();!f.done;f=c.next())f.value.remove(this)}catch(t){e={error:t}}finally{try{f&&!f.done&&(t=c.return)&&t.call(c)}finally{if(e)throw e.error}}else u.remove(this)}var d=this.initialTeardown;if((0,o.m)(d))try{d()}catch(e){a=e instanceof i?e.errors:[e]}var h=this._finalizers;if(h){this._finalizers=null;try{for(var p=(0,n.XA)(h),y=p.next();!y.done;y=p.next()){var m=y.value;try{l(m)}catch(e){a=null!=a?a:[],e instanceof i?a=(0,n.ev)((0,n.ev)([],(0,n.CR)(a)),(0,n.CR)(e.errors)):a.push(e)}}}catch(e){r={error:e}}finally{try{y&&!y.done&&(s=p.return)&&s.call(p)}finally{if(r)throw r.error}}}if(a)throw new i(a)}},t.prototype.add=function(e){var r;if(e&&e!==this){if(this.closed)l(e);else{if(e instanceof t){if(e.closed||e._hasParent(this))return;e._addParent(this)}(this._finalizers=null!==(r=this._finalizers)&&void 0!==r?r:[]).push(e)}}},t.prototype._hasParent=function(e){var t=this._parentage;return t===e||Array.isArray(t)&&t.includes(e)},t.prototype._addParent=function(e){var t=this._parentage;this._parentage=Array.isArray(t)?(t.push(e),t):t?[t,e]:e},t.prototype._removeParent=function(e){var t=this._parentage;t===e?this._parentage=null:Array.isArray(t)&&(0,s.P)(t,e)},t.prototype.remove=function(e){var r=this._finalizers;r&&(0,s.P)(r,e),e instanceof t&&e._removeParent(this)},t.EMPTY=((e=new t).closed=!0,e),t}(),u=a.EMPTY;function c(e){return e instanceof a||e&&"closed"in e&&(0,o.m)(e.remove)&&(0,o.m)(e.add)&&(0,o.m)(e.unsubscribe)}function l(e){(0,o.m)(e)?e():e.unsubscribe()}},80124:function(e,t,r){r.d(t,{v:function(){return n}});var n={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1}},48522:function(e,t,r){r.d(t,{D:function(){return v}});var n=r(96750),o=r(96987),i=r(17753);function s(e,t){return void 0===t&&(t=0),(0,i.e)(function(r,n){n.add(e.schedule(function(){return r.subscribe(n)},t))})}var a=r(78743),u=r(53073),c=r(2070),l=r(42903);function f(e,t){if(!e)throw Error("Iterable cannot be null");return new a.y(function(r){(0,l.f)(r,t,function(){var n=e[Symbol.asyncIterator]();(0,l.f)(r,t,function(){n.next().then(function(e){e.done?r.complete():r.next(e.value)})},0,!0)})})}var d=r(87229),h=r(84223),p=r(76613),y=r(96090),m=r(67555),g=r(46345),b=r(32450);function v(e,t){return t?function(e,t){if(null!=e){if((0,d.c)(e))return(0,n.Xf)(e).pipe(s(t),(0,o.Q)(t));if((0,p.z)(e))return new a.y(function(r){var n=0;return t.schedule(function(){n===e.length?r.complete():(r.next(e[n++]),r.closed||this.schedule())})});if((0,h.t)(e))return(0,n.Xf)(e).pipe(s(t),(0,o.Q)(t));if((0,m.D)(e))return f(e,t);if((0,y.T)(e))return new a.y(function(r){var n;return(0,l.f)(r,t,function(){n=e[u.h](),(0,l.f)(r,t,function(){var e,t,o;try{t=(e=n.next()).value,o=e.done}catch(e){r.error(e);return}o?r.complete():r.next(t)},0,!0)}),function(){return(0,c.m)(null==n?void 0:n.return)&&n.return()}});if((0,b.L)(e))return f((0,b.Q)(e),t)}throw(0,g.z)(e)}(e,t):(0,n.Xf)(e)}},96750:function(e,t,r){r.d(t,{Xf:function(){return y}});var n=r(5853),o=r(76613),i=r(84223),s=r(78743),a=r(87229),u=r(67555),c=r(46345),l=r(96090),f=r(32450),d=r(2070),h=r(67444),p=r(27723);function y(e){if(e instanceof s.y)return e;if(null!=e){if((0,a.c)(e))return new s.y(function(t){var r=e[p.L]();if((0,d.m)(r.subscribe))return r.subscribe(t);throw TypeError("Provided object does not correctly implement Symbol.observable")});if((0,o.z)(e))return new s.y(function(t){for(var r=0;r<e.length&&!t.closed;r++)t.next(e[r]);t.complete()});if((0,i.t)(e))return new s.y(function(t){e.then(function(e){t.closed||(t.next(e),t.complete())},function(e){return t.error(e)}).then(null,h.h)});if((0,u.D)(e))return m(e);if((0,l.T)(e))return new s.y(function(t){var r,o;try{for(var i=(0,n.XA)(e),s=i.next();!s.done;s=i.next()){var a=s.value;if(t.next(a),t.closed)return}}catch(e){r={error:e}}finally{try{s&&!s.done&&(o=i.return)&&o.call(i)}finally{if(r)throw r.error}}t.complete()});if((0,f.L)(e))return m((0,f.Q)(e))}throw(0,c.z)(e)}function m(e){return new s.y(function(t){(function(e,t){var r,o,i,s;return(0,n.mG)(this,void 0,void 0,function(){var a;return(0,n.Jh)(this,function(u){switch(u.label){case 0:u.trys.push([0,5,6,11]),r=(0,n.KL)(e),u.label=1;case 1:return[4,r.next()];case 2:if((o=u.sent()).done)return[3,4];if(a=o.value,t.next(a),t.closed)return[2];u.label=3;case 3:return[3,1];case 4:return[3,11];case 5:return i={error:u.sent()},[3,11];case 6:if(u.trys.push([6,,9,10]),!(o&&!o.done&&(s=r.return)))return[3,8];return[4,s.call(r)];case 7:u.sent(),u.label=8;case 8:return[3,10];case 9:if(i)throw i.error;return[7];case 10:return[7];case 11:return t.complete(),[2]}})})})(e,t).catch(function(e){return t.error(e)})})}},4382:function(e,t,r){r.d(t,{x:function(){return o}});var n=r(5853);function o(e,t,r,n,o){return new i(e,t,r,n,o)}var i=function(e){function t(t,r,n,o,i,s){var a=e.call(this,t)||this;return a.onFinalize=i,a.shouldUnsubscribe=s,a._next=r?function(e){try{r(e)}catch(e){t.error(e)}}:e.prototype._next,a._error=o?function(e){try{o(e)}catch(e){t.error(e)}finally{this.unsubscribe()}}:e.prototype._error,a._complete=n?function(){try{n()}catch(e){t.error(e)}finally{this.unsubscribe()}}:e.prototype._complete,a}return(0,n.ZT)(t,e),t.prototype.unsubscribe=function(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){var r=this.closed;e.prototype.unsubscribe.call(this),r||null===(t=this.onFinalize)||void 0===t||t.call(this)}},t}(r(19142).Lv)},44465:function(e,t,r){r.d(t,{h:function(){return i}});var n=r(17753),o=r(4382);function i(e,t){return(0,n.e)(function(r,n){var i=0;r.subscribe((0,o.x)(n,function(r){return e.call(t,r,i++)&&n.next(r)}))})}},7449:function(e,t,r){r.d(t,{U:function(){return i}});var n=r(17753),o=r(4382);function i(e,t){return(0,n.e)(function(r,n){var i=0;r.subscribe((0,o.x)(n,function(r){n.next(e.call(t,r,i++))}))})}},96987:function(e,t,r){r.d(t,{Q:function(){return s}});var n=r(42903),o=r(17753),i=r(4382);function s(e,t){return void 0===t&&(t=0),(0,o.e)(function(r,o){r.subscribe((0,i.x)(o,function(r){return(0,n.f)(o,e,function(){return o.next(r)},t)},function(){return(0,n.f)(o,e,function(){return o.complete()},t)},function(r){return(0,n.f)(o,e,function(){return o.error(r)},t)}))})}},94814:function(e,t,r){r.d(t,{z:function(){return o}});var n=r(5853),o={setTimeout:function(e,t){for(var r=[],i=2;i<arguments.length;i++)r[i-2]=arguments[i];var s=o.delegate;return(null==s?void 0:s.setTimeout)?s.setTimeout.apply(s,(0,n.ev)([e,t],(0,n.CR)(r))):setTimeout.apply(void 0,(0,n.ev)([e,t],(0,n.CR)(r)))},clearTimeout:function(e){var t=o.delegate;return((null==t?void 0:t.clearTimeout)||clearTimeout)(e)},delegate:void 0}},53073:function(e,t,r){r.d(t,{h:function(){return n}});var n="function"==typeof Symbol&&Symbol.iterator?Symbol.iterator:"@@iterator"},27723:function(e,t,r){r.d(t,{L:function(){return n}});var n="function"==typeof Symbol&&Symbol.observable||"@@observable"},94146:function(e,t,r){r.d(t,{_6:function(){return a},jO:function(){return i},yG:function(){return s}});var n=r(2070);function o(e){return e[e.length-1]}function i(e){return(0,n.m)(o(e))?e.pop():void 0}function s(e){var t;return(t=o(e))&&(0,n.m)(t.schedule)?e.pop():void 0}function a(e,t){return"number"==typeof o(e)?e.pop():t}},30325:function(e,t,r){r.d(t,{P:function(){return n}});function n(e,t){if(e){var r=e.indexOf(t);0<=r&&e.splice(r,1)}}},37169:function(e,t,r){r.d(t,{d:function(){return n}});function n(e){var t=e(function(e){Error.call(e),e.stack=Error().stack});return t.prototype=Object.create(Error.prototype),t.prototype.constructor=t,t}},99287:function(e,t,r){r.d(t,{O:function(){return s},x:function(){return i}});var n=r(80124),o=null;function i(e){if(n.v.useDeprecatedSynchronousErrorHandling){var t=!o;if(t&&(o={errorThrown:!1,error:null}),e(),t){var r=o,i=r.errorThrown,s=r.error;if(o=null,i)throw s}}else e()}function s(e){n.v.useDeprecatedSynchronousErrorHandling&&o&&(o.errorThrown=!0,o.error=e)}},42903:function(e,t,r){r.d(t,{f:function(){return n}});function n(e,t,r,n,o){void 0===n&&(n=0),void 0===o&&(o=!1);var i=t.schedule(function(){r(),o?e.add(this.schedule(null,n)):this.unsubscribe()},n);if(e.add(i),!o)return i}},15531:function(e,t,r){r.d(t,{y:function(){return n}});function n(e){return e}},76613:function(e,t,r){r.d(t,{z:function(){return n}});var n=function(e){return e&&"number"==typeof e.length&&"function"!=typeof e}},67555:function(e,t,r){r.d(t,{D:function(){return o}});var n=r(2070);function o(e){return Symbol.asyncIterator&&(0,n.m)(null==e?void 0:e[Symbol.asyncIterator])}},2070:function(e,t,r){r.d(t,{m:function(){return n}});function n(e){return"function"==typeof e}},87229:function(e,t,r){r.d(t,{c:function(){return i}});var n=r(27723),o=r(2070);function i(e){return(0,o.m)(e[n.L])}},96090:function(e,t,r){r.d(t,{T:function(){return i}});var n=r(53073),o=r(2070);function i(e){return(0,o.m)(null==e?void 0:e[n.h])}},84223:function(e,t,r){r.d(t,{t:function(){return o}});var n=r(2070);function o(e){return(0,n.m)(null==e?void 0:e.then)}},32450:function(e,t,r){r.d(t,{L:function(){return s},Q:function(){return i}});var n=r(5853),o=r(2070);function i(e){return(0,n.FC)(this,arguments,function(){var t,r,o;return(0,n.Jh)(this,function(i){switch(i.label){case 0:t=e.getReader(),i.label=1;case 1:i.trys.push([1,,9,10]),i.label=2;case 2:return[4,(0,n.qq)(t.read())];case 3:if(o=(r=i.sent()).value,!r.done)return[3,5];return[4,(0,n.qq)(void 0)];case 4:return[2,i.sent()];case 5:return[4,(0,n.qq)(o)];case 6:return[4,i.sent()];case 7:return i.sent(),[3,2];case 8:return[3,10];case 9:return t.releaseLock(),[7];case 10:return[2]}})})}function s(e){return(0,o.m)(null==e?void 0:e.getReader)}},17753:function(e,t,r){r.d(t,{e:function(){return o}});var n=r(2070);function o(e){return function(t){if((0,n.m)(null==t?void 0:t.lift))return t.lift(function(t){try{return e(t,this)}catch(e){this.error(e)}});throw TypeError("Unable to lift unknown Observable type")}}},59326:function(e,t,r){r.d(t,{Z:function(){return s}});var n=r(5853),o=r(7449),i=Array.isArray;function s(e){return(0,o.U)(function(t){return i(t)?e.apply(void 0,(0,n.ev)([],(0,n.CR)(t))):e(t)})}},8365:function(e,t,r){r.d(t,{Z:function(){return n}});function n(){}},95767:function(e,t,r){r.d(t,{U:function(){return i},z:function(){return o}});var n=r(15531);function o(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return i(e)}function i(e){return 0===e.length?n.y:1===e.length?e[0]:function(t){return e.reduce(function(e,t){return t(e)},t)}}},67444:function(e,t,r){r.d(t,{h:function(){return i}});var n=r(80124),o=r(94814);function i(e){o.z.setTimeout(function(){var t=n.v.onUnhandledError;if(t)t(e);else throw e})}},46345:function(e,t,r){r.d(t,{z:function(){return n}});function n(e){return TypeError("You provided "+(null!==e&&"object"==typeof e?"an invalid object":"'"+e+"'")+" where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.")}},3607:function(e,t,r){r.d(t,{C:function(){return s},N:function(){return c}});var n={0:8203,1:8204,2:8205,3:8290,4:8291,5:8288,6:65279,7:8289,8:119155,9:119156,a:119157,b:119158,c:119159,d:119160,e:119161,f:119162},o={0:8203,1:8204,2:8205,3:65279},i=[,,,,].fill(String.fromCodePoint(o[0])).join("");function s(e,t,r="auto"){let n;return!0===r||"auto"===r&&(!(!Number.isNaN(Number(e))||/[a-z]/i.test(e)&&!/\d+(?:[-:\/]\d+){2}(?:T\d+(?:[-:\/]\d+){1,2}(\.\d+)?Z?)?/.test(e))&&Date.parse(e)||function(e){try{new URL(e,e.startsWith("/")?"https://acme.com":void 0)}catch{return!1}return!0}(e))?e:`${e}${n=JSON.stringify(t),`${i}${Array.from(n).map(e=>{let t=e.charCodeAt(0);if(t>255)throw Error(`Only ASCII edit info can be encoded. Error attempting to encode ${n} on character ${e} (${t})`);return Array.from(t.toString(4).padStart(4,"0")).map(e=>String.fromCodePoint(o[e])).join("")}).join("")}`}`}Object.fromEntries(Object.entries(o).map(e=>e.reverse())),Object.fromEntries(Object.entries(n).map(e=>e.reverse()));var a=`${Object.values(n).map(e=>`\\u{${e.toString(16)}}`).join("")}`,u=RegExp(`[${a}]{4,}`,"gu");function c(e){var t,r;return e&&JSON.parse({cleaned:(t=JSON.stringify(e)).replace(u,""),encoded:(null==(r=t.match(u))?void 0:r[0])||""}.cleaned)}},35992:function(e,t,r){r.d(t,{Bk:function(){return i},Uw:function(){return a},Y5:function(){return u},id:function(){return c},re:function(){return l}});let n=new Set,o="checking";function i(e){if(o!==e)for(let t of(o=e,n))t()}let s=new Set;function a(e){for(let e of s)e()}let u=new Set,c=null;function l(e){for(let t of(c=e,u))t()}},78848:function(e,t,r){r.d(t,{default:function(){return i}});var n=r(57437);let o=(0,r(30166).default)(()=>r.e(2343).then(r.bind(r,52343)),{loadableGenerated:{webpack:()=>[require.resolveWeak("../_chunks-es/SanityLiveStream.js")]},ssr:!1});function i(e){return(0,n.jsx)(o,{...e})}},38652:function(e,t,r){r.d(t,{default:function(){return t_}});var n=r(57437);let o=!(typeof navigator>"u")&&"ReactNative"===navigator.product,i={timeout:o?6e4:12e4},s=function(e){let t={...i,..."string"==typeof e?{url:e}:e};if(t.timeout=function e(t){if(!1===t||0===t)return!1;if(t.connect||t.socket)return t;let r=Number(t);return isNaN(r)?e(i.timeout):{connect:r,socket:r}}(t.timeout),t.query){let{url:e,searchParams:r}=function(e){let t=e.indexOf("?");if(-1===t)return{url:e,searchParams:new URLSearchParams};let r=e.slice(0,t),n=e.slice(t+1);if(!o)return{url:r,searchParams:new URLSearchParams(n)};if("function"!=typeof decodeURIComponent)throw Error("Broken `URLSearchParams` implementation, and `decodeURIComponent` is not defined");let i=new URLSearchParams;for(let e of n.split("&")){let[t,r]=e.split("=");t&&i.append(a(t),a(r||""))}return{url:r,searchParams:i}}(t.url);for(let[n,o]of Object.entries(t.query)){if(void 0!==o){if(Array.isArray(o))for(let e of o)r.append(n,e);else r.append(n,o)}let i=r.toString();i&&(t.url=`${e}?${i}`)}}return t.method=t.body&&!t.method?"POST":(t.method||"GET").toUpperCase(),t};function a(e){return decodeURIComponent(e.replace(/\+/g," "))}let u=/^https?:\/\//i,c=function(e){if(!u.test(e.url))throw Error(`"${e.url}" is not a valid URL`)};function l(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}let f=["request","response","progress","error","abort"],d=["processOptions","validateOptions","interceptRequest","finalizeOptions","onRequest","onResponse","onError","onReturn","onHeaders"];var h=function(e){return e.replace(/^\s+|\s+$/g,"")},p=l(function(e){if(!e)return{};for(var t,r={},n=h(e).split("\n"),o=0;o<n.length;o++){var i=n[o],s=i.indexOf(":"),a=h(i.slice(0,s)).toLowerCase(),u=h(i.slice(s+1));typeof r[a]>"u"?r[a]=u:(t=r[a],"[object Array]"===Object.prototype.toString.call(t)?r[a].push(u):r[a]=[r[a],u])}return r});class y{onabort;onerror;onreadystatechange;ontimeout;readyState=0;response;responseText="";responseType="";status;statusText;withCredentials;#e;#t;#r;#n={};#o;#i={};#s;open(e,t,r){this.#e=e,this.#t=t,this.#r="",this.readyState=1,this.onreadystatechange?.(),this.#o=void 0}abort(){this.#o&&this.#o.abort()}getAllResponseHeaders(){return this.#r}setRequestHeader(e,t){this.#n[e]=t}setInit(e,t=!0){this.#i=e,this.#s=t}send(e){let t="arraybuffer"!==this.responseType,r={...this.#i,method:this.#e,headers:this.#n,body:e};"function"==typeof AbortController&&this.#s&&(this.#o=new AbortController,"u">typeof EventTarget&&this.#o.signal instanceof EventTarget&&(r.signal=this.#o.signal)),"u">typeof document&&(r.credentials=this.withCredentials?"include":"omit"),fetch(this.#t,r).then(e=>(e.headers.forEach((e,t)=>{this.#r+=`${t}: ${e}\r
`}),this.status=e.status,this.statusText=e.statusText,this.readyState=3,this.onreadystatechange?.(),t?e.text():e.arrayBuffer())).then(e=>{"string"==typeof e?this.responseText=e:this.response=e,this.readyState=4,this.onreadystatechange?.()}).catch(e=>{"AbortError"!==e.name?this.onerror?.(e):this.onabort?.()})}}let m="function"==typeof XMLHttpRequest?"xhr":"fetch",g="xhr"===m?XMLHttpRequest:y,b=(e,t)=>{let r=e.options,n=e.applyMiddleware("finalizeOptions",r),o={},i=e.applyMiddleware("interceptRequest",void 0,{adapter:m,context:e});if(i){let e=setTimeout(t,0,null,i);return{abort:()=>clearTimeout(e)}}let s=new g;s instanceof y&&"object"==typeof n.fetch&&s.setInit(n.fetch,n.useAbortSignal??!0);let a=n.headers,u=n.timeout,c=!1,l=!1,f=!1;if(s.onerror=e=>{b(s instanceof y?e instanceof Error?e:Error(`Request error while attempting to reach is ${n.url}`,{cause:e}):Error(`Request error while attempting to reach is ${n.url}${e.lengthComputable?`(${e.loaded} of ${e.total} bytes transferred)`:""}`))},s.ontimeout=e=>{b(Error(`Request timeout while attempting to reach ${n.url}${e.lengthComputable?`(${e.loaded} of ${e.total} bytes transferred)`:""}`))},s.onabort=()=>{h(!0),c=!0},s.onreadystatechange=()=>{u&&(h(),o.socket=setTimeout(()=>d("ESOCKETTIMEDOUT"),u.socket)),c||4!==s.readyState||0===s.status||function(){if(!(c||l||f)){if(0===s.status)return void b(Error("Unknown XHR error"));h(),l=!0,t(null,{body:s.response||(""===s.responseType||"text"===s.responseType?s.responseText:""),url:n.url,method:n.method,headers:p(s.getAllResponseHeaders()),statusCode:s.status,statusMessage:s.statusText})}}()},s.open(n.method,n.url,!0),s.withCredentials=!!n.withCredentials,a&&s.setRequestHeader)for(let e in a)a.hasOwnProperty(e)&&s.setRequestHeader(e,a[e]);return n.rawBody&&(s.responseType="arraybuffer"),e.applyMiddleware("onRequest",{options:n,adapter:m,request:s,context:e}),s.send(n.body||null),u&&(o.connect=setTimeout(()=>d("ETIMEDOUT"),u.connect)),{abort:function(){c=!0,s&&s.abort()}};function d(t){f=!0,s.abort();let r=Error("ESOCKETTIMEDOUT"===t?`Socket timed out on request to ${n.url}`:`Connection timed out on request to ${n.url}`);r.code=t,e.channels.error.publish(r)}function h(e){(e||c||s.readyState>=2&&o.connect)&&clearTimeout(o.connect),o.socket&&clearTimeout(o.socket)}function b(e){if(l)return;h(!0),l=!0,s=null;let r=e||Error(`Network error while attempting to reach ${n.url}`);r.isNetworkError=!0,r.request=n,t(r)}},v=(e=[],t=b)=>(function e(t,r){let n=[],o=d.reduce((e,t)=>(e[t]=e[t]||[],e),{processOptions:[s],validateOptions:[c]});function i(e){let t;let n=f.reduce((e,t)=>(e[t]=function(){let e=Object.create(null),t=0;return{publish:function(t){for(let r in e)e[r](t)},subscribe:function(r){let n=t++;return e[n]=r,function(){delete e[n]}}}}(),e),{}),i=function(e,t,...r){let n="onError"===e,i=t;for(let t=0;t<o[e].length&&(i=(0,o[e][t])(i,...r),!n||i);t++);return i},s=i("processOptions",e);i("validateOptions",s);let a={options:s,channels:n,applyMiddleware:i},u=n.request.subscribe(e=>{t=r(e,(t,r)=>((e,t,r)=>{let o=e,s=t;if(!o)try{s=i("onResponse",t,r)}catch(e){s=null,o=e}(o=o&&i("onError",o,r))?n.error.publish(o):s&&n.response.publish(s)})(t,r,e))});n.abort.subscribe(()=>{u(),t&&t.abort()});let c=i("onReturn",n,a);return c===n&&n.request.publish(a),c}return i.use=function(e){if(!e)throw Error("Tried to add middleware that resolved to falsey value");if("function"==typeof e)throw Error("Tried to add middleware that was a function. It probably expects you to pass options to it.");if(e.onReturn&&o.onReturn.length>0)throw Error("Tried to add new middleware with `onReturn` handler, but another handler has already been registered for this event");return d.forEach(t=>{e[t]&&o[t].push(e[t])}),n.push(e),i},i.clone=()=>e(n,r),t.forEach(i.use),i})(e,t);var w,C,E=r(25566),x=r(82957).Buffer,R={exports:{}},j=function(e){function t(e){let n,o,i,s=null;function a(...e){if(!a.enabled)return;let r=Number(new Date),o=r-(n||r);a.diff=o,a.prev=n,a.curr=r,n=r,e[0]=t.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let i=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,(r,n)=>{if("%%"===r)return"%";i++;let o=t.formatters[n];if("function"==typeof o){let t=e[i];r=o.call(a,t),e.splice(i,1),i--}return r}),t.formatArgs.call(a,e),(a.log||t.log).apply(a,e)}return a.namespace=e,a.useColors=t.useColors(),a.color=t.selectColor(e),a.extend=r,a.destroy=t.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==s?s:(o!==t.namespaces&&(o=t.namespaces,i=t.enabled(e)),i),set:e=>{s=e}}),"function"==typeof t.init&&t.init(a),a}function r(e,r){let n=t(this.namespace+(typeof r>"u"?":":r)+e);return n.log=this.log,n}function n(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return t.debug=t,t.default=t,t.coerce=function(e){return e instanceof Error?e.stack||e.message:e},t.disable=function(){let e=[...t.names.map(n),...t.skips.map(n).map(e=>"-"+e)].join(",");return t.enable(""),e},t.enable=function(e){let r;t.save(e),t.namespaces=e,t.names=[],t.skips=[];let n=("string"==typeof e?e:"").split(/[\s,]+/),o=n.length;for(r=0;r<o;r++)n[r]&&("-"===(e=n[r].replace(/\*/g,".*?"))[0]?t.skips.push(RegExp("^"+e.slice(1)+"$")):t.names.push(RegExp("^"+e+"$")))},t.enabled=function(e){let r,n;if("*"===e[e.length-1])return!0;for(r=0,n=t.skips.length;r<n;r++)if(t.skips[r].test(e))return!1;for(r=0,n=t.names.length;r<n;r++)if(t.names[r].test(e))return!0;return!1},t.humanize=function(){if(C)return w;function e(e,t,r,n){return Math.round(e/r)+" "+n+(t>=1.5*r?"s":"")}return C=1,w=function(t,r){r=r||{};var n,o,i=typeof t;if("string"===i&&t.length>0)return function(e){if(!((e=String(e)).length>100)){var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(t){var r=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*r;case"weeks":case"week":case"w":return 6048e5*r;case"days":case"day":case"d":return 864e5*r;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*r;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*r;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*r;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return r;default:return}}}}(t);if("number"===i&&isFinite(t))return r.long?(o=Math.abs(t))>=864e5?e(t,o,864e5,"day"):o>=36e5?e(t,o,36e5,"hour"):o>=6e4?e(t,o,6e4,"minute"):o>=1e3?e(t,o,1e3,"second"):t+" ms":(n=Math.abs(t))>=864e5?Math.round(t/864e5)+"d":n>=36e5?Math.round(t/36e5)+"h":n>=6e4?Math.round(t/6e4)+"m":n>=1e3?Math.round(t/1e3)+"s":t+"ms";throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(t))}}(),t.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach(r=>{t[r]=e[r]}),t.names=[],t.skips=[],t.formatters={},t.selectColor=function(e){let r=0;for(let t=0;t<e.length;t++)r=(r<<5)-r+e.charCodeAt(t)|0;return t.colors[Math.abs(r)%t.colors.length]},t.enable(t.load()),t};!function(e,t){let r;t.formatArgs=function(t){if(t[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+t[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;let r="color: "+this.color;t.splice(1,0,r,"color: inherit");let n=0,o=0;t[0].replace(/%[a-zA-Z%]/g,e=>{"%%"!==e&&(n++,"%c"===e&&(o=n))}),t.splice(o,0,r)},t.save=function(e){try{e?t.storage.setItem("debug",e):t.storage.removeItem("debug")}catch{}},t.load=function(){let e;try{e=t.storage.getItem("debug")}catch{}return!e&&"u">typeof E&&"env"in E&&(e=E.env.DEBUG),e},t.useColors=function(){return!(!("u">typeof window&&window.process)||"renderer"!==window.process.type&&!window.process.__nwjs)||!("u">typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("u">typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"u">typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"u">typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"u">typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},t.storage=function(){try{return localStorage}catch{}}(),t.destroy=(r=!1,()=>{r||(r=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}),t.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],t.log=console.debug||console.log||(()=>{}),e.exports=j(t);let{formatters:n}=e.exports;n.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}}(R,R.exports),l(R.exports),Object.prototype.hasOwnProperty;let O=typeof x>"u"?()=>!1:e=>x.isBuffer(e);function q(e){return"[object Object]"===Object.prototype.toString.call(e)}let T=["boolean","string","number"],S={};"u">typeof globalThis?S=globalThis:"u">typeof window?S=window:"u">typeof global?S=global:"u">typeof self&&(S=self);var $=S;let I=(e={})=>{let t=e.implementation||Promise;if(!t)throw Error("`Promise` is not available in global scope, and no implementation was passed");return{onReturn:(r,n)=>new t((t,o)=>{let i=n.options.cancelToken;i&&i.promise.then(e=>{r.abort.publish(e),o(e)}),r.error.subscribe(o),r.response.subscribe(r=>{t(e.onlyBody?r.body:r)}),setTimeout(()=>{try{r.request.publish(n)}catch(e){o(e)}},0)})}};class A{__CANCEL__=!0;message;constructor(e){this.message=e}toString(){return"Cancel"+(this.message?`: ${this.message}`:"")}}class P{promise;reason;constructor(e){if("function"!=typeof e)throw TypeError("executor must be a function.");let t=null;this.promise=new Promise(e=>{t=e}),e(e=>{this.reason||(this.reason=new A(e),t(this.reason))})}static source=()=>{let e;return{token:new P(t=>{e=t}),cancel:e}}}I.Cancel=A,I.CancelToken=P,I.isCancel=e=>!(!e||!e?.__CANCEL__);var _=(e,t,r)=>("GET"===r.method||"HEAD"===r.method)&&(e.isNetworkError||!1);function k(e){return 100*Math.pow(2,e)+100*Math.random()}let F=(e={})=>(e=>{let t=e.maxRetries||5,r=e.retryDelay||k,n=e.shouldRetry;return{onError:(e,o)=>{var i;let s=o.options,a=s.maxRetries||t,u=s.retryDelay||r,c=s.shouldRetry||n,l=s.attemptNumber||0;if(null!==(i=s.body)&&"object"==typeof i&&"function"==typeof i.pipe||!c(e,l,s)||l>=a)return e;let f=Object.assign({},o,{options:Object.assign({},s,{attemptNumber:l+1})});return setTimeout(()=>o.channels.request.publish(f),u(l)),null}}})({shouldRetry:_,...e});F.shouldRetry=_;var D=r(78743),U=r(48522),L=(0,r(37169).d)(function(e){return function(){e(this),this.name="EmptyError",this.message="no elements in sequence"}});function M(e,t){var r="object"==typeof t;return new Promise(function(n,o){var i,s=!1;e.subscribe({next:function(e){i=e,s=!0},error:o,complete:function(){s?n(i):r?n(t.defaultValue):o(new L)}})})}var N=r(3607),z=r(5853),H=r(15531),B=r(4382),G=r(42903);function J(e,t,r){e?(0,G.f)(r,e,t):t()}var X=r(17753),V=Array.isArray,Q=r(59326),W=r(95767),Y=r(94146);function Z(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=(0,Y.jO)(e);return r?(0,W.z)(Z.apply(void 0,(0,z.ev)([],(0,z.CR)(e))),(0,Q.Z)(r)):(0,X.e)(function(t,r){var n,o,i;(n=(0,z.ev)([t],(0,z.CR)(1===e.length&&V(e[0])?e[0]:e)),void 0===i&&(i=H.y),function(e){J(void 0,function(){for(var t=n.length,r=Array(t),s=t,a=t,u=function(t){J(o,function(){var u=(0,U.D)(n[t],o),c=!1;u.subscribe((0,B.x)(e,function(n){r[t]=n,!c&&(c=!0,a--),a||e.next(i(r.slice()))},function(){--s||e.complete()}))},e)},c=0;c<t;c++)u(c)},e)})(r)})}var K=r(7449),ee=r(44465);class et extends Error{response;statusCode=400;responseBody;details;constructor(e){let t=en(e);super(t.message),Object.assign(this,t)}}class er extends Error{response;statusCode=500;responseBody;details;constructor(e){let t=en(e);super(t.message),Object.assign(this,t)}}function en(e){let t=e.body,r={response:e,statusCode:e.statusCode,responseBody:-1!==(e.headers["content-type"]||"").toLowerCase().indexOf("application/json")?JSON.stringify(t,null,2):t,message:"",details:void 0};if(t.error&&t.message)return r.message=`${t.error} - ${t.message}`,r;if(eo(t)&&eo(t.error)&&"mutationError"===t.error.type&&"string"==typeof t.error.description||eo(t)&&eo(t.error)&&"actionError"===t.error.type&&"string"==typeof t.error.description){let e=t.error.items||[],n=e.slice(0,5).map(e=>e.error?.description).filter(Boolean),o=n.length?`:
- ${n.join(`
- `)}`:"";return e.length>5&&(o+=`
...and ${e.length-5} more`),r.message=`${t.error.description}${o}`,r.details=t.error,r}return t.error&&t.error.description?(r.message=t.error.description,r.details=t.error):r.message=t.error||t.message||function(e){let t=e.statusMessage?` ${e.statusMessage}`:"";return`${e.method}-request to ${e.url} resulted in HTTP ${e.statusCode}${t}`}(e),r}function eo(e){return"object"==typeof e&&null!==e&&!Array.isArray(e)}class ei extends Error{projectId;addOriginUrl;constructor({projectId:e}){super("CorsOriginError"),this.name="CorsOriginError",this.projectId=e;let t=new URL(`https://sanity.io/manage/project/${e}/api`);if("u">typeof location){let{origin:e}=location;t.searchParams.set("cors","add"),t.searchParams.set("origin",e),this.addOriginUrl=t,this.message=`The current origin is not allowed to connect to the Live Content API. Add it here: ${t}`}else this.message=`The current origin is not allowed to connect to the Live Content API. Change your configuration here: ${t}`}}let es={onResponse:e=>{if(e.statusCode>=500)throw new er(e);if(e.statusCode>=400)throw new et(e);return e}},ea={onResponse:e=>{let t=e.headers["x-sanity-warning"];return(Array.isArray(t)?t:[t]).filter(Boolean).forEach(e=>console.warn(e)),e}};function eu(e,t,r){if(0===r.maxRetries)return!1;let n="GET"===r.method||"HEAD"===r.method,o=(r.uri||r.url).startsWith("/data/query"),i=e.response&&(429===e.response.statusCode||502===e.response.statusCode||503===e.response.statusCode);return(!!n||!!o)&&!!i||F.shouldRetry(e,t,r)}function ec(e){return"https://www.sanity.io/help/"+e}let el=["image","file"],ef=["before","after","replace"],ed=e=>{if(!/^(~[a-z0-9]{1}[-\w]{0,63}|[a-z0-9]{1}[-\w]{0,63})$/.test(e))throw Error("Datasets can only contain lowercase characters, numbers, underscores and dashes, and start with tilde, and be maximum 64 characters")},eh=e=>{if(!/^[-a-z0-9]+$/i.test(e))throw Error("`projectId` can only contain only a-z, 0-9 and dashes")},ep=e=>{if(-1===el.indexOf(e))throw Error(`Invalid asset type: ${e}. Must be one of ${el.join(", ")}`)},ey=(e,t)=>{if(null===t||"object"!=typeof t||Array.isArray(t))throw Error(`${e}() takes an object of properties`)},em=(e,t)=>{if("string"!=typeof t||!/^[a-z0-9_][a-z0-9_.-]{0,127}$/i.test(t)||t.includes(".."))throw Error(`${e}(): "${t}" is not a valid document ID`)},eg=(e,t)=>{if(!t._id)throw Error(`${e}() requires that the document contains an ID ("_id" property)`);em(e,t._id)},eb=(e,t,r)=>{let n="insert(at, selector, items)";if(-1===ef.indexOf(e)){let e=ef.map(e=>`"${e}"`).join(", ");throw Error(`${n} takes an "at"-argument which is one of: ${e}`)}if("string"!=typeof t)throw Error(`${n} takes a "selector"-argument which must be a string`);if(!Array.isArray(r))throw Error(`${n} takes an "items"-argument which must be an array`)},ev=e=>{if(!e.dataset)throw Error("`dataset` must be provided to perform queries");return e.dataset||""},ew=e=>{if("string"!=typeof e||!/^[a-z0-9._-]{1,75}$/i.test(e))throw Error("Tag can only contain alphanumeric characters, underscores, dashes and dots, and be between one and 75 characters long.");return e},eC=e=>{var t;let r,n;return t=(...t)=>console.warn(e.join(" "),...t),r=!1,(...e)=>(r||(n=t(...e),r=!0),n)},eE=eC(["Because you set `withCredentials` to true, we will override your `useCdn`","setting to be false since (cookie-based) credentials are never set on the CDN"]),ex=eC(["Since you haven't set a value for `useCdn`, we will deliver content using our","global, edge-cached API-CDN. If you wish to have content delivered faster, set","`useCdn: false` to use the Live API. Note: You may incur higher costs using the live API."]),eR=eC(["The Sanity client is configured with the `perspective` set to `previewDrafts`, which doesn't support the API-CDN.","The Live API will be used instead. Set `useCdn: false` in your configuration to hide this warning."]),ej=eC(["You have configured Sanity client to use a token in the browser. This may cause unintentional security issues.",`See ${ec("js-client-browser-token")} for more information and how to hide this warning.`]),eO=eC(["Using the Sanity client without specifying an API version is deprecated.",`See ${ec("js-client-api-version")}`]),eq=(eC(["The default export of @sanity/client has been deprecated. Use the named export `createClient` instead."]),{apiHost:"https://api.sanity.io",apiVersion:"1",useProjectHostname:!0,stega:{enabled:!1}}),eT=["localhost","127.0.0.1","0.0.0.0"],eS=e=>-1!==eT.indexOf(e);function e$(e){if(Array.isArray(e)){for(let t of e)if("published"!==t&&"drafts"!==t&&!("string"==typeof t&&t.startsWith("r")&&"raw"!==t))throw TypeError("Invalid API perspective value, expected `published`, `drafts` or a valid release identifier string");return}switch(e){case"previewDrafts":case"drafts":case"published":case"raw":return;default:throw TypeError("Invalid API perspective string, expected `published`, `previewDrafts` or `raw`")}}let eI=(e,t)=>{let r={...t,...e,stega:{..."boolean"==typeof t.stega?{enabled:t.stega}:t.stega||eq.stega,..."boolean"==typeof e.stega?{enabled:e.stega}:e.stega||{}}};r.apiVersion||eO();let n={...eq,...r},o=n.useProjectHostname;if(typeof Promise>"u"){let e=ec("js-client-promise-polyfill");throw Error(`No native Promise-implementation found, polyfill needed - see ${e}`)}if(o&&!n.projectId)throw Error("Configuration must contain `projectId`");if("u">typeof n.perspective&&e$(n.perspective),"encodeSourceMap"in n)throw Error("It looks like you're using options meant for '@sanity/preview-kit/client'. 'encodeSourceMap' is not supported in '@sanity/client'. Did you mean 'stega.enabled'?");if("encodeSourceMapAtPath"in n)throw Error("It looks like you're using options meant for '@sanity/preview-kit/client'. 'encodeSourceMapAtPath' is not supported in '@sanity/client'. Did you mean 'stega.filter'?");if("boolean"!=typeof n.stega.enabled)throw Error(`stega.enabled must be a boolean, received ${n.stega.enabled}`);if(n.stega.enabled&&void 0===n.stega.studioUrl)throw Error("stega.studioUrl must be defined when stega.enabled is true");if(n.stega.enabled&&"string"!=typeof n.stega.studioUrl&&"function"!=typeof n.stega.studioUrl)throw Error(`stega.studioUrl must be a string or a function, received ${n.stega.studioUrl}`);let i="u">typeof window&&window.location&&window.location.hostname,s=i&&eS(window.location.hostname);i&&s&&n.token&&!0!==n.ignoreBrowserTokenWarning?ej():typeof n.useCdn>"u"&&ex(),o&&eh(n.projectId),n.dataset&&ed(n.dataset),"requestTagPrefix"in n&&(n.requestTagPrefix=n.requestTagPrefix?ew(n.requestTagPrefix).replace(/\.+$/,""):void 0),n.apiVersion=`${n.apiVersion}`.replace(/^v/,""),n.isDefaultApi=n.apiHost===eq.apiHost,!0===n.useCdn&&n.withCredentials&&eE(),n.useCdn=!1!==n.useCdn&&!n.withCredentials,function(e){if("1"===e||"X"===e)return;let t=new Date(e);if(!(/^\d{4}-\d{2}-\d{2}$/.test(e)&&t instanceof Date&&t.getTime()>0))throw Error("Invalid API version string, expected `1` or date in format `YYYY-MM-DD`")}(n.apiVersion);let a=n.apiHost.split("://",2),u=a[0],c=a[1],l=n.isDefaultApi?"apicdn.sanity.io":c;return n.useProjectHostname?(n.url=`${u}://${n.projectId}.${c}/v${n.apiVersion}`,n.cdnUrl=`${u}://${n.projectId}.${l}/v${n.apiVersion}`):(n.url=`${n.apiHost}/v${n.apiVersion}`,n.cdnUrl=n.url),n};function eA(e){if("string"==typeof e)return{id:e};if(Array.isArray(e))return{query:"*[_id in $ids]",params:{ids:e}};if("object"==typeof e&&null!==e&&"query"in e&&"string"==typeof e.query)return"params"in e&&"object"==typeof e.params&&null!==e.params?{query:e.query,params:e.params}:{query:e.query};let t=["* Document ID (<docId>)","* Array of document IDs","* Object containing `query`"].join(`
`);throw Error(`Unknown selection - must be one of:

${t}`)}class eP{selection;operations;constructor(e,t={}){this.selection=e,this.operations=t}set(e){return this._assign("set",e)}setIfMissing(e){return this._assign("setIfMissing",e)}diffMatchPatch(e){return ey("diffMatchPatch",e),this._assign("diffMatchPatch",e)}unset(e){if(!Array.isArray(e))throw Error("unset(attrs) takes an array of attributes to unset, non-array given");return this.operations=Object.assign({},this.operations,{unset:e}),this}inc(e){return this._assign("inc",e)}dec(e){return this._assign("dec",e)}insert(e,t,r){return eb(e,t,r),this._assign("insert",{[e]:t,items:r})}append(e,t){return this.insert("after",`${e}[-1]`,t)}prepend(e,t){return this.insert("before",`${e}[0]`,t)}splice(e,t,r,n){let o=t<0?t-1:t,i=typeof r>"u"||-1===r?-1:Math.max(0,t+r),s=`${e}[${o}:${o<0&&i>=0?"":i}]`;return this.insert("replace",s,n||[])}ifRevisionId(e){return this.operations.ifRevisionID=e,this}serialize(){return{...eA(this.selection),...this.operations}}toJSON(){return this.serialize()}reset(){return this.operations={},this}_assign(e,t,r=!0){return ey(e,t),this.operations=Object.assign({},this.operations,{[e]:Object.assign({},r&&this.operations[e]||{},t)}),this}_set(e,t){return this._assign(e,t,!1)}}class e_ extends eP{#a;constructor(e,t,r){super(e,t),this.#a=r}clone(){return new e_(this.selection,{...this.operations},this.#a)}commit(e){if(!this.#a)throw Error("No `client` passed to patch, either provide one or pass the patch to a clients `mutate()` method");let t=Object.assign({returnFirst:"string"==typeof this.selection,returnDocuments:!0},e);return this.#a.mutate({patch:this.serialize()},t)}}class ek extends eP{#a;constructor(e,t,r){super(e,t),this.#a=r}clone(){return new ek(this.selection,{...this.operations},this.#a)}commit(e){if(!this.#a)throw Error("No `client` passed to patch, either provide one or pass the patch to a clients `mutate()` method");let t=Object.assign({returnFirst:"string"==typeof this.selection,returnDocuments:!0},e);return this.#a.mutate({patch:this.serialize()},t)}}let eF={returnDocuments:!1};class eD{operations;trxId;constructor(e=[],t){this.operations=e,this.trxId=t}create(e){return ey("create",e),this._add({create:e})}createIfNotExists(e){let t="createIfNotExists";return ey(t,e),eg(t,e),this._add({[t]:e})}createOrReplace(e){let t="createOrReplace";return ey(t,e),eg(t,e),this._add({[t]:e})}delete(e){return em("delete",e),this._add({delete:{id:e}})}transactionId(e){return e?(this.trxId=e,this):this.trxId}serialize(){return[...this.operations]}toJSON(){return this.serialize()}reset(){return this.operations=[],this}_add(e){return this.operations.push(e),this}}class eU extends eD{#a;constructor(e,t,r){super(e,r),this.#a=t}clone(){return new eU([...this.operations],this.#a,this.trxId)}commit(e){if(!this.#a)throw Error("No `client` passed to transaction, either provide one or pass the transaction to a clients `mutate()` method");return this.#a.mutate(this.serialize(),Object.assign({transactionId:this.trxId},eF,e||{}))}patch(e,t){let r="function"==typeof t;if("string"!=typeof e&&e instanceof ek)return this._add({patch:e.serialize()});if(r){let r=t(new ek(e,{},this.#a));if(!(r instanceof ek))throw Error("function passed to `patch()` must return the patch");return this._add({patch:r.serialize()})}return this._add({patch:{id:e,...t}})}}class eL extends eD{#a;constructor(e,t,r){super(e,r),this.#a=t}clone(){return new eL([...this.operations],this.#a,this.trxId)}commit(e){if(!this.#a)throw Error("No `client` passed to transaction, either provide one or pass the transaction to a clients `mutate()` method");return this.#a.mutate(this.serialize(),Object.assign({transactionId:this.trxId},eF,e||{}))}patch(e,t){let r="function"==typeof t;if("string"!=typeof e&&e instanceof e_)return this._add({patch:e.serialize()});if(r){let r=t(new e_(e,{},this.#a));if(!(r instanceof e_))throw Error("function passed to `patch()` must return the patch");return this._add({patch:r.serialize()})}return this._add({patch:{id:e,...t}})}}let eM=({query:e,params:t={},options:r={}})=>{let n=new URLSearchParams,{tag:o,includeMutations:i,returnQuery:s,...a}=r;for(let[r,i]of(o&&n.append("tag",o),n.append("query",e),Object.entries(t)))n.append(`$${r}`,JSON.stringify(i));for(let[e,t]of Object.entries(a))t&&n.append(e,`${t}`);return!1===s&&n.append("returnQuery","false"),!1===i&&n.append("includeMutations","false"),`?${n}`},eN=(e,t)=>!1===e?void 0:typeof e>"u"?t:e,ez=(e={})=>({dryRun:e.dryRun,returnIds:!0,returnDocuments:eN(e.returnDocuments,!0),visibility:e.visibility||"sync",autoGenerateArrayKeys:e.autoGenerateArrayKeys,skipCrossDatasetReferenceValidation:e.skipCrossDatasetReferenceValidation}),eH=e=>"response"===e.type,eB=e=>e.body,eG=(e,t)=>e.reduce((e,r)=>(e[t(r)]=r,e),Object.create(null));function eJ(e,t,n,o,i={},s={}){let a="stega"in s?{...n||{},..."boolean"==typeof s.stega?{enabled:s.stega}:s.stega||{}}:n,u=a.enabled?(0,N.N)(i):i,c=!1===s.filterResponse?e=>e:e=>e.result,{cache:l,next:f,...d}={useAbortSignal:"u">typeof s.signal,resultSourceMap:a.enabled?"withKeyArraySelector":s.resultSourceMap,...s,returnQuery:!1===s.filterResponse&&!1!==s.returnQuery},h=e0(e,t,"query",{query:o,params:u},"u">typeof l||"u">typeof f?{...d,fetch:{cache:l,next:f}}:d);return a.enabled?h.pipe(function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return Z.apply(void 0,(0,z.ev)([],(0,z.CR)(e)))}((0,U.D)(r.e(3139).then(r.bind(r,13139)).then(function(e){return e.stegaEncodeSourceMap$1}).then(({stegaEncodeSourceMap:e})=>e))),(0,K.U)(([e,t])=>{let r=t(e.result,e.resultSourceMap,a);return c({...e,result:r})})):h.pipe((0,K.U)(c))}function eX(e,t,r,n={}){let o={uri:e6(e,"doc",r),json:!0,tag:n.tag,signal:n.signal};return e3(e,t,o).pipe((0,ee.h)(eH),(0,K.U)(e=>e.body.documents&&e.body.documents[0]))}function eV(e,t,r,n={}){let o={uri:e6(e,"doc",r.join(",")),json:!0,tag:n.tag,signal:n.signal};return e3(e,t,o).pipe((0,ee.h)(eH),(0,K.U)(e=>{let t=eG(e.body.documents||[],e=>e._id);return r.map(e=>t[e]||null)}))}function eQ(e,t,r,n){return eg("createIfNotExists",r),e1(e,t,r,"createIfNotExists",n)}function eW(e,t,r,n){return eg("createOrReplace",r),e1(e,t,r,"createOrReplace",n)}function eY(e,t,r,n){return e0(e,t,"mutate",{mutations:[{delete:eA(r)}]},n)}function eZ(e,t,r,n){let o;return e0(e,t,"mutate",{mutations:Array.isArray(o=r instanceof ek||r instanceof e_?{patch:r.serialize()}:r instanceof eU||r instanceof eL?r.serialize():r)?o:[o],transactionId:n&&n.transactionId||void 0},n)}function eK(e,t,r,n){let o=Array.isArray(r)?r:[r];return e0(e,t,"actions",{actions:o,transactionId:n&&n.transactionId||void 0,skipCrossDatasetReferenceValidation:n&&n.skipCrossDatasetReferenceValidation||void 0,dryRun:n&&n.dryRun||void 0},n)}function e0(e,t,r,n,o={}){let i="mutate"===r,s="actions"===r,a=i||s?"":eM(n),u=!i&&!s&&a.length<11264,c=o.returnFirst,{timeout:l,token:f,tag:d,headers:h,returnQuery:p,lastLiveEventId:y,cacheMode:m}=o,g=e6(e,r,u?a:"");return e3(e,t,{method:u?"GET":"POST",uri:g,json:!0,body:u?void 0:n,query:i&&ez(o),timeout:l,headers:h,token:f,tag:d,returnQuery:p,perspective:o.perspective,resultSourceMap:o.resultSourceMap,lastLiveEventId:Array.isArray(y)?y[0]:y,cacheMode:m,canUseCdn:"query"===r,signal:o.signal,fetch:o.fetch,useAbortSignal:o.useAbortSignal,useCdn:o.useCdn}).pipe((0,ee.h)(eH),(0,K.U)(eB),(0,K.U)(e=>{if(!i)return e;let t=e.results||[];if(o.returnDocuments)return c?t[0]&&t[0].document:t.map(e=>e.document);let r=c?t[0]&&t[0].id:t.map(e=>e.id);return{transactionId:e.transactionId,results:t,[c?"documentId":"documentIds"]:r}}))}function e1(e,t,r,n,o={}){return e0(e,t,"mutate",{mutations:[{[n]:r}]},Object.assign({returnFirst:!0,returnDocuments:!0},o))}function e3(e,t,r){var n;let o=r.url||r.uri,i=e.config(),s=typeof r.canUseCdn>"u"?["GET","HEAD"].indexOf(r.method||"GET")>=0&&0===o.indexOf("/data/"):r.canUseCdn,a=(r.useCdn??i.useCdn)&&s,u=r.tag&&i.requestTagPrefix?[i.requestTagPrefix,r.tag].join("."):r.tag||i.requestTagPrefix;if(u&&null!==r.tag&&(r.query={tag:ew(u),...r.query}),["GET","HEAD","POST"].indexOf(r.method||"GET")>=0&&0===o.indexOf("/data/query/")){let e=r.resultSourceMap??i.resultSourceMap;void 0!==e&&!1!==e&&(r.query={resultSourceMap:e,...r.query});let t=r.perspective||i.perspective;"u">typeof t&&(e$(t),r.query={perspective:Array.isArray(t)?t.join(","):t,...r.query},"previewDrafts"===t&&a&&(a=!1,eR())),r.lastLiveEventId&&(r.query={...r.query,lastLiveEventId:r.lastLiveEventId}),!1===r.returnQuery&&(r.query={returnQuery:"false",...r.query}),a&&"noStale"==r.cacheMode&&(r.query={cacheMode:"noStale",...r.query})}let c=function(e,t={}){let r={},n=t.token||e.token;n&&(r.Authorization=`Bearer ${n}`),t.useGlobalApi||e.useProjectHostname||!e.projectId||(r["X-Sanity-Project-ID"]=e.projectId);let o=!!(typeof t.withCredentials>"u"?e.token||e.withCredentials:t.withCredentials),i=typeof t.timeout>"u"?e.timeout:t.timeout;return Object.assign({},t,{headers:Object.assign({},r,t.headers||{}),timeout:typeof i>"u"?3e5:i,proxy:t.proxy||e.proxy,json:!0,withCredentials:o,fetch:"object"==typeof t.fetch&&"object"==typeof e.fetch?{...e.fetch,...t.fetch}:t.fetch||e.fetch})}(i,Object.assign({},r,{url:e5(e,o,a)})),l=new D.y(e=>t(c,i.requester).subscribe(e));return r.signal?l.pipe((n=r.signal,e=>new D.y(t=>{let r=()=>t.error(function(e){if(e9)return new DOMException(e?.reason??"The operation was aborted.","AbortError");let t=Error(e?.reason??"The operation was aborted.");return t.name="AbortError",t}(n));if(n&&n.aborted){r();return}let o=e.subscribe(t);return n.addEventListener("abort",r),()=>{n.removeEventListener("abort",r),o.unsubscribe()}}))):l}function e2(e,t,r){return e3(e,t,r).pipe((0,ee.h)(e=>"response"===e.type),(0,K.U)(e=>e.body))}function e6(e,t,r){let n=ev(e.config()),o=`/${t}/${n}`;return`/data${r?`${o}/${r}`:o}`.replace(/\/($|\?)/,"$1")}function e5(e,t,r=!1){let{url:n,cdnUrl:o}=e.config();return`${r?o:n}/${t.replace(/^\//,"")}`}let e9=!!globalThis.DOMException;class e4{#a;#u;constructor(e,t){this.#a=e,this.#u=t}upload(e,t,r){return e8(this.#a,this.#u,e,t,r)}}class e7{#a;#u;constructor(e,t){this.#a=e,this.#u=t}upload(e,t,r){return M(e8(this.#a,this.#u,e,t,r).pipe((0,ee.h)(e=>"response"===e.type),(0,K.U)(e=>e.body.document)))}}function e8(e,t,r,n,o={}){ep(r);let i=o.extract||void 0;i&&!i.length&&(i=["none"]);let s=ev(e.config()),a="image"===r?"images":"files",u=!(typeof File>"u")&&n instanceof File?Object.assign({filename:!1===o.preserveFilename?void 0:n.name,contentType:n.type},o):o,{tag:c,label:l,title:f,description:d,creditLine:h,filename:p,source:y}=u,m={label:l,title:f,description:d,filename:p,meta:i,creditLine:h};return y&&(m.sourceId=y.id,m.sourceName=y.name,m.sourceUrl=y.url),e3(e,t,{tag:c,method:"POST",timeout:u.timeout||0,uri:`/assets/${a}/${s}`,headers:u.contentType?{"Content-Type":u.contentType}:{},query:m,body:n})}var te=(e,t)=>Object.keys(t).concat(Object.keys(e)).reduce((r,n)=>(r[n]=typeof e[n]>"u"?t[n]:e[n],r),{});let tt=(e,t)=>t.reduce((t,r)=>(typeof e[r]>"u"||(t[r]=e[r]),t),{}),tr=["includePreviousRevision","includeResult","includeMutations","visibility","effectFormat","tag"],tn={includeResult:!0};function to(e,t,n={}){let{url:o,token:i,withCredentials:s,requestTagPrefix:a}=this.config(),u=n.tag&&a?[a,n.tag].join("."):n.tag,c={...te(n,tn),tag:u},l=eM({query:e,params:t,options:{tag:u,...tt(c,tr)}}),f=`${o}${e6(this,"listen",l)}`;if(f.length>14800)return new D.y(e=>e.error(Error("Query too large for listener")));let d=c.events?c.events:["mutation"],h=-1!==d.indexOf("reconnect"),p={};return(i||s)&&(p.withCredentials=!0),i&&(p.headers={Authorization:`Bearer ${i}`}),new D.y(e=>{let t,n,o=!1,i=!1;function s(){o||(h&&e.next({type:"reconnect"}),o||t.readyState!==t.CLOSED||(l(),clearTimeout(n),n=setTimeout(m,100)))}function a(t){e.error(function(e){if(e instanceof Error)return e;let t=ti(e);return t instanceof Error?t:Error(t.error?t.error.description?t.error.description:"string"==typeof t.error?t.error:JSON.stringify(t.error,null,2):t.message||"Unknown listener error")}(t))}function u(t){let r=ti(t);return r instanceof Error?e.error(r):e.next(r)}function c(){o=!0,l(),e.complete()}function l(){t&&(t.removeEventListener("error",s),t.removeEventListener("channelError",a),t.removeEventListener("disconnect",c),d.forEach(e=>t.removeEventListener(e,u)),t.close())}async function y(){let{default:e}=await r.e(6247).then(r.t.bind(r,46247,19));if(i)return;let t=new e(f,p);return t.addEventListener("error",s),t.addEventListener("channelError",a),t.addEventListener("disconnect",c),d.forEach(e=>t.addEventListener(e,u)),t}function m(){y().then(e=>{e&&(t=e,i&&l())}).catch(t=>{e.error(t),g()})}function g(){o=!0,l(),i=!0}return m(),g})}function ti(e){try{let t=e.data&&JSON.parse(e.data)||{};return Object.assign({type:e.type},t)}catch(e){return e}}let ts="2021-03-26";class ta{#a;constructor(e){this.#a=e}events({includeDrafts:e=!1,tag:t}={}){let{projectId:n,apiVersion:o,token:i,withCredentials:s,requestTagPrefix:a}=this.#a.config(),u=o.replace(/^v/,"");if("X"!==u&&u<ts)throw Error(`The live events API requires API version ${ts} or later. The current API version is ${u}. Please update your API version to use this feature.`);if(e&&!i&&!s)throw Error("The live events API requires a token or withCredentials when 'includeDrafts: true'. Please update your client configuration. The token should have the lowest possible access role.");if(e&&"X"!==u)throw Error("The live events API requires API version X when 'includeDrafts: true'. This API is experimental and may change or even be removed.");let c=e6(this.#a,"live/events"),l=new URL(this.#a.getUrl(c,!1)),f=t&&a?[a,t].join("."):t;f&&l.searchParams.set("tag",f),e&&l.searchParams.set("includeDrafts","true");let d=["restart","message","welcome","reconnect"],h={};return e&&i&&(h.headers={Authorization:`Bearer ${i}`}),e&&s&&(h.withCredentials=!0),new D.y(e=>{let t,o,i=!1,s=!1;function a(r){if(!i){if("data"in r){let t=tu(r);e.error(Error(t.message,{cause:t}))}t.readyState===t.CLOSED&&(c(),clearTimeout(o),o=setTimeout(p,100))}}function u(t){let r=tu(t);return r instanceof Error?e.error(r):e.next(r)}function c(){if(t){for(let e of(t.removeEventListener("error",a),d))t.removeEventListener(e,u);t.close()}}async function f(){let e=typeof EventSource>"u"||h.headers||h.withCredentials?(await r.e(6247).then(r.t.bind(r,46247,19))).default:EventSource;if(s)return;try{if(await fetch(l,{method:"OPTIONS",mode:"cors",credentials:h.withCredentials?"include":"omit",headers:h.headers}),s)return}catch{throw new ei({projectId:n})}let t=new e(l.toString(),h);for(let e of(t.addEventListener("error",a),d))t.addEventListener(e,u);return t}function p(){f().then(e=>{e&&(t=e,s&&c())}).catch(t=>{e.error(t),y()})}function y(){i=!0,c(),s=!0}return p(),y})}}function tu(e){try{let t=e.data&&JSON.parse(e.data)||{};return{type:e.type,id:e.lastEventId,...t}}catch(e){return e}}class tc{#a;#u;constructor(e,t){this.#a=e,this.#u=t}create(e,t){return tf(this.#a,this.#u,"PUT",e,t)}edit(e,t){return tf(this.#a,this.#u,"PATCH",e,t)}delete(e){return tf(this.#a,this.#u,"DELETE",e)}list(){return e2(this.#a,this.#u,{uri:"/datasets",tag:null})}}class tl{#a;#u;constructor(e,t){this.#a=e,this.#u=t}create(e,t){return M(tf(this.#a,this.#u,"PUT",e,t))}edit(e,t){return M(tf(this.#a,this.#u,"PATCH",e,t))}delete(e){return M(tf(this.#a,this.#u,"DELETE",e))}list(){return M(e2(this.#a,this.#u,{uri:"/datasets",tag:null}))}}function tf(e,t,r,n,o){return ed(n),e2(e,t,{method:r,uri:`/datasets/${n}`,body:o,tag:null})}class td{#a;#u;constructor(e,t){this.#a=e,this.#u=t}list(e){let t=e?.includeMembers===!1?"/projects?includeMembers=false":"/projects";return e2(this.#a,this.#u,{uri:t})}getById(e){return e2(this.#a,this.#u,{uri:`/projects/${e}`})}}class th{#a;#u;constructor(e,t){this.#a=e,this.#u=t}list(e){let t=e?.includeMembers===!1?"/projects?includeMembers=false":"/projects";return M(e2(this.#a,this.#u,{uri:t}))}getById(e){return M(e2(this.#a,this.#u,{uri:`/projects/${e}`}))}}class tp{#a;#u;constructor(e,t){this.#a=e,this.#u=t}getById(e){return e2(this.#a,this.#u,{uri:`/users/${e}`})}}class ty{#a;#u;constructor(e,t){this.#a=e,this.#u=t}getById(e){return M(e2(this.#a,this.#u,{uri:`/users/${e}`}))}}class tm{assets;datasets;live;projects;users;#c;#u;listen=to;constructor(e,t=eq){this.config(t),this.#u=e,this.assets=new e4(this,this.#u),this.datasets=new tc(this,this.#u),this.live=new ta(this),this.projects=new td(this,this.#u),this.users=new tp(this,this.#u)}clone(){return new tm(this.#u,this.config())}config(e){if(void 0===e)return{...this.#c};if(this.#c&&!1===this.#c.allowReconfigure)throw Error("Existing client instance cannot be reconfigured - use `withConfig(newConfig)` to return a new client");return this.#c=eI(e,this.#c||{}),this}withConfig(e){let t=this.config();return new tm(this.#u,{...t,...e,stega:{...t.stega||{},..."boolean"==typeof e?.stega?{enabled:e.stega}:e?.stega||{}}})}fetch(e,t,r){return eJ(this,this.#u,this.#c.stega,e,t,r)}getDocument(e,t){return eX(this,this.#u,e,t)}getDocuments(e,t){return eV(this,this.#u,e,t)}create(e,t){return e1(this,this.#u,e,"create",t)}createIfNotExists(e,t){return eQ(this,this.#u,e,t)}createOrReplace(e,t){return eW(this,this.#u,e,t)}delete(e,t){return eY(this,this.#u,e,t)}mutate(e,t){return eZ(this,this.#u,e,t)}patch(e,t){return new e_(e,t,this)}transaction(e){return new eL(e,this)}action(e,t){return eK(this,this.#u,e,t)}request(e){return e2(this,this.#u,e)}getUrl(e,t){return e5(this,e,t)}getDataUrl(e,t){return e6(this,e,t)}}class tg{assets;datasets;live;projects;users;observable;#c;#u;listen=to;constructor(e,t=eq){this.config(t),this.#u=e,this.assets=new e7(this,this.#u),this.datasets=new tl(this,this.#u),this.live=new ta(this),this.projects=new th(this,this.#u),this.users=new ty(this,this.#u),this.observable=new tm(e,t)}clone(){return new tg(this.#u,this.config())}config(e){if(void 0===e)return{...this.#c};if(this.#c&&!1===this.#c.allowReconfigure)throw Error("Existing client instance cannot be reconfigured - use `withConfig(newConfig)` to return a new client");return this.observable&&this.observable.config(e),this.#c=eI(e,this.#c||{}),this}withConfig(e){let t=this.config();return new tg(this.#u,{...t,...e,stega:{...t.stega||{},..."boolean"==typeof e?.stega?{enabled:e.stega}:e?.stega||{}}})}fetch(e,t,r){return M(eJ(this,this.#u,this.#c.stega,e,t,r))}getDocument(e,t){return M(eX(this,this.#u,e,t))}getDocuments(e,t){return M(eV(this,this.#u,e,t))}create(e,t){return M(e1(this,this.#u,e,"create",t))}createIfNotExists(e,t){return M(eQ(this,this.#u,e,t))}createOrReplace(e,t){return M(eW(this,this.#u,e,t))}delete(e,t){return M(eY(this,this.#u,e,t))}mutate(e,t){return M(eZ(this,this.#u,e,t))}patch(e,t){return new ek(e,t,this)}transaction(e){return new eU(e,this)}action(e,t){return M(eK(this,this.#u,e,t))}request(e){return M(e2(this,this.#u,e))}dataRequest(e,t,r){return M(e0(this,this.#u,e,t,r))}getUrl(e,t){return e5(this,e,t)}getDataUrl(e,t){return e6(this,e,t)}}let tb=function(e,t){let r=v([F({shouldRetry:eu}),...e,ea,{processOptions:e=>{let t=e.body;return!t||"function"==typeof t.pipe||O(t)||-1===T.indexOf(typeof t)&&!Array.isArray(t)&&!function(e){if(!1===q(e))return!1;let t=e.constructor;if(void 0===t)return!0;let r=t.prototype;return!(!1===q(r)||!1===r.hasOwnProperty("isPrototypeOf"))}(t)?e:Object.assign({},e,{body:JSON.stringify(e.body),headers:Object.assign({},e.headers,{"Content-Type":"application/json"})})}},{onResponse:e=>{let t=e.headers["content-type"]||"",r=-1!==t.indexOf("application/json");return e.body&&t&&r?Object.assign({},e,{body:function(e){try{return JSON.parse(e)}catch(e){throw e.message=`Failed to parsed response body as JSON: ${e.message}`,e}}(e.body)}):e},processOptions:e=>Object.assign({},e,{headers:Object.assign({Accept:"application/json"},e.headers)})},{onRequest:e=>{if("xhr"!==e.adapter)return;let t=e.request,r=e.context;function n(e){return t=>{let n=t.lengthComputable?t.loaded/t.total*100:-1;r.channels.progress.publish({stage:e,percent:n,total:t.total,loaded:t.loaded,lengthComputable:t.lengthComputable})}}"upload"in t&&"onprogress"in t.upload&&(t.upload.onprogress=n("upload")),"onprogress"in t&&(t.onprogress=n("download"))}},es,function(e={}){let t=e.implementation||$.Observable;if(!t)throw Error("`Observable` is not available in global scope, and no implementation was passed");return{onReturn:(e,r)=>new t(t=>(e.error.subscribe(e=>t.error(e)),e.progress.subscribe(e=>t.next(Object.assign({type:"progress"},e))),e.response.subscribe(e=>{t.next(Object.assign({type:"response"},e)),t.complete()}),e.request.publish(r),()=>e.abort.publish()))}}({implementation:D.y})]);return{requester:r,createClient:e=>new t((t,n)=>(n||r)({maxRedirects:0,maxRetries:e.maxRetries,retryDelay:e.retryDelay,...t}),e)}}([],tg),tv=(tb.requester,tb.createClient);var tw=r(25096),tC=r(30166),tE=r(99376),tx=r(2265),tR=r(52141),tj=r(35992);let tO=(0,tC.default)(()=>Promise.all([r.e(5069),r.e(1803)]).then(r.bind(r,11803)),{loadableGenerated:{webpack:()=>[require.resolveWeak("../_chunks-es/PresentationComlink.js")]},ssr:!1}),tq=(0,tC.default)(()=>r.e(8122).then(r.bind(r,98122)),{loadableGenerated:{webpack:()=>[require.resolveWeak("../_chunks-es/RefreshOnMount.js")]},ssr:!1}),tT=(0,tC.default)(()=>r.e(1313).then(r.bind(r,21313)),{loadableGenerated:{webpack:()=>[require.resolveWeak("../_chunks-es/RefreshOnFocus.js")]},ssr:!1}),tS=(0,tC.default)(()=>r.e(153).then(r.bind(r,10153)),{loadableGenerated:{webpack:()=>[require.resolveWeak("../_chunks-es/RefreshOnReconnect.js")]},ssr:!1}),t$=()=>window!==window.parent,tI=()=>!!window.opener,tA=()=>t$()||tI(),tP=e=>{var t;e instanceof ei?console.warn("Sanity Live is unable to connect to the Sanity API as the current origin - ".concat(window.origin," - is not in the list of allowed CORS origins for this Sanity Project."),e.addOriginUrl&&"Add it here:",null===(t=e.addOriginUrl)||void 0===t?void 0:t.toString()):console.error(e)};function t_(e){let{projectId:t,dataset:r,apiHost:o,apiVersion:i,useProjectHostname:s,token:a,requestTagPrefix:u,draftModeEnabled:c,draftModePerspective:l,refreshOnMount:f=!1,refreshOnFocus:d=!c&&(typeof window>"u"||window.self===window.top),refreshOnReconnect:h=!0,tag:p,onError:y=tP}=e,m=(0,tx.useMemo)(()=>tv({projectId:t,dataset:r,apiHost:o,apiVersion:i,useProjectHostname:s,ignoreBrowserTokenWarning:!0,token:a,useCdn:!1,requestTagPrefix:u}),[o,i,r,t,u,a,s]),g=(0,tE.useRouter)(),b=(0,tR.i)(e=>{"message"===e.type?(0,tw.n)(e.tags):"restart"===e.type&&g.refresh()});(0,tx.useEffect)(()=>{let e=m.live.events({includeDrafts:!!a,tag:p}).subscribe({next:e=>{("message"===e.type||"restart"===e.type||"welcome"===e.type)&&b(e)},error:e=>{y(e)}});return()=>e.unsubscribe()},[m.live,b,y,p,a]),(0,tx.useEffect)(()=>{c&&l?(0,tj.Bk)(l):(0,tj.Bk)("unknown")},[c,l]);let[v,w]=(0,tx.useState)(!1);(0,tx.useEffect)(()=>{if(!tA()){if(c&&a){(0,tj.Uw)("live");return}if(c){(0,tj.Uw)("static");return}(0,tj.Uw)("unknown")}},[c,a]),(0,tx.useEffect)(()=>{if(!tA())return;let e=new AbortController,t=setTimeout(()=>(0,tj.Uw)("live"),3e3);return window.addEventListener("message",r=>{let{data:n}=r;n&&"object"==typeof n&&"domain"in n&&"sanity/channels"===n.domain&&"from"in n&&"presentation"===n.from&&(clearTimeout(t),(0,tj.Uw)(tI()?"presentation-window":"presentation-iframe"),w(!0),e.abort())},{signal:e.signal}),()=>{clearTimeout(t),e.abort()}},[]);let C=(0,tx.useRef)(void 0);return(0,tx.useEffect)(()=>{if(c)return clearTimeout(C.current),()=>{C.current=setTimeout(()=>{console.warn("Sanity Live: Draft mode was enabled, but is now being disabled")})}},[c]),(0,n.jsxs)(n.Fragment,{children:[c&&v&&(0,n.jsx)(tO,{draftModeEnabled:c,draftModePerspective:l}),!c&&f&&(0,n.jsx)(tq,{}),!c&&d&&(0,n.jsx)(tT,{}),!c&&h&&(0,n.jsx)(tS,{})]})}t_.displayName="SanityLive"},25096:function(e,t,r){r.d(t,{N:function(){return o},n:function(){return i}}),r(83079);var n=r(12119),o=(0,n.$)("6ebeb7e19dda4f3641cdd24f3ee5eadb0810a726"),i=(0,n.$)("21a7b89139c1ae9f09080bde8b72017631c6bb15")},76939:function(e,t,r){r.d(t,{default:function(){return s}});var n=r(57437),o=r(2265);let i=(0,o.lazy)(()=>Promise.all([r.e(32),r.e(5069),r.e(6686)]).then(r.bind(r,56686)));function s(e){return(0,n.jsx)(o.Suspense,{fallback:null,children:(0,n.jsx)(i,{...e})})}},52141:function(e,t,r){r.d(t,{i:function(){return o}});var n=r(2265);function o(e){let t=(0,n.useRef)(null);return(0,n.useInsertionEffect)(()=>{t.current=e},[e]),(0,n.useCallback)((...e)=>(0,t.current)(...e),[])}}}]);