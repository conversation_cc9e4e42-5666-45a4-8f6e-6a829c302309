"use strict";(()=>{var e={};e.id=859,e.ids=[859],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},87737:(e,t,a)=>{a.r(t),a.d(t,{originalPathname:()=>P,patchFetch:()=>g,requestAsyncStorage:()=>d,routeModule:()=>l,serverHooks:()=>h,staticGenerationAsyncStorage:()=>c});var r={};a.r(r),a.d(r,{POST:()=>u});var n=a(73278),s=a(45002),o=a(54877),i=a(71309);let p="https://translation.googleapis.com/language/translate/v2";async function u(e){try{let{text:t}=await e.json();if(!t)return i.NextResponse.json({error:"Text is required"},{status:400});let a=await fetch(`${p}/detect?q=${t}&key=${process.env.GOOGLE_TRANSLATE_API}`,{method:"POST"}).then(e=>e.json()).then(e=>e.data),r="en"==a.detections[0][0].language?"id":"en",n=await fetch(`${p}?q=${t}&target=${r}&key=${process.env.GOOGLE_TRANSLATE_API}`,{method:"POST"}),s=(await n.json()).data.translations[0];return i.NextResponse.json({translatedText:s,translatedFrom:a.detections[0][0].language})}catch(e){if(403===e.code)return i.NextResponse.json({error:"API authentication failed. Please check your API key."},{status:403});return i.NextResponse.json({error:"Translation failed"},{status:500})}}let l=new n.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/api/translate/route",pathname:"/api/translate",filename:"route",bundlePath:"app/api/translate/route"},resolvedPagePath:"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\api\\translate\\route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:d,staticGenerationAsyncStorage:c,serverHooks:h}=l,P="/api/translate/route";function g(){return(0,o.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:c})}}};var t=require("../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[9379,4833],()=>a(87737));module.exports=r})();