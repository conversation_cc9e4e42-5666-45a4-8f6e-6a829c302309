(()=>{var e={};e.id=6166,e.ids=[6166],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},30875:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>p,tree:()=>c}),r(12911),r(55695),r(3929),r(84448),r(81729),r(90996);var s=r(30170),a=r(45002),i=r(83876),n=r.n(i),l=r(66299),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let c=["",{children:["[locale]",{children:["(user-profile)",{children:["security",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,12911)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user-profile)\\security\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,55695)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user-profile)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,3929)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user-profile)\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,80603))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,84448)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,81729)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,90996,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user-profile)\\security\\page.tsx"],u="/[locale]/(user-profile)/security/page",m={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/[locale]/(user-profile)/security/page",pathname:"/[locale]/security",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},89449:(e,t,r)=>{let s={ace39bf07124e0ba39e8486050a53bc79b0621b3:()=>Promise.resolve().then(r.bind(r,18714)).then(e=>e.default)};async function a(e,...t){return(await s[e]()).apply(null,t)}e.exports={ace39bf07124e0ba39e8486050a53bc79b0621b3:a.bind(null,"ace39bf07124e0ba39e8486050a53bc79b0621b3")}},10805:(e,t,r)=>{Promise.resolve().then(r.bind(r,96076)),Promise.resolve().then(r.bind(r,69514)),Promise.resolve().then(r.bind(r,17027)),Promise.resolve().then(r.bind(r,26793)),Promise.resolve().then(r.bind(r,70697)),Promise.resolve().then(r.bind(r,92941))},96076:(e,t,r)=>{"use strict";r.d(t,{default:()=>u});var s=r(97247),a=r(58053),i=r(27757),n=r(37129),l=r(10906),o=r(92894);let c=(0,r(26323).Z)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);var d=r(84879);function u(){let e=(0,d.useTranslations)("seeker"),t=(0,n.N)(),r=(0,o.L)(e=>e.seekers.email),{toast:u}=(0,l.pm)(),m=async()=>{try{await t.mutateAsync({email:r}),u({title:e("success.requestForgetPassword.title"),description:e("success.requestForgetPassword.description",{email:r})})}catch(t){u({title:e("error.requestForgetPassword.title")})}};return(0,s.jsxs)(i.Zb,{children:[(0,s.jsxs)(i.Ol,{className:"flex max-sm:flex-col max-sm:items-start max-sm:gap-4 max-sm:pb-4 flex-row  items-center justify-between space-y-0  pb-2",children:[(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsxs)(i.ll,{className:"text-seekers-primary flex items-center",children:[s.jsx(c,{className:"mr-2 h-4 w-4"}),e("settings.profile.security.password.title")]}),s.jsx(i.SZ,{children:e("settings.profile.security.password.description")})]}),s.jsx(a.z,{size:"sm",variant:"outline",className:"border-seekers-primary text-seekers-primary hover:text-seekers-primary hover:bg-seekers-primary/10",onClick:()=>m(),children:e("cta.changePassword")})]}),s.jsx(i.aY,{children:(0,s.jsxs)("div",{className:"text-sm text-muted-foreground",children:[e("setting.profile.security.password.lastChanged")," January 15, 2025"]})})]})}},69514:(e,t,r)=>{"use strict";r.d(t,{default:()=>S});var s=r(97247),a=r(88964),i=r(27757),n=r(25008);let l=(0,r(26323).Z)("Smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]);var o=r(84879),c=r(34357),d=r(15238),u=r(81441),m=r(50555),p=r(58053),x=r(87721);async function f(e){try{return{data:(await (0,x.Ew)(e)).data.data}}catch(e){return{error:e.data.error??"An unknown error occurred"}}}var h=r(9190),y=r(44597),g=r(28964),b=r(47751),v=r(2704),j=r(34631),w=r(52208),N=r(93572),k=r(69693),P=r(12961),A=r(6649),C=r(88111),z=r(10906),_=r(92894);function q({setOpenDialog:e}){let t=(0,o.useTranslations)("seeker"),r=b.z.object({otp:b.z.string().max(6)}),{seekers:a}=(0,_.L)(),i=(0,C.D)({mutationFn:e=>(0,A.Af)(e)}),n=(0,C.D)({mutationFn:e=>(0,A.Ew)({...e,request_setting:"ACTIVE_2FA"})}),l=(0,v.cI)({resolver:(0,j.F)(r),defaultValues:{otp:""}}),{toast:c}=(0,z.pm)();l.watch("otp");let d=async r=>{try{a.has2FA?await i.mutateAsync({otp:r.otp,requested_by:a.code}):await n.mutateAsync({otp:+r.otp,request_setting:"ACTIVE_2FA"}),c({title:t("success.activateTotp")}),e?.(!1),window.location.reload()}catch(r){let e=r.response.data;c({variant:"destructive",title:t("error.failedEnablingTwoFA"),description:e.message})}};return s.jsx(w.l0,{...l,children:s.jsx("form",{id:"submit-form",onSubmit:l.handleSubmit(d),children:s.jsx(w.Wi,{control:l.control,name:"otp",render:({field:e})=>s.jsx("div",{className:"flex justify-start",children:s.jsx(N.Z,{label:"",children:s.jsx(k.Zn,{maxLength:6,...e,pattern:P.Ww,required:!0,containerClassName:"flex justify-start",children:s.jsx(k.hf,{children:Array.from({length:6},(e,t)=>s.jsx(k.cY,{index:t,className:"w-10 h-10 text-xl"},t))})})})})})})})}var T=r(82328);function F({onSubmit:e,isLoading:t}){let r=(0,o.useTranslations)("seeker"),a=((0,o.useTranslations)("seeker"),b.z.object({password:b.z.string()})),{seekers:i}=(0,_.L)(),n=(0,v.cI)({resolver:(0,j.F)(a),defaultValues:{password:""}});return s.jsx(w.l0,{...n,children:(0,s.jsxs)("form",{onSubmit:n.handleSubmit(t=>{e(t.password)}),className:"space-y-4",children:[s.jsx(T.Z,{label:r("form.label.password"),form:n,name:"password",placeholder:""}),s.jsx(p.z,{className:"w-full",variant:"default-seekers",loading:t,children:r("cta.get2FACode")})]})})}function R({}){let e=(0,o.useTranslations)("seeker"),[t,r]=(0,g.useState)(!1),[a,i]=(0,g.useState)(!1),{seekers:n}=(0,_.L)(),[l,x]=(0,g.useState)(!1),[b,v]=(0,g.useState)("ACTIVE_2FA"),[j,w]=(0,g.useState)({request_setting:"ACTIVE_2FA",password:""}),N=function(e,t=!1){return(0,h.a)({queryKey:["two-fa-otp",e],queryFn:async()=>await f(e),retry:0,enabled:t})}(j,t&&a),k=e=>{w(t=>({...t,password:e})),i(!0)};return(0,s.jsxs)(m.Z,{open:t,setOpen:r,dialogClassName:"!max-w-fit !w-fit",openTrigger:s.jsx(p.z,{variant:"outline",size:"sm",className:n.has2FA?"border-red-500 text-red-500 hover:text-red-500 hover:bg-red-50":"border-seekers-primary text-seekers-primary hover:text-seekers-primary hover:bg-seekers-primary/10",onClick:()=>{},children:e(n.has2FA?"cta.disable":"cta.enable")}),children:[(0,s.jsxs)(d.Z,{className:"text-seekers-text text-center flex flex-col items-center",children:[s.jsx(u.Z,{children:e("setting.profile.security.twoFA.title")}),s.jsx(c.Z,{className:"text-seekers-text-light",children:e("setting.profile.security.twoFA.description")})]}),l?(0,s.jsxs)("div",{className:"flex max-sm:flex-col gap-4",children:[s.jsx("div",{className:"relative aspect-square",style:{margin:"0 auto",height:200,width:200},children:s.jsx(y.default,{src:N.data?.data.qr_image||"",alt:"",fill:!0,style:{objectFit:"cover"}})}),(0,s.jsxs)("div",{className:"md:max-w-xs space-y-4",children:[s.jsx("h2",{className:"max-sm:text-sm max-sm:text-center text-xl font-bold",children:e("setting.profile.security.twoFA.useAuthenticatorApp")}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("p",{children:[(0,s.jsxs)("span",{className:"font-bold",children:[e("misc.step",{count:1})," "]})," ",e("setting.profile.security.twoFA.scanQRCodeWithAuthenticatorApp")]}),(0,s.jsxs)("p",{children:[s.jsx("span",{className:"font-bold",children:e("misc.step",{count:2})})," ",e("setting.profile.security.twoFA.enterAuthenticatorCode")]})]}),s.jsx(q,{setOpenDialog:e=>r(e)})]})]}):s.jsx("div",{children:s.jsx(F,{isLoading:N.isLoading,onSubmit:e=>k(e)})})]})}function S(){let e=(0,o.useTranslations)("seeker"),{seekers:t}=(0,_.L)();return(0,s.jsxs)(i.Zb,{children:[(0,s.jsxs)(i.Ol,{className:"flex max-sm:flex-col max-sm:items-start max-sm:gap-4 max-sm:pb-4 flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsxs)(i.ll,{className:"text-seekers-primary flex items-center",children:[s.jsx(l,{className:"mr-2 h-4 w-4"}),e("setting.profile.security.twoFA.title")]}),s.jsx(i.SZ,{children:e("setting.profile.security.twoFA.description")})]}),s.jsx(R,{enableTwoFA:t.has2FA})]}),s.jsx(i.aY,{children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx("span",{className:"text-sm text-muted-foreground",children:e("misc.status")}),s.jsx(a.C,{variant:t.has2FA?"default":"destructive",className:(0,n.cn)(t.has2FA?"bg-green-500/10 text-green-500":"bg-red-500/10 text-red-500"),children:e(t.has2FA?"cta.enable":"cta.disable")})]})})]})}},34357:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l});var s=r(97247),a=r(93009),i=r(27387),n=r(98969);function l({children:e,className:t}){return(0,a.a)("(min-width:768px)")?s.jsx(n.Be,{className:t,children:e}):s.jsx(i.u6,{className:t,children:e})}},88964:(e,t,r)=>{"use strict";r.d(t,{C:()=>l});var s=r(97247);r(28964);var a=r(87972),i=r(25008);let n=(0,a.j)("cursor-pointer inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs h-fit font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground  hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground  hover:bg-destructive/80",outline:"text-foreground",seekers:"border-transparent bg-seekers-primary/30 text-seekers-primary hover:bg-seekers-primary/80 rounded-full px-4 py-1.5"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...r}){return s.jsx("div",{className:(0,i.cn)(n({variant:t}),e,"pointer-events-none"),...r})}},27757:(e,t,r)=>{"use strict";r.d(t,{Ol:()=>l,SZ:()=>c,Zb:()=>n,aY:()=>d,ll:()=>o});var s=r(97247),a=r(28964),i=r(25008);let n=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,i.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...t}));n.displayName="Card";let l=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t}));l.displayName="CardHeader";let o=a.forwardRef(({className:e,...t},r)=>s.jsx("h3",{ref:r,className:(0,i.cn)("font-semibold leading-none tracking-tight",e),...t}));o.displayName="CardTitle";let c=a.forwardRef(({className:e,...t},r)=>s.jsx("p",{ref:r,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));c.displayName="CardDescription";let d=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,i.cn)("p-6 pt-0",e),...t}));d.displayName="CardContent",a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},12911:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>V,generateMetadata:()=>E});var s=r(72051),a=r(29507),i=r(83266),n=r(79438),l=r(35243),o=r(4459),c=r(37170),d=r(53189),u=r(69385),m=r(93844);function p(){let e=(0,u.Z)("seeker");return(0,s.jsxs)(n.Z,{className:(0,c.cn)("mx-auto flex gap-2 items-end max-sm:px-0 space-y-6 pt-6 max-sm:pt-0"),children:[s.jsx(o.vP,{className:"items-end -ml-2"}),s.jsx(l.aG,{className:"",children:(0,s.jsxs)(l.Jb,{className:"space-x-4 sm:gap-0",children:[s.jsx(l.gN,{className:"text-seekers-text font-medium text-sm",children:(0,s.jsxs)(m.rU,{href:"/",className:"flex gap-2.5 items-center",children:[s.jsx(d.Z,{className:"w-4 h-4",strokeWidth:1}),e("misc.home")]})}),s.jsx(l.bg,{className:"text-seekers-text font-medium text-sm w-3 h-fit text-center",children:"/"}),s.jsx(l.gN,{className:"capitalize text-seekers-text font-medium text-sm",children:e("accountAndProfile.security")})]})})]})}var x=r(20353),f=r(26513),h=r(26269);let y=h.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,c.cn)("rounded-xl border bg-card text-card-foreground shadow",e),...t}));y.displayName="Card";let g=h.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,c.cn)("flex flex-col space-y-1.5 p-6",e),...t}));g.displayName="CardHeader";let b=h.forwardRef(({className:e,...t},r)=>s.jsx("h3",{ref:r,className:(0,c.cn)("font-semibold leading-none tracking-tight",e),...t}));b.displayName="CardTitle";let v=h.forwardRef(({className:e,...t},r)=>s.jsx("p",{ref:r,className:(0,c.cn)("text-sm text-muted-foreground",e),...t}));v.displayName="CardDescription";let j=h.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,c.cn)("p-6 pt-0",e),...t}));j.displayName="CardContent",h.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,c.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter";var w=r(86449);let N=(0,w.Z)("Laptop",[["path",{d:"M20 16V7a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v9m16 0H4m16 0 1.28 2.55a1 1 0 0 1-.9 1.45H3.62a1 1 0 0 1-.9-1.45L4 16",key:"tarvll"}]]);function k(){let e=(0,u.Z)("seeker");return(0,s.jsxs)(y,{children:[(0,s.jsxs)(g,{className:"flex max-sm:flex-col max-sm:items-start max-sm:gap-4 max-sm:pb-4 flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsxs)(b,{className:"text-seekers-primary flex items-center",children:[s.jsx(N,{className:"mr-2 h-4 w-4"}),e("setting.profile.security.connectedDevice.title")]}),s.jsx(v,{children:e("setting.profile.security.connectedDevice.description")})]}),s.jsx(f.z,{variant:"outline",className:"border-red-500 text-red-500 hover:bg-red-50 hover:text-red-500",size:"sm",children:e("cta.signOutAll")})]}),s.jsx(j,{children:s.jsx("div",{className:"space-y-4",children:[{name:"MacBook Pro",type:"Desktop",lastActive:"Active now",isCurrent:!0},{name:"iPhone 15",type:"Mobile",lastActive:"2 hours ago",isCurrent:!1},{name:"iPad Air",type:"Tablet",lastActive:"3 days ago",isCurrent:!1}].map((t,r)=>(0,s.jsxs)("div",{className:"flex items-center justify-between border-b last:border-0 pb-4 last:pb-0",children:[(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("span",{className:"font-medium",children:t.name}),t.isCurrent&&s.jsx(x.C,{variant:"outline",className:"ml-2  bg-green-500/10 text-green-500",children:e("setting.profile.security.connectedDevice.currentDevice")})]}),(0,s.jsxs)("div",{className:"text-sm text-muted-foreground",children:[e("setting.profile.security.connectedDevice.lastActive"),": ",t.lastActive]})]}),!t.isCurrent&&s.jsx(f.z,{variant:"link",className:"text-red-500",size:"sm",children:e("cta.signOutDevice")})]},r))})})]})}let P=h.forwardRef(({className:e,...t},r)=>s.jsx("div",{className:"relative w-full overflow-auto",children:s.jsx("table",{ref:r,className:(0,c.cn)("w-full caption-bottom text-sm",e),...t})}));P.displayName="Table";let A=h.forwardRef(({className:e,...t},r)=>s.jsx("thead",{ref:r,className:(0,c.cn)("[&_tr]:border-b",e),...t}));A.displayName="TableHeader";let C=h.forwardRef(({className:e,...t},r)=>s.jsx("tbody",{ref:r,className:(0,c.cn)("[&_tr:last-child]:border-0",e),...t}));C.displayName="TableBody",h.forwardRef(({className:e,...t},r)=>s.jsx("tfoot",{ref:r,className:(0,c.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t})).displayName="TableFooter";let z=h.forwardRef(({className:e,...t},r)=>s.jsx("tr",{ref:r,className:(0,c.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));z.displayName="TableRow";let _=h.forwardRef(({className:e,...t},r)=>s.jsx("th",{ref:r,className:(0,c.cn)("h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t}));_.displayName="TableHead";let q=h.forwardRef(({className:e,...t},r)=>s.jsx("td",{ref:r,className:(0,c.cn)("p-2 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",e),...t}));q.displayName="TableCell",h.forwardRef(({className:e,...t},r)=>s.jsx("caption",{ref:r,className:(0,c.cn)("mt-4 text-sm text-muted-foreground",e),...t})).displayName="TableCaption";let T=(0,w.Z)("History",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}],["path",{d:"M12 7v5l4 2",key:"1fdv2h"}]]);function F(){let e=(0,u.Z)("seeker"),t=[{date:new Date("2025-02-02 14:30").toLocaleDateString(),device:"MacBook Pro",location:"Amsterdam, Netherlands",status:"Success"},{date:new Date("2025-02-01 09:15").toLocaleDateString(),device:"iPhone 15",location:"Utrecht, Netherlands",status:"Success"},{date:new Date("2025-01-31 18:45").toLocaleDateString(),device:"Chrome on Windows",location:"Den Haag, Netherlands",status:"Success"}];return(0,s.jsxs)(y,{children:[(0,s.jsxs)(g,{className:"flex max-sm:flex-col max-sm:items-start max-sm:gap-4 max-sm:pb-4 flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsxs)("div",{className:"space-y-1",children:[(0,s.jsxs)(b,{className:"text-seekers-primary flex items-center",children:[s.jsx(T,{className:"mr-2 h-4 w-4"}),e("setting.profile.security.loginHistory.title")]}),s.jsx(v,{children:e("setting.profile.security.loginHistory.description")})]}),s.jsx(f.z,{size:"sm",variant:"outline",className:"border-seekers-primary text-seekers-primary hover:text-seekers-primary hover:bg-seekers-primary/10",children:e("cta.viewAll")})]}),s.jsx(j,{children:(0,s.jsxs)(P,{children:[s.jsx(A,{children:(0,s.jsxs)(z,{className:"hover:bg-transparent",children:[s.jsx(_,{children:e("setting.profile.security.loginHistory.table.date")}),s.jsx(_,{children:e("setting.profile.security.loginHistory.table.device")}),s.jsx(_,{children:e("setting.profile.security.loginHistory.table.location")}),s.jsx(_,{children:e("setting.profile.security.loginHistory.table.status")})]})}),s.jsx(C,{children:t.map((e,t)=>(0,s.jsxs)(z,{className:"hover:bg-transparent",children:[s.jsx(q,{children:e.date}),s.jsx(q,{children:e.device}),s.jsx(q,{children:e.location}),s.jsx(q,{children:s.jsx(x.C,{variant:"outline",className:"bg-green-500/10 text-green-500",children:e.status})})]},t))})]})})]})}var R=r(45347);let S=(0,R.createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\app\[locale]\(user-profile)\security\change-password.tsx#default`),Z=(0,R.createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\app\[locale]\(user-profile)\security\two-fa.tsx#default`);var D=r(92898);async function E(){let e=await (0,a.Z)("seeker"),t=process.env.USER_DOMAIN||"https://www.property-plaza.com/",r=await (0,i.Z)()||m.DI.defaultLocale;return{title:e("metadata.security.title"),description:e("metadata.security.description"),alternates:{canonical:t+r+D.s0,languages:{en:t+"en"+D.s0,id:t+"id"+D.s0,"x-default":t+D.s0.replace("/","")}},openGraph:{title:e("metadata.subsriptionPlan.title"),description:e("metadata.subsriptionPlan.description"),images:[{url:t+"og.png",width:1200,height:630,alt:"Property Plaza"}],type:"website",url:t+r+D.s0,countryName:"Indonesia",emails:"<EMAIL>",locale:r,alternateLocale:m.DI.locales,siteName:"Property plaza"},applicationName:"Property plaza",twitter:{card:"summary_large_image",title:e("metadata.subsriptionPlan.title"),description:e("metadata.subsriptionPlan.description"),images:[t+"og.jpg"]},robots:{index:!0,follow:!0,nocache:!1}}}function V(){let e=(0,u.Z)("seeker");return(0,s.jsxs)(s.Fragment,{children:[s.jsx(p,{}),(0,s.jsxs)(n.Z,{className:"space-y-8 mt-8 mb-14 max-sm:px-0",children:[s.jsx("div",{className:"flex justify-between",children:(0,s.jsxs)("div",{children:[s.jsx("h1",{className:"text-2xl font-bold tracking-tight",children:e("setting.accountAndProfile.security.title")}),s.jsx("h2",{className:"text-muted-foreground mt-2",children:e("setting.accountAndProfile.security.description")})]})}),s.jsx(S,{}),s.jsx(Z,{}),s.jsx(F,{}),s.jsx(k,{})]})]})}},20353:(e,t,r)=>{"use strict";r.d(t,{C:()=>l});var s=r(72051);r(26269);var a=r(29666),i=r(37170);let n=(0,a.j)("cursor-pointer inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs h-fit font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground  hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground  hover:bg-destructive/80",outline:"text-foreground",seekers:"border-transparent bg-seekers-primary/30 text-seekers-primary hover:bg-seekers-primary/80 rounded-full px-4 py-1.5"}},defaultVariants:{variant:"default"}});function l({className:e,variant:t,...r}){return s.jsx("div",{className:(0,i.cn)(n({variant:t}),e,"pointer-events-none"),...r})}},26513:(e,t,r)=>{"use strict";r.d(t,{z:()=>d});var s=r(72051),a=r(26269),i=r(21322),n=r(29666),l=r(37170);let o=(0,r(86449).Z)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),c=(0,n.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-seekers-primary text-white shadow hover:bg-seekers-primary/90","default-seekers":"bg-seekers-primary text-white shadow hover:bg-seekers-primary-light/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef(({className:e,variant:t,size:r,asChild:a=!1,loading:n=!1,...d},u)=>{let m=a?i.g7:"button";return s.jsx(m,{className:(0,l.cn)(c({variant:t,size:r,className:e})),ref:u,disabled:n||d.disabled,...d,children:n?s.jsx(o,{className:(0,l.cn)("h-4 w-4 animate-spin")}):d.children})});d.displayName="Button"},18714:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(28713);r(9640);var a=r(53020);async function i(e,t,r){let s=(0,a.cookies)(),i=s.get("tkn")?.value;try{let s=await fetch(e,{method:t,headers:{Authorization:`Bearer ${i}`,"Content-Type":"application/json"},...r});if(!s.ok)return{data:null,meta:void 0,error:{status:s.status,name:s.statusText,message:await s.text()||"Unexpected error",details:{}}};let a=await s.json();if(a.error)return{data:null,meta:void 0,error:a.error};return{data:a.data,meta:a.meta,error:void 0}}catch(e){return{data:null,meta:void 0,error:{status:500,details:{cause:e.cause},message:e.message,name:e.name}}}}(0,r(83557).h)([i]),(0,s.j)("ace39bf07124e0ba39e8486050a53bc79b0621b3",i)},29507:(e,t,r)=>{"use strict";r.d(t,{Z:()=>n});var s=r(26269),a=r(95817),i=r(60434),n=(0,s.cache)(async function(e){let t,r;"string"==typeof e?t=e:e&&(r=e.locale,t=e.namespace);let s=await (0,i.Z)(r);return(0,a.eX)({...s,namespace:t,messages:s.messages})})},29666:(e,t,r)=>{"use strict";r.d(t,{j:()=>n});var s=r(36272);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=s.W,n=(e,t)=>r=>{var s;if((null==t?void 0:t.variants)==null)return i(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:n,defaultVariants:l}=t,o=Object.keys(n).map(e=>{let t=null==r?void 0:r[e],s=null==l?void 0:l[e];if(null===t)return null;let i=a(t)||a(s);return n[e][i]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,s]=t;return void 0===s||(e[r]=s),e},{});return i(e,o,null==t?void 0:null===(s=t.compoundVariants)||void 0===s?void 0:s.reduce((e,t)=>{let{class:r,className:s,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...c}[t]):({...l,...c})[t]===r})?[...e,r,s]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[9379,5063,4916,9467,4859,5268,3832,6666,9965,7496],()=>r(30875));module.exports=s})();