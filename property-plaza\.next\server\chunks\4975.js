"use strict";exports.id=4975,exports.ids=[4975],exports.modules={94975:(e,t,n)=>{n.d(t,{YI:()=>L});var r=n(72051);function l(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function i(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?l(Object(n),!0).forEach(function(t){var r,l;r=t,l=n[t],(r=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(r))in e?Object.defineProperty(e,r,{value:l,enumerable:!0,configurable:!0,writable:!0}):e[r]=l}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):l(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function o(e){return"span"===e._type&&"text"in e&&"string"==typeof e.text&&(typeof e.marks>"u"||Array.isArray(e.marks)&&e.marks.every(e=>"string"==typeof e))}function s(e){return"string"==typeof e._type&&"@"!==e._type[0]&&(!("markDefs"in e)||!e.markDefs||Array.isArray(e.markDefs)&&e.markDefs.every(e=>"string"==typeof e._key))&&"children"in e&&Array.isArray(e.children)&&e.children.every(e=>"object"==typeof e&&"_type"in e)}function u(e){return s(e)&&"listItem"in e&&"string"==typeof e.listItem&&(typeof e.level>"u"||"number"==typeof e.level)}function c(e){return"@list"===e._type}function f(e){return"@span"===e._type}function p(e){return"@text"===e._type}let y=["strong","em","code","underline","strike-through"];function a(e,t,n){if(!o(e)||!e.marks||!e.marks.length)return[];let r=e.marks.slice(),l={};return r.forEach(e=>{l[e]=1;for(let r=t+1;r<n.length;r++){let t=n[r];if(t&&o(t)&&Array.isArray(t.marks)&&-1!==t.marks.indexOf(e))l[e]++;else break}}),r.sort((e,t)=>(function(e,t,n){let r=e[t],l=e[n];if(r!==l)return l-r;let i=y.indexOf(t),o=y.indexOf(n);return i!==o?i-o:t.localeCompare(n)})(l,e,t))}function m(e,t,n){return{_type:"@list",_key:`${e._key||`${t}`}-parent`,mode:n,level:e.level||1,listItem:e.listItem,children:[e]}}function h(e,t){let n=t.level||1,r=t.listItem||"normal",l="string"==typeof t.listItem;if(c(e)&&(e.level||1)===n&&l&&(e.listItem||"normal")===r)return e;if(!("children"in e))return;let i=e.children[e.children.length-1];return i&&!o(i)?h(i,t):void 0}var k=n(26269);let d=["block","list","listItem","marks","types"],b=["listItem"],v=["_key"];function j(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}function x(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?j(Object(n),!0).forEach(function(t){var r,l;r=t,l=n[t],(r=function(e){var t=function(e,t){if("object"!=typeof e||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:t+""}(r))in e?Object.defineProperty(e,r,{value:l,enumerable:!0,configurable:!0,writable:!0}):e[r]=l}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):j(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function g(e,t){if(null==e)return{};var n,r,l=function(e,t){if(null==e)return{};var n={};for(var r in e)if(({}).hasOwnProperty.call(e,r)){if(t.includes(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.includes(n)||({}).propertyIsEnumerable.call(e,n)&&(l[n]=e[n])}return l}let O={textDecoration:"underline"},w=(e,t)=>`[@portabletext/react] Unknown ${e}, specify a component for it in the \`components.${t}\` prop`,I=e=>w(`block type "${e}"`,"types"),_=e=>w(`mark type "${e}"`,"marks"),P=e=>w(`block style "${e}"`,"block"),S=e=>w(`list style "${e}"`,"list"),$=e=>w(`list item style "${e}"`,"listItem");function D(e){console.warn(e)}let A={display:"none"},T={types:{},block:{normal:({children:e})=>(0,r.jsx)("p",{children:e}),blockquote:({children:e})=>(0,r.jsx)("blockquote",{children:e}),h1:({children:e})=>(0,r.jsx)("h1",{children:e}),h2:({children:e})=>(0,r.jsx)("h2",{children:e}),h3:({children:e})=>(0,r.jsx)("h3",{children:e}),h4:({children:e})=>(0,r.jsx)("h4",{children:e}),h5:({children:e})=>(0,r.jsx)("h5",{children:e}),h6:({children:e})=>(0,r.jsx)("h6",{children:e})},marks:{em:({children:e})=>(0,r.jsx)("em",{children:e}),strong:({children:e})=>(0,r.jsx)("strong",{children:e}),code:({children:e})=>(0,r.jsx)("code",{children:e}),underline:({children:e})=>(0,r.jsx)("span",{style:O,children:e}),"strike-through":({children:e})=>(0,r.jsx)("del",{children:e}),link:({children:e,value:t})=>(0,r.jsx)("a",{href:t?.href,children:e})},list:{number:({children:e})=>(0,r.jsx)("ol",{children:e}),bullet:({children:e})=>(0,r.jsx)("ul",{children:e})},listItem:({children:e})=>(0,r.jsx)("li",{children:e}),hardBreak:()=>(0,r.jsx)("br",{}),unknownType:({value:e,isInline:t})=>{let n=I(e._type);return t?(0,r.jsx)("span",{style:A,children:n}):(0,r.jsx)("div",{style:A,children:n})},unknownMark:({markType:e,children:t})=>(0,r.jsx)("span",{className:`unknown__pt__mark__${e}`,children:t}),unknownList:({children:e})=>(0,r.jsx)("ul",{children:e}),unknownListItem:({children:e})=>(0,r.jsx)("li",{children:e}),unknownBlockStyle:({children:e})=>(0,r.jsx)("p",{children:e})};function E(e,t,n){let r=t[n],l=e[n];return"function"==typeof r||r&&"function"==typeof l?r:r?x(x({},l),r):l}function L({value:e,components:t,listNestingMode:n,onMissingComponent:l=D}){let o=l||N,s=function(e,t){let n;let r=[];for(let o=0;o<e.length;o++){let s=e[o];if(s){var l;if(!u(s)){r.push(s),n=void 0;continue}if(!n){n=m(s,o,t),r.push(n);continue}if(l=n,(s.level||1)===l.level&&s.listItem===l.listItem){n.children.push(s);continue}if((s.level||1)>n.level){let e=m(s,o,t);if("html"===t){let t=n.children[n.children.length-1],r=i(i({},t),{},{children:[...t.children,e]});n.children[n.children.length-1]=r}else n.children.push(e);n=e;continue}if((s.level||1)<n.level){let e=r[r.length-1],l=e&&h(e,s);if(l){(n=l).children.push(s);continue}n=m(s,o,t),r.push(n);continue}if(s.listItem!==n.listItem){let e=r[r.length-1],l=e&&h(e,{level:s.level||1});if(l&&l.listItem===s.listItem){(n=l).children.push(s);continue}n=m(s,o,t),r.push(n);continue}console.warn("Unknown state encountered for block",s),r.push(s)}}return r}(Array.isArray(e)?e:[e],n||"html"),c=(0,k.useMemo)(()=>t?function(e,t){let{block:n,list:r,listItem:l,marks:i,types:o}=t,s=g(t,d);return x(x({},e),{},{block:E(e,t,"block"),list:E(e,t,"list"),listItem:E(e,t,"listItem"),marks:E(e,t,"marks"),types:E(e,t,"types")},s)}(T,t):T,[t]),f=(0,k.useMemo)(()=>M(c,o),[c,o]),p=s.map((e,t)=>f({node:e,index:t,isInline:!1,renderNode:f}));return(0,r.jsx)(r.Fragment,{children:p})}let M=(e,t)=>function n(l){let{node:i,index:o,isInline:y}=l,a=i._key||`node-${o}`;return c(i)?function(l,i,o){let s=l.children.map((e,t)=>n({node:e._key?e:x(x({},e),{},{_key:`li-${i}-${t}`}),index:t,isInline:!1,renderNode:n})),u=e.list,c=("function"==typeof u?u:u[l.listItem])||e.unknownList;if(c===e.unknownList){let e=l.listItem||"bullet";t(S(e),{nodeType:"listStyle",type:e})}return(0,r.jsx)(c,{value:l,index:i,isInline:!1,renderNode:n,children:s},o)}(i,o,a):u(i)?function(l,i,o){let s=B({node:l,index:i,isInline:!1,renderNode:n}),u=e.listItem,c=("function"==typeof u?u:u[l.listItem])||e.unknownListItem;if(c===e.unknownListItem){let e=l.listItem||"bullet";t($(e),{type:e,nodeType:"listItemStyle"})}let f=s.children;if(l.style&&"normal"!==l.style){let{listItem:e}=l;f=n({node:g(l,b),index:i,isInline:!1,renderNode:n})}return(0,r.jsx)(c,{value:l,index:i,isInline:!1,renderNode:n,children:f},o)}(i,o,a):f(i)?function(l,i,o){let{markDef:s,markType:u,markKey:c}=l,y=e.marks[u]||e.unknownMark,a=l.children.map((e,t)=>n({node:e,index:t,isInline:!0,renderNode:n}));return y===e.unknownMark&&t(_(u),{nodeType:"mark",type:u}),(0,r.jsx)(y,{text:function e(t){let n="";return t.children.forEach(t=>{p(t)?n+=t.text:f(t)&&(n+=e(t))}),n}(l),value:s,markType:u,markKey:c,renderNode:n,children:a},o)}(i,0,a):i._type in e.types?function(t,l,i,o){let s=e.types[t._type];return s?(0,r.jsx)(s,x({},{value:t,isInline:o,index:l,renderNode:n}),i):null}(i,o,a,y):s(i)?function(l,i,o,s){let u=B({node:l,index:i,isInline:s,renderNode:n}),{_key:c}=u,f=g(u,v),p=f.node.style||"normal",y=("function"==typeof e.block?e.block:e.block[p])||e.unknownBlockStyle;return y===e.unknownBlockStyle&&t(P(p),{nodeType:"blockStyle",type:p}),(0,r.jsx)(y,x(x({},f),{},{value:f.node,renderNode:n}),o)}(i,o,a,y):p(i)?function(t,n){if(t.text===`
`){let t=e.hardBreak;return t?(0,r.jsx)(t,{},n):`
`}return t.text}(i,a):function(l,i,o,s){t(I(l._type),{nodeType:"block",type:l._type});let u=e.unknownType;return(0,r.jsx)(u,x({},{value:l,isInline:s,index:i,renderNode:n}),o)}(i,o,a,y)};function B(e){let{node:t,index:n,isInline:r,renderNode:l}=e,i=(function(e){var t;let{children:n,markDefs:r=[]}=e;if(!n||!n.length)return[];let l=n.map(a),i={_type:"@span",children:[],markType:"<unknown>"},s=[i];for(let e=0;e<n.length;e++){let i=n[e];if(!i)continue;let u=l[e]||[],c=1;if(s.length>1)for(;c<s.length;c++){let e=(null==(t=s[c])?void 0:t.markKey)||"",n=u.indexOf(e);if(-1===n)break;u.splice(n,1)}let f=(s=s.slice(0,c))[s.length-1];if(f){for(let e of u){let t=r.find(t=>t._key===e),n=t?t._type:e,l={_type:"@span",_key:i._key,children:[],markDef:t,markType:n,markKey:e};f.children.push(l),s.push(l),f=l}if(o(i)){let e=i.text.split(`
`);for(let t=e.length;t-- >1;)e.splice(t,0,`
`);f.children=f.children.concat(e.map(e=>({_type:"@text",text:e})))}else f.children=f.children.concat(i)}}return i.children})(t).map((e,t)=>l({node:e,isInline:!0,index:t,renderNode:l}));return{_key:t._key||`block-${n}`,children:i,index:n,isInline:r,node:t}}function N(){}}};