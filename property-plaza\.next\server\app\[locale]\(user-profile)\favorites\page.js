(()=>{var e={};e.id=6535,e.ids=[6535],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},29521:(e,a,t)=>{"use strict";t.r(a),t.d(a,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>p,pages:()=>d,routeModule:()=>u,tree:()=>c}),t(5181),t(55695),t(3929),t(84448),t(81729),t(90996);var r=t(30170),s=t(45002),l=t(83876),i=t.n(l),n=t(66299),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(a,o);let c=["",{children:["[locale]",{children:["(user-profile)",{children:["favorites",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,5181)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user-profile)\\favorites\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,55695)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user-profile)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,3929)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user-profile)\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,80603))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,84448)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,81729)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(t.t.bind(t,90996,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user-profile)\\favorites\\page.tsx"],p="/[locale]/(user-profile)/favorites/page",m={require:t,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/[locale]/(user-profile)/favorites/page",pathname:"/[locale]/favorites",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},10874:(e,a,t)=>{Promise.resolve().then(t.bind(t,76830)),Promise.resolve().then(t.bind(t,13029)),Promise.resolve().then(t.bind(t,17027)),Promise.resolve().then(t.bind(t,26793)),Promise.resolve().then(t.bind(t,70697)),Promise.resolve().then(t.bind(t,92941))},76830:(e,a,t)=>{"use strict";t.d(a,{default:()=>v});var r=t(97247),s=t(76542),l=t(84879),i=t(88183);t(28964);var n=t(28556),o=t(59683),c=t(9190),d=t(24519),p=t(79984),m=t(32430),u=t.n(m),x=t(34200),f=t(58053),g=t(75476),h=t(92894);function v({page:e,conversions:a,sortBy:t}){let m=(0,l.useTranslations)("seeker"),v=(0,i.h)(),{seekers:y}=(0,h.L)(),_=(0,l.useLocale)(),{query:P}=function(e,a=!1,t="en"){let r=(0,d.Q)(),s=r?.data?.data,l=["favorite-seekers-listing",e],{failureCount:i,...n}=(0,c.a)({queryKey:l,queryFn:async()=>{let a=e.max_price||s?.priceRange.max,r=e.min_price||s?.priceRange.min||1,l=e.building_largest||s?.buildingSizeRange.max,i=e.building_smallest||s?.buildingSizeRange.min||1,n=e.land_largest||s?.landSizeRange.max,c=e.land_smallest||s?.landSizeRange.min||1,d=e.garden_largest||s?.gardenSizeRange.max,m=e.garden_smallest||s?.gardenSizeRange.min||1,f=e.area;e.area?.zoom==x.lJ.toString()&&(f=void 0);let g=e.type?.includes("all")?void 0:u().uniq(e.type?.flatMap(e=>e!==p.yJ.commercialSpace?e:[p.yJ.cafeOrRestaurants,p.yJ.shops,p.yJ.offices])),h={...e,type:g,search:"all"==e.search?void 0:e.search,min_price:r,max_price:a,building_largest:l,building_smallest:i,land_largest:n,land_smallest:c,garden_largest:d,garden_smallest:m,area:f||void 0,property_of_view:e.property_of_view,sort_by:e.sort_by};return e.min_price&&e.min_price!=s?.priceRange.min||a!=s?.priceRange.max||(h.max_price=void 0,h.min_price=void 0),e.building_smallest&&e.building_smallest!=s?.buildingSizeRange.min||l!=s?.buildingSizeRange.max||(h.building_largest=void 0,h.building_smallest=void 0),e.land_smallest&&e.land_smallest!=s?.landSizeRange.min||n!=s?.landSizeRange.max||(h.land_largest=void 0,h.land_smallest=void 0),e.garden_smallest&&e.garden_smallest!=s?.gardenSizeRange.min||d!=s?.gardenSizeRange.max||(h.garden_largest=void 0,h.garden_smallest=void 0),await (0,o.sK)(h,t)},enabled:!0,retry:!1});return{query:n,filterQueryKey:l}}({page:e||"1",per_page:"20",sort_by:t||"DATE_NEWEST"},y?.email!="",_);return(0,r.jsxs)(r.Fragment,{children:[r.jsx("section",{className:"min-h-[calc(100vh-202px)]",children:r.jsx("div",{className:"grid gap-3 gap-x-3 gap-y-6 max-sm:my-4 md:grid-cols-2 xl:grid-cols-4",children:P.isPending?Array(12).fill(0).map((e,a)=>r.jsx(n.yZ,{},a)):v.data&&v.data.length>0?r.jsx(r.Fragment,{children:v.data.map((e,t)=>r.jsx("div",{children:(0,r.jsxs)(n.yd,{className:"space-y-3",data:{...e,isFavorite:!0},conversion:a,handleFavoriteListing:e=>{if(!e){let e=v.data.filter((e,a)=>a!==t);v.setData(e||[])}},children:[r.jsx(n.Zf,{heartSize:"large",allowFavoriteWhileInactive:!0}),(0,r.jsxs)("div",{className:"px-0.5 space-y-2",children:[(0,r.jsxs)("div",{children:[r.jsx("h2",{className:"text-base text-seekers-text font-semibold line-clamp-1",children:e.title}),r.jsx(n.$D,{className:"text-seekers-text"})]}),r.jsx(n.hj,{})]})]})},t))}):(0,r.jsxs)("div",{className:"col-span-4 flex flex-col justify-center items-center",children:[r.jsx("p",{className:"col-span-full text-center font-semibold max-w-md py-8",children:m("listing.misc.favoritePropertyNotFound")}),r.jsx(f.z,{variant:"default-seekers",asChild:!0,children:r.jsx(g.rU,{href:"/",children:m("cta.showAllProperty")})})]})})}),r.jsx("section",{className:"!my-12",children:P.isPending||P.data?.data?.length&&P.data?.data?.length<20&&1==P.data.meta.pageCount?r.jsx(r.Fragment,{}):r.jsx("div",{className:"w-fit mx-auto",children:r.jsx(s.g,{meta:P?.data?.meta,totalThreshold:20,disableRowPerPage:!0})})})]})}},90532:(e,a,t)=>{"use strict";t.d(a,{A0:()=>v,KI:()=>x,Pz:()=>h,am:()=>g,d$:()=>f,lr:()=>u});var r=t(97247),s=t(28964),l=t(33327),i=t(25008),n=t(58053),o=t(30938),c=t(67636),d=t(84879);let p=s.createContext(null);function m(){let e=s.useContext(p);if(!e)throw Error("useCarousel must be used within a <Carousel />");return e}let u=s.forwardRef(({orientation:e="horizontal",opts:a,setApi:t,plugins:n,className:o,children:c,...d},m)=>{let[u,x]=(0,l.Z)({...a,axis:"horizontal"===e?"x":"y"},n),[f,g]=s.useState(!1),[h,v]=s.useState(!1),[y,_]=s.useState(0),P=s.useCallback(e=>{e&&(g(e.canScrollPrev()),v(e.canScrollNext()),_(e.selectedScrollSnap()))},[]),b=s.useCallback(()=>{x?.scrollPrev()},[x]),j=s.useCallback(()=>{x?.scrollNext()},[x]),w=s.useCallback(e=>{"ArrowLeft"===e.key?(e.preventDefault(),b()):"ArrowRight"===e.key&&(e.preventDefault(),j())},[b,j]),z=s.useCallback(e=>{x?.scrollTo(e)},[x]);return s.useEffect(()=>{x&&t&&t(x)},[x,t]),s.useEffect(()=>{if(x)return P(x),x.on("reInit",P),x.on("select",P),()=>{x?.off("select",P)}},[x,P]),r.jsx(p.Provider,{value:{carouselRef:u,api:x,opts:a,orientation:e||(a?.axis==="y"?"vertical":"horizontal"),scrollPrev:b,scrollNext:j,canScrollPrev:f,canScrollNext:h,selectedIndex:y,scrollTo:z},children:r.jsx("div",{...d,ref:m,onKeyDownCapture:w,className:(0,i.cn)("relative",o),role:"region","aria-roledescription":"carousel",children:c})})});u.displayName="Carousel";let x=s.forwardRef(({className:e,...a},t)=>{let{carouselRef:s,orientation:l}=m();return r.jsx("div",{ref:s,className:"overflow-hidden",children:r.jsx("div",{...a,ref:t,className:(0,i.cn)("flex","horizontal"===l?"-ml-4":"-mt-4 flex-col",e)})})});x.displayName="CarouselContent";let f=s.forwardRef(({className:e,...a},t)=>{let{orientation:s}=m();return r.jsx("div",{...a,ref:t,role:"group","aria-roledescription":"slide",className:(0,i.cn)("min-w-0 shrink-0 grow-0 basis-full","horizontal"===s?"pl-4":"pt-4",e)})});f.displayName="CarouselItem";let g=s.forwardRef(({iconClassName:e,className:a,variant:t="outline",size:s="icon",...l},c)=>{let{orientation:p,scrollPrev:u,canScrollPrev:x}=m(),f=(0,d.useTranslations)("universal");return(0,r.jsxs)(n.z,{...l,ref:c,variant:t,size:s,className:(0,i.cn)("absolute  h-6 w-6 rounded-full","horizontal"===p?"-left-12 top-1/2 -translate-y-1/2":"-top-12 left-1/2 -translate-x-1/2 rotate-90",a),disabled:!x,onClick:u,children:[r.jsx(o.Z,{className:(0,i.cn)("h-4 w-4",e)}),r.jsx("span",{className:"sr-only",children:f("cta.previous")})]})});g.displayName="CarouselPrevious";let h=s.forwardRef(({iconClassName:e,className:a,variant:t="outline",size:s="icon",...l},o)=>{let{orientation:p,scrollNext:u,canScrollNext:x}=m(),f=(0,d.useTranslations)("seeker");return(0,r.jsxs)(n.z,{...l,ref:o,variant:t,size:s,className:(0,i.cn)("absolute h-6 w-6 rounded-full","horizontal"===p?"-right-12 top-1/2 -translate-y-1/2":"-bottom-12 left-1/2 -translate-x-1/2 rotate-90",a),disabled:!x,onClick:u,children:[r.jsx(c.Z,{className:(0,i.cn)("h-4 w-4",e)}),r.jsx("span",{className:"sr-only",children:f("cta.next")})]})});h.displayName="CarouselNext";let v=s.forwardRef(({className:e,carouselDotClassName:a,...t},s)=>{let{selectedIndex:l,scrollTo:o,api:c}=m();return r.jsx("div",{ref:s,className:(0,i.cn)("embla__dots absolute z-50 flex w-fit items-center justify-center gap-2",e),...t,children:c?.scrollSnapList().map((e,t)=>r.jsx(n.z,{size:"icon",className:i.cn(a,"embla__dot h-2 w-2 rounded-full ",t===l?"bg-white/90 ":"bg-black/10"),onClick:()=>o?.(t)},t))})});v.displayName="CarouselDots"},5181:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>_,generateMetadata:()=>y});var r=t(72051),s=t(79438),l=t(35243),i=t(4459),n=t(37170),o=t(53189),c=t(69385),d=t(93844);function p(){let e=(0,c.Z)("seeker");return(0,r.jsxs)(s.Z,{className:(0,n.cn)("mx-auto flex gap-2 items-end max-sm:px-0 space-y-6 pt-6 max-sm:pt-0"),children:[r.jsx(i.vP,{className:"items-end -ml-2"}),r.jsx(l.aG,{className:"",children:(0,r.jsxs)(l.Jb,{className:"space-x-4 sm:gap-0",children:[r.jsx(l.gN,{className:"text-seekers-text font-medium text-sm",children:(0,r.jsxs)(d.rU,{href:"/",className:"flex gap-2.5 items-center",children:[r.jsx(o.Z,{className:"w-4 h-4",strokeWidth:1}),e("misc.home")]})}),r.jsx(l.bg,{className:"text-seekers-text font-medium text-sm w-3 h-fit text-center",children:"/"}),r.jsx(l.gN,{className:"capitalize text-seekers-text font-medium text-sm",children:e("accountAndProfile.favorite")})]})})]})}var m=t(29507),u=t(83266),x=t(45347);let f=(0,x.createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\app\[locale]\(user-profile)\favorites\content.tsx#default`),g=(0,x.createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\app\[locale]\(user)\s\filter-header.tsx#default`);var h=t(70276),v=t(92898);async function y(){let e=await (0,m.Z)("seeker"),a=await (0,u.Z)()||d.DI.defaultLocale,t=process.env.USER_DOMAIN||"https://www.property-plaza.com/";return{title:e("metadata.favorite.title"),description:e("metadata.favorite.description"),alternates:{canonical:t+a+v.Y8,languages:{en:t+"en"+v.Y8,id:t+"id"+v.Y8,"x-default":t+v.Y8.replace("/","")}},openGraph:{title:e("metadata.favorite.title"),description:e("metadata.favorite.description"),images:[{url:t+"og.png",width:1200,height:630,alt:"Property Plaza"}],type:"website",url:t+a+v.Y8,countryName:"Indonesia",emails:"<EMAIL>",locale:a,alternateLocale:d.DI.locales,siteName:"Property plaza"},applicationName:"Property plaza",twitter:{card:"summary_large_image",title:e("metadata.favorite.title"),description:e("metadata.favorite.description"),images:[t+"og.jpg"]},robots:{index:!1,follow:!0,nocache:!1}}}async function _({params:e,searchParams:a}){let t=await (0,m.Z)("seeker"),l=await (0,h.T)();return(0,r.jsxs)(r.Fragment,{children:[r.jsx(p,{}),(0,r.jsxs)(s.Z,{className:"space-y-8 my-8 max-sm:px-0",children:[(0,r.jsxs)("div",{className:"flex max-sm:flex-col justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-2xl font-bold tracking-tight",children:t("setting.favorites.savedItems.title")}),r.jsx("h2",{className:"text-muted-foreground mt-2",children:t("setting.favorites.savedItems.description")})]}),r.jsx(g,{showFilter:!1,conversions:l.data||[]})]}),r.jsx(f,{page:a.page,types:"all",query:"all",sortBy:a.sortBy||void 0,conversions:l.data})]})]})}}};var a=require("../../../../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),r=a.X(0,[9379,5063,4916,9467,4859,5268,3327,3832,5500,6430,6666,9965,7496,4805,2620],()=>t(29521));module.exports=r})();