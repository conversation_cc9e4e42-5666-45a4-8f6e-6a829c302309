import { Metadata } from "next"
import { getLocale, getTranslations } from "next-intl/server"
import SecurityBreadCrumb from "./bread-crumb"
import MainContentLayout from "@/components/seekers-content-layout/main-content-layout"
import { useTranslations } from "next-intl"
import ConnectedDevice from "./connected-device"
import LoginHistory from "./login-history"
import ChangePassword from "./change-password"
import TwoFA from "./two-fa"
import { securitySettingUrl } from "@/lib/constanta/route"
import { routing } from "@/lib/locale/routing"

export async function generateMetadata(): Promise<Metadata> {
  const t = await getTranslations("seeker")
  const baseUrl = process.env.USER_DOMAIN || "https://www.property-plaza.com/"
  const locale = await getLocale() || routing.defaultLocale

  return {
    title: t("metadata.security.title"),
    description: t("metadata.security.description"),
    alternates: {
      canonical: baseUrl + locale + securitySettingUrl,
      languages: {
        en: baseUrl + "en" + securitySettingUrl,
        id: baseUrl + "id" + securitySettingUrl,
        "x-default": baseUrl + securitySettingUrl.replace("/", ""),
      }
    },
    openGraph: {
      title: t("metadata.subsriptionPlan.title"),
      description: t("metadata.subsriptionPlan.description"),
      images: [
        {
          url: baseUrl + "og.png",
          width: 1200,
          height: 630,
          alt: "Property Plaza",
        }
      ],
      type: "website",
      url: baseUrl + locale + securitySettingUrl,
      countryName: "Indonesia",
      emails: "<EMAIL>",
      locale: locale,
      alternateLocale: routing.locales,
      siteName: "Property plaza"
    },
    applicationName: "Property plaza",
    twitter: {
      card: "summary_large_image",
      title: t("metadata.subsriptionPlan.title"),
      description: t("metadata.subsriptionPlan.description"),
      images: [baseUrl + "og.jpg"],
    },

    robots: {
      index: true,
      follow: true,
      nocache: false,
    },
  }
}

export default function SecurityPage() {
  const t = useTranslations("seeker")
  return <>
    <SecurityBreadCrumb />
    <MainContentLayout className="space-y-8 mt-8 mb-14 max-sm:px-0">
      <div className="flex justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">{t("setting.accountAndProfile.security.title")}</h1>
          <h2 className="text-muted-foreground mt-2">{t("setting.accountAndProfile.security.description")}</h2>
        </div>
      </div>
      <ChangePassword />
      <TwoFA />
      <LoginHistory />
      <ConnectedDevice />
    </MainContentLayout>
  </>
}