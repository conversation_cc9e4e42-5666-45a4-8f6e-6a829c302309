(()=>{var e={};e.id=8462,e.ids=[8462],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},78897:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c}),r(51239),r(46227),r(84448),r(81729),r(90996);var s=r(30170),a=r(45002),i=r(83876),n=r.n(i),l=r(66299),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let c=["",{children:["[locale]",{children:["verify",{children:["success",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,51239)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\verify\\success\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,46227)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\verify\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,80603))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,84448)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,81729)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,90996,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\verify\\success\\page.tsx"],u="/[locale]/verify/success/page",p={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/[locale]/verify/success/page",pathname:"/[locale]/verify/success",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},89449:(e,t,r)=>{let s={ace39bf07124e0ba39e8486050a53bc79b0621b3:()=>Promise.resolve().then(r.bind(r,18714)).then(e=>e.default)};async function a(e,...t){return(await s[e]()).apply(null,t)}e.exports={ace39bf07124e0ba39e8486050a53bc79b0621b3:a.bind(null,"ace39bf07124e0ba39e8486050a53bc79b0621b3")}},14038:(e,t,r)=>{Promise.resolve().then(r.bind(r,84059)),Promise.resolve().then(r.bind(r,78781)),Promise.resolve().then(r.bind(r,91860)),Promise.resolve().then(r.bind(r,33626)),Promise.resolve().then(r.bind(r,26793)),Promise.resolve().then(r.bind(r,70697)),Promise.resolve().then(r.bind(r,92941)),Promise.resolve().then(r.t.bind(r,15889,23)),Promise.resolve().then(r.bind(r,62648))},47767:(e,t,r)=>{Promise.resolve().then(r.bind(r,26793)),Promise.resolve().then(r.bind(r,70697)),Promise.resolve().then(r.bind(r,92941)),Promise.resolve().then(r.t.bind(r,34080,23))},46227:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(72051),a=r(81413),i=r(98798),n=r(56886);r(26269);var l=r(86677);function o({children:e}){return(0,s.jsxs)(s.Fragment,{children:[s.jsx(l.Z,{isSeeker:!0}),s.jsx(i.Z,{}),s.jsx("div",{className:"w-full sticky top-0 z-10 bg-white !mt-0",children:s.jsx(n.Z,{currency_:"IDR",localeId:"en"})}),s.jsx("div",{className:"!mt-0 relative min-h-screen max-sm:max-w-screen-sm",children:e}),s.jsx("div",{className:"!mt-0",children:s.jsx(a.Z,{})})]})}},51239:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var s=r(72051),a=r(26269),i=r(69385),n=r(86449);let l=(0,n.Z)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),o=(0,n.Z)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]),c=(0,n.Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]),d=(0,n.Z)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);var u=r(26513),p=r(92349),m=r(79438);function x(){let e=(0,i.Z)("verify.success");return s.jsx(m.Z,{children:s.jsx("div",{className:"min-h-screen py-16 bg-gradient-to-br from-green-50 to-emerald-50",children:(0,s.jsxs)("div",{className:"max-w-2xl mx-auto text-center",children:[(0,s.jsxs)("div",{className:"mb-8",children:[s.jsx(l,{className:"w-20 h-20 text-green-500 mx-auto mb-4"}),s.jsx("h1",{className:"text-3xl md:text-4xl font-bold text-gray-900 mb-4",children:e("title")}),s.jsx("p",{className:"text-lg text-gray-600",children:e("subtitle")})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-8 mb-8 text-left",children:[s.jsx("h2",{className:"text-xl font-semibold text-gray-900 mb-6",children:e("details.title")}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[s.jsx(o,{className:"w-5 h-5 text-green-500 mt-0.5"}),(0,s.jsxs)("div",{children:[s.jsx("p",{className:"font-medium text-gray-900",children:e("details.confirmation")}),s.jsx("p",{className:"text-sm text-gray-600",children:e("details.confirmationDesc")})]})]}),(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[s.jsx(c,{className:"w-5 h-5 text-green-500 mt-0.5"}),(0,s.jsxs)("div",{children:[s.jsx("p",{className:"font-medium text-gray-900",children:e("details.scheduling")}),s.jsx("p",{className:"text-sm text-gray-600",children:e("details.schedulingDesc")})]})]}),(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[s.jsx(d,{className:"w-5 h-5 text-green-500 mt-0.5"}),(0,s.jsxs)("div",{children:[s.jsx("p",{className:"font-medium text-gray-900",children:e("details.contact")}),s.jsx("p",{className:"text-sm text-gray-600",children:e("details.contactDesc")})]})]})]})]}),(0,s.jsxs)("div",{className:"bg-blue-50 rounded-lg p-6 mb-8",children:[s.jsx("h3",{className:"text-lg font-semibold text-blue-900 mb-3",children:e("nextSteps.title")}),(0,s.jsxs)("ul",{className:"text-sm text-blue-800 space-y-2 text-left",children:[(0,s.jsxs)("li",{children:["• ",e("nextSteps.step1")]}),(0,s.jsxs)("li",{children:["• ",e("nextSteps.step2")]}),(0,s.jsxs)("li",{children:["• ",e("nextSteps.step3")]}),(0,s.jsxs)("li",{children:["• ",e("nextSteps.step4")]})]})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[s.jsx(u.z,{asChild:!0,className:"bg-seekers-primary hover:bg-seekers-primary/90",children:s.jsx(p.default,{href:"/",children:e("actions.backHome")})}),s.jsx(u.z,{variant:"outline",asChild:!0,children:s.jsx(p.default,{href:"/contact",children:e("actions.contactUs")})})]})]})})})}function h(){return s.jsx(a.Suspense,{fallback:s.jsx("div",{children:"Loading..."}),children:s.jsx(x,{})})}},26513:(e,t,r)=>{"use strict";r.d(t,{z:()=>d});var s=r(72051),a=r(26269),i=r(21322),n=r(29666),l=r(37170);let o=(0,r(86449).Z)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),c=(0,n.j)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-seekers-primary text-white shadow hover:bg-seekers-primary/90","default-seekers":"bg-seekers-primary text-white shadow hover:bg-seekers-primary-light/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input bg-background shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef(({className:e,variant:t,size:r,asChild:a=!1,loading:n=!1,...d},u)=>{let p=a?i.g7:"button";return s.jsx(p,{className:(0,l.cn)(c({variant:t,size:r,className:e})),ref:u,disabled:n||d.disabled,...d,children:n?s.jsx(o,{className:(0,l.cn)("h-4 w-4 animate-spin")}):d.children})});d.displayName="Button"},18714:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(28713);r(9640);var a=r(53020);async function i(e,t,r){let s=(0,a.cookies)(),i=s.get("tkn")?.value;try{let s=await fetch(e,{method:t,headers:{Authorization:`Bearer ${i}`,"Content-Type":"application/json"},...r});if(!s.ok)return{data:null,meta:void 0,error:{status:s.status,name:s.statusText,message:await s.text()||"Unexpected error",details:{}}};let a=await s.json();if(a.error)return{data:null,meta:void 0,error:a.error};return{data:a.data,meta:a.meta,error:void 0}}catch(e){return{data:null,meta:void 0,error:{status:500,details:{cause:e.cause},message:e.message,name:e.name}}}}(0,r(83557).h)([i]),(0,s.j)("ace39bf07124e0ba39e8486050a53bc79b0621b3",i)},92349:(e,t,r)=>{"use strict";r.d(t,{default:()=>a.a});var s=r(53160),a=r.n(s)},53160:(e,t,r)=>{"use strict";let{createProxy:s}=r(45347);e.exports=s("C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\node_modules\\next\\dist\\client\\link.js")},21322:(e,t,r)=>{"use strict";r.d(t,{g7:()=>n});var s=r(26269);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}var i=r(72051),n=s.forwardRef((e,t)=>{let{children:r,...a}=e,n=s.Children.toArray(r),o=n.find(c);if(o){let e=o.props.children,r=n.map(t=>t!==o?t:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,i.jsx)(l,{...a,ref:t,children:s.isValidElement(e)?s.cloneElement(e,void 0,r):null})}return(0,i.jsx)(l,{...a,ref:t,children:r})});n.displayName="Slot";var l=s.forwardRef((e,t)=>{let{children:r,...i}=e;if(s.isValidElement(r)){let e=function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(r);return s.cloneElement(r,{...function(e,t){let r={...t};for(let s in t){let a=e[s],i=t[s];/^on[A-Z]/.test(s)?a&&i?r[s]=(...e)=>{i(...e),a(...e)}:a&&(r[s]=a):"style"===s?r[s]={...a,...i}:"className"===s&&(r[s]=[a,i].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props),ref:t?function(...e){return t=>{let r=!1,s=e.map(e=>{let s=a(e,t);return r||"function"!=typeof s||(r=!0),s});if(r)return()=>{for(let t=0;t<s.length;t++){let r=s[t];"function"==typeof r?r():a(e[t],null)}}}}(t,e):e})}return s.Children.count(r)>1?s.Children.only(null):null});l.displayName="SlotClone";var o=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});function c(e){return s.isValidElement(e)&&e.type===o}},29666:(e,t,r)=>{"use strict";r.d(t,{j:()=>n});var s=r(36272);let a=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,i=s.W,n=(e,t)=>r=>{var s;if((null==t?void 0:t.variants)==null)return i(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:n,defaultVariants:l}=t,o=Object.keys(n).map(e=>{let t=null==r?void 0:r[e],s=null==l?void 0:l[e];if(null===t)return null;let i=a(t)||a(s);return n[e][i]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,s]=t;return void 0===s||(e[r]=s),e},{});return i(e,o,null==t?void 0:null===(s=t.compoundVariants)||void 0===s?void 0:s.reduce((e,t)=>{let{class:r,className:s,...a}=t;return Object.entries(a).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...l,...c}[t]):({...l,...c})[t]===r})?[...e,r,s]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[9379,5063,4916,9467,4859,5268,3327,8530,6666,9965,595],()=>r(78897));module.exports=s})();