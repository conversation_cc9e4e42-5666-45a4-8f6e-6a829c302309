"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8618],{32660:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(79205).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},17580:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(79205).Z)("ArrowUpRight",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]])},32489:function(e,t,r){r.d(t,{Z:function(){return n}});let n=(0,r(79205).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},49988:function(e,t,r){r.d(t,{g:function(){return n}});function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}},97867:function(e,t,r){r.d(t,{default:function(){return u}});var n=r(49988),o=r(27648),l=r(99376),i=r(2265),a=r(48706),u=(0,i.forwardRef)(function(e,t){let{defaultLocale:r,href:u,locale:c,localeCookie:s,onClick:d,prefetch:f,unprefixed:p,...h}=e,v=(0,a.Z)(),m=c!==v,w=c||v,g=function(){let[e,t]=(0,i.useState)();return(0,i.useEffect)(()=>{t(window.location.host)},[]),e}(),b=g&&p&&(p.domains[g]===w||!Object.keys(p.domains).includes(g)&&v===r&&!c)?p.pathname:u,y=(0,l.usePathname)();return m&&(f&&console.error("The `prefetch` prop is currently not supported when using the `locale` prop on `Link` to switch the locale.`"),f=!1),i.createElement(o.default,(0,n.g)({ref:t,href:b,hrefLang:m?c:void 0,onClick:function(e){(function(e,t,r,n){if(!e||!(n!==r&&null!=n)||!t)return;let o=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window.location.pathname;return"/"===e?t:t.replace(e,"")}(t),{name:l,...i}=e;i.path||(i.path=""!==o?o:"/");let a="".concat(l,"=").concat(n,";");for(let[e,t]of Object.entries(i))a+="".concat("maxAge"===e?"max-age":e),"boolean"!=typeof t&&(a+="="+t),a+=";";document.cookie=a})(s,y,v,c),d&&d(e)},prefetch:f},h))})},31085:function(e,t,r){r.d(t,{default:function(){return d}});var n=r(49988),o=r(99376),l=r(2265),i=r(48706);function a(e){return("object"==typeof e?null==e.host&&null==e.hostname:!/^[a-z]+:/i.test(e))&&!function(e){let t="object"==typeof e?e.pathname:e;return null!=t&&!t.startsWith("/")}(e)}function u(e,t){let r;return"string"==typeof e?r=c(t,e):(r={...e},e.pathname&&(r.pathname=c(t,e.pathname))),r}function c(e,t){let r=e;return/^\/(\?.*)?$/.test(t)&&(t=t.slice(1)),r+=t}r(25566);var s=r(97867);let d=(0,l.forwardRef)(function(e,t){let{href:r,locale:c,localeCookie:d,localePrefixMode:f,prefix:p,...h}=e,v=(0,o.usePathname)(),m=(0,i.Z)(),w=c!==m,[g,b]=(0,l.useState)(()=>a(r)&&("never"!==f||w)?u(r,p):r);return(0,l.useEffect)(()=>{v&&b(function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t,n=arguments.length>3?arguments[3]:void 0,o=arguments.length>4?arguments[4]:void 0;if(!a(e))return e;let l=n===o||n.startsWith("".concat(o,"/"));return(t!==r||l)&&null!=o?u(e,o):e}(r,c,m,v,p))},[m,r,c,v,p]),l.createElement(s.default,(0,n.g)({ref:t,href:g,locale:c,localeCookie:d},h))});d.displayName="ClientLink"},48706:function(e,t,r){r.d(t,{Z:function(){return i}});var n=r(99376),o=r(526);let l="locale";function i(){let e;let t=(0,n.useParams)();try{e=(0,o.useLocale)()}catch(r){if("string"!=typeof(null==t?void 0:t[l]))throw r;e=t[l]}return e}},70650:function(e,t,r){r.d(t,{Ee:function(){return y},F$:function(){return m},NY:function(){return S},fC:function(){return b}});var n=r(2265),o=r(73966),l=r(26606),i=r(61188),a=r(82912),u=r(57437),c="Avatar",[s,d]=(0,o.b)(c),[f,p]=s(c),h=n.forwardRef((e,t)=>{let{__scopeAvatar:r,...o}=e,[l,i]=n.useState("idle");return(0,u.jsx)(f,{scope:r,imageLoadingStatus:l,onImageLoadingStatusChange:i,children:(0,u.jsx)(a.WV.span,{...o,ref:t})})});h.displayName=c;var v="AvatarImage",m=n.forwardRef((e,t)=>{let{__scopeAvatar:r,src:o,onLoadingStatusChange:c=()=>{},...s}=e,d=p(v,r),f=function(e){let[t,r]=n.useState("idle");return(0,i.b)(()=>{if(!e){r("error");return}let t=!0,n=new window.Image,o=e=>()=>{t&&r(e)};return r("loading"),n.onload=o("loaded"),n.onerror=o("error"),n.src=e,()=>{t=!1}},[e]),t}(o),h=(0,l.W)(e=>{c(e),d.onImageLoadingStatusChange(e)});return(0,i.b)(()=>{"idle"!==f&&h(f)},[f,h]),"loaded"===f?(0,u.jsx)(a.WV.img,{...s,ref:t,src:o}):null});m.displayName=v;var w="AvatarFallback",g=n.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:o,...l}=e,i=p(w,r),[c,s]=n.useState(void 0===o);return n.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>s(!0),o);return()=>window.clearTimeout(e)}},[o]),c&&"loaded"!==i.imageLoadingStatus?(0,u.jsx)(a.WV.span,{...l,ref:t}):null});g.displayName=w;var b=h,y=m,S=g},73966:function(e,t,r){r.d(t,{b:function(){return l}});var n=r(2265),o=r(57437);function l(e,t=[]){let r=[],l=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return l.scopeName=e,[function(t,l){let i=n.createContext(l),a=r.length;function u(t){let{scope:r,children:l,...u}=t,c=r?.[e][a]||i,s=n.useMemo(()=>u,Object.values(u));return(0,o.jsx)(c.Provider,{value:s,children:l})}return r=[...r,l],u.displayName=t+"Provider",[u,function(r,o){let u=o?.[e][a]||i,c=n.useContext(u);if(c)return c;if(void 0!==l)return l;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(l,...t)]}},18756:function(e,t,r){r.d(t,{Ns:function(){return q},fC:function(){return Z},gb:function(){return E},q4:function(){return W},l_:function(){return B}});var n=r(2265),o=r(82912),l=r(98575),i=r(61188),a=e=>{var t,r;let o,a;let{present:c,children:s}=e,d=function(e){var t,r;let[o,l]=n.useState(),a=n.useRef({}),c=n.useRef(e),s=n.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return n.useEffect(()=>{let e=u(a.current);s.current="mounted"===d?e:"none"},[d]),(0,i.b)(()=>{let t=a.current,r=c.current;if(r!==e){let n=s.current,o=u(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):r&&n!==o?f("ANIMATION_OUT"):f("UNMOUNT"),c.current=e}},[e,f]),(0,i.b)(()=>{if(o){var e;let t;let r=null!==(e=o.ownerDocument.defaultView)&&void 0!==e?e:window,n=e=>{let n=u(a.current).includes(e.animationName);if(e.target===o&&n&&(f("ANIMATION_END"),!c.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},l=e=>{e.target===o&&(s.current=u(a.current))};return o.addEventListener("animationstart",l),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{r.clearTimeout(t),o.removeEventListener("animationstart",l),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:n.useCallback(e=>{e&&(a.current=getComputedStyle(e)),l(e)},[])}}(c),f="function"==typeof s?s({present:d.isPresent}):n.Children.only(s),p=(0,l.e)(d.ref,(o=null===(t=Object.getOwnPropertyDescriptor(f.props,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in o&&o.isReactWarning?f.ref:(o=null===(r=Object.getOwnPropertyDescriptor(f,"ref"))||void 0===r?void 0:r.get)&&"isReactWarning"in o&&o.isReactWarning?f.props.ref:f.props.ref||f.ref);return"function"==typeof s||d.isPresent?n.cloneElement(f,{ref:p}):null};function u(e){return(null==e?void 0:e.animationName)||"none"}a.displayName="Presence";var c=r(57437),s=r(26606),d=r(29114),f=r(62484),p=r(6741),h="ScrollArea",[v,m]=function(e,t=[]){let r=[],o=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return o.scopeName=e,[function(t,o){let l=n.createContext(o),i=r.length;r=[...r,o];let a=t=>{let{scope:r,children:o,...a}=t,u=r?.[e]?.[i]||l,s=n.useMemo(()=>a,Object.values(a));return(0,c.jsx)(u.Provider,{value:s,children:o})};return a.displayName=t+"Provider",[a,function(r,a){let u=a?.[e]?.[i]||l,c=n.useContext(u);if(c)return c;if(void 0!==o)return o;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(o,...t)]}(h),[w,g]=v(h),b=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,type:i="hover",dir:a,scrollHideDelay:u=600,...s}=e,[f,p]=n.useState(null),[h,v]=n.useState(null),[m,g]=n.useState(null),[b,y]=n.useState(null),[S,x]=n.useState(null),[E,C]=n.useState(0),[N,T]=n.useState(0),[R,L]=n.useState(!1),[P,_]=n.useState(!1),j=(0,l.e)(t,e=>p(e)),A=(0,d.gm)(a);return(0,c.jsx)(w,{scope:r,type:i,dir:A,scrollHideDelay:u,scrollArea:f,viewport:h,onViewportChange:v,content:m,onContentChange:g,scrollbarX:b,onScrollbarXChange:y,scrollbarXEnabled:R,onScrollbarXEnabledChange:L,scrollbarY:S,onScrollbarYChange:x,scrollbarYEnabled:P,onScrollbarYEnabledChange:_,onCornerWidthChange:C,onCornerHeightChange:T,children:(0,c.jsx)(o.WV.div,{dir:A,...s,ref:j,style:{position:"relative","--radix-scroll-area-corner-width":E+"px","--radix-scroll-area-corner-height":N+"px",...e.style}})})});b.displayName=h;var y="ScrollAreaViewport",S=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,children:i,asChild:a,nonce:u,...s}=e,d=g(y,r),f=n.useRef(null),p=(0,l.e)(t,f,d.onViewportChange);return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("style",{dangerouslySetInnerHTML:{__html:"\n[data-radix-scroll-area-viewport] {\n  scrollbar-width: none;\n  -ms-overflow-style: none;\n  -webkit-overflow-scrolling: touch;\n}\n[data-radix-scroll-area-viewport]::-webkit-scrollbar {\n  display: none;\n}\n:where([data-radix-scroll-area-viewport]) {\n  display: flex;\n  flex-direction: column;\n  align-items: stretch;\n}\n:where([data-radix-scroll-area-content]) {\n  flex-grow: 1;\n}\n"},nonce:u}),(0,c.jsx)(o.WV.div,{"data-radix-scroll-area-viewport":"",...s,asChild:a,ref:p,style:{overflowX:d.scrollbarXEnabled?"scroll":"hidden",overflowY:d.scrollbarYEnabled?"scroll":"hidden",...e.style},children:function(e,t){let{asChild:r,children:o}=e;if(!r)return"function"==typeof t?t(o):t;let l=n.Children.only(o);return n.cloneElement(l,{children:"function"==typeof t?t(l.props.children):t})}({asChild:a,children:i},e=>(0,c.jsx)("div",{"data-radix-scroll-area-content":"",ref:d.onContentChange,style:{minWidth:d.scrollbarXEnabled?"fit-content":void 0},children:e}))})]})});S.displayName=y;var x="ScrollAreaScrollbar",E=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,l=g(x,e.__scopeScrollArea),{onScrollbarXEnabledChange:i,onScrollbarYEnabledChange:a}=l,u="horizontal"===e.orientation;return n.useEffect(()=>(u?i(!0):a(!0),()=>{u?i(!1):a(!1)}),[u,i,a]),"hover"===l.type?(0,c.jsx)(C,{...o,ref:t,forceMount:r}):"scroll"===l.type?(0,c.jsx)(N,{...o,ref:t,forceMount:r}):"auto"===l.type?(0,c.jsx)(T,{...o,ref:t,forceMount:r}):"always"===l.type?(0,c.jsx)(R,{...o,ref:t}):null});E.displayName=x;var C=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,l=g(x,e.__scopeScrollArea),[i,u]=n.useState(!1);return n.useEffect(()=>{let e=l.scrollArea,t=0;if(e){let r=()=>{window.clearTimeout(t),u(!0)},n=()=>{t=window.setTimeout(()=>u(!1),l.scrollHideDelay)};return e.addEventListener("pointerenter",r),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",r),e.removeEventListener("pointerleave",n)}}},[l.scrollArea,l.scrollHideDelay]),(0,c.jsx)(a,{present:r||i,children:(0,c.jsx)(T,{"data-state":i?"visible":"hidden",...o,ref:t})})}),N=n.forwardRef((e,t)=>{var r,o;let{forceMount:l,...i}=e,u=g(x,e.__scopeScrollArea),s="horizontal"===e.orientation,d=Y(()=>h("SCROLL_END"),100),[f,h]=(r="hidden",o={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},n.useReducer((e,t)=>{let r=o[e][t];return null!=r?r:e},r));return n.useEffect(()=>{if("idle"===f){let e=window.setTimeout(()=>h("HIDE"),u.scrollHideDelay);return()=>window.clearTimeout(e)}},[f,u.scrollHideDelay,h]),n.useEffect(()=>{let e=u.viewport,t=s?"scrollLeft":"scrollTop";if(e){let r=e[t],n=()=>{let n=e[t];r!==n&&(h("SCROLL"),d()),r=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[u.viewport,s,h,d]),(0,c.jsx)(a,{present:l||"hidden"!==f,children:(0,c.jsx)(R,{"data-state":"hidden"===f?"hidden":"visible",...i,ref:t,onPointerEnter:(0,p.M)(e.onPointerEnter,()=>h("POINTER_ENTER")),onPointerLeave:(0,p.M)(e.onPointerLeave,()=>h("POINTER_LEAVE"))})})}),T=n.forwardRef((e,t)=>{let r=g(x,e.__scopeScrollArea),{forceMount:o,...l}=e,[i,u]=n.useState(!1),s="horizontal"===e.orientation,d=Y(()=>{if(r.viewport){let e=r.viewport.offsetWidth<r.viewport.scrollWidth,t=r.viewport.offsetHeight<r.viewport.scrollHeight;u(s?e:t)}},10);return $(r.viewport,d),$(r.content,d),(0,c.jsx)(a,{present:o||i,children:(0,c.jsx)(R,{"data-state":i?"visible":"hidden",...l,ref:t})})}),R=n.forwardRef((e,t)=>{let{orientation:r="vertical",...o}=e,l=g(x,e.__scopeScrollArea),i=n.useRef(null),a=n.useRef(0),[u,s]=n.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),d=z(u.viewport,u.content),f={...o,sizes:u,onSizesChange:s,hasThumb:!!(d>0&&d<1),onThumbChange:e=>i.current=e,onThumbPointerUp:()=>a.current=0,onThumbPointerDown:e=>a.current=e};function p(e,t){return function(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"ltr",o=H(r),l=t||o/2,i=r.scrollbar.paddingStart+l,a=r.scrollbar.size-r.scrollbar.paddingEnd-(o-l),u=r.content-r.viewport;return X([i,a],"ltr"===n?[0,u]:[-1*u,0])(e)}(e,a.current,u,t)}return"horizontal"===r?(0,c.jsx)(L,{...f,ref:t,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=V(l.viewport.scrollLeft,u,l.dir);i.current.style.transform="translate3d(".concat(e,"px, 0, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollLeft=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollLeft=p(e,l.dir))}}):"vertical"===r?(0,c.jsx)(P,{...f,ref:t,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=V(l.viewport.scrollTop,u);i.current.style.transform="translate3d(0, ".concat(e,"px, 0)")}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollTop=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollTop=p(e))}}):null}),L=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...i}=e,a=g(x,e.__scopeScrollArea),[u,s]=n.useState(),d=n.useRef(null),f=(0,l.e)(t,d,a.onScrollbarXChange);return n.useEffect(()=>{d.current&&s(getComputedStyle(d.current))},[d]),(0,c.jsx)(A,{"data-orientation":"horizontal",...i,ref:f,sizes:r,style:{bottom:0,left:"rtl"===a.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===a.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":H(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,r)=>{if(a.viewport){let n=a.viewport.scrollLeft+t.deltaX;e.onWheelScroll(n),n>0&&n<r&&t.preventDefault()}},onResize:()=>{d.current&&a.viewport&&u&&o({content:a.viewport.scrollWidth,viewport:a.viewport.offsetWidth,scrollbar:{size:d.current.clientWidth,paddingStart:U(u.paddingLeft),paddingEnd:U(u.paddingRight)}})}})}),P=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...i}=e,a=g(x,e.__scopeScrollArea),[u,s]=n.useState(),d=n.useRef(null),f=(0,l.e)(t,d,a.onScrollbarYChange);return n.useEffect(()=>{d.current&&s(getComputedStyle(d.current))},[d]),(0,c.jsx)(A,{"data-orientation":"vertical",...i,ref:f,sizes:r,style:{top:0,right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":H(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,r)=>{if(a.viewport){let n=a.viewport.scrollTop+t.deltaY;e.onWheelScroll(n),n>0&&n<r&&t.preventDefault()}},onResize:()=>{d.current&&a.viewport&&u&&o({content:a.viewport.scrollHeight,viewport:a.viewport.offsetHeight,scrollbar:{size:d.current.clientHeight,paddingStart:U(u.paddingTop),paddingEnd:U(u.paddingBottom)}})}})}),[_,j]=v(x),A=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,sizes:i,hasThumb:a,onThumbChange:u,onThumbPointerUp:d,onThumbPointerDown:f,onThumbPositionChange:h,onDragScroll:v,onWheelScroll:m,onResize:w,...b}=e,y=g(x,r),[S,E]=n.useState(null),C=(0,l.e)(t,e=>E(e)),N=n.useRef(null),T=n.useRef(""),R=y.viewport,L=i.content-i.viewport,P=(0,s.W)(m),j=(0,s.W)(h),A=Y(w,10);function M(e){N.current&&v({x:e.clientX-N.current.left,y:e.clientY-N.current.top})}return n.useEffect(()=>{let e=e=>{let t=e.target;(null==S?void 0:S.contains(t))&&P(e,L)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[R,S,L,P]),n.useEffect(j,[i,j]),$(S,A),$(y.content,A),(0,c.jsx)(_,{scope:r,scrollbar:S,hasThumb:a,onThumbChange:(0,s.W)(u),onThumbPointerUp:(0,s.W)(d),onThumbPositionChange:j,onThumbPointerDown:(0,s.W)(f),children:(0,c.jsx)(o.WV.div,{...b,ref:C,style:{position:"absolute",...b.style},onPointerDown:(0,p.M)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),N.current=S.getBoundingClientRect(),T.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",y.viewport&&(y.viewport.style.scrollBehavior="auto"),M(e))}),onPointerMove:(0,p.M)(e.onPointerMove,M),onPointerUp:(0,p.M)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=T.current,y.viewport&&(y.viewport.style.scrollBehavior=""),N.current=null})})})}),M="ScrollAreaThumb",W=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,o=j(M,e.__scopeScrollArea);return(0,c.jsx)(a,{present:r||o.hasThumb,children:(0,c.jsx)(O,{ref:t,...n})})}),O=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,style:i,...a}=e,u=g(M,r),s=j(M,r),{onThumbPositionChange:d}=s,f=(0,l.e)(t,e=>s.onThumbChange(e)),h=n.useRef(),v=Y(()=>{h.current&&(h.current(),h.current=void 0)},100);return n.useEffect(()=>{let e=u.viewport;if(e){let t=()=>{if(v(),!h.current){let t=F(e,d);h.current=t,d()}};return d(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[u.viewport,v,d]),(0,c.jsx)(o.WV.div,{"data-state":s.hasThumb?"visible":"hidden",...a,ref:f,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...i},onPointerDownCapture:(0,p.M)(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),r=e.clientX-t.left,n=e.clientY-t.top;s.onThumbPointerDown({x:r,y:n})}),onPointerUp:(0,p.M)(e.onPointerUp,s.onThumbPointerUp)})});W.displayName=M;var D="ScrollAreaCorner",k=n.forwardRef((e,t)=>{let r=g(D,e.__scopeScrollArea),n=!!(r.scrollbarX&&r.scrollbarY);return"scroll"!==r.type&&n?(0,c.jsx)(I,{...e,ref:t}):null});k.displayName=D;var I=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,...l}=e,i=g(D,r),[a,u]=n.useState(0),[s,d]=n.useState(0),f=!!(a&&s);return $(i.scrollbarX,()=>{var e;let t=(null===(e=i.scrollbarX)||void 0===e?void 0:e.offsetHeight)||0;i.onCornerHeightChange(t),d(t)}),$(i.scrollbarY,()=>{var e;let t=(null===(e=i.scrollbarY)||void 0===e?void 0:e.offsetWidth)||0;i.onCornerWidthChange(t),u(t)}),f?(0,c.jsx)(o.WV.div,{...l,ref:t,style:{width:a,height:s,position:"absolute",right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:0,...e.style}}):null});function U(e){return e?parseInt(e,10):0}function z(e,t){let r=e/t;return isNaN(r)?0:r}function H(e){let t=z(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-r)*t,18)}function V(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",n=H(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,l=t.scrollbar.size-o,i=t.content-t.viewport,a=(0,f.u)(e,"ltr"===r?[0,i]:[-1*i,0]);return X([0,i],[0,l-n])(a)}function X(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}var F=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:()=>{},r={left:e.scrollLeft,top:e.scrollTop},n=0;return!function o(){let l={left:e.scrollLeft,top:e.scrollTop},i=r.left!==l.left,a=r.top!==l.top;(i||a)&&t(),r=l,n=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(n)};function Y(e,t){let r=(0,s.W)(e),o=n.useRef(0);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),n.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(r,t)},[r,t])}function $(e,t){let r=(0,s.W)(t);(0,i.b)(()=>{let t=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(r)});return n.observe(e),()=>{window.cancelAnimationFrame(t),n.unobserve(e)}}},[e,r])}var Z=b,B=S,q=k}}]);