"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6868],{75189:function(e,r,n){let t,o,a;var u=Object.create,l=Object.defineProperty,i=Object.defineProperties,c=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyDescriptors,d=Object.getOwnPropertyNames,f=Object.getOwnPropertySymbols,p=Object.getPrototypeOf,v=Object.prototype.hasOwnProperty,m=Object.prototype.propertyIsEnumerable,g=(e,r,n)=>r in e?l(e,r,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[r]=n,h=(e,r)=>{for(var n in r||(r={}))v.call(r,n)&&g(e,n,r[n]);if(f)for(var n of f(r))m.call(r,n)&&g(e,n,r[n]);return e},w=(e,r)=>i(e,s(r)),y=(e,r,n,t)=>{if(r&&"object"==typeof r||"function"==typeof r)for(let o of d(r))v.call(e,o)||o===n||l(e,o,{get:()=>r[o],enumerable:!(t=c(r,o))||t.enumerable});return e},x={};((e,r)=>{for(var n in r)l(e,n,{get:r[n],enumerable:!0})})(x,{useRouter:()=>R}),e.exports=y(l({},"__esModule",{value:!0}),x);var M=n(99376),b=n(2265),C=(a=null!=(t=n(71318))?u(p(t)):{},y(!o&&t&&t.__esModule?a:l(a,"default",{value:t,enumerable:!0}),t)),R=l(()=>{let e=(0,M.useRouter)(),r=(0,M.usePathname)();(0,b.useEffect)(()=>{C.done()},[r]);let n=(0,b.useCallback)((n,t)=>{n!==r&&C.start(),e.replace(n,t)},[e,r]),t=(0,b.useCallback)((n,t)=>{n!==r&&C.start(),e.push(n,t)},[e,r]);return w(h({},e),{replace:n,push:t})},"name",{value:"useRouter",configurable:!0})},28119:function(e,r,n){n.d(r,{oC:function(){return e4},VY:function(){return e8},$F:function(){return eV},ZA:function(){return e2},ck:function(){return e3},wU:function(){return rn},__:function(){return e9},Uv:function(){return e7},Ee:function(){return re},Rk:function(){return rr},fC:function(){return e5},Z0:function(){return rt},Tr:function(){return ro},tu:function(){return ru},fF:function(){return ra},xz:function(){return e6}});var t=n(2265),o=n(6741),a=n(98575),u=n(73966),l=n(80886),i=n(82912),c=n(71605),s=n(29114),d=n(22308),f=n(86097),p=n(99103),v=n(99255),m=n(21107),g=n(83832),h=n(71599),w=n(1353),y=n(57437),x=t.forwardRef((e,r)=>{let{children:n,...o}=e,a=t.Children.toArray(n),u=a.find(C);if(u){let e=u.props.children,n=a.map(r=>r!==u?r:t.Children.count(e)>1?t.Children.only(null):t.isValidElement(e)?e.props.children:null);return(0,y.jsx)(M,{...o,ref:r,children:t.isValidElement(e)?t.cloneElement(e,void 0,n):null})}return(0,y.jsx)(M,{...o,ref:r,children:n})});x.displayName="Slot";var M=t.forwardRef((e,r)=>{let{children:n,...o}=e;if(t.isValidElement(n)){let e,u;let l=(e=Object.getOwnPropertyDescriptor(n.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.ref:(e=Object.getOwnPropertyDescriptor(n,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?n.props.ref:n.props.ref||n.ref;return t.cloneElement(n,{...function(e,r){let n={...r};for(let t in r){let o=e[t],a=r[t];/^on[A-Z]/.test(t)?o&&a?n[t]=(...e)=>{a(...e),o(...e)}:o&&(n[t]=o):"style"===t?n[t]={...o,...a}:"className"===t&&(n[t]=[o,a].filter(Boolean).join(" "))}return{...e,...n}}(o,n.props),ref:r?(0,a.F)(r,l):l})}return t.Children.count(n)>1?t.Children.only(null):null});M.displayName="SlotClone";var b=({children:e})=>(0,y.jsx)(y.Fragment,{children:e});function C(e){return t.isValidElement(e)&&e.type===b}var R=n(26606),j=n(5478),D=n(60703),O=["Enter"," "],_=["ArrowUp","PageDown","End"],E=["ArrowDown","PageUp","Home",..._],P={ltr:[...O,"ArrowRight"],rtl:[...O,"ArrowLeft"]},N={ltr:["ArrowLeft"],rtl:["ArrowRight"]},I="Menu",[k,T,S]=(0,c.B)(I),[A,F]=(0,u.b)(I,[S,m.D7,w.Pc]),L=(0,m.D7)(),K=(0,w.Pc)(),[U,W]=A(I),[V,G]=A(I),B=e=>{let{__scopeMenu:r,open:n=!1,children:o,dir:a,onOpenChange:u,modal:l=!0}=e,i=L(r),[c,d]=t.useState(null),f=t.useRef(!1),p=(0,R.W)(u),v=(0,s.gm)(a);return t.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",r,{capture:!0,once:!0}),document.addEventListener("pointermove",r,{capture:!0,once:!0})},r=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",r,{capture:!0}),document.removeEventListener("pointermove",r,{capture:!0})}},[]),(0,y.jsx)(m.fC,{...i,children:(0,y.jsx)(U,{scope:r,open:n,onOpenChange:p,content:c,onContentChange:d,children:(0,y.jsx)(V,{scope:r,onClose:t.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:f,dir:v,modal:l,children:o})})})};B.displayName=I;var z=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e,o=L(n);return(0,y.jsx)(m.ee,{...o,...t,ref:r})});z.displayName="MenuAnchor";var X="MenuPortal",[Z,Y]=A(X,{forceMount:void 0}),H=e=>{let{__scopeMenu:r,forceMount:n,children:t,container:o}=e,a=W(X,r);return(0,y.jsx)(Z,{scope:r,forceMount:n,children:(0,y.jsx)(h.z,{present:n||a.open,children:(0,y.jsx)(g.h,{asChild:!0,container:o,children:t})})})};H.displayName=X;var $="MenuContent",[q,J]=A($),Q=t.forwardRef((e,r)=>{let n=Y($,e.__scopeMenu),{forceMount:t=n.forceMount,...o}=e,a=W($,e.__scopeMenu),u=G($,e.__scopeMenu);return(0,y.jsx)(k.Provider,{scope:e.__scopeMenu,children:(0,y.jsx)(h.z,{present:t||a.open,children:(0,y.jsx)(k.Slot,{scope:e.__scopeMenu,children:u.modal?(0,y.jsx)(ee,{...o,ref:r}):(0,y.jsx)(er,{...o,ref:r})})})})}),ee=t.forwardRef((e,r)=>{let n=W($,e.__scopeMenu),u=t.useRef(null),l=(0,a.e)(r,u);return t.useEffect(()=>{let e=u.current;if(e)return(0,j.Ry)(e)},[]),(0,y.jsx)(en,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),er=t.forwardRef((e,r)=>{let n=W($,e.__scopeMenu);return(0,y.jsx)(en,{...e,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),en=t.forwardRef((e,r)=>{let{__scopeMenu:n,loop:u=!1,trapFocus:l,onOpenAutoFocus:i,onCloseAutoFocus:c,disableOutsidePointerEvents:s,onEntryFocus:v,onEscapeKeyDown:g,onPointerDownOutside:h,onFocusOutside:M,onInteractOutside:b,onDismiss:C,disableOutsideScroll:R,...j}=e,O=W($,n),P=G($,n),N=L(n),I=K(n),k=T(n),[S,A]=t.useState(null),F=t.useRef(null),U=(0,a.e)(r,F,O.onContentChange),V=t.useRef(0),B=t.useRef(""),z=t.useRef(0),X=t.useRef(null),Z=t.useRef("right"),Y=t.useRef(0),H=R?D.Z:t.Fragment,J=e=>{var r,n;let t=B.current+e,o=k().filter(e=>!e.disabled),a=document.activeElement,u=null===(r=o.find(e=>e.ref.current===a))||void 0===r?void 0:r.textValue,l=function(e,r,n){var t;let o=r.length>1&&Array.from(r).every(e=>e===r[0])?r[0]:r,a=(t=Math.max(n?e.indexOf(n):-1,0),e.map((r,n)=>e[(t+n)%e.length]));1===o.length&&(a=a.filter(e=>e!==n));let u=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return u!==n?u:void 0}(o.map(e=>e.textValue),t,u),i=null===(n=o.find(e=>e.textValue===l))||void 0===n?void 0:n.ref.current;!function e(r){B.current=r,window.clearTimeout(V.current),""!==r&&(V.current=window.setTimeout(()=>e(""),1e3))}(t),i&&setTimeout(()=>i.focus())};t.useEffect(()=>()=>window.clearTimeout(V.current),[]),(0,f.EW)();let Q=t.useCallback(e=>{var r,n,t;return Z.current===(null===(r=X.current)||void 0===r?void 0:r.side)&&!!(t=null===(n=X.current)||void 0===n?void 0:n.area)&&function(e,r){let{x:n,y:t}=e,o=!1;for(let e=0,a=r.length-1;e<r.length;a=e++){let u=r[e].x,l=r[e].y,i=r[a].x,c=r[a].y;l>t!=c>t&&n<(i-u)*(t-l)/(c-l)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)},[]);return(0,y.jsx)(q,{scope:n,searchRef:B,onItemEnter:t.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),onItemLeave:t.useCallback(e=>{var r;Q(e)||(null===(r=F.current)||void 0===r||r.focus(),A(null))},[Q]),onTriggerLeave:t.useCallback(e=>{Q(e)&&e.preventDefault()},[Q]),pointerGraceTimerRef:z,onPointerGraceIntentChange:t.useCallback(e=>{X.current=e},[]),children:(0,y.jsx)(H,{...R?{as:x,allowPinchZoom:!0}:void 0,children:(0,y.jsx)(p.M,{asChild:!0,trapped:l,onMountAutoFocus:(0,o.M)(i,e=>{var r;e.preventDefault(),null===(r=F.current)||void 0===r||r.focus({preventScroll:!0})}),onUnmountAutoFocus:c,children:(0,y.jsx)(d.XB,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:g,onPointerDownOutside:h,onFocusOutside:M,onInteractOutside:b,onDismiss:C,children:(0,y.jsx)(w.fC,{asChild:!0,...I,dir:P.dir,orientation:"vertical",loop:u,currentTabStopId:S,onCurrentTabStopIdChange:A,onEntryFocus:(0,o.M)(v,e=>{P.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,y.jsx)(m.VY,{role:"menu","aria-orientation":"vertical","data-state":eP(O.open),"data-radix-menu-content":"",dir:P.dir,...N,...j,ref:U,style:{outline:"none",...j.style},onKeyDown:(0,o.M)(j.onKeyDown,e=>{let r=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,t=1===e.key.length;r&&("Tab"===e.key&&e.preventDefault(),!n&&t&&J(e.key));let o=F.current;if(e.target!==o||!E.includes(e.key))return;e.preventDefault();let a=k().filter(e=>!e.disabled).map(e=>e.ref.current);_.includes(e.key)&&a.reverse(),function(e){let r=document.activeElement;for(let n of e)if(n===r||(n.focus(),document.activeElement!==r))return}(a)}),onBlur:(0,o.M)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(V.current),B.current="")}),onPointerMove:(0,o.M)(e.onPointerMove,ek(e=>{let r=e.target,n=Y.current!==e.clientX;if(e.currentTarget.contains(r)&&n){let r=e.clientX>Y.current?"right":"left";Z.current=r,Y.current=e.clientX}}))})})})})})})});Q.displayName=$;var et=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,y.jsx)(i.WV.div,{role:"group",...t,ref:r})});et.displayName="MenuGroup";var eo=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,y.jsx)(i.WV.div,{...t,ref:r})});eo.displayName="MenuLabel";var ea="MenuItem",eu="menu.itemSelect",el=t.forwardRef((e,r)=>{let{disabled:n=!1,onSelect:u,...l}=e,c=t.useRef(null),s=G(ea,e.__scopeMenu),d=J(ea,e.__scopeMenu),f=(0,a.e)(r,c),p=t.useRef(!1);return(0,y.jsx)(ei,{...l,ref:f,disabled:n,onClick:(0,o.M)(e.onClick,()=>{let e=c.current;if(!n&&e){let r=new CustomEvent(eu,{bubbles:!0,cancelable:!0});e.addEventListener(eu,e=>null==u?void 0:u(e),{once:!0}),(0,i.jH)(e,r),r.defaultPrevented?p.current=!1:s.onClose()}}),onPointerDown:r=>{var n;null===(n=e.onPointerDown)||void 0===n||n.call(e,r),p.current=!0},onPointerUp:(0,o.M)(e.onPointerUp,e=>{var r;p.current||null===(r=e.currentTarget)||void 0===r||r.click()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let r=""!==d.searchRef.current;!n&&(!r||" "!==e.key)&&O.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});el.displayName=ea;var ei=t.forwardRef((e,r)=>{let{__scopeMenu:n,disabled:u=!1,textValue:l,...c}=e,s=J(ea,n),d=K(n),f=t.useRef(null),p=(0,a.e)(r,f),[v,m]=t.useState(!1),[g,h]=t.useState("");return t.useEffect(()=>{let e=f.current;if(e){var r;h((null!==(r=e.textContent)&&void 0!==r?r:"").trim())}},[c.children]),(0,y.jsx)(k.ItemSlot,{scope:n,disabled:u,textValue:null!=l?l:g,children:(0,y.jsx)(w.ck,{asChild:!0,...d,focusable:!u,children:(0,y.jsx)(i.WV.div,{role:"menuitem","data-highlighted":v?"":void 0,"aria-disabled":u||void 0,"data-disabled":u?"":void 0,...c,ref:p,onPointerMove:(0,o.M)(e.onPointerMove,ek(e=>{u?s.onItemLeave(e):(s.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.M)(e.onPointerLeave,ek(e=>s.onItemLeave(e))),onFocus:(0,o.M)(e.onFocus,()=>m(!0)),onBlur:(0,o.M)(e.onBlur,()=>m(!1))})})})}),ec=t.forwardRef((e,r)=>{let{checked:n=!1,onCheckedChange:t,...a}=e;return(0,y.jsx)(eh,{scope:e.__scopeMenu,checked:n,children:(0,y.jsx)(el,{role:"menuitemcheckbox","aria-checked":eN(n)?"mixed":n,...a,ref:r,"data-state":eI(n),onSelect:(0,o.M)(a.onSelect,()=>null==t?void 0:t(!!eN(n)||!n),{checkForDefaultPrevented:!1})})})});ec.displayName="MenuCheckboxItem";var es="MenuRadioGroup",[ed,ef]=A(es,{value:void 0,onValueChange:()=>{}}),ep=t.forwardRef((e,r)=>{let{value:n,onValueChange:t,...o}=e,a=(0,R.W)(t);return(0,y.jsx)(ed,{scope:e.__scopeMenu,value:n,onValueChange:a,children:(0,y.jsx)(et,{...o,ref:r})})});ep.displayName=es;var ev="MenuRadioItem",em=t.forwardRef((e,r)=>{let{value:n,...t}=e,a=ef(ev,e.__scopeMenu),u=n===a.value;return(0,y.jsx)(eh,{scope:e.__scopeMenu,checked:u,children:(0,y.jsx)(el,{role:"menuitemradio","aria-checked":u,...t,ref:r,"data-state":eI(u),onSelect:(0,o.M)(t.onSelect,()=>{var e;return null===(e=a.onValueChange)||void 0===e?void 0:e.call(a,n)},{checkForDefaultPrevented:!1})})})});em.displayName=ev;var eg="MenuItemIndicator",[eh,ew]=A(eg,{checked:!1}),ey=t.forwardRef((e,r)=>{let{__scopeMenu:n,forceMount:t,...o}=e,a=ew(eg,n);return(0,y.jsx)(h.z,{present:t||eN(a.checked)||!0===a.checked,children:(0,y.jsx)(i.WV.span,{...o,ref:r,"data-state":eI(a.checked)})})});ey.displayName=eg;var ex=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e;return(0,y.jsx)(i.WV.div,{role:"separator","aria-orientation":"horizontal",...t,ref:r})});ex.displayName="MenuSeparator";var eM=t.forwardRef((e,r)=>{let{__scopeMenu:n,...t}=e,o=L(n);return(0,y.jsx)(m.Eh,{...o,...t,ref:r})});eM.displayName="MenuArrow";var eb="MenuSub",[eC,eR]=A(eb),ej=e=>{let{__scopeMenu:r,children:n,open:o=!1,onOpenChange:a}=e,u=W(eb,r),l=L(r),[i,c]=t.useState(null),[s,d]=t.useState(null),f=(0,R.W)(a);return t.useEffect(()=>(!1===u.open&&f(!1),()=>f(!1)),[u.open,f]),(0,y.jsx)(m.fC,{...l,children:(0,y.jsx)(U,{scope:r,open:o,onOpenChange:f,content:s,onContentChange:d,children:(0,y.jsx)(eC,{scope:r,contentId:(0,v.M)(),triggerId:(0,v.M)(),trigger:i,onTriggerChange:c,children:n})})})};ej.displayName=eb;var eD="MenuSubTrigger",eO=t.forwardRef((e,r)=>{let n=W(eD,e.__scopeMenu),u=G(eD,e.__scopeMenu),l=eR(eD,e.__scopeMenu),i=J(eD,e.__scopeMenu),c=t.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:d}=i,f={__scopeMenu:e.__scopeMenu},p=t.useCallback(()=>{c.current&&window.clearTimeout(c.current),c.current=null},[]);return t.useEffect(()=>p,[p]),t.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),d(null)}},[s,d]),(0,y.jsx)(z,{asChild:!0,...f,children:(0,y.jsx)(ei,{id:l.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":l.contentId,"data-state":eP(n.open),...e,ref:(0,a.F)(r,l.onTriggerChange),onClick:r=>{var t;null===(t=e.onClick)||void 0===t||t.call(e,r),e.disabled||r.defaultPrevented||(r.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.M)(e.onPointerMove,ek(r=>{i.onItemEnter(r),r.defaultPrevented||e.disabled||n.open||c.current||(i.onPointerGraceIntentChange(null),c.current=window.setTimeout(()=>{n.onOpenChange(!0),p()},100))})),onPointerLeave:(0,o.M)(e.onPointerLeave,ek(e=>{var r,t;p();let o=null===(r=n.content)||void 0===r?void 0:r.getBoundingClientRect();if(o){let r=null===(t=n.content)||void 0===t?void 0:t.dataset.side,a="right"===r,u=o[a?"left":"right"],l=o[a?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(a?-5:5),y:e.clientY},{x:u,y:o.top},{x:l,y:o.top},{x:l,y:o.bottom},{x:u,y:o.bottom}],side:r}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.M)(e.onKeyDown,r=>{let t=""!==i.searchRef.current;if(!e.disabled&&(!t||" "!==r.key)&&P[u.dir].includes(r.key)){var o;n.onOpenChange(!0),null===(o=n.content)||void 0===o||o.focus(),r.preventDefault()}})})})});eO.displayName=eD;var e_="MenuSubContent",eE=t.forwardRef((e,r)=>{let n=Y($,e.__scopeMenu),{forceMount:u=n.forceMount,...l}=e,i=W($,e.__scopeMenu),c=G($,e.__scopeMenu),s=eR(e_,e.__scopeMenu),d=t.useRef(null),f=(0,a.e)(r,d);return(0,y.jsx)(k.Provider,{scope:e.__scopeMenu,children:(0,y.jsx)(h.z,{present:u||i.open,children:(0,y.jsx)(k.Slot,{scope:e.__scopeMenu,children:(0,y.jsx)(en,{id:s.contentId,"aria-labelledby":s.triggerId,...l,ref:f,align:"start",side:"rtl"===c.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var r;c.isUsingKeyboardRef.current&&(null===(r=d.current)||void 0===r||r.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>{e.target!==s.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:(0,o.M)(e.onEscapeKeyDown,e=>{c.onClose(),e.preventDefault()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let r=e.currentTarget.contains(e.target),n=N[c.dir].includes(e.key);if(r&&n){var t;i.onOpenChange(!1),null===(t=s.trigger)||void 0===t||t.focus(),e.preventDefault()}})})})})})});function eP(e){return e?"open":"closed"}function eN(e){return"indeterminate"===e}function eI(e){return eN(e)?"indeterminate":e?"checked":"unchecked"}function ek(e){return r=>"mouse"===r.pointerType?e(r):void 0}eE.displayName=e_;var eT="DropdownMenu",[eS,eA]=(0,u.b)(eT,[F]),eF=F(),[eL,eK]=eS(eT),eU=e=>{let{__scopeDropdownMenu:r,children:n,dir:o,open:a,defaultOpen:u,onOpenChange:i,modal:c=!0}=e,s=eF(r),d=t.useRef(null),[f=!1,p]=(0,l.T)({prop:a,defaultProp:u,onChange:i});return(0,y.jsx)(eL,{scope:r,triggerId:(0,v.M)(),triggerRef:d,contentId:(0,v.M)(),open:f,onOpenChange:p,onOpenToggle:t.useCallback(()=>p(e=>!e),[p]),modal:c,children:(0,y.jsx)(B,{...s,open:f,onOpenChange:p,dir:o,modal:c,children:n})})};eU.displayName=eT;var eW="DropdownMenuTrigger",eV=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,disabled:t=!1,...u}=e,l=eK(eW,n),c=eF(n);return(0,y.jsx)(z,{asChild:!0,...c,children:(0,y.jsx)(i.WV.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":t?"":void 0,disabled:t,...u,ref:(0,a.F)(r,l.triggerRef),onPointerDown:(0,o.M)(e.onPointerDown,e=>{t||0!==e.button||!1!==e.ctrlKey||(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{!t&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eV.displayName=eW;var eG=e=>{let{__scopeDropdownMenu:r,...n}=e,t=eF(r);return(0,y.jsx)(H,{...t,...n})};eG.displayName="DropdownMenuPortal";var eB="DropdownMenuContent",ez=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...a}=e,u=eK(eB,n),l=eF(n),i=t.useRef(!1);return(0,y.jsx)(Q,{id:u.contentId,"aria-labelledby":u.triggerId,...l,...a,ref:r,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{var r;i.current||null===(r=u.triggerRef.current)||void 0===r||r.focus(),i.current=!1,e.preventDefault()}),onInteractOutside:(0,o.M)(e.onInteractOutside,e=>{let r=e.detail.originalEvent,n=0===r.button&&!0===r.ctrlKey,t=2===r.button||n;(!u.modal||t)&&(i.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});ez.displayName=eB;var eX=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eF(n);return(0,y.jsx)(et,{...o,...t,ref:r})});eX.displayName="DropdownMenuGroup";var eZ=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eF(n);return(0,y.jsx)(eo,{...o,...t,ref:r})});eZ.displayName="DropdownMenuLabel";var eY=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eF(n);return(0,y.jsx)(el,{...o,...t,ref:r})});eY.displayName="DropdownMenuItem";var eH=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eF(n);return(0,y.jsx)(ec,{...o,...t,ref:r})});eH.displayName="DropdownMenuCheckboxItem";var e$=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eF(n);return(0,y.jsx)(ep,{...o,...t,ref:r})});e$.displayName="DropdownMenuRadioGroup";var eq=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eF(n);return(0,y.jsx)(em,{...o,...t,ref:r})});eq.displayName="DropdownMenuRadioItem";var eJ=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eF(n);return(0,y.jsx)(ey,{...o,...t,ref:r})});eJ.displayName="DropdownMenuItemIndicator";var eQ=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eF(n);return(0,y.jsx)(ex,{...o,...t,ref:r})});eQ.displayName="DropdownMenuSeparator",t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eF(n);return(0,y.jsx)(eM,{...o,...t,ref:r})}).displayName="DropdownMenuArrow";var e0=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eF(n);return(0,y.jsx)(eO,{...o,...t,ref:r})});e0.displayName="DropdownMenuSubTrigger";var e1=t.forwardRef((e,r)=>{let{__scopeDropdownMenu:n,...t}=e,o=eF(n);return(0,y.jsx)(eE,{...o,...t,ref:r,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});e1.displayName="DropdownMenuSubContent";var e5=eU,e6=eV,e7=eG,e8=ez,e2=eX,e9=eZ,e3=eY,e4=eH,re=e$,rr=eq,rn=eJ,rt=eQ,ro=e=>{let{__scopeDropdownMenu:r,children:n,open:t,onOpenChange:o,defaultOpen:a}=e,u=eF(r),[i=!1,c]=(0,l.T)({prop:t,defaultProp:a,onChange:o});return(0,y.jsx)(ej,{...u,open:i,onOpenChange:c,children:n})},ra=e0,ru=e1},71599:function(e,r,n){n.d(r,{z:function(){return l}});var t=n(2265),o=n(54887),a=n(98575),u=n(61188),l=e=>{var r,n;let l,c;let{present:s,children:d}=e,f=function(e){var r,n;let[a,l]=t.useState(),c=t.useRef({}),s=t.useRef(e),d=t.useRef("none"),[f,p]=(r=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},t.useReducer((e,r)=>{let t=n[e][r];return null!=t?t:e},r));return t.useEffect(()=>{let e=i(c.current);d.current="mounted"===f?e:"none"},[f]),(0,u.b)(()=>{let r=c.current,n=s.current;if(n!==e){let t=d.current,o=i(r);e?p("MOUNT"):"none"===o||(null==r?void 0:r.display)==="none"?p("UNMOUNT"):n&&t!==o?p("ANIMATION_OUT"):p("UNMOUNT"),s.current=e}},[e,p]),(0,u.b)(()=>{if(a){let e=e=>{let r=i(c.current).includes(e.animationName);e.target===a&&r&&o.flushSync(()=>p("ANIMATION_END"))},r=e=>{e.target===a&&(d.current=i(c.current))};return a.addEventListener("animationstart",r),a.addEventListener("animationcancel",e),a.addEventListener("animationend",e),()=>{a.removeEventListener("animationstart",r),a.removeEventListener("animationcancel",e),a.removeEventListener("animationend",e)}}p("ANIMATION_END")},[a,p]),{isPresent:["mounted","unmountSuspended"].includes(f),ref:t.useCallback(e=>{e&&(c.current=getComputedStyle(e)),l(e)},[])}}(s),p="function"==typeof d?d({present:f.isPresent}):t.Children.only(d),v=(0,a.e)(f.ref,(l=null===(r=Object.getOwnPropertyDescriptor(p.props,"ref"))||void 0===r?void 0:r.get)&&"isReactWarning"in l&&l.isReactWarning?p.ref:(l=null===(n=Object.getOwnPropertyDescriptor(p,"ref"))||void 0===n?void 0:n.get)&&"isReactWarning"in l&&l.isReactWarning?p.props.ref:p.props.ref||p.ref);return"function"==typeof d||f.isPresent?t.cloneElement(p,{ref:v}):null};function i(e){return(null==e?void 0:e.animationName)||"none"}l.displayName="Presence"},1353:function(e,r,n){n.d(r,{Pc:function(){return M},ck:function(){return N},fC:function(){return P}});var t=n(2265),o=n(6741),a=n(71605),u=n(98575),l=n(73966),i=n(99255),c=n(82912),s=n(26606),d=n(80886),f=n(29114),p=n(57437),v="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},g="RovingFocusGroup",[h,w,y]=(0,a.B)(g),[x,M]=(0,l.b)(g,[y]),[b,C]=x(g),R=t.forwardRef((e,r)=>(0,p.jsx)(h.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(h.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,p.jsx)(j,{...e,ref:r})})}));R.displayName=g;var j=t.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:n,orientation:a,loop:l=!1,dir:i,currentTabStopId:g,defaultCurrentTabStopId:h,onCurrentTabStopIdChange:y,onEntryFocus:x,preventScrollOnEntryFocus:M=!1,...C}=e,R=t.useRef(null),j=(0,u.e)(r,R),D=(0,f.gm)(i),[O=null,_]=(0,d.T)({prop:g,defaultProp:h,onChange:y}),[P,N]=t.useState(!1),I=(0,s.W)(x),k=w(n),T=t.useRef(!1),[S,A]=t.useState(0);return t.useEffect(()=>{let e=R.current;if(e)return e.addEventListener(v,I),()=>e.removeEventListener(v,I)},[I]),(0,p.jsx)(b,{scope:n,orientation:a,dir:D,loop:l,currentTabStopId:O,onItemFocus:t.useCallback(e=>_(e),[_]),onItemShiftTab:t.useCallback(()=>N(!0),[]),onFocusableItemAdd:t.useCallback(()=>A(e=>e+1),[]),onFocusableItemRemove:t.useCallback(()=>A(e=>e-1),[]),children:(0,p.jsx)(c.WV.div,{tabIndex:P||0===S?-1:0,"data-orientation":a,...C,ref:j,style:{outline:"none",...e.style},onMouseDown:(0,o.M)(e.onMouseDown,()=>{T.current=!0}),onFocus:(0,o.M)(e.onFocus,e=>{let r=!T.current;if(e.target===e.currentTarget&&r&&!P){let r=new CustomEvent(v,m);if(e.currentTarget.dispatchEvent(r),!r.defaultPrevented){let e=k().filter(e=>e.focusable);E([e.find(e=>e.active),e.find(e=>e.id===O),...e].filter(Boolean).map(e=>e.ref.current),M)}}T.current=!1}),onBlur:(0,o.M)(e.onBlur,()=>N(!1))})})}),D="RovingFocusGroupItem",O=t.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:n,focusable:a=!0,active:u=!1,tabStopId:l,...s}=e,d=(0,i.M)(),f=l||d,v=C(D,n),m=v.currentTabStopId===f,g=w(n),{onFocusableItemAdd:y,onFocusableItemRemove:x}=v;return t.useEffect(()=>{if(a)return y(),()=>x()},[a,y,x]),(0,p.jsx)(h.ItemSlot,{scope:n,id:f,focusable:a,active:u,children:(0,p.jsx)(c.WV.span,{tabIndex:m?0:-1,"data-orientation":v.orientation,...s,ref:r,onMouseDown:(0,o.M)(e.onMouseDown,e=>{a?v.onItemFocus(f):e.preventDefault()}),onFocus:(0,o.M)(e.onFocus,()=>v.onItemFocus(f)),onKeyDown:(0,o.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){v.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let r=function(e,r,n){var t;let o=(t=e.key,"rtl"!==n?t:"ArrowLeft"===t?"ArrowRight":"ArrowRight"===t?"ArrowLeft":t);if(!("vertical"===r&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===r&&["ArrowUp","ArrowDown"].includes(o)))return _[o]}(e,v.orientation,v.dir);if(void 0!==r){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let o=g().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===r)o.reverse();else if("prev"===r||"next"===r){var n,t;"prev"===r&&o.reverse();let a=o.indexOf(e.currentTarget);o=v.loop?(n=o,t=a+1,n.map((e,r)=>n[(t+r)%n.length])):o.slice(a+1)}setTimeout(()=>E(o))}})})})});O.displayName=D;var _={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function E(e){let r=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let t of e)if(t===n||(t.focus({preventScroll:r}),document.activeElement!==n))return}var P=R,N=O}}]);