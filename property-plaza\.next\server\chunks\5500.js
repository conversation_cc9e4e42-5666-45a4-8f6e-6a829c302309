"use strict";exports.id=5500,exports.ids=[5500],exports.modules={75500:(e,t,r)=>{r.d(t,{Ns:()=>G,fC:()=>B,gb:()=>x,q4:()=>W,l_:()=>q});var n=r(28964),o=r(20002),l=r(93191),i=r(9537),a=e=>{let{present:t,children:r}=e,o=function(e){var t,r;let[o,l]=n.useState(),a=n.useRef({}),c=n.useRef(e),u=n.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>r[e][t]??e,t));return n.useEffect(()=>{let e=s(a.current);u.current="mounted"===d?e:"none"},[d]),(0,i.b)(()=>{let t=a.current,r=c.current;if(r!==e){let n=u.current,o=s(t);e?f("MOUNT"):"none"===o||t?.display==="none"?f("UNMOUNT"):r&&n!==o?f("ANIMATION_OUT"):f("UNMOUNT"),c.current=e}},[e,f]),(0,i.b)(()=>{if(o){let e;let t=o.ownerDocument.defaultView??window,r=r=>{let n=s(a.current).includes(r.animationName);if(r.target===o&&n&&(f("ANIMATION_END"),!c.current)){let r=o.style.animationFillMode;o.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=r)})}},n=e=>{e.target===o&&(u.current=s(a.current))};return o.addEventListener("animationstart",n),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{t.clearTimeout(e),o.removeEventListener("animationstart",n),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:n.useCallback(e=>{e&&(a.current=getComputedStyle(e)),l(e)},[])}}(t),a="function"==typeof r?r({present:o.isPresent}):n.Children.only(r),c=(0,l.e)(o.ref,function(e){let t=Object.getOwnPropertyDescriptor(e.props,"ref")?.get,r=t&&"isReactWarning"in t&&t.isReactWarning;return r?e.ref:(r=(t=Object.getOwnPropertyDescriptor(e,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(a));return"function"==typeof r||o.isPresent?n.cloneElement(a,{ref:c}):null};function s(e){return e?.animationName||"none"}a.displayName="Presence";var c=r(97247),u=r(85090),d=r(71310),f=r(28779),p=r(70319),h="ScrollArea",[v,w]=function(e,t=[]){let r=[],o=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return o.scopeName=e,[function(t,o){let l=n.createContext(o),i=r.length;r=[...r,o];let a=t=>{let{scope:r,children:o,...a}=t,s=r?.[e]?.[i]||l,u=n.useMemo(()=>a,Object.values(a));return(0,c.jsx)(s.Provider,{value:u,children:o})};return a.displayName=t+"Provider",[a,function(r,a){let s=a?.[e]?.[i]||l,c=n.useContext(s);if(c)return c;if(void 0!==o)return o;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(o,...t)]}(h),[m,g]=v(h),b=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,type:i="hover",dir:a,scrollHideDelay:s=600,...u}=e,[f,p]=n.useState(null),[h,v]=n.useState(null),[w,g]=n.useState(null),[b,S]=n.useState(null),[y,E]=n.useState(null),[x,T]=n.useState(0),[C,R]=n.useState(0),[N,P]=n.useState(!1),[L,_]=n.useState(!1),A=(0,l.e)(t,e=>p(e)),D=(0,d.gm)(a);return(0,c.jsx)(m,{scope:r,type:i,dir:D,scrollHideDelay:s,scrollArea:f,viewport:h,onViewportChange:v,content:w,onContentChange:g,scrollbarX:b,onScrollbarXChange:S,scrollbarXEnabled:N,onScrollbarXEnabledChange:P,scrollbarY:y,onScrollbarYChange:E,scrollbarYEnabled:L,onScrollbarYEnabledChange:_,onCornerWidthChange:T,onCornerHeightChange:R,children:(0,c.jsx)(o.WV.div,{dir:D,...u,ref:A,style:{position:"relative","--radix-scroll-area-corner-width":x+"px","--radix-scroll-area-corner-height":C+"px",...e.style}})})});b.displayName=h;var S="ScrollAreaViewport",y=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,children:i,asChild:a,nonce:s,...u}=e,d=g(S,r),f=n.useRef(null),p=(0,l.e)(t,f,d.onViewportChange);return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("style",{dangerouslySetInnerHTML:{__html:`
[data-radix-scroll-area-viewport] {
  scrollbar-width: none;
  -ms-overflow-style: none;
  -webkit-overflow-scrolling: touch;
}
[data-radix-scroll-area-viewport]::-webkit-scrollbar {
  display: none;
}
:where([data-radix-scroll-area-viewport]) {
  display: flex;
  flex-direction: column;
  align-items: stretch;
}
:where([data-radix-scroll-area-content]) {
  flex-grow: 1;
}
`},nonce:s}),(0,c.jsx)(o.WV.div,{"data-radix-scroll-area-viewport":"",...u,asChild:a,ref:p,style:{overflowX:d.scrollbarXEnabled?"scroll":"hidden",overflowY:d.scrollbarYEnabled?"scroll":"hidden",...e.style},children:function(e,t){let{asChild:r,children:o}=e;if(!r)return"function"==typeof t?t(o):t;let l=n.Children.only(o);return n.cloneElement(l,{children:"function"==typeof t?t(l.props.children):t})}({asChild:a,children:i},e=>(0,c.jsx)("div",{"data-radix-scroll-area-content":"",ref:d.onContentChange,style:{minWidth:d.scrollbarXEnabled?"fit-content":void 0},children:e}))})]})});y.displayName=S;var E="ScrollAreaScrollbar",x=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,l=g(E,e.__scopeScrollArea),{onScrollbarXEnabledChange:i,onScrollbarYEnabledChange:a}=l,s="horizontal"===e.orientation;return n.useEffect(()=>(s?i(!0):a(!0),()=>{s?i(!1):a(!1)}),[s,i,a]),"hover"===l.type?(0,c.jsx)(T,{...o,ref:t,forceMount:r}):"scroll"===l.type?(0,c.jsx)(C,{...o,ref:t,forceMount:r}):"auto"===l.type?(0,c.jsx)(R,{...o,ref:t,forceMount:r}):"always"===l.type?(0,c.jsx)(N,{...o,ref:t}):null});x.displayName=E;var T=n.forwardRef((e,t)=>{let{forceMount:r,...o}=e,l=g(E,e.__scopeScrollArea),[i,s]=n.useState(!1);return n.useEffect(()=>{let e=l.scrollArea,t=0;if(e){let r=()=>{window.clearTimeout(t),s(!0)},n=()=>{t=window.setTimeout(()=>s(!1),l.scrollHideDelay)};return e.addEventListener("pointerenter",r),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",r),e.removeEventListener("pointerleave",n)}}},[l.scrollArea,l.scrollHideDelay]),(0,c.jsx)(a,{present:r||i,children:(0,c.jsx)(R,{"data-state":i?"visible":"hidden",...o,ref:t})})}),C=n.forwardRef((e,t)=>{var r,o;let{forceMount:l,...i}=e,s=g(E,e.__scopeScrollArea),u="horizontal"===e.orientation,d=k(()=>h("SCROLL_END"),100),[f,h]=(r="hidden",o={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},n.useReducer((e,t)=>o[e][t]??e,r));return n.useEffect(()=>{if("idle"===f){let e=window.setTimeout(()=>h("HIDE"),s.scrollHideDelay);return()=>window.clearTimeout(e)}},[f,s.scrollHideDelay,h]),n.useEffect(()=>{let e=s.viewport,t=u?"scrollLeft":"scrollTop";if(e){let r=e[t],n=()=>{let n=e[t];r!==n&&(h("SCROLL"),d()),r=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[s.viewport,u,h,d]),(0,c.jsx)(a,{present:l||"hidden"!==f,children:(0,c.jsx)(N,{"data-state":"hidden"===f?"hidden":"visible",...i,ref:t,onPointerEnter:(0,p.M)(e.onPointerEnter,()=>h("POINTER_ENTER")),onPointerLeave:(0,p.M)(e.onPointerLeave,()=>h("POINTER_LEAVE"))})})}),R=n.forwardRef((e,t)=>{let r=g(E,e.__scopeScrollArea),{forceMount:o,...l}=e,[i,s]=n.useState(!1),u="horizontal"===e.orientation,d=k(()=>{if(r.viewport){let e=r.viewport.offsetWidth<r.viewport.scrollWidth,t=r.viewport.offsetHeight<r.viewport.scrollHeight;s(u?e:t)}},10);return $(r.viewport,d),$(r.content,d),(0,c.jsx)(a,{present:o||i,children:(0,c.jsx)(N,{"data-state":i?"visible":"hidden",...l,ref:t})})}),N=n.forwardRef((e,t)=>{let{orientation:r="vertical",...o}=e,l=g(E,e.__scopeScrollArea),i=n.useRef(null),a=n.useRef(0),[s,u]=n.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),d=H(s.viewport,s.content),f={...o,sizes:s,onSizesChange:u,hasThumb:!!(d>0&&d<1),onThumbChange:e=>i.current=e,onThumbPointerUp:()=>a.current=0,onThumbPointerDown:e=>a.current=e};function p(e,t){return function(e,t,r,n="ltr"){let o=X(r),l=t||o/2,i=r.scrollbar.paddingStart+l,a=r.scrollbar.size-r.scrollbar.paddingEnd-(o-l),s=r.content-r.viewport;return Y([i,a],"ltr"===n?[0,s]:[-1*s,0])(e)}(e,a.current,s,t)}return"horizontal"===r?(0,c.jsx)(P,{...f,ref:t,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=V(l.viewport.scrollLeft,s,l.dir);i.current.style.transform=`translate3d(${e}px, 0, 0)`}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollLeft=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollLeft=p(e,l.dir))}}):"vertical"===r?(0,c.jsx)(L,{...f,ref:t,onThumbPositionChange:()=>{if(l.viewport&&i.current){let e=V(l.viewport.scrollTop,s);i.current.style.transform=`translate3d(0, ${e}px, 0)`}},onWheelScroll:e=>{l.viewport&&(l.viewport.scrollTop=e)},onDragScroll:e=>{l.viewport&&(l.viewport.scrollTop=p(e))}}):null}),P=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...i}=e,a=g(E,e.__scopeScrollArea),[s,u]=n.useState(),d=n.useRef(null),f=(0,l.e)(t,d,a.onScrollbarXChange);return n.useEffect(()=>{d.current&&u(getComputedStyle(d.current))},[d]),(0,c.jsx)(D,{"data-orientation":"horizontal",...i,ref:f,sizes:r,style:{bottom:0,left:"rtl"===a.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===a.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":X(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,r)=>{if(a.viewport){let n=a.viewport.scrollLeft+t.deltaX;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{d.current&&a.viewport&&s&&o({content:a.viewport.scrollWidth,viewport:a.viewport.offsetWidth,scrollbar:{size:d.current.clientWidth,paddingStart:z(s.paddingLeft),paddingEnd:z(s.paddingRight)}})}})}),L=n.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...i}=e,a=g(E,e.__scopeScrollArea),[s,u]=n.useState(),d=n.useRef(null),f=(0,l.e)(t,d,a.onScrollbarYChange);return n.useEffect(()=>{d.current&&u(getComputedStyle(d.current))},[d]),(0,c.jsx)(D,{"data-orientation":"vertical",...i,ref:f,sizes:r,style:{top:0,right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":X(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,r)=>{if(a.viewport){let n=a.viewport.scrollTop+t.deltaY;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{d.current&&a.viewport&&s&&o({content:a.viewport.scrollHeight,viewport:a.viewport.offsetHeight,scrollbar:{size:d.current.clientHeight,paddingStart:z(s.paddingTop),paddingEnd:z(s.paddingBottom)}})}})}),[_,A]=v(E),D=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,sizes:i,hasThumb:a,onThumbChange:s,onThumbPointerUp:d,onThumbPointerDown:f,onThumbPositionChange:h,onDragScroll:v,onWheelScroll:w,onResize:m,...b}=e,S=g(E,r),[y,x]=n.useState(null),T=(0,l.e)(t,e=>x(e)),C=n.useRef(null),R=n.useRef(""),N=S.viewport,P=i.content-i.viewport,L=(0,u.W)(w),A=(0,u.W)(h),D=k(m,10);function j(e){C.current&&v({x:e.clientX-C.current.left,y:e.clientY-C.current.top})}return n.useEffect(()=>{let e=e=>{let t=e.target;y?.contains(t)&&L(e,P)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[N,y,P,L]),n.useEffect(A,[i,A]),$(y,D),$(S.content,D),(0,c.jsx)(_,{scope:r,scrollbar:y,hasThumb:a,onThumbChange:(0,u.W)(s),onThumbPointerUp:(0,u.W)(d),onThumbPositionChange:A,onThumbPointerDown:(0,u.W)(f),children:(0,c.jsx)(o.WV.div,{...b,ref:T,style:{position:"absolute",...b.style},onPointerDown:(0,p.M)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),C.current=y.getBoundingClientRect(),R.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",S.viewport&&(S.viewport.style.scrollBehavior="auto"),j(e))}),onPointerMove:(0,p.M)(e.onPointerMove,j),onPointerUp:(0,p.M)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=R.current,S.viewport&&(S.viewport.style.scrollBehavior=""),C.current=null})})})}),j="ScrollAreaThumb",W=n.forwardRef((e,t)=>{let{forceMount:r,...n}=e,o=A(j,e.__scopeScrollArea);return(0,c.jsx)(a,{present:r||o.hasThumb,children:(0,c.jsx)(O,{ref:t,...n})})}),O=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,style:i,...a}=e,s=g(j,r),u=A(j,r),{onThumbPositionChange:d}=u,f=(0,l.e)(t,e=>u.onThumbChange(e)),h=n.useRef(),v=k(()=>{h.current&&(h.current(),h.current=void 0)},100);return n.useEffect(()=>{let e=s.viewport;if(e){let t=()=>{if(v(),!h.current){let t=F(e,d);h.current=t,d()}};return d(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[s.viewport,v,d]),(0,c.jsx)(o.WV.div,{"data-state":u.hasThumb?"visible":"hidden",...a,ref:f,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...i},onPointerDownCapture:(0,p.M)(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),r=e.clientX-t.left,n=e.clientY-t.top;u.onThumbPointerDown({x:r,y:n})}),onPointerUp:(0,p.M)(e.onPointerUp,u.onThumbPointerUp)})});W.displayName=j;var M="ScrollAreaCorner",I=n.forwardRef((e,t)=>{let r=g(M,e.__scopeScrollArea),n=!!(r.scrollbarX&&r.scrollbarY);return"scroll"!==r.type&&n?(0,c.jsx)(U,{...e,ref:t}):null});I.displayName=M;var U=n.forwardRef((e,t)=>{let{__scopeScrollArea:r,...l}=e,i=g(M,r),[a,s]=n.useState(0),[u,d]=n.useState(0),f=!!(a&&u);return $(i.scrollbarX,()=>{let e=i.scrollbarX?.offsetHeight||0;i.onCornerHeightChange(e),d(e)}),$(i.scrollbarY,()=>{let e=i.scrollbarY?.offsetWidth||0;i.onCornerWidthChange(e),s(e)}),f?(0,c.jsx)(o.WV.div,{...l,ref:t,style:{width:a,height:u,position:"absolute",right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:0,...e.style}}):null});function z(e){return e?parseInt(e,10):0}function H(e,t){let r=e/t;return isNaN(r)?0:r}function X(e){let t=H(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-r)*t,18)}function V(e,t,r="ltr"){let n=X(t),o=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,l=t.scrollbar.size-o,i=t.content-t.viewport,a=(0,f.u)(e,"ltr"===r?[0,i]:[-1*i,0]);return Y([0,i],[0,l-n])(a)}function Y(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}var F=(e,t=()=>{})=>{let r={left:e.scrollLeft,top:e.scrollTop},n=0;return function o(){let l={left:e.scrollLeft,top:e.scrollTop},i=r.left!==l.left,a=r.top!==l.top;(i||a)&&t(),r=l,n=window.requestAnimationFrame(o)}(),()=>window.cancelAnimationFrame(n)};function k(e,t){let r=(0,u.W)(e),o=n.useRef(0);return n.useEffect(()=>()=>window.clearTimeout(o.current),[]),n.useCallback(()=>{window.clearTimeout(o.current),o.current=window.setTimeout(r,t)},[r,t])}function $(e,t){let r=(0,u.W)(t);(0,i.b)(()=>{let t=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(r)});return n.observe(e),()=>{window.cancelAnimationFrame(t),n.unobserve(e)}}},[e,r])}var B=b,q=y,G=I}};