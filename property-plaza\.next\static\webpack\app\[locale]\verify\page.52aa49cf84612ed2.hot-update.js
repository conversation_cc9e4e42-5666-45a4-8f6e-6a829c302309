"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/verify/page",{

/***/ "(app-pages-browser)/./app/[locale]/verify/verify-hero.tsx":
/*!*********************************************!*\
  !*** ./app/[locale]/verify/verify-hero.tsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ VerifyHero; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _components_seekers_content_layout_main_content_layout__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/seekers-content-layout/main-content-layout */ \"(app-pages-browser)/./components/seekers-content-layout/main-content-layout.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _lib_locale_routing__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/locale/routing */ \"(app-pages-browser)/./lib/locale/routing.ts\");\n/* harmony import */ var _components_ui_optimized_video__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/optimized-video */ \"(app-pages-browser)/./components/ui/optimized-video.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction VerifyHero() {\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_5__.useTranslations)(\"verify\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"bg-gradient-to-br from-seekers-primary/5 to-seekers-primary/10 py-6 md:py-10 lg:py-16\",\n        \"aria-labelledby\": \"hero-title\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_seekers_content_layout_main_content_layout__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col lg:grid lg:grid-cols-2 gap-4 md:gap-6 lg:gap-8 items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-3 md:space-y-4 lg:space-y-5 order-2 lg:order-1 text-center lg:text-left\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center lg:justify-start gap-2 text-red-600 font-semibold\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-xl md:text-2xl\",\n                                        children: \"\\uD83D\\uDEA8\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-hero.tsx\",\n                                        lineNumber: 18,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm md:text-base\",\n                                        children: t(\"hero.badge\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-hero.tsx\",\n                                        lineNumber: 19,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-hero.tsx\",\n                                lineNumber: 17,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                id: \"hero-title\",\n                                className: \"text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold text-seekers-text leading-tight\",\n                                children: t(\"hero.title\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-hero.tsx\",\n                                lineNumber: 22,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-base md:text-lg text-seekers-text-light leading-relaxed\",\n                                children: t(\"hero.subtitle\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-hero.tsx\",\n                                lineNumber: 26,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-3 md:space-y-4\",\n                                children: [\n                                    t(\"hero.benefits.0\"),\n                                    t(\"hero.benefits.1\"),\n                                    t(\"hero.benefits.2\"),\n                                    t(\"hero.benefits.3\")\n                                ].map((benefit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start lg:items-center gap-3 justify-center lg:justify-start\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-seekers-primary text-lg mt-0.5 lg:mt-0\",\n                                                children: \"•\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-hero.tsx\",\n                                                lineNumber: 38,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm md:text-base text-seekers-text font-medium text-left\",\n                                                children: benefit\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-hero.tsx\",\n                                                lineNumber: 39,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-hero.tsx\",\n                                        lineNumber: 37,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-hero.tsx\",\n                                lineNumber: 30,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-red-50 border border-red-200 rounded-lg p-3 md:p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-800 font-medium text-sm md:text-base text-center lg:text-left\",\n                                    children: [\n                                        t(\"hero.warning\"),\n                                        \" \",\n                                        t(\"hero.cta\")\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-hero.tsx\",\n                                    lineNumber: 47,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-hero.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-4 md:pt-6 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"lg\",\n                                        className: \"bg-seekers-primary hover:bg-seekers-primary/90 text-white px-6 md:px-8 py-3 md:py-4 text-base md:text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-200 w-full sm:w-auto\",\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_locale_routing__WEBPACK_IMPORTED_MODULE_3__.Link, {\n                                            href: \"#booking-form\",\n                                            children: t(\"cta.bookInspection\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-hero.tsx\",\n                                            lineNumber: 58,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-hero.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs md:text-sm text-seekers-text-light mt-2 md:mt-3\",\n                                        children: t(\"footnote\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-hero.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-hero.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-hero.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex justify-center order-1 lg:order-2 w-full\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-full max-w-xs sm:max-w-sm md:max-w-md lg:max-w-xs xl:max-w-sm\",\n                            style: {\n                                aspectRatio: \"9/16\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative group\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_optimized_video__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        src: \"/videos/villa-inspection-hero.mp4\",\n                                        poster: \"/images/villa-inspection-poster.jpg\",\n                                        className: \"w-full h-full shadow-lg hover:shadow-xl transition-shadow duration-300\",\n                                        autoPlay: false,\n                                        muted: false,\n                                        loop: false,\n                                        playsInline: true,\n                                        controls: true,\n                                        preload: \"metadata\",\n                                        lazy: false,\n                                        fallbackContent: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center space-y-2 p-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-4xl\",\n                                                    children: \"\\uD83D\\uDCF1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-hero.tsx\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 21\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-neutral-500 font-medium\",\n                                                    children: \"Villa Inspection Video\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-hero.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 21\n                                                }, void 0),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-neutral-400 leading-relaxed\",\n                                                    children: [\n                                                        \"Professional villa inspection\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-hero.tsx\",\n                                                            lineNumber: 89,\n                                                            columnNumber: 52\n                                                        }, void 0),\n                                                        \"process and red flags\",\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-hero.tsx\",\n                                                            lineNumber: 90,\n                                                            columnNumber: 44\n                                                        }, void 0),\n                                                        \"identification\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-hero.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-hero.tsx\",\n                                            lineNumber: 85,\n                                            columnNumber: 19\n                                        }, void 0)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-hero.tsx\",\n                                        lineNumber: 73,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-3 left-3 bg-black/70 text-white px-2 py-1 rounded text-xs font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                        children: \"\\uD83C\\uDFAC Watch Inspection Process\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-hero.tsx\",\n                                        lineNumber: 98,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-hero.tsx\",\n                                lineNumber: 72,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-hero.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-hero.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-hero.tsx\",\n                lineNumber: 14,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-hero.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-hero.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, this);\n}\n_s(VerifyHero, \"h6+q2O3NJKPY5uL0BIJGLIanww8=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_5__.useTranslations\n    ];\n});\n_c = VerifyHero;\nvar _c;\n$RefreshReg$(_c, \"VerifyHero\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/verify/verify-hero.tsx\n"));

/***/ })

});