"use client"
import { useEffect, useRef, useState } from 'react';
import Image from 'next/image';

interface OptimizedVideoProps {
  src: string;
  poster?: string;
  className?: string;
  autoPlay?: boolean;
  muted?: boolean;
  loop?: boolean;
  playsInline?: boolean;
  controls?: boolean;
  preload?: 'none' | 'metadata' | 'auto';
  lazy?: boolean;
  fallbackContent?: React.ReactNode;
}

export default function OptimizedVideo({
  src,
  poster,
  className = "",
  autoPlay = true,
  muted = true,
  loop = true,
  playsInline = true,
  controls = false,
  preload = "metadata",
  lazy = true,
  fallbackContent
}: OptimizedVideoProps) {
  const [shouldLoad, setShouldLoad] = useState(!lazy);
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const videoRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!lazy) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setShouldLoad(true);
          observer.disconnect();
        }
      },
      { 
        threshold: 0.1,
        rootMargin: '50px' // Start loading 50px before video comes into view
      }
    );

    if (videoRef.current) {
      observer.observe(videoRef.current);
    }

    return () => observer.disconnect();
  }, [lazy]);

  const handleLoadStart = () => {
    setIsLoading(true);
    setHasError(false);
  };

  const handleCanPlay = () => {
    setIsLoading(false);
  };

  const handleError = () => {
    setIsLoading(false);
    setHasError(true);
  };

  return (
    <div ref={videoRef} className={`relative ${className}`}>
      {/* Loading State */}
      {isLoading && shouldLoad && (
        <div className="absolute inset-0 bg-gray-100 flex items-center justify-center z-10">
          <div className="flex flex-col items-center space-y-2">
            <div className="w-8 h-8 border-2 border-seekers-primary border-t-transparent rounded-full animate-spin"></div>
            <p className="text-sm text-gray-500">Loading video...</p>
          </div>
        </div>
      )}

      {/* Error State */}
      {hasError && (
        <div className="absolute inset-0 bg-gray-100 flex items-center justify-center z-10">
          <div className="text-center space-y-2">
            <div className="text-4xl">⚠️</div>
            <p className="text-sm text-gray-500">Video could not be loaded</p>
            {fallbackContent}
          </div>
        </div>
      )}

      {/* Poster Image (shown before video loads or if lazy loading) */}
      {poster && !shouldLoad && (
        <Image
          src={poster}
          alt="Video preview"
          fill
          className="object-cover rounded-lg"
        />
      )}

      {/* Video Element */}
      {shouldLoad && (
        <video
          autoPlay={autoPlay}
          muted={muted}
          loop={loop}
          playsInline={playsInline}
          controls={controls}
          preload={preload}
          poster={poster}
          className="w-full h-full object-cover rounded-lg"
          onLoadStart={handleLoadStart}
          onCanPlay={handleCanPlay}
          onError={handleError}
        >
          <source src={src} type="video/mp4" />
          <source src={src.replace('.mp4', '.webm')} type="video/webm" />
          
          {/* Fallback for browsers that don't support video */}
          <div className="flex items-center justify-center h-full bg-gray-100">
            <div className="text-center space-y-2">
              <div className="text-4xl">📹</div>
              <p className="text-sm text-gray-500">Your browser doesn&apos;t support video playback</p>
              {fallbackContent}
            </div>
          </div>
        </video>
      )}
    </div>
  );
}
