"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/verify/page",{

/***/ "(app-pages-browser)/./components/ui/optimized-video.tsx":
/*!*******************************************!*\
  !*** ./components/ui/optimized-video.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OptimizedVideo; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction OptimizedVideo(param) {\n    let { src, poster, className = \"\", autoPlay = true, muted = true, loop = true, playsInline = true, controls = false, preload = \"metadata\", lazy = true, fallbackContent, showCustomControls = false } = param;\n    _s();\n    const [shouldLoad, setShouldLoad] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!lazy);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [canPlay, setCanPlay] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasError, setHasError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const videoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!lazy) return;\n        const observer = new IntersectionObserver((param)=>{\n            let [entry] = param;\n            if (entry.isIntersecting) {\n                setShouldLoad(true);\n                observer.disconnect();\n            }\n        }, {\n            threshold: 0.1,\n            rootMargin: \"50px\" // Start loading 50px before video comes into view\n        });\n        if (videoRef.current) {\n            observer.observe(videoRef.current);\n        }\n        return ()=>observer.disconnect();\n    }, [\n        lazy\n    ]);\n    const handleLoadStart = ()=>{\n        setIsLoading(true);\n        setCanPlay(false);\n        setHasError(false);\n    };\n    const handleCanPlay = ()=>{\n        setIsLoading(false);\n        setCanPlay(true);\n    };\n    const handleError = ()=>{\n        setIsLoading(false);\n        setCanPlay(false);\n        setHasError(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: videoRef,\n        className: \"relative \".concat(className),\n        children: [\n            isLoading && shouldLoad && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gray-100 flex items-center justify-center z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 border-2 border-seekers-primary border-t-transparent rounded-full animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: \"Loading video...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                lineNumber: 84,\n                columnNumber: 9\n            }, this),\n            hasError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gray-100 flex items-center justify-center z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-4xl\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: \"Video could not be loaded\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 13\n                        }, this),\n                        fallbackContent\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                lineNumber: 94,\n                columnNumber: 9\n            }, this),\n            poster && (!shouldLoad || !canPlay) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 z-10\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        src: poster,\n                        alt: \"Video preview\",\n                        fill: true,\n                        className: \"object-cover rounded-lg\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, this),\n                    shouldLoad && !isLoading && !hasError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 flex items-center justify-center bg-black/20 rounded-lg\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-16 h-16 bg-white/90 rounded-full flex items-center justify-center shadow-lg hover:bg-white transition-colors duration-200 cursor-pointer\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-0 h-0 border-l-[12px] border-l-black border-t-[8px] border-t-transparent border-b-[8px] border-b-transparent ml-1\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                                lineNumber: 117,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                        lineNumber: 115,\n                        columnNumber: 13\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                lineNumber: 105,\n                columnNumber: 9\n            }, this),\n            shouldLoad && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                autoPlay: autoPlay,\n                muted: muted,\n                loop: loop,\n                playsInline: playsInline,\n                controls: controls,\n                preload: preload,\n                poster: poster,\n                className: \"w-full h-full object-cover rounded-lg \".concat(controls ? \"cursor-pointer\" : \"\"),\n                onLoadStart: handleLoadStart,\n                onCanPlay: handleCanPlay,\n                onError: handleError,\n                style: {\n                    outline: \"none\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                        src: src,\n                        type: \"video/mp4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                        src: src.replace(\".mp4\", \".webm\"),\n                        type: \"video/webm\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                        lineNumber: 143,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-full bg-gray-100\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl\",\n                                    children: \"\\uD83D\\uDCF9\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: \"Your browser doesn't support video playback\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 15\n                                }, this),\n                                fallbackContent\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                lineNumber: 126,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\n_s(OptimizedVideo, \"Zg8iB6wn/MI0qUbHEmOGw0Twf6Y=\");\n_c = OptimizedVideo;\nvar _c;\n$RefreshReg$(_c, \"OptimizedVideo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/optimized-video.tsx\n"));

/***/ })

});