(()=>{var e={};e.id=9426,e.ids=[9426],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},40092:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>d,originalPathname:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>p}),r(2087),r(55695),r(3929),r(84448),r(81729),r(90996);var s=r(30170),a=r(45002),i=r(83876),o=r.n(i),n=r(66299),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let p=["",{children:["[locale]",{children:["(user-profile)",{children:["subscription",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,2087)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user-profile)\\subscription\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,55695)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user-profile)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,3929)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user-profile)\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,80603))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,84448)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,81729)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,90996,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user-profile)\\subscription\\page.tsx"],u="/[locale]/(user-profile)/subscription/page",d={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/[locale]/(user-profile)/subscription/page",pathname:"/[locale]/subscription",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:p}})},5366:(e,t,r)=>{Promise.resolve().then(r.bind(r,48721)),Promise.resolve().then(r.bind(r,17027)),Promise.resolve().then(r.bind(r,74993)),Promise.resolve().then(r.bind(r,26793)),Promise.resolve().then(r.bind(r,70697)),Promise.resolve().then(r.bind(r,92941))},2087:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g,generateMetadata:()=>f});var s=r(72051),a=r(79438),i=r(35243),o=r(4459),n=r(37170),l=r(53189),p=r(69385),c=r(93844);function u(){let e=(0,p.Z)("seeker");return(0,s.jsxs)(a.Z,{className:(0,n.cn)("mx-auto flex gap-2 items-end max-sm:px-0 space-y-6 pt-6 max-sm:pt-0"),children:[s.jsx(o.vP,{className:"items-end -ml-2"}),s.jsx(i.aG,{className:"",children:(0,s.jsxs)(i.Jb,{className:"space-x-4 sm:gap-0",children:[s.jsx(i.gN,{className:"text-seekers-text font-medium text-sm",children:(0,s.jsxs)(c.rU,{href:"/",className:"flex gap-2.5 items-center",children:[s.jsx(l.Z,{className:"w-4 h-4",strokeWidth:1}),e("misc.home")]})}),s.jsx(i.bg,{className:"text-seekers-text font-medium text-sm w-3 h-fit text-center",children:"/"}),s.jsx(i.gN,{className:"capitalize text-seekers-text font-medium text-sm",children:e("setting.subscriptionStatus.subscription.title")})]})})]})}var d=r(29507),m=r(83266),x=r(504),h=r(70276),P=r(5348),b=r(92898);async function f(){let e=await (0,d.Z)("seeker"),t=await (0,m.Z)()||c.DI.defaultLocale,r=process.env.USER_DOMAIN||"https://www.property-plaza.com/";return{title:e("metadata.subsriptionPlan.title"),description:e("metadata.subsriptionPlan.description"),alternates:{canonical:r+t+b.OM,languages:{en:r+"en"+b.OM,id:r+"id"+b.OM,"x-default":r+b.OM.replace("/","")}},openGraph:{title:e("metadata.subsriptionPlan.title"),description:e("metadata.subsriptionPlan.description"),images:[{url:r+"og.png",width:1200,height:630,alt:"Property Plaza"}],type:"website",url:r+t+b.OM,countryName:"Indonesia",emails:"<EMAIL>",locale:t,alternateLocale:c.DI.locales,siteName:"Property plaza"},applicationName:"Property plaza",twitter:{card:"summary_large_image",title:e("metadata.subsriptionPlan.title"),description:e("metadata.subsriptionPlan.description"),images:[r+"og.jpg"]},robots:{index:!0,follow:!0,nocache:!1}}}async function g(){let e=await (0,h.T)("EUR"),t=(await (0,P.K)()).data,r=await (0,d.Z)("seeker");return(0,s.jsxs)(s.Fragment,{children:[s.jsx(u,{}),(0,s.jsxs)(a.Z,{className:"space-y-8 my-8 mb-14 max-sm:px-0",children:[(0,s.jsxs)("div",{children:[s.jsx("h1",{className:"text-2xl font-bold tracking-tight",children:r("setting.subscriptionStatus.subscription.title")}),s.jsx("h2",{className:"text-muted-foreground mt-2",children:r("setting.subscriptionStatus.subscription.description")})]}),s.jsx(x.Z,{SubscriptionPackages:t||[],conversionRate:e.data})]})]})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[9379,5063,4916,9467,4859,3832,8465,5857,6666,9965,7496,9429],()=>r(40092));module.exports=s})();