"use strict";(()=>{var e={};e.id=318,e.ids=[318],e.modules={20399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},56400:(e,A,o)=>{o.r(A),o.d(A,{originalPathname:()=>S,patchFetch:()=>B,requestAsyncStorage:()=>u,routeModule:()=>l,serverHooks:()=>g,staticGenerationAsyncStorage:()=>C});var t={};o.r(t),o.d(t,{GET:()=>s,dynamic:()=>c});var a=o(73278),r=o(45002),i=o(54877),p=o(95774);let n=Buffer.from("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","base64");function s(){return new p.NextResponse(n,{headers:{"Content-Type":"image/x-icon","Cache-Control":"public, immutable, no-transform, max-age=31536000"}})}let c="force-static",l=new a.AppRouteRouteModule({definition:{kind:r.x.APP_ROUTE,page:"/[locale]/icon.ico/route",pathname:"/[locale]/icon.ico",filename:"icon",bundlePath:"app/[locale]/icon.ico/route"},resolvedPagePath:"next-metadata-route-loader?page=%2F%5Blocale%5D%2Ficon.ico%2Froute&filePath=C%3A%5C_PRIVATE%5CProperty%20Plaza%20-%20Seekers%5Cproperty-plaza%5Capp%5C%5Blocale%5D%5Cicon.ico&isDynamic=0!?__next_metadata_route__",nextConfigOutput:"",userland:t}),{requestAsyncStorage:u,staticGenerationAsyncStorage:C,serverHooks:g}=l,S="/[locale]/icon.ico/route";function B(){return(0,i.patchFetch)({serverHooks:g,staticGenerationAsyncStorage:C})}}};var A=require("../../../webpack-runtime.js");A.C(e);var o=e=>A(A.s=e),t=A.X(0,[9379,239],()=>o(56400));module.exports=t})();