(()=>{var e={};e.id=329,e.ids=[329],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},85726:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>d,originalPathname:()=>u,pages:()=>p,routeModule:()=>m,tree:()=>c}),r(43038),r(55695),r(3929),r(84448),r(81729),r(90996);var i=r(30170),s=r(45002),a=r(83876),o=r.n(a),n=r(66299),l={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>n[e]);r.d(t,l);let c=["",{children:["[locale]",{children:["(user-profile)",{children:["notification",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,43038)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user-profile)\\notification\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,55695)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user-profile)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,3929)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user-profile)\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,80603))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,84448)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,81729)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,90996,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],p=["C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\(user-profile)\\notification\\page.tsx"],u="/[locale]/(user-profile)/notification/page",d={require:r,loadChunk:()=>Promise.resolve()},m=new i.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/[locale]/(user-profile)/notification/page",pathname:"/[locale]/notification",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},89449:(e,t,r)=>{let i={ace39bf07124e0ba39e8486050a53bc79b0621b3:()=>Promise.resolve().then(r.bind(r,18714)).then(e=>e.default)};async function s(e,...t){return(await i[e]()).apply(null,t)}e.exports={ace39bf07124e0ba39e8486050a53bc79b0621b3:s.bind(null,"ace39bf07124e0ba39e8486050a53bc79b0621b3")}},26090:(e,t,r)=>{Promise.resolve().then(r.bind(r,94242)),Promise.resolve().then(r.bind(r,17027)),Promise.resolve().then(r.bind(r,26793)),Promise.resolve().then(r.bind(r,70697)),Promise.resolve().then(r.bind(r,92941))},94242:(e,t,r)=>{"use strict";r.d(t,{default:()=>x});var i=r(97247),s=r(84879),a=r(2704),o=r(47751);let n=o.z.object({soundNotifications:o.z.boolean(),emailNewMessages:o.z.boolean(),emailNewProperties:o.z.boolean(),priceChangeAlerts:o.z.boolean(),newsletters:o.z.boolean(),specialOffers:o.z.boolean(),surveys:o.z.boolean()});var l=r(34631),c=r(28964),p=r(52208),u=r(80526),d=r(37872),m=r(10906),f=r(92894);function x(){let e=(0,s.useTranslations)("seeker"),[t,r]=(0,c.useState)(!1),o=(0,f.L)(e=>e.seekers),x=(0,d.K)(),g=(0,a.cI)({resolver:(0,l.F)(n),defaultValues:{soundNotifications:o.setting.soundNotif,emailNewMessages:o.setting.messageNotif,emailNewProperties:o.setting.priceAlertNotif,priceChangeAlerts:o.setting.priceAlertNotif,newsletters:o.setting.newsletterNotif,specialOffers:o.setting.specialOfferNotif,surveys:o.setting.surveyNotif}}),{toast:h}=(0,m.pm)(),y=[{name:"soundNotifications",label:e("setting.notification.soundNotification.title"),description:e("setting.notification.soundNotification.description"),form:g,key:"sound_notif"},{name:"emailNewMessages",label:e("setting.notification.emailForNewMessages.title"),description:e("setting.notification.emailForNewMessages.description"),form:g,key:"message_notif"},{name:"emailNewProperties",label:e("setting.notification.emailForNewProperties.title"),description:e("setting.notification.emailForNewProperties.description"),form:g,key:"property_notif"},{name:"priceChangeAlerts",label:e("setting.notification.priceChangeAlert.title"),description:e("setting.notification.priceChangeAlert.description"),form:g,key:"price_alert_notif"},{name:"newsletters",label:e("setting.notification.newsletter.title"),description:e("setting.notification.newsletter.description"),form:g,key:"newsletter_notif"},{name:"specialOffers",label:e("setting.notification.specialOffer.title"),description:e("setting.notification.specialOffer.description"),form:g,key:"special_offer_notif"},{name:"surveys",label:e("setting.notification.surveys.title"),description:e("setting.notification.surveys.description"),form:g,key:"survey_notif"}],b=async(t,r,i,s)=>{try{await x.mutateAsync({settings:{[i]:s}}),h({title:e("success.updateNotification.title",{field:t})}),g.setValue(r.name,s)}catch(r){h({title:e("error.updateNotification.title",{field:t})})}};return i.jsx(i.Fragment,{children:i.jsx(p.l0,{...g,children:i.jsx("form",{onSubmit:g.handleSubmit(e=>{r(!0),setTimeout(()=>{r(!1)},1e3)}),className:"space-y-8 pb-8",children:i.jsx("div",{className:"space-y-4",children:y.map((e,t)=>i.jsx(p.Wi,{control:g.control,name:e.name,render:({field:t})=>(0,i.jsxs)(p.xJ,{className:"flex flex-row items-center justify-between rounded-lg border p-4",children:[(0,i.jsxs)("div",{className:"space-y-0.5",children:[i.jsx(p.lX,{className:"text-base",children:e.label}),i.jsx(p.pf,{children:e.description})]}),i.jsx(p.NI,{children:i.jsx(u.r,{checked:t.value,className:"data-[state=checked]:bg-seekers-primary",onCheckedChange:t=>b(e.label,{form:g,name:e.name},e.key,t)})})]})},t))})})})})}},37872:(e,t,r)=>{"use strict";r.d(t,{K:()=>c});var i=r(43189),s=r(41755),a=r(88111),o=r(23866),n=r(10906),l=r(84879);function c(){let e=(0,s.NL)(),{toast:t}=(0,n.pm)(),r=(0,l.useTranslations)("universal");return(0,a.D)({mutationFn:e=>(0,i.f_)(e),onSuccess:()=>{e.invalidateQueries({queryKey:[o.J]}),t({title:r("success.updateUser")})},onError:e=>{let i=e.response.data;t({title:r("error.foundError"),description:i.message,variant:"destructive"})}})}},43038:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g,generateMetadata:()=>x});var i=r(72051),s=r(29507),a=r(83266),o=r(79438),n=r(35243),l=r(4459),c=r(53189),p=r(69385),u=r(93844);function d(){let e=(0,p.Z)("seeker");return(0,i.jsxs)(o.Z,{className:"mx-auto flex gap-2 items-end max-sm:px-0 space-y-6 pt-6 max-sm:pt-0",children:[i.jsx(l.vP,{className:"items-end -ml-2"}),i.jsx(n.aG,{className:"",children:(0,i.jsxs)(n.Jb,{className:"space-x-4 sm:gap-0",children:[i.jsx(n.gN,{className:"text-seekers-text font-medium text-sm",children:(0,i.jsxs)(u.rU,{href:"/",className:"flex gap-2.5 items-center",children:[i.jsx(c.Z,{className:"w-4 h-4",strokeWidth:1}),e("misc.home")]})}),i.jsx(n.bg,{className:"text-seekers-text font-medium text-sm w-3 h-fit text-center",children:"/"}),i.jsx(n.gN,{className:"capitalize text-seekers-text font-medium text-sm",children:e("accountAndProfile.notification")})]})})]})}let m=(0,r(45347).createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\app\[locale]\(user-profile)\notification\form\notification.form.tsx#default`);var f=r(92898);async function x(){let e=await (0,s.Z)("seeker"),t=await (0,a.Z)()||u.DI.defaultLocale,r=process.env.USER_DOMAIN||"https://www.property-plaza.com/";return{title:e("metadata.notificationSetting.title"),description:e("metadata.notificationSetting.description"),alternates:{canonical:r+t+"",languages:{en:r+"en"+f.Qk,id:r+"id"+f.Qk,"x-default":r+f.Qk.replace("/","")}},openGraph:{title:e("metadata.rootLayout.title"),description:e("metadata.rootLayout.description"),images:[{url:r+"og.png",width:1200,height:630,alt:"Property Plaza"}],type:"website",url:r+t,countryName:"Indonesia",emails:"<EMAIL>",locale:t,alternateLocale:u.DI.locales,siteName:"Property plaza"},applicationName:"Property plaza",twitter:{card:"summary_large_image",title:e("metadata.rootLayout.title"),description:e("metadata.rootLayout.description"),images:[r+"og.jpg"]},robots:{index:!1,follow:!1,nocache:!1}}}function g(){let e=(0,p.Z)("seeker");return(0,i.jsxs)(i.Fragment,{children:[i.jsx(d,{}),(0,i.jsxs)(o.Z,{className:"space-y-8 my-8 max-sm:px-0",children:[(0,i.jsxs)("div",{children:[i.jsx("h1",{className:"text-2xl font-bold tracking-tight",children:e("setting.profile.notification.title")}),i.jsx("h2",{className:"text-muted-foreground mt-2",children:e("settings.profile.notification.description")})]}),i.jsx(m,{})]})]})}},18714:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var i=r(28713);r(9640);var s=r(53020);async function a(e,t,r){let i=(0,s.cookies)(),a=i.get("tkn")?.value;try{let i=await fetch(e,{method:t,headers:{Authorization:`Bearer ${a}`,"Content-Type":"application/json"},...r});if(!i.ok)return{data:null,meta:void 0,error:{status:i.status,name:i.statusText,message:await i.text()||"Unexpected error",details:{}}};let s=await i.json();if(s.error)return{data:null,meta:void 0,error:s.error};return{data:s.data,meta:s.meta,error:void 0}}catch(e){return{data:null,meta:void 0,error:{status:500,details:{cause:e.cause},message:e.message,name:e.name}}}}(0,r(83557).h)([a]),(0,i.j)("ace39bf07124e0ba39e8486050a53bc79b0621b3",a)},29507:(e,t,r)=>{"use strict";r.d(t,{Z:()=>o});var i=r(26269),s=r(95817),a=r(60434),o=(0,i.cache)(async function(e){let t,r;"string"==typeof e?t=e:e&&(r=e.locale,t=e.namespace);let i=await (0,a.Z)(r);return(0,s.eX)({...i,namespace:t,messages:i.messages})})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[9379,5063,4916,9467,4859,5268,3832,6666,9965,7496],()=>r(85726));module.exports=i})();