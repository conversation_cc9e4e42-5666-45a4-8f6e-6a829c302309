(()=>{var e={};e.id=4617,e.ids=[4617],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27790:e=>{"use strict";e.exports=require("assert")},78893:e=>{"use strict";e.exports=require("buffer")},61282:e=>{"use strict";e.exports=require("child_process")},84770:e=>{"use strict";e.exports=require("crypto")},17702:e=>{"use strict";e.exports=require("events")},92048:e=>{"use strict";e.exports=require("fs")},32615:e=>{"use strict";e.exports=require("http")},35240:e=>{"use strict";e.exports=require("https")},98216:e=>{"use strict";e.exports=require("net")},19801:e=>{"use strict";e.exports=require("os")},55315:e=>{"use strict";e.exports=require("path")},76162:e=>{"use strict";e.exports=require("stream")},82452:e=>{"use strict";e.exports=require("tls")},74175:e=>{"use strict";e.exports=require("tty")},17360:e=>{"use strict";e.exports=require("url")},21764:e=>{"use strict";e.exports=require("util")},71568:e=>{"use strict";e.exports=require("zlib")},9775:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>p,originalPathname:()=>m,pages:()=>d,routeModule:()=>x,tree:()=>c}),r(73138),r(46227),r(84448),r(81729),r(90996);var s=r(30170),i=r(45002),a=r(83876),l=r.n(a),o=r(66299),n={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>o[e]);r.d(t,n);let c=["",{children:["[locale]",{children:["verify",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,73138)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\verify\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,46227)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\verify\\layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,80603))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,84448)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,81729)),"C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\not-found.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{"not-found":[()=>Promise.resolve().then(r.t.bind(r,90996,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,57481))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\app\\[locale]\\verify\\page.tsx"],m="/[locale]/verify/page",p={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/[locale]/verify/page",pathname:"/[locale]/verify",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},89449:(e,t,r)=>{let s={ace39bf07124e0ba39e8486050a53bc79b0621b3:()=>Promise.resolve().then(r.bind(r,18714)).then(e=>e.default)};async function i(e,...t){return(await s[e]()).apply(null,t)}e.exports={ace39bf07124e0ba39e8486050a53bc79b0621b3:i.bind(null,"ace39bf07124e0ba39e8486050a53bc79b0621b3")}},97782:(e,t,r)=>{Promise.resolve().then(r.bind(r,37072)),Promise.resolve().then(r.bind(r,15589)),Promise.resolve().then(r.bind(r,26793)),Promise.resolve().then(r.bind(r,70697)),Promise.resolve().then(r.t.bind(r,35268,23))},14038:(e,t,r)=>{Promise.resolve().then(r.bind(r,84059)),Promise.resolve().then(r.bind(r,78781)),Promise.resolve().then(r.bind(r,91860)),Promise.resolve().then(r.bind(r,33626)),Promise.resolve().then(r.bind(r,26793)),Promise.resolve().then(r.bind(r,70697)),Promise.resolve().then(r.bind(r,92941)),Promise.resolve().then(r.t.bind(r,15889,23)),Promise.resolve().then(r.bind(r,62648))},37072:(e,t,r)=>{"use strict";r.d(t,{default:()=>d});var s=r(97247),i=r(22288),a=r(58053),l=r(84879),o=r(75476),n=r(28964);function c({src:e,poster:t,className:r="",autoPlay:i=!0,muted:a=!0,loop:l=!0,playsInline:o=!0,controls:c=!1,preload:d="metadata",lazy:m=!0,fallbackContent:p}){let[x,u]=(0,n.useState)(!m),[h,f]=(0,n.useState)(!0),[g,b]=(0,n.useState)(!1),v=(0,n.useRef)(null);return(0,s.jsxs)("div",{ref:v,className:`relative ${r}`,children:[h&&x&&s.jsx("div",{className:"absolute inset-0 bg-gray-100 flex items-center justify-center z-10",children:(0,s.jsxs)("div",{className:"flex flex-col items-center space-y-2",children:[s.jsx("div",{className:"w-8 h-8 border-2 border-seekers-primary border-t-transparent rounded-full animate-spin"}),s.jsx("p",{className:"text-sm text-gray-500",children:"Loading video..."})]})}),g&&s.jsx("div",{className:"absolute inset-0 bg-gray-100 flex items-center justify-center z-10",children:(0,s.jsxs)("div",{className:"text-center space-y-2",children:[s.jsx("div",{className:"text-4xl",children:"⚠️"}),s.jsx("p",{className:"text-sm text-gray-500",children:"Video could not be loaded"}),p]})}),t&&!x&&s.jsx("img",{src:t,alt:"Video preview",className:"w-full h-full object-cover rounded-lg"}),x&&(0,s.jsxs)("video",{autoPlay:i,muted:a,loop:l,playsInline:o,controls:c,preload:d,poster:t,className:"w-full h-full object-cover rounded-lg",onLoadStart:()=>{f(!0),b(!1)},onCanPlay:()=>{f(!1)},onError:()=>{f(!1),b(!0)},children:[s.jsx("source",{src:e,type:"video/mp4"}),s.jsx("source",{src:e.replace(".mp4",".webm"),type:"video/webm"}),s.jsx("div",{className:"flex items-center justify-center h-full bg-gray-100",children:(0,s.jsxs)("div",{className:"text-center space-y-2",children:[s.jsx("div",{className:"text-4xl",children:"\uD83D\uDCF9"}),s.jsx("p",{className:"text-sm text-gray-500",children:"Your browser doesn't support video playback"}),p]})})]})]})}function d(){let e=(0,l.useTranslations)("verify");return s.jsx("section",{className:"bg-gradient-to-br from-seekers-primary/5 to-seekers-primary/10 py-6 md:py-10 lg:py-16","aria-labelledby":"hero-title",children:s.jsx(i.Z,{children:(0,s.jsxs)("div",{className:"flex flex-col lg:grid lg:grid-cols-2 gap-4 md:gap-6 lg:gap-8 items-center",children:[(0,s.jsxs)("div",{className:"space-y-3 md:space-y-4 lg:space-y-5 order-2 lg:order-1 text-center lg:text-left",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center lg:justify-start gap-2 text-red-600 font-semibold",children:[s.jsx("span",{className:"text-xl md:text-2xl",children:"\uD83D\uDEA8"}),s.jsx("span",{className:"text-sm md:text-base",children:e("hero.badge")})]}),s.jsx("h1",{id:"hero-title",className:"text-2xl md:text-3xl lg:text-4xl xl:text-5xl font-bold text-seekers-text leading-tight",children:e("hero.title")}),s.jsx("p",{className:"text-base md:text-lg text-seekers-text-light leading-relaxed",children:e("hero.subtitle")}),s.jsx("div",{className:"space-y-3 md:space-y-4",children:[e("hero.benefits.0"),e("hero.benefits.1"),e("hero.benefits.2"),e("hero.benefits.3")].map((e,t)=>(0,s.jsxs)("div",{className:"flex items-start lg:items-center gap-3 justify-center lg:justify-start",children:[s.jsx("span",{className:"text-seekers-primary text-lg mt-0.5 lg:mt-0",children:"•"}),s.jsx("span",{className:"text-sm md:text-base text-seekers-text font-medium text-left",children:e})]},t))}),s.jsx("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 md:p-4",children:(0,s.jsxs)("p",{className:"text-red-800 font-medium text-sm md:text-base text-center lg:text-left",children:[e("hero.warning")," ",e("hero.cta")]})}),(0,s.jsxs)("div",{className:"pt-4 md:pt-6 text-center",children:[s.jsx(a.z,{size:"lg",className:"bg-seekers-primary hover:bg-seekers-primary/90 text-white px-6 md:px-8 py-3 md:py-4 text-base md:text-lg font-semibold shadow-lg hover:shadow-xl transition-all duration-200 w-full sm:w-auto",asChild:!0,children:s.jsx(o.rU,{href:"#booking-form",children:e("cta.bookInspection")})}),s.jsx("p",{className:"text-xs md:text-sm text-seekers-text-light mt-2 md:mt-3",children:e("footnote")})]})]}),s.jsx("div",{className:"relative flex justify-center order-1 lg:order-2 w-full",children:s.jsx("div",{className:"w-full max-w-xs sm:max-w-sm md:max-w-md lg:max-w-xs xl:max-w-sm",style:{aspectRatio:"9/16"},children:s.jsx(c,{src:"https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",poster:"https://images.pexels.com/photos/1396122/pexels-photo-1396122.jpeg?auto=compress&cs=tinysrgb&w=400&h=711",className:"w-full h-full shadow-lg",autoPlay:!0,muted:!0,loop:!0,playsInline:!0,controls:!1,preload:"metadata",lazy:!1,fallbackContent:(0,s.jsxs)("div",{className:"text-center space-y-2 p-4",children:[s.jsx("div",{className:"text-4xl",children:"\uD83D\uDCF1"}),s.jsx("p",{className:"text-neutral-500 font-medium",children:"Villa Inspection Video"}),(0,s.jsxs)("p",{className:"text-sm text-neutral-400 leading-relaxed",children:["Professional villa inspection",s.jsx("br",{}),"process and red flags",s.jsx("br",{}),"identification"]})]})})})})]})})})}},15589:(e,t,r)=>{"use strict";r.d(t,{default:()=>A});var s=r(97247),i=r(28964),a=r(22288),l=r(84879),o=r(26323);let n=(0,o.Z)("Calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]);var c=r(49256);let d=(0,o.Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);function m(){let e=(0,l.useTranslations)("verify"),t=[{icon:s.jsx(n,{className:"w-6 h-6"}),title:e("howItWorks.steps.book.title"),description:e("howItWorks.steps.book.description"),result:e("howItWorks.steps.book.result")},{icon:s.jsx(c.Z,{className:"w-6 h-6"}),title:e("howItWorks.steps.inspect.title"),description:e("howItWorks.steps.inspect.description"),result:[e("howItWorks.steps.inspect.result.basic"),e("howItWorks.steps.inspect.result.standard"),e("howItWorks.steps.inspect.result.premium")]},{icon:s.jsx(d,{className:"w-6 h-6"}),title:e("howItWorks.steps.report.title"),description:e("howItWorks.steps.report.description"),result:[e("howItWorks.steps.report.result.basic"),e("howItWorks.steps.report.result.standard"),e("howItWorks.steps.report.result.premium")]}];return s.jsx("section",{className:"bg-seekers-foreground/50 py-12",children:(0,s.jsxs)(a.Z,{children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[s.jsx("h2",{className:"text-3xl md:text-4xl font-bold text-seekers-text mb-4",children:e("howItWorks.title")}),s.jsx("p",{className:"text-lg text-seekers-text-light",children:e("howItWorks.subtitle")})]}),s.jsx("div",{className:"relative max-w-[1200px] mx-auto px-4",children:s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 relative",children:t.map((e,t)=>(0,s.jsxs)("div",{className:"group relative bg-white p-6 rounded-2xl border border-gray-100   hover:border-seekers-primary/20 transition-all duration-300 shadow-sm hover:shadow-md   flex flex-col",children:[(0,s.jsxs)("div",{className:"flex items-center gap-3 mb-4",children:[(0,s.jsxs)("div",{className:"relative shrink-0",children:[s.jsx("div",{className:"w-12 h-12 bg-seekers-primary/10 rounded-xl flex items-center justify-center   text-seekers-primary group-hover:scale-110 transition-transform duration-300",children:e.icon}),s.jsx("div",{className:"absolute inset-0 bg-seekers-primary/5 blur-xl rounded-full   group-hover:blur-2xl transition-all duration-300"})]}),s.jsx("h4",{className:"text-lg font-semibold text-gray-900   group-hover:text-seekers-primary transition-colors duration-300",children:e.title})]}),s.jsx("p",{className:"text-gray-600 text-sm leading-relaxed flex-1 whitespace-pre-line text-left",children:e.description}),s.jsx("div",{className:"absolute inset-0 border-2 border-transparent   group-hover:border-seekers-primary/20 rounded-2xl transition-colors duration-300"})]},t))})}),(0,s.jsxs)("div",{className:"mt-12 bg-seekers-primary/5 rounded-lg p-6 md:p-8 text-center max-w-4xl mx-auto",children:[s.jsx("h3",{className:"text-lg md:text-xl font-semibold text-seekers-text mb-4",children:e("howItWorks.whyChoose.title")}),s.jsx("p",{className:"text-base text-seekers-text-light",children:e("howItWorks.whyChoose.description")})]})]})})}var p=r(58053),x=r(25008),u=r(98563);function h({conversions:e,onSelectTier:t}){let{currency:r,isLoading:o}=(0,u.R)(),[n,c]=(0,i.useState)("IDR"),d=(0,l.useLocale)(),m=(0,l.useTranslations)("verify"),h=[{id:"basic",name:m("pricing.tiers.basic.name"),price:19e5,features:[m("pricing.tiers.basic.features.0"),m("pricing.tiers.basic.features.1"),m("pricing.tiers.basic.features.2"),m("pricing.tiers.basic.features.3"),m("pricing.tiers.basic.features.4")]},{id:"standard",name:m("pricing.tiers.standard.name"),price:45e5,popular:!0,features:[m("pricing.tiers.standard.features.0"),m("pricing.tiers.standard.features.1"),m("pricing.tiers.standard.features.2"),m("pricing.tiers.standard.features.3"),m("pricing.tiers.standard.features.4")]},{id:"premium",name:m("pricing.tiers.premium.name"),price:7e6,features:[m("pricing.tiers.premium.features.0"),m("pricing.tiers.premium.features.1"),m("pricing.tiers.premium.features.2"),m("pricing.tiers.premium.features.3")]}],f=t=>{let r=t*(e[n]||1);return(0,x.xG)(r,n,d)};return s.jsx("section",{className:"py-16 bg-white","aria-labelledby":"pricing-title",children:(0,s.jsxs)(a.Z,{children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[s.jsx("h2",{id:"pricing-title",className:"text-3xl md:text-4xl font-bold text-seekers-text mb-4",children:m("pricing.title")}),s.jsx("p",{className:"text-lg text-seekers-text-light",children:m("pricing.subtitle")})]}),s.jsx("div",{className:"grid sm:grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8 max-w-5xl mx-auto items-stretch",children:h.map(e=>(0,s.jsxs)("div",{className:`relative bg-white rounded-xl border-2 p-6 lg:p-8 transition-all duration-200 hover:shadow-lg flex flex-col h-full ${e.popular?"border-seekers-primary shadow-lg":"border-neutral-200"}`,itemScope:!0,itemType:"https://schema.org/Offer",children:[e.popular&&s.jsx("div",{className:"absolute -top-3 left-1/2 transform -translate-x-1/2",children:s.jsx("span",{className:"bg-seekers-primary text-white px-4 py-1 rounded-full text-sm font-semibold",children:m("pricing.tiers.standard.popular")})}),(0,s.jsxs)("div",{className:"text-center mb-6",children:[s.jsx("h3",{className:"text-xl font-bold text-seekers-text mb-1",itemProp:"name",children:e.name}),s.jsx("p",{className:"text-sm text-seekers-text-light mb-3 whitespace-pre-line",itemProp:"description",children:m(`pricing.tiers.${e.id}.subtitle`)}),(0,s.jsxs)("div",{className:"text-3xl font-bold text-seekers-primary",itemProp:"price",content:e.price.toString(),children:[f(e.price),s.jsx("meta",{itemProp:"priceCurrency",content:"IDR"})]})]}),(0,s.jsxs)("div",{className:"flex-grow",children:[s.jsx("ul",{className:"space-y-3",children:e.features.map((e,t)=>(0,s.jsxs)("li",{className:"flex items-start gap-3",children:[s.jsx("span",{className:"text-seekers-primary mt-1",children:"✓"}),s.jsx("span",{className:"text-seekers-text-light",children:e})]},t))}),s.jsx(p.z,{onClick:()=>t(e),className:`w-full py-3 font-semibold transition-all duration-200 ${"premium"===e.id?"mt-9":"mt-8"} ${e.popular?"bg-seekers-primary hover:bg-seekers-primary/90 text-white":"bg-neutral-100 hover:bg-neutral-200 text-seekers-text border border-neutral-300"}`,variant:e.popular?"default":"outline",children:m("pricing.cta",{tierName:e.name})}),s.jsx("div",{className:"text-xs text-gray-500 italic text-center min-h-[1.5rem] pt-3 flex items-center justify-center",children:"premium"===e.id&&m(`pricing.tiers.${e.id}.footnote`)})]}),s.jsx("div",{className:"flex-grow"})]},e.id))})]})})}var f=r(2704),g=r(34631),b=r(28050),v=r(52208),y=r(10906),j=r(4955),w=r(16626),N=r.n(w),k=r(47751),P=r(93572),I=r(94049);function C({form:e,label:t,name:r,placeholder:i,description:a,selectList:l,children:o,disabled:n,containerClassName:c,inputContainer:d,inputProps:m,labelClassName:p,variant:u}){return s.jsx(v.Wi,{control:e.control,name:r,render:({field:e})=>s.jsx(P.Z,{label:t,description:a,labelClassName:(0,x.cn)("float"==u?"absolute -top-2 left-2 px-1 text-xs bg-background z-10":"",p),containerClassName:c,variant:u,children:(0,s.jsxs)(I.Ph,{onValueChange:e.onChange,name:e.name,value:e.value,disabled:e.disabled||n,children:[s.jsx(v.NI,{children:s.jsx(I.i4,{className:(0,x.cn)("border-none focus:outline-none shadow-none focus-visible:ring-0 w-full","float"==u?"px-0":"",m?.className),children:s.jsx(I.ki,{placeholder:i})})}),(0,s.jsxs)(I.Bw,{onClick:e=>{e.stopPropagation()},children:[Array.isArray(l)&&l.map(e=>s.jsx(I.Ql,{onClick:e=>{e.stopPropagation()},value:e.value,children:e.content},e.id)),o]})]})})})}var z=r(74993);let T=e=>z.v.post("/verify-booking-checkout",e);var _=r(88111);function R({selectedTier:e,conversions:t}){let{executeRecaptcha:r}=(0,b.CL)(),{toast:i}=(0,y.pm)(),o=(0,l.useTranslations)("verify"),n=function(){let e=(0,l.useTranslations)("seeker");return k.z.object({firstName:k.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.firstName")})}).min(2,e("form.utility.minimumLength",{field:e("form.field.firstName"),length:2})),lastName:k.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.lastName")})}).min(2,e("form.utility.minimumLength",{field:e("form.field.lastName"),length:2})),email:k.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.email")})}).email({message:e("form.utility.invalidFormat",{field:e("form.field.email")})}),whatsappNumber:k.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.phoneOrWhatsappNumber")})}).refine(e=>N()(e).isValid,e("form.utility.invalidFormat",{field:e("form.field.phoneOrWhatsappNumber")})),villaAddress:k.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.villaAddress")})}).min(10,e("form.utility.minimumLength",{field:e("form.field.villaAddress"),length:10})),preferredDate:k.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.preferredDate")})}).date(e("form.utility.invalidFormat",{field:e("form.field.preferredDate")})),tier:k.z.string({message:e("form.utility.fieldRequired",{field:e("form.field.tier")})})})}(),c=(0,_.D)({mutationFn:async e=>await T(e)}),d=[{id:"basic",content:o("booking.form.tier.options.basic"),value:"basic"},{id:"smart",content:o("booking.form.tier.options.smart"),value:"standard"},{id:"full-shield",content:o("booking.form.tier.options.fullShield"),value:"premium"}],m=(0,f.cI)({resolver:(0,g.F)(n),defaultValues:{firstName:"",lastName:"",email:"",whatsappNumber:"",villaAddress:"",preferredDate:"",tier:e?.id||""}}),x=async e=>{try{let t=await r("verify_booking"),s={...e,recaptchaToken:t},i=await c.mutateAsync(s);if(console.log(i),i.data?.url)window.location.href=i.data?.url;else throw Error(i.data?.url||"Failed to create checkout session")}catch(e){console.error("Checkout error:",e),i({title:o("booking.form.error.title"),description:o("booking.form.error.message"),variant:"destructive"})}},u=new Date;u.setDate(u.getDate()+1);let h=u.toISOString().split("T")[0];return s.jsx("section",{id:"booking-form",className:"py-16 bg-white isolate","aria-labelledby":"booking-title",children:s.jsx(a.Z,{children:(0,s.jsxs)("div",{className:"max-w-lg mx-auto",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[s.jsx("h2",{id:"booking-title",className:"text-3xl md:text-4xl font-bold text-seekers-text mb-4",children:o("booking.title")}),s.jsx("p",{className:"text-lg text-seekers-text-light",children:o("booking.subtitle")})]}),s.jsx("div",{className:"w-full space-y-6",children:s.jsx(v.l0,{...m,children:(0,s.jsxs)("form",{onSubmit:m.handleSubmit(x),className:"space-y-8",children:[(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[s.jsx(j.Z,{form:m,label:o("booking.form.firstName.label"),name:"firstName",placeholder:"",type:"text",variant:"float",labelClassName:"text-xs text-seekers-text-light font-normal"}),s.jsx(j.Z,{form:m,label:o("booking.form.lastName.label"),name:"lastName",placeholder:"",type:"text",variant:"float",labelClassName:"text-xs text-seekers-text-light font-normal"})]}),s.jsx(j.Z,{form:m,label:o("booking.form.email.label"),name:"email",placeholder:"",type:"email",variant:"float",labelClassName:"text-xs text-seekers-text-light font-normal"}),s.jsx(j.Z,{form:m,label:o("booking.form.whatsappNumber.label"),name:"whatsappNumber",placeholder:"",type:"tel",variant:"float",labelClassName:"text-xs text-seekers-text-light font-normal"}),s.jsx(j.Z,{form:m,label:o("booking.form.villaAddress.label"),name:"villaAddress",placeholder:"",type:"text",variant:"float",labelClassName:"text-xs text-seekers-text-light font-normal"}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[s.jsx(j.Z,{form:m,label:o("booking.form.preferredDate.label"),name:"preferredDate",placeholder:"",type:"date",variant:"float",labelClassName:"text-xs text-seekers-text-light font-normal",inputProps:{min:h}}),s.jsx(C,{form:m,label:o("booking.form.tier.label"),name:"tier",placeholder:o("booking.form.tier.placeholder"),selectList:d,variant:"float",labelClassName:"text-xs text-seekers-text-light font-normal"})]}),s.jsx(p.z,{type:"submit",className:"w-full",loading:c.isPending,children:o("booking.form.cta")}),s.jsx("div",{className:"text-xs text-neutral space-x-1 !mt-2 text-center",children:s.jsx("span",{children:o("booking.form.disclaimer")})})]})})})]})})})}var S=r(74974);function q(){let e=(0,l.useTranslations)("verify.socialProof"),t=[{name:e("reviews.review1.name"),location:e("reviews.review1.location"),rating:5,text:e("reviews.review1.text"),propertyType:e("reviews.review1.propertyType")},{name:e("reviews.review2.name"),location:e("reviews.review2.location"),rating:5,text:e("reviews.review2.text"),propertyType:e("reviews.review2.propertyType")},{name:e("reviews.review3.name"),location:e("reviews.review3.location"),rating:5,text:e("reviews.review3.text"),propertyType:e("reviews.review3.propertyType")}],r=e=>Array.from({length:5},(t,r)=>s.jsx(S.Z,{className:`w-4 h-4 ${r<e?"fill-yellow-400 text-yellow-400":"fill-gray-200 text-gray-200"}`},r));return s.jsx("section",{className:"py-16 bg-gray-50",children:(0,s.jsxs)(a.Z,{children:[(0,s.jsxs)("div",{className:"text-center mb-12",children:[s.jsx("h2",{className:"text-3xl font-bold text-seekers-text mb-4",children:e("title")}),s.jsx("p",{className:"text-lg text-seekers-text-light max-w-3xl mx-auto",children:e("subtitle")})]}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:t.map((e,t)=>s.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow duration-300 flex flex-col h-full",children:(0,s.jsxs)("div",{className:"flex items-start gap-4 flex-1",children:[s.jsx("div",{className:"flex-shrink-0",children:s.jsx("div",{className:"w-12 h-12 bg-gradient-to-br from-seekers-primary to-seekers-primary/80 rounded-full flex items-center justify-center text-white font-semibold text-base",children:e.name.charAt(0)})}),(0,s.jsxs)("div",{className:"flex-1 min-w-0 flex flex-col",children:[s.jsx("div",{className:"flex items-center mb-3",children:s.jsx("div",{className:"flex space-x-1",children:r(e.rating)})}),(0,s.jsxs)("blockquote",{className:"text-seekers-text mb-4 leading-relaxed text-sm flex-1",children:['"',e.text,'"']}),(0,s.jsxs)("div",{className:"space-y-1 mt-auto",children:[s.jsx("p",{className:"text-sm font-semibold text-seekers-text",children:e.name}),(0,s.jsxs)("p",{className:"text-xs text-seekers-text-light",children:[e.location," • ",e.propertyType]})]})]})]})},t))})]})})}function D(){let e=(0,l.useTranslations)("verify");return s.jsx("section",{className:"py-12 bg-gray-50 border-t border-gray-200",children:s.jsx(a.Z,{children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:e("disclaimer.title")}),s.jsx("p",{className:"text-sm text-gray-600 leading-relaxed",children:e("disclaimer.content")})]})})})}function A({conversions:e}){let[t,r]=(0,i.useState)();return(0,s.jsxs)("main",{className:"min-h-screen",children:[s.jsx(m,{}),s.jsx(h,{conversions:e,onSelectTier:e=>{r(e),setTimeout(()=>{!function(e,t=80,r=20){let s=document.getElementById(e);if(s){let e=s.offsetTop-t-r;window.scrollTo({top:Math.max(0,e),behavior:"smooth"})}}("booking-title",80,20)},100)}}),s.jsx(R,{selectedTier:t,conversions:e}),s.jsx(q,{}),s.jsx(D,{})]})}},46227:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(72051),i=r(81413),a=r(98798),l=r(56886);r(26269);var o=r(86677);function n({children:e}){return(0,s.jsxs)(s.Fragment,{children:[s.jsx(o.Z,{isSeeker:!0}),s.jsx(a.Z,{}),s.jsx("div",{className:"w-full sticky top-0 z-10 bg-white !mt-0",children:s.jsx(l.Z,{currency_:"IDR",localeId:"en"})}),s.jsx("div",{className:"!mt-0 relative min-h-screen max-sm:max-w-screen-sm",children:e}),s.jsx("div",{className:"!mt-0",children:s.jsx(i.Z,{})})]})}},73138:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p,generateMetadata:()=>m});var s=r(72051),i=r(70276),a=r(45347);let l=(0,a.createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\app\[locale]\verify\verify-page-client.tsx#default`);var o=r(29507),n=r(78573),c=r.n(n);let d=(0,a.createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\app\[locale]\verify\verify-hero.tsx#default`);async function m({params:e}){let t=await (0,o.Z)("verify.seo"),r=process.env.NEXT_PUBLIC_BASE_URL||"https://property-plaza.com",s="/verify";return{title:t("title"),description:t("description"),keywords:t("keywords"),openGraph:{title:t("title"),description:t("description"),type:"website",url:`${r}/${e.locale}${s}`,images:[{url:`${r}/og-verify.png`,width:1200,height:630,alt:"Bali Villa Inspection Service - Property Plaza"}]},twitter:{card:"summary_large_image",title:t("title"),description:t("description"),images:[`${r}/og-verify.png`]},alternates:{canonical:`${r}/${e.locale}${s}`,languages:{id:`${r}/id${s}`,en:`${r}/en${s}`,"x-default":`${r}${s}`}},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}}}async function p(){let e=(await (0,i.T)()).data||{IDR:1,EUR:63e-6,USD:67e-6,GBP:53e-6,AUD:99e-6};return(0,s.jsxs)(s.Fragment,{children:[s.jsx(c(),{id:"json-ld-schema",type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"Service",name:"Bali Villa Inspection Service",description:"Professional villa inspection service in Bali. We inspect legal/title red flags, structural issues & hidden costs, then deliver full video + risk report.",provider:{"@type":"Organization",name:"Property Plaza",url:"https://property-plaza.com"},areaServed:{"@type":"Place",name:"Bali, Indonesia"},serviceType:"Property Inspection",offers:[{"@type":"Offer",name:"Basic Package",price:"1900000",priceCurrency:"IDR",description:"Appointment & villa visit, rental agreement draft, landlord identity verification, photos of the property, short voice summary"},{"@type":"Offer",name:"Standard Package",price:"4500000",priceCurrency:"IDR",description:"Includes Basic Package, plus: general property inspection, written inspection report, video walkthrough of the property, call to discuss key findings"},{"@type":"Offer",name:"Premium Package",price:"7000000",priceCurrency:"IDR",description:"Includes Standard Package, plus: ownership verification via BPN (Indonesian Land Registry), long-term rental agreement template, priority booking for inspections"}]})}}),s.jsx(d,{}),s.jsx(l,{conversions:e})]})}},70276:(e,t,r)=>{"use strict";r.d(t,{T:()=>i});let s=process.env.CURRENCY_API+`latest?apikey=${process.env.CURRENCY_API_KEY}`,i=async e=>await fetch(s+`&currencies=EUR%2CUSD%2CAUD%2CIDR%2CGBP&base_currency=${e||"IDR"}`,{next:{revalidate:86400}}).then(e=>e.json())},18714:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(28713);r(9640);var i=r(53020);async function a(e,t,r){let s=(0,i.cookies)(),a=s.get("tkn")?.value;try{let s=await fetch(e,{method:t,headers:{Authorization:`Bearer ${a}`,"Content-Type":"application/json"},...r});if(!s.ok)return{data:null,meta:void 0,error:{status:s.status,name:s.statusText,message:await s.text()||"Unexpected error",details:{}}};let i=await s.json();if(i.error)return{data:null,meta:void 0,error:i.error};return{data:i.data,meta:i.meta,error:void 0}}catch(e){return{data:null,meta:void 0,error:{status:500,details:{cause:e.cause},message:e.message,name:e.name}}}}(0,r(83557).h)([a]),(0,s.j)("ace39bf07124e0ba39e8486050a53bc79b0621b3",a)},78573:(e,t,r)=>{"use strict";let{createProxy:s}=r(45347);e.exports=s("C:\\_PRIVATE\\Property Plaza - Seekers\\property-plaza\\node_modules\\next\\dist\\client\\script.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[9379,5063,4916,9467,4859,5268,3327,8530,7341,6666,9965,595],()=>r(9775));module.exports=s})();