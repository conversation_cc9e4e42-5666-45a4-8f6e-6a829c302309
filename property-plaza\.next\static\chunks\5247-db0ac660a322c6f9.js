(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5247],{48166:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(79205).Z)("<PERSON><PERSON>",[["path",{d:"M12 2a10 10 0 1 0 10 10 4 4 0 0 1-5-5 4 4 0 0 1-5-5",key:"laymnq"}],["path",{d:"M8.5 8.5v.01",key:"ue8clq"}],["path",{d:"M16 15.5v.01",key:"14dtrp"}],["path",{d:"M12 12v.01",key:"u5ubse"}],["path",{d:"M11 17v.01",key:"1hyl5a"}],["path",{d:"M7 14v.01",key:"uct60s"}]])},33702:function(e,t,r){!function(e){e.defineLocale("id",{months:"Jan<PERSON><PERSON>_Feb<PERSON><PERSON>_Maret_April_Mei_Juni_Juli_Agus<PERSON>_September_Oktober_November_Desember".split("_"),monthsShort:"Jan_Feb_Mar_Apr_Mei_Jun_Jul_Agt_Sep_Okt_Nov_Des".split("_"),weekdays:"Minggu_Senin_Selasa_Rabu_Kamis_Jumat_Sabtu".split("_"),weekdaysShort:"Min_Sen_Sel_Rab_Kam_Jum_Sab".split("_"),weekdaysMin:"Mg_Sn_Sl_Rb_Km_Jm_Sb".split("_"),longDateFormat:{LT:"HH.mm",LTS:"HH.mm.ss",L:"DD/MM/YYYY",LL:"D MMMM YYYY",LLL:"D MMMM YYYY [pukul] HH.mm",LLLL:"dddd, D MMMM YYYY [pukul] HH.mm"},meridiemParse:/pagi|siang|sore|malam/,meridiemHour:function(e,t){return(12===e&&(e=0),"pagi"===t)?e:"siang"===t?e>=11?e:e+12:"sore"===t||"malam"===t?e+12:void 0},meridiem:function(e,t,r){return e<11?"pagi":e<15?"siang":e<19?"sore":"malam"},calendar:{sameDay:"[Hari ini pukul] LT",nextDay:"[Besok pukul] LT",nextWeek:"dddd [pukul] LT",lastDay:"[Kemarin pukul] LT",lastWeek:"dddd [lalu pukul] LT",sameElse:"L"},relativeTime:{future:"dalam %s",past:"%s yang lalu",s:"beberapa detik",ss:"%d detik",m:"semenit",mm:"%d menit",h:"sejam",hh:"%d jam",d:"sehari",dd:"%d hari",M:"sebulan",MM:"%d bulan",y:"setahun",yy:"%d tahun"},week:{dow:0,doy:6}})}(r(77398))},49988:function(e,t,r){"use strict";function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}r.d(t,{g:function(){return n}})},97867:function(e,t,r){"use strict";r.d(t,{default:function(){return u}});var n=r(49988),i=r(27648),s=r(99376),a=r(2265),o=r(48706),u=(0,a.forwardRef)(function(e,t){let{defaultLocale:r,href:u,locale:l,localeCookie:c,onClick:d,prefetch:h,unprefixed:f,...p}=e,m=(0,o.Z)(),v=l!==m,y=l||m,g=function(){let[e,t]=(0,a.useState)();return(0,a.useEffect)(()=>{t(window.location.host)},[]),e}(),b=g&&f&&(f.domains[g]===y||!Object.keys(f.domains).includes(g)&&m===r&&!l)?f.pathname:u,w=(0,s.usePathname)();return v&&(h&&console.error("The `prefetch` prop is currently not supported when using the `locale` prop on `Link` to switch the locale.`"),h=!1),a.createElement(i.default,(0,n.g)({ref:t,href:b,hrefLang:v?l:void 0,onClick:function(e){(function(e,t,r,n){if(!e||!(n!==r&&null!=n)||!t)return;let i=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window.location.pathname;return"/"===e?t:t.replace(e,"")}(t),{name:s,...a}=e;a.path||(a.path=""!==i?i:"/");let o="".concat(s,"=").concat(n,";");for(let[e,t]of Object.entries(a))o+="".concat("maxAge"===e?"max-age":e),"boolean"!=typeof t&&(o+="="+t),o+=";";document.cookie=o})(c,w,m,l),d&&d(e)},prefetch:h},p))})},31085:function(e,t,r){"use strict";r.d(t,{default:function(){return d}});var n=r(49988),i=r(99376),s=r(2265),a=r(48706);function o(e){return("object"==typeof e?null==e.host&&null==e.hostname:!/^[a-z]+:/i.test(e))&&!function(e){let t="object"==typeof e?e.pathname:e;return null!=t&&!t.startsWith("/")}(e)}function u(e,t){let r;return"string"==typeof e?r=l(t,e):(r={...e},e.pathname&&(r.pathname=l(t,e.pathname))),r}function l(e,t){let r=e;return/^\/(\?.*)?$/.test(t)&&(t=t.slice(1)),r+=t}r(25566);var c=r(97867);let d=(0,s.forwardRef)(function(e,t){let{href:r,locale:l,localeCookie:d,localePrefixMode:h,prefix:f,...p}=e,m=(0,i.usePathname)(),v=(0,a.Z)(),y=l!==v,[g,b]=(0,s.useState)(()=>o(r)&&("never"!==h||y)?u(r,f):r);return(0,s.useEffect)(()=>{m&&b(function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t,n=arguments.length>3?arguments[3]:void 0,i=arguments.length>4?arguments[4]:void 0;if(!o(e))return e;let s=n===i||n.startsWith("".concat(i,"/"));return(t!==r||s)&&null!=i?u(e,i):e}(r,l,v,m,f))},[v,r,l,m,f]),s.createElement(c.default,(0,n.g)({ref:t,href:g,locale:l,localeCookie:d},p))});d.displayName="ClientLink"},48706:function(e,t,r){"use strict";r.d(t,{Z:function(){return a}});var n=r(99376),i=r(526);let s="locale";function a(){let e;let t=(0,n.useParams)();try{e=(0,i.useLocale)()}catch(r){if("string"!=typeof(null==t?void 0:t[s]))throw r;e=t[s]}return e}},36539:function(e,t,r){"use strict";var n=Object.create,i=Object.defineProperty,s=Object.getOwnPropertyDescriptor,a=Object.getOwnPropertyNames,o=Object.getPrototypeOf,u=Object.prototype.hasOwnProperty,l=(e,t)=>i(e,"name",{value:t,configurable:!0}),c=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let o of a(t))u.call(e,o)||o===r||i(e,o,{get:()=>t[o],enumerable:!(n=s(t,o))||n.enumerable});return e},d=(e,t,r)=>(r=null!=e?n(o(e)):{},c(!t&&e&&e.__esModule?r:i(r,"default",{value:e,enumerable:!0}),e)),h={};((e,t)=>{for(var r in t)i(e,r,{get:t[r],enumerable:!0})})(h,{default:()=>y}),e.exports=c(i({},"__esModule",{value:!0}),h);var f=d(r(40718)),p=d(r(2265)),m=d(r(71318)),v=l(e=>{let{color:t,height:r,showSpinner:n,crawl:i,crawlSpeed:s,initialPosition:a,easing:o,speed:u,shadow:c,template:d,zIndex:h=1600,showAtBottom:f=!1,showForHashAnchor:v=!0}=e,y=null!=t?t:"#29d",g=c||void 0===c?c?"box-shadow:".concat(c):"box-shadow:0 0 10px ".concat(y,",0 0 5px ").concat(y):"",b=p.createElement("style",null,"#nprogress{pointer-events:none}#nprogress .bar{background:".concat(y,";position:fixed;z-index:").concat(h,";").concat(f?"bottom: 0;":"top: 0;","left:0;width:100%;height:").concat(null!=r?r:3,"px}#nprogress .peg{display:block;position:absolute;right:0;width:100px;height:100%;").concat(g,";opacity:1;-webkit-transform:rotate(3deg) translate(0px,-4px);-ms-transform:rotate(3deg) translate(0px,-4px);transform:rotate(3deg) translate(0px,-4px)}#nprogress .spinner{display:block;position:fixed;z-index:").concat(h,";").concat(f?"bottom: 15px;":"top: 15px;","right:15px}#nprogress .spinner-icon{width:18px;height:18px;box-sizing:border-box;border:2px solid transparent;border-top-color:").concat(y,";border-left-color:").concat(y,";border-radius:50%;-webkit-animation:nprogress-spinner 400ms linear infinite;animation:nprogress-spinner 400ms linear infinite}.nprogress-custom-parent{overflow:hidden;position:relative}.nprogress-custom-parent #nprogress .bar,.nprogress-custom-parent #nprogress .spinner{position:absolute}@-webkit-keyframes nprogress-spinner{0%{-webkit-transform:rotate(0deg)}100%{-webkit-transform:rotate(360deg)}}@keyframes nprogress-spinner{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}")),w=l(e=>new URL(e,window.location.href).href,"toAbsoluteURL"),E=l((e,t)=>{let r=new URL(w(e)),n=new URL(w(t));return r.href.split("#")[0]===n.href.split("#")[0]},"isHashAnchor"),C=l((e,t)=>{let r=new URL(w(e)),n=new URL(w(t));return r.hostname.replace(/^www\./,"")===n.hostname.replace(/^www\./,"")},"isSameHostName");return p.useEffect(()=>{let e,t;function r(e,t){let r=new URL(e),n=new URL(t);if(r.hostname===n.hostname&&r.pathname===n.pathname&&r.search===n.search){let e=r.hash,t=n.hash;return e!==t&&r.href.replace(e,"")===n.href.replace(t,"")}return!1}m.configure({showSpinner:null==n||n,trickle:null==i||i,trickleSpeed:null!=s?s:200,minimum:null!=a?a:.08,easing:null!=o?o:"ease",speed:null!=u?u:200,template:null!=d?d:'<div class="bar" role="bar"><div class="peg"></div></div><div class="spinner" role="spinner"><div class="spinner-icon"></div></div>'}),l(r,"isAnchorOfCurrentUrl");var c,h,f=document.querySelectorAll("html");let p=l(()=>f.forEach(e=>e.classList.remove("nprogress-busy")),"removeNProgressClass");function y(e){for(;e&&"a"!==e.tagName.toLowerCase();)e=e.parentElement;return e}function g(e){try{let t=e.target,n=y(t),i=null==n?void 0:n.href;if(i){let t=window.location.href,s="_blank"===n.target,a=["tel:","mailto:","sms:","blob:","download:"].some(e=>i.startsWith(e));if(!C(window.location.href,n.href))return;let o=r(t,i)||E(window.location.href,n.href);if(!v&&o)return;i===t||s||a||o||e.ctrlKey||e.metaKey||e.shiftKey||e.altKey||!w(n.href).startsWith("http")?(m.start(),m.done(),p()):m.start()}}catch(e){m.start(),m.done()}}function b(){m.done(),p()}function x(){m.done()}return l(y,"findClosestAnchor"),l(g,"handleClick"),e=(c=window.history).pushState,c.pushState=function(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];return m.done(),p(),e.apply(c,r)},t=(h=window.history).replaceState,h.replaceState=function(){for(var e=arguments.length,r=Array(e),n=0;n<e;n++)r[n]=arguments[n];return m.done(),p(),t.apply(h,r)},l(b,"handlePageHide"),l(x,"handleBackAndForth"),window.addEventListener("popstate",x),document.addEventListener("click",g),window.addEventListener("pagehide",b),()=>{document.removeEventListener("click",g),window.removeEventListener("pagehide",b),window.removeEventListener("popstate",x)}},[]),b},"NextTopLoader"),y=v;v.propTypes={color:f.string,height:f.number,showSpinner:f.bool,crawl:f.bool,crawlSpeed:f.number,initialPosition:f.number,easing:f.string,speed:f.number,template:f.string,shadow:f.oneOfType([f.string,f.bool]),zIndex:f.number,showAtBottom:f.bool}},48049:function(e,t,r){"use strict";var n=r(14397);function i(){}function s(){}s.resetWarningCache=i,e.exports=function(){function e(e,t,r,i,s,a){if(a!==n){var o=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw o.name="Invariant Violation",o}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:s,resetWarningCache:i};return r.PropTypes=r,r}},40718:function(e,t,r){e.exports=r(48049)()},14397:function(e){"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},68421:function(e){e.exports={style:{fontFamily:"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'",fontStyle:"normal"},className:"__className_e8ce0c"}},6741:function(e,t,r){"use strict";function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}r.d(t,{M:function(){return n}})},71605:function(e,t,r){"use strict";r.d(t,{B:function(){return d}});var n=r(2265),i=r(73966),s=r(98575),a=r(57437),o=n.forwardRef((e,t)=>{let{children:r,...i}=e,s=n.Children.toArray(r),o=s.find(c);if(o){let e=o.props.children,r=s.map(t=>t!==o?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(u,{...i,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,a.jsx)(u,{...i,ref:t,children:r})});o.displayName="Slot";var u=n.forwardRef((e,t)=>{let{children:r,...i}=e;if(n.isValidElement(r)){let e,a;let o=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref;return n.cloneElement(r,{...function(e,t){let r={...t};for(let n in t){let i=e[n],s=t[n];/^on[A-Z]/.test(n)?i&&s?r[n]=(...e)=>{s(...e),i(...e)}:i&&(r[n]=i):"style"===n?r[n]={...i,...s}:"className"===n&&(r[n]=[i,s].filter(Boolean).join(" "))}return{...e,...r}}(i,r.props),ref:t?(0,s.F)(t,o):o})}return n.Children.count(r)>1?n.Children.only(null):null});u.displayName="SlotClone";var l=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});function c(e){return n.isValidElement(e)&&e.type===l}function d(e){let t=e+"CollectionProvider",[r,u]=(0,i.b)(t),[l,c]=r(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:r}=e,i=n.useRef(null),s=n.useRef(new Map).current;return(0,a.jsx)(l,{scope:t,itemMap:s,collectionRef:i,children:r})};d.displayName=t;let h=e+"CollectionSlot",f=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,i=c(h,r),u=(0,s.e)(t,i.collectionRef);return(0,a.jsx)(o,{ref:u,children:n})});f.displayName=h;let p=e+"CollectionItemSlot",m="data-radix-collection-item",v=n.forwardRef((e,t)=>{let{scope:r,children:i,...u}=e,l=n.useRef(null),d=(0,s.e)(t,l),h=c(p,r);return n.useEffect(()=>(h.itemMap.set(l,{ref:l,...u}),()=>void h.itemMap.delete(l))),(0,a.jsx)(o,{[m]:"",ref:d,children:i})});return v.displayName=p,[{Provider:d,Slot:f,ItemSlot:v},function(t){let r=c(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(m,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},u]}},22308:function(e,t,r){"use strict";r.d(t,{I0:function(){return g},XB:function(){return f},fC:function(){return y}});var n,i=r(2265),s=r(6741),a=r(82912),o=r(98575),u=r(26606),l=r(91096),c=r(57437),d="dismissableLayer.update",h=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=i.forwardRef((e,t)=>{var r,f;let{disableOutsidePointerEvents:p=!1,onEscapeKeyDown:y,onPointerDownOutside:g,onFocusOutside:b,onInteractOutside:w,onDismiss:E,...C}=e,x=i.useContext(h),[P,O]=i.useState(null),T=null!==(f=null==P?void 0:P.ownerDocument)&&void 0!==f?f:null===(r=globalThis)||void 0===r?void 0:r.document,[,S]=i.useState({}),R=(0,o.e)(t,e=>O(e)),M=Array.from(x.layers),[D]=[...x.layersWithOutsidePointerEventsDisabled].slice(-1),k=M.indexOf(D),L=P?M.indexOf(P):-1,A=x.layersWithOutsidePointerEventsDisabled.size>0,_=L>=k,N=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,n=(0,u.W)(e),s=i.useRef(!1),a=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!s.current){let t=function(){v("dismissableLayer.pointerDownOutside",n,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",a.current),a.current=t,r.addEventListener("click",a.current,{once:!0})):t()}else r.removeEventListener("click",a.current);s.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",a.current)}},[r,n]),{onPointerDownCapture:()=>s.current=!0}}(e=>{let t=e.target,r=[...x.branches].some(e=>e.contains(t));!_||r||(null==g||g(e),null==w||w(e),e.defaultPrevented||null==E||E())},T),j=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,n=(0,u.W)(e),s=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!s.current&&v("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,n]),{onFocusCapture:()=>s.current=!0,onBlurCapture:()=>s.current=!1}}(e=>{let t=e.target;[...x.branches].some(e=>e.contains(t))||(null==b||b(e),null==w||w(e),e.defaultPrevented||null==E||E())},T);return(0,l.e)(e=>{L!==x.layers.size-1||(null==y||y(e),!e.defaultPrevented&&E&&(e.preventDefault(),E()))},T),i.useEffect(()=>{if(P)return p&&(0===x.layersWithOutsidePointerEventsDisabled.size&&(n=T.body.style.pointerEvents,T.body.style.pointerEvents="none"),x.layersWithOutsidePointerEventsDisabled.add(P)),x.layers.add(P),m(),()=>{p&&1===x.layersWithOutsidePointerEventsDisabled.size&&(T.body.style.pointerEvents=n)}},[P,T,p,x]),i.useEffect(()=>()=>{P&&(x.layers.delete(P),x.layersWithOutsidePointerEventsDisabled.delete(P),m())},[P,x]),i.useEffect(()=>{let e=()=>S({});return document.addEventListener(d,e),()=>document.removeEventListener(d,e)},[]),(0,c.jsx)(a.WV.div,{...C,ref:R,style:{pointerEvents:A?_?"auto":"none":void 0,...e.style},onFocusCapture:(0,s.M)(e.onFocusCapture,j.onFocusCapture),onBlurCapture:(0,s.M)(e.onBlurCapture,j.onBlurCapture),onPointerDownCapture:(0,s.M)(e.onPointerDownCapture,N.onPointerDownCapture)})});f.displayName="DismissableLayer";var p=i.forwardRef((e,t)=>{let r=i.useContext(h),n=i.useRef(null),s=(0,o.e)(t,n);return i.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,c.jsx)(a.WV.div,{...e,ref:s})});function m(){let e=new CustomEvent(d);document.dispatchEvent(e)}function v(e,t,r,n){let{discrete:i}=n,s=r.originalEvent.target,o=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&s.addEventListener(e,t,{once:!0}),i?(0,a.jH)(s,o):s.dispatchEvent(o)}p.displayName="DismissableLayerBranch";var y=f,g=p},83832:function(e,t,r){"use strict";r.d(t,{h:function(){return u}});var n=r(2265),i=r(54887),s=r(82912),a=r(61188),o=r(57437),u=n.forwardRef((e,t)=>{var r,u;let{container:l,...c}=e,[d,h]=n.useState(!1);(0,a.b)(()=>h(!0),[]);let f=l||d&&(null===(u=globalThis)||void 0===u?void 0:null===(r=u.document)||void 0===r?void 0:r.body);return f?i.createPortal((0,o.jsx)(s.WV.div,{...c,ref:t}),f):null});u.displayName="Portal"},71599:function(e,t,r){"use strict";r.d(t,{z:function(){return o}});var n=r(2265),i=r(54887),s=r(98575),a=r(61188),o=e=>{var t,r;let o,l;let{present:c,children:d}=e,h=function(e){var t,r;let[s,o]=n.useState(),l=n.useRef({}),c=n.useRef(e),d=n.useRef("none"),[h,f]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return n.useEffect(()=>{let e=u(l.current);d.current="mounted"===h?e:"none"},[h]),(0,a.b)(()=>{let t=l.current,r=c.current;if(r!==e){let n=d.current,i=u(t);e?f("MOUNT"):"none"===i||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):r&&n!==i?f("ANIMATION_OUT"):f("UNMOUNT"),c.current=e}},[e,f]),(0,a.b)(()=>{if(s){let e=e=>{let t=u(l.current).includes(e.animationName);e.target===s&&t&&i.flushSync(()=>f("ANIMATION_END"))},t=e=>{e.target===s&&(d.current=u(l.current))};return s.addEventListener("animationstart",t),s.addEventListener("animationcancel",e),s.addEventListener("animationend",e),()=>{s.removeEventListener("animationstart",t),s.removeEventListener("animationcancel",e),s.removeEventListener("animationend",e)}}f("ANIMATION_END")},[s,f]),{isPresent:["mounted","unmountSuspended"].includes(h),ref:n.useCallback(e=>{e&&(l.current=getComputedStyle(e)),o(e)},[])}}(c),f="function"==typeof d?d({present:h.isPresent}):n.Children.only(d),p=(0,s.e)(h.ref,(o=null===(t=Object.getOwnPropertyDescriptor(f.props,"ref"))||void 0===t?void 0:t.get)&&"isReactWarning"in o&&o.isReactWarning?f.ref:(o=null===(r=Object.getOwnPropertyDescriptor(f,"ref"))||void 0===r?void 0:r.get)&&"isReactWarning"in o&&o.isReactWarning?f.props.ref:f.props.ref||f.ref);return"function"==typeof d||h.isPresent?n.cloneElement(f,{ref:p}):null};function u(e){return(null==e?void 0:e.animationName)||"none"}o.displayName="Presence"},50721:function(e,t,r){"use strict";r.d(t,{bU:function(){return x},fC:function(){return C}});var n=r(2265),i=r(6741),s=r(98575),a=r(73966),o=r(80886),u=r(6718),l=r(90420),c=r(82912),d=r(57437),h="Switch",[f,p]=(0,a.b)(h),[m,v]=f(h),y=n.forwardRef((e,t)=>{let{__scopeSwitch:r,name:a,checked:u,defaultChecked:l,required:h,disabled:f,value:p="on",onCheckedChange:v,...y}=e,[g,b]=n.useState(null),C=(0,s.e)(t,e=>b(e)),x=n.useRef(!1),P=!g||!!g.closest("form"),[O=!1,T]=(0,o.T)({prop:u,defaultProp:l,onChange:v});return(0,d.jsxs)(m,{scope:r,checked:O,disabled:f,children:[(0,d.jsx)(c.WV.button,{type:"button",role:"switch","aria-checked":O,"aria-required":h,"data-state":E(O),"data-disabled":f?"":void 0,disabled:f,value:p,...y,ref:C,onClick:(0,i.M)(e.onClick,e=>{T(e=>!e),P&&(x.current=e.isPropagationStopped(),x.current||e.stopPropagation())})}),P&&(0,d.jsx)(w,{control:g,bubbles:!x.current,name:a,value:p,checked:O,required:h,disabled:f,style:{transform:"translateX(-100%)"}})]})});y.displayName=h;var g="SwitchThumb",b=n.forwardRef((e,t)=>{let{__scopeSwitch:r,...n}=e,i=v(g,r);return(0,d.jsx)(c.WV.span,{"data-state":E(i.checked),"data-disabled":i.disabled?"":void 0,...n,ref:t})});b.displayName=g;var w=e=>{let{control:t,checked:r,bubbles:i=!0,...s}=e,a=n.useRef(null),o=(0,u.D)(r),c=(0,l.t)(t);return n.useEffect(()=>{let e=a.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(o!==r&&t){let n=new Event("click",{bubbles:i});t.call(e,r),e.dispatchEvent(n)}},[o,r,i]),(0,d.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:r,...s,tabIndex:-1,ref:a,style:{...e.style,...c,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function E(e){return e?"checked":"unchecked"}var C=y,x=b},41915:function(e,t,r){"use strict";r.d(t,{Dx:function(){return $},aU:function(){return et},dk:function(){return ee},fC:function(){return X},l_:function(){return G},x8:function(){return er},zt:function(){return J}});var n=r(2265),i=r(54887),s=r(6741),a=r(98575),o=r(71605),u=r(73966),l=r(22308),c=r(83832),d=r(71599),h=r(82912),f=r(26606),p=r(80886),m=r(61188),v=r(95098),y=r(57437),g="ToastProvider",[b,w,E]=(0,o.B)("Toast"),[C,x]=(0,u.b)("Toast",[E]),[P,O]=C(g),T=e=>{let{__scopeToast:t,label:r="Notification",duration:i=5e3,swipeDirection:s="right",swipeThreshold:a=50,children:o}=e,[u,l]=n.useState(null),[c,d]=n.useState(0),h=n.useRef(!1),f=n.useRef(!1);return r.trim()||console.error("Invalid prop `label` supplied to `".concat(g,"`. Expected non-empty `string`.")),(0,y.jsx)(b.Provider,{scope:t,children:(0,y.jsx)(P,{scope:t,label:r,duration:i,swipeDirection:s,swipeThreshold:a,toastCount:c,viewport:u,onViewportChange:l,onToastAdd:n.useCallback(()=>d(e=>e+1),[]),onToastRemove:n.useCallback(()=>d(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:h,isClosePausedRef:f,children:o})})};T.displayName=g;var S="ToastViewport",R=["F8"],M="toast.viewportPause",D="toast.viewportResume",k=n.forwardRef((e,t)=>{let{__scopeToast:r,hotkey:i=R,label:s="Notifications ({hotkey})",...o}=e,u=O(S,r),c=w(r),d=n.useRef(null),f=n.useRef(null),p=n.useRef(null),m=n.useRef(null),v=(0,a.e)(t,m,u.onViewportChange),g=i.join("+").replace(/Key/g,"").replace(/Digit/g,""),E=u.toastCount>0;n.useEffect(()=>{let e=e=>{var t;i.every(t=>e[t]||e.code===t)&&(null===(t=m.current)||void 0===t||t.focus())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[i]),n.useEffect(()=>{let e=d.current,t=m.current;if(E&&e&&t){let r=()=>{if(!u.isClosePausedRef.current){let e=new CustomEvent(M);t.dispatchEvent(e),u.isClosePausedRef.current=!0}},n=()=>{if(u.isClosePausedRef.current){let e=new CustomEvent(D);t.dispatchEvent(e),u.isClosePausedRef.current=!1}},i=t=>{e.contains(t.relatedTarget)||n()},s=()=>{e.contains(document.activeElement)||n()};return e.addEventListener("focusin",r),e.addEventListener("focusout",i),e.addEventListener("pointermove",r),e.addEventListener("pointerleave",s),window.addEventListener("blur",r),window.addEventListener("focus",n),()=>{e.removeEventListener("focusin",r),e.removeEventListener("focusout",i),e.removeEventListener("pointermove",r),e.removeEventListener("pointerleave",s),window.removeEventListener("blur",r),window.removeEventListener("focus",n)}}},[E,u.isClosePausedRef]);let C=n.useCallback(e=>{let{tabbingDirection:t}=e,r=c().map(e=>{let r=e.ref.current,n=[r,...function(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}(r)];return"forwards"===t?n:n.reverse()});return("forwards"===t?r.reverse():r).flat()},[c]);return n.useEffect(()=>{let e=m.current;if(e){let t=t=>{let r=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!r){var n,i,s;let r=document.activeElement,a=t.shiftKey;if(t.target===e&&a){null===(n=f.current)||void 0===n||n.focus();return}let o=C({tabbingDirection:a?"backwards":"forwards"}),u=o.findIndex(e=>e===r);Z(o.slice(u+1))?t.preventDefault():a?null===(i=f.current)||void 0===i||i.focus():null===(s=p.current)||void 0===s||s.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[c,C]),(0,y.jsxs)(l.I0,{ref:d,role:"region","aria-label":s.replace("{hotkey}",g),tabIndex:-1,style:{pointerEvents:E?void 0:"none"},children:[E&&(0,y.jsx)(A,{ref:f,onFocusFromOutsideViewport:()=>{Z(C({tabbingDirection:"forwards"}))}}),(0,y.jsx)(b.Slot,{scope:r,children:(0,y.jsx)(h.WV.ol,{tabIndex:-1,...o,ref:v})}),E&&(0,y.jsx)(A,{ref:p,onFocusFromOutsideViewport:()=>{Z(C({tabbingDirection:"backwards"}))}})]})});k.displayName=S;var L="ToastFocusProxy",A=n.forwardRef((e,t)=>{let{__scopeToast:r,onFocusFromOutsideViewport:n,...i}=e,s=O(L,r);return(0,y.jsx)(v.T,{"aria-hidden":!0,tabIndex:0,...i,ref:t,style:{position:"fixed"},onFocus:e=>{var t;let r=e.relatedTarget;(null===(t=s.viewport)||void 0===t?void 0:t.contains(r))||n()}})});A.displayName=L;var _="Toast",N=n.forwardRef((e,t)=>{let{forceMount:r,open:n,defaultOpen:i,onOpenChange:a,...o}=e,[u=!0,l]=(0,p.T)({prop:n,defaultProp:i,onChange:a});return(0,y.jsx)(d.z,{present:r||u,children:(0,y.jsx)(q,{open:u,...o,ref:t,onClose:()=>l(!1),onPause:(0,f.W)(e.onPause),onResume:(0,f.W)(e.onResume),onSwipeStart:(0,s.M)(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,s.M)(e.onSwipeMove,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y","".concat(r,"px"))}),onSwipeCancel:(0,s.M)(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,s.M)(e.onSwipeEnd,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y","".concat(r,"px")),l(!1)})})})});N.displayName=_;var[j,F]=C(_,{onClose(){}}),q=n.forwardRef((e,t)=>{let{__scopeToast:r,type:o="foreground",duration:u,open:c,onClose:d,onEscapeKeyDown:p,onPause:m,onResume:v,onSwipeStart:g,onSwipeMove:w,onSwipeCancel:E,onSwipeEnd:C,...x}=e,P=O(_,r),[T,S]=n.useState(null),R=(0,a.e)(t,e=>S(e)),k=n.useRef(null),L=n.useRef(null),A=u||P.duration,N=n.useRef(0),F=n.useRef(A),q=n.useRef(0),{onToastAdd:W,onToastRemove:U}=P,K=(0,f.W)(()=>{var e;(null==T?void 0:T.contains(document.activeElement))&&(null===(e=P.viewport)||void 0===e||e.focus()),d()}),V=n.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(q.current),N.current=new Date().getTime(),q.current=window.setTimeout(K,e))},[K]);n.useEffect(()=>{let e=P.viewport;if(e){let t=()=>{V(F.current),null==v||v()},r=()=>{let e=new Date().getTime()-N.current;F.current=F.current-e,window.clearTimeout(q.current),null==m||m()};return e.addEventListener(M,r),e.addEventListener(D,t),()=>{e.removeEventListener(M,r),e.removeEventListener(D,t)}}},[P.viewport,A,m,v,V]),n.useEffect(()=>{c&&!P.isClosePausedRef.current&&V(A)},[c,A,P.isClosePausedRef,V]),n.useEffect(()=>(W(),()=>U()),[W,U]);let Q=n.useMemo(()=>T?function e(t){let r=[];return Array.from(t.childNodes).forEach(t=>{if(t.nodeType===t.TEXT_NODE&&t.textContent&&r.push(t.textContent),t.nodeType===t.ELEMENT_NODE){let n=t.ariaHidden||t.hidden||"none"===t.style.display,i=""===t.dataset.radixToastAnnounceExclude;if(!n){if(i){let e=t.dataset.radixToastAnnounceAlt;e&&r.push(e)}else r.push(...e(t))}}}),r}(T):null,[T]);return P.viewport?(0,y.jsxs)(y.Fragment,{children:[Q&&(0,y.jsx)(I,{__scopeToast:r,role:"status","aria-live":"foreground"===o?"assertive":"polite","aria-atomic":!0,children:Q}),(0,y.jsx)(j,{scope:r,onClose:K,children:i.createPortal((0,y.jsx)(b.ItemSlot,{scope:r,children:(0,y.jsx)(l.fC,{asChild:!0,onEscapeKeyDown:(0,s.M)(p,()=>{P.isFocusedToastEscapeKeyDownRef.current||K(),P.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,y.jsx)(h.WV.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":c?"open":"closed","data-swipe-direction":P.swipeDirection,...x,ref:R,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:(0,s.M)(e.onKeyDown,e=>{"Escape"!==e.key||(null==p||p(e.nativeEvent),e.nativeEvent.defaultPrevented||(P.isFocusedToastEscapeKeyDownRef.current=!0,K()))}),onPointerDown:(0,s.M)(e.onPointerDown,e=>{0===e.button&&(k.current={x:e.clientX,y:e.clientY})}),onPointerMove:(0,s.M)(e.onPointerMove,e=>{if(!k.current)return;let t=e.clientX-k.current.x,r=e.clientY-k.current.y,n=!!L.current,i=["left","right"].includes(P.swipeDirection),s=["left","up"].includes(P.swipeDirection)?Math.min:Math.max,a=i?s(0,t):0,o=i?0:s(0,r),u="touch"===e.pointerType?10:2,l={x:a,y:o},c={originalEvent:e,delta:l};n?(L.current=l,Y("toast.swipeMove",w,c,{discrete:!1})):B(l,P.swipeDirection,u)?(L.current=l,Y("toast.swipeStart",g,c,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>u||Math.abs(r)>u)&&(k.current=null)}),onPointerUp:(0,s.M)(e.onPointerUp,e=>{let t=L.current,r=e.target;if(r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),L.current=null,k.current=null,t){let r=e.currentTarget,n={originalEvent:e,delta:t};B(t,P.swipeDirection,P.swipeThreshold)?Y("toast.swipeEnd",C,n,{discrete:!0}):Y("toast.swipeCancel",E,n,{discrete:!0}),r.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),P.viewport)})]}):null}),I=e=>{let{__scopeToast:t,children:r,...i}=e,s=O(_,t),[a,o]=n.useState(!1),[u,l]=n.useState(!1);return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:()=>{},t=(0,f.W)(e);(0,m.b)(()=>{let e=0,r=0;return e=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(r)}},[t])}(()=>o(!0)),n.useEffect(()=>{let e=window.setTimeout(()=>l(!0),1e3);return()=>window.clearTimeout(e)},[]),u?null:(0,y.jsx)(c.h,{asChild:!0,children:(0,y.jsx)(v.T,{...i,children:a&&(0,y.jsxs)(y.Fragment,{children:[s.label," ",r]})})})},W=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,y.jsx)(h.WV.div,{...n,ref:t})});W.displayName="ToastTitle";var U=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e;return(0,y.jsx)(h.WV.div,{...n,ref:t})});U.displayName="ToastDescription";var K="ToastAction",V=n.forwardRef((e,t)=>{let{altText:r,...n}=e;return r.trim()?(0,y.jsx)(z,{altText:r,asChild:!0,children:(0,y.jsx)(H,{...n,ref:t})}):(console.error("Invalid prop `altText` supplied to `".concat(K,"`. Expected non-empty `string`.")),null)});V.displayName=K;var Q="ToastClose",H=n.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e,i=F(Q,r);return(0,y.jsx)(z,{asChild:!0,children:(0,y.jsx)(h.WV.button,{type:"button",...n,ref:t,onClick:(0,s.M)(e.onClick,i.onClose)})})});H.displayName=Q;var z=n.forwardRef((e,t)=>{let{__scopeToast:r,altText:n,...i}=e;return(0,y.jsx)(h.WV.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":n||void 0,...i,ref:t})});function Y(e,t,r,n){let{discrete:i}=n,s=r.originalEvent.currentTarget,a=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:r});t&&s.addEventListener(e,t,{once:!0}),i?(0,h.jH)(s,a):s.dispatchEvent(a)}var B=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=Math.abs(e.x),i=Math.abs(e.y),s=n>i;return"left"===t||"right"===t?s&&n>r:!s&&i>r};function Z(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var J=T,G=k,X=N,$=W,ee=U,et=V,er=H},6718:function(e,t,r){"use strict";r.d(t,{D:function(){return i}});var n=r(2265);function i(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},90420:function(e,t,r){"use strict";r.d(t,{t:function(){return s}});var n=r(2265),i=r(61188);function s(e){let[t,r]=n.useState(void 0);return(0,i.b)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,i;if(!Array.isArray(t)||!t.length)return;let s=t[0];if("borderBoxSize"in s){let e=s.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,i=t.blockSize}else n=e.offsetWidth,i=e.offsetHeight;r({width:n,height:i})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}},95098:function(e,t,r){"use strict";r.d(t,{T:function(){return a}});var n=r(2265),i=r(82912),s=r(57437),a=n.forwardRef((e,t)=>(0,s.jsx)(i.WV.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));a.displayName="VisuallyHidden"},21733:function(e,t,r){"use strict";r.d(t,{A:function(){return o},z:function(){return u}});var n=r(45345),i=r(18238),s=r(11255),a=r(7989),o=class extends a.F{#e;#t;#r;#n;#i;#s;constructor(e){super(),this.#s=!1,this.#i=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#r=e.cache,this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#e=function(e){let t="function"==typeof e.initialData?e.initialData():e.initialData,r=void 0!==t,n=r?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:r?n??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#e,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#n?.promise}setOptions(e){this.options={...this.#i,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#r.remove(this)}setData(e,t){let r=(0,n.oE)(this.state.data,e,this.options);return this.#a({data:r,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),r}setState(e,t){this.#a({type:"setState",state:e,setStateOptions:t})}cancel(e){let t=this.#n?.promise;return this.#n?.cancel(e),t?t.then(n.ZT).catch(n.ZT):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#e)}isActive(){return this.observers.some(e=>!1!==(0,n.Nc)(e.options.enabled,this))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===n.CN||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some(e=>e.getCurrentResult().isStale):void 0===this.state.data)}isStaleByTime(e=0){return this.state.isInvalidated||void 0===this.state.data||!(0,n.Kp)(this.state.dataUpdatedAt,e)}onFocus(){let e=this.observers.find(e=>e.shouldFetchOnWindowFocus());e?.refetch({cancelRefetch:!1}),this.#n?.continue()}onOnline(){let e=this.observers.find(e=>e.shouldFetchOnReconnect());e?.refetch({cancelRefetch:!1}),this.#n?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#r.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(t=>t!==e),this.observers.length||(this.#n&&(this.#s?this.#n.cancel({revert:!0}):this.#n.cancelRetry()),this.scheduleGc()),this.#r.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#a({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus){if(void 0!==this.state.data&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#n)return this.#n.continueRetry(),this.#n.promise}if(e&&this.setOptions(e),!this.options.queryFn){let e=this.observers.find(e=>e.options.queryFn);e&&this.setOptions(e.options)}let r=new AbortController,i=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#s=!0,r.signal)})},a={fetchOptions:t,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:()=>{let e=(0,n.cG)(this.options,t),r={queryKey:this.queryKey,meta:this.meta};return(i(r),this.#s=!1,this.options.persister)?this.options.persister(e,r,this):e(r)}};i(a),this.options.behavior?.onFetch(a,this),this.#t=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==a.fetchOptions?.meta)&&this.#a({type:"fetch",meta:a.fetchOptions?.meta});let o=e=>{(0,s.DV)(e)&&e.silent||this.#a({type:"error",error:e}),(0,s.DV)(e)||(this.#r.config.onError?.(e,this),this.#r.config.onSettled?.(this.state.data,e,this)),this.isFetchingOptimistic||this.scheduleGc(),this.isFetchingOptimistic=!1};return this.#n=(0,s.Mz)({initialPromise:t?.initialPromise,fn:a.fetchFn,abort:r.abort.bind(r),onSuccess:e=>{if(void 0===e){o(Error(`${this.queryHash} data is undefined`));return}try{this.setData(e)}catch(e){o(e);return}this.#r.config.onSuccess?.(e,this),this.#r.config.onSettled?.(e,this.state.error,this),this.isFetchingOptimistic||this.scheduleGc(),this.isFetchingOptimistic=!1},onError:o,onFail:(e,t)=>{this.#a({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#a({type:"pause"})},onContinue:()=>{this.#a({type:"continue"})},retry:a.options.retry,retryDelay:a.options.retryDelay,networkMode:a.options.networkMode,canRun:()=>!0}),this.#n.start()}#a(e){this.state=(t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":return{...t,...u(t.data,this.options),fetchMeta:e.meta??null};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let r=e.error;if((0,s.DV)(r)&&r.revert&&this.#t)return{...this.#t,fetchStatus:"idle"};return{...t,error:r,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:r,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),i.V.batch(()=>{this.observers.forEach(e=>{e.onQueryUpdate()}),this.#r.notify({query:this,type:"updated",action:e})})}};function u(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:(0,s.Kw)(t.networkMode)?"fetching":"paused",...void 0===e&&{error:null,status:"pending"}}}},21623:function(e,t,r){"use strict";r.d(t,{S:function(){return m}});var n=r(45345),i=r(21733),s=r(18238),a=r(24112),o=class extends a.l{constructor(e={}){super(),this.config=e,this.#o=new Map}#o;build(e,t,r){let s=t.queryKey,a=t.queryHash??(0,n.Rm)(s,t),o=this.get(a);return o||(o=new i.A({cache:this,queryKey:s,queryHash:a,options:e.defaultQueryOptions(t),state:r,defaultOptions:e.getQueryDefaults(s)}),this.add(o)),o}add(e){this.#o.has(e.queryHash)||(this.#o.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){let t=this.#o.get(e.queryHash);t&&(e.destroy(),t===e&&this.#o.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){s.V.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#o.get(e)}getAll(){return[...this.#o.values()]}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,n._x)(t,e))}findAll(e={}){let t=this.getAll();return Object.keys(e).length>0?t.filter(t=>(0,n._x)(e,t)):t}notify(e){s.V.batch(()=>{this.listeners.forEach(t=>{t(e)})})}onFocus(){s.V.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){s.V.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},u=r(2894),l=class extends a.l{constructor(e={}){super(),this.config=e,this.#u=new Map,this.#l=Date.now()}#u;#l;build(e,t,r){let n=new u.m({mutationCache:this,mutationId:++this.#l,options:e.defaultMutationOptions(t),state:r});return this.add(n),n}add(e){let t=c(e),r=this.#u.get(t)??[];r.push(e),this.#u.set(t,r),this.notify({type:"added",mutation:e})}remove(e){let t=c(e);if(this.#u.has(t)){let r=this.#u.get(t)?.filter(t=>t!==e);r&&(0===r.length?this.#u.delete(t):this.#u.set(t,r))}this.notify({type:"removed",mutation:e})}canRun(e){let t=this.#u.get(c(e))?.find(e=>"pending"===e.state.status);return!t||t===e}runNext(e){let t=this.#u.get(c(e))?.find(t=>t!==e&&t.state.isPaused);return t?.continue()??Promise.resolve()}clear(){s.V.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}getAll(){return[...this.#u.values()].flat()}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,n.X7)(t,e))}findAll(e={}){return this.getAll().filter(t=>(0,n.X7)(e,t))}notify(e){s.V.batch(()=>{this.listeners.forEach(t=>{t(e)})})}resumePausedMutations(){let e=this.getAll().filter(e=>e.state.isPaused);return s.V.batch(()=>Promise.all(e.map(e=>e.continue().catch(n.ZT))))}};function c(e){return e.options.scope?.id??String(e.mutationId)}var d=r(87045),h=r(57853);function f(e){return{onFetch:(t,r)=>{let i=t.options,s=t.fetchOptions?.meta?.fetchMore?.direction,a=t.state.data?.pages||[],o=t.state.data?.pageParams||[],u={pages:[],pageParams:[]},l=0,c=async()=>{let r=!1,c=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(t.signal.aborted?r=!0:t.signal.addEventListener("abort",()=>{r=!0}),t.signal)})},d=(0,n.cG)(t.options,t.fetchOptions),h=async(e,i,s)=>{if(r)return Promise.reject();if(null==i&&e.pages.length)return Promise.resolve(e);let a={queryKey:t.queryKey,pageParam:i,direction:s?"backward":"forward",meta:t.options.meta};c(a);let o=await d(a),{maxPages:u}=t.options,l=s?n.Ht:n.VX;return{pages:l(e.pages,o,u),pageParams:l(e.pageParams,i,u)}};if(s&&a.length){let e="backward"===s,t={pages:a,pageParams:o},r=(e?function(e,{pages:t,pageParams:r}){return t.length>0?e.getPreviousPageParam?.(t[0],t,r[0],r):void 0}:p)(i,t);u=await h(t,r,e)}else{let t=e??a.length;do{let e=0===l?o[0]??i.initialPageParam:p(i,u);if(l>0&&null==e)break;u=await h(u,e),l++}while(l<t)}return u};t.options.persister?t.fetchFn=()=>t.options.persister?.(c,{queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},r):t.fetchFn=c}}}function p(e,{pages:t,pageParams:r}){let n=t.length-1;return t.length>0?e.getNextPageParam(t[n],t,r[n],r):void 0}var m=class{#c;#d;#i;#h;#f;#p;#m;#v;constructor(e={}){this.#c=e.queryCache||new o,this.#d=e.mutationCache||new l,this.#i=e.defaultOptions||{},this.#h=new Map,this.#f=new Map,this.#p=0}mount(){this.#p++,1===this.#p&&(this.#m=d.j.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#c.onFocus())}),this.#v=h.N.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#c.onOnline())}))}unmount(){this.#p--,0===this.#p&&(this.#m?.(),this.#m=void 0,this.#v?.(),this.#v=void 0)}isFetching(e){return this.#c.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#d.findAll({...e,status:"pending"}).length}getQueryData(e){let t=this.defaultQueryOptions({queryKey:e});return this.#c.get(t.queryHash)?.state.data}ensureQueryData(e){let t=this.getQueryData(e.queryKey);if(void 0===t)return this.fetchQuery(e);{let r=this.defaultQueryOptions(e),i=this.#c.build(this,r);return e.revalidateIfStale&&i.isStaleByTime((0,n.KC)(r.staleTime,i))&&this.prefetchQuery(r),Promise.resolve(t)}}getQueriesData(e){return this.#c.findAll(e).map(({queryKey:e,state:t})=>[e,t.data])}setQueryData(e,t,r){let i=this.defaultQueryOptions({queryKey:e}),s=this.#c.get(i.queryHash),a=s?.state.data,o=(0,n.SE)(t,a);if(void 0!==o)return this.#c.build(this,i).setData(o,{...r,manual:!0})}setQueriesData(e,t,r){return s.V.batch(()=>this.#c.findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,t,r)]))}getQueryState(e){let t=this.defaultQueryOptions({queryKey:e});return this.#c.get(t.queryHash)?.state}removeQueries(e){let t=this.#c;s.V.batch(()=>{t.findAll(e).forEach(e=>{t.remove(e)})})}resetQueries(e,t){let r=this.#c,n={type:"active",...e};return s.V.batch(()=>(r.findAll(e).forEach(e=>{e.reset()}),this.refetchQueries(n,t)))}cancelQueries(e={},t={}){let r={revert:!0,...t};return Promise.all(s.V.batch(()=>this.#c.findAll(e).map(e=>e.cancel(r)))).then(n.ZT).catch(n.ZT)}invalidateQueries(e={},t={}){return s.V.batch(()=>{if(this.#c.findAll(e).forEach(e=>{e.invalidate()}),"none"===e.refetchType)return Promise.resolve();let r={...e,type:e.refetchType??e.type??"active"};return this.refetchQueries(r,t)})}refetchQueries(e={},t){let r={...t,cancelRefetch:t?.cancelRefetch??!0};return Promise.all(s.V.batch(()=>this.#c.findAll(e).filter(e=>!e.isDisabled()).map(e=>{let t=e.fetch(void 0,r);return r.throwOnError||(t=t.catch(n.ZT)),"paused"===e.state.fetchStatus?Promise.resolve():t}))).then(n.ZT)}fetchQuery(e){let t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);let r=this.#c.build(this,t);return r.isStaleByTime((0,n.KC)(t.staleTime,r))?r.fetch(t):Promise.resolve(r.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(n.ZT).catch(n.ZT)}fetchInfiniteQuery(e){return e.behavior=f(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(n.ZT).catch(n.ZT)}ensureInfiniteQueryData(e){return e.behavior=f(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return h.N.isOnline()?this.#d.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#c}getMutationCache(){return this.#d}getDefaultOptions(){return this.#i}setDefaultOptions(e){this.#i=e}setQueryDefaults(e,t){this.#h.set((0,n.Ym)(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){let t=[...this.#h.values()],r={};return t.forEach(t=>{(0,n.to)(e,t.queryKey)&&(r={...r,...t.defaultOptions})}),r}setMutationDefaults(e,t){this.#f.set((0,n.Ym)(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){let t=[...this.#f.values()],r={};return t.forEach(t=>{(0,n.to)(e,t.mutationKey)&&(r={...r,...t.defaultOptions})}),r}defaultQueryOptions(e){if(e._defaulted)return e;let t={...this.#i.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=(0,n.Rm)(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),!0!==t.enabled&&t.queryFn===n.CN&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#i.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#c.clear(),this.#d.clear()}}},48614:function(e,t,r){"use strict";r.d(t,{M:function(){return y}});var n=r(57437),i=r(2265),s=r(58881),a=r(53576),o=r(64252),u=r(45750);class l extends i.Component{getSnapshotBeforeUpdate(e){let t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){let e=this.props.sizeRef.current;e.height=t.offsetHeight||0,e.width=t.offsetWidth||0,e.top=t.offsetTop,e.left=t.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function c(e){let{children:t,isPresent:r}=e,s=(0,i.useId)(),a=(0,i.useRef)(null),o=(0,i.useRef)({width:0,height:0,top:0,left:0}),{nonce:c}=(0,i.useContext)(u._);return(0,i.useInsertionEffect)(()=>{let{width:e,height:t,top:n,left:i}=o.current;if(r||!a.current||!e||!t)return;a.current.dataset.motionPopId=s;let u=document.createElement("style");return c&&(u.nonce=c),document.head.appendChild(u),u.sheet&&u.sheet.insertRule('\n          [data-motion-pop-id="'.concat(s,'"] {\n            position: absolute !important;\n            width: ').concat(e,"px !important;\n            height: ").concat(t,"px !important;\n            top: ").concat(n,"px !important;\n            left: ").concat(i,"px !important;\n          }\n        ")),()=>{document.head.removeChild(u)}},[r]),(0,n.jsx)(l,{isPresent:r,childRef:a,sizeRef:o,children:i.cloneElement(t,{ref:a})})}let d=e=>{let{children:t,initial:r,isPresent:s,onExitComplete:u,custom:l,presenceAffectsLayout:d,mode:f}=e,p=(0,a.h)(h),m=(0,i.useId)(),v=(0,i.useCallback)(e=>{for(let t of(p.set(e,!0),p.values()))if(!t)return;u&&u()},[p,u]),y=(0,i.useMemo)(()=>({id:m,initial:r,isPresent:s,custom:l,onExitComplete:v,register:e=>(p.set(e,!1),()=>p.delete(e))}),d?[Math.random(),v]:[s,v]);return(0,i.useMemo)(()=>{p.forEach((e,t)=>p.set(t,!1))},[s]),i.useEffect(()=>{s||p.size||!u||u()},[s]),"popLayout"===f&&(t=(0,n.jsx)(c,{isPresent:s,children:t})),(0,n.jsx)(o.O.Provider,{value:y,children:t})};function h(){return new Map}var f=r(49637);let p=e=>e.key||"";function m(e){let t=[];return i.Children.forEach(e,e=>{(0,i.isValidElement)(e)&&t.push(e)}),t}var v=r(11534);let y=e=>{let{children:t,custom:r,initial:o=!0,onExitComplete:u,presenceAffectsLayout:l=!0,mode:c="sync",propagate:h=!1}=e,[y,g]=(0,f.oO)(h),b=(0,i.useMemo)(()=>m(t),[t]),w=h&&!y?[]:b.map(p),E=(0,i.useRef)(!0),C=(0,i.useRef)(b),x=(0,a.h)(()=>new Map),[P,O]=(0,i.useState)(b),[T,S]=(0,i.useState)(b);(0,v.L)(()=>{E.current=!1,C.current=b;for(let e=0;e<T.length;e++){let t=p(T[e]);w.includes(t)?x.delete(t):!0!==x.get(t)&&x.set(t,!1)}},[T,w.length,w.join("-")]);let R=[];if(b!==P){let e=[...b];for(let t=0;t<T.length;t++){let r=T[t],n=p(r);w.includes(n)||(e.splice(t,0,r),R.push(r))}"wait"===c&&R.length&&(e=R),S(m(e)),O(b);return}let{forceRender:M}=(0,i.useContext)(s.p);return(0,n.jsx)(n.Fragment,{children:T.map(e=>{let t=p(e),i=(!h||!!y)&&(b===T||w.includes(t));return(0,n.jsx)(d,{isPresent:i,initial:(!E.current||!!o)&&void 0,custom:i?void 0:r,presenceAffectsLayout:l,mode:c,onExitComplete:i?void 0:()=>{if(!x.has(t))return;x.set(t,!0);let e=!0;x.forEach(t=>{t||(e=!1)}),e&&(null==M||M(),S(C.current),h&&(null==g||g()),u&&u())},children:e},t)})})}}}]);