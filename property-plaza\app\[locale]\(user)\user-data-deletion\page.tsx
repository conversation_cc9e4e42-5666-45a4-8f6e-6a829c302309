import { cookies } from "next/headers";
import { getLocale, getTranslations } from "next-intl/server";
import { getUserDeletionContent } from "@/core/services/sanity/services";
import Content from "./content";
import { Metadata } from "next";
import HeroUserDataDeletionSection from "./hero";
import { userDataDeletionUrl } from "@/lib/constanta/route";
import { routing } from "@/lib/locale/routing";

export async function generateMetadata(): Promise<Metadata> {
  // eslint-disable-next-line react-hooks/rules-of-hooks
  const t = await getTranslations("seeker")
  const locale = await getLocale() || routing.defaultLocale
  const baseUrl = process.env.USER_DOMAIN || "https://www.property-plaza.com/"
  return {
    title: t('metadata.userDataDeletion.title'),
    description: t('metadata.userDataDeletion.description'),
    alternates: {
      canonical: baseUrl + locale + userDataDeletionUrl,
      languages: {
        "id": baseUrl + `id${userDataDeletionUrl}`,
        "en": baseUrl + `en${userDataDeletionUrl}`,
        "x-default": baseUrl + userDataDeletionUrl.replace("/", ""),
      },
    },
    openGraph: {
      title: t('metadata.userDataDeletion.title'),
      description: t('metadata.userDataDeletion.description'),
      images: [
        {
          url: baseUrl + "og.png",
          width: 1200,
          height: 630,
          alt: "Property Plaza",
        }
      ],
      type: "website",
      url: baseUrl + locale + userDataDeletionUrl,
      countryName: "Indonesia",
      emails: "<EMAIL>",
      locale: locale,
      alternateLocale: routing.locales,
      siteName: "Property plaza"
    },
    applicationName: "Property plaza",
    twitter: {
      card: "summary_large_image",
      title: t('metadata.userDataDeletion.title'),
      description: t('metadata.userDataDeletion.description'),
      images: [baseUrl + "og.jpg"],
    },

    robots: {
      index: false,
      follow: false,
      nocache: false,
    },
  }
}

export default async function userDataDeletionPage() {
  const cookiesStore = cookies()
  const locale = cookiesStore.get('NEXT_LOCALE')?.value
  const t = await getTranslations("seeker")
  // const termOfUseContent = await getTermsOfUseContent(locale?.toLocaleLowerCase() || "en") // use this when locale for content are implemented
  const userDataDeletionSection = await getUserDeletionContent("en")
  return <>
    <HeroUserDataDeletionSection />
    <Content content={userDataDeletionSection[0]} />
  </>
}