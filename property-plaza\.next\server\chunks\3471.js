"use strict";exports.id=3471,exports.ids=[3471],exports.modules={13471:(e,a,r)=>{r.r(a),r.d(a,{default:()=>c});var s=r(72051),t=r(45347);let l=(0,t.createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\app\[locale]\(user)\(listings)\faq\sidebar.tsx#default`);(0,t.createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\app\[locale]\(user)\(listings)\faq\sidebar.tsx#SidebarContent`);let p=(0,t.createProxy)(String.raw`C:\_PRIVATE\Property Plaza - Seekers\property-plaza\app\[locale]\(user)\(listings)\faq\detail.tsx#default`);var i=r(695),d=r(79438),n=r(69385),x=r(23141);function c(){let e=(0,n.Z)("seeker");return s.jsx("div",{id:"faq",className:"bg-seekers-foreground/50 text-black w-full py-12 mt-12",children:s.jsx(d.Z,{children:s.jsx(i.Z,{title:(0,s.jsxs)("span",{className:"inline-flex items-center gap-1",children:[s.jsx(x.Z,{className:"max-sm:hidden"})," ",e("faq.title")]}),className:"!mt-2",children:(0,s.jsxs)("div",{className:"flex md:gap-8",children:[s.jsx(l,{}),s.jsx(p,{})]})})})})}},23141:(e,a,r)=>{r.d(a,{Z:()=>s});let s=(0,r(86449).Z)("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])}};