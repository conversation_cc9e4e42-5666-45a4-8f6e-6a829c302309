(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8293],{94856:function(e,t,n){Promise.resolve().then(n.bind(n,12229)),Promise.resolve().then(n.bind(n,43180)),Promise.resolve().then(n.bind(n,58622)),Promise.resolve().then(n.bind(n,6512)),Promise.resolve().then(n.bind(n,97867)),Promise.resolve().then(n.bind(n,31085)),Promise.resolve().then(n.bind(n,10575)),Promise.resolve().then(n.t.bind(n,65878,23)),Promise.resolve().then(n.bind(n,69729))},12229:function(e,t,n){"use strict";n.d(t,{default:function(){return p}});var i=n(57437),a=n(99376),s=n(27668),o=n(42586),l=n(26902);let r={active:!0,breakpoints:{},delay:4e3,jump:!1,playOnInit:!0,stopOnFocusIn:!0,stopOnInteraction:!0,stopOnMouseEnter:!1,stopOnLastSnap:!1,rootNode:null};function d(e={}){let t,n,i,a;let s=null,o=0,l=!1,c=!1,u=!1,p=!1;function f(){i||(l||n.emit("autoplay:play"),function(){let{ownerWindow:e}=n.internalEngine();e.clearTimeout(o),o=e.setTimeout(w,a[n.selectedScrollSnap()]),s=new Date().getTime(),n.emit("autoplay:timerset")}(),l=!0)}function m(){i||(l&&n.emit("autoplay:stop"),function(){let{ownerWindow:e}=n.internalEngine();e.clearTimeout(o),o=0,s=null,n.emit("autoplay:timerstopped")}(),l=!1)}function v(){if(h())return u=l,m();u&&f()}function h(){let{ownerDocument:e}=n.internalEngine();return"hidden"===e.visibilityState}function g(){c||m()}function x(){c||f()}function y(){c=!0,m()}function N(){c=!1,f()}function w(){let{index:e}=n.internalEngine(),i=e.clone().add(1).get(),a=n.scrollSnapList().length-1,s=t.stopOnLastSnap&&i===a;if(n.canScrollNext()?n.scrollNext(p):n.scrollTo(0,p),n.emit("autoplay:select"),s)return m();f()}return{name:"autoplay",options:e,init:function(s,o){n=s;let{mergeOptions:l,optionsAtMedia:c}=o,u=l(r,d.globalOptions);if(t=c(l(u,e)),n.scrollSnapList().length<=1)return;p=t.jump,i=!1,a=function(e,t){let n=e.scrollSnapList();return"number"==typeof t?n.map(()=>t):t(n,e)}(n,t.delay);let{eventStore:w,ownerDocument:b}=n.internalEngine(),_=!!n.internalEngine().options.watchDrag,S=function(e,t){let n=e.rootNode();return t&&t(n)||n}(n,t.rootNode);w.add(b,"visibilitychange",v),_&&n.on("pointerDown",g),_&&!t.stopOnInteraction&&n.on("pointerUp",x),t.stopOnMouseEnter&&w.add(S,"mouseenter",y),t.stopOnMouseEnter&&!t.stopOnInteraction&&w.add(S,"mouseleave",N),t.stopOnFocusIn&&n.on("slideFocusStart",m),t.stopOnFocusIn&&!t.stopOnInteraction&&w.add(n.containerNode(),"focusout",f),t.playOnInit&&!h()&&f()},destroy:function(){n.off("pointerDown",g).off("pointerUp",x).off("slideFocusStart",m),m(),i=!0,l=!1},play:function(e){void 0!==e&&(p=e),f()},stop:function(){l&&m()},reset:function(){l&&f()},isPlaying:function(){return l},timeUntilNext:function(){return s?a[n.selectedScrollSnap()]-(new Date().getTime()-s):null}}}d.globalOptions=void 0;let c=(0,n(79205).Z)("Fan",[["path",{d:"M10.827 16.379a6.082 6.082 0 0 1-8.618-7.002l5.412 1.45a6.082 6.082 0 0 1 7.002-8.618l-1.45 5.412a6.082 6.082 0 0 1 8.618 7.002l-5.412-1.45a6.082 6.082 0 0 1-7.002 8.618l1.45-5.412Z",key:"484a7f"}],["path",{d:"M12 12v.01",key:"u5ubse"}]]);var u=n(89337);function p(){let e=d({delay:3e3,stopOnInteraction:!1}),t=(0,o.useTranslations)("seeker"),n=(0,a.usePathname)();return(0,i.jsxs)(i.Fragment,{children:[(n.includes("/s/"),(0,i.jsx)(i.Fragment,{})),(0,i.jsxs)("div",{className:"w-full py-3 bg-[#F7ECDC]",children:[(0,i.jsx)(s.Z,{className:"md:hidden",children:(0,i.jsx)("div",{className:"w-full md:hidden",children:(0,i.jsx)(l.lr,{opts:{active:!0,loop:!0},plugins:[e],children:(0,i.jsxs)(l.KI,{className:"text-seekers-primary",children:[(0,i.jsxs)(l.d$,{className:"inline-flex gap-2 items-center",children:[(0,i.jsx)(c,{className:"!w-5 !h-5"}),(0,i.jsx)("p",{className:"font-semibold text-xs",children:t("banner.seekers.discoverDreamHome.title")})]}),(0,i.jsxs)(l.d$,{className:"inline-flex gap-2 items-center",children:[(0,i.jsx)(u.Z,{className:"!w-5 !h-5"}),(0,i.jsx)("p",{className:" font-semibold text-xs",children:t("banner.seekers.connectToPropertyOwner.title")})]})]})})})}),(0,i.jsx)(s.Z,{className:"hidden md:block text-seekers-primary",children:(0,i.jsxs)("div",{className:"hidden md:flex gap-4 md:justify-between",children:[(0,i.jsxs)("div",{className:"inline-flex gap-2 items-center",children:[(0,i.jsx)("p",{className:"font-semibold text-xs",children:t("banner.seekers.discoverDreamHome.title")}),(0,i.jsx)(c,{className:"!w-4 !h-4"})]}),(0,i.jsxs)("div",{className:"inline-flex gap-2 items-center",children:[(0,i.jsx)(u.Z,{className:"!w-4 !h-4"}),(0,i.jsx)("p",{className:"font-semibold text-xs",children:t("banner.seekers.connectToPropertyOwner.title")})]})]})})]})]})}},58622:function(e,t,n){"use strict";n.d(t,{default:function(){return m}});var i=n(57437),a=n(78645),s=n(34755),o=n(35153),l=n(96261),r=n(59625),d=n(89134);let c=(0,r.Ue)()((0,d.tJ)(e=>({hasNotificationSound:void 0,setNotificationSound:t=>e({hasNotificationSound:t}),isLoading:!0,setIsLoading:t=>e({isLoading:t}),editingStatus:[],setEditingStatus:(t,n)=>e(e=>{let i=e.editingStatus;if("add"==n)return i.includes(t)?{...e}:(i.push(t),{...e,editingStatus:i});if("remove"==n){let n=i.filter(e=>e!==t);return{...e,editingStatus:n}}return{...e}}),removeEditingStatus:()=>e({editingStatus:[]})}),{name:"settings",storage:(0,d.FL)(()=>localStorage),onRehydrateStorage:()=>e=>{e&&e.setIsLoading(!1)}}));var u=n(2265);let p=()=>{let{setNotificationSound:e}=c(e=>e),[t,n]=(0,u.useState)(null);return(0,u.useEffect)(()=>{let e=new Audio;e.src="/sounds/notification.mp3",n(e)},[]),{enableSoundNotification:t=>{e(t)},playSound:()=>{let e=new Audio;e.src="/sounds/notification.mp3",e.volume=1,e.play().then(()=>{}).catch(e=>{console.error("sound error",e)})},popUpNotification:(e,t)=>{if(!("Notification"in window)){console.warn("This browser does not support desktop notifications.");return}"granted"===Notification.permission?new Notification(e,{body:t||""}):"default"===Notification.permission?Notification.requestPermission().then(n=>{"granted"===n?new Notification(e,{body:t||""}):console.warn("Notification permission denied.")}):"denied"===Notification.permission&&console.warn("Notifications are denied by the user.")}}};var f=n(42586);function m(e){let{isSeeker:t=!1}=e,n=(0,f.useTranslations)("seeker"),{toast:r}=(0,o.pm)(),[d,m]=(0,u.useState)(!1),{updatechatDetail:v,updateSpecificAllChat:h}=(0,l.R)(e=>e),{hasNotificationSound:g,isLoading:x}=c(e=>e),{enableSoundNotification:y,playSound:N,popUpNotification:w}=p();return(0,u.useEffect)(()=>{s.W.connected||s.W.connect();let e=e=>{let t=(0,a.Z5)(e);r({title:n("message.newMessage")+t.displayName,description:t.text}),window.dispatchEvent(new CustomEvent("newMessage",{detail:t}))};return s.W.on("newChatNotif",e),()=>{s.W.off("newChatNotif",e)}},[]),(0,u.useEffect)(()=>{let e=e=>{N(),w(n("message.newMessage")+e.detail.displayName,e.detail.text),v(e.detail),h(e.detail)};return window.addEventListener("newMessage",e),()=>{window.removeEventListener("newMessage",e)}},[N]),(0,u.useEffect)(()=>{x||(void 0==g?m(!0):m(!1))},[g]),(0,i.jsx)(i.Fragment,{})}},26902:function(e,t,n){"use strict";n.d(t,{A0:function(){return x},KI:function(){return m},Pz:function(){return g},am:function(){return h},d$:function(){return v},lr:function(){return f}});var i=n(57437),a=n(2265),s=n(9467),o=n(94508),l=n(62869),r=n(92451),d=n(10407),c=n(42586);let u=a.createContext(null);function p(){let e=a.useContext(u);if(!e)throw Error("useCarousel must be used within a <Carousel />");return e}let f=a.forwardRef((e,t)=>{let{orientation:n="horizontal",opts:l,setApi:r,plugins:d,className:c,children:p,...f}=e,[m,v]=(0,s.Z)({...l,axis:"horizontal"===n?"x":"y"},d),[h,g]=a.useState(!1),[x,y]=a.useState(!1),[N,w]=a.useState(0),b=a.useCallback(e=>{e&&(g(e.canScrollPrev()),y(e.canScrollNext()),w(e.selectedScrollSnap()))},[]),_=a.useCallback(()=>{null==v||v.scrollPrev()},[v]),S=a.useCallback(()=>{null==v||v.scrollNext()},[v]),j=a.useCallback(e=>{"ArrowLeft"===e.key?(e.preventDefault(),_()):"ArrowRight"===e.key&&(e.preventDefault(),S())},[_,S]),C=a.useCallback(e=>{null==v||v.scrollTo(e)},[v]);return a.useEffect(()=>{v&&r&&r(v)},[v,r]),a.useEffect(()=>{if(v)return b(v),v.on("reInit",b),v.on("select",b),()=>{null==v||v.off("select",b)}},[v,b]),(0,i.jsx)(u.Provider,{value:{carouselRef:m,api:v,opts:l,orientation:n||((null==l?void 0:l.axis)==="y"?"vertical":"horizontal"),scrollPrev:_,scrollNext:S,canScrollPrev:h,canScrollNext:x,selectedIndex:N,scrollTo:C},children:(0,i.jsx)("div",{...f,ref:t,onKeyDownCapture:j,className:(0,o.cn)("relative",c),role:"region","aria-roledescription":"carousel",children:p})})});f.displayName="Carousel";let m=a.forwardRef((e,t)=>{let{className:n,...a}=e,{carouselRef:s,orientation:l}=p();return(0,i.jsx)("div",{ref:s,className:"overflow-hidden",children:(0,i.jsx)("div",{...a,ref:t,className:(0,o.cn)("flex","horizontal"===l?"-ml-4":"-mt-4 flex-col",n)})})});m.displayName="CarouselContent";let v=a.forwardRef((e,t)=>{let{className:n,...a}=e,{orientation:s}=p();return(0,i.jsx)("div",{...a,ref:t,role:"group","aria-roledescription":"slide",className:(0,o.cn)("min-w-0 shrink-0 grow-0 basis-full","horizontal"===s?"pl-4":"pt-4",n)})});v.displayName="CarouselItem";let h=a.forwardRef((e,t)=>{let{iconClassName:n,className:a,variant:s="outline",size:d="icon",...u}=e,{orientation:f,scrollPrev:m,canScrollPrev:v}=p(),h=(0,c.useTranslations)("universal");return(0,i.jsxs)(l.z,{...u,ref:t,variant:s,size:d,className:(0,o.cn)("absolute  h-6 w-6 rounded-full","horizontal"===f?"-left-12 top-1/2 -translate-y-1/2":"-top-12 left-1/2 -translate-x-1/2 rotate-90",a),disabled:!v,onClick:m,children:[(0,i.jsx)(r.Z,{className:(0,o.cn)("h-4 w-4",n)}),(0,i.jsx)("span",{className:"sr-only",children:h("cta.previous")})]})});h.displayName="CarouselPrevious";let g=a.forwardRef((e,t)=>{let{iconClassName:n,className:a,variant:s="outline",size:r="icon",...u}=e,{orientation:f,scrollNext:m,canScrollNext:v}=p(),h=(0,c.useTranslations)("seeker");return(0,i.jsxs)(l.z,{...u,ref:t,variant:s,size:r,className:(0,o.cn)("absolute h-6 w-6 rounded-full","horizontal"===f?"-right-12 top-1/2 -translate-y-1/2":"-bottom-12 left-1/2 -translate-x-1/2 rotate-90",a),disabled:!v,onClick:m,children:[(0,i.jsx)(d.Z,{className:(0,o.cn)("h-4 w-4",n)}),(0,i.jsx)("span",{className:"sr-only",children:h("cta.next")})]})});g.displayName="CarouselNext";let x=a.forwardRef((e,t)=>{let{className:n,carouselDotClassName:a,...s}=e,{selectedIndex:r,scrollTo:d,api:c}=p();return(0,i.jsx)("div",{ref:t,className:(0,o.cn)("embla__dots absolute z-50 flex w-fit items-center justify-center gap-2",n),...s,children:null==c?void 0:c.scrollSnapList().map((e,t)=>(0,i.jsx)(l.z,{size:"icon",className:(0,o.cn)(a,"embla__dot h-2 w-2 rounded-full ",t===r?"bg-white/90 ":"bg-black/10"),onClick:()=>null==d?void 0:d(t)},t))})});x.displayName="CarouselDots"},78645:function(e,t,n){"use strict";n.d(t,{Z5:function(){return l},eN:function(){return r},ug:function(){return o}});var i=n(77398),a=n.n(i),s=n(33254);function o(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"en";return e.map(e=>{var n,i,a,o,l,r,d;let c=e.messages[0];if(c)return console.log(t,(0,s.P)(null===(n=e.ref_data)||void 0===n?void 0:n.title,t)),{code:e.code,category:e.category,roomId:e.participants.room_id,lastMessages:{createdAt:(null==c?void 0:c.created_at)||e.created_at,displayAs:(null==c?void 0:c.display_as)||"",displayName:(null==c?void 0:c.display_name)||"",text:(null==c?void 0:c.text)||"",isRead:(null==c?void 0:c.is_read)||!1,isSent:(null==c?void 0:c.is_send)||!1,id:(null==c?void 0:c.id)||"",code:(null==c?void 0:c.code)||""},participant:{email:(null===(i=e.participants.info)||void 0===i?void 0:i.email)||"",fullName:(null===(a=e.participants.info)||void 0===a?void 0:a.display_name)||"",phoneNumber:(null===(o=e.participants.info)||void 0===o?void 0:o.phone_number)||"",image:e.participants.info.image||"",id:(null===(l=e.participants.info)||void 0===l?void 0:l.id)||"",category:e.category,status:e.status,property:{title:(0,s.P)(null===(r=e.ref_data)||void 0===r?void 0:r.title,t)||void 0,image:(null===(d=e.ref_data)||void 0===d?void 0:d.images[0].image)||void 0},otherProperty:[]},status:e.status,updatedAt:e.updated_at}}).filter(e=>void 0!==e).sort((e,t)=>a()(t.lastMessages.createdAt).unix()-a()(e.lastMessages.createdAt).unix())}function l(e){return{createdAt:e.created_at,displayAs:e.display_as,displayName:e.display_name,isRead:e.is_read,isSent:e.is_send,text:e.text,id:e.id,code:e.code}}function r(e){var t,n,i,a,o,l,r,d,c,u,p,f;let m=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"en";console.log(e.messages);let v=e.messages[(null===(t=e.messages)||void 0===t?void 0:t.length)-1]||void 0,h=e.messages.map(e=>({createdAt:e.created_at,displayAs:e.display_as,displayName:e.display_name,isRead:e.is_read,isSent:e.is_send,text:e.text,id:e.id,code:e.code})),g=null===(i=e.ref_data)||void 0===i?void 0:null===(n=i.extended_list)||void 0===n?void 0:n.map(e=>{var t;return{id:e.code,image:(null===(t=e.images[0])||void 0===t?void 0:t.image)||"",title:(0,s.P)(e.title,m)}});return{code:e.code,category:e.category,roomId:e.participants.room_id,lastMessages:{createdAt:v.created_at,displayAs:v.display_as,displayName:v.display_name,text:v.text,isRead:v.is_read,isSent:v.is_send,id:v.id,code:v.code||""},participant:{email:(null===(a=e.participants.info)||void 0===a?void 0:a.email)||"",fullName:(null===(o=e.participants.info)||void 0===o?void 0:o.display_name)||"",phoneNumber:(null===(l=e.participants.info)||void 0===l?void 0:l.phone_number)||"",image:(null===(r=e.participants.info)||void 0===r?void 0:r.image)||"",id:(null===(d=e.participants.info)||void 0===d?void 0:d.id)||"",category:e.category,status:e.status,property:{id:(null===(c=e.ref_data)||void 0===c?void 0:c.code)||"",image:(null===(p=e.ref_data)||void 0===p?void 0:null===(u=p.images[0])||void 0===u?void 0:u.image)||"",title:(0,s.P)(null===(f=e.ref_data)||void 0===f?void 0:f.title,m)||""},moreProperty:g||[]},allMessages:h,updatedAt:e.updated_at}}},34755:function(e,t,n){"use strict";n.d(t,{W:function(){return o}});var i=n(68680),a=n(64131),s=n(6404);let o=(0,i.ZP)("https://dev.property-plaza.id/",{extraHeaders:{"auth-token":a.Z.get(s.LA)||""},autoConnect:!1})},89337:function(e,t,n){"use strict";n.d(t,{Z:function(){return i}});let i=(0,n(79205).Z)("BadgeCheck",[["path",{d:"M3.85 8.62a4 4 0 0 1 4.78-4.77 4 4 0 0 1 6.74 0 4 4 0 0 1 4.78 4.78 4 4 0 0 1 0 6.74 4 4 0 0 1-4.77 4.78 4 4 0 0 1-6.75 0 4 4 0 0 1-4.78-4.77 4 4 0 0 1 0-6.76Z",key:"3c2336"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]])},96261:function(e,t,n){"use strict";n.d(t,{R:function(){return l}});var i=n(77398),a=n.n(i),s=n(59625),o=n(89134);let l=(0,s.Ue)()((0,o.tJ)(e=>({currentLayout:"list",setlayout:t=>e({currentLayout:t}),roomId:void 0,setRoomId:t=>e({roomId:t}),chatDetail:[],setchatDetail:t=>e({chatDetail:t}),updatechatDetail:t=>e(e=>{let n=e.chatDetail.length;return e.chatDetail[n-1].id==t.id?{}:{chatDetail:[...e.chatDetail,t]}}),participant:void 0,setParticipant:t=>e({participant:t}),allChat:[],setAllChat:t=>e({allChat:t}),updateSpecificAllChat:(t,n,i)=>e(e=>{let{allChat:s}=e,o=e=>e.sort((e,t)=>a()(t.lastMessages.createdAt).unix()-a()(e.lastMessages.createdAt).unix());if(n)return{allChat:o([...s,t])};if(i){let e=s.findIndex(e=>e.code===i);if("roomId"in t){if(e<0)return{allChat:o([...s,t])};{let n=[...s];return n[e]=t,{allChat:o(n)}}}if("id"in t)return e>=0?{allChat:o(s.map((n,i)=>i===e?{...n,lastMessages:t}:n))}:{allChat:s}}if("roomId"in t){let e=s.findIndex(e=>e.code===t.code);if(e<0)return{allChat:o([...s,t].sort((e,t)=>a()(t.lastMessages.createdAt).unix()-a()(e.lastMessages.createdAt).unix()))};{let n=[...s];return n[e]=t,{allChat:o(n)}}}if("id"in t){let e=s.findIndex(e=>e.code===t.code);if(e>=0)return{allChat:o(s.map((n,i)=>i===e?{...n,lastMessages:t}:n))}}return{allChat:s}}),updateParticipantStatus:t=>e(e=>e.participant?{participant:{...e.participant,status:t}}:{})}),{name:"messagingLayout",storage:(0,o.FL)(()=>sessionStorage)}))}},function(e){e.O(0,[6990,8310,7699,680,1866,2068,6290,8094,2586,2957,4956,3448,8658,6088,9623,3398,6205,4216,3682,3145,1298,4461,7060,4797,6245,4413,6868,2920,3784,8100,3180,2971,2117,1744],function(){return e(e.s=94856)}),_N_E=e.O()}]);