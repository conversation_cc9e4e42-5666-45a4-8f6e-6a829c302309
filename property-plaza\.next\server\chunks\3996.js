"use strict";exports.id=3996,exports.ids=[3996],exports.modules={76843:(t,e,s)=>{s.d(e,{P:()=>i});var n=s(1508),r=s(1116);function i(t){return new n.y(function(e){(0,r.Xf)(t()).subscribe(e)})}},95393:(t,e,s)=>{s.d(e,{E:()=>n});var n=new(s(1508)).y(function(t){return t.complete()})},25801:(t,e,s)=>{s.d(e,{h:()=>i});var n=s(74219),r=s(91126);function i(t,e){return(0,n.e)(function(s,n){var i=0;s.subscribe((0,r.x)(n,function(s){return t.call(e,s,i++)&&n.next(s)}))})}},2973:(t,e,s)=>{s.d(e,{U:()=>i});var n=s(74219),r=s(91126);function i(t,e){return(0,n.e)(function(s,n){var i=0;s.subscribe((0,r.x)(n,function(s){n.next(t.call(e,s,i++))}))})}},54998:(t,e,s)=>{s.d(e,{z:()=>function t(e,s,c){return(void 0===c&&(c=1/0),(0,a.m)(s))?t(function(t,i){return(0,n.U)(function(e,n){return s(t,e,i,n)})((0,r.Xf)(e(t,i)))},c):("number"==typeof s&&(c=s),(0,i.e)(function(t,s){var n,i,a,u,l,d,h,f,p;return n=c,a=[],u=0,l=0,d=!1,h=function(){!d||a.length||u||s.complete()},f=function(t){return u<n?p(t):a.push(t)},p=function(t){u++;var c=!1;(0,r.Xf)(e(t,l++)).subscribe((0,o.x)(s,function(t){i?f(t):s.next(t)},function(){c=!0},void 0,function(){if(c)try{for(u--;a.length&&u<n;)!function(){var t=a.shift();p(t)}();h()}catch(t){s.error(t)}}))},t.subscribe((0,o.x)(s,f,function(){d=!0,h()})),function(){}}))}});var n=s(2973),r=s(1116),i=s(74219),o=(s(15978),s(91126)),a=s(46037)},94139:(t,e,s)=>{s.d(e,{E9:()=>i,Y$:()=>c,ah:()=>p,p4:()=>h});var n=s(11742);let r=new WeakMap;function i(t){return{config:t,start:(e,s)=>{let{self:n,system:i,emit:o}=s,a={receivers:void 0,dispose:void 0};r.set(n,a),a.dispose=t({input:e.input,system:i,self:n,sendBack:t=>{"stopped"!==n.getSnapshot().status&&n._parent&&i._relay(n,n._parent,t)},receive:t=>{a.receivers??=new Set,a.receivers.add(t)},emit:o})},transition:(t,e,s)=>{let i=r.get(s.self);return e.type===n.X?(t={...t,status:"stopped",error:void 0},i.dispose?.()):i.receivers?.forEach(t=>t(e)),t},getInitialSnapshot:(t,e)=>({status:"active",output:void 0,error:void 0,input:e}),getPersistedSnapshot:t=>t,restoreSnapshot:t=>t}}let o="xstate.observable.error",a="xstate.observable.complete";function c(t){return{config:t,transition:(t,e)=>{if("active"!==t.status)return t;switch(e.type){case o:return{...t,status:"error",error:e.data,input:void 0,_subscription:void 0};case a:return{...t,status:"done",input:void 0,_subscription:void 0};case n.X:return t._subscription.unsubscribe(),{...t,status:"stopped",input:void 0,_subscription:void 0};default:return t}},getInitialSnapshot:(t,e)=>({status:"active",output:void 0,error:void 0,context:void 0,input:e,_subscription:void 0}),start:(e,{self:s,system:n,emit:r})=>{"done"!==e.status&&(e._subscription=t({input:e.input,system:n,self:s,emit:r}).subscribe({next:t=>{s._parent&&n._relay(s,s._parent,t)},error:t=>{n._relay(s,s,{type:o,data:t})},complete:()=>{n._relay(s,s,{type:a})}}))},getPersistedSnapshot:({_subscription:t,...e})=>e,restoreSnapshot:t=>({...t,_subscription:void 0})}}let u="xstate.promise.resolve",l="xstate.promise.reject",d=new WeakMap;function h(t){return{config:t,transition:(t,e,s)=>{if("active"!==t.status)return t;switch(e.type){case u:{let s=e.data;return{...t,status:"done",output:s,input:void 0}}case l:return{...t,status:"error",error:e.data,input:void 0};case n.X:return d.get(s.self)?.abort(),{...t,status:"stopped",input:void 0};default:return t}},start:(e,{self:s,system:n,emit:r})=>{if("active"!==e.status)return;let i=new AbortController;d.set(s,i),Promise.resolve(t({input:e.input,system:n,self:s,signal:i.signal,emit:r})).then(t=>{"active"===s.getSnapshot().status&&(d.delete(s),n._relay(s,s,{type:u,data:t}))},t=>{"active"===s.getSnapshot().status&&(d.delete(s),n._relay(s,s,{type:l,data:t}))})},getInitialSnapshot:(t,e)=>({status:"active",output:void 0,error:void 0,input:e}),getPersistedSnapshot:t=>t,restoreSnapshot:t=>t}}let f=function(t,e){return{config:t,transition:(e,s,n)=>({...e,context:t(e.context,s,n)}),getInitialSnapshot:(t,s)=>({status:"active",output:void 0,error:void 0,context:"function"==typeof e?e({input:s}):e}),getPersistedSnapshot:t=>t,restoreSnapshot:t=>t}}(t=>void 0,void 0);function p(){return(0,n.A)(f)}},84742:(t,e,s)=>{s.d(e,{a:()=>i,b:()=>g,c:()=>f,e:()=>c,s:()=>p});var n=s(11742);function r(t,e,s,r,{assignment:i}){if(!e.context)throw Error("Cannot assign to undefined `context`. Ensure that `context` is defined in the machine config.");let o={},a={context:e.context,event:s.event,spawn:function(t,{machine:e,context:s},r,i){let o=(o,a)=>{if("string"!=typeof o)return(0,n.A)(o,{id:a?.id,parent:t.self,syncSnapshot:a?.syncSnapshot,input:a?.input,src:o,systemId:a?.systemId});{let c=(0,n.z)(e,o);if(!c)throw Error(`Actor logic '${o}' not implemented in machine '${e.id}'`);let u=(0,n.A)(c,{id:a?.id,parent:t.self,syncSnapshot:a?.syncSnapshot,input:"function"==typeof a?.input?a.input({context:s,event:r,self:t.self}):a?.input,src:o,systemId:a?.systemId});return i[u.id]=u,u}};return(e,s)=>{let r=o(e,s);return i[r.id]=r,t.defer(()=>{r._processingStatus!==n.T.Stopped&&r.start()}),r}}(t,e,s.event,o),self:t.self,system:t.system},c={};if("function"==typeof i)c=i(a,r);else for(let t of Object.keys(i)){let e=i[t];c[t]="function"==typeof e?e(a,r):e}let u=Object.assign({},e.context,c);return[(0,n.U)(e,{context:u,children:Object.keys(o).length?{...e.children,...o}:e.children}),void 0,void 0]}function i(t){function e(t,e){}return e.type="xstate.assign",e.assignment=t,e.resolve=r,e}function o(t,e,s,n,{event:r}){return[e,{event:"function"==typeof r?r(s,n):r},void 0]}function a(t,{event:e}){t.defer(()=>t.emit(e))}function c(t){function e(t,e){}return e.type="xstate.emit",e.event=t,e.resolve=o,e.execute=a,e}let u=function(t){return t.Parent="#_parent",t.Internal="#_internal",t}({});function l(t,e,s,n,{to:r,event:i,id:o,delay:a},c){let l,d;let h=e.machine.implementations.delays;if("string"==typeof i)throw Error(`Only event objects may be used with sendTo; use sendTo({ type: "${i}" }) instead`);let f="function"==typeof i?i(s,n):i;if("string"==typeof a){let t=h&&h[a];l="function"==typeof t?t(s,n):t}else l="function"==typeof a?a(s,n):a;let p="function"==typeof r?r(s,n):r;if("string"==typeof p){if(!(d=p===u.Parent?t.self._parent:p===u.Internal?t.self:p.startsWith("#_")?e.children[p.slice(2)]:c.deferredActorIds?.includes(p)?p:e.children[p]))throw Error(`Unable to send event to actor '${p}' from machine '${e.machine.id}'.`)}else d=p||t.self;return[e,{to:d,targetId:"string"==typeof p?p:void 0,event:f,id:o,delay:l},void 0]}function d(t,e,s){"string"==typeof s.to&&(s.to=e.children[s.to])}function h(t,e){t.defer(()=>{let{to:s,event:r,delay:i,id:o}=e;if("number"==typeof i){t.system.scheduler.schedule(t.self,s,r,i,o);return}t.system._relay(t.self,s,r.type===n.V?(0,n.W)(t.self.id,r.data):r)})}function f(t,e,s){function n(t,e){}return n.type="xstate.sendTo",n.to=t,n.event=e,n.id=s?.id,n.delay=s?.delay,n.resolve=l,n.retryResolve=d,n.execute=h,n}function p(t,e){return f(u.Parent,t,e)}function y(t,e,s,r,{collect:o}){let a=[],u=function(t){a.push(t)};return u.assign=(...t)=>{a.push(i(...t))},u.cancel=(...t)=>{a.push((0,n.M)(...t))},u.raise=(...t)=>{a.push((0,n.O)(...t))},u.sendTo=(...t)=>{a.push(f(...t))},u.sendParent=(...t)=>{a.push(p(...t))},u.spawnChild=(...t)=>{a.push((0,n.P)(...t))},u.stopChild=(...t)=>{a.push((0,n.R)(...t))},u.emit=(...t)=>{a.push(c(...t))},o({context:s.context,event:s.event,enqueue:u,check:t=>(0,n.e)(t,e.context,s.event,e),self:t.self,system:t.system},r),[e,void 0,a]}function g(t){function e(t,e){}return e.type="xstate.enqueueActions",e.collect=t,e.resolve=y,e}},11742:(t,e,s)=>{s.d(e,{$:()=>q,A:()=>j,M:()=>N,N:()=>o,O:()=>tj,P:()=>z,R:()=>L,S:()=>i,T:()=>A,U:()=>tq,V:()=>c,W:()=>d,X:()=>u,a:()=>x,b:()=>tt,c:()=>k,d:()=>ts,e:()=>W,f:()=>te,g:()=>Q,h:()=>H,i:()=>V,j:()=>tu,k:()=>tT,l:()=>K,m:()=>v,n:()=>t_,o:()=>function t(e,s,n,r){return"string"==typeof s?function(t,e,s,n){let r=ta(t,e).next(s,n);return r&&r.length?r:t.next(s,n)}(e,s,n,r):1===Object.keys(s).length?function(e,s,n,r){let i=Object.keys(s),o=t(ta(e,i[0]),s[i[0]],n,r);return o&&o.length?o:e.next(n,r)}(e,s,n,r):function(e,s,n,r){let i=[];for(let o of Object.keys(s)){let a=s[o];if(!a)continue;let c=t(ta(e,o),a,n,r);c&&i.push(...c)}return i.length?i:e.next(n,r)}(e,s,n,r)},p:()=>tm,q:()=>h,r:()=>tS,s:()=>ty,t:()=>m,u:()=>to,v:()=>y,w:()=>G,x:()=>tc,y:()=>tA,z:()=>I});let n=t=>{if("undefined"==typeof window)return;let e=function(){let t="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:void 0;if(t.__xstate__)return t.__xstate__}();e&&e.register(t)};class r{constructor(t){this._process=t,this._active=!1,this._current=null,this._last=null}start(){this._active=!0,this.flush()}clear(){this._current&&(this._current.next=null,this._last=this._current)}enqueue(t){let e={value:t,next:null};if(this._current){this._last.next=e,this._last=e;return}this._current=e,this._last=e,this._active&&this.flush()}flush(){for(;this._current;){let t=this._current;this._process(t.value),this._current=t.next}this._last=null}}let i=".",o="",a="xstate.init",c="xstate.error",u="xstate.stop";function l(t,e){return{type:`xstate.done.state.${t}`,output:e}}function d(t,e){return{type:`xstate.error.actor.${t}`,error:e,actorId:t}}function h(t){return{type:a,input:t}}function f(t){setTimeout(()=>{throw t})}let p="function"==typeof Symbol&&Symbol.observable||"@@observable";function y(t){if(b(t))return t;let e=[],s="";for(let n=0;n<t.length;n++){switch(t.charCodeAt(n)){case 92:s+=t[n+1],n++;continue;case 46:e.push(s),s="";continue}s+=t[n]}return e.push(s),e}function g(t){return t&&"object"==typeof t&&"machine"in t&&"value"in t?t.value:"string"!=typeof t?t:function(t){if(1===t.length)return t[0];let e={},s=e;for(let e=0;e<t.length-1;e++)if(e===t.length-2)s[t[e]]=t[e+1];else{let n=s;s={},n[t[e]]=s}return e}(y(t))}function v(t,e){let s={},n=Object.keys(t);for(let r=0;r<n.length;r++){let i=n[r];s[i]=e(t[i],i,t,r)}return s}function m(t){return void 0===t?[]:b(t)?t:[t]}function _(t,e,s,n){return"function"==typeof t?t({context:e,event:s,self:n}):t}function b(t){return Array.isArray(t)}function x(t){return(b(t)?t:[t]).map(t=>void 0===t||"string"==typeof t?{target:t}:t)}function S(t){if(void 0!==t&&""!==t)return m(t)}function w(t,e,s){let n="object"==typeof t,r=n?t:void 0;return{next:(n?t.next:t)?.bind(r),error:(n?t.error:e)?.bind(r),complete:(n?t.complete:s)?.bind(r)}}function k(t,e){return`${e}.${t}`}function I(t,e){let s=e.match(/^xstate\.invoke\.(\d+)\.(.*)/);if(!s)return t.implementations.actors[e];let[,n,r]=s,i=t.getStateNodeById(r).config.invoke;return(Array.isArray(i)?i[n]:i).src}function $(t,e){return`${t.sessionId}.${e}`}let E=0,T=!1,q=1,A=function(t){return t[t.NotStarted=0]="NotStarted",t[t.Running=1]="Running",t[t.Stopped=2]="Stopped",t}({}),O={clock:{setTimeout:(t,e)=>setTimeout(t,e),clearTimeout:t=>clearTimeout(t)},logger:console.log.bind(console),devTools:!1};class R{constructor(t,e){this.logic=t,this._snapshot=void 0,this.clock=void 0,this.options=void 0,this.id=void 0,this.mailbox=new r(this._process.bind(this)),this.observers=new Set,this.eventListeners=new Map,this.logger=void 0,this._processingStatus=A.NotStarted,this._parent=void 0,this._syncSnapshot=void 0,this.ref=void 0,this._actorScope=void 0,this._systemId=void 0,this.sessionId=void 0,this.system=void 0,this._doneEvent=void 0,this.src=void 0,this._deferred=[];let s={...O,...e},{clock:n,logger:i,parent:o,syncSnapshot:a,id:c,systemId:u,inspect:l}=s;this.system=o?o.system:function(t,e){let s=new Map,n=new Map,r=new WeakMap,i=new Set,o={},{clock:a,logger:c}=e,u={schedule:(t,e,s,n,r=Math.random().toString(36).slice(2))=>{let i={source:t,target:e,event:s,delay:n,id:r,startedAt:Date.now()},c=$(t,r);l._snapshot._scheduledEvents[c]=i;let u=a.setTimeout(()=>{delete o[c],delete l._snapshot._scheduledEvents[c],l._relay(t,e,s)},n);o[c]=u},cancel:(t,e)=>{let s=$(t,e),n=o[s];delete o[s],delete l._snapshot._scheduledEvents[s],void 0!==n&&a.clearTimeout(n)},cancelAll:t=>{for(let e in l._snapshot._scheduledEvents){let s=l._snapshot._scheduledEvents[e];s.source===t&&u.cancel(t,s.id)}}},l={_snapshot:{_scheduledEvents:(e?.snapshot&&e.snapshot.scheduler)??{}},_bookId:()=>`x:${E++}`,_register:(t,e)=>(s.set(t,e),t),_unregister:t=>{s.delete(t.sessionId);let e=r.get(t);void 0!==e&&(n.delete(e),r.delete(t))},get:t=>n.get(t),_set:(t,e)=>{let s=n.get(t);if(s&&s!==e)throw Error(`Actor with system ID '${t}' already exists.`);n.set(t,e),r.set(e,t)},inspect:t=>{let e=w(t);return i.add(e),{unsubscribe(){i.delete(e)}}},_sendInspectionEvent:e=>{if(!i.size)return;let s={...e,rootId:t.sessionId};i.forEach(t=>t.next?.(s))},_relay:(t,e,s)=>{l._sendInspectionEvent({type:"@xstate.event",sourceRef:t,actorRef:e,event:s}),e._send(s)},scheduler:u,getSnapshot:()=>({_scheduledEvents:{...l._snapshot._scheduledEvents}}),start:()=>{let t=l._snapshot._scheduledEvents;for(let e in l._snapshot._scheduledEvents={},t){let{source:s,target:n,event:r,delay:i,id:o}=t[e];u.schedule(s,n,r,i,o)}},_clock:a,_logger:c};return l}(this,{clock:n,logger:i}),l&&!o&&this.system.inspect(w(l)),this.sessionId=this.system._bookId(),this.id=c??this.sessionId,this.logger=e?.logger??this.system._logger,this.clock=e?.clock??this.system._clock,this._parent=o,this._syncSnapshot=a,this.options=s,this.src=s.src??t,this.ref=this,this._actorScope={self:this,id:this.id,sessionId:this.sessionId,logger:this.logger,defer:t=>{this._deferred.push(t)},system:this.system,stopChild:t=>{if(t._parent!==this)throw Error(`Cannot stop child actor ${t.id} of ${this.id} because it is not a child`);t._stop()},emit:t=>{let e=this.eventListeners.get(t.type),s=this.eventListeners.get("*");if(e||s)for(let n of[...e?e.values():[],...s?s.values():[]])n(t)},actionExecutor:t=>{let e=()=>{if(this._actorScope.system._sendInspectionEvent({type:"@xstate.action",actorRef:this,action:{type:t.type,params:t.params}}),!t.exec)return;let e=T;try{T=!0,t.exec(t.info,t.params)}finally{T=e}};this._processingStatus===A.Running?e():this._deferred.push(e)}},this.send=this.send.bind(this),this.system._sendInspectionEvent({type:"@xstate.actor",actorRef:this}),u&&(this._systemId=u,this.system._set(u,this)),this._initState(e?.snapshot??e?.state),u&&"active"!==this._snapshot.status&&this.system._unregister(this)}_initState(t){try{this._snapshot=t?this.logic.restoreSnapshot?this.logic.restoreSnapshot(t,this._actorScope):t:this.logic.getInitialSnapshot(this._actorScope,this.options?.input)}catch(t){this._snapshot={status:"error",output:void 0,error:t}}}update(t,e){let s;for(this._snapshot=t;s=this._deferred.shift();)try{s()}catch(e){this._deferred.length=0,this._snapshot={...t,status:"error",error:e}}switch(this._snapshot.status){case"active":for(let e of this.observers)try{e.next?.(t)}catch(t){f(t)}break;case"done":var n;for(let e of this.observers)try{e.next?.(t)}catch(t){f(t)}this._stopProcedure(),this._complete(),this._doneEvent=(n=this.id,{type:`xstate.done.actor.${n}`,output:this._snapshot.output,actorId:n}),this._parent&&this.system._relay(this,this._parent,this._doneEvent);break;case"error":this._error(this._snapshot.error)}this.system._sendInspectionEvent({type:"@xstate.snapshot",actorRef:this,event:e,snapshot:t})}subscribe(t,e,s){let n=w(t,e,s);if(this._processingStatus!==A.Stopped)this.observers.add(n);else switch(this._snapshot.status){case"done":try{n.complete?.()}catch(t){f(t)}break;case"error":{let t=this._snapshot.error;if(n.error)try{n.error(t)}catch(t){f(t)}else f(t)}}return{unsubscribe:()=>{this.observers.delete(n)}}}on(t,e){let s=this.eventListeners.get(t);s||(s=new Set,this.eventListeners.set(t,s));let n=e.bind(void 0);return s.add(n),{unsubscribe:()=>{s.delete(n)}}}start(){if(this._processingStatus===A.Running)return this;this._syncSnapshot&&this.subscribe({next:t=>{"active"===t.status&&this.system._relay(this,this._parent,{type:`xstate.snapshot.${this.id}`,snapshot:t})},error:()=>{}}),this.system._register(this.sessionId,this),this._systemId&&this.system._set(this._systemId,this),this._processingStatus=A.Running;let t=h(this.options.input);switch(this.system._sendInspectionEvent({type:"@xstate.event",sourceRef:this._parent,actorRef:this,event:t}),this._snapshot.status){case"done":return this.update(this._snapshot,t),this;case"error":return this._error(this._snapshot.error),this}if(this._parent||this.system.start(),this.logic.start)try{this.logic.start(this._snapshot,this._actorScope)}catch(t){return this._snapshot={...this._snapshot,status:"error",error:t},this._error(t),this}return this.update(this._snapshot,t),this.options.devTools&&this.attachDevTools(),this.mailbox.start(),this}_process(t){let e,s;try{e=this.logic.transition(this._snapshot,t,this._actorScope)}catch(t){s={err:t}}if(s){let{err:t}=s;this._snapshot={...this._snapshot,status:"error",error:t},this._error(t);return}this.update(e,t),t.type===u&&(this._stopProcedure(),this._complete())}_stop(){return this._processingStatus===A.Stopped||((this.mailbox.clear(),this._processingStatus===A.NotStarted)?this._processingStatus=A.Stopped:this.mailbox.enqueue({type:u})),this}stop(){if(this._parent)throw Error("A non-root actor cannot be stopped directly.");return this._stop()}_complete(){for(let t of this.observers)try{t.complete?.()}catch(t){f(t)}this.observers.clear()}_reportError(t){if(!this.observers.size){this._parent||f(t);return}let e=!1;for(let s of this.observers){let n=s.error;e||=!n;try{n?.(t)}catch(t){f(t)}}this.observers.clear(),e&&f(t)}_error(t){this._stopProcedure(),this._reportError(t),this._parent&&this.system._relay(this,this._parent,d(this.id,t))}_stopProcedure(){return this._processingStatus!==A.Running||(this.system.scheduler.cancelAll(this),this.mailbox.clear(),this.mailbox=new r(this._process.bind(this)),this._processingStatus=A.Stopped,this.system._unregister(this)),this}_send(t){this._processingStatus!==A.Stopped&&this.mailbox.enqueue(t)}send(t){this.system._relay(void 0,this,t)}attachDevTools(){let{devTools:t}=this.options;t&&("function"==typeof t?t:n)(this)}toJSON(){return{xstate$$type:q,id:this.id}}getPersistedSnapshot(t){return this.logic.getPersistedSnapshot(this._snapshot,t)}[p](){return this}getSnapshot(){return this._snapshot}}function j(t,...[e]){return new R(t,e)}function D(t,e,s,n,{sendId:r}){return[e,{sendId:"function"==typeof r?r(s,n):r},void 0]}function M(t,e){t.defer(()=>{t.system.scheduler.cancel(t.self,e.sendId)})}function N(t){function e(t,e){}return e.type="xstate.cancel",e.sendId=t,e.resolve=D,e.execute=M,e}function P(t,e,s,n,{id:r,systemId:i,src:o,input:a,syncSnapshot:c}){let u,l;let d="string"==typeof o?I(e.machine,o):o,h="function"==typeof r?r(s):r;return d&&(l="function"==typeof a?a({context:e.context,event:s.event,self:t.self}):a,u=j(d,{id:h,src:o,parent:t.self,syncSnapshot:c,systemId:i,input:l})),[tq(e,{children:{...e.children,[h]:u}}),{id:r,systemId:i,actorRef:u,src:o,input:l},void 0]}function U(t,{actorRef:e}){e&&t.defer(()=>{e._processingStatus!==A.Stopped&&e.start()})}function z(...[t,{id:e,systemId:s,input:n,syncSnapshot:r=!1}={}]){function i(t,e){}return i.type="xstate.spawnChild",i.id=e,i.systemId=s,i.src=t,i.input=n,i.syncSnapshot=r,i.resolve=P,i.execute=U,i}function C(t,e,s,n,{actorRef:r}){let i="function"==typeof r?r(s,n):r,o="string"==typeof i?e.children[i]:i,a=e.children;return o&&(a={...a},delete a[o.id]),[tq(e,{children:a}),o,void 0]}function B(t,e){if(e){if(t.system._unregister(e),e._processingStatus!==A.Running){t.stopChild(e);return}t.defer(()=>{t.stopChild(e)})}}function L(t){function e(t,e){}return e.type="xstate.stopChild",e.actorRef=t,e.resolve=C,e.execute=B,e}function W(t,e,s,n){let{machine:r}=n,i="function"==typeof t,o=i?t:r.implementations.guards["string"==typeof t?t:t.type];if(!i&&!o)throw Error(`Guard '${"string"==typeof t?t:t.type}' is not implemented.'.`);if("function"!=typeof o)return W(o,e,s,n);let a={context:e,event:s},c=i||"string"==typeof t?void 0:"params"in t?"function"==typeof t.params?t.params({context:e,event:s}):t.params:void 0;return"check"in o?o.check(n,a,o):o(a,c)}let Z=t=>"atomic"===t.type||"final"===t.type;function X(t){return Object.values(t.states).filter(t=>"history"!==t.type)}function J(t,e){let s=[];if(e===t)return s;let n=t.parent;for(;n&&n!==e;)s.push(n),n=n.parent;return s}function V(t){let e=new Set(t),s=Y(e);for(let t of e)if("compound"!==t.type||s.get(t)&&s.get(t).length){if("parallel"===t.type){for(let s of X(t))if("history"!==s.type&&!e.has(s))for(let t of ti(s))e.add(t)}}else ti(t).forEach(t=>e.add(t));for(let t of e){let s=t.parent;for(;s;)e.add(s),s=s.parent}return e}function Y(t){let e=new Map;for(let s of t)e.has(s)||e.set(s,[]),s.parent&&(e.has(s.parent)||e.set(s.parent,[]),e.get(s.parent).push(s));return e}function F(t,e){return function t(e,s){let n=s.get(e);if(!n)return{};if("compound"===e.type){let t=n[0];if(!t)return{};if(Z(t))return t.key}let r={};for(let e of n)r[e.key]=t(e,s);return r}(t,Y(V(e)))}function K(t,e){return"compound"===e.type?X(e).some(e=>"final"===e.type&&t.has(e)):"parallel"===e.type?X(e).every(e=>K(t,e)):"final"===e.type}let G=t=>"#"===t[0];function H(t,e){return t.transitions.get(e)||[...t.transitions.keys()].filter(t=>{if("*"===t)return!0;if(!t.endsWith(".*"))return!1;let s=t.split("."),n=e.split(".");for(let t=0;t<s.length;t++){let e=s[t],r=n[t];if("*"===e)return t===s.length-1;if(e!==r)return!1}return!0}).sort((t,e)=>e.length-t.length).flatMap(e=>t.transitions.get(e))}function Q(t){let e=t.config.after;if(!e)return[];let s=e=>{var s;let n=(s=t.id,{type:`xstate.after.${e}.${s}`}),r=n.type;return t.entry.push(tj(n,{id:r,delay:e})),t.exit.push(N(r)),r};return Object.keys(e).flatMap(t=>{let n=e[t],r=Number.isNaN(+t)?t:+t,i=s(r);return m("string"==typeof n?{target:n}:n).map(t=>({...t,event:i,delay:r}))}).map(e=>{let{delay:s}=e;return{...tt(t,e.event,e),delay:s}})}function tt(t,e,s){let n=S(s.target),r=s.reenter??!1,o=function(t,e){if(void 0!==e)return e.map(e=>{if("string"!=typeof e)return e;if(G(e))return t.machine.getStateNodeById(e);let s=e[0]===i;if(s&&!t.parent)return tc(t,e.slice(1));let n=s?t.key+e:e;if(t.parent)try{return tc(t.parent,n)}catch(e){throw Error(`Invalid transition definition for state node '${t.id}':
${e.message}`)}else throw Error(`Invalid target: "${e}" is not a valid target from the root node. Did you mean ".${e}"?`)})}(t,n),a={...s,actions:m(s.actions),guard:s.guard,target:o,source:t,reenter:r,eventType:e,toJSON:()=>({...a,source:`#${t.id}`,target:o?o.map(t=>`#${t.id}`):void 0})};return a}function te(t){let e=new Map;if(t.config.on)for(let s of Object.keys(t.config.on)){if(s===o)throw Error('Null events ("") cannot be specified as a transition key. Use `always: { ... }` instead.');let n=t.config.on[s];e.set(s,x(n).map(e=>tt(t,s,e)))}if(t.config.onDone){let s=`xstate.done.state.${t.id}`;e.set(s,x(t.config.onDone).map(e=>tt(t,s,e)))}for(let s of t.invoke){if(s.onDone){let n=`xstate.done.actor.${s.id}`;e.set(n,x(s.onDone).map(e=>tt(t,n,e)))}if(s.onError){let n=`xstate.error.actor.${s.id}`;e.set(n,x(s.onError).map(e=>tt(t,n,e)))}if(s.onSnapshot){let n=`xstate.snapshot.${s.id}`;e.set(n,x(s.onSnapshot).map(e=>tt(t,n,e)))}}for(let s of t.after){let t=e.get(s.eventType);t||(t=[],e.set(s.eventType,t)),t.push(s)}return e}function ts(t,e){let s="string"==typeof e?t.states[e]:e?t.states[e.target]:void 0;if(!s&&e)throw Error(`Initial state node "${e}" not found on parent state node #${t.id}`);let n={source:t,actions:e&&"string"!=typeof e?m(e.actions):[],eventType:null,reenter:!1,target:s?[s]:[],toJSON:()=>({...n,source:`#${t.id}`,target:s?[`#${s.id}`]:[]})};return n}function tn(t){let e=S(t.config.target);return e?{target:e.map(e=>"string"==typeof e?tc(t.parent,e):e)}:t.parent.initial}function tr(t){return"history"===t.type}function ti(t){let e=to(t);for(let s of e)for(let n of J(s,t))e.add(n);return e}function to(t){let e=new Set;return function t(s){if(!e.has(s)){if(e.add(s),"compound"===s.type)t(s.initial.target[0]);else if("parallel"===s.type)for(let e of X(s))t(e)}}(t),e}function ta(t,e){if(G(e))return t.machine.getStateNodeById(e);if(!t.states)throw Error(`Unable to retrieve child state '${e}' from '${t.id}'; no child states exist.`);let s=t.states[e];if(!s)throw Error(`Child state '${e}' does not exist on '${t.id}'`);return s}function tc(t,e){if("string"==typeof e&&G(e))try{return t.machine.getStateNodeById(e)}catch{}let s=y(e).slice(),n=t;for(;s.length;){let t=s.shift();if(!t.length)break;n=ta(n,t)}return n}function tu(t,e){if("string"==typeof e){let s=t.states[e];if(!s)throw Error(`State '${e}' does not exist on '${t.id}'`);return[t,s]}let s=Object.keys(e),n=s.map(e=>ta(t,e)).filter(Boolean);return[t.machine.root,t].concat(n,s.reduce((s,n)=>{let r=ta(t,n);if(!r)return s;let i=tu(r,e[n]);return s.concat(i)},[]))}function tl(t,e){let s=t;for(;s.parent&&s.parent!==e;)s=s.parent;return s.parent===e}function td(t,e,s){let n=new Set;for(let r of t){let t=!1,i=new Set;for(let o of n)if(function(t,e){let s=new Set(t),n=new Set(e);for(let t of s)if(n.has(t))return!0;for(let t of n)if(s.has(t))return!0;return!1}(tp([r],e,s),tp([o],e,s))){if(tl(r.source,o.source))i.add(o);else{t=!0;break}}if(!t){for(let t of i)n.delete(t);n.add(r)}}return Array.from(n)}function th(t,e){if(!t.target)return[];let s=new Set;for(let n of t.target)if(tr(n)){if(e[n.id])for(let t of e[n.id])s.add(t);else for(let t of th(tn(n),e))s.add(t)}else s.add(n);return[...s]}function tf(t,e){let s=th(t,e);if(!s)return;if(!t.reenter&&s.every(e=>e===t.source||tl(e,t.source)))return t.source;let n=function(t){let[e,...s]=t;for(let t of J(e,void 0))if(s.every(e=>tl(e,t)))return t}(s.concat(t.source));return n||(t.reenter?void 0:t.source.machine.root)}function tp(t,e,s){let n=new Set;for(let r of t)if(r.target?.length){let t=tf(r,s);for(let s of(r.reenter&&r.source===t&&n.add(t),e))tl(s,t)&&n.add(s)}return[...n]}function ty(t,e,s,n,r,i){if(!t.length)return e;let o=new Set(e._nodes),a=e.historyValue,c=td(t,o,a),u=e;r||([u,a]=function(t,e,s,n,r,i,o,a){let c,u=t,l=tp(n,r,i);for(let t of(l.sort((t,e)=>e.order-t.order),l))for(let e of function(t){return Object.keys(t.states).map(e=>t.states[e]).filter(t=>"history"===t.type)}(t)){let s;s="deep"===e.history?e=>Z(e)&&tl(e,t):e=>e.parent===t,(c??={...i})[e.id]=Array.from(r).filter(s)}for(let t of l)u=tm(u,e,s,[...t.exit,...t.invoke.map(t=>L(t.id))],o,void 0),r.delete(t);return[u,c||i]}(u,n,s,c,o,a,i,s.actionExecutor)),u=function(t,e,s,n,r,i,o,a){let c=t,u=new Set,d=new Set;(function(t,e,s,n){for(let r of t){let t=tf(r,e);for(let i of r.target||[])!tr(i)&&(r.source!==i||r.source!==t||r.reenter)&&(n.add(i),s.add(i)),tg(i,e,s,n);for(let i of th(r,e)){let o=J(i,t);t?.type==="parallel"&&o.push(t),tv(n,e,s,o,!r.source.parent&&r.reenter?void 0:t)}}})(n,o,d,u),a&&d.add(t.machine.root);let h=new Set;for(let t of[...u].sort((t,e)=>t.order-e.order)){r.add(t);let n=[];for(let e of(n.push(...t.entry),t.invoke))n.push(z(e.src,{...e,syncSnapshot:!!e.onSnapshot}));if(d.has(t)){let e=t.initial.actions;n.push(...e)}if(c=tm(c,e,s,n,i,t.invoke.map(t=>t.id)),"final"===t.type){let n=t.parent,o=n?.type==="parallel"?n:n?.parent,a=o||t;for(n?.type==="compound"&&i.push(l(n.id,void 0!==t.output?_(t.output,c.context,e,s.self):void 0));o?.type==="parallel"&&!h.has(o)&&K(r,o);)h.add(o),i.push(l(o.id)),a=o,o=o.parent;if(o)continue;c=tq(c,{status:"done",output:function(t,e,s,n,r){if(void 0===n.output)return;let i=l(r.id,void 0!==r.output&&r.parent?_(r.output,t.context,e,s.self):void 0);return _(n.output,t.context,i,s.self)}(c,e,s,c.machine.root,a)})}}return c}(u=tm(u,n,s,c.flatMap(t=>t.actions),i,void 0),n,s,c,o,i,a,r);let d=[...o];"done"===u.status&&(u=tm(u,n,s,d.sort((t,e)=>e.order-t.order).flatMap(t=>t.exit),i,void 0));try{if(a===e.historyValue&&function(t,e){if(t.length!==e.size)return!1;for(let s of t)if(!e.has(s))return!1;return!0}(e._nodes,o))return u;return tq(u,{_nodes:d,historyValue:a})}catch(t){throw t}}function tg(t,e,s,n){if(tr(t)){if(e[t.id]){let r=e[t.id];for(let t of r)n.add(t),tg(t,e,s,n);for(let i of r)tv(n,e,s,J(i,t.parent))}else{let r=tn(t);for(let i of r.target)n.add(i),r===t.parent?.initial&&s.add(t.parent),tg(i,e,s,n);for(let i of r.target)tv(n,e,s,J(i,t.parent))}}else if("compound"===t.type){let[r]=t.initial.target;tr(r)||(n.add(r),s.add(r)),tg(r,e,s,n),tv(n,e,s,J(r,t))}else if("parallel"===t.type)for(let r of X(t).filter(t=>!tr(t)))[...n].some(t=>tl(t,r))||(tr(r)||(n.add(r),s.add(r)),tg(r,e,s,n))}function tv(t,e,s,n,r){for(let i of n)if((!r||tl(i,r))&&t.add(i),"parallel"===i.type)for(let n of X(i).filter(t=>!tr(t)))[...t].some(t=>tl(t,n))||(t.add(n),tg(n,e,s,t))}function tm(t,e,s,n,r,i){let o=i?[]:void 0,a=function t(e,s,n,r,i,o){let{machine:a}=e,c=e;for(let e of r){var u;let r="function"==typeof e,l=r?e:(u="string"==typeof e?e:e.type,a.implementations.actions[u]),d={context:c.context,event:s,self:n.self,system:n.system},h=r||"string"==typeof e?void 0:"params"in e?"function"==typeof e.params?e.params({context:c.context,event:s}):e.params:void 0;if(!l||!("resolve"in l)){n.actionExecutor({type:"string"==typeof e?e:"object"==typeof e?e.type:e.name||"(anonymous)",info:d,params:h,exec:l});continue}let[f,p,y]=l.resolve(n,c,d,h,l,i);c=f,"retryResolve"in l&&o?.push([l,p]),"execute"in l&&n.actionExecutor({type:l.type,info:d,params:p,exec:l.execute.bind(null,n,p)}),y&&(c=t(c,s,n,y,i,o))}return c}(t,e,s,n,{internalQueue:r,deferredActorIds:i},o);return o?.forEach(([t,e])=>{t.retryResolve(s,a,e)}),a}function t_(t,e,s,n){let r=t,i=[];function o(t,e,n){s.system._sendInspectionEvent({type:"@xstate.microstep",actorRef:s.self,event:e,snapshot:t,_transitions:n}),i.push(t)}if(e.type===u)return o(r=tq(tb(r,e,s),{status:"stopped"}),e,[]),{snapshot:r,microstates:i};let c=e;if(c.type!==a){let e=c,a=e.type.startsWith("xstate.error.actor"),u=tx(e,r);if(a&&!u.length)return o(r=tq(t,{status:"error",error:e.error}),e,[]),{snapshot:r,microstates:i};o(r=ty(u,t,s,c,!1,n),e,u)}let l=!0;for(;"active"===r.status;){let t=l?function(t,e){let s=new Set;for(let n of t._nodes.filter(Z))t:for(let r of[n].concat(J(n,void 0)))if(r.always){for(let n of r.always)if(void 0===n.guard||W(n.guard,t.context,e,t)){s.add(n);break t}}return td(Array.from(s),new Set(t._nodes),t.historyValue)}(r,c):[],e=t.length?r:void 0;if(!t.length){if(!n.length)break;t=tx(c=n.shift(),r)}l=(r=ty(t,r,s,c,!1,n))!==e,o(r,c,t)}return"active"!==r.status&&tb(r,c,s),{snapshot:r,microstates:i}}function tb(t,e,s){return tm(t,e,s,Object.values(t.children).map(t=>L(t)),[],void 0)}function tx(t,e){return e.machine.getTransitionData(e,t)}function tS(t,e){let s=V(tu(t,e));return F(t,[...s])}let tw=function(t){return function t(e,s){let n=g(e),r=g(s);return"string"==typeof r?"string"==typeof n&&r===n:"string"==typeof n?n in r:Object.keys(n).every(e=>e in r&&t(n[e],r[e]))}(t,this.value)},tk=function(t){return this.tags.has(t)},tI=function(t){let e=this.machine.getTransitionData(this,t);return!!e?.length&&e.some(t=>void 0!==t.target||t.actions.length)},t$=function(){let{_nodes:t,tags:e,machine:s,getMeta:n,toJSON:r,can:i,hasTag:o,matches:a,...c}=this;return{...c,tags:Array.from(e)}},tE=function(){return this._nodes.reduce((t,e)=>(void 0!==e.meta&&(t[e.id]=e.meta),t),{})};function tT(t,e){return{status:t.status,output:t.output,error:t.error,machine:e,context:t.context,_nodes:t._nodes,value:F(e.root,t._nodes),tags:new Set(t._nodes.flatMap(t=>t.tags)),children:t.children,historyValue:t.historyValue||{},matches:tw,hasTag:tk,can:tI,getMeta:tE,toJSON:t$}}function tq(t,e={}){return tT({...t,...e},t.machine)}function tA(t,e){let{_nodes:s,tags:n,machine:r,children:i,context:o,can:a,hasTag:c,matches:u,getMeta:l,toJSON:d,...h}=t,f={};for(let t in i){let s=i[t];f[t]={snapshot:s.getPersistedSnapshot(e),src:s.src,systemId:s._systemId,syncSnapshot:s._syncSnapshot}}return{...h,context:function t(e){let s;for(let n in e){let r=e[n];if(r&&"object"==typeof r){if("sessionId"in r&&"send"in r&&"ref"in r)(s??=Array.isArray(e)?e.slice():{...e})[n]={xstate$$type:q,id:r.id};else{let i=t(r);i!==r&&((s??=Array.isArray(e)?e.slice():{...e})[n]=i)}}}return s??e}(o),children:f}}function tO(t,e,s,n,{event:r,id:i,delay:o},{internalQueue:a}){let c;let u=e.machine.implementations.delays;if("string"==typeof r)throw Error(`Only event objects may be used with raise; use raise({ type: "${r}" }) instead`);let l="function"==typeof r?r(s,n):r;if("string"==typeof o){let t=u&&u[o];c="function"==typeof t?t(s,n):t}else c="function"==typeof o?o(s,n):o;return"number"!=typeof c&&a.push(l),[e,{event:l,id:i,delay:c},void 0]}function tR(t,e){let{event:s,delay:n,id:r}=e;if("number"==typeof n){t.defer(()=>{let e=t.self;t.system.scheduler.schedule(e,e,s,n,r)});return}}function tj(t,e){function s(t,e){}return s.type="xstate.raise",s.event=t,s.id=e?.id,s.delay=e?.delay,s.resolve=tO,s.execute=tR,s}},73790:(t,e,s)=>{s.d(e,{ZD:()=>i,cY:()=>h}),s(94139);var n=s(11742),r=s(84742);function i(t,e){let s=(0,n.t)(e);if(!s.includes(t.type)){let e=1===s.length?`type "${s[0]}"`:`one of types "${s.join('", "')}"`;throw Error(`Expected event ${JSON.stringify(t)} to have ${e}`)}}let o=new WeakMap;function a(t,e,s){let n=o.get(t);return n?e in n||(n[e]=s()):(n={[e]:s()},o.set(t,n)),n[e]}let c={},u=t=>"string"==typeof t?{type:t}:"function"==typeof t?"resolve"in t?{type:t.type}:{type:t.name}:t;class l{constructor(t,e){if(this.config=t,this.key=void 0,this.id=void 0,this.type=void 0,this.path=void 0,this.states=void 0,this.history=void 0,this.entry=void 0,this.exit=void 0,this.parent=void 0,this.machine=void 0,this.meta=void 0,this.output=void 0,this.order=-1,this.description=void 0,this.tags=[],this.transitions=void 0,this.always=void 0,this.parent=e._parent,this.key=e._key,this.machine=e._machine,this.path=this.parent?this.parent.path.concat(this.key):[],this.id=this.config.id||[this.machine.id,...this.path].join(n.S),this.type=this.config.type||(this.config.states&&Object.keys(this.config.states).length?"compound":this.config.history?"history":"atomic"),this.description=this.config.description,this.order=this.machine.idMap.size,this.machine.idMap.set(this.id,this),this.states=this.config.states?(0,n.m)(this.config.states,(t,e)=>new l(t,{_parent:this,_key:e,_machine:this.machine})):c,"compound"===this.type&&!this.config.initial)throw Error(`No initial state specified for compound state node "#${this.id}". Try adding { initial: "${Object.keys(this.states)[0]}" } to the state config.`);this.history=!0===this.config.history?"shallow":this.config.history||!1,this.entry=(0,n.t)(this.config.entry).slice(),this.exit=(0,n.t)(this.config.exit).slice(),this.meta=this.config.meta,this.output="final"!==this.type&&this.parent?void 0:this.config.output,this.tags=(0,n.t)(t.tags).slice()}_initialize(){this.transitions=(0,n.f)(this),this.config.always&&(this.always=(0,n.a)(this.config.always).map(t=>(0,n.b)(this,n.N,t))),Object.keys(this.states).forEach(t=>{this.states[t]._initialize()})}get definition(){return{id:this.id,key:this.key,version:this.machine.version,type:this.type,initial:this.initial?{target:this.initial.target,source:this,actions:this.initial.actions.map(u),eventType:null,reenter:!1,toJSON:()=>({target:this.initial.target.map(t=>`#${t.id}`),source:`#${this.id}`,actions:this.initial.actions.map(u),eventType:null})}:void 0,history:this.history,states:(0,n.m)(this.states,t=>t.definition),on:this.on,transitions:[...this.transitions.values()].flat().map(t=>({...t,actions:t.actions.map(u)})),entry:this.entry.map(u),exit:this.exit.map(u),meta:this.meta,order:this.order||-1,output:this.output,invoke:this.invoke,description:this.description,tags:this.tags}}toJSON(){return this.definition}get invoke(){return a(this,"invoke",()=>(0,n.t)(this.config.invoke).map((t,e)=>{let{src:s,systemId:r}=t,i=t.id??(0,n.c)(this.id,e),o="string"==typeof s?s:`xstate.invoke.${(0,n.c)(this.id,e)}`;return{...t,src:o,id:i,systemId:r,toJSON(){let{onDone:e,onError:s,...n}=t;return{...n,type:"xstate.invoke",src:o,id:i}}}}))}get on(){return a(this,"on",()=>[...this.transitions].flatMap(([t,e])=>e.map(e=>[t,e])).reduce((t,[e,s])=>(t[e]=t[e]||[],t[e].push(s),t),{}))}get after(){return a(this,"delayedTransitions",()=>(0,n.g)(this))}get initial(){return a(this,"initial",()=>(0,n.d)(this,this.config.initial))}next(t,e){let s;let r=e.type,i=[];for(let o of a(this,`candidates-${r}`,()=>(0,n.h)(this,r))){let{guard:a}=o,c=t.context,u=!1;try{u=!a||(0,n.e)(a,c,e,t)}catch(e){let t="string"==typeof a?a:"object"==typeof a?a.type:void 0;throw Error(`Unable to evaluate guard ${t?`'${t}' `:""}in transition for event '${r}' in state node '${this.id}':
${e.message}`)}if(u){i.push(...o.actions),s=o;break}}return s?[s]:void 0}get events(){return a(this,"events",()=>{let{states:t}=this,e=new Set(this.ownEvents);if(t)for(let s of Object.keys(t)){let n=t[s];if(n.states)for(let t of n.events)e.add(`${t}`)}return Array.from(e)})}get ownEvents(){return Array.from(new Set([...this.transitions.keys()].filter(t=>this.transitions.get(t).some(t=>!(!t.target&&!t.actions.length&&!t.reenter)))))}}class d{constructor(t,e){this.config=t,this.version=void 0,this.schemas=void 0,this.implementations=void 0,this.__xstatenode=!0,this.idMap=new Map,this.root=void 0,this.id=void 0,this.states=void 0,this.events=void 0,this.id=t.id||"(machine)",this.implementations={actors:e?.actors??{},actions:e?.actions??{},delays:e?.delays??{},guards:e?.guards??{}},this.version=this.config.version,this.schemas=this.config.schemas,this.transition=this.transition.bind(this),this.getInitialSnapshot=this.getInitialSnapshot.bind(this),this.getPersistedSnapshot=this.getPersistedSnapshot.bind(this),this.restoreSnapshot=this.restoreSnapshot.bind(this),this.start=this.start.bind(this),this.root=new l(t,{_key:this.id,_machine:this}),this.root._initialize(),this.states=this.root.states,this.events=this.root.events}provide(t){let{actions:e,guards:s,actors:n,delays:r}=this.implementations;return new d(this.config,{actions:{...e,...t.actions},guards:{...s,...t.guards},actors:{...n,...t.actors},delays:{...r,...t.delays}})}resolveState(t){let e=(0,n.r)(this.root,t.value),s=(0,n.i)((0,n.j)(this.root,e));return(0,n.k)({_nodes:[...s],context:t.context||{},children:{},status:(0,n.l)(s,this.root)?"done":t.status||"active",output:t.output,error:t.error,historyValue:t.historyValue},this)}transition(t,e,s){return(0,n.n)(t,e,s,[]).snapshot}microstep(t,e,s){return(0,n.n)(t,e,s,[]).microstates}getTransitionData(t,e){return(0,n.o)(this.root,t.value,t,e)||[]}getPreInitialState(t,e,s){let{context:i}=this.config,o=(0,n.k)({context:"function"!=typeof i&&i?i:{},_nodes:[this.root],children:{},status:"active"},this);return"function"==typeof i?(0,n.p)(o,e,t,[(0,r.a)(({spawn:t,event:e,self:s})=>i({spawn:t,input:e.input,self:s}))],s,void 0):o}getInitialSnapshot(t,e){let s=(0,n.q)(e),r=[],i=this.getPreInitialState(t,s,r),o=(0,n.s)([{target:[...(0,n.u)(this.root)],source:this.root,reenter:!0,actions:[],eventType:null,toJSON:null}],i,t,s,!0,r),{snapshot:a}=(0,n.n)(o,s,t,r);return a}start(t){Object.values(t.children).forEach(t=>{"active"===t.getSnapshot().status&&t.start()})}getStateNodeById(t){let e=(0,n.v)(t),s=e.slice(1),r=(0,n.w)(e[0])?e[0].slice(1):e[0],i=this.idMap.get(r);if(!i)throw Error(`Child state node '#${r}' does not exist on machine '${this.id}'`);return(0,n.x)(i,s)}get definition(){return this.root.definition}toJSON(){return this.definition}getPersistedSnapshot(t,e){return(0,n.y)(t,e)}restoreSnapshot(t,e){let s={},r=t.children;Object.keys(r).forEach(t=>{let i=r[t],o=i.snapshot,a=i.src,c="string"==typeof a?(0,n.z)(this,a):a;if(!c)return;let u=(0,n.A)(c,{id:t,parent:e.self,syncSnapshot:i.syncSnapshot,snapshot:o,src:a,systemId:i.systemId});s[t]=u});let i=(0,n.k)({...t,children:s,_nodes:Array.from((0,n.i)((0,n.j)(this.root,t.value)))},this),o=new Set;return function t(e,s){if(!o.has(e))for(let r in o.add(e),e){let i=e[r];if(i&&"object"==typeof i){if("xstate$$type"in i&&i.xstate$$type===n.$){e[r]=s[i.id];continue}t(i,s)}}}(i.context,s),i}}function h({schemas:t,actors:e,actions:s,guards:n,delays:r}){return{createMachine:i=>new d({...i,schemas:t},{actors:e,actions:s,guards:n,delays:r})}}},93996:(t,e,s)=>{s.d(e,{yK:()=>C,Hq:()=>W,ei:()=>J,bK:()=>Z,Ol:()=>X,UE:()=>L,Ag:()=>B,F8:()=>z,dS:()=>K,zB:()=>F,lB:()=>Y});var n=s(84770);let r={randomUUID:n.randomUUID},i=new Uint8Array(256),o=i.length,a=[];for(let t=0;t<256;++t)a.push((t+256).toString(16).slice(1));let c=function(t,e,s){if(r.randomUUID&&!e&&!t)return r.randomUUID();let c=(t=t||{}).random||(t.rng||function(){return o>i.length-16&&((0,n.randomFillSync)(i),o=0),i.slice(o,o+=16)})();if(c[6]=15&c[6]|64,c[8]=63&c[8]|128,e){s=s||0;for(let t=0;t<16;++t)e[s+t]=c[t];return e}return function(t,e=0){return(a[t[e+0]]+a[t[e+1]]+a[t[e+2]]+a[t[e+3]]+"-"+a[t[e+4]]+a[t[e+5]]+"-"+a[t[e+6]]+a[t[e+7]]+"-"+a[t[e+8]]+a[t[e+9]]+"-"+a[t[e+10]]+a[t[e+11]]+a[t[e+12]]+a[t[e+13]]+a[t[e+14]]+a[t[e+15]]).toLowerCase()}(c)};var u=s(94139),l=s(73790),d=s(84742),h=s(11742),f=s(76843),p=s(27863),y=s(1116),g=s(1508),v=s(54998),m=s(93609),_=s(46037),b=s(2973),x=Array.isArray,S=["addListener","removeListener"],w=["addEventListener","removeEventListener"],k=["on","off"];function I(t,e,s,n){if((0,_.m)(s)&&(n=s,s=void 0),n)return I(t,e,s).pipe((r=n,(0,b.U)(function(t){return x(t)?r.apply(void 0,(0,p.ev)([],(0,p.CR)(t))):r(t)})));var r,i=(0,p.CR)((0,_.m)(t.addEventListener)&&(0,_.m)(t.removeEventListener)?w.map(function(n){return function(r){return t[n](e,r,s)}}):(0,_.m)(t.addListener)&&(0,_.m)(t.removeListener)?S.map($(t,e)):(0,_.m)(t.on)&&(0,_.m)(t.off)?k.map($(t,e)):[],2),o=i[0],a=i[1];if(!o&&(0,m.z)(t))return(0,v.z)(function(t){return I(t,e,s)})((0,y.Xf)(t));if(!o)throw TypeError("Invalid event target");return new g.y(function(t){var e=function(){for(var e=[],s=0;s<arguments.length;s++)e[s]=arguments[s];return t.next(1<e.length?e:e[0])};return o(e),function(){return a(e)}})}function $(t,e){return function(s){return function(n){return t[s](e,n)}}}var E=s(12328),T=s(25801),q=s(74219),A=s(91126),O=s(43088),R=s(95393);function j(t){return t<=0?function(){return R.E}:(0,q.e)(function(e,s){var n=0;e.subscribe((0,A.x)(s,function(e){++n<=t&&(s.next(e),t<=n&&s.complete())}))})}var D=s(21138);let M=t=>({context:e})=>{let{count:s,include:n,exclude:r,responseType:i="message.received"}=t;return{count:s,domain:e.domain,from:e.connectTo,include:n?Array.isArray(n)?n:[n]:[],exclude:r?Array.isArray(r)?r:[r]:[],responseType:i,target:e.target,to:e.name}},N=t=>e=>{let{data:s}=e;return(!t.include.length||t.include.includes(s.type))&&(!t.exclude.length||!t.exclude.includes(s.type))&&s.domain===t.domain&&s.from===t.from&&s.to===t.to&&(!t.target||e.source===t.target)},P=t=>e=>({type:t,message:e}),U=(0,f.P)(()=>I(window,"message")),z=t=>(0,u.Y$)(({input:e})=>U.pipe(t?(0,b.U)(t):(0,E.z)(),(0,T.h)(N(e)),(0,b.U)(P(e.responseType)),e.count?(0,E.z)(function(t,e){return void 0===e&&(e=null),e=null!=e?e:t,(0,q.e)(function(s,n){var r=[],i=0;s.subscribe((0,A.x)(n,function(s){var o,a,c,u,l=null;i++%e==0&&r.push([]);try{for(var d=(0,p.XA)(r),h=d.next();!h.done;h=d.next()){var f=h.value;f.push(s),t<=f.length&&(l=null!=l?l:[]).push(f)}}catch(t){o={error:t}}finally{try{h&&!h.done&&(a=d.return)&&a.call(d)}finally{if(o)throw o.error}}if(l)try{for(var y=(0,p.XA)(l),g=y.next();!g.done;g=y.next()){var f=g.value;(0,O.P)(r,f),n.next(f)}}catch(t){c={error:t}}finally{try{g&&!g.done&&(u=y.return)&&u.call(y)}finally{if(c)throw c.error}}},function(){var t,e;try{for(var s=(0,p.XA)(r),i=s.next();!i.done;i=s.next()){var o=i.value;n.next(o)}}catch(e){t={error:e}}finally{try{i&&!i.done&&(e=s.return)&&e.call(s)}finally{if(t)throw t.error}}n.complete()},void 0,function(){r=null}))})}(e.count),function(t,e){return(0,_.m)(void 0)?(0,v.z)(t,void 0,1):(0,v.z)(t,1)}(t=>t),j(e.count)):(0,E.z)())),C="sanity/comlink",B="comlink/response",L="comlink/heartbeat",W="comlink/disconnect",Z="comlink/handshake/syn",X="comlink/handshake/syn-ack",J="comlink/handshake/ack",V=t=>e=>e.pipe(j(1),(0,b.U)(()=>{throw Error(t)})),Y=()=>(0,l.cY)({types:{},actors:{listen:(0,u.Y$)(({input:t})=>{let e=t.signal?I(t.signal,"abort").pipe(V(`Request ${t.requestId} aborted`)):R.E;return I(window,"message").pipe((0,T.h)(e=>e.data?.type===B&&e.data?.responseTo===t.requestId&&!!e.source&&t.sources.has(e.source)),j(t.sources.size),(0,q.e)(function(t,s){(0,y.Xf)(e).subscribe((0,A.x)(s,function(){return s.complete()},D.Z)),s.closed||t.subscribe(s)}))})},actions:{"send message":({context:t},e)=>{let{sources:s,targetOrigin:n}=t,{message:r}=e;s.forEach(t=>{t.postMessage(r,{targetOrigin:n})})},"on success":(0,d.c)(({context:t})=>t.parentRef,({context:t,self:e})=>(t.response&&t.resolvable?.resolve(t.response),{type:"request.success",requestId:e.id,response:t.response,responseTo:t.responseTo})),"on fail":(0,d.c)(({context:t})=>t.parentRef,({context:t,self:e})=>(t.suppressWarnings||console.warn(`[@sanity/comlink] Received no response to message '${t.type}' on client '${t.from}' (ID: '${t.id}').`),t.resolvable?.reject(Error("No response received")),{type:"request.failed",requestId:e.id})),"on abort":(0,d.c)(({context:t})=>t.parentRef,({context:t,self:e})=>(t.resolvable?.reject(Error("Request aborted")),{type:"request.aborted",requestId:e.id}))},guards:{expectsResponse:({context:t})=>t.expectResponse},delays:{initialTimeout:0,responseTimeout:({context:t})=>t.responseTimeout??3e3}}).createMachine({context:({input:t})=>({channelId:t.channelId,data:t.data,domain:t.domain,expectResponse:t.expectResponse??!1,from:t.from,id:`msg-${c()}`,parentRef:t.parentRef,resolvable:t.resolvable,response:null,responseTimeout:t.responseTimeout,responseTo:t.responseTo,signal:t.signal,sources:t.sources instanceof Set?t.sources:new Set([t.sources]),suppressWarnings:t.suppressWarnings,targetOrigin:t.targetOrigin,to:t.to,type:t.type}),initial:"idle",on:{abort:".aborted"},states:{idle:{after:{initialTimeout:[{target:"sending"}]}},sending:{entry:{type:"send message",params:({context:t})=>{let{channelId:e,data:s,domain:n,from:r,id:i,responseTo:o,to:a,type:c}=t;return{message:{channelId:e,data:s,domain:n,from:r,id:i,to:a,type:c,responseTo:o}}}},always:[{guard:"expectsResponse",target:"awaiting"},"success"]},awaiting:{invoke:{id:"listen for response",src:"listen",input:({context:t})=>({requestId:t.id,sources:t.sources,signal:t.signal}),onError:"aborted"},after:{responseTimeout:"failed"},on:{message:{actions:(0,d.a)({response:({event:t})=>t.data.data,responseTo:({event:t})=>t.data.responseTo}),target:"success"}}},failed:{type:"final",entry:"on fail"},success:{type:"final",entry:"on success"},aborted:{type:"final",entry:"on abort"}},output:({context:t,self:e})=>({requestId:e.id,response:t.response,responseTo:t.responseTo})}),F=((0,u.E9)(({sendBack:t,input:e})=>{let s=()=>{t(e.event)};e.immediate&&s();let n=setInterval(s,e.interval);return()=>{clearInterval(n)}}),()=>(0,l.cY)({types:{},actors:{requestMachine:Y(),listen:z()},actions:{"buffer incoming message":(0,d.a)({handshakeBuffer:({event:t,context:e})=>((0,l.ZD)(t,"message.received"),[...e.handshakeBuffer,t])}),"buffer message":(0,d.b)(({enqueue:t})=>{t.assign({buffer:({event:t,context:e})=>((0,l.ZD)(t,"post"),[...e.buffer,{data:t.data,resolvable:t.resolvable,options:t.options}])}),t.emit(({event:t})=>((0,l.ZD)(t,"post"),{type:"_buffer.added",message:t.data}))}),"create request":(0,d.a)({requests:({context:t,event:e,self:s,spawn:n})=>{(0,l.ZD)(e,"request");let r=(Array.isArray(e.data)?e.data:[e.data]).map(e=>n("requestMachine",{id:`req-${c()}`,input:{channelId:t.channelId,data:e.data,domain:t.domain,expectResponse:e.expectResponse,from:t.name,parentRef:s,resolvable:e.resolvable,responseTimeout:e.options?.responseTimeout,responseTo:e.responseTo,signal:e.options?.signal,sources:t.target,suppressWarnings:e.options?.suppressWarnings,targetOrigin:t.targetOrigin,to:t.connectTo,type:e.type}}));return[...t.requests,...r]}}),"emit heartbeat":(0,d.e)(()=>({type:"_heartbeat"})),"emit received message":(0,d.b)(({enqueue:t})=>{t.emit(({event:t})=>((0,l.ZD)(t,"message.received"),{type:"_message",message:t.message.data})),t.emit(({event:t})=>((0,l.ZD)(t,"message.received"),{type:t.message.data.type,message:t.message.data}))}),"emit status":(0,d.e)((t,e)=>({type:"_status",status:e.status})),"flush buffer":(0,d.b)(({enqueue:t})=>{t.raise(({context:t})=>({type:"request",data:t.buffer.map(({data:t,resolvable:e,options:s})=>({data:t.data,type:t.type,expectResponse:!!e,resolvable:e,options:s}))})),t.emit(({context:t})=>({type:"_buffer.flushed",messages:t.buffer.map(({data:t})=>t)})),t.assign({buffer:[]})}),"flush handshake buffer":(0,d.b)(({context:t,enqueue:e})=>{t.handshakeBuffer.forEach(t=>e.raise(t)),e.assign({handshakeBuffer:[]})}),post:(0,h.O)(({event:t})=>((0,l.ZD)(t,"post"),{type:"request",data:{data:t.data.data,expectResponse:!!t.resolvable,type:t.data.type,resolvable:t.resolvable,options:t.options}})),"remove request":(0,d.b)(({context:t,enqueue:e,event:s})=>{(0,l.ZD)(s,["request.success","request.failed","request.aborted"]),(0,h.R)(s.requestId),e.assign({requests:t.requests.filter(({id:t})=>t!==s.requestId)})}),"send response":(0,h.O)(({event:t})=>((0,l.ZD)(t,["message.received","heartbeat.received"]),{type:"request",data:{type:B,responseTo:t.message.data.id,data:void 0}})),"send handshake syn ack":(0,h.O)({type:"request",data:{type:X}}),"set connection config":(0,d.a)({channelId:({event:t})=>((0,l.ZD)(t,"handshake.syn"),t.message.data.channelId),target:({event:t})=>((0,l.ZD)(t,"handshake.syn"),t.message.source||void 0),targetOrigin:({event:t})=>((0,l.ZD)(t,"handshake.syn"),t.message.origin)})},guards:{hasSource:({context:t})=>null!==t.target}}).createMachine({id:"node",context:({input:t})=>({buffer:[],channelId:null,connectTo:t.connectTo,domain:t.domain??C,handshakeBuffer:[],name:t.name,requests:[],target:void 0,targetOrigin:null}),invoke:{id:"listen for handshake syn",src:"listen",input:M({include:Z,responseType:"handshake.syn"})},on:{"request.success":{actions:"remove request"},"request.failed":{actions:"remove request"},"request.aborted":{actions:"remove request"},"handshake.syn":{actions:"set connection config",target:".handshaking"}},initial:"idle",states:{idle:{entry:[{type:"emit status",params:{status:"idle"}}],on:{post:{actions:"buffer message"}}},handshaking:{guard:"hasSource",entry:["send handshake syn ack",{type:"emit status",params:{status:"handshaking"}}],invoke:[{id:"listen for handshake ack",src:"listen",input:M({include:J,count:1,responseType:"handshake.complete"}),onDone:"connected"},{id:"listen for disconnect",src:"listen",input:M({include:W,count:1,responseType:"disconnect"})},{id:"listen for messages",src:"listen",input:M({exclude:[W,Z,J,L,B]})}],on:{request:{actions:"create request"},post:{actions:"buffer message"},"message.received":{actions:"buffer incoming message"},disconnect:{target:"idle"}}},connected:{entry:["flush handshake buffer","flush buffer",{type:"emit status",params:{status:"connected"}}],invoke:[{id:"listen for messages",src:"listen",input:M({exclude:[W,Z,J,L,B]})},{id:"listen for heartbeat",src:"listen",input:M({include:L,responseType:"heartbeat.received"})},{id:"listen for disconnect",src:"listen",input:M({include:W,count:1,responseType:"disconnect"})}],on:{request:{actions:"create request"},post:{actions:"post"},disconnect:{target:"idle"},"message.received":{actions:["send response","emit received message"]},"heartbeat.received":{actions:["send response","emit heartbeat"]}}}}})),K=(t,e=F())=>{let s;let n=(0,h.A)(e,{input:t}),r=()=>{n.stop()};return{actor:n,fetch:(t,e,s)=>{let{responseTimeout:r=1e4,signal:i,suppressWarnings:o}=s||{},a=Promise.withResolvers();return n.send({type:"post",data:{type:t,data:e},resolvable:a,options:{responseTimeout:r,signal:i,suppressWarnings:o}}),a.promise},machine:e,on:(t,e)=>{let{unsubscribe:s}=n.on(t,t=>{e(t.message.data)});return s},onStatus:(t,e)=>{let{unsubscribe:r}=n.on("_status",n=>{s=n.status,e&&n.status!==e||t(n.status)});return s&&t(s),r},post:(t,e)=>{n.send({type:"post",data:{type:t,data:e}})},start:()=>(n.start(),r),stop:r}}}};