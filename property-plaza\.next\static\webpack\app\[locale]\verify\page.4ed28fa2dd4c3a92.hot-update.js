"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/verify/page",{

/***/ "(app-pages-browser)/./components/ui/optimized-video.tsx":
/*!*******************************************!*\
  !*** ./components/ui/optimized-video.tsx ***!
  \*******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ OptimizedVideo; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nfunction OptimizedVideo(param) {\n    let { src, poster, className = \"\", autoPlay = true, muted = true, loop = true, playsInline = true, controls = false, preload = \"metadata\", lazy = true, fallbackContent, showCustomControls = false } = param;\n    _s();\n    const [shouldLoad, setShouldLoad] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!lazy);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [canPlay, setCanPlay] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasError, setHasError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const videoRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (!lazy) return;\n        const observer = new IntersectionObserver((param)=>{\n            let [entry] = param;\n            if (entry.isIntersecting) {\n                setShouldLoad(true);\n                observer.disconnect();\n            }\n        }, {\n            threshold: 0.1,\n            rootMargin: \"50px\" // Start loading 50px before video comes into view\n        });\n        if (videoRef.current) {\n            observer.observe(videoRef.current);\n        }\n        return ()=>observer.disconnect();\n    }, [\n        lazy\n    ]);\n    const handleLoadStart = ()=>{\n        setIsLoading(true);\n        setCanPlay(false);\n        setHasError(false);\n    };\n    const handleCanPlay = ()=>{\n        setIsLoading(false);\n        setCanPlay(true);\n    };\n    const handleError = ()=>{\n        setIsLoading(false);\n        setCanPlay(false);\n        setHasError(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: videoRef,\n        className: \"relative \".concat(className),\n        children: [\n            isLoading && shouldLoad && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gray-100 flex items-center justify-center z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col items-center space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 border-2 border-seekers-primary border-t-transparent rounded-full animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: \"Loading video...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                    lineNumber: 85,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                lineNumber: 84,\n                columnNumber: 9\n            }, this),\n            hasError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-gray-100 flex items-center justify-center z-10\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center space-y-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-4xl\",\n                            children: \"⚠️\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                            lineNumber: 96,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-500\",\n                            children: \"Video could not be loaded\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                            lineNumber: 97,\n                            columnNumber: 13\n                        }, this),\n                        fallbackContent\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                    lineNumber: 95,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                lineNumber: 94,\n                columnNumber: 9\n            }, this),\n            poster && !shouldLoad && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                src: poster,\n                alt: \"Video preview\",\n                fill: true,\n                className: \"object-cover rounded-lg\"\n            }, void 0, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                lineNumber: 105,\n                columnNumber: 9\n            }, this),\n            shouldLoad && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"video\", {\n                autoPlay: autoPlay,\n                muted: muted,\n                loop: loop,\n                playsInline: playsInline,\n                controls: controls,\n                preload: preload,\n                poster: poster,\n                className: \"w-full h-full object-cover rounded-lg \".concat(controls ? \"cursor-pointer\" : \"\"),\n                onLoadStart: handleLoadStart,\n                onCanPlay: handleCanPlay,\n                onError: handleError,\n                style: {\n                    outline: \"none\"\n                },\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                        src: src,\n                        type: \"video/mp4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                        src: src.replace(\".mp4\", \".webm\"),\n                        type: \"video/webm\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center h-full bg-gray-100\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center space-y-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-4xl\",\n                                    children: \"\\uD83D\\uDCF9\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                                    lineNumber: 137,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-500\",\n                                    children: \"Your browser doesn't support video playback\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                                    lineNumber: 138,\n                                    columnNumber: 15\n                                }, this),\n                                fallbackContent\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                            lineNumber: 136,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                        lineNumber: 135,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n                lineNumber: 115,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\components\\\\ui\\\\optimized-video.tsx\",\n        lineNumber: 81,\n        columnNumber: 5\n    }, this);\n}\n_s(OptimizedVideo, \"Zg8iB6wn/MI0qUbHEmOGw0Twf6Y=\");\n_c = OptimizedVideo;\nvar _c;\n$RefreshReg$(_c, \"OptimizedVideo\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/optimized-video.tsx\n"));

/***/ })

});