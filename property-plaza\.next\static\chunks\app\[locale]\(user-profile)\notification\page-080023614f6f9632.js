(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[329],{12033:function(e,t,n){Promise.resolve().then(n.bind(n,19596)),Promise.resolve().then(n.bind(n,12545)),Promise.resolve().then(n.bind(n,97867)),Promise.resolve().then(n.bind(n,31085)),Promise.resolve().then(n.bind(n,10575))},19596:function(e,t,n){"use strict";n.d(t,{default:function(){return h}});var i=n(57437),r=n(42586),o=n(29501),s=n(31229);let a=s.z.object({soundNotifications:s.z.boolean(),emailNewMessages:s.z.boolean(),emailNewProperties:s.z.boolean(),priceChangeAlerts:s.z.boolean(),newsletters:s.z.boolean(),specialOffers:s.z.boolean(),surveys:s.z.boolean()});var l=n(13590),c=n(2265),u=n(15681),f=n(1828),d=n(24596),p=n(35153),m=n(30078);function h(){let e=(0,r.useTranslations)("seeker"),[t,n]=(0,c.useState)(!1),s=(0,m.L)(e=>e.seekers),h=(0,d.K)(),g=(0,o.cI)({resolver:(0,l.F)(a),defaultValues:{soundNotifications:s.setting.soundNotif,emailNewMessages:s.setting.messageNotif,emailNewProperties:s.setting.priceAlertNotif,priceChangeAlerts:s.setting.priceAlertNotif,newsletters:s.setting.newsletterNotif,specialOffers:s.setting.specialOfferNotif,surveys:s.setting.surveyNotif}});(0,c.useEffect)(()=>{s.setting&&(g.setValue("emailNewMessages",s.setting.messageNotif||!1),g.setValue("emailNewProperties",s.setting.propertyNotif||!1),g.setValue("newsletters",s.setting.newsletterNotif||!1),g.setValue("priceChangeAlerts",s.setting.priceAlertNotif||!1),g.setValue("soundNotifications",s.setting.soundNotif||!1),g.setValue("specialOffers",s.setting.specialOfferNotif||!1),g.setValue("surveys",s.setting.surveyNotif||!1))},[s.setting,g]);let{toast:b}=(0,p.pm)(),v=[{name:"soundNotifications",label:e("setting.notification.soundNotification.title"),description:e("setting.notification.soundNotification.description"),form:g,key:"sound_notif"},{name:"emailNewMessages",label:e("setting.notification.emailForNewMessages.title"),description:e("setting.notification.emailForNewMessages.description"),form:g,key:"message_notif"},{name:"emailNewProperties",label:e("setting.notification.emailForNewProperties.title"),description:e("setting.notification.emailForNewProperties.description"),form:g,key:"property_notif"},{name:"priceChangeAlerts",label:e("setting.notification.priceChangeAlert.title"),description:e("setting.notification.priceChangeAlert.description"),form:g,key:"price_alert_notif"},{name:"newsletters",label:e("setting.notification.newsletter.title"),description:e("setting.notification.newsletter.description"),form:g,key:"newsletter_notif"},{name:"specialOffers",label:e("setting.notification.specialOffer.title"),description:e("setting.notification.specialOffer.description"),form:g,key:"special_offer_notif"},{name:"surveys",label:e("setting.notification.surveys.title"),description:e("setting.notification.surveys.description"),form:g,key:"survey_notif"}],N=async(t,n,i,r)=>{try{await h.mutateAsync({settings:{[i]:r}}),b({title:e("success.updateNotification.title",{field:t})}),g.setValue(n.name,r)}catch(n){b({title:e("error.updateNotification.title",{field:t})})}};return(0,i.jsx)(i.Fragment,{children:(0,i.jsx)(u.l0,{...g,children:(0,i.jsx)("form",{onSubmit:g.handleSubmit(e=>{n(!0),setTimeout(()=>{n(!1)},1e3)}),className:"space-y-8 pb-8",children:(0,i.jsx)("div",{className:"space-y-4",children:v.map((e,t)=>(0,i.jsx)(u.Wi,{control:g.control,name:e.name,render:t=>{let{field:n}=t;return(0,i.jsxs)(u.xJ,{className:"flex flex-row items-center justify-between rounded-lg border p-4",children:[(0,i.jsxs)("div",{className:"space-y-0.5",children:[(0,i.jsx)(u.lX,{className:"text-base",children:e.label}),(0,i.jsx)(u.pf,{children:e.description})]}),(0,i.jsx)(u.NI,{children:(0,i.jsx)(f.r,{checked:n.value,className:"data-[state=checked]:bg-seekers-primary",onCheckedChange:t=>N(e.label,{form:g,name:e.name},e.key,t)})})]})}},t))})})})})}},1828:function(e,t,n){"use strict";n.d(t,{r:function(){return a}});var i=n(57437),r=n(2265),o=n(50721),s=n(94508);let a=r.forwardRef((e,t)=>{let{className:n,...r}=e;return(0,i.jsx)(o.fC,{className:(0,s.cn)("peer inline-flex h-5 w-9 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-seekers-primary data-[state=unchecked]:bg-input",n),...r,ref:t,children:(0,i.jsx)(o.bU,{className:(0,s.cn)("pointer-events-none block h-4 w-4 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0")})})});a.displayName=o.fC.displayName},49988:function(e,t,n){"use strict";function i(){return(i=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)({}).hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e}).apply(null,arguments)}n.d(t,{g:function(){return i}})},97867:function(e,t,n){"use strict";n.d(t,{default:function(){return l}});var i=n(49988),r=n(27648),o=n(99376),s=n(2265),a=n(48706),l=(0,s.forwardRef)(function(e,t){let{defaultLocale:n,href:l,locale:c,localeCookie:u,onClick:f,prefetch:d,unprefixed:p,...m}=e,h=(0,a.Z)(),g=c!==h,b=c||h,v=function(){let[e,t]=(0,s.useState)();return(0,s.useEffect)(()=>{t(window.location.host)},[]),e}(),N=v&&p&&(p.domains[v]===b||!Object.keys(p.domains).includes(v)&&h===n&&!c)?p.pathname:l,w=(0,o.usePathname)();return g&&(d&&console.error("The `prefetch` prop is currently not supported when using the `locale` prop on `Link` to switch the locale.`"),d=!1),s.createElement(r.default,(0,i.g)({ref:t,href:N,hrefLang:g?c:void 0,onClick:function(e){(function(e,t,n,i){if(!e||!(i!==n&&null!=i)||!t)return;let r=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:window.location.pathname;return"/"===e?t:t.replace(e,"")}(t),{name:o,...s}=e;s.path||(s.path=""!==r?r:"/");let a="".concat(o,"=").concat(i,";");for(let[e,t]of Object.entries(s))a+="".concat("maxAge"===e?"max-age":e),"boolean"!=typeof t&&(a+="="+t),a+=";";document.cookie=a})(u,w,h,c),f&&f(e)},prefetch:d},m))})},31085:function(e,t,n){"use strict";n.d(t,{default:function(){return f}});var i=n(49988),r=n(99376),o=n(2265),s=n(48706);function a(e){return("object"==typeof e?null==e.host&&null==e.hostname:!/^[a-z]+:/i.test(e))&&!function(e){let t="object"==typeof e?e.pathname:e;return null!=t&&!t.startsWith("/")}(e)}function l(e,t){let n;return"string"==typeof e?n=c(t,e):(n={...e},e.pathname&&(n.pathname=c(t,e.pathname))),n}function c(e,t){let n=e;return/^\/(\?.*)?$/.test(t)&&(t=t.slice(1)),n+=t}n(25566);var u=n(97867);let f=(0,o.forwardRef)(function(e,t){let{href:n,locale:c,localeCookie:f,localePrefixMode:d,prefix:p,...m}=e,h=(0,r.usePathname)(),g=(0,s.Z)(),b=c!==g,[v,N]=(0,o.useState)(()=>a(n)&&("never"!==d||b)?l(n,p):n);return(0,o.useEffect)(()=>{h&&N(function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t,i=arguments.length>3?arguments[3]:void 0,r=arguments.length>4?arguments[4]:void 0;if(!a(e))return e;let o=i===r||i.startsWith("".concat(r,"/"));return(t!==n||o)&&null!=r?l(e,r):e}(n,c,g,h,p))},[g,n,c,h,p]),o.createElement(u.default,(0,i.g)({ref:t,href:v,locale:c,localeCookie:f},m))});f.displayName="ClientLink"},48706:function(e,t,n){"use strict";n.d(t,{Z:function(){return s}});var i=n(99376),r=n(526);let o="locale";function s(){let e;let t=(0,i.useParams)();try{e=(0,r.useLocale)()}catch(n){if("string"!=typeof(null==t?void 0:t[o]))throw n;e=t[o]}return e}},10575:function(e,t,n){"use strict";n.d(t,{default:function(){return s}});var i=n(49988),r=n(2265),o=n(69362);function s(e){let{locale:t,...n}=e;if(!t)throw Error("Failed to determine locale in `NextIntlClientProvider`, please provide the `locale` prop explicitly.\n\nSee https://next-intl-docs.vercel.app/docs/configuration#locale");return r.createElement(o.IntlProvider,(0,i.g)({locale:t},n))}},6741:function(e,t,n){"use strict";function i(e,t,{checkForDefaultPrevented:n=!0}={}){return function(i){if(e?.(i),!1===n||!i.defaultPrevented)return t?.(i)}}n.d(t,{M:function(){return i}})},73966:function(e,t,n){"use strict";n.d(t,{b:function(){return o}});var i=n(2265),r=n(57437);function o(e,t=[]){let n=[],o=()=>{let t=n.map(e=>i.createContext(e));return function(n){let r=n?.[e]||t;return i.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return o.scopeName=e,[function(t,o){let s=i.createContext(o),a=n.length;function l(t){let{scope:n,children:o,...l}=t,c=n?.[e][a]||s,u=i.useMemo(()=>l,Object.values(l));return(0,r.jsx)(c.Provider,{value:u,children:o})}return n=[...n,o],l.displayName=t+"Provider",[l,function(n,r){let l=r?.[e][a]||s,c=i.useContext(l);if(c)return c;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:i})=>{let r=n(e)[`__scope${i}`];return{...t,...r}},{});return i.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(o,...t)]}},50721:function(e,t,n){"use strict";n.d(t,{bU:function(){return x},fC:function(){return k}});var i=n(2265),r=n(6741),o=n(98575),s=n(73966),a=n(80886),l=n(6718),c=n(90420),u=n(82912),f=n(57437),d="Switch",[p,m]=(0,s.b)(d),[h,g]=p(d),b=i.forwardRef((e,t)=>{let{__scopeSwitch:n,name:s,checked:l,defaultChecked:c,required:d,disabled:p,value:m="on",onCheckedChange:g,...b}=e,[v,N]=i.useState(null),k=(0,o.e)(t,e=>N(e)),x=i.useRef(!1),j=!v||!!v.closest("form"),[C=!1,P]=(0,a.T)({prop:l,defaultProp:c,onChange:g});return(0,f.jsxs)(h,{scope:n,checked:C,disabled:p,children:[(0,f.jsx)(u.WV.button,{type:"button",role:"switch","aria-checked":C,"aria-required":d,"data-state":y(C),"data-disabled":p?"":void 0,disabled:p,value:m,...b,ref:k,onClick:(0,r.M)(e.onClick,e=>{P(e=>!e),j&&(x.current=e.isPropagationStopped(),x.current||e.stopPropagation())})}),j&&(0,f.jsx)(w,{control:v,bubbles:!x.current,name:s,value:m,checked:C,required:d,disabled:p,style:{transform:"translateX(-100%)"}})]})});b.displayName=d;var v="SwitchThumb",N=i.forwardRef((e,t)=>{let{__scopeSwitch:n,...i}=e,r=g(v,n);return(0,f.jsx)(u.WV.span,{"data-state":y(r.checked),"data-disabled":r.disabled?"":void 0,...i,ref:t})});N.displayName=v;var w=e=>{let{control:t,checked:n,bubbles:r=!0,...o}=e,s=i.useRef(null),a=(0,l.D)(n),u=(0,c.t)(t);return i.useEffect(()=>{let e=s.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(a!==n&&t){let i=new Event("click",{bubbles:r});t.call(e,n),e.dispatchEvent(i)}},[a,n,r]),(0,f.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:n,...o,tabIndex:-1,ref:s,style:{...e.style,...u,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function y(e){return e?"checked":"unchecked"}var k=b,x=N},6718:function(e,t,n){"use strict";n.d(t,{D:function(){return r}});var i=n(2265);function r(e){let t=i.useRef({value:e,previous:e});return i.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}}},function(e){e.O(0,[6990,8310,6290,8094,2586,2957,4956,3448,8658,6088,9623,3398,6205,3682,2545,7668,2971,2117,1744],function(){return e(e.s=12033)}),_N_E=e.O()}]);