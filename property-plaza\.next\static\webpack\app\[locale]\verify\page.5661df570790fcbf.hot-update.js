"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/verify/page",{

/***/ "(app-pages-browser)/./app/[locale]/verify/verify-social-proof.tsx":
/*!*****************************************************!*\
  !*** ./app/[locale]/verify/verify-social-proof.tsx ***!
  \*****************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ VerifySocialProof; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Star!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _components_seekers_content_layout_main_content_layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/seekers-content-layout/main-content-layout */ \"(app-pages-browser)/./components/seekers-content-layout/main-content-layout.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction VerifySocialProof() {\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_3__.useTranslations)(\"verify.socialProof\");\n    const reviews = [\n        {\n            name: t(\"reviews.review1.name\"),\n            location: t(\"reviews.review1.location\"),\n            rating: 5,\n            text: t(\"reviews.review1.text\"),\n            propertyType: t(\"reviews.review1.propertyType\")\n        },\n        {\n            name: t(\"reviews.review2.name\"),\n            location: t(\"reviews.review2.location\"),\n            rating: 5,\n            text: t(\"reviews.review2.text\"),\n            propertyType: t(\"reviews.review2.propertyType\")\n        },\n        {\n            name: t(\"reviews.review3.name\"),\n            location: t(\"reviews.review3.location\"),\n            rating: 5,\n            text: t(\"reviews.review3.text\"),\n            propertyType: t(\"reviews.review3.propertyType\")\n        }\n    ];\n    const renderStars = (rating)=>{\n        return Array.from({\n            length: 5\n        }, (_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Star_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"w-4 h-4 \".concat(index < rating ? \"fill-yellow-400 text-yellow-400\" : \"fill-gray-200 text-gray-200\")\n            }, index, false, {\n                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-social-proof.tsx\",\n                lineNumber: 44,\n                columnNumber: 7\n            }, this));\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-16 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_seekers_content_layout_main_content_layout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold text-seekers-text mb-4\",\n                            children: t(\"title\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-social-proof.tsx\",\n                            lineNumber: 60,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-seekers-text-light max-w-3xl mx-auto\",\n                            children: t(\"subtitle\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-social-proof.tsx\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-social-proof.tsx\",\n                    lineNumber: 59,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                    children: reviews.map((review, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-100 p-6 hover:shadow-md transition-shadow duration-300\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-start gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-gradient-to-br from-seekers-primary to-seekers-primary/80 rounded-full flex items-center justify-center text-white font-semibold text-base\",\n                                            children: review.name.charAt(0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-social-proof.tsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-social-proof.tsx\",\n                                        lineNumber: 77,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 min-w-0\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center mb-3\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex space-x-1\",\n                                                    children: renderStars(review.rating)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-social-proof.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-social-proof.tsx\",\n                                                lineNumber: 86,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                                className: \"text-seekers-text mb-4 leading-relaxed text-sm\",\n                                                children: [\n                                                    '\"',\n                                                    review.text,\n                                                    '\"'\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-social-proof.tsx\",\n                                                lineNumber: 93,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm font-semibold text-seekers-text\",\n                                                        children: review.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-social-proof.tsx\",\n                                                        lineNumber: 99,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-xs text-seekers-text-light\",\n                                                        children: [\n                                                            review.location,\n                                                            \" • \",\n                                                            review.propertyType\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-social-proof.tsx\",\n                                                        lineNumber: 102,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-social-proof.tsx\",\n                                                lineNumber: 98,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-social-proof.tsx\",\n                                        lineNumber: 84,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-social-proof.tsx\",\n                                lineNumber: 75,\n                                columnNumber: 15\n                            }, this)\n                        }, index, false, {\n                            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-social-proof.tsx\",\n                            lineNumber: 71,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-social-proof.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-12 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-wrap justify-center items-center gap-4 sm:gap-6 text-sm text-seekers-text-light\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-green-500 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-social-proof.tsx\",\n                                        lineNumber: 116,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: t(\"trustIndicators.verified\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-social-proof.tsx\",\n                                        lineNumber: 117,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-social-proof.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-seekers-primary rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-social-proof.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: t(\"trustIndicators.realClients\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-social-proof.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-social-proof.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-2 h-2 bg-purple-500 rounded-full\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-social-proof.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: t(\"trustIndicators.actualInspections\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-social-proof.tsx\",\n                                        lineNumber: 125,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-social-proof.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-social-proof.tsx\",\n                        lineNumber: 114,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-social-proof.tsx\",\n                    lineNumber: 113,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-social-proof.tsx\",\n            lineNumber: 57,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\_PRIVATE\\\\Property Plaza - Seekers\\\\property-plaza\\\\app\\\\[locale]\\\\verify\\\\verify-social-proof.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n_s(VerifySocialProof, \"h6+q2O3NJKPY5uL0BIJGLIanww8=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_3__.useTranslations\n    ];\n});\n_c = VerifySocialProof;\nvar _c;\n$RefreshReg$(_c, \"VerifySocialProof\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/[locale]/verify/verify-social-proof.tsx\n"));

/***/ })

});